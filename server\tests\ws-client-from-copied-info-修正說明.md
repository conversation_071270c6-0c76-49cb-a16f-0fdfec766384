# WebSocket 測試客戶端 dataId 欄位修正說明

## 修正概述
根據系統架構要求，`dataId` 是由前端或 API 控制的欄位，不應該由裝置自己回報。本次修正移除了所有 WebSocket 測試客戶端中設備回報的 `dataId` 欄位。

## 主要問題與修正

### 1. dataId 欄位問題
**問題**：設備回報中包含 `dataId` 欄位，但這是由前端或API控制的欄位，不應該由裝置自己回報。

**修正**：
- 移除所有設備回報中的 `dataId` 欄位
- 更新測試客戶端的用戶界面，不再要求輸入或顯示 `dataId`
- 添加註釋說明不包含 `dataId` 的原因

### 2. 設備回報格式統一
**問題**：不同測試客戶端的設備回報格式不一致。

**修正**：
- 統一所有測試客戶端的設備回報格式
- 確保只包含裝置應該回報的欄位：`macAddress`、`status`、`data`
- 保持 imageCode 處理邏輯不變（只有本地有時才包含）

### 3. 文檔同步更新
**修正內容**：
- 更新 Gateway-Device-Implementation-Guide.md
- 更新 Gateway-Device-Quick-Reference.md
- 添加重要注意事項和常見問題解答

## 修正的文件

### 1. `ws-client-from-copied-info.js`
- 移除設備回報中的 `dataId` 欄位
- 更新添加自定義設備的提示，不再要求輸入 `dataId`
- 更新設備列表顯示，不再顯示 `dataId`
- 更新幫助信息，說明設備回報不包含 `dataId`

### 2. `test-ws-client-interactive.js`
- 移除所有預設設備和自定義設備回報中的 `dataId` 欄位
- 更新添加自定義設備的流程，不再要求輸入 `dataId`
- 更新設備列表顯示格式
- 調整移除設備的索引計算（因為有4個預設設備）

### 3. `test-ws-client.js`
- 移除設備回報中的 `dataId` 欄位
- 添加註釋說明不包含 `dataId` 的原因

### 4. 文檔更新
- `Gateway-Device-Implementation-Guide.md`：更新設備回報格式和說明
- `Gateway-Device-Quick-Reference.md`：更新快速參考和常見問題

## 設備回報格式對比

### 修正前
```json
{
  "type": "deviceStatus",
  "devices": [
    {
      "macAddress": "11:22:33:44:55:66",
      "status": "online",
      "dataId": "TEST001",  // ❌ 不應該包含
      "data": {
        "size": "2.9\"",
        "battery": 85,
        "rssi": -65,
        "colorType": "BW"
      }
    }
  ]
}
```

### 修正後
```json
{
  "type": "deviceStatus",
  "devices": [
    {
      "macAddress": "11:22:33:44:55:66",
      "status": "online",
      "data": {
        "size": "2.9\"",
        "battery": 85,
        "rssi": -65,
        "colorType": "BW",
        "imageCode": "12345678"  // 可選，只有本地有時才包含
      }
    }
  ]
}
```

## 使用方法

### 1. 啟動程序
```bash
node server/tests/ws-client-from-copied-info.js
```

### 2. 主要命令
- `help` - 顯示命令幫助
- `add` - 添加自定義設備
- `list` - 列出所有模擬設備
- `remove <序號>` - 移除指定設備
- `request-image <MAC>` - 請求設備預覽圖像
- `q` - 退出程序

### 3. 修正後的特性
- 設備回報不包含 `dataId`（由前端或API控制）
- 設備回報格式符合最新 server 架構要求
- 自動處理來自 server 的圖像更新
- 本地存儲和管理設備的 `imageCode`

## 修正原因

1. **架構一致性**：`dataId` 是由前端或 API 管理的欄位，用於關聯設備與特定的數據項目
2. **職責分離**：裝置只負責回報自身的硬體狀態和基本信息，不負責管理數據關聯
3. **避免衝突**：防止裝置回報的 `dataId` 與系統管理的 `dataId` 產生衝突

## 影響範圍

- ✅ WebSocket 測試客戶端
- ✅ Gateway/Device 實作文檔
- ✅ 快速參考文檔
- ❌ 實際的 Gateway/Device 韌體（需要工程師根據文檔更新）

## 後續行動

Gateway/Device 工程師需要根據更新的文檔修正實際的韌體代碼，確保設備回報格式符合新的規範。

## 測試驗證

所有修正的測試客戶端都已經過驗證：
1. 設備回報不再包含 `dataId` 欄位
2. 測試客戶端的用戶界面已更新
3. 文檔已同步更新
4. 註釋已添加說明修正原因

## 注意事項

1. 確保 server 端已正確配置 WebSocket 服務
2. **重要**：必須先在 server 端使用腳本生成的 MAC 地址新增網關記錄
3. 圖像文件會自動保存到 `saved_images` 目錄
4. 程序會自動處理連接斷開和重連邏輯
5. imageCode 處理邏輯保持不變（只有本地有時才包含）
