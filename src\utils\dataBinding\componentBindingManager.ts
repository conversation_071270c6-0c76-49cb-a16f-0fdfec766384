import { DataField, TemplateElement } from "../../types";
import { bindingCore } from "./bindingCore";
import { TextBinding } from "./textBinding";

/**
 * 元件綁定管理器
 * 負責管理不同類型元件的綁定邏輯
 */
export class ComponentBindingManager {
  private static instance: ComponentBindingManager;
  private bindingHandlers: Record<string, any> = {};

  private constructor() {
    // 註冊各種元件類型的綁定處理器
    this.registerBindingHandlers();
  }

  /**
   * 獲取 ComponentBindingManager 的單例實例
   */
  public static getInstance(): ComponentBindingManager {
    if (!ComponentBindingManager.instance) {
      ComponentBindingManager.instance = new ComponentBindingManager();
    }
    return ComponentBindingManager.instance;
  }

  /**
   * 註冊各種元件類型的綁定處理器
   */
  private registerBindingHandlers(): void {
    // 註冊文字元件的綁定處理器
    this.bindingHandlers['text'] = TextBinding;
    this.bindingHandlers['multiline-text'] = TextBinding;
  }

  /**
   * 獲取元件的綁定處理器
   * @param elementType 元件類型
   * @returns 綁定處理器或null
   */
  public getBindingHandler(elementType: string): any {
    return this.bindingHandlers[elementType] || null;
  }

  /**
   * 檢查元件是否支援綁定
   * @param elementType 元件類型
   * @returns 是否支援綁定
   */
  public isBindingSupported(elementType: string): boolean {
    return !!this.bindingHandlers[elementType];
  }

  /**
   * 根據元件類型獲取可綁定的資料欄位
   * @param elementType 元件類型
   * @param fields 所有資料欄位
   * @returns 可綁定的資料欄位
   */
  public getBindableFieldsForElement(elementType: string, fields: DataField[]): DataField[] {
    const handler = this.getBindingHandler(elementType);
    if (!handler) return [];

    return handler.getBindableFields(fields);
  }

  /**
   * 處理元件綁定顯示
   * @param element 元件
   * @param fields 所有資料欄位
   * @param sampleData 範例數據
   * @returns 處理後的元件內容
   */
  public processElementBinding(
    element: TemplateElement, 
    fields: DataField[], 
    sampleData?: Record<string, any>
  ): TemplateElement {
    if (!bindingCore.isElementBound(element)) return element;

    const handler = this.getBindingHandler(element.type);
    if (!handler) return element;

    return handler.processBinding(element, fields, sampleData);
  }
}

// 導出元件綁定管理器的單例實例
export const componentBindingManager = ComponentBindingManager.getInstance();
