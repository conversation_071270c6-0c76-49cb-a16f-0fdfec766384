# 預覽圖生成服務實現方案

本文檔詳細說明了使用 Headless Browser 結合共享渲染邏輯模塊來實現預覽圖生成服務的方案。

## 目錄

1. [概述](#概述)
2. [架構設計](#架構設計)
3. [技術選擇](#技術選擇)
4. [實現細節](#實現細節)
5. [部署與配置](#部署與配置)
6. [測試計劃](#測試計劃)
7. [效能優化](#效能優化)
8. [擴展性考慮](#擴展性考慮)

## 概述

目前系統中的預覽圖生成依賴於前端渲染組件，服務端無法直接生成預覽圖。這導致在某些場景下（如自動更新、批量處理等）無法生成預覽圖。本方案旨在通過結合 Headless Browser 和共享渲染邏輯模塊，實現在服務端生成與前端完全一致的預覽圖。

### 現有問題

1. 服務端無法直接生成預覽圖，導致需要先在前端生成
2. 無法實現自動化的預覽圖更新
3. 渲染邏輯分散，前後端可能出現不一致

### 解決方案

1. 將核心渲染邏輯抽取為共享模塊
2. 在服務端使用 Headless Browser 執行這些共享邏輯
3. 建立獨立的預覽服務，提供 API 接口

## 架構設計

### 整體架構

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  前端應用       │     │  預覽服務       │     │  主服務器       │
│  (React)        │◄────┤  (Express)      │◄────┤  (Node.js)      │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        ▲                       ▲                       ▲
        │                       │                       │
        │                       │                       │
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                      共享渲染邏輯模塊                           │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 組件關係

1. **共享渲染邏輯模塊**：包含所有與渲染相關的核心邏輯
2. **前端應用**：使用共享模塊在瀏覽器中渲染預覽圖
3. **預覽服務**：使用 Headless Browser 和共享模塊在服務端渲染預覽圖
4. **主服務器**：處理業務邏輯，調用預覽服務生成預覽圖

## 技術選擇

### Headless Browser

推薦使用 **Puppeteer**，理由如下：

1. 穩定性高，由 Google Chrome 團隊維護
2. 與 Chrome/Chromium 完全兼容，渲染效果一致
3. API 簡潔，易於集成
4. 性能優良，資源佔用較低
5. 社區活躍，文檔完善

### 服務端框架

使用 **Express.js**，理由如下：

1. 輕量級，適合微服務架構
2. 與現有後端技術棧一致
3. 易於擴展和維護
4. 豐富的中間件生態

### 共享模塊打包工具

使用 **Rollup**，理由如下：

1. 適合打包庫和模塊
2. 支持 ESM 和 CommonJS 輸出
3. Tree-shaking 效果好，輸出代碼體積小

## 實現細節

### 1. 共享渲染邏輯模塊

將現有的渲染邏輯抽取為獨立模塊，確保前端和服務端可以共用：

```
shared/
  ├── components/
  │   └── PreviewRenderer.js     # 核心渲染組件
  ├── utils/
  │   ├── canvasUtils.js         # 畫布工具函數
  │   ├── previewUtils.js        # 預覽處理工具
  │   └── imageEffects.js        # 圖像效果處理
  └── index.js                   # 導出所有模塊
```

### 2. 預覽服務實現

```javascript
// preview-service/server.js
const express = require('express');
const puppeteer = require('puppeteer');
const { renderPreview } = require('../shared');

const app = express();
app.use(express.json({ limit: '50mb' }));

// 預覽圖生成API
app.post('/api/preview/generate', async (req, res) => {
  try {
    const { template, bindingData, storeData, effectType, threshold } = req.body;
    
    // 啟動Headless Browser
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // 注入共享渲染邏輯
    await page.addScriptTag({ content: `
      window.template = ${JSON.stringify(template)};
      window.bindingData = ${JSON.stringify(bindingData)};
      window.storeData = ${JSON.stringify(storeData)};
      window.effectType = "${effectType || 'blackAndWhite'}";
      window.threshold = ${threshold || 128};
    `});
    
    // 執行渲染
    const previewData = await page.evaluate(async () => {
      // 使用共享渲染邏輯
      return await window.renderPreview(
        window.template,
        window.bindingData,
        window.storeData,
        window.effectType,
        window.threshold
      );
    });
    
    await browser.close();
    
    // 返回預覽圖數據
    res.json({
      success: true,
      previewData
    });
  } catch (error) {
    console.error('預覽圖生成失敗:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

const PORT = process.env.PREVIEW_SERVICE_PORT || 3002;
app.listen(PORT, () => {
  console.log(`預覽服務運行在 http://localhost:${PORT}`);
});
```

### 3. 主服務器集成

```javascript
// server/services/previewService.js
const fetch = require('node-fetch');
const { buildEndpointUrl } = require('../utils/apiConfig');

// 預覽服務配置
const PREVIEW_SERVICE_URL = process.env.PREVIEW_SERVICE_URL || 'http://localhost:3002';

/**
 * 從預覽服務生成預覽圖
 */
async function generatePreviewFromService(template, bindingData, storeData, options = {}) {
  try {
    const response = await fetch(`${PREVIEW_SERVICE_URL}/api/preview/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        template,
        bindingData,
        storeData,
        effectType: options.effectType || 'blackAndWhite',
        threshold: options.threshold || 128
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`預覽服務錯誤: ${response.status} - ${errorText}`);
    }
    
    const result = await response.json();
    return result.previewData;
  } catch (error) {
    console.error('調用預覽服務失敗:', error);
    throw error;
  }
}

module.exports = {
  generatePreviewFromService
};
```

## 部署與配置

### Docker 部署

為確保 Headless Browser 在各環境中穩定運行，建議使用 Docker 部署預覽服務：

```dockerfile
FROM node:18-slim

# 安裝 Puppeteer 依賴
RUN apt-get update && apt-get install -y \
    chromium \
    fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-kacst fonts-freefont-ttf \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# 設置工作目錄
WORKDIR /app

# 複製依賴文件
COPY package*.json ./

# 安裝依賴
RUN npm ci

# 複製源代碼
COPY . .

# 設置環境變量
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium

# 暴露端口
EXPOSE 3002

# 啟動服務
CMD ["node", "preview-service/server.js"]
```

## 測試計劃

1. **單元測試**：測試共享渲染邏輯模塊的各個組件
2. **集成測試**：測試預覽服務的API接口
3. **比較測試**：比較前端和服務端生成的預覽圖是否一致
4. **性能測試**：測試預覽服務的響應時間和資源佔用
5. **負載測試**：測試預覽服務在高並發下的表現

## 效能優化

1. **瀏覽器實例池**：預先啟動多個瀏覽器實例，避免頻繁啟動和關閉
2. **緩存機制**：對相同參數的請求結果進行緩存
3. **資源限制**：限制每個請求的最大執行時間和內存使用
4. **並行處理**：支持多個請求並行處理
5. **壓縮傳輸**：對API請求和響應進行壓縮

## 擴展性考慮

1. **支持更多渲染選項**：如不同的效果、分辨率等
2. **批量處理**：支持一次請求生成多個預覽圖
3. **異步處理**：對於大量請求，支持異步處理和回調通知
4. **監控與日誌**：添加詳細的監控和日誌記錄
5. **錯誤處理**：完善的錯誤處理和重試機制
