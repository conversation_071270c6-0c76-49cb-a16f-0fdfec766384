// test-gateway-discovery.js
// 用於測試網關發現功能的腳本
// 此腳本模擬移動應用發送 UDP 廣播消息，並接收網關回應

const dgram = require('dgram');
const os = require('os');

// 配置
const UDP_PORT = 5000;
const BROADCAST_ADDRESS = '***************'; // 廣播地址
const PROTOCOL = "EPD-GATEWAY-DISCOVERY";
const VERSION = "1.0";
const SCAN_TIMEOUT = 10000; // 掃描超時時間（毫秒）

// 創建 UDP 客戶端
const client = dgram.createSocket('udp4');

// 發現的網關列表
const discoveredGateways = [];

// 處理錯誤
client.on('error', (err) => {
  console.error(`UDP 客戶端錯誤: ${err.stack}`);
  client.close();
});

// 處理消息
client.on('message', (msg, rinfo) => {
  try {
    // 解析 JSON 數據
    const response = JSON.parse(msg.toString());
    
    console.log(`收到來自 ${rinfo.address}:${rinfo.port} 的回應:`, response);
    
    // 驗證回應格式和協議
    if (response.type === 'discovery-response' && 
        response.protocol === PROTOCOL && 
        response.version === VERSION) {
      
      console.log('收到有效的網關回應');
      
      // 檢查是否已經發現該網關
      const existingIndex = discoveredGateways.findIndex(
        g => g.macAddress === response.macAddress
      );
      
      if (existingIndex === -1) {
        // 添加新發現的網關
        discoveredGateways.push({
          ...response,
          ipAddress: rinfo.address,
          port: rinfo.port
        });
        
        console.log(`發現新網關: ${response.name || 'Unknown'} (${response.macAddress})`);
      }
    } else {
      console.log('收到無效的回應，忽略');
    }
  } catch (error) {
    console.error(`處理回應時出錯: ${error}`);
  }
});

// 啟用廣播
client.bind(() => {
  client.setBroadcast(true);
  console.log('UDP 客戶端已啟動，準備發送廣播消息');
  
  // 發送廣播消息
  sendDiscoveryRequest();
  
  // 設置掃描超時
  setTimeout(() => {
    // 顯示掃描結果
    showScanResults();
    
    // 關閉客戶端
    client.close();
  }, SCAN_TIMEOUT);
});

// 發送發現請求
function sendDiscoveryRequest() {
  // 創建發現請求消息
  const discoveryRequest = {
    type: 'discovery',
    protocol: PROTOCOL,
    version: VERSION,
    timestamp: Date.now()
  };
  
  // 轉換為 Buffer
  const message = Buffer.from(JSON.stringify(discoveryRequest));
  
  // 發送廣播消息
  client.send(message, 0, message.length, UDP_PORT, BROADCAST_ADDRESS, (err) => {
    if (err) {
      console.error(`發送廣播消息時出錯: ${err}`);
    } else {
      console.log('已發送廣播消息:', discoveryRequest);
      console.log(`等待網關回應 (${SCAN_TIMEOUT / 1000} 秒超時)...`);
    }
  });
}

// 顯示掃描結果
function showScanResults() {
  console.log('\n--- 掃描結果 ---');
  
  if (discoveredGateways.length === 0) {
    console.log('未發現任何網關');
  } else {
    console.log(`發現 ${discoveredGateways.length} 個網關:`);
    
    discoveredGateways.forEach((gateway, index) => {
      console.log(`\n${index + 1}. ${gateway.name || 'Unknown Gateway'}`);
      console.log(`   MAC 地址: ${gateway.macAddress}`);
      console.log(`   IP 地址: ${gateway.ipAddress}`);
      console.log(`   型號: ${gateway.model || 'Unknown'}`);
      console.log(`   固件版本: ${gateway.firmwareVersion || 'Unknown'}`);
      
      if (gateway.capabilities && gateway.capabilities.length > 0) {
        console.log(`   功能: ${gateway.capabilities.join(', ')}`);
      }
      
      console.log(`   狀態: ${gateway.status || 'Unknown'}`);
    });
  }
  
  console.log('\n掃描完成');
}

// 處理進程終止
process.on('SIGINT', () => {
  console.log('測試腳本已停止');
  client.close();
  process.exit();
});

console.log('啟動網關掃描測試...');
