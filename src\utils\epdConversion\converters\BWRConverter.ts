import { BaseConverter } from './BaseConverter';
import { EPDConversionOptions, PixelData } from '../types';
import { DisplayColorType } from '../../../types';
import { getColorPalette, findExactColorMatch } from '../utils/colorPalettes';

/**
 * BWR 轉換器
 * 2 tables, 1bit per pixel, 寬度需為8的倍數
 */
export class BWRConverter extends BaseConverter {
  private buffer2: Uint8Array; // 紅色數據表
  private index: number = 0;

  constructor(options: EPDConversionOptions) {
    super(options);
    this.buffer2 = new Uint8Array(this.calculateBufferSize());
  }

  calculatePaddedWidth(): number {
    // 寬度必須是8的倍數 (1bit per pixel)
    return Math.ceil(this.width / 8) * 8;
  }

  calculateBufferSize(): number {
    // 每8個像素佔用1個字節，需要2個表格
    return Math.ceil((this.paddedWidth * this.height) / 8);
  }

  protected getBytesPerPixel(): number {
    return 0.25; // 1bit per pixel, 但有2個表格
  }

  processPixel(x: number, _y: number, pixel: PixelData): void {
    const { isRed, isWhite } = this.analyzeColor(pixel);
    const bitMask = 0x80 >> (x & 7);

    if (isRed) {
      // 紅色：兩個buffer都設為0 (已經初始化為0)
    } else {
      this.buffer2[this.index] |= bitMask;
      if (isWhite) {
        this.buffer[this.index] |= bitMask;
      }
    }

    // 當處理完一個字節或到達行尾時，移動到下一個字節
    if ((bitMask === 1) || ((x + 1) === this.paddedWidth)) {
      this.index++;
    }
  }

  private analyzeColor(pixel: PixelData): { isRed: boolean; isWhite: boolean } {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BWR);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 根據匹配的顏色判斷類型
    const isRed = matchedColor.r === 255 && matchedColor.g === 0 && matchedColor.b === 0;
    const isWhite = matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 255;

    return { isRed, isWhite };
  }

  getPixelData(): Uint8Array {
    // 合併兩個表格
    const result = new Uint8Array(this.buffer.length * 2);
    result.set(this.buffer, 0);
    result.set(this.buffer2, this.buffer.length);
    return result;
  }
}
