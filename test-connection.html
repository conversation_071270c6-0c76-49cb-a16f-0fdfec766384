<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EPD Manager 連線測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .url-input {
            width: 100%;
            padding: 8px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>EPD Manager 連線測試工具</h1>

        <div class="test-section">
            <h3>1. 瀏覽器兼容性檢查</h3>
            <div id="browser-status" class="status info">檢查中...</div>
            <button onclick="checkBrowserCompatibility()">重新檢查</button>
        </div>

        <div class="test-section">
            <h3>2. 服務器連線測試</h3>
            <label for="server-url">服務器地址:</label>
            <input type="text" id="server-url" class="url-input" placeholder="http://localhost:3001">
            <button onclick="testServerConnection()">測試連線</button>
            <button onclick="autoDetectServer()">自動檢測</button>
            <div id="server-status" class="status info">等待測試...</div>
        </div>

        <div class="test-section">
            <h3>3. API 端點測試</h3>
            <button onclick="testApiEndpoints()">測試 API</button>
            <div id="api-status" class="status info">等待測試...</div>
        </div>

        <div class="test-section">
            <h3>4. 測試日誌</h3>
            <button onclick="clearLog()">清除日誌</button>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script>
        let currentServerUrl = 'http://localhost:3001';

        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function checkBrowserCompatibility() {
            log('開始檢查瀏覽器兼容性...');

            const checks = [];

            // 檢查 crypto API
            if (window.crypto && window.crypto.randomUUID) {
                checks.push('✓ crypto.randomUUID 支援');
            } else {
                checks.push('✗ crypto.randomUUID 不支援');
            }

            // 檢查 fetch API
            if (window.fetch) {
                checks.push('✓ Fetch API 支援');
            } else {
                checks.push('✗ Fetch API 不支援');
            }

            // 檢查 WebSocket
            if (window.WebSocket) {
                checks.push('✓ WebSocket 支援');
            } else {
                checks.push('✗ WebSocket 不支援');
            }

            const hasIssues = checks.some(check => check.includes('✗'));
            const status = hasIssues ? 'error' : 'success';
            const message = checks.join(', ');

            updateStatus('browser-status', message, status);
            log(`瀏覽器兼容性檢查完成: ${message}`);
        }

        async function testServerConnection() {
            const urlInput = document.getElementById('server-url');
            const testUrl = urlInput.value || currentServerUrl;

            log(`測試服務器連線: ${testUrl}`);
            updateStatus('server-status', '連線中...', 'info');

            try {
                const response = await fetch(`${testUrl}/dbtest`, {
                    method: 'GET',
                    mode: 'cors'
                });

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('server-status', `連線成功! 狀態: ${data.status}`, 'success');
                    log(`服務器連線成功: ${JSON.stringify(data)}`);
                    currentServerUrl = testUrl;
                } else {
                    updateStatus('server-status', `連線失敗: HTTP ${response.status}`, 'error');
                    log(`服務器連線失敗: HTTP ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                updateStatus('server-status', `連線錯誤: ${error.message}`, 'error');
                log(`服務器連線錯誤: ${error.message}`);
            }
        }

        async function autoDetectServer() {
            log('開始自動檢測服務器...');
            updateStatus('server-status', '自動檢測中...', 'info');

            const hostname = window.location.hostname;
            const testUrls = [
                `http://localhost:3001`,
                `http://127.0.0.1:3001`,
                `http://${hostname}:3001`
            ];

            for (const url of testUrls) {
                try {
                    log(`嘗試連線: ${url}`);
                    const response = await fetch(`${url}/dbtest`, {
                        method: 'GET',
                        mode: 'cors'
                    });

                    if (response.ok) {
                        const data = await response.json();
                        updateStatus('server-status', `自動檢測成功! 服務器: ${url}`, 'success');
                        log(`自動檢測成功: ${url} - ${JSON.stringify(data)}`);
                        document.getElementById('server-url').value = url;
                        currentServerUrl = url;
                        return;
                    }
                } catch (error) {
                    log(`${url} 連線失敗: ${error.message}`);
                }
            }

            updateStatus('server-status', '自動檢測失敗，請手動輸入服務器地址', 'error');
            log('所有自動檢測嘗試都失敗了');
        }

        async function testApiEndpoints() {
            log('開始測試 API 端點...');
            updateStatus('api-status', '測試中...', 'info');

            const endpoints = [
                { path: '/api/auth/init-check', needsCredentials: false },
                { path: '/api/configs/system', needsCredentials: true },
                { path: '/api/auth/check', needsCredentials: true }
            ];

            const results = [];

            for (const endpoint of endpoints) {
                try {
                    log(`測試端點: ${currentServerUrl}${endpoint.path} (credentials: ${endpoint.needsCredentials})`);

                    const fetchOptions = {
                        method: 'GET',
                        mode: 'cors',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    };

                    if (endpoint.needsCredentials) {
                        fetchOptions.credentials = 'include';
                    }

                    const response = await fetch(`${currentServerUrl}${endpoint.path}`, fetchOptions);

                    if (response.ok) {
                        results.push(`✓ ${endpoint.path}`);
                        log(`${endpoint.path} 測試成功`);
                    } else {
                        results.push(`✗ ${endpoint.path} (${response.status})`);
                        log(`${endpoint.path} 測試失敗: HTTP ${response.status}`);
                    }
                } catch (error) {
                    results.push(`✗ ${endpoint.path} (錯誤)`);
                    log(`${endpoint.path} 測試錯誤: ${error.message}`);

                    // 檢查是否為 CORS 錯誤
                    if (error.message.includes('CORS') || error.message.includes('Failed to fetch')) {
                        log(`可能的 CORS 問題，檢查服務器 CORS 設置`);
                    }
                }
            }

            const hasErrors = results.some(result => result.includes('✗'));
            const status = hasErrors ? 'error' : 'success';
            const message = results.join(', ');

            updateStatus('api-status', message, status);
            log(`API 端點測試完成: ${message}`);
        }

        // 頁面載入時自動執行檢查
        window.addEventListener('load', () => {
            log('頁面載入完成，開始初始化檢查...');
            checkBrowserCompatibility();

            // 設置默認服務器 URL
            const hostname = window.location.hostname;
            if (hostname !== 'localhost' && hostname !== '127.0.0.1') {
                document.getElementById('server-url').value = `http://${hostname}:3001`;
            } else {
                document.getElementById('server-url').value = 'http://localhost:3001';
            }
        });
    </script>
</body>
</html>
