// 使用內建的 fetch (Node.js 18+)

async function testRefreshPlanApi() {
  try {
    console.log('🧪 測試刷圖計畫 API');
    
    // 1. 登入獲取 token
    console.log('📝 正在登入...');
    const loginRes = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'root', password: '12345689' })
    });
    
    const loginData = await loginRes.json();
    console.log('登入結果:', loginRes.status, loginData.success ? '成功' : '失敗');
    
    if (!loginData.success || !loginData.token) {
      console.error('❌ 登入失敗:', loginData.error);
      return;
    }
    
    const token = loginData.token;
    console.log('✅ 獲取到 token');
    
    // 2. 測試獲取刷圖計畫列表
    console.log('\n📋 測試獲取刷圖計畫列表...');
    const plansRes = await fetch('http://localhost:3001/api/stores/KH001/refresh-plans', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('API 狀態碼:', plansRes.status);
    console.log('API 響應頭:', Object.fromEntries(plansRes.headers.entries()));

    const plansData = await plansRes.text(); // 先獲取文本
    console.log('原始響應:', plansData.substring(0, 500));

    try {
      const parsedData = JSON.parse(plansData);
      console.log('✅ 成功解析 JSON');
      console.log('響應結構:', {
        success: parsedData.success,
        hasData: !!parsedData.data,
        plansCount: parsedData.data?.plans?.length || 0,
        total: parsedData.data?.total || 0
      });

      if (parsedData.data?.plans?.length > 0) {
        console.log('第一個計畫:', parsedData.data.plans[0].name);
      } else {
        console.log('沒有找到計畫，這是正常的（新系統）');
      }
    } catch (parseError) {
      console.error('❌ JSON 解析失敗:', parseError.message);
      console.log('響應內容類型:', plansRes.headers.get('content-type'));

      // 檢查是否是 HTML 響應
      if (plansData.includes('<!doctype') || plansData.includes('<html')) {
        console.error('❌ 收到 HTML 響應而不是 JSON，可能是路由問題');
      }
    }
    
  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testRefreshPlanApi();
