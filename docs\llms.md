# 架構圖
```mermaid
graph TD
    %% 主要應用結構
    Root[epd-manager] --> Server[server/]
    Root --> Client[client/]
    
    %% 服務端文件結構
    Server --> Routes[routes/]
    Server --> Tests[tests/]
    Server --> AppFile[app.js]
    Server --> DBFile[database.js]
    Server --> PackageFile[package.json]
    
    %% API 路由文件
    Routes --> StoreDataApiFile[storeDataApi.js]
    Routes --> SystemConfigApiFile[systemConfigApi.js]
    Routes --> TemplatesApiFile[templatesApi.js]
    Routes --> TemplateValidationApiFile[templateValidationApi.js]
    
    %% 測試文件結構
    Tests --> Helpers[helpers/]
    Tests --> StoreDataTest[storeDataApi.test.js]
    Tests --> SystemConfigTest[systemConfigApi.test.js]
    Tests --> TemplatesTest[templatesApi.test.js]
    Tests --> TemplateValidationTest[templateValidationApi.test.js]
    
    %% 測試輔助文件
    Helpers --> MockDBFile[mockDb.js]
    Helpers --> MockStoreApi[mockStoreDataApi.js]
    Helpers --> MockSystemConfigApi[mockSystemConfigApi.js]
    Helpers --> MockTemplatesApi[mockTemplatesApi.js]
    Helpers --> MockTemplateValidationApi[mockTemplateValidationApi.js]
    
    %% 功能連接
    StoreDataApiFile -- API調用 --> MongoDB[(MongoDB)]
    SystemConfigApiFile -- API調用 --> MongoDB
    TemplatesApiFile -- API調用 --> MongoDB
    TemplateValidationApiFile -- 使用 --> Handlebars[Handlebars]
    
    %% 數據集合
    MongoDB --> StoresCollection[(商店資料集合)]
    MongoDB --> ConfigCollection[(系統配置集合)]
    MongoDB --> TemplatesCollection[(模板集合)]
    
    %% 測試連接
    StoreDataTest -- 測試 --> StoreDataApiFile
    SystemConfigTest -- 測試 --> SystemConfigApiFile
    TemplatesTest -- 測試 --> TemplatesApiFile
    TemplateValidationTest -- 測試 --> TemplateValidationApiFile

    %% 設定不同區塊的顏色
    classDef rootClass fill:#f9d5e5,stroke:#333,stroke-width:1px;
    classDef serverClass fill:#d3f0ee,stroke:#333,stroke-width:1px;
    classDef clientClass fill:#eeeeee,stroke:#333,stroke-width:1px;
    classDef routesClass fill:#b5ead7,stroke:#333,stroke-width:1px;
    classDef testsClass fill:#ffdac1,stroke:#333,stroke-width:1px;
    classDef helpersClass fill:#c7ceea,stroke:#333,stroke-width:1px;
    classDef dbClass fill:#ff9aa2,stroke:#333,stroke-width:1px;
    classDef handlebarsClass fill:#e2f0cb,stroke:#333,stroke-width:1px;

    %% 應用顏色分類
    class Root rootClass;
    class Server,AppFile,DBFile,PackageFile serverClass;
    class Client clientClass;
    class Routes,StoreDataApiFile,SystemConfigApiFile,TemplatesApiFile,TemplateValidationApiFile routesClass;
    class Tests,StoreDataTest,SystemConfigTest,TemplatesTest,TemplateValidationTest testsClass;
    class Helpers,MockDBFile,MockStoreApi,MockSystemConfigApi,MockTemplatesApi,MockTemplateValidationApi helpersClass;
    class MongoDB,StoresCollection,ConfigCollection,TemplatesCollection dbClass;
    class Handlebars handlebarsClass;
```

## 顏色區塊說明

    淺藍系：服務端主要檔案 (server/ 目錄及其主要檔案)

    淺綠系：API 路由相關檔案 (routes/ 目錄及其檔案)

    橘色系：測試檔案 (tests/ 目錄及測試檔案)

    藍紫色系：測試輔助檔案 (helpers/ 目錄及其檔案)

    淺紅系：資料庫相關 (MongoDB 及其集合)

    淺黃綠系：模板引擎 (Handlebars)
