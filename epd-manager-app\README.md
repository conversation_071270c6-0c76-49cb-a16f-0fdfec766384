# EPD Manager App

EPD Manager App 是一個基於 React Native 開發的移動應用程序，用於管理電子紙顯示器系統。本應用程序提供了用戶認證、門店管理、網關管理、設備管理和 WebSocket 通信等功能，使用戶能夠在移動設備上方便地管理和監控電子紙顯示系統。

## 功能特點

- **用戶認證**: 安全的登入系統，支持記住登入狀態
- **門店管理**: 查看和選擇門店
- **網關管理**: 查看、創建和管理網關設備
- **設備管理**: 添加、移除和監控電子紙顯示器
- **WebSocket 通信**: 實時與後端服務器通信，監控設備狀態
- **圖像處理**: 請求、顯示和保存設備預覽圖像

## 技術棧

- **React Native**: 跨平台移動應用開發框架
- **Expo**: 簡化 React Native 開發的工具和服務
- **Redux**: 應用狀態管理
- **React Navigation**: 頁面導航
- **Axios**: HTTP 請求
- **WebSocket**: 實時通信
- **AsyncStorage**: 本地數據存儲

## 開始使用

### 前提條件

- Node.js (v14 或更高版本)
- npm 或 yarn
- Expo CLI
- Android Studio (Android 開發) 或 Xcode (iOS 開發)

### 安裝

1. 克隆倉庫:

```bash
git clone https://github.com/your-username/epd-manager-app.git
cd epd-manager-app
```

2. 安裝依賴:

```bash
npm install
# 或
yarn install
```

3. 啟動開發服務器:

```bash
npm start
# 或
yarn start
```

4. 使用 Expo Go 應用掃描 QR 碼在真機上運行，或使用模擬器運行。

### 配置

在 `src/constants/api.js` 文件中配置 API 和 WebSocket 服務器地址:

```javascript
export const API_CONFIG = {
  host: 'localhost',  // 修改為你的服務器地址
  port: 3001,         // 修改為你的服務器端口
  useHttps: false     // 是否使用 HTTPS
};

export const WS_CONFIG = {
  host: 'localhost',  // 修改為你的 WebSocket 服務器地址
  port: 3001,         // 修改為你的 WebSocket 服務器端口
  useWss: false       // 是否使用 WSS (WebSocket Secure)
};
```

## 項目結構

```
epd-manager-app/
├── assets/                  # 靜態資源文件
├── src/                     # 源代碼
│   ├── api/                 # API 相關
│   ├── components/          # 可重用組件
│   ├── navigation/          # 導航相關
│   ├── screens/             # 頁面
│   ├── services/            # 服務
│   ├── store/               # Redux 狀態管理
│   ├── utils/               # 工具函數
│   ├── constants/           # 常量
│   ├── hooks/               # 自定義 Hooks
│   ├── localization/        # 國際化
│   ├── theme/               # 主題
│   ├── App.js               # 應用入口
│   └── index.js             # 根組件
├── docs/                    # 文檔
└── ...                      # 其他配置文件
```

## 文檔

詳細的文檔可以在 `docs/` 目錄中找到:

- [實現計劃](docs/app-implementation-plan.md): 詳細的實現計劃和設計細節
- [通信流程](docs/communication-flow.md): 應用程序的通信流程和架構
- [專案結構](docs/project-structure.md): 專案的目錄結構和主要組件說明
- [UI 設計](docs/ui-design.md): 應用程序的 UI 設計和用戶體驗

## 開發指南

### 添加新頁面

1. 在 `src/screens/` 目錄下創建新的頁面組件
2. 在 `src/navigation/` 中添加新頁面的路由
3. 如果需要，在 Redux 中添加相關的狀態管理

### 添加新 API

1. 在 `src/api/` 目錄下添加新的 API 函數
2. 在 `src/constants/api.js` 中添加相關的 API 端點
3. 在相關的頁面或服務中使用新 API

### 添加新組件

1. 在 `src/components/` 目錄下創建新的組件
2. 確保組件遵循項目的設計規範
3. 在相關的頁面中使用新組件

## 測試

### 單元測試

```bash
npm test
# 或
yarn test
```

### 端到端測試

```bash
npm run e2e
# 或
yarn e2e
```

## 構建和發布

### Android

```bash
expo build:android
```

### iOS

```bash
expo build:ios
```

## 貢獻

1. Fork 倉庫
2. 創建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 創建 Pull Request

## 許可證

本項目採用 MIT 許可證 - 詳見 [LICENSE](LICENSE) 文件。

## 聯繫方式

如有任何問題或建議，請聯繫項目維護者:

- 電子郵件: <EMAIL>
- GitHub: [your-username](https://github.com/your-username)

## 致謝

- [React Native](https://reactnative.dev/)
- [Expo](https://expo.dev/)
- [Redux](https://redux.js.org/)
- [React Navigation](https://reactnavigation.org/)
- 所有貢獻者和使用者
