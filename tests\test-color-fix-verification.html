<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文字顏色修復驗證</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .canvas-container {
            border: 1px solid #ccc;
            background: white;
            position: relative;
            width: 400px;
            height: 300px;
            margin: 20px 0;
        }
        .text-element-content {
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>文字顏色修復驗證測試</h1>
    <p>這個測試用來驗證修復後的 canvasUtils.tsx 是否正確保留文字顏色。</p>

    <div class="test-container">
        <h2>測試場景</h2>
        <p>模擬 renderCanvasToImage 函數中對綁定文字元素的處理，驗證顏色是否被正確保留。</p>
        
        <div class="canvas-container" id="testCanvas">
            <!-- 測試文字元素 1: 紅色文字，有綁定 -->
            <div data-element-type="text" data-has-binding="true" data-element-id="1">
                <div class="text-element-content" style="
                    left: 10px; 
                    top: 10px; 
                    width: 150px; 
                    height: 40px;
                    fontSize: 20px;
                    fontFamily: Arial;
                    color: rgb(255, 0, 0);
                ">紅色綁定文字</div>
            </div>
            
            <!-- 測試文字元素 2: 藍色文字，有綁定 -->
            <div data-element-type="text" data-has-binding="true" data-element-id="2">
                <div class="text-element-content" style="
                    left: 10px; 
                    top: 60px; 
                    width: 150px; 
                    height: 30px;
                    fontSize: 16px;
                    fontFamily: Georgia;
                    color: rgb(0, 0, 255);
                ">藍色綁定文字</div>
            </div>
            
            <!-- 測試文字元素 3: 綠色文字，有綁定 -->
            <div data-element-type="text" data-has-binding="true" data-element-id="3">
                <div class="text-element-content" style="
                    left: 10px; 
                    top: 110px; 
                    width: 150px; 
                    height: 35px;
                    fontSize: 18px;
                    fontFamily: Times;
                    color: rgb(0, 128, 0);
                ">綠色綁定文字</div>
            </div>

            <!-- 測試文字元素 4: 無顏色設置，有綁定 -->
            <div data-element-type="text" data-has-binding="true" data-element-id="4">
                <div class="text-element-content" style="
                    left: 10px; 
                    top: 160px; 
                    width: 150px; 
                    height: 30px;
                    fontSize: 14px;
                    fontFamily: Arial;
                ">無顏色設置文字</div>
            </div>

            <!-- 測試文字元素 5: 透明顏色，有綁定 -->
            <div data-element-type="text" data-has-binding="true" data-element-id="5">
                <div class="text-element-content" style="
                    left: 10px; 
                    top: 210px; 
                    width: 150px; 
                    height: 30px;
                    fontSize: 14px;
                    fontFamily: Arial;
                    color: transparent;
                ">透明顏色文字</div>
            </div>
        </div>

        <button class="test-button" onclick="runColorPreservationTest()">執行顏色保留測試</button>
        <button class="test-button" onclick="clearResults()">清除結果</button>
        
        <div id="testResults"></div>
    </div>

    <script>
        // 模擬修復後的顏色處理邏輯
        function simulateFixedColorHandling(textContainer, hasBoundDataField) {
            if (hasBoundDataField) {
                console.log('處理綁定數據的文字元件');

                // 修復後的邏輯：保留原始顏色，不強制設置為黑色
                if (!textContainer.style.color || textContainer.style.color === 'transparent') {
                    textContainer.style.color = 'black'; // 只有在沒有顏色或透明時才設置為黑色
                }
                textContainer.style.backgroundColor = 'transparent';
                textContainer.style.border = 'none';
                textContainer.style.textOverflow = 'visible';
                textContainer.style.overflow = 'visible';
                textContainer.style.fontWeight = 'normal';
            }
        }

        // 模擬修復前的錯誤邏輯（用於對比）
        function simulateOldBuggyColorHandling(textContainer, hasBoundDataField) {
            if (hasBoundDataField) {
                console.log('處理綁定數據的文字元件（舊版本）');

                // 舊版本的錯誤邏輯：強制設置為黑色
                textContainer.style.color = 'black'; // 這會覆蓋原始顏色
                textContainer.style.backgroundColor = 'transparent';
                textContainer.style.border = 'none';
                textContainer.style.textOverflow = 'visible';
                textContainer.style.overflow = 'visible';
                textContainer.style.fontWeight = 'normal';
            }
        }

        function runColorPreservationTest() {
            const resultsDiv = document.getElementById('testResults');
            let results = '<h3>顏色保留測試結果</h3>';

            try {
                // 獲取所有測試元素
                const testElements = document.querySelectorAll('[data-element-id]');
                
                results += `<div class="result info">找到 ${testElements.length} 個測試元素</div>`;

                let passedTests = 0;
                let totalTests = 0;

                testElements.forEach((element, index) => {
                    const textContainer = element.querySelector('.text-element-content');
                    const hasBoundDataField = element.getAttribute('data-has-binding') === 'true';
                    
                    if (textContainer) {
                        totalTests++;
                        
                        // 記錄原始顏色
                        const originalColor = textContainer.style.color;
                        const elementId = element.getAttribute('data-element-id');
                        
                        results += `<div class="result info">元素 ${elementId}: 原始顏色 = "${originalColor}"</div>`;

                        // 創建副本來測試修復後的邏輯
                        const testContainer = textContainer.cloneNode(true);
                        simulateFixedColorHandling(testContainer, hasBoundDataField);
                        
                        const newColor = testContainer.style.color;
                        
                        // 檢查顏色是否被正確保留
                        let testPassed = false;
                        let expectedBehavior = '';
                        
                        if (!originalColor || originalColor === 'transparent') {
                            // 沒有顏色或透明色應該被設置為黑色
                            testPassed = newColor === 'black';
                            expectedBehavior = '應該設置為黑色';
                        } else {
                            // 有顏色的應該保留原始顏色
                            testPassed = newColor === originalColor;
                            expectedBehavior = '應該保留原始顏色';
                        }

                        if (testPassed) {
                            passedTests++;
                            results += `<div class="result success">✓ 元素 ${elementId}: ${expectedBehavior} - 通過 (結果: "${newColor}")</div>`;
                        } else {
                            results += `<div class="result error">✗ 元素 ${elementId}: ${expectedBehavior} - 失敗 (期望: "${originalColor || 'black'}", 實際: "${newColor}")</div>`;
                        }

                        // 對比舊版本的錯誤行為
                        const oldTestContainer = textContainer.cloneNode(true);
                        simulateOldBuggyColorHandling(oldTestContainer, hasBoundDataField);
                        const oldColor = oldTestContainer.style.color;
                        
                        if (originalColor && originalColor !== 'transparent' && oldColor === 'black') {
                            results += `<div class="result info">  舊版本會錯誤地將此顏色強制改為黑色</div>`;
                        }
                    }
                });

                // 總結
                if (passedTests === totalTests) {
                    results += `<div class="result success"><strong>✓ 所有測試通過！(${passedTests}/${totalTests})</strong></div>`;
                    results += `<div class="result success">修復成功：文字顏色現在能正確保留！</div>`;
                } else {
                    results += `<div class="result error"><strong>✗ 部分測試失敗 (${passedTests}/${totalTests})</strong></div>`;
                }

            } catch (error) {
                results += `<div class="result error">測試執行錯誤: ${error.message}</div>`;
            }

            resultsDiv.innerHTML = results;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        // 頁面加載時自動運行測試
        window.addEventListener('load', () => {
            setTimeout(runColorPreservationTest, 500);
        });
    </script>
</body>
</html>
