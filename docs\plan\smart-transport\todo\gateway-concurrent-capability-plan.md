# Gateway 併發能力通報與動態調整規劃

## 概述

本規劃旨在讓每個 Gateway 通報自身同時可接收的 chunk 傳圖併發數，使 Gateway 不再限制於同時只能處理一個傳圖任務，而是根據其硬體能力動態調整。Server 端的忙碌判斷邏輯也將相應調整，以 Gateway 回報的能力為標準。

## 當前狀況分析

### 現有限制
1. **單一併發限制**：每個 Gateway 同時只能處理 1 個傳圖任務
2. **硬編碼判斷**：Server 端忙碌判斷邏輯固定，無法適應不同 Gateway 能力
3. **資源浪費**：高性能 Gateway 無法充分利用其處理能力
4. **擴展性差**：無法適應不同硬體規格的 Gateway

### 問題根源
```javascript
// 當前實現 - server/services/websocketService.js
const isGatewayBusyWithChunk = (gatewayId) => {
  const gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
  // 只要有任何活躍傳輸就視為忙碌
  return hasActiveTransmission; // 硬編碼為 1 個任務的限制
};
```

## 規劃目標

### 1. 動態併發能力
- Gateway 可通報自身最大併發處理能力
- Server 根據 Gateway 能力動態分配任務
- 支援不同硬體規格的 Gateway

### 2. 智能負載分配
- 高性能 Gateway 可同時處理多個任務
- 低性能 Gateway 維持單任務處理
- 動態調整系統總併發數

### 3. 向後兼容
- 未通報併發能力的 Gateway 默認為 1
- 現有 Gateway 無需強制升級
- 漸進式部署支援

## 技術實現規劃

### 階段一：Gateway 能力通報機制

#### 1.1 Gateway 能力結構擴展

```javascript
// Gateway 連接時發送的 gatewayInfo 訊息擴展
const gatewayInfo = {
  type: 'gatewayInfo',
  info: {
    macAddress: 'AA:BB:CC:DD:EE:FF',
    model: 'ESP32-Gateway-V3',
    wifiFirmwareVersion: '3.0.0',
    btFirmwareVersion: '2.0.0',
    ipAddress: '*************',
    
    // 現有分片傳輸能力
    chunkingSupport: {
      enabled: true,
      maxChunkSize: 200,
      maxSingleMessageSize: 10240,
      embeddedIndex: true,
      jsonHeader: true
    },
    
    // 新增：併發處理能力 (v3.0)
    concurrencyCapability: {
      maxConcurrentDevices: 2           // 最大同時處理設備數
    }
  }
};
```

#### 1.2 Server 端能力存儲

```javascript
// server/services/websocketService.js 擴展
// 存儲 Gateway 併發能力信息
const gatewayConcurrencyCapabilities = new Map(); // gatewayId -> concurrencyInfo

// 處理 Gateway 能力通報
const handleGatewayCapabilityReport = (gatewayId, capabilities) => {
  const concurrencyInfo = {
    maxConcurrentDevices: capabilities.maxConcurrentDevices || 1,
    lastUpdated: Date.now(),

    // 運行時統計
    currentActiveDevices: 0
  };

  gatewayConcurrencyCapabilities.set(gatewayId, concurrencyInfo);
  console.log(`🔧 Gateway ${gatewayId} 併發能力已更新: 最大併發設備數=${concurrencyInfo.maxConcurrentDevices}`);
};
```

### 階段二：動態忙碌判斷邏輯

#### 2.1 新的忙碌判斷機制

```javascript
// 新的 Gateway 忙碌判斷邏輯
const isGatewayBusyWithConcurrency = (gatewayId) => {
  const concurrencyInfo = gatewayConcurrencyCapabilities.get(gatewayId);

  // 如果沒有併發能力信息，使用舊邏輯（向後兼容）
  if (!concurrencyInfo) {
    return isGatewayBusyWithChunk(gatewayId); // 舊邏輯：任何傳輸都視為忙碌
  }

  const currentActiveDevices = getCurrentActiveDeviceCount(gatewayId);
  const maxConcurrentDevices = concurrencyInfo.maxConcurrentDevices;

  // 基於併發設備數判斷
  const isBusy = currentActiveDevices >= maxConcurrentDevices;

  console.log(`🔍 Gateway ${gatewayId} 併發狀態檢查: ${currentActiveDevices}/${maxConcurrentDevices} 設備, 忙碌=${isBusy}`);

  return isBusy;
};

// 檢查 Gateway 是否可以接受新任務
const canGatewayAcceptNewTask = (gatewayId) => {
  const concurrencyInfo = gatewayConcurrencyCapabilities.get(gatewayId);

  if (!concurrencyInfo) {
    // 舊版 Gateway，使用原有邏輯
    return !isGatewayBusyWithChunk(gatewayId);
  }

  // 檢查設備數量限制
  const currentDevices = getCurrentActiveDeviceCount(gatewayId);
  const canAccept = currentDevices < concurrencyInfo.maxConcurrentDevices;

  console.log(`🔍 Gateway ${gatewayId} 任務接受檢查: ${currentDevices}/${concurrencyInfo.maxConcurrentDevices} 設備, 可接受=${canAccept}`);

  return canAccept;
};
```

#### 2.2 增強的狀態追蹤

```javascript
// 增強的傳輸狀態追蹤
const startConcurrentChunkTransmission = (gatewayId, chunkId, deviceMac, deviceId) => {
  const transmission = {
    chunkId,
    deviceMac,
    deviceId,
    startTime: Date.now(),
    status: 'active'
  };

  // 原有邏輯
  let gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
  if (!gatewayTransmissions) {
    gatewayTransmissions = new Map();
    activeChunkTransmissions.set(gatewayId, gatewayTransmissions);
  }

  gatewayTransmissions.set(chunkId, transmission);

  // 更新 Gateway 併發統計
  const concurrencyInfo = gatewayConcurrencyCapabilities.get(gatewayId);
  if (concurrencyInfo) {
    const uniqueDevices = getUniqueActiveDevices(gatewayId);
    concurrencyInfo.currentActiveDevices = uniqueDevices.size;
  }

  const currentDevices = getCurrentActiveDeviceCount(gatewayId);
  console.log(`🚀 Gateway ${gatewayId} 開始併發傳輸: ${chunkId}, 當前設備數: ${currentDevices}`);
};

const endConcurrentChunkTransmission = (gatewayId, chunkId) => {
  const gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
  if (!gatewayTransmissions || !gatewayTransmissions.has(chunkId)) {
    return;
  }

  const transmission = gatewayTransmissions.get(chunkId);
  const duration = Date.now() - transmission.startTime;

  gatewayTransmissions.delete(chunkId);

  // 更新 Gateway 併發統計
  const concurrencyInfo = gatewayConcurrencyCapabilities.get(gatewayId);
  if (concurrencyInfo) {
    const uniqueDevices = getUniqueActiveDevices(gatewayId);
    concurrencyInfo.currentActiveDevices = uniqueDevices.size;
  }

  const currentDevices = getCurrentActiveDeviceCount(gatewayId);
  console.log(`✅ Gateway ${gatewayId} 完成併發傳輸: ${chunkId}, 耗時: ${duration}ms, 當前設備數: ${currentDevices}`);

  // 如果沒有任何傳輸了，清理網關記錄並發射事件
  if (gatewayTransmissions.size === 0) {
    activeChunkTransmissions.delete(gatewayId);
    console.log(`🔔 Gateway ${gatewayId} 所有併發傳輸已完成`);
    gatewayStatusEmitter.emit('gatewayAvailable', gatewayId);
  } else if (concurrencyInfo && currentDevices < concurrencyInfo.maxConcurrentDevices) {
    // Gateway 有空餘併發能力，發射可用事件
    console.log(`🔔 Gateway ${gatewayId} 有空餘併發能力: ${currentDevices}/${concurrencyInfo.maxConcurrentDevices} 設備`);
    gatewayStatusEmitter.emit('gatewayAvailable', gatewayId);
  }
};
```

### 階段三：智能任務分配

#### 3.1 併發感知的網關選擇

```javascript
// 獲取可用的併發網關
const getAvailableConcurrentGateways = (gatewayIds) => {
  return gatewayIds.filter(gatewayId => {
    const isOnline = isGatewayOnline(gatewayId);
    const canAccept = canGatewayAcceptNewTask(gatewayId);

    if (isOnline) {
      const concurrencyInfo = gatewayConcurrencyCapabilities.get(gatewayId);
      const currentDevices = getCurrentActiveDeviceCount(gatewayId);
      const maxDevices = concurrencyInfo?.maxConcurrentDevices || 1;

      console.log(`🔍 Gateway ${gatewayId} 併發檢查: 在線=${isOnline}, 可接受=${canAccept}, 設備數=${currentDevices}/${maxDevices}`);
    }

    return isOnline && canAccept;
  });
};

// 智能網關選擇（考慮併發能力）
const selectOptimalGatewayForTask = (gatewayIds) => {
  const availableGateways = getAvailableConcurrentGateways(gatewayIds);

  if (availableGateways.length === 0) {
    return null;
  }

  // 按優先級排序選擇最佳網關
  const rankedGateways = availableGateways.map(gatewayId => {
    const concurrencyInfo = gatewayConcurrencyCapabilities.get(gatewayId);
    const currentDevices = getCurrentActiveDeviceCount(gatewayId);

    let score = 0;

    if (concurrencyInfo) {
      // 併發能力評分（利用率越低分數越高）
      const deviceUtilization = currentDevices / concurrencyInfo.maxConcurrentDevices;
      score = (1 - deviceUtilization) * 100;
    } else {
      // 舊版 Gateway 基礎分數
      score = currentDevices === 0 ? 100 : 0;
    }

    return { gatewayId, score, currentDevices };
  });

  // 選擇分數最高的網關
  rankedGateways.sort((a, b) => b.score - a.score);

  console.log(`🎯 Gateway 選擇排名:`, rankedGateways.map(g =>
    `${g.gatewayId}(分數:${g.score.toFixed(1)}, 設備數:${g.currentDevices})`
  ));

  return rankedGateways[0].gatewayId;
};

// 輔助函數：獲取網關當前活躍設備數量
const getCurrentActiveDeviceCount = (gatewayId) => {
  const gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
  if (!gatewayTransmissions) {
    return 0;
  }

  const uniqueDevices = new Set();
  for (const [chunkId, transmission] of gatewayTransmissions) {
    if (transmission.deviceId) {
      uniqueDevices.add(transmission.deviceId);
    }
  }

  return uniqueDevices.size;
};

// 輔助函數：獲取網關活躍設備集合
const getUniqueActiveDevices = (gatewayId) => {
  const gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
  const uniqueDevices = new Set();

  if (gatewayTransmissions) {
    for (const [chunkId, transmission] of gatewayTransmissions) {
      if (transmission.deviceId) {
        uniqueDevices.add(transmission.deviceId);
      }
    }
  }

  return uniqueDevices;
};
```

### 階段四：系統併發數動態調整

#### 4.1 動態併發計算

```javascript
// 計算系統最佳併發數
const calculateOptimalSystemConcurrency = () => {
  const connectedGateways = getConnectedGateways();
  let totalDeviceCapacity = 0;

  for (const [gatewayId] of connectedGateways) {
    const concurrencyInfo = gatewayConcurrencyCapabilities.get(gatewayId);

    if (concurrencyInfo) {
      totalDeviceCapacity += concurrencyInfo.maxConcurrentDevices;
    } else {
      // 舊版 Gateway 按 1 個設備計算
      totalDeviceCapacity += 1;
    }
  }

  // 系統併發數等於所有 Gateway 的設備處理能力總和
  const optimalConcurrency = Math.max(2, Math.min(totalDeviceCapacity, 20)); // 最大不超過 20

  console.log(`📊 系統併發能力計算: 總設備容量=${totalDeviceCapacity}, 建議併發=${optimalConcurrency}`);

  return optimalConcurrency;
};

// 在批量發送時使用動態併發數
const sendMultipleDevicePreviewsWithDynamicConcurrency = async (deviceIds, options = {}) => {
  const optimalConcurrency = calculateOptimalSystemConcurrency();
  const userConcurrency = options.concurrency || optimalConcurrency;
  
  // 使用較小值確保不超過系統能力
  const actualConcurrency = Math.min(userConcurrency, optimalConcurrency);
  
  console.log(`🚀 批量發送併發配置: 用戶設定=${userConcurrency}, 系統最佳=${optimalConcurrency}, 實際使用=${actualConcurrency}`);
  
  return sendMultipleDevicePreviewsToGateways(deviceIds, {
    ...options,
    concurrency: actualConcurrency
  });
};
```

## 部署計劃

### 階段一：基礎架構 (Week 1-2)
1. **Gateway 能力通報機制**
   - 擴展 gatewayInfo 訊息格式
   - 實現 Server 端能力存儲
   - 添加向後兼容邏輯

2. **測試驗證**
   - 單元測試併發能力解析
   - 集成測試能力通報流程

### 階段二：核心邏輯 (Week 3-4)
1. **動態忙碌判斷**
   - 實現新的忙碌判斷邏輯
   - 增強狀態追蹤機制
   - 保持向後兼容

2. **測試驗證**
   - 測試不同併發能力的 Gateway
   - 驗證狀態追蹤準確性

### 階段三：智能分配 (Week 5-6)
1. **智能任務分配**
   - 實現併發感知的網關選擇
   - 添加性能評分機制
   - 優化任務分配算法

2. **系統併發調整**
   - 實現動態併發計算
   - 集成到批量發送流程

### 階段四：優化與監控 (Week 7-8)
1. **性能監控**
   - 添加併發性能指標
   - 實現實時監控面板
   - 性能調優

2. **文檔與培訓**
   - 更新技術文檔
   - 準備部署指南

## 預期效果

### 性能提升
- **處理速度**: 高性能 Gateway 可同時處理多個設備，整體處理速度提升 2-3 倍
- **資源利用**: Gateway 硬體資源得到充分利用
- **系統吞吐**: 系統總併發能力大幅提升

### 量化效果對比

| 指標 | 當前狀況 | 規劃後效果 | 提升幅度 |
|------|----------|------------|----------|
| Gateway 併發能力 | 1個設備/Gateway | 2-3個設備/Gateway | 200-300% |
| 系統處理速度 | 基準 | 2-3倍提升 | 200-300% |
| 資源利用率 | 50% | 80-90% | 60-80% |
| 硬體適配性 | 固定 | 動態適配 | 完全靈活 |

### 靈活性增強
- **硬體適配**: 支援不同性能等級的 Gateway
- **動態調整**: 根據實際能力動態分配任務
- **擴展性**: 易於支援新的 Gateway 型號

### 穩定性保證
- **向後兼容**: 現有 Gateway 無需升級即可正常工作
- **漸進部署**: 可逐步升級 Gateway 韌體
- **故障隔離**: 單個 Gateway 故障不影響整體系統

## 風險評估與應對

### 技術風險
1. **複雜度增加**: 新增邏輯可能引入 bug
   - **應對**: 充分測試，保持向後兼容
   
2. **性能開銷**: 併發管理可能增加 CPU 開銷
   - **應對**: 優化算法，添加性能監控

### 部署風險
1. **Gateway 韌體升級**: 需要協調 Gateway 端開發
   - **應對**: 分階段部署，向後兼容設計
   
2. **配置複雜**: 新增配置參數可能造成混淆
   - **應對**: 提供默認值，詳細文檔說明

## 總結

此規劃將大幅提升系統的併發處理能力和資源利用效率，同時保持良好的向後兼容性。通過讓 Gateway 通報自身併發能力，系統可以更智能地分配任務，充分發揮每個 Gateway 的硬體潛力。
