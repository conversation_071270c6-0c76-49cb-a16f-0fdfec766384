const express = require('express');
const router = express.Router();
const { ObjectId } = require('mongodb');
const { authenticate, checkPermission } = require('../middleware/auth');
const multer = require('multer');

// MongoDB 連接信息
const collectionName = 'bugReports';

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取數據集合
const getCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  
  // 檢查 bugReports 集合是否存在，若不存在則創建
  const collections = await db.listCollections({ name: collectionName }).toArray();
  if (collections.length === 0) {
    console.log(`創建 ${collectionName} 集合`);
    await db.createCollection(collectionName);
  }

  const collection = db.collection(collectionName);
  return { collection, client };
};

// 配置 multer 用於文件上傳
const storage = multer.memoryStorage();
const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB 限制
  },
  fileFilter: (req, file, cb) => {
    // 只允許圖片文件
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允許上傳圖片文件'), false);
    }
  }
});

// 獲取所有bug回報
router.get('/bug-reports', authenticate, async (req, res) => {
  try {
    const { collection } = await getCollection();
    const { page = 1, limit = 10, status, priority } = req.query;
    
    // 構建查詢條件
    const query = {};
    if (status) query.status = status;
    if (priority) query.priority = priority;
    
    // 計算分頁
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // 獲取總數
    const total = await collection.countDocuments(query);
    
    // 獲取數據
    const bugReports = await collection
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .toArray();
    
    res.json({
      data: bugReports,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('獲取bug回報失敗:', error);
    res.status(500).json({ error: '獲取bug回報失敗' });
  }
});

// 創建bug回報
router.post('/bug-reports', upload.single('image'), async (req, res) => {
  try {
    const { title, content, priority = 'medium', currentPage } = req.body;
    
    if (!title || !content) {
      return res.status(400).json({ error: '標題和內容為必填項' });
    }
    
    const { collection, client } = await getCollection();
    const { gridFSBucket } = await getDbConnection();
    
    // 處理圖片上傳
    let imageId = null;
    if (req.file) {
      const { originalname, mimetype, buffer } = req.file;
      
      // 上傳到 GridFS
      const uploadStream = gridFSBucket.openUploadStream(originalname, {
        metadata: { 
          mimetype, 
          originalFilename: originalname,
          type: 'bug-report-image'
        }
      });
      
      uploadStream.write(buffer);
      uploadStream.end();
      
      await new Promise((resolve, reject) => {
        uploadStream.on('finish', () => {
          imageId = uploadStream.id;
          resolve();
        });
        uploadStream.on('error', reject);
      });
    }
    
    // 創建bug回報記錄
    const bugReport = {
      title,
      content,
      priority,
      currentPage: currentPage || '未知頁面',
      status: 'open',
      imageId,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: req.user ? req.user.username : 'anonymous'
    };
    
    const result = await collection.insertOne(bugReport);
    
    res.status(201).json({
      message: 'Bug回報創建成功',
      id: result.insertedId,
      bugReport: { ...bugReport, _id: result.insertedId }
    });
  } catch (error) {
    console.error('創建bug回報失敗:', error);
    res.status(500).json({ error: '創建bug回報失敗' });
  }
});

// 更新bug回報狀態
router.patch('/bug-reports/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, priority, notes } = req.body;
    
    if (!ObjectId.isValid(id)) {
      return res.status(400).json({ error: '無效的ID格式' });
    }
    
    const { collection } = await getCollection();
    
    const updateData = {
      updatedAt: new Date(),
      updatedBy: req.user.username
    };
    
    if (status) updateData.status = status;
    if (priority) updateData.priority = priority;
    if (notes) updateData.notes = notes;
    
    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );
    
    if (result.matchedCount === 0) {
      return res.status(404).json({ error: 'Bug回報不存在' });
    }
    
    res.json({ message: 'Bug回報更新成功' });
  } catch (error) {
    console.error('更新bug回報失敗:', error);
    res.status(500).json({ error: '更新bug回報失敗' });
  }
});

// 刪除bug回報
router.delete('/bug-reports/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!ObjectId.isValid(id)) {
      return res.status(400).json({ error: '無效的ID格式' });
    }
    
    const { collection } = await getCollection();
    const { gridFSBucket } = await getDbConnection();
    
    // 先獲取bug回報信息，檢查是否有關聯的圖片
    const bugReport = await collection.findOne({ _id: new ObjectId(id) });
    if (!bugReport) {
      return res.status(404).json({ error: 'Bug回報不存在' });
    }
    
    // 如果有關聯的圖片，先刪除圖片
    if (bugReport.imageId) {
      try {
        await gridFSBucket.delete(bugReport.imageId);
      } catch (error) {
        console.warn('刪除關聯圖片失敗:', error);
      }
    }
    
    // 刪除bug回報記錄
    await collection.deleteOne({ _id: new ObjectId(id) });
    
    res.json({ message: 'Bug回報刪除成功' });
  } catch (error) {
    console.error('刪除bug回報失敗:', error);
    res.status(500).json({ error: '刪除bug回報失敗' });
  }
});

// 獲取單個bug回報詳情
router.get('/bug-reports/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!ObjectId.isValid(id)) {
      return res.status(400).json({ error: '無效的ID格式' });
    }
    
    const { collection } = await getCollection();
    const bugReport = await collection.findOne({ _id: new ObjectId(id) });
    
    if (!bugReport) {
      return res.status(404).json({ error: 'Bug回報不存在' });
    }
    
    res.json(bugReport);
  } catch (error) {
    console.error('獲取bug回報詳情失敗:', error);
    res.status(500).json({ error: '獲取bug回報詳情失敗' });
  }
});

// 檢查測試模式是否啟用
router.get('/test-mode/status', async (req, res) => {
  try {
    const testModeEnv = process.env.TEST_MODE;
    const isSet = testModeEnv !== undefined;
    const testMode = testModeEnv === 'dev';
    console.log(`測試模式檢查: TEST_MODE環境變數='${testModeEnv}', 是否設定=${isSet}, 啟用狀態=${testMode}`);
    res.json({
      enabled: testMode,
      isSet: isSet
    });
  } catch (error) {
    console.error('檢查測試模式狀態失敗:', error);
    res.status(500).json({ error: '檢查測試模式狀態失敗' });
  }
});

// 檢查懸浮球是否啟用
router.get('/floating-button/status', async (req, res) => {
  try {
    const floatingButtonEnv = process.env.FLOATING_BUTTON;
    const isSet = floatingButtonEnv !== undefined;
    const floatingButtonEnabled = floatingButtonEnv === 'enable';
    console.log(`懸浮球檢查: FLOATING_BUTTON環境變數='${floatingButtonEnv}', 是否設定=${isSet}, 啟用狀態=${floatingButtonEnabled}`);
    res.json({
      enabled: floatingButtonEnabled,
      isSet: isSet
    });
  } catch (error) {
    console.error('檢查懸浮球狀態失敗:', error);
    res.status(500).json({ error: '檢查懸浮球狀態失敗' });
  }
});

// 導出bug回報數據
router.get('/bug-reports/export/csv', authenticate, async (req, res) => {
  try {
    const { collection } = await getCollection();
    const bugReports = await collection.find().sort({ createdAt: -1 }).toArray();
    
    // 生成CSV格式
    const csvHeader = 'ID,標題,內容,優先級,狀態,當前頁面,創建時間,創建者,更新時間,更新者,備註\n';
    const csvRows = bugReports.map(report => {
      return [
        report._id,
        `"${report.title.replace(/"/g, '""')}"`,
        `"${report.content.replace(/"/g, '""')}"`,
        report.priority,
        report.status,
        `"${report.currentPage.replace(/"/g, '""')}"`,
        report.createdAt.toISOString(),
        report.createdBy || '',
        report.updatedAt ? report.updatedAt.toISOString() : '',
        report.updatedBy || '',
        `"${(report.notes || '').replace(/"/g, '""')}"`
      ].join(',');
    }).join('\n');
    
    const csv = csvHeader + csvRows;
    
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', 'attachment; filename="bug-reports.csv"');
    res.send('\uFEFF' + csv); // 添加BOM以支持中文
  } catch (error) {
    console.error('導出bug回報失敗:', error);
    res.status(500).json({ error: '導出bug回報失敗' });
  }
});

module.exports = { router, initDB };
