<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BWRY 顏色轉換測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .canvas-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 15px;
        }
        .canvas-item {
            text-align: center;
        }
        .canvas-item canvas {
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .canvas-item p {
            margin: 10px 0 0 0;
            font-size: 14px;
            color: #666;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .controls button {
            padding: 8px 16px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .controls button:hover {
            background: #005a87;
        }
        .color-palette {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        .color-swatch {
            width: 40px;
            height: 40px;
            border: 1px solid #ccc;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BWRY 顏色轉換測試</h1>
        <p>測試黑白紅黃四色電子紙的顏色轉換效果</p>

        <div class="controls">
            <button onclick="createTestImage()">創建測試圖像</button>
            <button onclick="testBWRYConversion()">測試 BWRY 轉換</button>
            <button onclick="testBWRConversion()">測試 BWR 轉換</button>
            <button onclick="testAllConversions()">測試所有轉換</button>
        </div>

        <div class="test-section">
            <h3>BWRY 調色板</h3>
            <div class="color-palette">
                <div class="color-swatch" style="background-color: #000000; color: white;">黑</div>
                <div class="color-swatch" style="background-color: #FFFFFF; color: black;">白</div>
                <div class="color-swatch" style="background-color: #FF0000; color: white;">紅</div>
                <div class="color-swatch" style="background-color: #FFFF00; color: black;">黃</div>
            </div>
        </div>

        <div class="test-section">
            <h3>原始測試圖像</h3>
            <div class="canvas-container">
                <div class="canvas-item">
                    <canvas id="originalCanvas" width="200" height="150"></canvas>
                    <p>原始彩色圖像</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>轉換結果</h3>
            <div class="canvas-container" id="resultsContainer">
                <!-- 轉換結果將在這裡顯示 -->
            </div>
        </div>
    </div>

    <script>
        // 模擬改進的顏色量化算法
        function findClosestColorBWRY(r, g, b) {
            // 檢查是否為黃色（高紅色和綠色，低藍色）
            if (r > 180 && g > 180 && b < 100) {
                return { r: 255, g: 255, b: 0, name: '黃色' };
            }
            // 檢查是否為紅色（高紅色，低綠色和藍色）
            if (r > 150 && g < 100 && b < 100) {
                return { r: 255, g: 0, b: 0, name: '紅色' };
            }
            // 檢查是否為白色（高亮度）
            const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
            if (brightness > 128) {
                return { r: 255, g: 255, b: 255, name: '白色' };
            } else {
                return { r: 0, g: 0, b: 0, name: '黑色' };
            }
        }

        function findClosestColorBWR(r, g, b) {
            // 檢查是否為紅色（高紅色，低綠色和藍色）
            if (r > 150 && g < 100 && b < 100) {
                return { r: 255, g: 0, b: 0, name: '紅色' };
            }
            // 檢查是否為白色（高亮度）
            const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
            if (brightness > 128) {
                return { r: 255, g: 255, b: 255, name: '白色' };
            } else {
                return { r: 0, g: 0, b: 0, name: '黑色' };
            }
        }

        function createTestImage() {
            const canvas = document.getElementById('originalCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清除畫布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 創建一個包含各種顏色的測試圖像
            // 背景漸變
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
            gradient.addColorStop(0, '#ff0000');    // 紅色
            gradient.addColorStop(0.25, '#ffff00'); // 黃色
            gradient.addColorStop(0.5, '#00ff00');  // 綠色
            gradient.addColorStop(0.75, '#00ffff'); // 青色
            gradient.addColorStop(1, '#0000ff');    // 藍色
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 添加一些純色區塊
            ctx.fillStyle = '#000000'; // 黑色
            ctx.fillRect(10, 10, 30, 30);
            
            ctx.fillStyle = '#ffffff'; // 白色
            ctx.fillRect(50, 10, 30, 30);
            
            ctx.fillStyle = '#ff0000'; // 紅色
            ctx.fillRect(90, 10, 30, 30);
            
            ctx.fillStyle = '#ffff00'; // 黃色
            ctx.fillRect(130, 10, 30, 30);
            
            // 添加一些中間色調
            ctx.fillStyle = '#808080'; // 灰色
            ctx.fillRect(10, 50, 30, 30);
            
            ctx.fillStyle = '#ff8000'; // 橙色
            ctx.fillRect(50, 50, 30, 30);
            
            ctx.fillStyle = '#8000ff'; // 紫色
            ctx.fillRect(90, 50, 30, 30);
            
            ctx.fillStyle = '#00ff80'; // 青綠色
            ctx.fillRect(130, 50, 30, 30);
            
            console.log('測試圖像創建完成');
        }

        function convertCanvas(sourceCanvas, colorType, algorithm) {
            const newCanvas = document.createElement('canvas');
            newCanvas.width = sourceCanvas.width;
            newCanvas.height = sourceCanvas.height;
            const ctx = newCanvas.getContext('2d');
            
            // 複製原始圖像
            ctx.drawImage(sourceCanvas, 0, 0);
            
            const imageData = ctx.getImageData(0, 0, newCanvas.width, newCanvas.height);
            const data = imageData.data;
            
            // 統計顏色使用情況
            const colorStats = {};
            
            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                
                let closestColor;
                if (colorType === 'BWRY') {
                    closestColor = findClosestColorBWRY(r, g, b);
                } else if (colorType === 'BWR') {
                    closestColor = findClosestColorBWR(r, g, b);
                } else {
                    // 默認黑白
                    const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
                    closestColor = brightness > 128 
                        ? { r: 255, g: 255, b: 255, name: '白色' }
                        : { r: 0, g: 0, b: 0, name: '黑色' };
                }
                
                data[i] = closestColor.r;
                data[i + 1] = closestColor.g;
                data[i + 2] = closestColor.b;
                
                // 統計顏色使用
                const colorKey = `${closestColor.r},${closestColor.g},${closestColor.b}`;
                colorStats[colorKey] = (colorStats[colorKey] || 0) + 1;
            }
            
            ctx.putImageData(imageData, 0, 0);
            
            // 輸出顏色統計
            console.log(`${colorType} 轉換顏色統計:`, colorStats);
            
            return newCanvas;
        }

        function testBWRYConversion() {
            const originalCanvas = document.getElementById('originalCanvas');
            if (!originalCanvas.getContext('2d').getImageData(0, 0, 1, 1)) {
                alert('請先創建測試圖像');
                return;
            }
            
            const convertedCanvas = convertCanvas(originalCanvas, 'BWRY', 'colorQuantization');
            displayResult(convertedCanvas, 'BWRY 四色量化');
        }

        function testBWRConversion() {
            const originalCanvas = document.getElementById('originalCanvas');
            if (!originalCanvas.getContext('2d').getImageData(0, 0, 1, 1)) {
                alert('請先創建測試圖像');
                return;
            }
            
            const convertedCanvas = convertCanvas(originalCanvas, 'BWR', 'colorQuantization');
            displayResult(convertedCanvas, 'BWR 三色量化');
        }

        function testAllConversions() {
            const originalCanvas = document.getElementById('originalCanvas');
            if (!originalCanvas.getContext('2d').getImageData(0, 0, 1, 1)) {
                alert('請先創建測試圖像');
                return;
            }
            
            // 清除之前的結果
            document.getElementById('resultsContainer').innerHTML = '';
            
            // 測試所有轉換
            const conversions = [
                { type: 'BWRY', name: 'BWRY 四色量化' },
                { type: 'BWR', name: 'BWR 三色量化' },
                { type: 'BW', name: 'BW 黑白二值化' }
            ];
            
            conversions.forEach(conversion => {
                const convertedCanvas = convertCanvas(originalCanvas, conversion.type, 'colorQuantization');
                displayResult(convertedCanvas, conversion.name);
            });
        }

        function displayResult(canvas, title) {
            const resultsContainer = document.getElementById('resultsContainer');
            
            const canvasItem = document.createElement('div');
            canvasItem.className = 'canvas-item';
            
            const clonedCanvas = document.createElement('canvas');
            clonedCanvas.width = canvas.width;
            clonedCanvas.height = canvas.height;
            clonedCanvas.style.border = '1px solid #ccc';
            clonedCanvas.style.borderRadius = '4px';
            
            const ctx = clonedCanvas.getContext('2d');
            ctx.drawImage(canvas, 0, 0);
            
            const description = document.createElement('p');
            description.textContent = title;
            
            canvasItem.appendChild(clonedCanvas);
            canvasItem.appendChild(description);
            resultsContainer.appendChild(canvasItem);
        }

        // 頁面載入時創建測試圖像
        document.addEventListener('DOMContentLoaded', function() {
            createTestImage();
            console.log('BWRY 顏色轉換測試頁面初始化完成');
        });
    </script>
</body>
</html>
