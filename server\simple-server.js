// 簡化版服務器用於測試
const express = require('express');
const http = require('http');
const os = require('os');

const app = express();
const server = http.createServer(app);
const port = 3001;

// 獲取本機 IP 地址
function getLocalIpAddress() {
  const interfaces = os.networkInterfaces();
  
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost';
}

const localIp = getLocalIpAddress();
console.log(`檢測到本機 IP 地址: ${localIp}`);

// 簡化的 CORS 中間件
app.use((req, res, next) => {
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    `http://${localIp}:5173`,
    `http://${localIp}:5174`,
    `http://${localIp}:5175`
  ];

  const origin = req.headers.origin;

  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else if (origin) {
    const originUrl = new URL(origin);
    const originHost = originUrl.hostname;
    
    const isLocalNetwork = 
      originHost === 'localhost' ||
      originHost === '127.0.0.1' ||
      originHost === localIp ||
      /^192\.168\.\d+\.\d+$/.test(originHost) ||
      /^10\.\d+\.\d+\.\d+$/.test(originHost) ||
      /^172\.(1[6-9]|2\d|3[01])\.\d+\.\d+$/.test(originHost);
    
    if (isLocalNetwork && (originUrl.port === '5173' || originUrl.port === '5174' || originUrl.port === '5175')) {
      res.header('Access-Control-Allow-Origin', origin);
      console.log(`允許來自同網段的請求: ${origin}`);
    }
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');

  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }

  console.log(`${new Date().toISOString()} - ${req.method} ${req.url} - Origin: ${origin}`);
  next();
});

app.use(express.json());

// 測試路由
app.get('/dbtest', (req, res) => {
  console.log('收到數據庫測試請求');
  res.json({
    status: 'ok',
    message: '簡化服務器運行正常',
    timestamp: new Date().toISOString(),
    server: {
      localhost: `http://localhost:${port}`,
      network: `http://${localIp}:${port}`
    }
  });
});

// API 測試路由
app.get('/api/auth/init-check', (req, res) => {
  console.log('收到初始化檢查請求');
  res.json({
    initialized: true,
    message: '系統已初始化'
  });
});

app.get('/api/configs/system', (req, res) => {
  console.log('收到系統配置請求');
  res.json({
    timeout: 5000,
    retryCount: 3,
    bufferSize: 1024,
    maxBindingDataCount: 8
  });
});

app.get('/api/auth/check', (req, res) => {
  console.log('收到認證檢查請求');
  res.json({
    authenticated: false,
    message: '未登入'
  });
});

// 錯誤處理
app.use((error, req, res, next) => {
  console.error('服務器錯誤:', error);
  res.status(500).json({
    error: '內部服務器錯誤',
    message: error.message
  });
});

// 啟動服務器
server.listen(port, '0.0.0.0', (error) => {
  if (error) {
    console.error('服務器監聽失敗:', error);
    process.exit(1);
  }
  
  console.log(`簡化服務器運行在:`);
  console.log(`  - 本機訪問: http://localhost:${port}`);
  console.log(`  - 網路訪問: http://${localIp}:${port}`);
  console.log(`測試連接:`);
  console.log(`  - http://localhost:${port}/dbtest`);
  console.log(`  - http://${localIp}:${port}/dbtest`);
});

server.on('error', (error) => {
  console.error('服務器錯誤:', error);
});

// 優雅關閉
process.on('SIGINT', () => {
  console.log('\n正在關閉服務器...');
  server.close(() => {
    console.log('服務器已關閉');
    process.exit(0);
  });
});
