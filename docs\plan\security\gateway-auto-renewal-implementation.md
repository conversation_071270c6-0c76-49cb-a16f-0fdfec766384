# Gateway Token 自動續約快速實作指南

## 🚀 快速實作方案

基於您的問題，我建議實作 **Gateway 自動續約機制**，這樣可以避免手動複製 Token 的麻煩。

---

## 📋 實作步驟

### 步驟 1: 創建 Gateway Token 續約 API

#### 新增路由檔案 `server/routes/gatewayAuthApi.js`
```javascript
const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { ObjectId } = require('mongodb');
const { generateGatewayToken, verifyToken } = require('../utils/jwtUtils');

// 數據庫連接
let getDbConnection = null;

const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

/**
 * Gateway Token 自動續約
 * POST /api/gateway/renew-token
 */
router.post('/gateway/renew-token', async (req, res) => {
  try {
    // 從 Authorization header 獲取當前 Token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        error: 'Missing authorization header',
        code: 'NO_TOKEN'
      });
    }

    const currentToken = authHeader.substring(7);
    
    // 嘗試驗證 Token (即使過期也要能解析基本資訊)
    let decoded;
    try {
      decoded = verifyToken(currentToken);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        // Token 過期但仍可解析
        decoded = jwt.decode(currentToken);
        if (!decoded || decoded.type !== 'gateway') {
          return res.status(401).json({ 
            error: 'Invalid token type',
            code: 'WRONG_TOKEN_TYPE'
          });
        }
      } else {
        return res.status(401).json({ 
          error: 'Invalid token',
          code: 'INVALID_TOKEN'
        });
      }
    }

    const { gatewayId, storeId, macAddress } = decoded;

    // 驗證 Gateway 是否存在且有效
    const { db } = await getDbConnection();
    const gateway = await db.collection('gateways').findOne({
      _id: new ObjectId(gatewayId),
      storeId: storeId,
      macAddress: macAddress
    });

    if (!gateway) {
      return res.status(404).json({ 
        error: 'Gateway not found',
        code: 'GATEWAY_NOT_FOUND'
      });
    }

    // 生成新 Token (使用系統配置的過期時間)
    const newToken = generateGatewayToken({
      gatewayId,
      storeId,
      macAddress
    });

    // 更新資料庫中的 WebSocket 資訊
    const wsPath = `/ws/store/${storeId}/gateway/${gatewayId}`;
    const { buildWebSocketUrl } = require('../utils/networkUtils');
    const wsUrl = buildWebSocketUrl(wsPath, 3001);

    const wsInfo = {
      url: wsUrl,
      path: wsPath,
      token: newToken,
      protocol: 'json',
      lastRenewed: new Date(),
      renewCount: (gateway.websocket?.renewCount || 0) + 1
    };

    await db.collection('gateways').updateOne(
      { _id: new ObjectId(gatewayId) },
      { $set: { websocket: wsInfo } }
    );

    console.log(`Gateway ${gatewayId} token renewed successfully (count: ${wsInfo.renewCount})`);

    res.json({
      success: true,
      token: newToken,
      expiresIn: 24 * 60 * 60, // 24小時 (秒)
      wsUrl: `${wsUrl}?token=${newToken}`,
      renewedAt: new Date().toISOString(),
      renewCount: wsInfo.renewCount
    });

  } catch (error) {
    console.error('Gateway token renewal failed:', error);
    res.status(500).json({ 
      error: 'Token renewal failed',
      code: 'RENEWAL_ERROR'
    });
  }
});

/**
 * 檢查 Token 狀態
 * GET /api/gateway/token-status
 */
router.get('/gateway/token-status', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing authorization header' });
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = verifyToken(token);
      const timeToExpiry = decoded.exp - Math.floor(Date.now() / 1000);
      
      res.json({
        valid: true,
        expiresIn: timeToExpiry,
        expiresAt: new Date(decoded.exp * 1000).toISOString(),
        shouldRenew: timeToExpiry < (2 * 60 * 60), // 2小時內過期建議續約
        gatewayInfo: {
          gatewayId: decoded.gatewayId,
          storeId: decoded.storeId,
          macAddress: decoded.macAddress
        }
      });
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        res.json({
          valid: false,
          expired: true,
          shouldRenew: true
        });
      } else {
        res.status(401).json({ error: 'Invalid token' });
      }
    }

  } catch (error) {
    console.error('Token status check failed:', error);
    res.status(500).json({ error: 'Status check failed' });
  }
});

module.exports = { router, initDB };
```

### 步驟 2: 註冊新的 API 路由

#### 修改 `server/index.js`
```javascript
// 在現有的路由註冊部分添加
const { router: gatewayAuthRouter, initDB: initGatewayAuthApi } = require('./routes/gatewayAuthApi');

// 註冊路由
app.use('/api', gatewayAuthRouter);

// 初始化數據庫連接 (在其他 initXXXApi 調用附近添加)
initGatewayAuthApi(connectDB);
```

### 步驟 3: 增強 JWT 工具函數

#### 修改 `server/utils/jwtUtils.js`
```javascript
// 添加 Token 過期檢查函數
const isTokenExpiringSoon = (token, thresholdSeconds = 2 * 60 * 60) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) return true;
    
    const timeToExpiry = decoded.exp - Math.floor(Date.now() / 1000);
    return timeToExpiry <= thresholdSeconds;
  } catch (error) {
    return true;
  }
};

// 添加到 module.exports
module.exports = {
  // ... 現有的導出
  isTokenExpiringSoon
};
```

### 步驟 4: Gateway 端自動續約邏輯 (Python 範例)

#### 創建 `gateway_token_manager.py`
```python
import asyncio
import aiohttp
import json
import jwt
import time
import logging
from datetime import datetime

class GatewayTokenManager:
    def __init__(self, config):
        self.server_url = config['server_url']
        self.current_token = config['token']
        self.renewal_interval = 30 * 60  # 30分鐘檢查一次
        self.renewal_threshold = 2 * 60 * 60  # 2小時前續約
        self.running = False
        
    async def start(self):
        """啟動自動續約服務"""
        self.running = True
        asyncio.create_task(self._renewal_loop())
        logging.info("Gateway Token 自動續約服務已啟動")
    
    async def stop(self):
        """停止自動續約服務"""
        self.running = False
        logging.info("Gateway Token 自動續約服務已停止")
    
    async def _renewal_loop(self):
        """自動續約循環"""
        while self.running:
            try:
                if await self._should_renew():
                    await self._renew_token()
                
                await asyncio.sleep(self.renewal_interval)
                
            except Exception as e:
                logging.error(f"Token 續約循環錯誤: {e}")
                await asyncio.sleep(60)  # 錯誤時1分鐘後重試
    
    async def _should_renew(self):
        """檢查是否需要續約"""
        try:
            # 檢查 Token 狀態
            headers = {'Authorization': f'Bearer {self.current_token}'}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f'{self.server_url}/api/gateway/token-status',
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        return data.get('shouldRenew', False)
                    else:
                        # 如果檢查失敗，嘗試續約
                        return True
                        
        except Exception as e:
            logging.error(f"Token 狀態檢查失敗: {e}")
            return True
    
    async def _renew_token(self):
        """執行 Token 續約"""
        try:
            headers = {
                'Authorization': f'Bearer {self.current_token}',
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f'{self.server_url}/api/gateway/renew-token',
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        # 更新 Token
                        old_token = self.current_token
                        self.current_token = data['token']
                        
                        # 保存到配置檔案
                        await self._save_token()
                        
                        logging.info(f"Token 續約成功 (第 {data.get('renewCount', 0)} 次)")
                        
                        # 通知其他組件 Token 已更新
                        await self._notify_token_updated(old_token, self.current_token)
                        
                    else:
                        error_data = await response.json()
                        logging.error(f"Token 續約失敗: {error_data.get('error', 'Unknown error')}")
                        
        except Exception as e:
            logging.error(f"Token 續約請求失敗: {e}")
    
    async def _save_token(self):
        """保存新 Token 到配置檔案"""
        try:
            # 讀取現有配置
            with open('gateway_config.json', 'r') as f:
                config = json.load(f)
            
            # 更新 Token
            config['token'] = self.current_token
            config['last_token_update'] = datetime.now().isoformat()
            
            # 寫回檔案
            with open('gateway_config.json', 'w') as f:
                json.dump(config, f, indent=2)
                
        except Exception as e:
            logging.error(f"保存 Token 失敗: {e}")
    
    async def _notify_token_updated(self, old_token, new_token):
        """通知其他組件 Token 已更新"""
        # 這裡可以通知 WebSocket 客戶端等組件更新 Token
        logging.info("Token 已更新，通知相關組件...")
    
    def get_current_token(self):
        """獲取當前有效的 Token"""
        return self.current_token

# 使用範例
async def main():
    config = {
        'server_url': 'http://localhost:3001',
        'token': 'your_initial_gateway_token'
    }
    
    token_manager = GatewayTokenManager(config)
    await token_manager.start()
    
    # 保持運行
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        await token_manager.stop()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
```

### 步驟 5: WebSocket 連接增強

#### 修改 WebSocket 客戶端以支援 Token 更新
```python
class GatewayWebSocketClient:
    def __init__(self, token_manager):
        self.token_manager = token_manager
        self.websocket = None
        
    async def connect_with_auto_renewal(self):
        """帶自動續約的 WebSocket 連接"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                token = self.token_manager.get_current_token()
                ws_url = f"ws://localhost:3001/ws/store/your_store/gateway/your_gateway?token={token}"
                
                self.websocket = await websockets.connect(ws_url)
                logging.info("WebSocket 連接成功")
                
                # 處理消息
                await self._handle_messages()
                break
                
            except websockets.exceptions.ConnectionClosedError as e:
                if e.code == 1008:  # 認證失敗，可能是 Token 過期
                    logging.warning("WebSocket 認證失敗，嘗試續約 Token...")
                    await self.token_manager._renew_token()
                    retry_count += 1
                else:
                    logging.error(f"WebSocket 連接錯誤: {e}")
                    break
                    
            except Exception as e:
                logging.error(f"WebSocket 連接異常: {e}")
                retry_count += 1
                await asyncio.sleep(5)
        
        if retry_count >= max_retries:
            logging.error("WebSocket 連接失敗，已達最大重試次數")
```

---

## 🎯 總結

### ✅ **推薦方案: Gateway 自動續約**

**優點**:
1. **無需人工介入** - Gateway 自動處理 Token 過期
2. **服務不中斷** - 無縫續約，用戶無感知
3. **適合大規模部署** - 可管理大量 Gateway 設備
4. **降低維護成本** - 減少人工操作和錯誤

**實作要點**:
1. **後端提供續約 API** - 驗證舊 Token 並生成新 Token
2. **Gateway 主動續約** - 定期檢查並在過期前續約
3. **容錯機制** - 處理網路異常和續約失敗情況
4. **安全驗證** - 確保只有合法 Gateway 可以續約

### 🔄 **與手動方式的比較**

| 方式 | 人工介入 | 服務中斷 | 適用場景 | 維護成本 |
|------|---------|---------|---------|---------|
| Web 手動重新生成 | 需要 | 可能中斷 | 少量設備 | 高 |
| Gateway 自動續約 | 不需要 | 無中斷 | 大量設備 | 低 |

**建議**: 實作自動續約作為主要方案，保留手動重新生成作為備用方案。

這樣的設計既保證了系統的自動化程度，又提供了必要的靈活性，是最佳的解決方案。
