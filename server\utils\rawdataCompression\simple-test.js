/**
 * 簡單測試
 */

console.log('開始簡單測試...');

try {
  const compression = require('./index.js');
  console.log('✅ 模組載入成功');
  
  const formats = compression.getSupportedFormats();
  console.log('✅ 支援的格式:', formats);
  
  const testData = new Uint8Array([0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x02]);
  console.log('✅ 測試數據:', Array.from(testData));
  
  const result = compression.processPixelData('runlendata', testData);
  console.log('✅ 壓縮結果:', {
    success: result.success,
    originalSize: result.originalSize,
    processedSize: result.processedSize
  });
  
  if (result.success) {
    const decompressed = compression.decompressData('runlendata', result.data);
    console.log('✅ 解壓縮成功，大小:', decompressed.length);
    
    const match = Array.from(testData).join(',') === Array.from(decompressed).join(',');
    console.log('✅ 數據匹配:', match);
  }
  
  console.log('🎉 測試完成！');
  
} catch (error) {
  console.error('❌ 測試失敗:', error.message);
  console.error(error.stack);
}
