# 圖片URL動態IP修復

## 問題描述

原本的圖片儲存機制會將完整的URL（包含固定IP）儲存到資料庫中，這會導致以下問題：

1. **IP變更問題**：當服務器IP變更時，儲存的圖片URL會失效
2. **部署環境問題**：在不同環境（開發、測試、生產）間移動時，URL會不匹配
3. **遠端連線問題**：localhost URL在遠端連線時無法正常載入

## 解決方案

### 1. 修改圖片URL儲存機制

**修改前**：
```javascript
// 儲存完整URL到資料庫
imageUrl: "http://*************:3001/api/files/64a7b8c9d1e2f3a4b5c6d7e8"
```

**修改後**：
```javascript
// 只儲存文件ID到資料庫
imageUrl: "64a7b8c9d1e2f3a4b5c6d7e8"
```

### 2. 創建統一的圖片URL處理工具

創建了 `src/utils/imageUrlUtils.ts` 工具文件，提供以下功能：

- `processImageUrl()`: 將文件ID或相對路徑轉換為完整URL
- `extractFileIdFromUrl()`: 從完整URL中提取文件ID
- `isFileId()`: 檢查是否為文件ID格式
- `isRelativePath()`: 檢查是否為相對路徑格式
- `isFullUrl()`: 檢查是否為完整URL格式

### 3. 修改的文件

#### 前端文件

1. **ImageSelectorModal.tsx**
   - 修改 `handleConfirm()` 函數，優先傳遞文件ID而不是完整URL
   - 更新確認按鈕的啟用邏輯
   - 添加文件名顯示，讓用戶看到有意義的信息

2. **ImageElement.tsx**
   - 移除重複的 `processImageUrl` 函數
   - 使用統一的工具函數
   - 添加文件名獲取邏輯，當 imageUrl 是文件ID時顯示實際文件名

3. **canvasUtils.tsx**
   - 移除重複的 `processImageUrl` 函數
   - 使用統一的工具函數

4. **PreviewComponent.tsx**
   - 移除重複的 `processImageUrl` 函數
   - 使用統一的工具函數

#### 後端工具

1. **imageUrlMigration.js**
   - 資料庫遷移工具，將現有的完整URL轉換為文件ID
   - 支援模板、設備、門店資料的圖片URL遷移

2. **migrate-image-urls.js**
   - 遷移腳本，方便執行資料庫遷移

## 使用方法

### 執行資料庫遷移

```bash
# 執行遷移腳本
node server/scripts/migrate-image-urls.js
```

### 新的圖片URL處理流程

1. **圖片選擇時**：只傳遞文件ID，但在UI中顯示文件名
2. **圖片儲存時**：只儲存文件ID到資料庫
3. **圖片顯示時**：使用 `processImageUrl()` 動態組裝完整URL
4. **用戶界面**：顯示有意義的文件名而不是技術性的文件ID

## 向後相容性

- 支援現有的完整URL格式
- 支援相對路徑格式 `/api/files/{fileId}`
- 支援新的文件ID格式 `{fileId}`

## 動態IP檢測機制

使用現有的 `buildEndpointUrl()` 函數，該函數會：

1. 自動檢測當前主機IP
2. 使用正確的協議（http/https）
3. 使用正確的端口號
4. 構建完整的API URL

## 測試驗證

1. ✅ 圖片選擇功能正常
2. ✅ 圖片顯示功能正常
3. ✅ 向後相容性正常
4. ✅ 動態IP檢測正常
5. ✅ 資料庫遷移功能正常

## 注意事項

1. **執行遷移前請備份資料庫**
2. 遷移是安全的，不會刪除任何數據
3. 遷移後的文件ID格式更簡潔，減少儲存空間
4. 新的機制完全支援動態IP，適合各種部署環境

## 相關文件

- `src/utils/imageUrlUtils.ts` - 圖片URL處理工具
- `server/utils/imageUrlMigration.js` - 資料庫遷移工具
- `server/scripts/migrate-image-urls.js` - 遷移執行腳本
- `src/utils/api/apiConfig.ts` - API配置（包含動態IP檢測）
- `server/utils/networkUtils.js` - 網路工具（動態IP檢測）
