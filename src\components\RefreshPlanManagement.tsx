import React, { useState, useEffect } from 'react';
import { Search, Plus, Filter, RefreshCw, Calendar, Grid, List, Activity } from 'lucide-react';
import { RefreshPlan, PlanAction } from '../types/refreshPlan';
import { refreshPlanApi, handleApiError } from '../services/refreshPlanApi';
import { RefreshPlanCard } from './RefreshPlanCard';
import { RefreshPlanCompactCard } from './RefreshPlanCompactCard';
import { AddRefreshPlanModal } from './AddRefreshPlanModal';
import { subscribeToRefreshPlanUpdate, RefreshPlanUpdateEvent } from '../utils/websocketClient';
import { EditRefreshPlanModal } from './EditRefreshPlanModal';
import { RefreshPlanStatisticsModal } from './RefreshPlanStatisticsModal';
import { Store } from '../types/store';

interface RefreshPlanManagementProps {
  store: Store;
}



export const RefreshPlanManagement: React.FC<RefreshPlanManagementProps> = ({ store }) => {
  
  // 狀態管理
  const [plans, setPlans] = useState<RefreshPlan[]>([]);
  const [filteredPlans, setFilteredPlans] = useState<RefreshPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalPlans, setTotalPlans] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [showFilters, setShowFilters] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showStatisticsModal, setShowStatisticsModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<RefreshPlan | null>(null);
  const [viewMode, setViewMode] = useState<'detailed' | 'compact'>('detailed');
  const [runningPlans, setRunningPlans] = useState<Set<string>>(new Set());
  const [schedulerStatus, setSchedulerStatus] = useState<any>(null);

  // 即時更新控制項狀態
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);

  // 前端過濾邏輯
  useEffect(() => {
    let filtered = [...plans];

    // 根據搜尋詞過濾
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(plan =>
        plan.name.toLowerCase().includes(searchLower) ||
        (plan.description && plan.description.toLowerCase().includes(searchLower))
      );
    }

    // 根據狀態過濾
    if (selectedStatus) {
      filtered = filtered.filter(plan => plan.status === selectedStatus);
    }

    setFilteredPlans(filtered);
  }, [plans, searchTerm, selectedStatus]);

  // WebSocket 連接和實時更新
  useEffect(() => {
    if (!realTimeEnabled) {
      return; // 如果即時更新關閉，不訂閱WebSocket
    }

    // 監聽刷圖計畫狀態更新
    const handleRefreshPlanUpdate = (event: RefreshPlanUpdateEvent) => {
      if (event.storeId === store.id) {
        console.log('收到刷圖計畫更新事件:', {
          planId: event.planId,
          updateType: event.updateType,
          timestamp: event.timestamp,
          updatedFields: event.planData.updatedFields
        });

        if (event.updateType === 'delete') {
          // 刪除操作：從列表中移除計畫
          setPlans(prevPlans => prevPlans.filter(plan => plan._id !== event.planId));
          setRunningPlans(prev => {
            const newSet = new Set(prev);
            newSet.delete(event.planId);
            return newSet;
          });
        } else if (event.updateType === 'create') {
          // 創建操作：重新獲取完整的計畫列表以確保數據完整性
          console.log('刷圖計畫新增，重新獲取計畫列表');
          loadPlans();
        } else if (event.updateType === 'status_change' || event.updateType === 'update') {
          // 狀態變更或更新操作：更新計畫列表中的特定計畫
          console.log(`收到計畫${event.updateType}事件:`, {
            planId: event.planId,
            updateType: event.updateType,
            updatedFields: event.planData.updatedFields,
            nextRun: event.planData.nextRun,
            hasCompleteData: !!(event.planData as any).trigger && !!(event.planData as any).targetSelection
          });

          setPlans(prevPlans =>
            prevPlans.map(plan => {
              if (plan._id !== event.planId) {
                return plan;
              }

              // 根據事件類型來決定如何更新
              if (event.updateType === 'status_change') {
                // 狀態變更事件：只更新狀態相關欄位，保護 nextRun 不被錯誤覆蓋
                return {
                  ...plan,
                  // 只更新狀態相關的欄位
                  ...(event.planData.status !== undefined && { status: event.planData.status }),
                  ...(event.planData.lastRun !== undefined && { lastRun: event.planData.lastRun }),
                  ...(event.planData.statistics !== undefined && { statistics: event.planData.statistics }),
                  // 保持原有的 nextRun 值，不被狀態變更影響
                  nextRun: plan.nextRun,
                  _id: event.planId
                };
              } else if (event.updateType === 'update') {
                // 更新事件：檢查是否包含完整的計畫數據
                const planDataAny = event.planData as any; // 類型斷言以訪問額外欄位
                const hasCompleteData = planDataAny.trigger && planDataAny.targetSelection;
                const updatedFields = event.planData.updatedFields || [];

                console.log(`處理更新事件 - 計畫 ${event.planId}:`, {
                  hasCompleteData,
                  updatedFields,
                  nextRun: event.planData.nextRun,
                  currentNextRun: plan.nextRun,
                  isSchedulerUpdate: updatedFields.includes('nextRun') && updatedFields.includes('updatedAt') && updatedFields.length === 2
                });

                // 特殊處理：調度器的 nextRun 更新事件
                if (updatedFields.includes('nextRun') && updatedFields.includes('updatedAt') && updatedFields.length === 2) {
                  console.log(`檢測到調度器 nextRun 更新事件，使用完整數據合併`);
                  return {
                    ...plan,
                    ...planDataAny,
                    _id: event.planId
                  };
                }

                if (hasCompleteData) {
                  // 完整的計畫數據更新，可以安全地合併所有欄位
                  console.log(`使用完整數據更新計畫 ${event.planId}`);
                  return {
                    ...plan,
                    ...planDataAny,
                    _id: event.planId
                  };
                } else {
                  // 部分數據更新，只更新明確指定的欄位
                  const updatedPlan = { ...plan };

                  console.log(`使用部分數據更新計畫 ${event.planId}，更新欄位:`, updatedFields);
                  console.log(`事件數據:`, {
                    nextRun: event.planData.nextRun,
                    name: event.planData.name,
                    status: event.planData.status,
                    enabled: event.planData.enabled
                  });

                  // 安全地更新指定欄位
                  if (updatedFields.includes('name') && event.planData.name !== undefined) {
                    updatedPlan.name = event.planData.name;
                    console.log(`更新 name: ${event.planData.name}`);
                  }
                  if (updatedFields.includes('status') && event.planData.status !== undefined) {
                    updatedPlan.status = event.planData.status;
                    console.log(`更新 status: ${event.planData.status}`);
                  }
                  if (updatedFields.includes('enabled') && event.planData.enabled !== undefined) {
                    updatedPlan.enabled = event.planData.enabled;
                    console.log(`更新 enabled: ${event.planData.enabled}`);
                  }
                  if (updatedFields.includes('nextRun')) {
                    // 對於 nextRun，即使值為 null 也要更新
                    updatedPlan.nextRun = event.planData.nextRun;
                    console.log(`更新 nextRun: ${plan.nextRun} -> ${event.planData.nextRun}`);
                  }
                  if (updatedFields.includes('lastRun') && event.planData.lastRun !== undefined) {
                    updatedPlan.lastRun = event.planData.lastRun;
                    console.log(`更新 lastRun: ${event.planData.lastRun}`);
                  }
                  if (updatedFields.includes('statistics') && event.planData.statistics !== undefined) {
                    updatedPlan.statistics = event.planData.statistics;
                    console.log(`更新 statistics`);
                  }

                  // 處理其他可能的欄位（如果後端發送了完整數據）
                  if (hasCompleteData) {
                    if (updatedFields.includes('trigger') && planDataAny.trigger) {
                      updatedPlan.trigger = planDataAny.trigger;
                      console.log(`更新 trigger`);
                    }
                    if (updatedFields.includes('targetSelection') && planDataAny.targetSelection) {
                      updatedPlan.targetSelection = planDataAny.targetSelection;
                      console.log(`更新 targetSelection`);
                    }
                    if (updatedFields.includes('description') && planDataAny.description !== undefined) {
                      updatedPlan.description = planDataAny.description;
                      console.log(`更新 description: ${planDataAny.description}`);
                    }
                    if (updatedFields.includes('priority') && planDataAny.priority !== undefined) {
                      updatedPlan.priority = planDataAny.priority;
                      console.log(`更新 priority: ${planDataAny.priority}`);
                    }
                  }

                  updatedPlan._id = event.planId;
                  console.log(`計畫 ${event.planId} 更新完成，最終 nextRun: ${updatedPlan.nextRun}`);
                  return updatedPlan;
                }
              }

              return plan;
            })
          );

          // 更新運行狀態
          if (event.planData.status === 'running') {
            setRunningPlans(prev => new Set(prev).add(event.planId));
          } else {
            setRunningPlans(prev => {
              const newSet = new Set(prev);
              newSet.delete(event.planId);
              return newSet;
            });
          }
        }
      }
    };

    // 訂閱刷圖計畫更新
    const unsubscribe = subscribeToRefreshPlanUpdate(
      store.id,
      handleRefreshPlanUpdate,
      {}
    );

    return () => {
      console.log(`取消刷圖計畫即時更新訂閱: storeId=${store.id}`);
      unsubscribe();
    };
  }, [store.id, realTimeEnabled]);

  // 載入調度器狀態
  const loadSchedulerStatus = async () => {
    try {
      const response = await refreshPlanApi.getSchedulerStatus(store.id);
      if (response.success) {
        setSchedulerStatus(response.data);

        // 更新運行狀態
        const running = new Set(response.data.runningPlans.map(plan => plan._id));
        setRunningPlans(running);
      }
    } catch (err) {
      console.error('載入調度器狀態失敗:', err);
    }
  };

  // 載入計畫列表
  const loadPlans = async (page = 1) => {
    try {
      setLoading(page === 1);
      setError(null);

      // 載入所有計畫，不進行後端過濾
      const response = await refreshPlanApi.getPlans(store.id, {
        page: 1,
        limit: 1000, // 載入大量數據以支援前端過濾
      });

      if (response.success) {
        setPlans(response.data.plans);
        setTotalPlans(response.data.total);

        // 更新運行狀態
        const running = new Set(
          response.data.plans
            .filter((plan: RefreshPlan) => plan.status === 'running')
            .map((plan: RefreshPlan) => plan._id)
        );
        setRunningPlans(running);
      }
    } catch (err) {
      setError(handleApiError(err));
      console.error('載入計畫列表失敗:', err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 初始載入
  useEffect(() => {
    if (store?.id) {
      loadPlans();
      loadSchedulerStatus();

      // 定期更新調度器狀態（每30秒）
      const statusInterval = setInterval(loadSchedulerStatus, 30000);

      return () => {
        clearInterval(statusInterval);
      };
    }
  }, [store?.id, pageSize]);



  // 搜索處理 - 現在只更新搜尋詞，過濾由useEffect處理
  const handleSearch = () => {
    // 搜尋詞已經通過onChange更新，過濾會自動觸發
  };

  // 狀態篩選處理 - 現在只更新狀態，過濾由useEffect處理
  const handleStatusFilter = (status: string) => {
    setSelectedStatus(status);
  };

  // 刷新列表
  const handleRefresh = () => {
    setRefreshing(true);
    loadPlans();
  };

  // 分頁處理 - 現在用於前端分頁
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 計畫操作處理
  const handlePlanAction = async (action: PlanAction, planId: string) => {
    try {
      switch (action) {
        case 'execute':
          await handleExecutePlan(planId);
          break;
        case 'edit':
          handleEditPlan(planId);
          break;
        case 'statistics':
          handleViewStatistics(planId);
          break;
        case 'toggle':
          await handleTogglePlan(planId);
          break;
        case 'delete':
          await handleDeletePlan(planId);
          break;
      }
    } catch (err) {
      setError(handleApiError(err));
    }
  };

  // 執行計畫
  const handleExecutePlan = async (planId: string) => {
    try {
      const response = await refreshPlanApi.executePlan(store.id, planId);
      if (response.success) {
        // 顯示成功消息
        console.log('計畫執行已啟動:', response.data.message);
        // 如果即時更新關閉，才手動刷新列表
        if (!realTimeEnabled) {
          loadPlans();
        }
        // 如果即時更新開啟，WebSocket會自動更新狀態
      }
    } catch (err) {
      throw err;
    }
  };

  // 編輯計畫
  const handleEditPlan = (planId: string) => {
    const plan = plans.find(p => p._id === planId);
    if (plan) {
      setSelectedPlan(plan);
      setShowEditModal(true);
    }
  };

  // 查看統計
  const handleViewStatistics = (planId: string) => {
    const plan = plans.find(p => p._id === planId);
    if (plan) {
      setSelectedPlan(plan);
      setShowStatisticsModal(true);
    }
  };

  // 切換計畫狀態
  const handleTogglePlan = async (planId: string) => {
    const plan = plans.find(p => p._id === planId);
    if (!plan) return;

    try {
      await refreshPlanApi.togglePlan(store.id, planId, !plan.enabled);
      // 如果即時更新關閉，才手動刷新列表
      if (!realTimeEnabled) {
        loadPlans();
      }
    } catch (err) {
      throw err;
    }
  };

  // 刪除計畫
  const handleDeletePlan = async (planId: string) => {
    if (!confirm('確定要刪除這個刷圖計畫嗎？此操作無法撤銷。')) {
      return;
    }

    try {
      await refreshPlanApi.deletePlan(store.id, planId);
      // 如果即時更新關閉，才手動刷新列表
      if (!realTimeEnabled) {
        loadPlans();
      }
    } catch (err) {
      throw err;
    }
  };

  // 新增計畫
  const handleAddPlan = () => {
    setShowAddModal(true);
  };

  // 新增計畫成功回調
  const handleAddPlanSuccess = () => {
    // 如果即時更新關閉，才手動刷新列表
    if (!realTimeEnabled) {
      loadPlans();
    }
  };

  // 編輯計畫成功回調
  const handleEditPlanSuccess = () => {
    console.log('編輯計畫成功，即時更新狀態:', realTimeEnabled);
    // 如果即時更新關閉，才手動刷新列表
    if (!realTimeEnabled) {
      console.log('即時更新關閉，手動刷新列表');
      loadPlans();
    } else {
      console.log('即時更新開啟，等待 WebSocket 更新');
    }
    setShowEditModal(false);
    setSelectedPlan(null);
  };

  // 即時更新控制項功能
  const handleToggleRealTime = () => {
    setRealTimeEnabled(!realTimeEnabled);
  };

  // 計算前端分頁
  const totalFilteredPlans = filteredPlans.length;
  const totalFilteredPages = Math.ceil(totalFilteredPlans / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentPagePlans = filteredPlans.slice(startIndex, endIndex);

  // 當過濾結果改變時，重置到第一頁
  useEffect(() => {
    setCurrentPage(1);
  }, [filteredPlans.length]);

  // 渲染分頁
  const renderPagination = () => {
    if (totalFilteredPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalFilteredPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`px-3 py-1 rounded-md text-sm ${
            i === currentPage
              ? 'bg-blue-500 text-white'
              : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          {i}
        </button>
      );
    }

    return (
      <div className="flex items-center justify-between mt-6">
        <div className="flex items-center space-x-2">
          {pages}
          <select
            value={pageSize}
            onChange={(e) => setPageSize(Number(e.target.value))}
            className="px-2 py-1 border border-gray-300 rounded-md text-sm"
          >
            <option value={10}>10條/頁</option>
            <option value={20}>20條/頁</option>
            <option value={50}>50條/頁</option>
          </select>
          <span className="text-sm text-gray-600">
            共{totalFilteredPlans}個計畫，第{currentPage}頁/共{totalFilteredPages}頁
            {totalFilteredPlans !== totalPlans && (
              <span className="text-gray-400 ml-1">(已過濾，原始{totalPlans}個)</span>
            )}
          </span>
        </div>
      </div>
    );
  };

  if (loading && plans.length === 0) {
    return (
      <div className="p-4">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 animate-spin text-blue-500 mx-auto mb-2" />
            <p className="text-gray-600">載入中...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      {/* 錯誤提示 */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700">
          {error}
        </div>
      )}

      {/* 搜索和操作欄 */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4">
          {/* 搜索框 */}
          <div className="relative">
            <input
              type="text"
              placeholder="搜索計畫名稱..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              className="w-64 pl-4 pr-10 py-2 rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
            <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
          </div>

          {/* 狀態篩選 */}
          <select
            value={selectedStatus}
            onChange={(e) => handleStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">全部狀態</option>
            <option value="active">已啟用</option>
            <option value="inactive">已停用</option>
            <option value="running">運行中</option>
            <option value="error">錯誤</option>
          </select>

          {/* 刷新按鈕 */}
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 mr-1 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </button>
        </div>

        {/* 操作按鈕 */}
        <div className="flex items-center space-x-2">
          {/* 即時更新開關 */}
          <button
            onClick={handleToggleRealTime}
            className={`flex items-center px-3 py-2 rounded-md border text-sm font-medium transition-colors ${
              realTimeEnabled
                ? 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200'
                : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200'
            }`}
            title={realTimeEnabled ? '點擊關閉即時更新' : '點擊開啟即時更新'}
          >
            <Activity className={`w-4 h-4 mr-2 ${realTimeEnabled ? 'animate-pulse' : ''}`} />
            即時更新 {realTimeEnabled ? '開啟' : '關閉'}
          </button>

          {/* 調度器狀態和運行狀態指示器 */}
          <div className="flex items-center space-x-2">
            {/* 調度器狀態 */}
            {schedulerStatus && (
              <div className={`flex items-center px-3 py-2 rounded-md border text-sm ${
                schedulerStatus.schedulerStatus.isHealthy
                  ? 'bg-blue-100 text-blue-800 border-blue-200'
                  : 'bg-red-100 text-red-800 border-red-200'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  schedulerStatus.schedulerStatus.isHealthy ? 'bg-blue-500' : 'bg-red-500'
                }`} />
                <span className="font-medium">
                  調度器 {schedulerStatus.schedulerStatus.isHealthy ? '正常' : '異常'}
                </span>
                <span className="ml-2 text-xs opacity-75">
                  ({schedulerStatus.schedulerStatus.totalActiveTasks} 個任務)
                </span>
              </div>
            )}

            {/* 運行狀態指示器 */}
            {runningPlans.size > 0 && (
              <div className="flex items-center px-3 py-2 bg-green-100 text-green-800 rounded-md border border-green-200">
                <Activity className="w-4 h-4 mr-2 animate-pulse" />
                <span className="text-sm font-medium">
                  {runningPlans.size} 個計畫運行中
                </span>
              </div>
            )}
          </div>

          {/* 視圖模式切換 */}
          <div className="flex items-center bg-gray-100 rounded-md p-1">
            <button
              onClick={() => setViewMode('detailed')}
              className={`flex items-center px-3 py-1 rounded text-sm font-medium transition-colors ${
                viewMode === 'detailed'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Grid className="w-4 h-4 mr-1" />
              詳細
            </button>
            <button
              onClick={() => setViewMode('compact')}
              className={`flex items-center px-3 py-1 rounded text-sm font-medium transition-colors ${
                viewMode === 'compact'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <List className="w-4 h-4 mr-1" />
              精簡
            </button>
          </div>

          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            <Filter className="w-4 h-4 mr-1" />
            篩選
          </button>
          <button
            onClick={handleAddPlan}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            <Plus className="w-4 h-4 mr-1" />
            新增計畫
          </button>
        </div>
      </div>



      {/* 計畫列表 */}
      {filteredPlans.length === 0 ? (
        <div className="bg-white py-12 text-center text-gray-500 rounded-lg border border-gray-200">
          <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          {plans.length === 0 ? (
            <>
              <p className="text-lg font-medium mb-2">暫無刷圖計畫</p>
              <p className="text-sm">點擊「新增計畫」按鈕創建您的第一個刷圖計畫</p>
            </>
          ) : (
            <>
              <p className="text-lg font-medium mb-2">沒有符合條件的計畫</p>
              <p className="text-sm">請調整搜尋條件或狀態篩選</p>
            </>
          )}
        </div>
      ) : viewMode === 'detailed' ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {currentPagePlans.map((plan) => (
            <RefreshPlanCard
              key={plan._id}
              plan={plan}
              onAction={handlePlanAction}
            />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {currentPagePlans.map((plan) => (
            <RefreshPlanCompactCard
              key={plan._id}
              plan={plan}
              onAction={handlePlanAction}
              isRunning={runningPlans.has(plan._id)}
            />
          ))}
        </div>
      )}

      {/* 分頁 */}
      {renderPagination()}

      {/* 新增計畫模態框 */}
      <AddRefreshPlanModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        store={store}
        onSuccess={handleAddPlanSuccess}
      />

      {/* 編輯計畫模態框 */}
      {selectedPlan && (
        <EditRefreshPlanModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedPlan(null);
          }}
          store={store}
          plan={selectedPlan}
          onSuccess={handleEditPlanSuccess}
        />
      )}

      {/* 統計模態框 */}
      {selectedPlan && (
        <RefreshPlanStatisticsModal
          isOpen={showStatisticsModal}
          onClose={() => {
            setShowStatisticsModal(false);
            setSelectedPlan(null);
          }}
          store={store}
          plan={selectedPlan}
        />
      )}
    </div>
  );
};

export default RefreshPlanManagement;
