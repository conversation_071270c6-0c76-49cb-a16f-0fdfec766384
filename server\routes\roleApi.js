const express = require('express');
const router = express.Router();
const Role = require('../models/Role');
const { authenticate, checkPermission } = require('../middleware/auth');

// 數據庫連接函數引用
let connectDBFunction;

// 初始化數據庫連接
function initDB(connectDB) {
  connectDBFunction = connectDB;
  return connectDBFunction;
}

/**
 * 獲取所有角色
 * GET /api/roles
 */
router.get('/roles', authenticate, async (req, res) => {
  try {
    // 獲取查詢參數
    const { type } = req.query;

    // 構建過濾條件
    const filter = {};

    if (type) {
      filter.type = type;
    }

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 獲取角色列表
    const roles = await Role.findAll(db, filter);

    res.json(roles);
  } catch (error) {
    console.error('獲取角色列表錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 獲取單個角色
 * GET /api/roles/:id
 */
router.get('/roles/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 查找角色
    const role = await Role.findById(db, id);

    if (!role) {
      return res.status(404).json({ error: '角色不存在' });
    }

    res.json(role);
  } catch (error) {
    console.error('獲取角色錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 創建角色
 * POST /api/roles
 */
router.post('/roles', authenticate, checkPermission('role:create'), async (req, res) => {
  try {
    const { name, description, type, permissions } = req.body;

    if (!name) {
      return res.status(400).json({ error: '角色名稱不能為空' });
    }

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 創建角色
    const role = await Role.createRole(db, {
      name,
      description,
      type: type || 'system',
      permissions: permissions || []
    });

    res.status(201).json(role);
  } catch (error) {
    console.error('創建角色錯誤:', error);

    if (error.message === '角色名稱已存在') {
      return res.status(400).json({ error: error.message });
    }

    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 更新角色
 * PUT /api/roles/:id
 */
router.put('/roles/:id', authenticate, checkPermission('role:update'), async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, permissions } = req.body;

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 檢查角色是否存在
    const existingRole = await Role.findById(db, id);

    if (!existingRole) {
      return res.status(404).json({ error: '角色不存在' });
    }

    // 更新角色
    await Role.updateRole(db, id, {
      name,
      description,
      permissions
    });

    // 獲取更新後的角色
    const updatedRole = await Role.findById(db, id);

    res.json(updatedRole);
  } catch (error) {
    console.error('更新角色錯誤:', error);

    if (error.message === '角色名稱已存在') {
      return res.status(400).json({ error: error.message });
    }

    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 刪除角色
 * DELETE /api/roles/:id
 */
router.delete('/roles/:id', authenticate, checkPermission('role:delete'), async (req, res) => {
  try {
    const { id } = req.params;

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 檢查角色是否存在
    const existingRole = await Role.findById(db, id);

    if (!existingRole) {
      return res.status(404).json({ error: '角色不存在' });
    }

    // 檢查角色是否被使用
    const { ObjectId } = require('mongodb');
    const permissionsCount = await db.collection('permissions').countDocuments({ roleId: new ObjectId(id) });

    if (permissionsCount > 0) {
      return res.status(400).json({ error: '該角色正在被使用，無法刪除' });
    }

    // 刪除角色
    await Role.deleteRole(db, id);

    res.json({ message: '角色刪除成功' });
  } catch (error) {
    console.error('刪除角色錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 獲取可用權限列表
 * GET /api/permissions/available
 */
router.get('/permissions/available', authenticate, async (req, res) => {
  try {
    // 定義可用權限列表，按照角色類型分組
    // 使用與 sidebar 相同的多國語言變數名稱

    // 系統角色權限
    const systemPermissions = [
      // 門店管理 (sidebar.storeManagement)
      { id: 'store:view', name: '查看門店', group: 'sidebar.storeManagement', roleType: 'system' },
      { id: 'store:create', name: '創建門店', group: 'sidebar.storeManagement', roleType: 'system' },
      { id: 'store:update', name: '更新門店', group: 'sidebar.storeManagement', roleType: 'system' },
      { id: 'store:delete', name: '刪除門店', group: 'sidebar.storeManagement', roleType: 'system' },

      // 系統數據 (sidebar.systemData)
      { id: 'system-data:view', name: '查看系統數據', group: 'sidebar.systemData', roleType: 'system' },
      { id: 'system-data:create', name: '創建系統數據', group: 'sidebar.systemData', roleType: 'system' },
      { id: 'system-data:update', name: '更新系統數據', group: 'sidebar.systemData', roleType: 'system' },
      { id: 'system-data:delete', name: '刪除系統數據', group: 'sidebar.systemData', roleType: 'system' },

      // 系統模板 (sidebar.systemTemplates)
      { id: 'template:view', name: '查看系統模板', group: 'sidebar.systemTemplates', roleType: 'system' },
      { id: 'template:create', name: '創建系統模板', group: 'sidebar.systemTemplates', roleType: 'system' },
      { id: 'template:update', name: '更新系統模板', group: 'sidebar.systemTemplates', roleType: 'system' },
      { id: 'template:delete', name: '刪除系統模板', group: 'sidebar.systemTemplates', roleType: 'system' },

      // 權限管理 (sidebar.permissionManagement)
      // 用戶管理
      { id: 'user:view', name: '查看用戶', group: 'sidebar.permissionManagement', subGroup: '用戶管理', roleType: 'system' },
      { id: 'user:create', name: '創建用戶', group: 'sidebar.permissionManagement', subGroup: '用戶管理', roleType: 'system' },
      { id: 'user:update', name: '更新用戶', group: 'sidebar.permissionManagement', subGroup: '用戶管理', roleType: 'system' },
      { id: 'user:delete', name: '刪除用戶', group: 'sidebar.permissionManagement', subGroup: '用戶管理', roleType: 'system' },

      // 角色管理
      { id: 'role:view', name: '查看角色', group: 'sidebar.permissionManagement', subGroup: '角色管理', roleType: 'system' },
      { id: 'role:create', name: '創建角色', group: 'sidebar.permissionManagement', subGroup: '角色管理', roleType: 'system' },
      { id: 'role:update', name: '更新角色', group: 'sidebar.permissionManagement', subGroup: '角色管理', roleType: 'system' },
      { id: 'role:delete', name: '刪除角色', group: 'sidebar.permissionManagement', subGroup: '角色管理', roleType: 'system' },

      // 權限分配
      { id: 'permission:view', name: '查看權限分配', group: 'sidebar.permissionManagement', subGroup: '權限分配', roleType: 'system' },
      { id: 'permission:create', name: '創建權限分配', group: 'sidebar.permissionManagement', subGroup: '權限分配', roleType: 'system' },
      { id: 'permission:update', name: '更新權限分配', group: 'sidebar.permissionManagement', subGroup: '權限分配', roleType: 'system' },
      { id: 'permission:delete', name: '刪除權限分配', group: 'sidebar.permissionManagement', subGroup: '權限分配', roleType: 'system' },

      // 系統紀錄 (sidebar.systemLogs)
      { id: 'system-logs:view', name: '查看系統紀錄', group: 'sidebar.systemLogs', roleType: 'system' },
      { id: 'system-logs:export', name: '匯出系統紀錄', group: 'sidebar.systemLogs', roleType: 'system' },
      { id: 'system-logs:delete', name: '刪除系統紀錄', group: 'sidebar.systemLogs', roleType: 'system' },

      // 系統設定 (sidebar.systemConfig)
      { id: 'system:view', name: '查看系統設定', group: 'sidebar.systemConfig', roleType: 'system' },
      { id: 'system:update', name: '更新系統設定', group: 'sidebar.systemConfig', roleType: 'system' }
    ];

    // 門店角色權限
    const storePermissions = [
      // 門店數據 (sidebar.database)
      { id: 'store-data:view', name: '查看門店數據', group: 'sidebar.database', roleType: 'store' },
      { id: 'store-data:create', name: '創建門店數據', group: 'sidebar.database', roleType: 'store' },
      { id: 'store-data:update', name: '更新門店數據', group: 'sidebar.database', roleType: 'store' },
      { id: 'store-data:delete', name: '刪除門店數據', group: 'sidebar.database', roleType: 'store' },

      // 門店模板 (sidebar.templates)
      { id: 'store-template:view', name: '查看門店模板', group: 'sidebar.templates', roleType: 'store' },
      { id: 'store-template:create', name: '創建門店模板', group: 'sidebar.templates', roleType: 'store' },
      { id: 'store-template:update', name: '更新門店模板', group: 'sidebar.templates', roleType: 'store' },
      { id: 'store-template:delete', name: '刪除門店模板', group: 'sidebar.templates', roleType: 'store' },

      // 網關管理 (sidebar.deploy)
      { id: 'gateway:view', name: '查看網關', group: 'sidebar.deploy', roleType: 'store' },
      { id: 'gateway:create', name: '創建網關', group: 'sidebar.deploy', roleType: 'store' },
      { id: 'gateway:update', name: '更新網關', group: 'sidebar.deploy', roleType: 'store' },
      { id: 'gateway:delete', name: '刪除網關', group: 'sidebar.deploy', roleType: 'store' },

      // 設備管理 (sidebar.devices)
      { id: 'device:view', name: '查看設備', group: 'sidebar.devices', roleType: 'store' },
      { id: 'device:create', name: '創建設備', group: 'sidebar.devices', roleType: 'store' },
      { id: 'device:update', name: '更新設備', group: 'sidebar.devices', roleType: 'store' },
      { id: 'device:delete', name: '刪除設備', group: 'sidebar.devices', roleType: 'store' },

      // 門店設置 (sidebar.users)
      { id: 'store-settings:view', name: '查看門店設置', group: 'sidebar.users', roleType: 'store' },
      { id: 'store-settings:update', name: '更新門店設置', group: 'sidebar.users', roleType: 'store' },

      // 系統分析 (sidebar.analytics)
      { id: 'analytics:view', name: '查看系統分析', group: 'sidebar.analytics', roleType: 'store' },
      { id: 'analytics:export', name: '匯出分析數據', group: 'sidebar.analytics', roleType: 'store' }
    ];

    // 根據請求參數返回對應的權限列表
    const { type } = req.query;
    let availablePermissions = [];

    if (type === 'system') {
      availablePermissions = [...systemPermissions];
    } else if (type === 'store') {
      availablePermissions = [...storePermissions];
    } else {
      // 如果沒有指定類型，則返回所有權限
      availablePermissions = [...systemPermissions, ...storePermissions];
    }

    res.json(availablePermissions);
  } catch (error) {
    console.error('獲取可用權限列表錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

module.exports = { router, initDB };
