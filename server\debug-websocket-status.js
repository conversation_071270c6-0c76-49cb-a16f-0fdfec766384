// 調試 WebSocket 狀態的工具
const websocketService = require('./services/websocketService');

async function checkWebSocketStatus() {
  console.log('🔧 檢查 WebSocket 服務狀態...');
  
  try {
    // 檢查連接的網關數量
    const connectedGateways = websocketService.getConnectedGateways();
    console.log(`📡 已連接的網關數量: ${connectedGateways.size}`);
    
    if (connectedGateways.size > 0) {
      console.log('📋 已連接的網關列表:');
      for (const [gatewayId, ws] of connectedGateways) {
        console.log(`  - 網關 ${gatewayId}: ${ws.readyState === 1 ? '在線' : '離線'}`);
      }
    } else {
      console.log('⚠️ 沒有網關連接');
    }
    
    // 檢查批量進度訂閱者
    const batchSubscribers = websocketService.getBatchProgressSubscribers();
    console.log(`📊 批量進度訂閱者數量: ${batchSubscribers.size}`);
    
    if (batchSubscribers.size > 0) {
      console.log('📋 批量進度訂閱列表:');
      for (const [batchId, subscribers] of batchSubscribers) {
        console.log(`  - 批量 ${batchId}: ${subscribers.size} 個訂閱者`);
      }
    }
    
  } catch (error) {
    console.error('❌ 檢查狀態時出錯:', error);
  }
}

// 如果直接運行此文件
if (require.main === module) {
  checkWebSocketStatus();
}

module.exports = { checkWebSocketStatus };
