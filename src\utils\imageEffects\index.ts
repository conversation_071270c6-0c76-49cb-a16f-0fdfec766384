/**
 * 圖像效果處理工具庫
 */

/**
 * 將 Canvas 元素轉換為黑白二值化圖像
 * @param canvas 輸入的 Canvas 元素
 * @param threshold 閾值 (0-255)，低於此值為黑，高於此值為白，默認 128
 * @returns 新的 Canvas 元素
 */
export const convertToBlackAndWhite = (
  canvas: HTMLCanvasElement,
  threshold: number = 128
): HTMLCanvasElement => {
  // 創建新的 Canvas
  const newCanvas = document.createElement('canvas');
  newCanvas.width = canvas.width;
  newCanvas.height = canvas.height;
  const ctx = newCanvas.getContext('2d');
  
  if (!ctx) {
    console.error('無法獲取 Canvas 上下文');
    return canvas;
  }
  
  // 獲取原始 Canvas 的圖像數據
  const origCtx = canvas.getContext('2d');
  if (!origCtx) {
    console.error('無法獲取原始 Canvas 上下文');
    return canvas;
  }
  
  const imageData = origCtx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;
  
  // 轉換為黑白二值化圖像
  for (let i = 0; i < data.length; i += 4) {
    // 使用灰度計算公式轉換為灰度值
    const grayscale = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
    
    // 應用閾值，低於閾值為黑(0)，高於閾值為白(255)
    const color = grayscale < threshold ? 0 : 255;
    
    // 設置 RGB 值相同以得到黑白效果
    data[i] = data[i + 1] = data[i + 2] = color;
    // 透明度保持不變
    // data[i + 3] 不變
  }
  
  // 將處理後的圖像數據繪製到新 Canvas
  ctx.putImageData(imageData, 0, 0);
  
  return newCanvas;
};

/**
 * 將 Canvas 元素轉換為灰階圖像
 * @param canvas 輸入的 Canvas 元素
 * @returns 新的 Canvas 元素
 */
export const convertToGrayscale = (canvas: HTMLCanvasElement): HTMLCanvasElement => {
  const newCanvas = document.createElement('canvas');
  newCanvas.width = canvas.width;
  newCanvas.height = canvas.height;
  const ctx = newCanvas.getContext('2d');
  
  if (!ctx) {
    console.error('無法獲取 Canvas 上下文');
    return canvas;
  }
  
  const origCtx = canvas.getContext('2d');
  if (!origCtx) {
    console.error('無法獲取原始 Canvas 上下文');
    return canvas;
  }
  
  const imageData = origCtx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;
  
  for (let i = 0; i < data.length; i += 4) {
    const grayscale = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
    data[i] = data[i + 1] = data[i + 2] = grayscale;
  }
  
  ctx.putImageData(imageData, 0, 0);
  
  return newCanvas;
};

/**
 * 將 Canvas 元素轉換為反轉顏色效果
 * @param canvas 輸入的 Canvas 元素
 * @returns 新的 Canvas 元素
 */
export const convertToInverted = (canvas: HTMLCanvasElement): HTMLCanvasElement => {
  const newCanvas = document.createElement('canvas');
  newCanvas.width = canvas.width;
  newCanvas.height = canvas.height;
  const ctx = newCanvas.getContext('2d');
  
  if (!ctx) {
    console.error('無法獲取 Canvas 上下文');
    return canvas;
  }
  
  const origCtx = canvas.getContext('2d');
  if (!origCtx) {
    console.error('無法獲取原始 Canvas 上下文');
    return canvas;
  }
  
  const imageData = origCtx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;
  
  for (let i = 0; i < data.length; i += 4) {
    // 反轉 RGB 值
    data[i] = 255 - data[i];         // R
    data[i + 1] = 255 - data[i + 1]; // G
    data[i + 2] = 255 - data[i + 2]; // B
    // 不改變 Alpha 通道
  }
  
  ctx.putImageData(imageData, 0, 0);
  
  return newCanvas;
};

/**
 * 將 Canvas 元素轉換為錯誤擴散抖動效果 (Floyd-Steinberg)
 * 適合 e-paper 顯示的灰階模擬效果
 * @param canvas 輸入的 Canvas 元素
 * @returns 新的 Canvas 元素
 */
export const convertToDithering = (canvas: HTMLCanvasElement): HTMLCanvasElement => {
  const newCanvas = document.createElement('canvas');
  newCanvas.width = canvas.width;
  newCanvas.height = canvas.height;
  const ctx = newCanvas.getContext('2d');
  
  if (!ctx) {
    console.error('無法獲取 Canvas 上下文');
    return canvas;
  }
  
  const origCtx = canvas.getContext('2d');
  if (!origCtx) {
    console.error('無法獲取原始 Canvas 上下文');
    return canvas;
  }
  
  const imageData = origCtx.getImageData(0, 0, canvas.width, canvas.height);
  const data = new Uint8ClampedArray(imageData.data);
  const width = canvas.width;
  const height = canvas.height;
  
  // 轉換為灰階並保存在新數組
  const grayscale = new Uint8ClampedArray(width * height);
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = (y * width + x) * 4;
      grayscale[y * width + x] = Math.round(0.299 * data[idx] + 0.587 * data[idx + 1] + 0.114 * data[idx + 2]);
    }
  }
  
  // 應用 Floyd-Steinberg 抖動算法
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = y * width + x;
      const oldPixel = grayscale[idx];
      const newPixel = oldPixel < 128 ? 0 : 255;
      grayscale[idx] = newPixel;
      
      const error = oldPixel - newPixel;
      
      // 將誤差分散到鄰近像素
      if (x + 1 < width) {
        grayscale[idx + 1] += error * 7 / 16;
      }
      if (y + 1 < height) {
        if (x - 1 >= 0) {
          grayscale[(y + 1) * width + x - 1] += error * 3 / 16;
        }
        grayscale[(y + 1) * width + x] += error * 5 / 16;
        if (x + 1 < width) {
          grayscale[(y + 1) * width + x + 1] += error * 1 / 16;
        }
      }
    }
  }
  
  // 將灰階值寫回原始圖像數據
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = (y * width + x) * 4;
      const val = grayscale[y * width + x];
      imageData.data[idx] = val;
      imageData.data[idx + 1] = val;
      imageData.data[idx + 2] = val;
    }
  }
  
  ctx.putImageData(imageData, 0, 0);
  
  return newCanvas;
};