# test-ws-client-interactive.js 存檔功能同步完成

## 更新總結

我已經成功將 `test-ws-client-interactive.js` 的存檔功能與 `ws-client-from-copied-info.js` 完全同步，確保兩個測試文件具有一致的存檔行為和影像顯示功能。

## 主要更新內容

### 1. 新增解壓縮功能

**添加的函數和常數**：
```javascript
// Rawdata 格式常數
const RAWDATA_FORMATS = {
  RAWDATA: 'rawdata',
  RUNLENDATA: 'runlendata'
};

// Run-Length 解壓縮函數
function decompressRunLength(compressedData)

// 通用解壓縮函數
function decompressRawdata(rawdata, format)
```

### 2. 增強 saveRawData 函數

**新增功能**：
- ✅ 支援 `dataType` 參數（rawdata, runlendata）
- ✅ 根據格式調整檔案名（`${format}_${deviceMac}_${timestamp}.bin`）
- ✅ 壓縮格式的解壓縮驗證
- ✅ 壓縮比計算和顯示
- ✅ 解壓縮數據單獨存檔（`rawdata_decompressed_*.bin`）
- ✅ 使用 `subarray` 替代已棄用的 `slice` 方法

**函數簽名更新**：
```javascript
// 舊版本
async function saveRawData(rawdata, deviceMac = 'unknown', imageCode = null)

// 新版本
async function saveRawData(rawdata, deviceMac = 'unknown', imageCode = null, format = 'rawdata')
```

### 3. 增強分片重組功能

**ChunkReceiver 類更新**：
- ✅ 新增 `dataType` 屬性追蹤數據格式
- ✅ 在 `handleChunkStart` 中記錄數據格式
- ✅ 在 `saveReassembledData` 中支援格式參數
- ✅ 分片重組數據也支援解壓縮驗證
- ✅ 檔案名格式：`${format}_chunked_${deviceMac}_${timestamp}.bin`

### 4. 更新調用點

**更新的調用位置**：
1. **rawdata 處理**：
   ```javascript
   const dataType = message.dataType || 'rawdata';
   await saveRawData(message.rawdata, message.deviceMac || 'unknown', message.imageCode, dataType);
   ```

2. **分片重組**：
   ```javascript
   await this.saveReassembledData(completeData, this.deviceMac, this.imageCode, this.dataType);
   ```

### 5. 保持的現有功能

**完全保留的功能**：
- ✅ imageCode 關聯記錄和本地變數存儲
- ✅ 數據預覽顯示（前16字節hex）
- ✅ 圖片格式驗證和存檔
- ✅ base64 數據處理
- ✅ 錯誤處理和日誌記錄
- ✅ 所有現有的分片接收邏輯

## 存檔行為對比

### 檔案命名規則

| 數據類型 | 檔案名格式 | 範例 |
|---------|-----------|------|
| 直接 rawdata | `rawdata_${deviceMac}_${timestamp}.bin` | `rawdata_001122334455_2025-06-09T09-20-13.bin` |
| 直接 runlendata | `runlendata_${deviceMac}_${timestamp}.bin` | `runlendata_001122334455_2025-06-09T09-20-13.bin` |
| 分片 rawdata | `rawdata_chunked_${deviceMac}_${timestamp}.bin` | `rawdata_chunked_001122334455_2025-06-09T09-20-13.bin` |
| 分片 runlendata | `runlendata_chunked_${deviceMac}_${timestamp}.bin` | `runlendata_chunked_001122334455_2025-06-09T09-20-13.bin` |
| 解壓縮數據 | `rawdata_decompressed_${deviceMac}_${timestamp}.bin` | `rawdata_decompressed_001122334455_2025-06-09T09-20-13.bin` |
| 分片解壓縮 | `rawdata_decompressed_chunked_${deviceMac}_${timestamp}.bin` | `rawdata_decompressed_chunked_001122334455_2025-06-09T09-20-13.bin` |
| 圖片數據 | `preview_${deviceMac}_${timestamp}.${extension}` | `preview_001122334455_2025-06-09T09-20-13.png` |

### 解壓縮驗證輸出

**壓縮格式數據會顯示**：
```
✅ 已成功將 runlendata 數據保存到: runlendata_001122334455_2025-06-09T09-20-13.bin
📊 數據大小: 164 字節
🔧 解壓縮驗證成功，解壓縮後大小: 9484 字節
📈 壓縮比: 1.7%
💾 解壓縮數據已保存到: rawdata_decompressed_001122334455_2025-06-09T09-20-13.bin
🔗 關聯的 imageCode: img_12345
🔍 數據前 16 字節 (hex): 000000800128012800ff00ff00ff00ff
```

## 測試驗證

### 功能測試結果

**測試腳本**：`test-interactive-save-functions.js`

**測試案例**：
- ✅ rawdata 格式存檔
- ✅ runlendata 格式存檔和解壓縮
- ✅ base64 字符串處理
- ✅ 圖片數據存檔
- ✅ 分片重組數據存檔
- ✅ 壓縮比計算
- ✅ imageCode 關聯記錄

**測試結果**：所有 7 個測試案例通過，生成了正確的檔案格式和內容。

## 向後兼容性

### 完全兼容

- ✅ 所有現有的 API 調用保持不變
- ✅ 預設參數確保舊代碼正常工作
- ✅ 現有的分片接收邏輯完全保留
- ✅ 圖片顯示和存檔功能不受影響

### 新功能可選

- ✅ `dataType` 參數為可選，預設為 'rawdata'
- ✅ 解壓縮功能自動觸發，不影響原有流程
- ✅ 新的檔案命名不會覆蓋現有檔案

## 使用方式

### 啟動測試

```bash
node test-ws-client-interactive.js
```

### 新的互動選項

1. **選擇 maxChunkSize**：20/200/1024/4096 bytes
2. **選擇 maxSingleMessageSize**：500/1024/2048/4096 bytes  
3. **選擇偏好格式**：rawdata/runlendata

### 觀察存檔行為

**存檔目錄**：`server/tests/saved_images/`

**檔案類型**：
- `.bin` - 原始數據檔案
- `.png/.jpg` - 圖片檔案
- `*_decompressed_*.bin` - 解壓縮後的數據檔案

## 與 ws-client-from-copied-info.js 的一致性

### 完全同步的功能

- ✅ 存檔函數邏輯完全一致
- ✅ 解壓縮算法完全相同
- ✅ 檔案命名規則一致
- ✅ 錯誤處理方式相同
- ✅ 日誌輸出格式一致
- ✅ imageCode 處理邏輯相同

### 測試環境差異

| 功能 | test-ws-client-interactive.js | ws-client-from-copied-info.js |
|------|------------------------------|------------------------------|
| 登入方式 | 互動式登入 | 複製 WebSocket 資訊 |
| 參數設定 | 互動式選擇 | 互動式選擇 |
| 網關管理 | 完整的 CRUD 操作 | 模擬網關資訊 |
| 設備管理 | 動態添加/移除 | 固定設備列表 |

## 結論

`test-ws-client-interactive.js` 現在具有與 `ws-client-from-copied-info.js` 完全一致的存檔功能，包括：

1. **完整的數據格式支援**（rawdata, runlendata）
2. **智能解壓縮驗證**
3. **詳細的壓縮統計**
4. **一致的檔案命名**
5. **完整的錯誤處理**
6. **相同的影像顯示邏輯**

兩個測試文件現在可以提供一致的測試體驗，確保 gateway 開發人員在不同測試環境下都能獲得相同的存檔行為和數據處理結果。

---

**更新完成日期**：2024年1月  
**版本**：2.2.0  
**狀態**：✅ 完成並通過測試
