const { ObjectId } = require('mongodb');

/**
 * 角色模型
 * 用於存儲角色信息
 */
class Role {
  /**
   * 創建角色集合
   * @param {Object} db - MongoDB 數據庫實例
   * @returns {Object} - MongoDB 集合
   */
  static getCollection(db) {
    return db.collection('roles');
  }

  /**
   * 創建新角色
   * @param {Object} db - MongoDB 數據庫實例
   * @param {Object} roleData - 角色數據
   * @returns {Object} - 創建的角色
   */
  static async createRole(db, roleData) {
    const collection = this.getCollection(db);
    
    // 檢查角色名稱是否已存在
    const existingRole = await collection.findOne({ name: roleData.name });
    if (existingRole) {
      throw new Error('角色名稱已存在');
    }

    // 創建角色
    const newRole = {
      name: roleData.name,
      description: roleData.description || '',
      type: roleData.type || 'system', // system, store
      permissions: roleData.permissions || [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await collection.insertOne(newRole);
    return { ...newRole, _id: result.insertedId };
  }

  /**
   * 根據 ID 查找角色
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 角色 ID
   * @returns {Object|null} - 找到的角色或 null
   */
  static async findById(db, id) {
    const collection = this.getCollection(db);
    return await collection.findOne({ _id: new ObjectId(id) });
  }

  /**
   * 根據名稱查找角色
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} name - 角色名稱
   * @returns {Object|null} - 找到的角色或 null
   */
  static async findByName(db, name) {
    const collection = this.getCollection(db);
    return await collection.findOne({ name });
  }

  /**
   * 獲取所有角色
   * @param {Object} db - MongoDB 數據庫實例
   * @param {Object} filter - 過濾條件
   * @returns {Array} - 角色列表
   */
  static async findAll(db, filter = {}) {
    const collection = this.getCollection(db);
    return await collection.find(filter).toArray();
  }

  /**
   * 更新角色
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 角色 ID
   * @param {Object} roleData - 更新的角色數據
   * @returns {Object} - 更新結果
   */
  static async updateRole(db, id, roleData) {
    const collection = this.getCollection(db);
    
    // 如果要更新角色名稱，檢查是否已存在
    if (roleData.name) {
      const existingRole = await collection.findOne({ 
        name: roleData.name,
        _id: { $ne: new ObjectId(id) }
      });
      
      if (existingRole) {
        throw new Error('角色名稱已存在');
      }
    }

    const updateData = {
      ...roleData,
      updatedAt: new Date()
    };

    // 移除不應該更新的字段
    delete updateData._id;
    
    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    return result;
  }

  /**
   * 刪除角色
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 角色 ID
   * @returns {Object} - 刪除結果
   */
  static async deleteRole(db, id) {
    const collection = this.getCollection(db);
    return await collection.deleteOne({ _id: new ObjectId(id) });
  }
}

module.exports = Role;
