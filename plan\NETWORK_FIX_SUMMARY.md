# EPD Manager 網路連線修復總結

## 問題描述

用戶在透過其他電腦連線 EPD Manager 系統時遇到以下錯誤：

1. **crypto.randomUUID is not a function** - 瀏覽器兼容性問題
2. **net::ERR_CONNECTION_REFUSED** - 跨電腦連線問題

## 修復方案

### 1. 解決 crypto.randomUUID 兼容性問題

**問題原因：** 某些瀏覽器或環境不支援 `crypto.randomUUID` API

**解決方案：** 在 `src/main.tsx` 中添加 polyfill

```typescript
// 添加 crypto.randomUUID polyfill 以解決兼容性問題
if (!window.crypto?.randomUUID) {
  // 使用 uuid 套件作為 polyfill
  import('uuid').then(({ v4: uuidv4 }) => {
    if (!window.crypto) {
      (window as any).crypto = {};
    }
    (window.crypto as any).randomUUID = uuidv4;
    console.log('已添加 crypto.randomUUID polyfill');
  }).catch(error => {
    console.warn('無法載入 uuid polyfill:', error);
  });
}
```

### 2. 解決跨電腦連線問題

**問題原因：**
- 服務器只監聽 localhost，無法接受來自其他電腦的連線
- CORS 設置不允許跨主機訪問

**解決方案：**

#### 2.1 修改服務器監聽設置

在 `server/index.js` 中：

```javascript
// 啟動服務器，監聽所有網路介面
server.listen(port, '0.0.0.0', () => {
  console.log(`後端服務運行在:`);
  console.log(`  - 本機訪問: http://localhost:${port}`);
  console.log(`  - 網路訪問: http://${localIp}:${port}`);
  // ...
});
```

#### 2.2 更新 CORS 設置

```javascript
// 獲取本機 IP 地址
function getLocalIpAddress() {
  const os = require('os');
  const interfaces = os.networkInterfaces();

  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost';
}

// 自定義 CORS 中間件
app.use((req, res, next) => {
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    `http://${localIp}:5173`,
    `http://${localIp}:5174`,
    `http://${localIp}:5175`
  ];

  const origin = req.headers.origin;
  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    res.header('Access-Control-Allow-Origin', '*');
  }
  // ...
});
```

#### 2.3 前端自動檢測服務器地址

在 `src/utils/api/apiConfig.ts` 中：

```typescript
function getServerHost(): string {
  // 檢查 URL 參數
  const urlParams = new URLSearchParams(window.location.search);
  const serverHost = urlParams.get('server');

  if (serverHost) {
    return serverHost;
  }

  // 如果不是從 localhost 訪問，使用當前主機名
  if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
    return window.location.hostname;
  }

  return 'localhost';
}

export const API_CONFIG = {
  host: getServerHost(),
  port: 3001,
  useHttps: false,
};
```

## 測試工具

創建了 `test-connection.html` 測試頁面，包含：

1. **瀏覽器兼容性檢查** - 檢測 crypto.randomUUID、Fetch API、WebSocket 支援
2. **服務器連線測試** - 測試與後端服務器的連線
3. **自動檢測功能** - 自動檢測可用的服務器地址
4. **API 端點測試** - 測試關鍵 API 端點的可用性

## 使用方法

### 本機訪問
- 前端：`http://localhost:5173`
- 後端：`http://localhost:3001`

### 網路訪問
- 前端：`http://[服務器IP]:5173`
- 後端：`http://[服務器IP]:3001`

### 手動指定服務器
可以通過 URL 參數指定服務器地址：
```
http://localhost:5173?server=*************
```

## 驗證步驟

### 方法一：使用簡化服務器（推薦用於測試）
1. 啟動簡化服務器：`cd server && node simple-server.js`
2. 啟動前端服務器：`npm run dev`
3. 打開測試頁面：`test-connection.html`
4. 執行各項測試確認修復效果

### 方法二：使用完整服務器
1. 確保 MongoDB 正在運行
2. 啟動後端服務器：`cd server && node index.js`
3. 啟動前端服務器：`npm run dev`
4. 從其他電腦訪問：`http://[服務器IP]:5173`

### 測試結果確認
- ✅ crypto.randomUUID polyfill 已載入
- ✅ 服務器可以從其他電腦訪問
- ✅ CORS 設置正確，支援 credentials
- ✅ API 端點正常響應

## 注意事項

1. 確保防火牆允許 3001 和 5173 端口的連線
2. 如果仍有問題，檢查網路設置和路由器配置
3. 在生產環境中，建議使用更嚴格的 CORS 設置
4. 建議使用 HTTPS 以提高安全性

## 修改的文件

- `src/main.tsx` - 添加 crypto.randomUUID polyfill
- `src/utils/api/apiConfig.ts` - 自動檢測服務器地址
- `server/index.js` - 修改監聽設置和 CORS 配置
- `test-connection.html` - 新增測試工具
