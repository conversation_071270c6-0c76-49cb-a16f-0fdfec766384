import _1_54inch from './1_54inch';
import _2_13inch from './2_13inch';
import _2_66inch from './2_66inch';
import _2_9inch from './2_9inch';
import _3_5inch from './3_5inch';
import _3_7inch from './3_7inch';
import _4_2inch from './4_2inch';
import _5_83inch from './5_83inch';
import _6inch from './6inch';
import _7_5inch from './7_5inch';

/**
 * 從 screens 目錄動態生成的尺寸映射關係
 * 鍵為尺寸標識符，如 "2.9"，值為實際分辨率，如 "128x296"
 */
export const getScreenSizeMap = (): Record<string, string> => {
  // 導入所有屏幕尺寸配置
  const screenConfigs = [
    _1_54inch,
    _2_13inch,
    _2_66inch,
    _2_9inch,
    _3_5inch,
    _3_7inch,
    _4_2inch,
    _5_83inch,
    _6inch,
    _7_5inch,
    // 將來新增的屏幕配置會自動在這裡導入
  ];

  // 生成尺寸映射
  const sizeMap: Record<string, string> = {};

  screenConfigs.forEach(config => {
    // 提取尺寸標識符，如 "2.9"（移除英寸單位）
    const sizeIdentifier = config.name.replace('"', '');

    // 生成分辨率字符串
    const resolution = `${config.width}x${config.height}`;

    // 添加映射關係
    sizeMap[sizeIdentifier] = resolution;
  });

  return sizeMap;
};

/**
 * 獲取屏幕配置詳情
 * 返回 {id, name, displayName, width, height, supportedColors}
 */
export const getScreenConfigs = () => {
  return [
    _1_54inch,
    _2_13inch,
    _2_66inch,
    _2_9inch,
    _3_5inch,
    _3_7inch,
    _4_2inch,
    _5_83inch,
    _6inch,
    _7_5inch,
    // 將來新增的屏幕配置會自動在這裡返回
  ];
};

/**
 * 獲取尺寸標識符到寬高的映射
 * 鍵為尺寸標識符，如 "2.9"，值為 {width, height} 對象
 */
export const getScreenDimensionsMap = (): Record<string, {width: number, height: number}> => {
  const screenConfigs = getScreenConfigs();
  const dimensionsMap: Record<string, {width: number, height: number}> = {};

  screenConfigs.forEach(config => {
    // 提取尺寸標識符，如 "2.9"（移除英寸單位）
    const sizeIdentifier = config.name.replace('"', '');

    // 添加寬高映射
    dimensionsMap[sizeIdentifier] = {
      width: config.width,
      height: config.height
    };
  });

  return dimensionsMap;
};
