#!/usr/bin/env node

/**
 * EPD Manager 安全檢查腳本
 * 用於快速檢測系統的安全配置狀態
 *
 * 使用方法:
 * node docs/plan/secure/security-check-script.js
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SecurityChecker {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.passed = [];
    this.rootDir = path.join(__dirname, '../../..');
  }

  // 添加問題
  addIssue(level, category, message, file = null) {
    const issue = {
      level,      // 'critical', 'high', 'medium', 'low'
      category,   // 'jwt', 'storage', 'api', 'config'
      message,
      file,
      timestamp: new Date().toISOString()
    };

    if (level === 'critical' || level === 'high') {
      this.issues.push(issue);
    } else if (level === 'medium') {
      this.warnings.push(issue);
    } else {
      this.passed.push(issue);
    }
  }

  // 檢查硬編碼密鑰
  checkHardcodedSecrets() {
    console.log('🔍 檢查硬編碼密鑰...');
    
    const dangerousSecrets = [
      'epd-manager-jwt-secret-key',
      'your_jwt_secret_here',
      'your-super-secret-jwt-key-change-this-in-production',
      'your_jwt_secret',
      'jwt-secret-key'
    ];

    const filesToCheck = [
      'server/utils/auth.js',
      'server/utils/jwtUtils.js',
      'server/index.js',
      'server/routes/gatewayApi.js',
      'server/tests/generate-token.js'
    ];

    let foundSecrets = false;

    filesToCheck.forEach(filePath => {
      const fullPath = path.join(this.rootDir, filePath);
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        dangerousSecrets.forEach(secret => {
          if (content.includes(secret)) {
            this.addIssue('critical', 'jwt', 
              `發現硬編碼密鑰: "${secret}"`, filePath);
            foundSecrets = true;
          }
        });
      }
    });

    if (!foundSecrets) {
      this.addIssue('low', 'jwt', '未發現硬編碼密鑰', null);
    }
  }

  // 檢查環境變數配置
  checkEnvironmentConfig() {
    console.log('🔍 檢查環境變數配置...');
    
    const envFiles = ['.env', '.env.example', 'release/.env.example'];
    
    envFiles.forEach(envFile => {
      const envPath = path.join(this.rootDir, envFile);
      if (fs.existsSync(envPath)) {
        const content = fs.readFileSync(envPath, 'utf8');
        
        // 檢查 JWT_SECRET 配置
        if (content.includes('JWT_SECRET=')) {
          const match = content.match(/JWT_SECRET=(.+)/);
          if (match) {
            const secret = match[1].trim();
            if (secret.length < 32) {
              this.addIssue('high', 'jwt', 
                `JWT_SECRET 長度不足 (${secret.length} < 32)`, envFile);
            } else if (secret.includes('change-this') || secret.includes('your-')) {
              this.addIssue('high', 'jwt', 
                `JWT_SECRET 使用預設值`, envFile);
            } else {
              this.addIssue('low', 'jwt', 
                `JWT_SECRET 配置正確`, envFile);
            }
          }
        } else {
          this.addIssue('medium', 'jwt', 
            `缺少 JWT_SECRET 配置`, envFile);
        }
      }
    });
  }

  // 檢查 Token 過期配置
  checkTokenExpiry() {
    console.log('🔍 檢查 Token 過期配置...');
    
    const filesToCheck = [
      'server/utils/jwtUtils.js',
      'server/routes/gatewayApi.js',
      'server/utils/auth.js'
    ];

    filesToCheck.forEach(filePath => {
      const fullPath = path.join(this.rootDir, filePath);
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // 檢查長期有效的 Token
        if (content.includes('30d') || content.includes('30 * 24 * 60 * 60')) {
          this.addIssue('medium', 'jwt', 
            'Gateway Token 有效期過長 (30天)', filePath);
        }
        
        // 檢查用戶 Token 過期時間
        if (content.includes('24h')) {
          this.addIssue('low', 'jwt', 
            '用戶 Token 有效期適中 (24小時)', filePath);
        }
      }
    });
  }

  // 檢查前端 Token 存儲
  checkFrontendStorage() {
    console.log('🔍 檢查前端 Token 存儲...');
    
    const authStorePath = path.join(this.rootDir, 'src/store/authStore.ts');
    if (fs.existsSync(authStorePath)) {
      const content = fs.readFileSync(authStorePath, 'utf8');
      
      if (content.includes('localStorage')) {
        this.addIssue('medium', 'storage', 
          '前端使用 localStorage 存儲 Token', 'src/store/authStore.ts');
      }
      
      if (content.includes('persist')) {
        this.addIssue('medium', 'storage', 
          '前端持久化存儲 Token', 'src/store/authStore.ts');
      }
      
      if (content.includes('httpOnly: true')) {
        this.addIssue('low', 'storage', 
          '使用 HttpOnly Cookie', 'src/store/authStore.ts');
      }
    }
  }

  // 檢查 API 端點安全性
  checkApiSecurity() {
    console.log('🔍 檢查 API 端點安全性...');
    
    const authApiPath = path.join(this.rootDir, 'server/routes/authApi.js');
    if (fs.existsSync(authApiPath)) {
      const content = fs.readFileSync(authApiPath, 'utf8');
      
      // 檢查是否直接返回 Token
      if (content.includes('res.json({') && content.includes('token:')) {
        this.addIssue('medium', 'api', 
          'API 端點直接返回 Token', 'server/routes/authApi.js');
      }
      
      // 檢查認證中間件使用
      if (content.includes('authenticate,')) {
        this.addIssue('low', 'api', 
          '使用認證中間件保護 API', 'server/routes/authApi.js');
      }
    }
  }

  // 檢查資料庫 Token 存儲
  checkDatabaseStorage() {
    console.log('🔍 檢查資料庫 Token 存儲...');
    
    const gatewayApiPath = path.join(this.rootDir, 'server/routes/gatewayApi.js');
    if (fs.existsSync(gatewayApiPath)) {
      const content = fs.readFileSync(gatewayApiPath, 'utf8');
      
      if (content.includes('token: token') || content.includes('websocket: wsInfo')) {
        this.addIssue('high', 'storage', 
          '資料庫中明文存儲 Token', 'server/routes/gatewayApi.js');
      }
    }
  }

  // 檢查測試檔案中的敏感資訊
  checkTestFiles() {
    console.log('🔍 檢查測試檔案...');
    
    const testDir = path.join(this.rootDir, 'server/tests');
    if (fs.existsSync(testDir)) {
      const testFiles = fs.readdirSync(testDir).filter(file => file.endsWith('.js'));
      
      testFiles.forEach(file => {
        const filePath = path.join(testDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        if (content.includes('your_jwt_secret_here')) {
          this.addIssue('medium', 'jwt', 
            '測試檔案包含硬編碼密鑰', `server/tests/${file}`);
        }
      });
    }
  }

  // 生成安全報告
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 EPD Manager 安全檢查報告');
    console.log('='.repeat(60));
    
    // 統計
    const criticalCount = this.issues.filter(i => i.level === 'critical').length;
    const highCount = this.issues.filter(i => i.level === 'high').length;
    const mediumCount = this.warnings.filter(i => i.level === 'medium').length;
    
    console.log(`\n📈 安全狀態統計:`);
    console.log(`🔴 嚴重問題: ${criticalCount}`);
    console.log(`🟠 高風險問題: ${highCount}`);
    console.log(`🟡 中風險問題: ${mediumCount}`);
    console.log(`🟢 通過檢查: ${this.passed.length}`);
    
    // 嚴重和高風險問題
    if (this.issues.length > 0) {
      console.log(`\n🚨 需要立即處理的問題:`);
      this.issues.forEach((issue, index) => {
        const icon = issue.level === 'critical' ? '🔴' : '🟠';
        console.log(`${icon} ${index + 1}. [${issue.category.toUpperCase()}] ${issue.message}`);
        if (issue.file) {
          console.log(`   📁 檔案: ${issue.file}`);
        }
      });
    }
    
    // 中風險警告
    if (this.warnings.length > 0) {
      console.log(`\n⚠️  建議改善的問題:`);
      this.warnings.forEach((warning, index) => {
        console.log(`🟡 ${index + 1}. [${warning.category.toUpperCase()}] ${warning.message}`);
        if (warning.file) {
          console.log(`   📁 檔案: ${warning.file}`);
        }
      });
    }
    
    // 安全建議
    console.log(`\n💡 安全建議:`);
    if (criticalCount > 0) {
      console.log(`🔴 立即修復所有嚴重問題，系統存在重大安全風險`);
    }
    if (highCount > 0) {
      console.log(`🟠 在 1 週內修復高風險問題`);
    }
    if (mediumCount > 0) {
      console.log(`🟡 在 1 個月內改善中風險問題`);
    }
    
    console.log(`\n📋 快速修復步驟:`);
    console.log(`1. 設置強密鑰: export JWT_SECRET=$(openssl rand -base64 64)`);
    console.log(`2. 移除硬編碼密鑰: 搜尋並替換所有預設密鑰`);
    console.log(`3. 縮短 Token 有效期: Gateway Token 改為 24 小時`);
    console.log(`4. 加密資料庫存儲: 不要明文存儲 Token`);
    
    // 總體評分
    const totalChecks = this.issues.length + this.warnings.length + this.passed.length;
    const score = Math.max(0, 100 - (criticalCount * 40) - (highCount * 20) - (mediumCount * 10));
    
    console.log(`\n🎯 安全評分: ${score}/100`);
    if (score >= 80) {
      console.log(`✅ 安全狀態良好`);
    } else if (score >= 60) {
      console.log(`⚠️  安全狀態一般，需要改善`);
    } else {
      console.log(`❌ 安全狀態不佳，存在重大風險`);
    }
    
    console.log('\n' + '='.repeat(60));
    console.log(`檢查完成時間: ${new Date().toLocaleString()}`);
    console.log('='.repeat(60));
  }

  // 執行所有檢查
  runAllChecks() {
    console.log('🚀 開始 EPD Manager 安全檢查...\n');
    
    this.checkHardcodedSecrets();
    this.checkEnvironmentConfig();
    this.checkTokenExpiry();
    this.checkFrontendStorage();
    this.checkApiSecurity();
    this.checkDatabaseStorage();
    this.checkTestFiles();
    
    this.generateReport();
  }
}

// 執行檢查
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new SecurityChecker();
  checker.runAllChecks();
}

export default SecurityChecker;
