// tests/storeDataApi.test.js
const request = require('supertest');
const express = require('express');
const { createMockCollection } = require('./helpers/mockDb');
const createMockStoreDataApi = require('./helpers/mockStoreDataApi');

// 防止實際路由檔案被引入
jest.mock('../routes/storeDataApi', () => {
  return {};
});

describe('商店資料 API 測試', () => {
  let app;
  let mockCollection;
  let router;
  beforeEach(() => {
    // 創建 Express 應用
    app = express();

    // 初始化模擬資料
    const initialData = [
      { _id: '1', name: '商店1', address: '地址1' },
      { _id: '2', name: '商店2', address: '地址2' },
    ];

    // 創建模擬集合
    mockCollection = createMockCollection(initialData);

    // 創建模擬的商店資料 API 路由
    router = createMockStoreDataApi(mockCollection);

    // 使用模擬路由
    app.use(express.json());
    app.use('/api', router);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/storeData', () => {
    test('應該返回所有商店資料', async () => {
      const response = await request(app).get('/api/storeData');

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
      expect(mockCollection.find).toHaveBeenCalled();
    });

    test('應該處理獲取商店資料時的錯誤', async () => {
      // 模擬錯誤
      const mockToArray = jest.fn().mockRejectedValueOnce(new Error('資料庫錯誤'));
      mockCollection.find.mockReturnValueOnce({
        toArray: mockToArray
      });

      const response = await request(app).get('/api/storeData');

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('GET /api/storeData/:id', () => {
    test('應該返回指定 ID 的商店資料', async () => {
      mockCollection.findOne = jest.fn().mockReturnValue({
        _id: '1',
        name: '商店1',
        address: '地址1'
      });

      const response = await request(app).get('/api/storeData/1');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('name', '商店1');
      expect(mockCollection.findOne).toHaveBeenCalled();
    });

    test('應該處理找不到商店資料的情況', async () => {
      mockCollection.findOne = jest.fn().mockReturnValue(null);

      const response = await request(app).get('/api/storeData/999');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', '找不到商店資料');
    });
  });

  describe('POST /api/storeData', () => {
    test('應該創建新商店資料', async () => {
      const newStore = {
        name: '新商店',
        address: '新地址'
      };

      const response = await request(app)
        .post('/api/storeData')
        .send(newStore);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(mockCollection.insertOne).toHaveBeenCalledWith(expect.objectContaining(newStore));
    });

    test('應該處理創建商店資料時的錯誤', async () => {
      mockCollection.insertOne.mockRejectedValueOnce(new Error('插入資料錯誤'));

      const response = await request(app)
        .post('/api/storeData')
        .send({ name: '錯誤商店' });

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('PUT /api/storeData/:id', () => {
    test('應該更新商店資料', async () => {
      const updatedStore = {
        name: '更新商店',
        address: '更新地址'
      };

      const response = await request(app)
        .put('/api/storeData/1')
        .send(updatedStore);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', '商店資料更新成功');
      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { _id: '1' },
        { $set: updatedStore }
      );
    });
  });

  describe('DELETE /api/storeData/:id', () => {
    test('應該刪除商店資料', async () => {
      const response = await request(app).delete('/api/storeData/1');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', '商店資料刪除成功');
      expect(mockCollection.deleteOne).toHaveBeenCalledWith({ _id: '1' });
    });
  });
});
