import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../../store/authStore';
import { Eye, EyeOff, AlertCircle, ChevronDown, ChevronUp } from 'lucide-react';
import { buildEndpointUrl } from '../../utils/api/apiConfig';
import { CircuitBackground } from './CircuitBackground';

export const LoginPage: React.FC = () => {
  const { t } = useTranslation();
  const { login, loading, error, clearError } = useAuthStore();

  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [localLoading, setLocalLoading] = useState(false);

  // 進階設定相關狀態
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');

  // 檢查系統是否已初始化
  useEffect(() => {
    const checkInitialization = async () => {
      try {
        const response = await fetch(buildEndpointUrl('auth', 'init-check'));
        const data = await response.json();

        setIsInitializing(!data.initialized);
      } catch (error) {
        console.error('檢查系統初始化狀態失敗:', error);
      }
    };

    checkInitialization();
  }, []);

  // 處理登入
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('登入按鈕被點擊');

    if (!username || !password) {
      console.log('用戶名或密碼為空');
      return;
    }

    console.log('嘗試登入:', { username, rememberMe });
    try {
      await login(username, password, rememberMe);
      console.log('登入函數執行完成');
    } catch (error) {
      console.error('登入過程中發生錯誤:', error);
    }
  };

  // 處理系統初始化
  const handleInitialize = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username || !password || password !== confirmPassword) {
      return;
    }

    try {
      // 設置 loading 狀態
      setLocalLoading(true);

      try {
        const response = await fetch(buildEndpointUrl('auth', 'initialize'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username,
            password,
            confirmPassword,
            // 只有在顯示進階設定時才傳送這些欄位
            ...(showAdvancedSettings && {
              name,
              email,
              phone
            })
          }),
        });

        const data = await response.json();

        if (!response.ok) {
          console.warn('初始化API返回錯誤:', data.error);
          // 顯示具體的錯誤訊息
          alert(data.error || t('auth.initializeFailed'));
          // 重置 loading 狀態
          setLocalLoading(false);
          return; // 終止後續操作
        } else {
          console.log('系統初始化API調用成功');
        }
      } catch (initError: any) {
        console.warn('初始化API調用失敗:', initError);
        // 顯示錯誤訊息
        alert(initError.message || t('auth.initializeFailed'));
        // 重置 loading 狀態
        setLocalLoading(false);
        return; // 終止後續操作
      }

      // 無論初始化API是否成功，都檢查初始化狀態
      console.log('正在檢查初始化狀態...');

      // 等待一秒，確保資料庫操作完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 檢查初始化狀態
      const checkResponse = await fetch(buildEndpointUrl('auth', 'init-check'));
      const checkData = await checkResponse.json();

      console.log('初始化狀態檢查結果:', checkData);

      // 更新初始化狀態
      setIsInitializing(!checkData.initialized);

      // 如果已初始化，則顯示登入表單
      if (checkData.initialized) {
        console.log('系統已初始化，顯示登入表單');
        // 清空確認密碼欄位
        setConfirmPassword('');
        // 顯示成功消息
        alert(t('auth.initializeSuccess'));
      } else {
        console.log('系統尚未初始化，保持在初始化頁面');
        // 如果仍未初始化，可能是真的失敗了
        alert(t('auth.initializeFailed'));
      }

      // 重置 loading 狀態
      setLocalLoading(false);

    } catch (error: any) {
      console.error('檢查初始化狀態失敗:', error);
      // 顯示錯誤消息
      alert(t('auth.checkInitStatusFailed'));
      // 重置 loading 狀態
      setLocalLoading(false);
    }
  };

  // 切換顯示密碼
  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  // 系統初始化表單額外字段
  const [confirmPassword, setConfirmPassword] = useState('');

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-sky-50 via-sky-100 to-sky-200 relative overflow-hidden">
      {/* 電路板背景 */}
      <CircuitBackground />

      {/* 使用天空藍色調和玻璃效果 */}
      <div className="w-full max-w-md p-8 space-y-8 rounded-lg bg-gradient-to-br from-white/80 via-white/70 to-sky-50/80 backdrop-blur-md border border-sky-300/30 shadow-[0_0_30px_5px_rgba(125,211,252,0.3)] animate-subtle-pulse relative overflow-hidden" style={{ zIndex: 10, position: 'relative' }}>
        {/* 動態光影效果 */}
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-sky-100/30 via-sky-200/20 to-sky-300/10 rounded-full blur-3xl animate-glass-light"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-sky-100/30 via-sky-200/20 to-sky-300/10 rounded-full blur-3xl animate-glass-light animation-delay-2000"></div>

        {/* CPU發光效果 */}
        <div className="absolute inset-0 bg-gradient-to-br from-sky-400/5 via-transparent to-transparent"></div>
        <div className="text-center">
          <h1 className="text-3xl font-bold text-sky-800">
            {isInitializing ? t('auth.initializeSystem') : t('auth.login')}
          </h1>
          <p className="mt-2 text-sm text-sky-700">
            {isInitializing ? t('auth.createAdminAccount') : t('auth.welcomeBack')}
          </p>
        </div>

        {error && (
          <div className="p-4 rounded-md flex items-start bg-red-50/80 backdrop-blur-sm border border-red-200/70">
            <AlertCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5" />
            <div>
              <p className="text-sm text-red-800 font-medium">{error}</p>
              <button
                onClick={clearError}
                className="text-xs text-red-600 hover:text-red-800 mt-1 transition-colors"
              >
                {t('common.dismiss')}
              </button>
            </div>
          </div>
        )}

        <form onSubmit={isInitializing ? handleInitialize : handleLogin} className="mt-8 space-y-6">
          <div className="space-y-4">
            {/* 用戶名 */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-sky-800">
                {t('auth.username')}
              </label>
              <input
                id="username"
                name="username"
                type="text"
                required
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-sky-200 rounded-md shadow-sm bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-sky-400 focus:border-sky-400"
                placeholder={t('auth.usernamePlaceholder')}
              />
            </div>

            {/* 密碼 */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-sky-800">
                {t('auth.password')}
              </label>
              <div className="relative mt-1">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="block w-full px-3 py-2 border border-sky-200 rounded-md shadow-sm bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-sky-400 focus:border-sky-400"
                  placeholder={t('auth.passwordPlaceholder')}
                />
                <button
                  type="button"
                  onClick={toggleShowPassword}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-sky-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-sky-400" />
                  )}
                </button>
              </div>
            </div>

            {/* 系統初始化時的確認密碼 */}
            {isInitializing && (
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-sky-800">
                  {t('auth.confirmPassword')}
                </label>
                <div className="relative mt-1">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showPassword ? 'text' : 'password'}
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="block w-full px-3 py-2 border border-sky-200 rounded-md shadow-sm bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-sky-400 focus:border-sky-400"
                    placeholder={t('auth.confirmPasswordPlaceholder')}
                  />
                </div>
                {password && confirmPassword && password !== confirmPassword && (
                  <p className="mt-1 text-xs text-red-600 font-medium flex items-center">
                    <AlertCircle className="w-3 h-3 mr-1" />
                    {t('auth.passwordMismatch')}
                  </p>
                )}
              </div>
            )}

            {/* 進階設定切換按鈕 - 只在初始化時顯示 */}
            {isInitializing && (
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                  className="flex items-center text-sm text-sky-600 hover:text-sky-800 focus:outline-none transition-all duration-300 relative z-20"
                >
                  {showAdvancedSettings ? (
                    <>
                      <ChevronUp className="h-4 w-4 mr-1" />
                      {t('auth.hideAdvancedSettings')}
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-4 w-4 mr-1" />
                      {t('auth.showAdvancedSettings')}
                    </>
                  )}
                </button>
              </div>
            )}

            {/* 進階設定欄位 - 只在初始化且進階設定開啟時顯示 */}
            {isInitializing && showAdvancedSettings && (
              <div className="space-y-4 mt-4 p-4 bg-sky-50/80 rounded-md border border-sky-200 backdrop-blur-sm shadow-inner">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-sky-800">
                    {t('auth.name')}
                  </label>
                  <input
                    id="name"
                    name="name"
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-sky-200 rounded-md shadow-sm bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-sky-400 focus:border-sky-400"
                    placeholder={t('auth.namePlaceholder')}
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-sky-800">
                    {t('auth.email')}
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-sky-200 rounded-md shadow-sm bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-sky-400 focus:border-sky-400"
                    placeholder={t('auth.emailPlaceholder')}
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-sky-800">
                    {t('auth.phone')}
                  </label>
                  <input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-sky-200 rounded-md shadow-sm bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-sky-400 focus:border-sky-400"
                    placeholder={t('auth.phonePlaceholder')}
                  />
                </div>
              </div>
            )}

            {/* 記住我 */}
            <div className="flex items-center relative z-20">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 border-gray-300 rounded text-sky-600 focus:ring-sky-500"
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm text-sky-800">
                {t('auth.rememberMe')}
              </label>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={(isInitializing ? localLoading : loading) || (isInitializing && password !== confirmPassword)}
              className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md text-sm font-medium text-white bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 focus:ring-sky-500 shadow-lg shadow-sky-200/50 backdrop-blur-sm border-sky-400/20 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-300 ${
                (isInitializing ? localLoading : loading) ? 'opacity-70 cursor-not-allowed' : ''
              }`}
            >
              {(isInitializing ? localLoading : loading) ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {isInitializing ? t('auth.initializing') : t('auth.loggingIn')}
                </span>
              ) : (
                <span>{isInitializing ? t('auth.initialize') : t('auth.login')}</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
