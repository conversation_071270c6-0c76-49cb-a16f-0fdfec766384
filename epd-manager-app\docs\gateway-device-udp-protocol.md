# 網關設備 UDP 發現協議規範

## 1. 概述

本文檔定義了 EPD Manager App 與網關設備之間的 UDP 發現協議。該協議允許移動應用程序在本地網絡中發現網關設備，並獲取其基本信息，以便將其添加到 EPD Manager 系統中。

## 2. 協議基本信息

- **協議名稱**: EPD-GATEWAY-DISCOVERY
- **協議版本**: 1.0
- **傳輸協議**: UDP
- **默認端口**: 5000
- **編碼格式**: UTF-8
- **數據格式**: JSON

## 3. 消息類型

### 3.1 發現請求 (Discovery Request)

移動應用程序發送到本地網絡的廣播消息，用於發現網關設備。

```json
{
  "type": "discovery",
  "protocol": "EPD-GATEWAY-DISCOVERY",
  "version": "1.0",
  "timestamp": 1683270664000
}
```

| 字段 | 類型 | 必填 | 描述 |
|------|------|------|------|
| type | string | 是 | 消息類型，固定為 "discovery" |
| protocol | string | 是 | 協議名稱，固定為 "EPD-GATEWAY-DISCOVERY" |
| version | string | 是 | 協議版本，當前為 "1.0" |
| timestamp | number | 是 | 發送時間戳（毫秒） |

### 3.2 發現回應 (Discovery Response)

網關設備對發現請求的回應，包含網關的基本信息。

```json
{
  "type": "discovery-response",
  "protocol": "EPD-GATEWAY-DISCOVERY",
  "version": "1.0",
  "timestamp": 1683270664500,
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "model": "GW-2000",
  "name": "Gateway-1234",
  "firmwareVersion": "1.0.0",
  "capabilities": ["wifi", "bluetooth"],
  "status": "ready"
}
```

| 字段 | 類型 | 必填 | 描述 |
|------|------|------|------|
| type | string | 是 | 消息類型，固定為 "discovery-response" |
| protocol | string | 是 | 協議名稱，固定為 "EPD-GATEWAY-DISCOVERY" |
| version | string | 是 | 協議版本，當前為 "1.0" |
| timestamp | number | 是 | 回應時間戳（毫秒） |
| macAddress | string | 是 | 網關的 MAC 地址，格式為 XX:XX:XX:XX:XX:XX |
| model | string | 是 | 網關型號 |
| name | string | 否 | 網關名稱 |
| firmwareVersion | string | 否 | 固件版本 |
| capabilities | array | 否 | 網關功能列表 |
| status | string | 否 | 網關狀態 |

## 4. 通信流程

### 4.1 基本流程

1. 移動應用程序發送 UDP 廣播消息到本地網絡的指定端口（默認 5000）
2. 網關設備監聽該端口，接收廣播消息
3. 網關設備驗證消息格式和協議
4. 網關設備向發送方的 IP 地址和端口回應發現回應消息
5. 移動應用程序接收並處理回應，顯示發現的網關設備

### 4.2 時序圖

```mermaid
sequenceDiagram
    participant App as 移動應用
    participant Network as 本地網絡
    participant Gateway as 網關設備
    
    App->>Network: 發送 UDP 廣播 (Discovery Request)
    Network->>Gateway: 轉發廣播消息
    Gateway->>Gateway: 驗證消息格式和協議
    Gateway->>Network: 發送回應 (Discovery Response)
    Network->>App: 轉發回應消息
    App->>App: 處理回應，顯示網關信息
```

## 5. 網關設備實現指南

### 5.1 基本要求

1. 網關設備必須在啟動時開始監聽 UDP 端口 5000
2. 網關設備必須能夠處理 UDP 廣播消息
3. 網關設備必須驗證接收到的消息格式和協議
4. 網關設備必須能夠向發送方的 IP 地址和端口發送回應
5. 網關設備必須能夠處理多次發現請求，每次都做出回應

### 5.2 實現示例 (Python)

以下是使用 Python 實現網關設備 UDP 發現協議的示例代碼：

```python
import socket
import json
import time
import random
import uuid
from threading import Thread

# 配置
UDP_PORT = 5000
PROTOCOL = "EPD-GATEWAY-DISCOVERY"
VERSION = "1.0"

# 網關信息
gateway_info = {
    "macAddress": "AA:BB:CC:DD:EE:FF",  # 應替換為實際的 MAC 地址
    "model": "GW-2000",
    "name": f"Gateway-{random.randint(1000, 9999)}",
    "firmwareVersion": "1.0.0",
    "capabilities": ["wifi", "bluetooth"],
    "status": "ready"
}

def start_discovery_service():
    """啟動 UDP 發現服務"""
    # 創建 UDP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    
    # 綁定到指定端口
    sock.bind(('', UDP_PORT))
    
    print(f"UDP 發現服務已啟動，監聽端口 {UDP_PORT}")
    
    try:
        while True:
            # 接收數據
            data, addr = sock.recvfrom(1024)
            
            # 處理接收到的數據
            handle_discovery_request(sock, data, addr)
    except Exception as e:
        print(f"UDP 發現服務錯誤: {e}")
    finally:
        sock.close()

def handle_discovery_request(sock, data, addr):
    """處理發現請求"""
    try:
        # 解析 JSON 數據
        request = json.loads(data.decode('utf-8'))
        
        # 驗證消息格式和協議
        if (request.get('type') == 'discovery' and 
            request.get('protocol') == PROTOCOL and 
            request.get('version') == VERSION):
            
            # 創建回應消息
            response = {
                "type": "discovery-response",
                "protocol": PROTOCOL,
                "version": VERSION,
                "timestamp": int(time.time() * 1000),
                **gateway_info
            }
            
            # 添加隨機延遲，避免網絡擁塞
            delay = random.uniform(0.1, 0.5)
            time.sleep(delay)
            
            # 發送回應
            sock.sendto(json.dumps(response).encode('utf-8'), addr)
            
            print(f"已回應發現請求: {addr}")
    except json.JSONDecodeError:
        print(f"無效的 JSON 數據: {data}")
    except Exception as e:
        print(f"處理發現請求時出錯: {e}")

if __name__ == "__main__":
    # 生成隨機 MAC 地址（實際應用中應使用真實的 MAC 地址）
    mac_bytes = [random.randint(0, 255) for _ in range(6)]
    mac_address = ':'.join([f"{b:02X}" for b in mac_bytes])
    gateway_info["macAddress"] = mac_address
    
    # 啟動發現服務
    discovery_thread = Thread(target=start_discovery_service)
    discovery_thread.daemon = True
    discovery_thread.start()
    
    print(f"網關設備已啟動，MAC 地址: {mac_address}")
    
    # 保持主線程運行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("網關設備已停止")
```

### 5.3 實現示例 (Node.js)

以下是使用 Node.js 實現網關設備 UDP 發現協議的示例代碼：

```javascript
const dgram = require('dgram');
const os = require('os');

// 配置
const UDP_PORT = 5000;
const PROTOCOL = "EPD-GATEWAY-DISCOVERY";
const VERSION = "1.0";

// 獲取 MAC 地址
function getMacAddress() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        // 這裡應該使用實際的 MAC 地址獲取方法
        // 這只是一個示例，生成隨機 MAC 地址
        return Array.from({ length: 6 }, () => 
          Math.floor(Math.random() * 256).toString(16).padStart(2, '0')
        ).join(':').toUpperCase();
      }
    }
  }
  return 'AA:BB:CC:DD:EE:FF';
}

// 網關信息
const gatewayInfo = {
  macAddress: getMacAddress(),
  model: "GW-2000",
  name: `Gateway-${Math.floor(1000 + Math.random() * 9000)}`,
  firmwareVersion: "1.0.0",
  capabilities: ["wifi", "bluetooth"],
  status: "ready"
};

// 創建 UDP 服務器
const server = dgram.createSocket('udp4');

// 處理錯誤
server.on('error', (err) => {
  console.error(`UDP 服務器錯誤: ${err.stack}`);
  server.close();
});

// 處理消息
server.on('message', (msg, rinfo) => {
  try {
    // 解析 JSON 數據
    const request = JSON.parse(msg.toString());
    
    // 驗證消息格式和協議
    if (request.type === 'discovery' && 
        request.protocol === PROTOCOL && 
        request.version === VERSION) {
      
      // 創建回應消息
      const response = {
        type: "discovery-response",
        protocol: PROTOCOL,
        version: VERSION,
        timestamp: Date.now(),
        ...gatewayInfo
      };
      
      // 添加隨機延遲，避免網絡擁塞
      const delay = Math.random() * 400 + 100; // 100-500ms
      setTimeout(() => {
        // 發送回應
        const responseBuffer = Buffer.from(JSON.stringify(response));
        server.send(responseBuffer, 0, responseBuffer.length, rinfo.port, rinfo.address, (err) => {
          if (err) {
            console.error(`發送回應時出錯: ${err}`);
          } else {
            console.log(`已回應發現請求: ${rinfo.address}:${rinfo.port}`);
          }
        });
      }, delay);
    }
  } catch (error) {
    console.error(`處理消息時出錯: ${error}`);
  }
});

// 啟動服務器
server.on('listening', () => {
  const address = server.address();
  console.log(`UDP 發現服務已啟動，監聽 ${address.address}:${address.port}`);
  console.log(`網關設備已啟動，MAC 地址: ${gatewayInfo.macAddress}`);
});

// 綁定端口
server.bind(UDP_PORT);

// 處理進程終止
process.on('SIGINT', () => {
  console.log('網關設備已停止');
  server.close();
  process.exit();
});
```

## 6. 安全考慮

### 6.1 基本安全措施

1. **消息驗證**: 網關設備應驗證接收到的消息格式和協議，拒絕處理無效消息
2. **回應限制**: 網關設備可以限制回應頻率，避免被用於 DoS 攻擊
3. **IP 過濾**: 網關設備可以只回應來自特定 IP 範圍的請求

### 6.2 高級安全措施

1. **消息加密**: 可以考慮對消息進行加密，防止信息洩露
2. **設備認證**: 可以在協議中添加設備認證機制，確保只有合法設備能夠被發現
3. **挑戰-響應機制**: 可以實現挑戰-響應機制，防止重放攻擊

## 7. 擴展性考慮

### 7.1 協議版本控制

協議包含版本字段，便於未來擴展和兼容性管理。當協議需要更新時：

1. 增加版本號（如 1.0 -> 1.1 或 2.0）
2. 在文檔中明確說明版本差異
3. 確保新版本兼容舊版本，或提供明確的遷移路徑

### 7.2 可擴展字段

回應消息中的 `capabilities` 字段是一個數組，可以靈活添加新的功能標識，而不破壞協議兼容性。

### 7.3 自定義字段

網關設備可以在回應中添加自定義字段，提供額外信息。移動應用程序應忽略不認識的字段，確保向前兼容性。

## 8. 測試與驗證

### 8.1 測試工具

可以使用以下工具測試 UDP 發現協議：

1. **Wireshark**: 捕獲和分析 UDP 數據包
2. **netcat**: 發送和接收 UDP 消息
3. **自定義測試腳本**: 使用 Python 或 Node.js 編寫測試腳本

### 8.2 驗證步驟

1. 啟動網關設備模擬器
2. 使用測試工具發送發現請求
3. 驗證網關設備是否正確回應
4. 檢查回應消息格式和內容是否符合規範
5. 測試異常情況（如無效消息、高頻請求等）
