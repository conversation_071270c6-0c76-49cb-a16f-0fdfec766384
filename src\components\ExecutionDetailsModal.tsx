import React from 'react';
import { X, CheckCircle, XCircle, Clock, Server, Monitor, Wifi } from 'lucide-react';
import { formatDateTime } from '../services/refreshPlanApi';

interface ExecutionDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  execution: any;
}

const ExecutionDetailsModal: React.FC<ExecutionDetailsModalProps> = ({
  isOpen,
  onClose,
  execution
}) => {
  if (!isOpen || !execution) return null;

  // 安全處理執行數據，確保所有字段都是可渲染的
  const safeExecution = {
    ...execution,
    planName: typeof execution.planName === 'string' ? execution.planName : '未知計畫',
    status: typeof execution.status === 'string' ? execution.status : 'unknown',
    startTime: execution.startTime || null,
    endTime: execution.endTime || null,
    result: execution.result && typeof execution.result === 'object' ? {
      ...execution.result,
      totalDevices: Number(execution.result.totalDevices) || 0,
      successDevices: Number(execution.result.successDevices) || 0,
      failedDevices: Number(execution.result.failedDevices) || 0,
      processingTime: Number(execution.result.processingTime) || 0,
      summary: execution.result.summary && typeof execution.result.summary === 'object' ? {
        ...execution.result.summary,
        successRate: Number(execution.result.summary.successRate) || 0,
        totalGatewaysUsed: Number(execution.result.summary.totalGatewaysUsed) || 0,
        averageProcessingTimePerDevice: Number(execution.result.summary.averageProcessingTimePerDevice) || 0
      } : {},
      gatewayStats: execution.result.gatewayStats || {},
      deviceDetails: Array.isArray(execution.result.deviceDetails) ? execution.result.deviceDetails.map((device: any) => ({
        ...device,
        deviceName: typeof device.deviceName === 'string' ? device.deviceName : '未知設備',
        macAddress: typeof device.macAddress === 'string' ? device.macAddress : '-',
        success: Boolean(device.success),
        gatewayName: typeof device.gatewayName === 'string' ? device.gatewayName : (typeof device.gatewayId === 'string' ? device.gatewayId : '-'),
        gatewayId: typeof device.gatewayId === 'string' ? device.gatewayId : '-',
        processingTime: Number(device.processingTime) || 0,
        error: device.error ? (typeof device.error === 'string' ? device.error :
               device.error.reason || device.error.message || JSON.stringify(device.error)) : null
      })) : []
    } : {},
    errors: Array.isArray(execution.errors) ? execution.errors.map((error: any) => ({
      type: typeof error.type === 'string' ? error.type : '執行錯誤',
      message: typeof error.message === 'string' ? error.message :
               error.reason || error.error ||
               (typeof error === 'string' ? error : JSON.stringify(error)),
      timestamp: error.timestamp || null
    })) : []
  };

  // 格式化持續時間
  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}分${seconds % 60}秒`;
    } else {
      return `${Math.floor(seconds / 3600)}小時${Math.floor((seconds % 3600) / 60)}分`;
    }
  };

  // 獲取狀態顏色
  const getStatusColor = (success: boolean) => {
    return success ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100';
  };

  // 獲取狀態圖標
  const getStatusIcon = (success: boolean) => {
    return success ? <CheckCircle className="w-4 h-4" /> : <XCircle className="w-4 h-4" />;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        {/* 標題欄 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Monitor className="w-6 h-6 text-blue-500 mr-2" />
            <h2 className="text-xl font-semibold text-gray-900">
              執行詳情 - {safeExecution.planName}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 內容 */}
        <div className="p-6 space-y-6">
          {/* 執行概要 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">執行概要</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-sm text-gray-600">執行狀態</p>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  safeExecution.status === 'completed' ? 'text-green-600 bg-green-100' :
                  safeExecution.status === 'failed' ? 'text-red-600 bg-red-100' : 'text-blue-600 bg-blue-100'
                }`}>
                  {safeExecution.status === 'completed' ? '已完成' :
                   safeExecution.status === 'failed' ? '失敗' : '執行中'}
                </span>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">開始時間</p>
                <p className="text-sm font-medium text-gray-900">
                  {safeExecution.startTime ? formatDateTime(safeExecution.startTime) : '-'}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">結束時間</p>
                <p className="text-sm font-medium text-gray-900">
                  {safeExecution.endTime ? formatDateTime(safeExecution.endTime) : '-'}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">執行時長</p>
                <p className="text-sm font-medium text-gray-900">
                  {safeExecution.result?.processingTime ? formatDuration(safeExecution.result.processingTime) : '-'}
                </p>
              </div>
            </div>
          </div>

          {/* 設備統計 */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">設備統計</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 rounded-lg p-4 text-center">
                <Monitor className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-blue-600">總設備數</p>
                <p className="text-2xl font-bold text-blue-900">{safeExecution.result.totalDevices}</p>
              </div>
              <div className="bg-green-50 rounded-lg p-4 text-center">
                <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-green-600">成功設備</p>
                <p className="text-2xl font-bold text-green-900">{safeExecution.result.successDevices}</p>
              </div>
              <div className="bg-red-50 rounded-lg p-4 text-center">
                <XCircle className="w-8 h-8 text-red-600 mx-auto mb-2" />
                <p className="text-sm text-red-600">失敗設備</p>
                <p className="text-2xl font-bold text-red-900">{safeExecution.result.failedDevices}</p>
              </div>
              <div className="bg-purple-50 rounded-lg p-4 text-center">
                <Clock className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                <p className="text-sm text-purple-600">成功率</p>
                <p className="text-2xl font-bold text-purple-900">
                  {safeExecution.result.summary.successRate}%
                </p>
              </div>
            </div>
          </div>

          {/* 網關統計 */}
          {safeExecution.result.gatewayStats && Object.keys(safeExecution.result.gatewayStats).length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">網關統計</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(safeExecution.result.gatewayStats).map(([gatewayId, stats]: [string, any]) => (
                  <div key={gatewayId} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Server className="w-5 h-5 text-gray-600 mr-2" />
                      <h4 className="font-medium text-gray-900">{stats.gatewayName || gatewayId}</h4>
                    </div>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">處理設備:</span>
                        <span className="font-medium">{Number(stats.deviceCount) || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-green-600">成功:</span>
                        <span className="font-medium text-green-600">{Number(stats.successCount) || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-red-600">失敗:</span>
                        <span className="font-medium text-red-600">{Number(stats.failedCount) || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-purple-600">成功率:</span>
                        <span className="font-medium text-purple-600">
                          {Number(stats.deviceCount) > 0 ? Math.round((Number(stats.successCount) / Number(stats.deviceCount)) * 100) : 0}%
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 設備詳細結果 */}
          {safeExecution.result.deviceDetails && safeExecution.result.deviceDetails.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">設備詳細結果</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        設備編號
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        MAC地址
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        狀態
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        網關
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        處理時間
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        錯誤信息
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {safeExecution.result.deviceDetails.map((device: any, index: number) => (
                      <tr key={device.deviceId || index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {device.deviceName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {device.macAddress}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(device.success)}`}>
                            {getStatusIcon(device.success)}
                            <span className="ml-1">{device.success ? '成功' : '失敗'}</span>
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="flex items-center">
                            <Wifi className="w-4 h-4 text-gray-400 mr-1" />
                            {device.gatewayName}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {device.processingTime ? formatDuration(device.processingTime) : '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-red-600 max-w-xs truncate">
                          {device.error || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* 錯誤信息 */}
          {safeExecution.errors && safeExecution.errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-red-900 mb-4">錯誤信息</h3>
              <div className="space-y-2">
                {safeExecution.errors.map((error: any, index: number) => (
                  <div key={index} className="bg-white rounded p-3">
                    <div className="flex items-center mb-1">
                      <XCircle className="w-4 h-4 text-red-600 mr-2" />
                      <span className="text-sm font-medium text-red-900">
                        {error.type}
                      </span>
                      <span className="text-xs text-gray-500 ml-auto">
                        {error.timestamp ? formatDateTime(error.timestamp) : ''}
                      </span>
                    </div>
                    <p className="text-sm text-red-700">
                      {error.message}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 底部按鈕 */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            關閉
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExecutionDetailsModal;
