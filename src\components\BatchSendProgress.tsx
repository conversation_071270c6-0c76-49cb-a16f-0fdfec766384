import React, { useState, useEffect, useCallback } from 'react';
import { X, ChevronDown, ChevronUp } from 'lucide-react';
import { subscribeToBatchProgress, WebSocketEvent } from '../utils/websocketClient';

interface BatchSendProgressProps {
  isVisible: boolean;
  onClose: () => void;
  onCancel?: () => void;
  batchId?: string;
}

interface ProgressData {
  totalDevices: number;
  completedDevices: number;
  failedDevices: number;
  currentDevice?: string;
  currentDeviceMac?: string;
  status: 'preparing' | 'running' | 'paused' | 'completed' | 'cancelled' | 'error';
  startTime?: number;
  estimatedTimeRemaining?: number;
  queueCycles?: number;
  waitCycles?: number;
  smartSelectionStats?: {
    totalAutoModeDevices: number;
    usedBackupGateway: number;
    primaryGatewayBusy: number;
  };
  error?: string;
}

export const BatchSendProgress: React.FC<BatchSendProgressProps> = ({
  isVisible,
  onClose,
  onCancel,
  batchId
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [progressData, setProgressData] = useState<ProgressData>({
    totalDevices: 0,
    completedDevices: 0,
    failedDevices: 0,
    status: 'preparing'
  });

  // 計算進度百分比
  const progressPercentage = progressData.totalDevices > 0 
    ? Math.round((progressData.completedDevices / progressData.totalDevices) * 100)
    : 0;

  // 計算成功率
  const successRate = progressData.completedDevices > 0
    ? Math.round(((progressData.completedDevices - progressData.failedDevices) / progressData.completedDevices) * 100)
    : 100;

  // 格式化時間
  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}秒`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  };

  // 計算已用時間
  const getElapsedTime = () => {
    if (!progressData.startTime) return 0;
    return Math.floor((Date.now() - progressData.startTime) / 1000);
  };

  // 狀態顏色
  const getStatusColor = () => {
    switch (progressData.status) {
      case 'preparing': return 'bg-blue-500';
      case 'running': return 'bg-green-500';
      case 'paused': return 'bg-yellow-500';
      case 'completed': return 'bg-green-600';
      case 'cancelled': return 'bg-gray-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-blue-500';
    }
  };

  // 狀態文字
  const getStatusText = () => {
    switch (progressData.status) {
      case 'preparing': return '準備中...';
      case 'running': return '傳送中';
      case 'paused': return '已暫停';
      case 'completed': return '已完成';
      case 'cancelled': return '已取消';
      case 'error': return '發生錯誤';
      default: return '未知狀態';
    }
  };

  // 使用真實的WebSocket進度數據
  useEffect(() => {
    if (!isVisible || !batchId) return;

    console.log(`🔗 BatchSendProgress: 訂閱批量傳送進度 ${batchId}`);

    // 初始化進度數據
    setProgressData({
      totalDevices: 0,
      completedDevices: 0,
      failedDevices: 0,
      status: 'preparing',
      startTime: Date.now()
    });

    // 訂閱WebSocket進度更新
    const unsubscribe = subscribeToBatchProgress(batchId, (event: WebSocketEvent) => {
      console.log(`📊 收到批量傳送進度事件:`, event);

      if (event.type === 'batch_progress') {
        setProgressData(prev => ({
          ...prev,
          totalDevices: event.totalDevices,
          completedDevices: event.completedDevices,
          failedDevices: event.failedDevices,
          status: event.status,
          currentDevice: event.currentDevice?.name || event.currentDevice?.id,
          currentDeviceMac: event.currentDevice?.macAddress,
          queueCycles: event.queueCycles,
          waitCycles: event.waitCycles,
          smartSelectionStats: event.smartSelectionStats,
          estimatedTimeRemaining: event.estimatedTimeRemaining,
          error: event.error
        }));
      } else if (event.type === 'batch_complete') {
        console.log(`🎯 批量傳送完成:`, event.result);

        // 根據結果確定最終狀態
        let finalStatus: 'completed' | 'error' = 'completed';
        if (event.result.successCount === 0 && event.result.failedCount > 0) {
          finalStatus = 'error';
        }

        setProgressData(prev => ({
          ...prev,
          status: finalStatus,
          completedDevices: event.result.successCount,
          failedDevices: event.result.failedCount,
          totalDevices: event.result.totalCount,
          error: finalStatus === 'error' ? '批量發送失敗，請檢查網關連接狀態' : undefined
        }));

        // 3秒後自動關閉
        setTimeout(() => {
          onClose();
        }, 3000);
      }
    });

    return () => {
      console.log(`🔗 BatchSendProgress: 取消訂閱批量傳送進度 ${batchId}`);
      unsubscribe();
    };
  }, [isVisible, batchId]); // 移除 onClose 依賴

  if (!isVisible) return null;

  return (
    <div 
      className="fixed bottom-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)]"
      style={{
        backdropFilter: 'blur(16px)',
        WebkitBackdropFilter: 'blur(16px)'
      }}
    >
      {/* 主容器 */}
      <div 
        className="bg-white/90 border border-white/30 rounded-xl shadow-2xl overflow-hidden"
        style={{
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.1)'
        }}
      >
        {/* 標題欄 */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-b border-white/20">
          <div className="flex items-center gap-3">
            <div className={`w-3 h-3 rounded-full ${getStatusColor()} animate-pulse`}></div>
            <h3 className="font-semibold text-gray-800">批量傳送進度</h3>
            <span className="text-sm text-gray-600">({progressData.completedDevices}/{progressData.totalDevices})</span>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1 hover:bg-white/20 rounded transition-colors"
            >
              {isCollapsed ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>
            <button
              onClick={onClose}
              className="p-1 hover:bg-white/20 rounded transition-colors"
            >
              <X size={16} />
            </button>
          </div>
        </div>

        {/* 進度內容 */}
        {!isCollapsed && (
          <div className="p-4 space-y-4">
            {/* 主進度條 */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-700">{getStatusText()}</span>
                <span className="font-medium text-gray-800">{progressPercentage}%</span>
              </div>
              <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-500 ease-out"
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
            </div>

            {/* 當前處理設備 */}
            {progressData.currentDevice && progressData.status === 'running' && (
              <div className="bg-blue-50/50 rounded-lg p-3 border border-blue-200/30">
                <div className="text-sm text-gray-600">正在處理</div>
                <div className="font-medium text-gray-800">{progressData.currentDevice}</div>
                {progressData.currentDeviceMac && (
                  <div className="text-xs text-gray-500 font-mono">{progressData.currentDeviceMac}</div>
                )}
              </div>
            )}

            {/* 錯誤信息 */}
            {progressData.error && progressData.status === 'error' && (
              <div className="bg-red-50/50 rounded-lg p-3 border border-red-200/30">
                <div className="text-sm text-red-600 font-medium">錯誤信息</div>
                <div className="text-sm text-red-700">{progressData.error}</div>
              </div>
            )}

            {/* 統計信息 */}
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className="bg-green-50/50 rounded-lg p-2 border border-green-200/30">
                <div className="text-green-600 font-medium">成功</div>
                <div className="text-lg font-bold text-green-700">
                  {Math.max(0, progressData.completedDevices - progressData.failedDevices)}
                </div>
              </div>
              <div className="bg-red-50/50 rounded-lg p-2 border border-red-200/30">
                <div className="text-red-600 font-medium">失敗</div>
                <div className="text-lg font-bold text-red-700">{progressData.failedDevices}</div>
              </div>
            </div>

            {/* 詳細統計 */}
            {progressData.smartSelectionStats && (
              <div className="space-y-2 text-xs text-gray-600">
                <div className="flex justify-between">
                  <span>智能模式設備:</span>
                  <span>{progressData.smartSelectionStats.totalAutoModeDevices}</span>
                </div>
                <div className="flex justify-between">
                  <span>使用備用網關:</span>
                  <span>{progressData.smartSelectionStats.usedBackupGateway}</span>
                </div>
                <div className="flex justify-between">
                  <span>隊列循環:</span>
                  <span>{progressData.queueCycles || 0}</span>
                </div>
                {progressData.waitCycles && progressData.waitCycles > 0 && (
                  <div className="flex justify-between">
                    <span>等待循環:</span>
                    <span>{progressData.waitCycles}</span>
                  </div>
                )}
              </div>
            )}

            {/* 時間信息 */}
            <div className="flex justify-between text-xs text-gray-500 pt-2 border-t border-gray-200/50">
              <span>已用時間: {formatTime(getElapsedTime())}</span>
              {progressData.estimatedTimeRemaining && (
                <span>預計剩餘: {formatTime(progressData.estimatedTimeRemaining)}</span>
              )}
            </div>

            {/* 操作按鈕 */}
            {progressData.status === 'running' && onCancel && (
              <div className="flex gap-2 pt-2">
                <button
                  onClick={onCancel}
                  className="flex-1 px-3 py-2 bg-red-500/10 text-red-600 rounded-lg hover:bg-red-500/20 transition-colors text-sm font-medium"
                >
                  取消傳送
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
