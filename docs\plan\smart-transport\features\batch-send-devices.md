# 設備批量發送功能

## 功能概述

設備管理頁面新增了批量發送功能，允許用戶一次性向多個設備發送預覽圖。該功能採用任務隊列機制，具有以下特點：

1. **智能篩選** - 只對狀態為"未更新"的設備進行發送
2. **任務隊列機制** - 採用真正的任務隊列，支持任務重新排隊
3. **智能網關選擇** - 每個任務動態選擇最佳網關，避免阻塞等待
4. **並發控制** - 可配置的並發數量，平衡效率和穩定性
5. **預防性標記** - 防止併發衝突，確保傳輸穩定
6. **詳細統計** - 提供完整的處理統計和性能數據

## 使用方法

### 1. 選擇設備
- 在設備管理頁面勾選需要發送的設備
- 可以使用全選功能快速選擇當前頁面的所有設備

### 2. 執行批量發送
- 點擊工具列中的"批量發送"按鈕（綠色按鈕，Send圖標）
- 系統會自動篩選出狀態為"未更新"的設備

### 3. 智能分配確認
- 如果選中的設備中有啟用自動網關選擇模式的設備，系統會顯示確認對話框
- 確認對話框會顯示：
  - 總共要發送的設備數量
  - 其中啟用智能分配的設備數量
  - 是否繼續執行的選項

### 4. 任務隊列處理
- 系統採用任務隊列機制處理批量發送
- 每個設備作為一個任務，支持並發處理（默認3個任務同時執行）
- 當網關忙碌時，任務會重新排隊而不是等待
- 智能選擇最佳網關，充分利用備用網關資源

### 5. 發送結果
- 系統會顯示發送進度通知（2秒）
- 完成後會顯示詳細的統計信息：
  - 基本統計：`成功發送 X/Y 個設備`（8秒）
  - 智能選擇統計：智能模式設備數量、使用備用網關次數
  - 任務隊列統計：隊列循環次數、重試次數
- 錯誤訊息會顯示6秒，確保用戶有足夠時間閱讀
- 設備列表會自動刷新以更新狀態

## 技術實現

### 前端邏輯
- 篩選選中設備中狀態為"未更新"的設備
- 檢測設備的`gatewaySelectionMode`屬性
- 調用`sendMultipleDevicePreviewsToGateways` API，配置參數：
  ```javascript
  {
    sendToAllGateways: hasAutoMode,
    storeId: store?.id,
    concurrency: 3,              // 並發數量
    enableSmartSelection: true   // 啟用智能選擇
  }
  ```
- 正確解析API返回結構，包括詳細統計信息

### 後端任務隊列處理
- 使用任務隊列API：`/api/devices/send-multiple-previews`
- **任務隊列機制**：
  - 創建任務隊列，每個設備一個任務
  - 隊列循環處理，支持並發控制
  - 任務重新排隊機制，避免阻塞等待
- **智能網關選擇**：
  - 每個任務獨立進行網關選擇
  - 檢查主要網關狀態，忙碌時選擇備用網關
  - 預防性標記機制，防止併發衝突
- **錯誤處理**：確保單個設備錯誤不會影響其他設備的處理

### 智能網關選擇邏輯
- **智能模式設備** (`gatewaySelectionMode: 'auto'`)：
  - 優先使用主要網關
  - 主要網關忙碌時自動選擇備用網關
  - 沒有可用網關時任務重新排隊
- **手動模式設備** (`gatewaySelectionMode: 'manual'`)：
  - 只使用主要網關
  - 主要網關忙碌時任務重新排隊
- **預防性標記**：使用 `startChunkTransmission` 預先標記網關為忙碌，防止併發衝突

## 錯誤處理

### 常見錯誤情況
1. **沒有選擇設備** - 顯示提示信息要求先選擇設備
2. **沒有需要更新的設備** - 顯示提示信息說明選中設備都不需要更新
3. **部分發送失敗** - 顯示成功/失敗統計，不會因為部分失敗而中斷整個流程

### 網關相關錯誤
- **主要網關離線**：智能模式設備會嘗試備用網關，手動模式設備任務重新排隊
- **備用網關離線**：自動選擇其他可用的備用網關
- **所有網關忙碌**：任務重新排隊，等待下次處理
- **網關不支持分片傳輸**：自動回退到傳統傳輸方式

### 任務隊列錯誤處理
- **可重試錯誤**：網關忙碌、連接超時等，任務重新排隊（最多重試3次）
- **不可重試錯誤**：設備不存在、權限問題等，直接標記為失敗
- **狀態清理**：無論成功失敗都會清理預防性標記，確保狀態一致性

## 性能考慮

### 任務隊列優化
- **並發控制**：可配置的並發數量（默認3個），根據網關數量動態調整
- **任務重新排隊**：避免阻塞等待，充分利用網關資源
- **預防性標記**：防止併發衝突，確保傳輸穩定性

### 負載均衡
- **智能網關選擇**：自動分散負載到不同網關
- **備用網關利用**：充分利用備用網關資源
- **動態狀態檢查**：每個任務都重新評估網關狀態

### 性能監控
- **詳細統計**：隊列循環次數、重試次數、處理時間等
- **實時反饋**：即時了解處理進度和狀態
- **問題診斷**：豐富的日誌信息便於排查問題

## 用戶體驗

### 操作體驗
- **發送過程**：按鈕會顯示loading狀態，防止重複點擊
- **進度反饋**：實時顯示發送進度通知
- **結果展示**：完成後顯示詳細的統計信息，包括智能選擇和任務隊列統計
- **狀態管理**：完成後自動清空選擇狀態，刷新設備列表
- **取消支持**：支持在確認對話框中取消操作

### 統計信息展示
- **基本統計**：成功/失敗設備數量
- **智能選擇統計**：智能模式設備數量、使用備用網關次數
- **性能統計**：隊列循環次數、重試次數、處理時間
- **錯誤詳情**：失敗設備的具體錯誤原因

## 配置建議

### 並發數量配置
- **小批量（<10設備）**：concurrency = 2-3
- **中批量（10-50設備）**：concurrency = 3-5
- **大批量（>50設備）**：concurrency = 5-8
- **動態調整**：系統會根據網關數量自動調整最大並發數

### 網關配置建議
- **備用網關**：每個設備建議配置2-3個備用網關
- **負載分布**：將設備均勻分配到不同的主要網關
- **監控指標**：定期檢查網關負載分布和使用情況

## 總結

新的批量發送功能採用任務隊列機制，實現了：

1. **真正的非阻塞處理**：任務重新排隊而不是等待
2. **智能網關選擇**：動態選擇最佳網關，充分利用資源
3. **併發安全**：預防性標記機制防止衝突
4. **詳細監控**：全面的統計信息和性能數據
5. **高效處理**：並發控制和負載均衡提高效率
6. **可靠性保障**：完善的錯誤處理和重試機制

這確保了批量發送功能在各種場景下都能穩定、高效地工作，大幅提升了用戶體驗和系統性能。
