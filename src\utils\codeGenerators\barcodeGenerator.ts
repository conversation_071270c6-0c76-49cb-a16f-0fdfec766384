/**
 * 前端 Barcode 生成器
 * 使用 jsbarcode 套件直接在前端生成條碼
 */

import JsBarcode from 'jsbarcode';

export interface BarcodeOptions {
  content: string;
  barcodeType?: 'code128' | 'ean13' | 'upc-a' | 'code39' | 'code93';
  quietZone?: number;
  foregroundColor?: string;
  backgroundColor?: string;
  width?: number;
  height?: number;
  showText?: boolean;
}

/**
 * 驗證條碼內容
 */
export function validateBarcodeContent(content: string, barcodeType: string = 'code128') {
  if (!content || content.length === 0) {
    return { isValid: false, error: '內容不能為空' };
  }

  // 根據條碼類型進行驗證
  switch (barcodeType) {
    case 'ean13':
      if (!/^\d{13}$/.test(content)) {
        return { isValid: false, error: 'EAN-13 必須是 13 位數字' };
      }
      break;
    case 'upc-a':
      if (!/^\d{12}$/.test(content)) {
        return { isValid: false, error: 'UPC-A 必須是 12 位數字' };
      }
      break;
    case 'code39':
      if (!/^[A-Z0-9\-. $/+%]*$/.test(content)) {
        return { isValid: false, error: 'Code 39 只支援大寫字母、數字和特殊字符 (-. $/+%)' };
      }
      if (content.length > 43) {
        return { isValid: false, error: 'Code 39 內容長度不能超過 43 個字符' };
      }
      break;
    case 'code93':
      if (content.length > 47) {
        return { isValid: false, error: 'Code 93 內容長度不能超過 47 個字符' };
      }
      break;
    case 'code128':
    default:
      if (content.length > 80) {
        return { isValid: false, error: 'Code 128 內容長度不能超過 80 個字符' };
      }
      break;
  }

  return { isValid: true, sanitizedContent: content };
}

/**
 * 生成條碼圖片
 * @param options 條碼選項
 * @returns Promise<string> base64 圖片數據
 */
export async function generateBarcode(options: BarcodeOptions): Promise<string> {
  const {
    content,
    barcodeType = 'code128',
    quietZone = 10,
    backgroundColor = '#FFFFFF',
    width = 200,
    height = 100,
    showText = false // 預設不顯示文字
  } = options;

  // 條碼顏色不支援透明，如果是透明則使用黑色
  const foregroundColor = (options.foregroundColor === 'transparent' || !options.foregroundColor) ? '#000000' : options.foregroundColor;

  // 驗證內容
  const validation = validateBarcodeContent(content, barcodeType);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  try {
    // 創建 Canvas 元素
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;

    // 根據是否顯示文字調整條碼高度
    const barcodeHeight = showText ? height - 20 : height - 10; // 如果不顯示文字，減少底部空間

    // 設定條碼選項
    const barcodeOptions = {
      format: barcodeType.toUpperCase().replace('-', ''),
      width: 2,
      height: barcodeHeight,
      displayValue: showText, // 根據設置決定是否顯示文字
      fontSize: 12,
      textAlign: 'center' as const,
      textPosition: 'bottom' as const,
      textMargin: 2,
      fontOptions: '',
      font: 'monospace',
      background: backgroundColor,
      lineColor: foregroundColor,
      margin: quietZone,
      marginTop: 10,
      marginBottom: showText ? 10 : 5, // 如果不顯示文字，減少底部邊距
      marginLeft: quietZone,
      marginRight: quietZone
    };

    // 生成條碼
    JsBarcode(canvas, validation.sanitizedContent || content, barcodeOptions);

    // 轉換為 data URL
    const dataUrl = canvas.toDataURL('image/png');
    return dataUrl;

  } catch (error) {
    console.error('條碼生成失敗:', error);
    throw new Error(`條碼生成失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
}

/**
 * 為編輯器生成條碼預覽圖片
 * 這個函數專門為編輯器優化，處理特殊情況
 */
export async function generateBarcodeForEditor(element: any): Promise<string> {
  // 獲取內容
  let content = element.codeContent;
  if (!content) {
    if (element.dataBinding?.fieldId) {
      content = getSampleValueForBarcodeType(element.barcodeType || 'code128');
    } else {
      content = getSampleValueForBarcodeType(element.barcodeType || 'code128');
    }
  }

  // 確保尺寸合理
  const width = Math.max(Math.round(element.width || 200), 100);
  const height = Math.max(Math.round(element.height || 80), 40);

  return generateBarcode({
    content,
    barcodeType: element.barcodeType || 'code128',
    quietZone: element.quietZone || 10,
    // 條碼顏色不支援透明，如果是透明則使用黑色
    foregroundColor: (element.lineColor === 'transparent' || !element.lineColor) ? '#000000' : element.lineColor,
    backgroundColor: element.fillColor || '#FFFFFF',
    width,
    height,
    showText: element.showText === true // 傳遞文字顯示設置，預設為 false
  });
}

/**
 * 獲取條碼類型的顯示名稱
 */
export function getBarcodeTypeDisplay(barcodeType?: string): string {
  const typeMap = {
    code128: 'Code 128',
    ean13: 'EAN-13',
    'upc-a': 'UPC-A',
    code39: 'Code 39',
    code93: 'Code 93'
  };
  
  return typeMap[barcodeType as keyof typeof typeMap] || 'Code 128';
}

/**
 * 獲取條碼類型的範例值
 */
export function getSampleValueForBarcodeType(barcodeType: string): string {
  const sampleValues = {
    code128: 'Sample123',
    ean13: '1234567890123',
    'upc-a': '123456789012',
    code39: 'SAMPLE123',
    code93: 'SAMPLE123'
  };
  
  return sampleValues[barcodeType as keyof typeof sampleValues] || 'Sample123';
}

/**
 * 獲取支援的條碼類型列表
 */
export function getSupportedBarcodeTypes() {
  return [
    { value: 'code128', label: 'Code 128', maxLength: 80 },
    { value: 'ean13', label: 'EAN-13', length: 13 },
    { value: 'upc-a', label: 'UPC-A', length: 12 },
    { value: 'code39', label: 'Code 39', maxLength: 43 },
    { value: 'code93', label: 'Code 93', maxLength: 47 }
  ];
}
