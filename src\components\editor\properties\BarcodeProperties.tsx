import React, { useEffect, useState } from 'react';
import { TemplateElement, DataField, DisplayColorType, DataFieldType } from '../../../types';
import { Store } from '../../../types/store';
import { FormField, TextInput, NumberInput, SelectInput, RestrictedColorInput, CheckboxInput } from './FormComponents';
import { NonTransparentColorInput } from './NonTransparentColorInput';
import { getAllDataFields } from '../../../utils/api/dataFieldApi';
import { getAllStores } from '../../../utils/api/storeApi';
import { bindingCore } from '../../../utils/dataBinding/bindingCore';
import { BarcodeBinding } from '../../../utils/dataBinding/barcodeBinding';

interface BarcodePropertiesProps {
  element: TemplateElement;
  updateElement: (updates: Partial<TemplateElement>) => void;
  colorType?: string | DisplayColorType;
}

export const BarcodeProperties: React.FC<BarcodePropertiesProps> = ({
  element,
  updateElement,
  colorType
}) => {
  // 資料欄位狀態
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [isLoadingDataFields, setIsLoadingDataFields] = useState(false);

  // 門店數據狀態
  const [storeData, setStoreData] = useState<Store[]>([]);
  const [selectedStoreId, setSelectedStoreId] = useState<string | null>(
    element.dataBinding?.selectedStoreId || null
  );

  // 預覽值狀態
  const [previewValue, setPreviewValue] = useState<string | null>(null);

  // 兼容舊版 dataFieldId 和新版 dataBinding.fieldId
  const [selectedDataFieldId, setSelectedDataFieldId] = useState<string | null>(
    (element.dataBinding?.fieldId || element.dataFieldId || null)
  );

  // 資料綁定相關狀態
  const [dataIndex, setDataIndex] = useState<number>(
    element.dataBinding?.dataIndex || 0
  );

  // 當前最大資料綁定數量
  const [maxBindingDataCount, setMaxBindingDataCount] = useState<number>(bindingCore.getMaxBindingDataCount());

  // 條碼類型選項
  const barcodeTypeOptions = [
    { value: 'code128', label: 'Code 128' },
    { value: 'ean13', label: 'EAN-13' },
    { value: 'upc-a', label: 'UPC-A' },
    { value: 'code39', label: 'Code 39' },
    { value: 'code93', label: 'Code 93' }
  ];

  // 生成資料索引選項
  const dataIndexOptions = Array.from({ length: maxBindingDataCount }, (_, i) => ({
    value: String(i),
    label: `資料 ${i + 1}`
  }));

  // 監聽最大綁定數量的變更
  useEffect(() => {
    const handleMaxBindingDataCountChange = (count: number) => {
      setMaxBindingDataCount(count);
    };

    bindingCore.addMaxBindingDataCountListener(handleMaxBindingDataCountChange);

    return () => {
      bindingCore.removeMaxBindingDataCountListener(handleMaxBindingDataCountChange);
    };
  }, []);

  // 載入資料欄位
  useEffect(() => {
    const fetchDataFields = async () => {
      setIsLoadingDataFields(true);
      try {
        const fields = await getAllDataFields();
        const bindableFields = bindingCore.getBindableFields(fields);
        setDataFields(bindableFields);
      } catch (error) {
        console.error('獲取資料欄位失敗:', error);
      } finally {
        setIsLoadingDataFields(false);
      }
    };

    fetchDataFields();
  }, []);

  // 在組件加載時獲取門店數據
  useEffect(() => {
    const fetchStoreData = async () => {
      if (!selectedDataFieldId) return;

      console.log('Barcode 開始獲取門店數據，當前選擇的門店ID:', selectedStoreId);

      try {
        const stores = await getAllStores();
        console.log('Barcode 獲取到門店數據，數量:', stores.length);
        // 輸出門店數據的結構，以便調試
        if (stores.length > 0) {
          console.log('Barcode 第一個門店的結構:', JSON.stringify(stores[0]));
        }
        setStoreData(stores);

        // 如果已經有選擇的門店ID
        if (selectedStoreId) {
          console.log('Barcode 已有選擇的門店ID:', selectedStoreId, '，立即更新預覽值');
          // 立即更新預覽值
          updatePreviewValueWithCurrentStore();
        }
        // 如果沒有選擇的門店ID，但有門店數據
        else if (stores.length > 0) {
          console.log('Barcode 沒有選擇的門店ID，預設選擇第一個門店:', stores[0].id);
          // 預設選擇第一個門店
          if (stores[0].id) {
            setSelectedStoreId(stores[0].id);

            // 在下一個渲染周期更新預覽值
            setTimeout(() => {
              console.log('Barcode 設置完門店ID後更新預覽值');
              updatePreviewValueWithCurrentStore();
            }, 0);
          }
        }
      } catch (error) {
        console.error('Barcode 獲取門店數據失敗:', error);
      }
    };

    fetchStoreData();
  }, [selectedDataFieldId, selectedStoreId]);

  // 添加一個新的 useEffect 來處理自動設置門店 ID 的邏輯
  useEffect(() => {
    // 當有綁定時，自動設置門店ID
    if (selectedDataFieldId && !selectedStoreId) {
      // 優先使用元素中已存在的門店ID
      if (element.dataBinding?.selectedStoreId) {
        console.log('Barcode 從元素中獲取門店ID:', element.dataBinding.selectedStoreId);
        setSelectedStoreId(element.dataBinding.selectedStoreId);
      }
      // 其次使用模板中的門店ID
      else if (element.templateStoreId) {
        console.log('Barcode 從模板中獲取門店ID:', element.templateStoreId);
        setSelectedStoreId(element.templateStoreId);
      }
      // 最後才使用第一個門店的ID
      else if (storeData.length > 0 && storeData[0].id) {
        console.log('Barcode 自動設置門店ID為第一個門店:', storeData[0].id);
        setSelectedStoreId(storeData[0].id);
      }
    }
  }, [selectedDataFieldId, storeData, selectedStoreId, element.dataBinding?.selectedStoreId, element.templateStoreId]);

  // 在組件首次加載時更新預覽值
  useEffect(() => {
    console.log('Barcode 元素首次加載，元素ID:', element.id, '綁定狀態:', !!element.dataBinding);

    if (element.dataBinding?.fieldId || element.dataFieldId) {
      console.log('Barcode 元素有綁定信息，立即更新預覽值');

      // 設置綁定相關的狀態
      if (element.dataBinding?.fieldId) {
        setSelectedDataFieldId(element.dataBinding.fieldId);
        setDataIndex(element.dataBinding.dataIndex || 0);

        if (element.dataBinding.selectedStoreId) {
          setSelectedStoreId(element.dataBinding.selectedStoreId);
        }
      } else if (element.dataFieldId) {
        setSelectedDataFieldId(element.dataFieldId);
        setDataIndex(0);
      }

      // 直接獲取所有資料欄位和門店數據
      Promise.all([
        getAllDataFields(),
        getAllStores()
      ]).then(([allFields, stores]) => {
        console.log('Barcode 首次加載時獲取到資料欄位和門店數據，欄位數量:', allFields.length, '門店數量:', stores.length);

        const bindableFields = bindingCore.getBindableFields(allFields);
        setDataFields(bindableFields);
        setStoreData(stores);

        // 確保有門店ID
        if (!selectedStoreId && stores.length > 0) {
          if (element.dataBinding?.selectedStoreId) {
            setSelectedStoreId(element.dataBinding.selectedStoreId);
          } else if (stores[0].id) {
            console.log('Barcode 自動設置門店ID為第一個門店:', stores[0].id);
            setSelectedStoreId(stores[0].id);
          }
        }

        setTimeout(() => {
          console.log('Barcode 首次加載時更新預覽值');
          updatePreviewValueWithCurrentStore();
        }, 300);
      }).catch(error => {
        console.error('Barcode 首次加載時獲取數據失敗:', error);
        setPreviewValue('Barcode Data');
      });
    }
  }, []);

  // 根據當前選擇的門店和商品ID更新預覽值
  const updatePreviewValueWithCurrentStore = () => {
    // 使用類型斷言獲取 storeItemUid（兼容舊版本的 storeItemSn）
    const elementStoreItemUid = (element as any).storeItemUid || (element as any).storeItemSn;
    console.log('Barcode 開始更新預覽值，元素ID:', element.id, '欄位ID:', selectedDataFieldId, '門店ID:', selectedStoreId, '商品UID:', elementStoreItemUid);

    if (!selectedDataFieldId) {
      console.log('Barcode 沒有選擇欄位ID，不更新預覽值');
      return;
    }

    // 檢查是否有欄位信息，但即使找不到也繼續執行
    const field = dataFields.find(f => f.id === selectedDataFieldId);
    if (!field) {
      console.log('Barcode 找不到欄位信息，欄位ID:', selectedDataFieldId, '但仍嘗試從門店數據中獲取值');
      // 不要返回，繼續執行
    }

    // 如果沒有選擇門店ID，顯示預設的Barcode Data
    if (!selectedStoreId) {
      console.log('Barcode 沒有選擇門店ID，顯示預設的Barcode Data');
      setPreviewValue(BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType));
      return;
    }

    // 使用已加載的數據進行處理
    const processWithLoadedData = (providedField?: DataField, providedStore?: Store) => {
      console.log('Barcode 使用已加載的數據進行處理');

      // 找到當前門店 - 優先使用提供的門店
      const currentStore = providedStore || storeData.find(store => store.id === selectedStoreId);
      if (!currentStore) {
        console.log('Barcode 找不到選擇的門店，顯示找不到門店提示');
        setPreviewValue(BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType));
        return;
      }

      // 找到當前欄位 - 優先使用提供的欄位
      const currentField = providedField || dataFields.find(f => f.id === selectedDataFieldId);

      // 即使找不到欄位信息，也嘗試從門店數據中獲取值
      // 這是因為門店數據中可能有欄位，但欄位定義可能不在dataFields中
      if (!currentField) {
        console.log('Barcode 找不到欄位信息，欄位ID:', selectedDataFieldId, '但仍嘗試從門店數據中獲取值');
      }

      // 如果有商品UID，則查找特定商品
      if (elementStoreItemUid && currentStore.storeSpecificData && Array.isArray(currentStore.storeSpecificData)) {
        console.log('Barcode 嘗試查找特定商品:', elementStoreItemUid);

        const item = currentStore.storeSpecificData.find(i => i.uid === elementStoreItemUid);

        if (item) {
          console.log('Barcode 找到商品:', item);

          if (selectedDataFieldId && item[selectedDataFieldId] !== undefined && item[selectedDataFieldId] !== null) {
            const displayValue = String(item[selectedDataFieldId]);
            console.log('Barcode 從特定商品找到數據:', displayValue);
            setPreviewValue(displayValue);
          } else {
            console.log('Barcode 商品沒有選擇的欄位數據');
            setPreviewValue(BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType));
          }
        } else {
          console.log('Barcode 找不到商品:', elementStoreItemUid);
          setPreviewValue(BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType));
        }

        return;
      }

      // 如果沒有商品ID，則顯示門店的第一個商品數據
      console.log('Barcode 沒有特定商品ID，嘗試顯示門店的第一個商品數據');

      // 檢查門店是否有數據
      let hasData = false;
      let displayValue = "";

      // 檢查門店是否有 storeSpecificData 陣列
      if (currentStore.storeSpecificData && Array.isArray(currentStore.storeSpecificData) && currentStore.storeSpecificData.length > 0) {
        // 使用第一筆門店專屬數據
        const firstStoreData = currentStore.storeSpecificData[0];

        // 檢查該筆數據是否有選擇的欄位
        if (firstStoreData && selectedDataFieldId && firstStoreData[selectedDataFieldId] !== undefined && firstStoreData[selectedDataFieldId] !== null) {
          displayValue = String(firstStoreData[selectedDataFieldId]);
          hasData = true;
          console.log('Barcode 從 storeSpecificData 找到數據:', displayValue);

          // 更新元素的商品ID
          const updatedElement = BarcodeBinding.setBinding(
            element,
            dataIndex,
            selectedDataFieldId,
            selectedStoreId
          );

          // 同時更新元素的 templateStoreId 屬性和商品ID
          // 使用一個臨時變量來避免 TypeScript 錯誤
          const elementUpdates: any = {
            ...updatedElement,
            templateStoreId: selectedStoreId || undefined,
            storeItemUid: firstStoreData.uid,
            storeItemSn: firstStoreData.uid, // 兼容舊版本
            codeContent: displayValue // 同步綁定的數據到 codeContent
          };
          updateElement(elementUpdates);
        }
      }

      // 如果沒有在 storeSpecificData 中找到數據，嘗試從門店根級獲取（向後兼容）
      if (!hasData && selectedDataFieldId && (currentStore as any)[selectedDataFieldId] !== undefined && (currentStore as any)[selectedDataFieldId] !== null) {
        displayValue = String((currentStore as any)[selectedDataFieldId]);
        hasData = true;
        console.log('Barcode 從門店根級找到數據:', displayValue);
      }

      if (hasData) {
        console.log('Barcode 欄位有數據，最終更新預覽值為:', displayValue);
        setPreviewValue(displayValue);

        // 更新元素的 codeContent 以同步到渲染器
        const updatedElement = BarcodeBinding.setBinding(
          element,
          dataIndex,
          selectedDataFieldId,
          selectedStoreId
        );

        // 同時更新 codeContent 屬性，確保渲染器能獲取到最新的內容
        const elementUpdates = {
          ...updatedElement,
          codeContent: displayValue // 將綁定的數據同步到 codeContent
        };

        // 確保元素的綁定信息中包含門店ID
        if (element.dataBinding && element.dataBinding.selectedStoreId !== selectedStoreId) {
          console.log('Barcode 更新元素的門店ID綁定信息，從', element.dataBinding.selectedStoreId, '到', selectedStoreId);
          elementUpdates.templateStoreId = selectedStoreId || undefined;
        }

        updateElement(elementUpdates);
      } else {
        console.log('Barcode 門店沒有選擇的欄位數據，顯示預設值');
        const defaultValue = BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType);
        setPreviewValue(defaultValue);

        // 同時更新元素的 codeContent
        updateElement({
          codeContent: defaultValue
        });
      }
    };

    // 檢查數據是否已加載
    if (dataFields.length > 0 && storeData.length > 0) {
      processWithLoadedData();
    } else {
      // 如果數據還沒有加載，則加載數據
      Promise.all([
        dataFields.length > 0 ? Promise.resolve(dataFields) : getAllDataFields().then(fields => bindingCore.getBindableFields(fields)),
        storeData.length > 0 ? Promise.resolve(storeData) : getAllStores()
      ]).then(([allFields, stores]) => {
        console.log('Barcode 加載到資料欄位和門店數據，欄位數量:', allFields.length, '門店數量:', stores.length);

        if (dataFields.length === 0) {
          setDataFields(allFields);
        }

        if (storeData.length === 0) {
          setStoreData(stores);
        }

        if (!selectedStoreId && stores.length > 0) {
          const firstStoreId = stores[0].id;
          if (firstStoreId) {
            console.log('Barcode 自動設置門店ID為第一個門店:', firstStoreId);
            setSelectedStoreId(firstStoreId);
          }
        }

        const currentField = allFields.find(f => f.id === selectedDataFieldId);
        const currentStore = stores.find(store => store.id === selectedStoreId);

        if (!currentStore) {
          console.log('Barcode 找不到選擇的門店');
          setPreviewValue(BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType));
          return;
        }

        setTimeout(() => {
          processWithLoadedData(currentField, currentStore);
        }, 0);
      }).catch(error => {
        console.error('Barcode 加載數據失敗:', error);
        setPreviewValue(BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType));
      });
    }
  };

  // 處理資料欄位綁定變更
  const handleDataFieldChange = (fieldId: string | null) => {
    const wasBound = !!selectedDataFieldId;

    setSelectedDataFieldId(fieldId);

    if (fieldId) {
      // 如果從未綁定狀態切換到綁定狀態，需要設置合適的門店ID但清除商品選擇
      if (!wasBound) {
        // 優先使用元素中已存在的門店ID
        if (element.dataBinding?.selectedStoreId) {
          console.log('Barcode 從元素綁定信息中獲取門店ID:', element.dataBinding.selectedStoreId);
          setSelectedStoreId(element.dataBinding.selectedStoreId);
        }
        // 其次使用模板中的門店ID
        else if (element.templateStoreId) {
          console.log('Barcode 從模板中獲取門店ID:', element.templateStoreId);
          setSelectedStoreId(element.templateStoreId);
        }
        // 最後使用第一個可用門店的ID
        else if (storeData.length > 0 && storeData[0].id) {
          console.log('Barcode 自動設置門店ID為第一個門店:', storeData[0].id);
          setSelectedStoreId(storeData[0].id);
        }
        // 如果沒有門店數據，嘗試載入
        else {
          console.log('Barcode 沒有門店數據，嘗試載入');
          getAllStores().then(stores => {
            if (stores.length > 0 && stores[0].id) {
              console.log('Barcode 載入門店數據後設置第一個門店:', stores[0].id);
              setStoreData(stores);
              setSelectedStoreId(stores[0].id);
            }
          }).catch(error => {
            console.error('Barcode 載入門店數據失敗:', error);
          });
        }
      }

      const updatedElement = BarcodeBinding.setBinding(
        element,
        dataIndex,
        fieldId,
        selectedStoreId
      );

      // 如果從未綁定狀態切換到綁定狀態，清除商品選擇，讓預覽數據選單顯示"請選擇預覽數據"
      const elementUpdates: any = {
        ...updatedElement,
        dataFieldId: fieldId
      };

      if (!wasBound) {
        console.log('Barcode 從未綁定切換到綁定狀態，清除商品選擇');
        elementUpdates.storeItemUid = null;
        elementUpdates.storeItemSn = null;
        elementUpdates.codeContent = BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType); // 設置為預設值
        setPreviewValue(BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType));
      }

      updateElement(elementUpdates);

      // 只有在已經綁定的狀態下才更新預覽值
      if (wasBound) {
        updatePreviewValueWithCurrentStore();
      }
    } else {
      console.log("Barcode 執行解除綁定操作");
      const updatedElement = BarcodeBinding.removeBinding(element);

      const cleanElement = {
        ...updatedElement,
        dataBinding: undefined,
        dataFieldId: null,
        codeContent: BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType),
        templateStoreId: element.templateStoreId
      };

      updateElement(cleanElement);
      setPreviewValue(null);

      setTimeout(() => {
        setSelectedDataFieldId(null);
      }, 0);
    }
  };

  // 處理資料索引變更
  const handleDataIndexChange = (indexValue: string) => {
    const newIndex = parseInt(indexValue, 10);
    setDataIndex(newIndex);

    if (selectedDataFieldId) {
      const updatedElement = BarcodeBinding.setBinding(
        element,
        newIndex,
        selectedDataFieldId,
        selectedStoreId
      );
      updateElement(updatedElement);

      updatePreviewValueWithCurrentStore();
    }
  };

  // 處理條碼類型變更
  const handleBarcodeTypeChange = (barcodeType: string) => {
    const updates: any = {
      barcodeType: barcodeType as 'code128' | 'ean13' | 'upc-a' | 'code39' | 'code93'
    };

    // 檢查當前綁定的資料欄位是否與新的條碼類型相容
    if (selectedDataFieldId) {
      const newBindableFields = BarcodeBinding.getBindableFields(dataFields, barcodeType);
      const isCurrentFieldCompatible = newBindableFields.some(field => field.id === selectedDataFieldId);

      if (!isCurrentFieldCompatible) {
        // 如果當前綁定的欄位不相容，清除綁定
        updates.dataBinding = undefined;
        updates.dataFieldId = null;
        setSelectedDataFieldId(null);
        setPreviewValue(null);
      }
    }

    updateElement(updates);
  };

  // 處理內容變更
  const handleContentChange = (content: string) => {
    if (!selectedDataFieldId) {
      const updates = { codeContent: content };
      updateElement(updates);
    }
  };

  // 生成預覽選項 - 顯示選定門店的商品數據
  const generatePreviewOptions = () => {
    if (!selectedDataFieldId) return [];

    const options = [];

    // 如果沒有門店數據，嘗試重新載入
    if (storeData.length === 0) {
      console.log('Barcode 沒有門店數據，嘗試重新載入');
      // 觸發重新載入門店數據
      getAllStores().then(stores => {
        setStoreData(stores);
        if (stores.length > 0 && !selectedStoreId) {
          setSelectedStoreId(stores[0].id);
        }
      }).catch(error => {
        console.error('Barcode 重新載入門店數據失敗:', error);
      });

      options.push({
        value: '',
        label: '正在載入門店數據...'
      });
      return options;
    }

    const selectedStore = storeData.find(store => store.id === selectedStoreId);

    if (!selectedStore) {
      console.log('Barcode 找不到選定的門店:', selectedStoreId, '可用門店:', storeData.map(s => s.id));

      // 如果找不到選定的門店，但有其他門店，自動選擇第一個
      if (storeData.length > 0) {
        console.log('Barcode 自動選擇第一個門店:', storeData[0].id);
        setSelectedStoreId(storeData[0].id);

        options.push({
          value: '',
          label: '正在切換門店...'
        });
        return options;
      }

      options.push({
        value: '',
        label: `找不到門店 ${selectedStoreId || '(未選擇)'}`
      });
      return options;
    }

    if (!selectedStore.storeSpecificData || !Array.isArray(selectedStore.storeSpecificData) || selectedStore.storeSpecificData.length === 0) {
      console.log('Barcode 門店沒有商品數據:', selectedStoreId);
      options.push({
        value: '',
        label: `門店 ${selectedStore.name || selectedStore.id} 沒有商品數據`
      });
      return options;
    }

    selectedStore.storeSpecificData.forEach((item, index) => {
      if (item && item.uid) {
        let displayValue = "(無數據)";
        let hasData = false;

        if (selectedDataFieldId && item[selectedDataFieldId] !== undefined && item[selectedDataFieldId] !== null) {
          displayValue = String(item[selectedDataFieldId]);
          hasData = true;
        }

        const itemLabel = item.name || item.id || `商品 ${index + 1}`;

        console.log(`Barcode 商品 ${index + 1} (UID: ${item.uid}) 數據預覽:`, displayValue, '有數據:', hasData);

        options.push({
          value: `item:${item.uid}`,
          label: `${itemLabel} : ${displayValue}`
        });
      }
    });

    if (options.length === 0) {
      options.push({
        value: '',
        label: `門店 ${selectedStore.name || selectedStore.id} 沒有可用的商品數據`
      });
    }

    return options;
  };

  // 處理預覽選擇 - 處理商品數據
  const handlePreviewSelect = (value: string) => {
    const field = selectedDataFieldId ? dataFields.find(f => f.id === selectedDataFieldId) : null;

    if (!field) {
      return;
    }

    if (!value) {
      setPreviewValue(BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType));

      if (selectedDataFieldId) {
        const updatedElement = BarcodeBinding.setBinding(
          element,
          dataIndex,
          selectedDataFieldId,
          selectedStoreId
        );

        const elementUpdates: any = {
          ...updatedElement,
          templateStoreId: element.templateStoreId,
          storeItemUid: null,
          storeItemSn: null,
          codeContent: BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType) // 重置為默認值
        };
        updateElement(elementUpdates);
      }

      return;
    }

    if (value.startsWith('item:')) {
      const itemUid = value.substring(5);
      console.log('Barcode 選擇商品:', itemUid);

      const selectedStore = storeData.find(store => store.id === selectedStoreId);
      if (selectedStore && selectedStore.storeSpecificData) {
        const item = selectedStore.storeSpecificData.find(i => i.uid === itemUid);
        if (item && selectedDataFieldId && item[selectedDataFieldId] !== undefined && item[selectedDataFieldId] !== null) {
          const displayValue = String(item[selectedDataFieldId]);
          console.log('Barcode 設置預覽值為:', displayValue);
          setPreviewValue(displayValue);

          const updatedElement = BarcodeBinding.setBinding(
            element,
            dataIndex,
            selectedDataFieldId,
            selectedStoreId
          );

          const elementUpdates: any = {
            ...updatedElement,
            templateStoreId: selectedStoreId || undefined,
            storeItemUid: itemUid,
            storeItemSn: itemUid,
            codeContent: displayValue // 同步綁定的數據到 codeContent
          };
          updateElement(elementUpdates);
        }
      }
    } else {
      setPreviewValue(BarcodeBinding.getSampleValueForBarcodeType(element.barcodeType));
    }
  };

  // 根據條碼類型獲取範例值
  const getSampleValueForBarcodeType = (type: string): string => {
    return BarcodeBinding.getSampleValueForBarcodeType(type);
  };

  // 獲取可綁定的資料欄位
  const getBindableFields = (): DataField[] => {
    return BarcodeBinding.getBindableFields(dataFields, element.barcodeType);
  };

  const bindableFields = getBindableFields();

  return (
    <div className="space-y-3">
      {/* 條碼類型選擇 */}
      <FormField label="條碼類型">
        <SelectInput
          value={element.barcodeType || 'code128'}
          onChange={handleBarcodeTypeChange}
          options={barcodeTypeOptions}
        />
      </FormField>

      {/* 條碼高度 */}
      <FormField label="條碼高度">
        <NumberInput
          value={element.height || 50}
          onChange={(value) => {
            const updates = { height: value };
            updateElement(updates);
          }}
          min={20}
          max={200}
          step={5}
          unit="px"
        />
      </FormField>

      {/* 靜區大小 */}
      <FormField label="靜區大小">
        <NumberInput
          value={element.quietZone || 10}
          onChange={(value) => {
            const updates = { quietZone: value };
            updateElement(updates);
          }}
          min={0}
          max={50}
          step={1}
          unit="px"
        />
      </FormField>

      {/* 顯示文字 */}
      <FormField label="顯示文字">
        <CheckboxInput
          checked={element.showText === true} // 預設為 false
          onChange={(checked) => {
            const updates = { showText: checked };
            updateElement(updates);
          }}
        />
      </FormField>

      {/* 資料綁定選擇 */}
      <FormField label="資料綁定">
        <SelectInput
          value={selectedDataFieldId || ''}
          onChange={handleDataFieldChange}
          options={[
            { value: '', label: '無綁定 (使用靜態內容)' },
            ...bindableFields.map(field => ({
              value: field.id,
              label: `${field.name} (${field.type})`
            }))
          ]}
        />
      </FormField>

      {/* 資料索引 - 只在有綁定時顯示 */}
      {selectedDataFieldId && (
        <FormField label="資料索引">
          <SelectInput
            value={dataIndex.toString()}
            onChange={handleDataIndexChange}
            options={dataIndexOptions}
          />
        </FormField>
      )}

      {/* 條碼內容 */}
      <FormField label="條碼內容">
        <TextInput
          value={selectedDataFieldId
            ? (previewValue !== null ? previewValue : getSampleValueForBarcodeType(element.barcodeType || 'code128'))
            : (element.codeContent || '')}
          onChange={handleContentChange}
          disabled={!!selectedDataFieldId}
          isBound={!!selectedDataFieldId}
        />
      </FormField>

      {/* 提示訊息 - 顯示在條碼內容下方 */}
      {selectedDataFieldId && (
        <div className="text-sm text-red-400 -mt-2">
          <span>已綁定資料欄位，顯示佔位符。將在預覽時替換為實際資料。</span>
        </div>
      )}

      {/* 預覽數據 - 只在有綁定時顯示 */}
      {selectedDataFieldId && (
        <FormField label="預覽數據">
          <select
            className="w-full p-2 bg-gray-700 rounded text-white border border-gray-600 focus:border-blue-500"
            value={(element as any).storeItemUid || (element as any).storeItemSn ? `item:${(element as any).storeItemUid || (element as any).storeItemSn}` : ''}
            onChange={(e) => handlePreviewSelect(e.target.value)}
          >
            <option value="">選擇預覽數據</option>
            {generatePreviewOptions().map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </FormField>
      )}

      {/* 條碼前景色（條碼顏色） */}
      <FormField label="條碼顏色">
        <NonTransparentColorInput
          value={element.lineColor || '#000000'}
          onChange={(value) => {
            const updates = { lineColor: value };
            updateElement(updates);
          }}
          colorType={colorType}
        />
      </FormField>

      {/* 背景色 */}
      <FormField label="背景色">
        <RestrictedColorInput
          value={element.fillColor || '#FFFFFF'}
          onChange={(value) => {
            const updates = { fillColor: value };
            updateElement(updates);
          }}
          colorType={colorType}
        />
      </FormField>

      {/* 邊框設定 */}
      <FormField label="顯示邊框">
        <CheckboxInput
          checked={element.showBorder !== false} // 默認顯示邊框
          onChange={(checked) => {
            const updates = { showBorder: checked };
            updateElement(updates);
          }}
        />
      </FormField>

      {/* 邊框寬度 - 只在顯示邊框時顯示 */}
      {element.showBorder !== false && (
        <FormField label="邊框寬度">
          <NumberInput
            value={element.lineWidth || 1}
            onChange={(value) => {
              const updates = { lineWidth: value };
              updateElement(updates);
            }}
            min={1}
            max={10}
            step={1}
            unit="px"
          />
        </FormField>
      )}

      {/* 邊框顏色 - 只在顯示邊框時顯示 */}
      {element.showBorder !== false && (
        <FormField label="邊框顏色">
          <RestrictedColorInput
            value={element.borderColor || '#000000'}
            onChange={(value) => {
              const updates = { borderColor: value };
              updateElement(updates);
            }}
            colorType={colorType}
          />
        </FormField>
      )}


    </div>
  );
};
