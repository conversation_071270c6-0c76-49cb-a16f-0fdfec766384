# 懸浮球增強功能文檔

## 🎯 解決的問題

用戶反饋懸浮球在某些頁面會遮擋重要內容或操作按鈕，影響使用體驗。

## ✨ 新增功能

### 1. 最小化功能 🔽
**觸發方式**: 點擊懸浮球右上角的 ❌ 按鈕
**效果**: 
- 懸浮球縮小到屏幕右邊緣
- 顯示為一個小的觸發器
- 完全不遮擋頁面內容

**恢復方式**: 點擊邊緣觸發器即可恢復

### 2. 智能隱藏功能 📱
**觸發條件**: 
- 用戶向下滾動超過100px時自動隱藏
- 向上滾動時自動顯示

**效果**:
- 滾動閱讀時不會被懸浮球干擾
- 需要使用時自然顯示

### 3. 拖拽移動功能 🖱️
**使用方式**: 
- 長按主按鈕可拖拽移動
- 拖拽時不會觸發展開功能
- 自動限制在視窗安全範圍內

**視覺反饋**:
- 拖拽時鼠標變為抓取狀態
- 位置變化有平滑過渡動畫

### 4. 邊界智能吸附 🧲
**功能**: 拖拽結束後自動吸附到最近的邊角
**好處**: 保持整潔的視覺效果

## 🎨 視覺設計

### 最小化觸發器
```css
/* 邊緣觸發器樣式 */
.fab-minimized-trigger {
  width: 1rem;
  height: 2rem;
  background: linear-gradient(135deg, purple, pink);
  backdrop-filter: blur(10px);
  border-radius: 0.5rem 0 0 0.5rem;
  transition: width 0.3s ease;
}

.fab-minimized-trigger:hover {
  width: 1.5rem; /* 懸停時稍微展開 */
}
```

### 智能隱藏動畫
```css
/* 隱藏狀態 */
.fab-auto-hide {
  transform: translateY(100px);
  opacity: 0.3;
  pointer-events: none;
}

/* 顯示狀態 */
.fab-auto-show {
  transform: translateY(0);
  opacity: 1;
  pointer-events: auto;
}
```

### 拖拽狀態
```css
/* 拖拽中 */
.fab-dragging {
  cursor: grabbing !important;
  user-select: none;
  z-index: 9999;
  transition: none; /* 拖拽時禁用過渡動畫 */
}

/* 可拖拽 */
.fab-draggable {
  cursor: grab;
  transition: all 0.3s ease-out;
}
```

## 🔧 技術實現

### 狀態管理
```typescript
const [isMinimized, setIsMinimized] = useState(false);
const [isHidden, setIsHidden] = useState(false);
const [position, setPosition] = useState({ x: 24, y: 24 });
const [isDragging, setIsDragging] = useState(false);
const [lastScrollY, setLastScrollY] = useState(0);
```

### 智能隱藏邏輯
```typescript
useEffect(() => {
  const handleScroll = () => {
    const currentScrollY = window.scrollY;
    
    // 向下滾動且超過100px時隱藏
    if (currentScrollY > lastScrollY && currentScrollY > 100) {
      setIsHidden(true);
      setIsExpanded(false);
    } 
    // 向上滾動時顯示
    else if (currentScrollY < lastScrollY) {
      setIsHidden(false);
    }
    
    setLastScrollY(currentScrollY);
  };

  // 使用 requestAnimationFrame 節流
  let ticking = false;
  const throttledScroll = () => {
    if (!ticking) {
      requestAnimationFrame(() => {
        handleScroll();
        ticking = false;
      });
      ticking = true;
    }
  };

  window.addEventListener('scroll', throttledScroll);
  return () => window.removeEventListener('scroll', throttledScroll);
}, [lastScrollY]);
```

### 拖拽實現
```typescript
const handleMouseDown = (e: React.MouseEvent) => {
  if (isExpanded) return; // 展開時不允許拖拽
  
  setIsDragging(true);
  dragRef.current = {
    startX: e.clientX,
    startY: e.clientY,
    startPosX: position.x,
    startPosY: position.y
  };
};

const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging) return;
  
  const deltaX = dragRef.current.startX - e.clientX;
  const deltaY = e.clientY - dragRef.current.startY;
  
  // 邊界限制
  const newX = Math.max(24, Math.min(window.innerWidth - 80, 
    dragRef.current.startPosX + deltaX));
  const newY = Math.max(24, Math.min(window.innerHeight - 80, 
    dragRef.current.startPosY + deltaY));
  
  setPosition({ x: newX, y: newY });
};
```

## 🎮 用戶交互

### 操作流程
1. **正常使用**: 點擊主按鈕展開功能
2. **需要移動**: 長按主按鈕拖拽到合適位置
3. **臨時隱藏**: 向下滾動頁面自動隱藏
4. **完全隱藏**: 點擊最小化按鈕縮到邊緣
5. **恢復顯示**: 點擊邊緣觸發器或向上滾動

### 視覺提示
- **可拖拽**: 鼠標懸停顯示抓取圖標
- **拖拽中**: 鼠標變為抓取狀態，按鈕有拖拽提示
- **最小化**: 右上角顯示 ❌ 按鈕
- **邊緣觸發**: 右邊緣顯示漸變色觸發器

## 📱 響應式適配

### 移動設備優化
- 觸摸拖拽支持
- 更大的觸摸目標
- 簡化的最小化觸發器

### 桌面設備增強
- 鼠標懸停效果
- 更精細的拖拽控制
- 鍵盤快捷鍵支持（未來功能）

## 🎯 用戶體驗改進

### 解決的問題
1. ✅ **內容遮擋**: 最小化功能完全解決
2. ✅ **閱讀干擾**: 智能隱藏自動處理
3. ✅ **位置固定**: 拖拽功能允許自定義位置
4. ✅ **操作衝突**: 拖拽和展開功能互不干擾

### 保持的優點
1. ✅ **Apple風格**: 所有動畫效果保持一致
2. ✅ **功能完整**: 原有功能完全保留
3. ✅ **性能優化**: 使用節流和 RAF 優化
4. ✅ **視覺美觀**: 新功能與整體設計和諧

## 🔮 未來擴展

### 可能的增強功能
1. **智能位置記憶**: 記住用戶偏好位置
2. **手勢控制**: 支持滑動手勢操作
3. **快捷鍵**: 鍵盤快捷鍵控制
4. **自動吸附**: 拖拽後自動吸附到邊角
5. **多狀態切換**: 支持更多顯示模式

### 配置選項
```typescript
interface FABConfig {
  autoHide: boolean;          // 是否啟用智能隱藏
  draggable: boolean;         // 是否允許拖拽
  minimizable: boolean;       // 是否顯示最小化按鈕
  snapToEdge: boolean;        // 是否自動吸附邊緣
  hideOnScroll: boolean;      // 滾動時是否隱藏
  scrollThreshold: number;    // 滾動隱藏閾值
}
```

## 📋 測試檢查清單

### 功能測試
- [ ] 最小化按鈕正常工作
- [ ] 邊緣觸發器可以恢復
- [ ] 拖拽移動流暢
- [ ] 智能隱藏正確觸發
- [ ] 邊界限制有效

### 交互測試
- [ ] 拖拽時不會展開
- [ ] 展開時不能拖拽
- [ ] 最小化時隱藏所有功能
- [ ] 滾動隱藏不影響其他功能

### 視覺測試
- [ ] 動畫過渡自然
- [ ] 觸發器設計美觀
- [ ] 拖拽狀態清晰
- [ ] 響應式適配正確

## 🎉 總結

新的懸浮球功能提供了完整的用戶控制選項：
- 🔽 **最小化**: 完全隱藏到邊緣
- 📱 **智能隱藏**: 滾動時自動處理
- 🖱️ **拖拽移動**: 自由調整位置
- 🎨 **視覺優化**: 保持Apple風格美感

這些功能完美解決了遮擋問題，同時保持了原有的優雅體驗！
