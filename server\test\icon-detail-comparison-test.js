/**
 * 圖標詳細比較測試 - 測試單個圖標的詳細渲染
 */

const fs = require('fs');
const path = require('path');

/**
 * 測試單個圖標的詳細渲染
 */
async function testIconDetailComparison() {
  try {
    console.log('=== 開始圖標詳細比較測試 ===\n');

    // 引入預覽服務
    const { regeneratePreviewBeforeSend } = require('../services/previewService');

    // 測試不同大小的同一個圖標
    const testCases = [
      {
        name: 'star-small',
        iconType: 'star',
        size: { width: 40, height: 40 },
        position: { x: 50, y: 50 }
      },
      {
        name: 'star-medium',
        iconType: 'star',
        size: { width: 60, height: 60 },
        position: { x: 120, y: 50 }
      },
      {
        name: 'star-large',
        iconType: 'star',
        size: { width: 80, height: 80 },
        position: { x: 210, y: 50 }
      },
      {
        name: 'heart-small',
        iconType: 'heart',
        size: { width: 40, height: 40 },
        position: { x: 50, y: 150 }
      },
      {
        name: 'alert-circle-medium',
        iconType: 'alert-circle',
        size: { width: 60, height: 60 },
        position: { x: 120, y: 150 }
      },
      {
        name: 'home-large',
        iconType: 'home',
        size: { width: 80, height: 80 },
        position: { x: 210, y: 150 }
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n--- 測試案例: ${testCase.name} ---`);
      
      // 創建測試模板
      const testTemplate = {
        id: `icon-detail-test-${testCase.name}`,
        name: `圖標詳細測試 - ${testCase.name}`,
        width: 350,
        height: 280,
        screenSize: '350x280',
        elements: [
          {
            id: 'test-icon',
            type: 'icon',
            x: testCase.position.x,
            y: testCase.position.y,
            width: testCase.size.width,
            height: testCase.size.height,
            iconType: testCase.iconType,
            lineColor: '#000000',
            lineWidth: 2
          }
        ]
      };

      // 創建測試設備數據
      const testDevice = {
        id: 'test-device',
        dataBindings: {}
      };

      // 創建測試門店數據
      const testStoreData = [
        {
          id: 'test-store',
          name: '測試門店',
          storeSpecificData: []
        }
      ];

      console.log(`生成預覽圖: ${testCase.iconType} (${testCase.size.width}x${testCase.size.height})`);
      
      // 計算預期的實際icon尺寸和位置
      const containerWidth = testCase.size.width;
      const containerHeight = testCase.size.height;
      const expectedIconSize = Math.min(containerWidth, containerHeight) * 0.8;
      const expectedOffsetX = (containerWidth - expectedIconSize) / 2;
      const expectedOffsetY = (containerHeight - expectedIconSize) / 2;
      const expectedActualX = testCase.position.x + expectedOffsetX;
      const expectedActualY = testCase.position.y + expectedOffsetY;
      
      console.log(`預期位置調整:`);
      console.log(`  容器: (${testCase.position.x}, ${testCase.position.y}, ${containerWidth}x${containerHeight})`);
      console.log(`  實際: (${expectedActualX}, ${expectedActualY}, ${expectedIconSize}x${expectedIconSize})`);

      const result = await regeneratePreviewBeforeSend(testDevice, testStoreData, testTemplate, []);

      if (result) {
        // 將 base64 圖片保存到檔案
        const base64Data = result.replace(/^data:image\/png;base64,/, '');
        const outputPath = path.join(__dirname, `icon-detail-${testCase.name}-output.png`);
        fs.writeFileSync(outputPath, base64Data, 'base64');
        
        console.log(`✓ 預覽圖已保存到: ${outputPath}`);
        console.log(`  圖片大小: ${Math.round(base64Data.length * 0.75 / 1024)} KB`);
      } else {
        console.log(`✗ ${testCase.name} 預覽圖生成失敗`);
      }
    }

    console.log('\n=== 圖標詳細比較測試完成 ===');

  } catch (error) {
    console.error('測試過程中發生錯誤:', error);
    console.error('錯誤堆疊:', error.stack);
  }
}

// 執行測試
testIconDetailComparison();
