# EPD Manager 快速操作指南

## 🚀 5分鐘快速啟動指南

本指南將幫助您在最短時間內完成EPD Manager系統的完整部署和配置，從零開始到設備正常運行。

---

## 📋 準備工作清單

### 硬體需求
- ✅ EPD Manager 服務器（或雲端服務）
- ✅ EPD 網關設備（至少1台）
- ✅ EPD 電子紙顯示器設備（至少1台）
- ✅ 智能手機（iOS/Android）
- ✅ 穩定的WiFi網絡環境

### 軟體需求
- ✅ EPD Manager Web系統
- ✅ EPD Manager 移動應用程序
- ✅ 現代瀏覽器（Chrome、Firefox、Safari等）

### 系統結構
以下架構圖展示了 EPD Manager 系統中各硬體與軟體元件之間的通訊關係：

![EPD Manager 系統架構圖](screenshots/quick-start/system-architecture-diagram.png)

---

## 🔄 完整操作流程圖

```mermaid
flowchart TD
    A[啟動EPD Manager系統] --> B[創建管理員帳號]
    B --> C[掃描QR碼下載App]
    C --> D[App自動搜尋Server]
    D --> E{Server連接成功?}
    E -->|否| F[手動輸入Server地址]
    F --> G[輸入帳號密碼登入App]
    E -->|是| G
    G --> H[創建或選擇門店]
    H --> I[App自動搜尋Gateway]
    I --> J{發現Gateway?}
    J -->|否| K[檢查網絡連接]
    K --> I
    J -->|是| L[選擇Gateway進行配網]
    L --> M[Gateway自動註冊到系統]
    M --> N[Gateway自動搜尋EPD設備]
    N --> O[設備自動綁定到系統]
    O --> P[配置設備數據和模板]
    P --> Q[發送測試圖片到設備]
    Q --> R[✅ 系統部署完成]

    style A fill:#e1f5fe,color:#01579b
    style R fill:#c8e6c9,color:#1b5e20
    style E fill:#fff3e0,color:#e65100
    style J fill:#fff3e0,color:#e65100
```

---

## 📖 詳細操作步驟

### 步驟 1: 啟動 EPD Manager 系統

**操作說明：**
1. 啟動EPD Manager服務器
2. 打開瀏覽器訪問系統地址
3. 確認系統正常運行

**預期結果：**
- 看到EPD Manager登入頁面
- 系統狀態顯示正常

![系統啟動頁面](screenshots/quick-start/01-system-startup.png)

**⚠️ 故障排除：**
- 如果無法訪問，檢查服務器狀態和網絡連接
- 確認防火牆設置允許相關端口訪問

---

### 步驟 2: 創建管理員帳號

**操作說明：**
1. 點擊「創建管理員帳號」或「註冊」按鈕
2. 填寫管理員信息：
   - 用戶名（建議使用英文）
   - 密碼（至少8位，包含字母和數字）
   - 確認密碼
   - 電子郵件（可選）
3. 點擊「創建帳號」完成註冊
4. 使用新創建的帳號登入系統

**預期結果：**
- 成功創建管理員帳號
- 能夠正常登入Web管理界面

<!-- 截圖預留位置 -->
![創建管理員帳號](screenshots/quick-start/02-create-admin-account.png)

**💡 最佳實踐：**
- 使用強密碼確保系統安全
- 記錄帳號信息以備後用

---

### 步驟 3: 掃描 QR 碼下載 App

**操作說明：**
1. 登入Web系統後，尋找「下載App」或「移動應用」區域
2. 使用手機掃描QR碼
3. 根據手機系統選擇下載：
   - iOS：跳轉到App Store
   - Android：下載APK文件或跳轉到Google Play
4. 安裝EPD Manager App

**預期結果：**
- 成功下載並安裝App
- App能夠正常啟動

<!-- 截圖預留位置 -->
![QR碼下載頁面](screenshots/quick-start/03-qr-code-download.png)

**📱 App安裝注意事項：**
- Android用戶可能需要允許「未知來源」安裝
- 確保手機有足夠的存儲空間

---

### 步驟 5: App 連接 Server

**操作說明：**

#### 5.1 自動搜尋 Server（推薦）
1. 打開EPD Manager App
2. App會自動搜尋同一網絡內的Server
3. 如果找到Server，會自動填入Server地址

#### 5.2 手動輸入 Server 地址
1. 如果自動搜尋失敗，點擊「手動設置」
2. 輸入Server地址：
   - 格式：`http://192.168.1.100:3000` 或 `https://your-domain.com`
   - 確保地址正確且可訪問
3. 點擊「測試連接」驗證

**預期結果：**
- App顯示「Server連接成功」
- 能夠進入登入頁面

<!-- 截圖預留位置 -->
![App Server連接](screenshots/quick-start/04-app-server-connection.png)

**🔧 連接問題排除：**
- 確認手機和Server在同一網絡
- 檢查Server地址和端口是否正確
- 確認Server防火牆設置

---

### 步驟 6: App 登入

**操作說明：**
1. 在App登入頁面輸入：
   - 用戶名：步驟2創建的管理員用戶名
   - 密碼：對應的密碼
2. 選擇「記住我」（可選）
3. 點擊「登入」按鈕

**預期結果：**
- 成功登入App
- 進入門店選擇頁面

<!-- 截圖預留位置 -->
![App登入頁面](screenshots/quick-start/05-app-login.png)

**🔐 登入提示：**
- 首次登入建議選擇「記住我」
- 如果忘記密碼，可在Web端重置

---

### 步驟 6: 創建或選擇門店

**操作說明：**

#### 6.1 創建新門店（如需要）
1. 在門店列表頁面點擊「創建新門店」按鈕
2. 填寫門店信息：
   - 門店名稱
   - 門店地址
   - 聯繫電話（可選）
   - 負責人（可選）
3. 點擊「確認創建」按鈕

![門店管理頁面](screenshots/quick-start/06-store-management.png)

#### 6.2 選擇門店
1. 在門店列表中選擇要管理的門店
   - 如果是首次使用，系統會自動創建默認門店
   - 可以使用搜索或篩選找到特定門店
2. 點擊門店卡片或「進入」按鈕
3. 進入門店管理界面

**預期結果：**
- 成功創建新門店（如有需要）
- 成功進入選擇的門店管理界面
- 看到網關管理等功能選項

![門店選擇頁面](screenshots/quick-start/06-store-selection.png)

**💡 門店管理提示：**
- 門店名稱建議使用容易識別的命名
- 可以添加門店備註信息以便管理
- 建議填寫完整的門店信息，方便後期維護
- 可以設置常用門店為默認門店

---

### 步驟 8: 自動搜尋和配網 Gateway

**操作說明：**

#### 8.1 自動搜尋 Gateway
1. 在App中點擊「網關管理」或「掃描網關」
2. App會自動使用UDP廣播搜尋本地網絡中的Gateway
3. 等待搜尋完成（通常需要10-30秒）

#### 8.2 選擇和配網 Gateway
1. 從搜尋結果中選擇要配網的Gateway
2. 查看Gateway信息：
   - MAC地址
   - IP地址
   - 設備型號
   - 固件版本
3. 點擊「配網」或「註冊」按鈕
4. App會自動：
   - 將Gateway註冊到Server
   - 配置WebSocket連接信息
   - 發送配置到Gateway

**預期結果：**
- Gateway成功註冊到系統
- Gateway狀態顯示「在線」
- 在Web端可以看到新註冊的Gateway

<!-- 截圖預留位置 -->
![Gateway搜尋和配網](screenshots/quick-start/07-gateway-scan-config.png)

![Gateway搜尋和配網](screenshots/quick-start/07-gateway-scan-config-1.png)

**🌐 網關配網注意事項：**
- 確保Gateway和手機在同一WiFi網絡
- Gateway需要處於配網模式（通常是出廠狀態）
- 配網過程中不要關閉App或切換網絡

---

### 步驟 9: 自動搜尋和綁定 EPD 設備

**操作說明：**

#### 9.1 Gateway 自動搜尋設備
1. Gateway配網成功後，會自動搜尋附近的EPD設備
2. 在Web端或App端查看「設備管理」頁面
3. 等待設備被自動發現（可能需要1-5分鐘）

#### 9.2 設備自動綁定
1. 發現的設備會自動綁定到當前門店
2. 設備狀態會顯示為「在線」或「離線」
3. 可以查看設備的基本信息：
   - MAC地址
   - 電池電量
   - 信號強度
   - 設備型號

**預期結果：**
- EPD設備出現在設備列表中
- 設備狀態正常顯示
- 可以對設備進行基本操作

<!-- 截圖預留位置 -->
![設備自動發現和綁定](screenshots/quick-start/08-device-discovery-binding.png)

**📡 設備發現提示：**
- 確保EPD設備已開機且電量充足
- 設備需要在Gateway的通信範圍內
- 如果設備未自動發現，可以手動添加

---

### 步驟 10: 配置設備數據和模板

**操作說明：**

#### 10.1 創建或選擇模板
1. 在Web端進入「模板管理」
2. 選擇現有模板或創建新模板
3. 使用模板編輯器設計顯示內容

#### 10.2 準備數據
1. 進入「數據管理」頁面
2. 添加要顯示的數據內容
3. 確保數據格式正確

#### 10.3 綁定設備
1. 在「設備管理」中選擇設備
2. 點擊「綁定數據」
3. 選擇模板和對應的數據
4. 確認綁定設置

**預期結果：**
- 設備成功綁定模板和數據
- 可以看到預覽圖效果

<!-- 截圖預留位置 -->
![設備數據和模板配置-WEB](screenshots/quick-start/09-device-template-data-config-web.png)

![設備數據和模板配置-APP](screenshots/quick-start/09-device-template-data-config-app.png)
---

### 步驟 11: 發送測試圖片

**操作說明：**
1. 在設備列表中選擇已配置的設備
2. 點擊「發送預覽圖」或「刷新顯示」
3. 等待圖片傳輸完成
4. 觀察EPD設備屏幕變化

**預期結果：**
- 設備成功接收並顯示新圖片
- 圖片內容與預覽一致
- 設備狀態更新為「已更新」

<!-- 截圖預留位置 -->
![發送測試圖片-WEB](screenshots/quick-start/10-send-test-image-web.png)

![發送測試圖片-APP](screenshots/quick-start/10-send-test-image-app.png)

**🖼️ 圖片發送提示：**
- 首次發送可能需要較長時間
- 確保Gateway和設備通信正常
- 如果發送失敗，檢查網絡連接

---

## ✅ 系統部署完成檢查清單

### 基本功能驗證
- [ ] Web系統可以正常訪問和操作
- [ ] 移動App可以正常連接和登入
- [ ] Gateway已成功註冊並顯示在線
- [ ] EPD設備已被發現並可以正常通信
- [ ] 可以成功發送圖片到設備
- [ ] 設備顯示內容正確

### 高級功能測試（可選）
- [ ] 批量設備操作
- [ ] 定時刷圖計劃
- [ ] 實時狀態監控
- [ ] 數據綁定和模板切換
- [ ] 用戶權限管理

---

## 🆘 常見問題和解決方案

### Q1: App無法找到Server
**解決方案：**
- 確認手機和Server在同一網絡
- 手動輸入Server的IP地址和端口
- 檢查Server防火牆設置

### Q2: Gateway搜尋不到
**解決方案：**
- 確認Gateway處於配網模式
- 檢查WiFi網絡連接
- 重啟Gateway設備
- 確認Gateway和手機在同一網段

### Q3: EPD設備無法發現
**解決方案：**
- 檢查設備電量是否充足
- 確認設備在Gateway通信範圍內
- 重啟Gateway和EPD設備
- 手動添加設備MAC地址

### Q4: 圖片發送失敗
**解決方案：**
- 檢查Gateway和設備的連接狀態
- 確認模板和數據配置正確
- 重試發送操作
- 檢查系統日誌獲取詳細錯誤信息

---

## 📞 技術支持

如果在快速部署過程中遇到問題，請：

1. **查看系統日誌**：在Web端「系統日誌」頁面查看詳細錯誤信息
2. **檢查網絡連接**：確保所有設備在同一網絡環境
3. **重啟設備**：嘗試重啟Gateway和EPD設備
4. **聯繫技術支持**：提供詳細的錯誤信息和系統配置

---

## 🎯 快速驗證系統功能

### 基本功能測試（必做）

#### 1. 設備通信測試
```bash
✅ 測試步驟：
1. 在設備列表中選擇一台設備
2. 點擊「發送預覽圖」
3. 觀察設備屏幕是否更新
4. 檢查設備狀態是否變為「已更新」

⏱️ 預期時間：30秒-2分鐘
```

#### 2. 實時狀態監控測試
```bash
✅ 測試步驟：
1. 關閉一台EPD設備的電源
2. 觀察Web端設備狀態是否變為「離線」
3. 重新開啟設備電源
4. 確認狀態恢復為「在線」

⏱️ 預期時間：1-3分鐘
```

#### 3. 批量操作測試
```bash
✅ 測試步驟：
1. 選擇多台設備（使用Ctrl+點擊或全選）
2. 點擊「批量發送」
3. 觀察所有設備是否同時更新
4. 檢查任務執行狀態

⏱️ 預期時間：根據設備數量而定
```

### 高級功能測試（推薦）

#### 1. 模板編輯測試
```bash
✅ 測試步驟：
1. 創建新模板或編輯現有模板
2. 添加文字、圖片等元素
3. 保存模板並綁定到設備
4. 發送到設備驗證顯示效果

⏱️ 預期時間：5-10分鐘
```

#### 2. 數據綁定測試
```bash
✅ 測試步驟：
1. 在數據管理中添加新數據
2. 將數據綁定到設備
3. 修改數據內容
4. 重新發送到設備確認更新

⏱️ 預期時間：3-5分鐘
```

---

## 📈 系統性能優化建議

### 網絡優化
- **WiFi信號強度**：確保Gateway和設備之間信號強度 > -70dBm
- **網絡帶寬**：建議至少10Mbps上行帶寬
- **網絡穩定性**：避免頻繁的網絡中斷

### 設備部署優化
- **Gateway位置**：放置在設備群的中心位置
- **設備密度**：每個Gateway建議管理不超過50台設備
- **電池管理**：定期檢查設備電池電量，及時更換

### 系統配置優化
- **並發數設置**：根據Gateway數量調整並發處理數
- **重試機制**：設置合理的重試次數和間隔
- **日誌級別**：生產環境建議使用INFO級別

---

## 🔄 日常維護檢查清單

### 每日檢查
- [ ] 檢查系統整體狀態
- [ ] 查看設備在線率
- [ ] 檢查Gateway連接狀態
- [ ] 查看異常告警信息

### 每週檢查
- [ ] 檢查設備電池電量
- [ ] 清理系統日誌
- [ ] 檢查存儲空間使用情況
- [ ] 備份重要配置和數據

### 每月檢查
- [ ] 檢查系統性能指標
- [ ] 更新固件版本（如有）
- [ ] 檢查用戶權限設置
- [ ] 生成月度運營報告

---

## 🚀 進階功能探索

### 1. AI智能助手（開發中）
- 智能故障診斷
- 自動優化建議
- 語音操作控制

### 2. 高級排程功能
- 複雜的定時任務
- 條件觸發刷圖
- 批量任務管理

### 3. 數據分析功能
- 設備使用統計
- 性能趨勢分析
- 自定義報表生成

### 4. 企業級功能
- 多租戶管理
- API接口集成
- 第三方系統對接

---

## 📚 相關文檔

- 📖 [EPD Manager 產品介紹](EPD_Manager_產品介紹.md)
- 📋 [頁面功能詳細介紹](EPD_Manager_頁面功能詳細介紹.md)
- 🔧 [系統配置手冊](系統配置手冊.md)
- 🛠️ [故障排除指南](故障排除指南.md)
- 📊 [API文檔](API文檔.md)

---

## 🎉 部署成功！

**恭喜！您已成功完成EPD Manager系統的快速部署！**

### 🎯 您現在可以：
- ✅ 管理所有EPD設備和網關
- ✅ 創建和編輯顯示模板
- ✅ 批量更新設備內容
- ✅ 監控系統實時狀態
- ✅ 使用移動App進行便攜管理

### 🚀 下一步建議：
1. **探索高級功能**：嘗試使用定時任務、數據分析等功能
2. **優化系統配置**：根據實際使用情況調整系統參數
3. **培訓團隊成員**：讓更多人學會使用系統
4. **制定維護計劃**：建立定期維護和檢查流程

### 💡 獲得幫助：
- 查看系統內置幫助文檔
- 參考相關技術文檔
- 聯繫技術支持團隊

**開始享受EPD Manager帶來的智能化管理體驗吧！** 🌟
