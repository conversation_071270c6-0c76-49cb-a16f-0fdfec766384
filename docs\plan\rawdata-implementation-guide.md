# Rawdata 格式增強實作指南

## 實作步驟詳解

### 步驟 1：建立壓縮模組架構

#### 1.1 創建目錄結構
```bash
server/utils/rawdataCompression/
├── index.js                    # 主要導出接口
├── types.js                    # 類型定義和常數
├── compressors/
│   ├── baseCompressor.js       # 基礎壓縮器抽象類
│   ├── runLengthCompressor.js  # Run-Length 壓縮器
│   └── compressionRegistry.js  # 壓縮器註冊管理
├── utils/
│   ├── formatSelector.js       # 格式選擇邏輯
│   └── compressionAnalyzer.js  # 壓縮效果分析
└── __tests__/
    ├── runLengthCompressor.test.js
    ├── formatSelector.test.js
    └── compressionAnalyzer.test.js
```

#### 1.2 types.js - 類型定義
```javascript
// 支援的 rawdata 格式
const RAWDATA_FORMATS = {
  RAWDATA: 'rawdata',           // 未壓縮原始數據
  RUNLENDATA: 'runlendata',     // Run-Length Encoding 壓縮
  // 預留未來格式
  LZ4DATA: 'lz4data',           // LZ4 壓縮 (未實作)
  GZIPDATA: 'gzipdata'          // GZIP 壓縮 (未實作)
};

// 壓縮結果結構
const CompressionResult = {
  success: false,               // 壓縮是否成功
  format: '',                   // 使用的格式
  originalSize: 0,              // 原始數據大小
  compressedSize: 0,            // 壓縮後大小
  compressionRatio: 1.0,        // 壓縮比 (compressedSize / originalSize)
  processingTime: 0,            // 處理時間 (ms)
  data: null,                   // 壓縮後的數據
  error: null                   // 錯誤信息
};

// 格式選擇結果
const FormatSelectionResult = {
  selectedFormat: '',           // 選中的格式
  estimatedRatio: 1.0,          // 預估壓縮比
  estimatedSize: 0,             // 預估壓縮後大小
  reason: ''                    // 選擇原因
};

module.exports = {
  RAWDATA_FORMATS,
  CompressionResult,
  FormatSelectionResult
};
```

#### 1.3 baseCompressor.js - 基礎壓縮器類
```javascript
const { CompressionResult } = require('../types');

/**
 * 基礎壓縮器抽象類
 * 所有具體壓縮器都應該繼承此類
 */
class BaseCompressor {
  constructor(formatName) {
    if (new.target === BaseCompressor) {
      throw new Error('BaseCompressor is abstract and cannot be instantiated directly');
    }
    
    this.formatName = formatName;
    this.isInitialized = false;
  }
  
  /**
   * 初始化壓縮器
   * 子類可以重寫此方法進行特定初始化
   */
  initialize() {
    this.isInitialized = true;
  }
  
  /**
   * 壓縮像素數據（不包含 ImageInfo）
   * @param {Uint8Array} pixelData - 像素數據
   * @returns {CompressionResult} 壓縮結果
   */
  compress(pixelData) {
    throw new Error('compress method must be implemented by subclass');
  }
  
  /**
   * 解壓縮數據
   * @param {Uint8Array} compressedData - 壓縮數據
   * @returns {Uint8Array} 解壓縮後的數據
   */
  decompress(compressedData) {
    throw new Error('decompress method must be implemented by subclass');
  }
  
  /**
   * 快速估算壓縮比（不實際壓縮）
   * @param {Uint8Array} pixelData - 像素數據
   * @returns {number} 預估壓縮比 (0.0 - 1.0)
   */
  estimateCompressionRatio(pixelData) {
    throw new Error('estimateCompressionRatio method must be implemented by subclass');
  }
  
  /**
   * 檢查數據是否適合此壓縮格式
   * @param {Uint8Array} pixelData - 像素數據
   * @returns {boolean} 是否適合
   */
  isSuitableFor(pixelData) {
    // 預設實作：估算壓縮比，如果小於 0.9 則認為適合
    return this.estimateCompressionRatio(pixelData) < 0.9;
  }
  
  /**
   * 獲取格式名稱
   * @returns {string} 格式名稱
   */
  getFormatName() {
    return this.formatName;
  }
  
  /**
   * 創建壓縮結果對象
   * @param {boolean} success - 是否成功
   * @param {Uint8Array} originalData - 原始數據
   * @param {Uint8Array} compressedData - 壓縮數據
   * @param {number} processingTime - 處理時間
   * @param {string} error - 錯誤信息
   * @returns {CompressionResult} 壓縮結果
   */
  createResult(success, originalData, compressedData = null, processingTime = 0, error = null) {
    return {
      success,
      format: this.formatName,
      originalSize: originalData ? originalData.length : 0,
      compressedSize: compressedData ? compressedData.length : 0,
      compressionRatio: compressedData && originalData ? 
        compressedData.length / originalData.length : 1.0,
      processingTime,
      data: compressedData,
      error
    };
  }
}

module.exports = BaseCompressor;
```

### 步驟 2：實作 Run-Length 壓縮器

#### 2.1 runLengthCompressor.js
```javascript
const BaseCompressor = require('./baseCompressor');
const { RAWDATA_FORMATS } = require('../types');

/**
 * Run-Length Encoding 壓縮器
 * 實作與 Go 版本相同的 RLE 算法
 * 適用於有大量重複數據的場景，如單色區域較多的 EPD 圖像
 */
class RunLengthCompressor extends BaseCompressor {
  constructor() {
    super(RAWDATA_FORMATS.RUNLENDATA);

    // RLE 編碼常數（與 Go 版本一致）
    this.MIN_RUN_LENGTH = 2;      // 最小重複長度
    this.MAX_RUN_LENGTH = 0x7F;   // 最大重複長度 (127)
    this.MAX_LITERAL_LENGTH = 0x7F; // 最大非重複長度 (127)
    this.LITERAL_FLAG = 0x80;     // 非重複序列標記 (bit7 = 1)
  }
  
  /**
   * 壓縮像素數據
   * 編碼格式（與 Go 版本相同）：
   * - 重複序列：[runLength, value] (runLength >= 2, bit7 = 0)
   * - 非重複序列：[0x80|length, data...] (bit7 = 1)
   */
  compress(pixelData) {
    const startTime = performance.now();

    try {
      if (!pixelData || pixelData.length === 0) {
        return this.createResult(false, pixelData, null, 0, 'Empty input data');
      }

      const result = [];
      const n = pixelData.length;
      let inx = 0;

      while (inx < n) {
        // 檢查重複序列
        let runLength = 1;
        while (inx + runLength < n &&
               pixelData[inx + runLength] === pixelData[inx] &&
               runLength < this.MAX_RUN_LENGTH) {
          runLength++;
        }

        if (runLength >= this.MIN_RUN_LENGTH) {
          // 編碼重複序列：[runLength, value] (bit7 = 0)
          result.push(runLength);
          result.push(pixelData[inx]);
          inx += runLength;
        } else {
          // 非重複序列
          const start = inx;
          while (inx < n &&
                 (inx + 1 >= n || pixelData[inx] !== pixelData[inx + 1]) &&
                 (inx - start) < this.MAX_LITERAL_LENGTH) {
            inx++;
          }
          const length = inx - start;
          result.push(this.LITERAL_FLAG | length); // bit7 = 1
          for (let i = start; i < inx; i++) {
            result.push(pixelData[i]);
          }
        }
      }

      const compressedData = new Uint8Array(result);
      const processingTime = performance.now() - startTime;

      return this.createResult(true, pixelData, compressedData, processingTime);

    } catch (error) {
      const processingTime = performance.now() - startTime;
      return this.createResult(false, pixelData, null, processingTime, error.message);
    }
  }
  
  /**
   * 解壓縮數據（與 Go 版本對應）
   */
  decompress(compressedData) {
    if (!compressedData || compressedData.length === 0) {
      throw new Error('Empty compressed data');
    }

    const decompressed = [];
    let i = 0;

    while (i < compressedData.length) {
      const header = compressedData[i];
      i++;

      if ((header & this.LITERAL_FLAG) === 0) {
        // 重複序列：bit7 = 0
        const runLength = header;
        if (i >= compressedData.length) {
          throw new Error('Incomplete RLE data: missing value byte');
        }
        const value = compressedData[i];
        i++;

        for (let j = 0; j < runLength; j++) {
          decompressed.push(value);
        }
      } else {
        // 非重複序列：bit7 = 1
        const length = header & this.MAX_LITERAL_LENGTH;
        if (i + length > compressedData.length) {
          throw new Error('Incomplete RLE data: insufficient data bytes');
        }

        for (let j = 0; j < length; j++) {
          decompressed.push(compressedData[i + j]);
        }
        i += length;
      }
    }

    return new Uint8Array(decompressed);
  }
  
  /**
   * 快速估算壓縮比（與 Go 版本邏輯一致）
   */
  estimateCompressionRatio(pixelData) {
    if (!pixelData || pixelData.length === 0) {
      return 1.0;
    }

    let estimatedSize = 0;
    const n = pixelData.length;
    let inx = 0;

    while (inx < n) {
      // 檢查重複序列
      let runLength = 1;
      while (inx + runLength < n &&
             pixelData[inx + runLength] === pixelData[inx] &&
             runLength < this.MAX_RUN_LENGTH) {
        runLength++;
      }

      if (runLength >= this.MIN_RUN_LENGTH) {
        // 重複序列：2 bytes (runLength + value)
        estimatedSize += 2;
        inx += runLength;
      } else {
        // 非重複序列
        const start = inx;
        while (inx < n &&
               (inx + 1 >= n || pixelData[inx] !== pixelData[inx + 1]) &&
               (inx - start) < this.MAX_LITERAL_LENGTH) {
          inx++;
        }
        const length = inx - start;
        // 非重複序列：1 + length bytes (header + data)
        estimatedSize += 1 + length;
      }
    }

    return estimatedSize / pixelData.length;
  }
  
  /**
   * 檢查數據是否適合 RLE 壓縮
   * RLE 適合有大量重複數據的場景
   */
  isSuitableFor(pixelData) {
    if (!pixelData || pixelData.length < 4) {
      return false;
    }

    // 快速檢查：計算連續相同字節對的數量
    let consecutivePairs = 0;
    for (let i = 0; i < pixelData.length - 1; i++) {
      if (pixelData[i] === pixelData[i + 1]) {
        consecutivePairs++;
      }
    }

    const consecutiveRatio = consecutivePairs / (pixelData.length - 1);

    // 如果連續相同字節對比例超過 25% 且估算壓縮比小於 0.85，則適合
    return consecutiveRatio > 0.25 && this.estimateCompressionRatio(pixelData) < 0.85;
  }
}

module.exports = RunLengthCompressor;
```

### 步驟 3：壓縮器註冊管理

#### 3.1 compressionRegistry.js
```javascript
const { RAWDATA_FORMATS } = require('../types');

/**
 * 壓縮器註冊表
 * 管理所有可用的壓縮器
 */
class CompressionRegistry {
  constructor() {
    this.compressors = new Map();
    this.defaultFormat = RAWDATA_FORMATS.RAWDATA;
  }
  
  /**
   * 註冊壓縮器
   * @param {BaseCompressor} compressor - 壓縮器實例
   */
  register(compressor) {
    if (!compressor || typeof compressor.getFormatName !== 'function') {
      throw new Error('Invalid compressor: must have getFormatName method');
    }
    
    const formatName = compressor.getFormatName();
    
    if (this.compressors.has(formatName)) {
      console.warn(`Compressor for format '${formatName}' already registered, overwriting`);
    }
    
    // 初始化壓縮器
    if (typeof compressor.initialize === 'function') {
      compressor.initialize();
    }
    
    this.compressors.set(formatName, compressor);
    console.log(`Registered compressor for format: ${formatName}`);
  }
  
  /**
   * 獲取壓縮器
   * @param {string} formatName - 格式名稱
   * @returns {BaseCompressor|null} 壓縮器實例
   */
  getCompressor(formatName) {
    return this.compressors.get(formatName) || null;
  }
  
  /**
   * 獲取所有支援的格式
   * @returns {string[]} 格式名稱列表
   */
  getSupportedFormats() {
    return Array.from(this.compressors.keys());
  }
  
  /**
   * 檢查格式是否支援
   * @param {string} formatName - 格式名稱
   * @returns {boolean} 是否支援
   */
  isFormatSupported(formatName) {
    return this.compressors.has(formatName);
  }
  
  /**
   * 移除壓縮器
   * @param {string} formatName - 格式名稱
   */
  unregister(formatName) {
    if (this.compressors.delete(formatName)) {
      console.log(`Unregistered compressor for format: ${formatName}`);
    }
  }
  
  /**
   * 清空所有壓縮器
   */
  clear() {
    this.compressors.clear();
    console.log('Cleared all compressors');
  }
  
  /**
   * 設定預設格式
   * @param {string} formatName - 格式名稱
   */
  setDefaultFormat(formatName) {
    this.defaultFormat = formatName;
  }
  
  /**
   * 獲取預設格式
   * @returns {string} 預設格式名稱
   */
  getDefaultFormat() {
    return this.defaultFormat;
  }
}

// 創建全局註冊表實例
const globalRegistry = new CompressionRegistry();

module.exports = {
  CompressionRegistry,
  globalRegistry
};
```

### 步驟 4：格式選擇邏輯

#### 4.1 formatSelector.js
```javascript
const { RAWDATA_FORMATS, FormatSelectionResult } = require('../types');
const { globalRegistry } = require('../compressors/compressionRegistry');

/**
 * 格式處理器
 * 根據 Gateway 指定的格式進行數據處理
 */
class FormatProcessor {
  constructor(registry = globalRegistry) {
    this.registry = registry;
  }

  /**
   * 根據指定格式處理數據
   * @param {string} format - 目標格式
   * @param {Uint8Array} pixelData - 像素數據
   * @returns {Object} 處理結果
   */
  processFormat(format, pixelData) {
    if (format === RAWDATA_FORMATS.RAWDATA) {
      // 不處理，直接返回
      return {
        success: true,
        format: format,
        data: pixelData,
        originalSize: pixelData.length,
        processedSize: pixelData.length,
        processingRatio: 1.0,
        processingTime: 0,
        reason: 'No processing required for rawdata format'
      };
    }

    const processor = this.registry.getCompressor(format);
    if (!processor) {
      throw new Error(`Unsupported format: ${format}`);
    }

    const result = processor.compress(pixelData);

    return {
      success: result.success,
      format: format,
      data: result.data,
      originalSize: result.originalSize,
      processedSize: result.compressedSize,
      processingRatio: result.compressionRatio,
      processingTime: result.processingTime,
      reason: result.success ? `Processed with ${format}` : result.error,
      error: result.error
    };
  }

  /**
   * 檢查格式是否支援
   * @param {string} format - 格式名稱
   * @returns {boolean} 是否支援
   */
  isFormatSupported(format) {
    return format === RAWDATA_FORMATS.RAWDATA || this.registry.isFormatSupported(format);
  }
}

  /**
   * 最佳壓縮策略：選擇壓縮比最好的格式
   */
  bestCompressionStrategy(gatewayMac, pixelData, supportedFormats) {
    let bestFormat = RAWDATA_FORMATS.RAWDATA;
    let bestRatio = 1.0;
    let bestReason = 'Default format (no compression)';

    // 測試所有支援的壓縮格式
    for (const format of supportedFormats) {
      if (format === RAWDATA_FORMATS.RAWDATA) continue;

      const compressor = this.registry.getCompressor(format);
      if (compressor && compressor.isSuitableFor(pixelData)) {
        const ratio = compressor.estimateCompressionRatio(pixelData);

        if (ratio < bestRatio) {
          bestFormat = format;
          bestRatio = ratio;
          bestReason = `Best compression ratio: ${(ratio * 100).toFixed(1)}%`;
        }
      }
    }

    return {
      selectedFormat: bestFormat,
      estimatedRatio: bestRatio,
      estimatedSize: Math.ceil(pixelData.length * bestRatio),
      reason: bestReason
    };
  }

  /**
   * 最快策略：優先選擇處理速度最快的格式
   */
  fastestStrategy(gatewayMac, pixelData, supportedFormats) {
    // 簡單實作：rawdata 最快，其次是 runlendata
    const formatPriority = [
      RAWDATA_FORMATS.RAWDATA,
      RAWDATA_FORMATS.RUNLENDATA
    ];

    for (const format of formatPriority) {
      if (supportedFormats.includes(format)) {
        const compressor = this.registry.getCompressor(format);
        const ratio = compressor ? compressor.estimateCompressionRatio(pixelData) : 1.0;

        return {
          selectedFormat: format,
          estimatedRatio: ratio,
          estimatedSize: Math.ceil(pixelData.length * ratio),
          reason: `Fastest processing format: ${format}`
        };
      }
    }

    // 備用方案
    return {
      selectedFormat: RAWDATA_FORMATS.RAWDATA,
      estimatedRatio: 1.0,
      estimatedSize: pixelData.length,
      reason: 'Fallback to default format'
    };
  }

  /**
   * 平衡策略：在壓縮效果和處理速度間平衡
   */
  balancedStrategy(gatewayMac, pixelData, supportedFormats) {
    // 如果數據較小（< 1KB），直接使用 rawdata
    if (pixelData.length < 1024) {
      return {
        selectedFormat: RAWDATA_FORMATS.RAWDATA,
        estimatedRatio: 1.0,
        estimatedSize: pixelData.length,
        reason: 'Small data, no compression needed'
      };
    }

    // 測試壓縮格式，選擇壓縮比 < 0.7 的最佳格式
    let bestFormat = RAWDATA_FORMATS.RAWDATA;
    let bestRatio = 1.0;
    let bestReason = 'No suitable compression format found';

    for (const format of supportedFormats) {
      if (format === RAWDATA_FORMATS.RAWDATA) continue;

      const compressor = this.registry.getCompressor(format);
      if (compressor && compressor.isSuitableFor(pixelData)) {
        const ratio = compressor.estimateCompressionRatio(pixelData);

        // 只有在壓縮效果顯著時才使用壓縮
        if (ratio < 0.7 && ratio < bestRatio) {
          bestFormat = format;
          bestRatio = ratio;
          bestReason = `Good compression ratio: ${(ratio * 100).toFixed(1)}%`;
        }
      }
    }

    return {
      selectedFormat: bestFormat,
      estimatedRatio: bestRatio,
      estimatedSize: Math.ceil(pixelData.length * bestRatio),
      reason: bestReason
    };
  }
}

module.exports = FormatSelector;
```

### 步驟 5：主要導出接口

#### 5.1 index.js
```javascript
const { RAWDATA_FORMATS } = require('./types');
const { globalRegistry } = require('./compressors/compressionRegistry');
const RunLengthCompressor = require('./compressors/runLengthCompressor');
const FormatSelector = require('./utils/formatSelector');

// 初始化：註冊所有可用的壓縮器
function initializeCompressors() {
  // 註冊 Run-Length 壓縮器
  globalRegistry.register(new RunLengthCompressor());

  console.log('Rawdata compression module initialized');
  console.log('Supported formats:', globalRegistry.getSupportedFormats());
}

// 創建格式處理器實例
const formatProcessor = new FormatProcessor(globalRegistry);

/**
 * 處理像素數據
 * @param {string} format - 目標格式
 * @param {Uint8Array} pixelData - 像素數據
 * @returns {Object} 處理結果
 */
function processPixelData(format, pixelData) {
  return formatProcessor.processFormat(format, pixelData);
}

/**
 * 壓縮像素數據（向後兼容）
 * @param {string} format - 目標格式
 * @param {Uint8Array} pixelData - 像素數據
 * @returns {CompressionResult} 壓縮結果
 */
function compressPixelData(format, pixelData) {
  const result = formatProcessor.processFormat(format, pixelData);

  // 轉換為舊的 CompressionResult 格式
  return {
    success: result.success,
    format: result.format,
    originalSize: result.originalSize,
    compressedSize: result.processedSize,
    compressionRatio: result.processingRatio,
    processingTime: result.processingTime,
    data: result.data,
    error: result.error
  };
}

/**
 * 解壓縮數據
 * @param {string} format - 數據格式
 * @param {Uint8Array} compressedData - 壓縮數據
 * @returns {Uint8Array} 解壓縮後的數據
 */
function decompressData(format, compressedData) {
  if (format === RAWDATA_FORMATS.RAWDATA) {
    // 未壓縮數據，直接返回
    return compressedData;
  }

  const compressor = globalRegistry.getCompressor(format);
  if (!compressor) {
    throw new Error(`Unsupported format: ${format}`);
  }

  return compressor.decompress(compressedData);
}

/**
 * 檢查格式是否支援
 * @param {string} format - 格式名稱
 * @returns {boolean} 是否支援
 */
function isFormatSupported(format) {
  return formatProcessor.isFormatSupported(format);
}

// 初始化模組
initializeCompressors();

module.exports = {
  // 常數
  RAWDATA_FORMATS,

  // 主要功能
  processPixelData,
  compressPixelData,  // 向後兼容
  decompressData,
  isFormatSupported,

  // 進階功能
  globalRegistry,
  formatProcessor,

  // 壓縮器類（用於擴展）
  RunLengthCompressor
};
```
```
