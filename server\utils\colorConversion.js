/**
 * 後端簡化版顏色轉換模組
 * 提供基本的顏色轉換功能，避免複雜的 Node.js canvas 操作
 */

/**
 * 根據 colorType 獲取推薦的轉換策略
 */
function getConversionStrategy(colorType) {
  const normalizedColorType = typeof colorType === 'string'
    ? colorType.toUpperCase()
    : colorType;

  switch (normalizedColorType) {
    case 'GRAY16':
    case 'BW':
      return {
        algorithm: 'gray16',
        name: '16階灰度量化'
      };

    case 'BLACK & WHITE & RED':
    case 'BWR':
      return {
        algorithm: 'colorQuantization',
        name: '三色量化'
      };

    case 'BLACK & WHITE & RED & YELLOW':
    case 'BWRY':
      return {
        algorithm: 'colorQuantization',
        name: '四色量化'
      };

    case 'ALL COLORS':
    case 'ALL':
      return {
        algorithm: 'original',
        name: '保持原色'
      };

    default:
      console.warn(`未知的顏色類型: ${colorType}，使用黑白二值化作為默認轉換`);
      return {
        algorithm: 'blackAndWhite',
        name: '黑白二值化'
      };
  }
}

/**
 * 根據顏色類型獲取調色板
 */
function getColorPalette(colorType) {
  const normalizedColorType = typeof colorType === 'string'
    ? colorType.toUpperCase()
    : colorType;

  switch (normalizedColorType) {
    case 'BLACK & WHITE & RED':
    case 'BWR':
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 }      // 紅色
      ];

    case 'BLACK & WHITE & RED & YELLOW':
    case 'BWRY':
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 },     // 紅色
        { r: 255, g: 255, b: 0 }    // 黃色
      ];

    default:
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }  // 白色
      ];
  }
}

/**
 * 找到最接近的調色板顏色
 * 改進的算法，對電子紙顏色進行特殊處理
 */
function findClosestColor(r, g, b, palette) {
  // 對於四色電子紙（BWRY），使用特殊的顏色匹配邏輯
  if (palette.length === 4) {
    // 檢查是否為黃色（高紅色和綠色，低藍色）
    if (r > 180 && g > 180 && b < 100) {
      return { r: 255, g: 255, b: 0 }; // 黃色
    }
    // 檢查是否為紅色（高紅色，低綠色和藍色）
    if (r > 150 && g < 100 && b < 100) {
      return { r: 255, g: 0, b: 0 }; // 紅色
    }
    // 檢查是否為白色（高亮度）
    const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
    if (brightness > 128) {
      return { r: 255, g: 255, b: 255 }; // 白色
    } else {
      return { r: 0, g: 0, b: 0 }; // 黑色
    }
  }

  // 對於三色電子紙（BWR），使用特殊的顏色匹配邏輯
  if (palette.length === 3) {
    // 檢查是否為紅色（高紅色，低綠色和藍色）
    if (r > 150 && g < 100 && b < 100) {
      return { r: 255, g: 0, b: 0 }; // 紅色
    }
    // 檢查是否為白色（高亮度）
    const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
    if (brightness > 128) {
      return { r: 255, g: 255, b: 255 }; // 白色
    } else {
      return { r: 0, g: 0, b: 0 }; // 黑色
    }
  }

  // 對於其他情況，使用標準的歐幾里得距離
  let minDistance = Infinity;
  let closestColor = palette[0];

  for (const color of palette) {
    // 使用加權歐幾里得距離，對人眼敏感的綠色給予更高權重
    const distance = Math.sqrt(
      0.3 * Math.pow(r - color.r, 2) +
      0.59 * Math.pow(g - color.g, 2) +
      0.11 * Math.pow(b - color.b, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      closestColor = color;
    }
  }

  return closestColor;
}

/**
 * 實際執行顏色轉換的函數
 * 提供真正的像素級顏色轉換功能
 */
function convertImageByColorType(canvas, colorType, options = {}) {
  try {
    const strategy = getConversionStrategy(colorType);
    console.log(`後端顏色轉換 - 類型: ${colorType}, 算法: ${strategy.name}`);

    // 記錄轉換策略信息
    const conversionInfo = {
      originalColorType: colorType,
      normalizedColorType: typeof colorType === 'string' ? colorType.toUpperCase() : colorType,
      algorithm: strategy.algorithm,
      algorithmName: strategy.name
    };

    console.log('後端顏色轉換詳情:', JSON.stringify(conversionInfo, null, 2));

    // 根據算法執行實際的顏色轉換
    switch (strategy.algorithm) {
      case 'original':
        console.log('後端保持原色，不進行轉換');
        return canvas;

      case 'colorQuantization':
        const palette = getColorPalette(colorType);
        console.log(`後端使用顏色量化，調色板包含 ${palette.length} 種顏色:`, palette);
        return applyColorQuantization(canvas, palette, options);

      case 'gray16':
        console.log('後端使用16階灰度量化，提供16個灰度級別');
        return applyGray16Conversion(canvas, options);

      case 'dithering':
        console.log('後端使用抖動算法進行黑白轉換');
        return applyDitheringConversion(canvas, options);

      case 'blackAndWhite':
        console.log('後端使用黑白二值化轉換');
        return applyBlackAndWhiteConversion(canvas, options);

      default:
        console.log(`後端使用 ${strategy.name} 轉換`);
        return canvas;
    }

  } catch (error) {
    console.error('後端顏色轉換失敗:', error);
    console.error('錯誤詳情:', {
      colorType,
      options,
      errorMessage: error.message,
      errorStack: error.stack
    });
    return canvas; // 返回原始 canvas 作為後備
  }
}

/**
 * 應用顏色量化轉換（用於BWR和BWRY）
 */
function applyColorQuantization(canvas, palette, options = {}) {
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    console.error('無法獲取canvas上下文');
    return canvas;
  }

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;

  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];

    const closestColor = findClosestColor(r, g, b, palette);

    data[i] = closestColor.r;
    data[i + 1] = closestColor.g;
    data[i + 2] = closestColor.b;
    // 保持alpha通道不變
  }

  ctx.putImageData(imageData, 0, 0);
  return canvas;
}

/**
 * 應用16階灰度轉換
 */
function applyGray16Conversion(canvas, options = {}) {
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    console.error('無法獲取canvas上下文');
    return canvas;
  }

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;

  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];

    // 計算灰度值
    const gray = 0.299 * r + 0.587 * g + 0.114 * b;

    // 量化到16個級別
    const quantizedGray = Math.round(gray / 255 * 15) * 17; // 0, 17, 34, ..., 255

    data[i] = quantizedGray;
    data[i + 1] = quantizedGray;
    data[i + 2] = quantizedGray;
    // 保持alpha通道不變
  }

  ctx.putImageData(imageData, 0, 0);
  return canvas;
}

/**
 * 應用黑白二值化轉換
 */
function applyBlackAndWhiteConversion(canvas, options = {}) {
  const threshold = options.threshold || 128;
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    console.error('無法獲取canvas上下文');
    return canvas;
  }

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;

  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];

    // 計算灰度值
    const gray = 0.299 * r + 0.587 * g + 0.114 * b;

    // 二值化
    const bwValue = gray > threshold ? 255 : 0;

    data[i] = bwValue;
    data[i + 1] = bwValue;
    data[i + 2] = bwValue;
    // 保持alpha通道不變
  }

  ctx.putImageData(imageData, 0, 0);
  return canvas;
}

/**
 * 應用抖動轉換（簡化版）
 */
function applyDitheringConversion(canvas, options = {}) {
  // 簡化版抖動，直接使用黑白二值化
  return applyBlackAndWhiteConversion(canvas, options);
}

/**
 * 檢查是否應該使用顏色轉換
 */
function shouldUseColorConversion(template) {
  return template && template.color && template.color !== 'original';
}

/**
 * 獲取轉換信息（用於日誌）
 */
function getConversionInfo(colorType) {
  const strategy = getConversionStrategy(colorType);
  return {
    colorType,
    algorithm: strategy.algorithm,
    name: strategy.name
  };
}

module.exports = {
  convertImageByColorType,
  getConversionStrategy,
  getColorPalette,
  findClosestColor,
  shouldUseColorConversion,
  getConversionInfo,
  applyColorQuantization,
  applyGray16Conversion,
  applyBlackAndWhiteConversion,
  applyDitheringConversion
};
