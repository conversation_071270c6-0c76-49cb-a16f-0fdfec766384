/* 流動邊框效果 */
@keyframes flowingBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes rotateGlow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulseGlow {
  0% {
    box-shadow: 0 0 5px rgba(185, 28, 28, 0.3); /* 紅色700的透明度版本 */
  }
  50% {
    box-shadow: 0 0 15px rgba(185, 28, 28, 0.6); 
  }
  100% {
    box-shadow: 0 0 5px rgba(185, 28, 28, 0.3);
  }
}

.auto-mode-container {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  animation: pulseGlow 4s infinite;
  background-color: rgba(254, 226, 226, 0.6); /* 改為淺紅色背景 - red-100 with opacity */
}

.flowing-border {
  background: linear-gradient(90deg, 
    rgba(185, 28, 28, 0.3), /* 紅色700 */
    rgba(220, 38, 38, 0.6), /* 紅色600 */
    rgba(153, 27, 27, 0.8), /* 紅色800 */
    rgba(220, 38, 38, 0.6), /* 紅色600 */
    rgba(185, 28, 28, 0.3)  /* 紅色700 */
  );
  background-size: 300% 100%;
  animation: flowingBorder 3s ease infinite;
  z-index: 1;
  pointer-events: none;
  border-radius: 0.5rem; /* 圓角邊框 */
  border: 2px solid transparent; /* 防止邊框大小變化 */
}

.auto-mode-container:before {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, 
    rgba(220, 38, 38, 0.05), /* 紅色600 */
    rgba(185, 28, 28, 0.1),  /* 紅色700 */
    rgba(239, 68, 68, 0.05), /* 紅色500 */
    rgba(220, 38, 38, 0.05)  /* 紅色600 */
  );
  top: -50%;
  left: -50%;
  animation: rotateGlow 10s linear infinite;
  pointer-events: none;
  z-index: 0;
}
