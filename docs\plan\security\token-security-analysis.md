# EPD Manager Token 安全性分析報告

## 📋 執行摘要

本報告針對 EPD Manager 系統的 JWT Token 機制進行全面安全性分析，評估當前實作的安全風險並提供改善建議。

### 🔄 重新評估後的關鍵發現
- **開發環境**: 硬編碼密鑰僅用於開發測試，生產環境透過 Docker 配置
- **生產環境**: 需要建立自動化密鑰管理機制
- **系統靈活性**: Token 過期時間應可透過系統設定調整
- **管理便利性**: 需要 Web 介面進行密鑰管理和系統配置

---

## 🔍 當前 Token 機制分析

### 1. Token 生成與管理

#### 1.1 JWT 密鑰配置
```javascript
// 發現的問題密鑰配置
const JWT_SECRET = process.env.JWT_SECRET || 'epd-manager-jwt-secret-key';
const jwtSecret = process.env.JWT_SECRET || 'your_jwt_secret_here';
```

**安全風險**:
- 🔴 **硬編碼預設密鑰**: 多個檔案中存在不同的預設密鑰
- 🔴 **弱密鑰**: 預設密鑰過於簡單，容易被猜測
- 🟡 **密鑰不一致**: 不同模組使用不同的預設值

#### 1.2 Token 類型與過期時間
| Token 類型 | 過期時間 | 用途 | 風險等級 |
|-----------|---------|------|---------|
| 用戶 API Token | 24小時 | HTTP API 認證 | 🟡 中等 |
| WebSocket Token | 24小時 | WebSocket 連接 | 🟡 中等 |
| Gateway Token | 30天 | 網關設備認證 | 🔴 高 |

**安全風險**:
- 🔴 **Gateway Token 過期時間過長**: 30天的有效期增加了被濫用的風險
- 🟡 **無自動刷新機制**: Token 接近過期時無自動更新

### 2. Token 存儲機制

#### 2.1 前端存儲
```typescript
// localStorage 存儲 (src/store/authStore.ts)
persist(
  (set, get) => ({
    token: null, // 存儲在 localStorage
    // ...
  }),
  {
    name: 'auth-storage', // localStorage 鍵名
  }
)
```

**安全風險**:
- 🟡 **明文存儲**: Token 在 localStorage 中明文存儲
- 🟡 **XSS 攻擊風險**: JavaScript 可直接訪問 localStorage
- 🟢 **HttpOnly Cookie**: 同時使用 HttpOnly Cookie 提供額外保護

#### 2.2 後端存儲
```javascript
// Cookie 配置
const cookieOptions = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict'
};
```

**安全優點**:
- 🟢 **HttpOnly**: 防止 JavaScript 訪問
- 🟢 **SameSite**: 防止 CSRF 攻擊
- 🟢 **Secure Flag**: 生產環境使用 HTTPS

### 3. API 端點安全性

#### 3.1 Token 暴露風險
發現以下端點可能暴露敏感資訊:

1. **`/api/auth/gateway-token`** (POST)
   - 🔴 返回完整的 Gateway Token
   - 🔴 包含 WebSocket URL 和 Token 參數
   - 🟡 需要認證但可能被濫用

2. **`/api/auth/websocket-token`** (GET)
   - 🟡 返回 WebSocket Token
   - 🟢 需要有效認證

3. **Gateway API 端點**
   - 🔴 在響應中包含完整的 WebSocket Token
   - 🔴 Token 存儲在資料庫中明文形式

#### 3.2 認證中間件
```javascript
const authenticate = async (req, res, next) => {
  // 從多個來源獲取 token
  const token = getTokenFromRequest(req);
  // 驗證 token
  const decoded = verifyToken(token);
}
```

**安全優點**:
- 🟢 **多重驗證**: 支援 Header 和 Cookie 兩種方式
- 🟢 **用戶狀態檢查**: 驗證用戶是否為 active 狀態
- 🟢 **錯誤處理**: 適當的錯誤回應

---

## 🚨 主要安全風險

### 1. 🔴 高風險問題

#### 1.1 硬編碼密鑰
**問題**: 多個檔案中存在硬編碼的預設 JWT 密鑰
```javascript
// 發現的硬編碼密鑰
'epd-manager-jwt-secret-key'
'your_jwt_secret_here'
'your-super-secret-jwt-key-change-this-in-production'
```

**影響**: 
- 攻擊者可以偽造任意 Token
- 完全繞過認證機制
- 獲得系統完全控制權

#### 1.2 Gateway Token 長期有效
**問題**: Gateway Token 有效期為 30 天
**影響**:
- 設備被盜用風險
- Token 洩露後長期有效
- 難以及時撤銷訪問權限

#### 1.3 Token 在資料庫明文存儲
**問題**: Gateway WebSocket Token 在 MongoDB 中明文存儲
**影響**:
- 資料庫洩露直接暴露所有 Token
- 內部人員可直接獲取 Token
- 無法追蹤 Token 使用情況

### 2. 🟡 中風險問題

#### 2.1 前端 Token 存儲
**問題**: Token 在 localStorage 中明文存儲
**影響**:
- XSS 攻擊可竊取 Token
- 瀏覽器插件可能訪問
- 用戶端安全依賴性高

#### 2.2 Token 暴露 API
**問題**: 部分 API 端點返回完整 Token
**影響**:
- 網路監聽可能截獲 Token
- 日誌記錄可能包含敏感資訊
- 增加 Token 洩露風險

### 3. 🟢 低風險問題

#### 3.1 Token 刷新機制
**問題**: 缺乏自動 Token 刷新
**影響**:
- 用戶體驗較差
- 需要重新登入
- 可能導致服務中斷

---

## 🛡️ 安全改善建議

### 1. 立即修復 (高優先級)

#### 1.1 移除所有硬編碼密鑰
```bash
# 生成強密鑰
openssl rand -base64 64

# 環境變數設置
JWT_SECRET=<生成的強密鑰>
```

#### 1.2 統一密鑰管理
```javascript
// 建議的密鑰配置
const getJwtSecret = () => {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET environment variable is required');
  }
  if (secret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }
  return secret;
};
```

#### 1.3 縮短 Gateway Token 有效期
```javascript
// 建議配置
const TOKEN_EXPIRY = {
  USER: '2h',        // 用戶 Token: 2小時
  WEBSOCKET: '4h',   // WebSocket Token: 4小時  
  GATEWAY: '24h'     // Gateway Token: 24小時
};
```

### 2. 中期改善 (中優先級)

#### 2.1 實作 Token 加密存儲
```javascript
// 資料庫存儲加密 Token
const encryptToken = (token) => {
  const cipher = crypto.createCipher('aes-256-cbc', process.env.ENCRYPTION_KEY);
  return cipher.update(token, 'utf8', 'hex') + cipher.final('hex');
};
```

#### 2.2 增強前端安全性
```typescript
// 使用 sessionStorage 替代 localStorage
// 實作 Token 自動清理
// 添加 Token 完整性檢查
```

#### 2.3 實作 Token 撤銷機制
```javascript
// Token 黑名單
const tokenBlacklist = new Set();

const isTokenRevoked = (tokenId) => {
  return tokenBlacklist.has(tokenId);
};
```

### 3. 長期優化 (低優先級)

#### 3.1 實作 Token 自動刷新
```javascript
// 自動刷新機制
const refreshToken = async (oldToken) => {
  // 檢查 Token 是否即將過期
  // 生成新 Token
  // 無縫更新認證狀態
};
```

#### 3.2 增強監控與審計
```javascript
// Token 使用日誌
const logTokenUsage = (token, action, ip) => {
  console.log(`Token ${action}: ${tokenId} from ${ip}`);
};
```

---

## 📊 風險評估矩陣

| 風險項目 | 可能性 | 影響程度 | 風險等級 | 建議處理時間 |
|---------|-------|---------|---------|-------------|
| 硬編碼密鑰 | 高 | 極高 | 🔴 極高 | 立即 |
| Gateway Token 過期 | 中 | 高 | 🔴 高 | 1週內 |
| 明文存儲 | 中 | 高 | 🔴 高 | 2週內 |
| 前端存儲 | 中 | 中 | 🟡 中 | 1個月內 |
| API 暴露 | 低 | 中 | 🟡 中 | 1個月內 |
| 缺乏刷新 | 低 | 低 | 🟢 低 | 3個月內 |

---

## 🔧 實作檢查清單

### 立即執行
- [ ] 檢查所有環境變數是否設置強密鑰
- [ ] 移除所有硬編碼預設密鑰
- [ ] 統一所有模組的密鑰獲取方式
- [ ] 縮短 Gateway Token 有效期至 24 小時

### 短期執行 (1-2週)
- [ ] 實作 Token 加密存儲
- [ ] 添加 Token 撤銷機制
- [ ] 增強 API 端點安全性
- [ ] 實作更嚴格的 Token 驗證

### 中期執行 (1個月)
- [ ] 改善前端 Token 存儲機制
- [ ] 實作 Token 自動刷新
- [ ] 添加安全監控與日誌
- [ ] 進行安全性測試

### 長期執行 (3個月)
- [ ] 實作完整的審計系統
- [ ] 添加異常檢測機制
- [ ] 定期安全性評估
- [ ] 建立安全事件回應流程

---

## 📝 結論

EPD Manager 系統的 Token 機制存在多個嚴重的安全風險，特別是硬編碼密鑰問題需要立即解決。建議按照優先級順序逐步實施改善措施，以確保系統安全性。

**關鍵建議**:
1. **立即更換所有硬編碼密鑰**
2. **實作統一的密鑰管理機制**  
3. **縮短 Token 有效期**
4. **加強 Token 存儲安全性**
5. **建立完整的安全監控體系**

定期進行安全性評估和滲透測試，確保系統持續符合安全標準。

---

## 🔧 詳細技術實作指南

### 1. 密鑰管理強化

#### 1.1 環境變數驗證
```javascript
// server/utils/security.js
const validateJwtSecret = () => {
  const secret = process.env.JWT_SECRET;

  if (!secret) {
    throw new Error('SECURITY ERROR: JWT_SECRET environment variable is required');
  }

  if (secret.length < 64) {
    throw new Error('SECURITY ERROR: JWT_SECRET must be at least 64 characters long');
  }

  // 檢查是否為預設值
  const defaultSecrets = [
    'epd-manager-jwt-secret-key',
    'your_jwt_secret_here',
    'your-super-secret-jwt-key-change-this-in-production'
  ];

  if (defaultSecrets.includes(secret)) {
    throw new Error('SECURITY ERROR: Default JWT_SECRET detected. Please use a strong random key.');
  }

  return secret;
};

// 啟動時驗證
const JWT_SECRET = validateJwtSecret();
```

#### 1.2 密鑰輪換機制
```javascript
// server/utils/keyRotation.js
const crypto = require('crypto');

class KeyRotationManager {
  constructor() {
    this.currentKey = process.env.JWT_SECRET;
    this.previousKey = process.env.JWT_SECRET_PREVIOUS;
    this.rotationInterval = 30 * 24 * 60 * 60 * 1000; // 30天
  }

  generateNewKey() {
    return crypto.randomBytes(64).toString('base64');
  }

  rotateKey() {
    this.previousKey = this.currentKey;
    this.currentKey = this.generateNewKey();

    // 更新環境變數或配置
    console.log('Key rotation completed');
    return this.currentKey;
  }

  verifyWithBothKeys(token) {
    try {
      return jwt.verify(token, this.currentKey);
    } catch (error) {
      if (this.previousKey) {
        return jwt.verify(token, this.previousKey);
      }
      throw error;
    }
  }
}
```

### 2. Token 安全增強

#### 2.1 Token 結構優化
```javascript
// server/utils/secureTokens.js
const crypto = require('crypto');

const generateSecureToken = (payload, type = 'user') => {
  const tokenId = crypto.randomUUID();
  const issuedAt = Math.floor(Date.now() / 1000);

  const securePayload = {
    ...payload,
    jti: tokenId,        // JWT ID for tracking
    iat: issuedAt,       // Issued at
    type: type,          // Token type
    version: '1.0'       // Token version
  };

  const expiresIn = getTokenExpiry(type);

  return {
    token: jwt.sign(securePayload, JWT_SECRET, { expiresIn }),
    tokenId,
    expiresAt: issuedAt + parseExpiry(expiresIn)
  };
};

const getTokenExpiry = (type) => {
  const expiry = {
    user: '2h',
    websocket: '4h',
    gateway: '24h',
    refresh: '7d'
  };
  return expiry[type] || '1h';
};
```

#### 2.2 Token 撤銷系統
```javascript
// server/utils/tokenBlacklist.js
class TokenBlacklist {
  constructor() {
    this.blacklist = new Map(); // tokenId -> expiryTime
    this.cleanupInterval = setInterval(() => this.cleanup(), 60000); // 每分鐘清理
  }

  revoke(tokenId, expiryTime) {
    this.blacklist.set(tokenId, expiryTime);
    console.log(`Token ${tokenId} revoked`);
  }

  isRevoked(tokenId) {
    return this.blacklist.has(tokenId);
  }

  cleanup() {
    const now = Math.floor(Date.now() / 1000);
    for (const [tokenId, expiryTime] of this.blacklist.entries()) {
      if (expiryTime < now) {
        this.blacklist.delete(tokenId);
      }
    }
  }

  revokeAllUserTokens(userId) {
    // 實作撤銷特定用戶的所有 Token
    // 可以通過在資料庫中記錄撤銷時間來實現
  }
}

const tokenBlacklist = new TokenBlacklist();
```

### 3. 前端安全改善

#### 3.1 安全的 Token 存儲
```typescript
// src/utils/secureStorage.ts
class SecureTokenStorage {
  private static readonly TOKEN_KEY = 'auth_token';
  private static readonly EXPIRY_KEY = 'auth_expiry';

  static setToken(token: string, expiryTime: number): void {
    try {
      // 使用 sessionStorage 而非 localStorage
      sessionStorage.setItem(this.TOKEN_KEY, token);
      sessionStorage.setItem(this.EXPIRY_KEY, expiryTime.toString());

      // 設置自動清理
      this.scheduleCleanup(expiryTime);
    } catch (error) {
      console.error('Failed to store token:', error);
    }
  }

  static getToken(): string | null {
    try {
      const token = sessionStorage.getItem(this.TOKEN_KEY);
      const expiry = sessionStorage.getItem(this.EXPIRY_KEY);

      if (!token || !expiry) return null;

      // 檢查是否過期
      if (Date.now() > parseInt(expiry) * 1000) {
        this.clearToken();
        return null;
      }

      return token;
    } catch (error) {
      console.error('Failed to retrieve token:', error);
      return null;
    }
  }

  static clearToken(): void {
    sessionStorage.removeItem(this.TOKEN_KEY);
    sessionStorage.removeItem(this.EXPIRY_KEY);
  }

  private static scheduleCleanup(expiryTime: number): void {
    const delay = (expiryTime * 1000) - Date.now();
    if (delay > 0) {
      setTimeout(() => this.clearToken(), delay);
    }
  }
}
```

#### 3.2 Token 自動刷新
```typescript
// src/utils/tokenRefresh.ts
class TokenRefreshManager {
  private refreshTimer: NodeJS.Timeout | null = null;
  private readonly REFRESH_THRESHOLD = 5 * 60 * 1000; // 5分鐘前刷新

  scheduleRefresh(expiryTime: number): void {
    this.clearRefreshTimer();

    const refreshTime = (expiryTime * 1000) - this.REFRESH_THRESHOLD;
    const delay = refreshTime - Date.now();

    if (delay > 0) {
      this.refreshTimer = setTimeout(() => {
        this.refreshToken();
      }, delay);
    }
  }

  private async refreshToken(): Promise<void> {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        SecureTokenStorage.setToken(data.token, data.expiresAt);
        this.scheduleRefresh(data.expiresAt);
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      // 觸發重新登入
      window.location.href = '/login';
    }
  }

  private clearRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }
}
```

### 4. API 安全強化

#### 4.1 安全的認證中間件
```javascript
// server/middleware/secureAuth.js
const secureAuthenticate = async (req, res, next) => {
  try {
    const token = getTokenFromRequest(req);

    if (!token) {
      return res.status(401).json({
        error: 'Authentication required',
        code: 'NO_TOKEN'
      });
    }

    // 驗證 Token
    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({
        error: 'Invalid token',
        code: 'INVALID_TOKEN'
      });
    }

    // 檢查 Token 是否被撤銷
    if (tokenBlacklist.isRevoked(decoded.jti)) {
      return res.status(401).json({
        error: 'Token revoked',
        code: 'TOKEN_REVOKED'
      });
    }

    // 檢查 Token 類型
    if (decoded.type !== 'user') {
      return res.status(401).json({
        error: 'Invalid token type',
        code: 'WRONG_TOKEN_TYPE'
      });
    }

    // 獲取用戶並檢查狀態
    const user = await User.findById(req.db, decoded.userId);
    if (!user || user.status !== 'active') {
      return res.status(401).json({
        error: 'User not found or inactive',
        code: 'USER_INACTIVE'
      });
    }

    // 記錄訪問日誌
    logTokenUsage(decoded.jti, 'ACCESS', req.ip);

    req.user = user;
    req.tokenId = decoded.jti;
    next();

  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      error: 'Authentication service error',
      code: 'AUTH_SERVICE_ERROR'
    });
  }
};
```

#### 4.2 敏感 API 保護
```javascript
// server/routes/secureAuthApi.js
// 移除直接返回 Token 的端點，改為安全的方式

// 替代 /api/auth/gateway-token
router.post('/auth/gateway-connection', authenticate, async (req, res) => {
  try {
    const { gatewayId, storeId, macAddress } = req.body;

    // 驗證參數
    if (!gatewayId || !storeId || !macAddress) {
      return res.status(400).json({
        error: 'Missing required parameters'
      });
    }

    // 生成 Token 但不直接返回
    const { token, tokenId } = generateSecureToken({
      gatewayId,
      storeId,
      macAddress
    }, 'gateway');

    // 將 Token 存儲到安全位置（加密）
    await storeGatewayToken(gatewayId, token, tokenId);

    // 只返回連接資訊，不包含 Token
    res.json({
      connectionId: tokenId,
      wsPath: `/ws/store/${storeId}/gateway/${gatewayId}`,
      expiresIn: 24 * 60 * 60,
      message: 'Gateway connection authorized'
    });

  } catch (error) {
    console.error('Gateway token generation error:', error);
    res.status(500).json({ error: 'Service error' });
  }
});
```

### 5. 監控與審計

#### 5.1 安全事件監控
```javascript
// server/utils/securityMonitor.js
class SecurityMonitor {
  constructor() {
    this.events = [];
    this.alertThresholds = {
      failedLogins: 5,        // 5次失敗登入
      tokenAbuse: 10,         // 10次無效 Token 使用
      timeWindow: 300000      // 5分鐘時間窗口
    };
  }

  logSecurityEvent(type, details) {
    const event = {
      type,
      details,
      timestamp: Date.now(),
      ip: details.ip,
      userAgent: details.userAgent
    };

    this.events.push(event);
    this.checkAlerts(type, details.ip);

    // 記錄到檔案或資料庫
    console.log(`SECURITY EVENT: ${type}`, details);
  }

  checkAlerts(type, ip) {
    const now = Date.now();
    const windowStart = now - this.alertThresholds.timeWindow;

    const recentEvents = this.events.filter(event =>
      event.type === type &&
      event.ip === ip &&
      event.timestamp > windowStart
    );

    if (recentEvents.length >= this.alertThresholds[type]) {
      this.triggerAlert(type, ip, recentEvents.length);
    }
  }

  triggerAlert(type, ip, count) {
    console.error(`SECURITY ALERT: ${type} from ${ip} (${count} events)`);

    // 可以實作：
    // - 發送郵件通知
    // - 暫時封鎖 IP
    // - 記錄到安全日誌
    // - 觸發自動回應
  }
}

const securityMonitor = new SecurityMonitor();
```

#### 5.2 審計日誌
```javascript
// server/utils/auditLogger.js
const auditLogger = {
  logTokenEvent(action, tokenId, userId, ip, details = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      action,           // 'ISSUED', 'VERIFIED', 'REVOKED', 'EXPIRED'
      tokenId,
      userId,
      ip,
      userAgent: details.userAgent,
      success: details.success !== false,
      error: details.error,
      additionalInfo: details.info
    };

    // 寫入審計日誌檔案
    fs.appendFileSync(
      path.join(__dirname, '../logs/audit.log'),
      JSON.stringify(logEntry) + '\n'
    );

    // 也可以寫入資料庫
    // await db.collection('audit_logs').insertOne(logEntry);
  },

  logSecurityViolation(type, ip, details) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      type: 'SECURITY_VIOLATION',
      violationType: type,
      ip,
      details
    };

    fs.appendFileSync(
      path.join(__dirname, '../logs/security.log'),
      JSON.stringify(logEntry) + '\n'
    );
  }
};
```

---

## 🚀 部署安全檢查清單

### Docker 環境安全
```bash
# 1. 檢查環境變數
docker exec epd-manager env | grep JWT_SECRET

# 2. 確認沒有預設密鑰
docker exec epd-manager grep -r "your_jwt_secret_here" /app/

# 3. 檢查檔案權限
docker exec epd-manager ls -la /app/server/logs/

# 4. 驗證 HTTPS 配置
curl -I https://your-domain.com/api/health
```

### 生產環境配置
```yaml
# docker-compose.prod.yml
services:
  epd-manager:
    environment:
      - NODE_ENV=production
      - JWT_SECRET=${JWT_SECRET}
      - JWT_SECRET_PREVIOUS=${JWT_SECRET_PREVIOUS}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - SECURITY_AUDIT_ENABLED=true
    volumes:
      - ./logs:/app/server/logs:rw
      - ./audit:/app/server/audit:rw
```

### 定期安全檢查
```bash
#!/bin/bash
# security-check.sh

echo "=== EPD Manager Security Check ==="

# 檢查密鑰強度
if [ ${#JWT_SECRET} -lt 64 ]; then
    echo "❌ JWT_SECRET too short"
    exit 1
fi

# 檢查預設密鑰
if grep -q "your_jwt_secret_here" /app/server/**/*.js; then
    echo "❌ Default JWT secret found"
    exit 1
fi

# 檢查 Token 過期配置
if grep -q "30d" /app/server/**/*.js; then
    echo "⚠️  Long-lived tokens detected"
fi

echo "✅ Security check passed"
```

---

## 📞 安全事件回應流程

### 1. Token 洩露事件
1. **立即撤銷** 所有相關 Token
2. **輪換密鑰** 使所有現有 Token 失效
3. **通知用戶** 重新登入
4. **調查來源** 確定洩露原因
5. **修復漏洞** 防止再次發生

### 2. 異常訪問檢測
1. **監控指標**: 失敗登入、無效 Token、異常 IP
2. **自動回應**: 暫時封鎖、限制訪問
3. **人工審查**: 分析攻擊模式
4. **加強防護**: 更新安全規則

### 3. 緊急聯絡資訊
- **系統管理員**: <EMAIL>
- **安全團隊**: <EMAIL>
- **緊急電話**: +886-xxx-xxx-xxx

---

## 📚 相關文件

- [JWT 最佳實踐指南](https://tools.ietf.org/html/rfc7519)
- [OWASP Token 安全指南](https://owasp.org/www-project-cheat-sheets/cheatsheets/JSON_Web_Token_for_Java_Cheat_Sheet.html)
- [Node.js 安全最佳實踐](https://nodejs.org/en/docs/guides/security/)

---

**最後更新**: 2024年12月19日
**版本**: 1.0
**審查者**: 系統安全團隊
