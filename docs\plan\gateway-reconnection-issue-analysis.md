# Gateway 當機重連問題分析與解決方案

## 問題描述

當 gateway 當機且沒有正常關閉 WebSocket 連接時，如果 gateway 在短時間內重新連接到 server，會出現以下問題：
- Gateway 實際已連接並能正常回應
- 但前端顯示 gateway 狀態仍為離線 (offline)
- 造成用戶誤判 gateway 連接狀態

## 問題根本原因分析

**問題位置**: `server/services/websocketService.js` 第 144-145 行

```javascript
// 存儲連接
connectedGateways.set(gatewayId, ws);
```

**問題分析**:
- 當 gateway 當機時，舊的 WebSocket 連接可能沒有觸發 `close` 事件
- 新連接建立時直接覆蓋 `connectedGateways` Map 中的舊連接
- 舊連接沒有被正確清理，可能導致後續的心跳檢測或其他邏輯仍然操作舊連接
- 沒有檢查是否已存在相同 gatewayId 的連接

**核心問題**: 在心跳檢測清理舊連接之前（最多需要60秒），新連接已經建立，但舊連接仍然存在於系統中，可能導致狀態混亂。

## 解決方案：連接建立時主動清理舊連接

**修改位置**: `server/services/websocketService.js` 的 `handleConnection` 函數

**具體修改**:
```javascript
const handleConnection = async (ws, req) => {
  try {
    const { gatewayId, storeId, macAddress } = req;
    console.log(`網關 ${gatewayId} (門店: ${storeId}, MAC: ${macAddress}) 已連接`);

    // === 新增: 檢查並清理舊連接 ===
    const existingConnection = connectedGateways.get(gatewayId);
    if (existingConnection) {
      console.log(`檢測到網關 ${gatewayId} 的舊連接，正在清理...`);

      // 強制關閉舊連接
      try {
        existingConnection.terminate();
      } catch (error) {
        console.error(`關閉舊連接失敗:`, error);
      }

      // 記錄舊連接被替換的事件
      logGatewayEvent(gatewayId, 'connection-replaced', {
        reason: 'new_connection_established',
        oldConnectionTime: existingConnection.connectionTime,
        newConnectionTime: Date.now()
      });
    }
    // === 新增結束 ===

    // 將 gatewayId、storeId 和 macAddress 附加到 WebSocket 實例
    ws.gatewayId = gatewayId;
    ws.storeId = storeId;
    ws.macAddress = macAddress;
    ws.isAlive = true;
    ws.connectionTime = Date.now();
    ws.lastActivityTime = Date.now();

    // 存儲連接
    connectedGateways.set(gatewayId, ws);

    // 更新網關狀態為在線
    await updateGatewayStatus(gatewayId, 'online');

    // 其餘邏輯保持不變...
  } catch (error) {
    console.error('處理 WebSocket 連接時發生錯誤:', error);
    ws.close(1011, 'Server error');
  }
};
```

## 測試驗證方案

### 測試場景 1: 模擬 Gateway 當機重連
1. 啟動 Gateway 連接
2. 強制終止 Gateway 進程 (模擬當機)
3. 在心跳檢測清理之前重新啟動 Gateway
4. 驗證連接狀態是否正確顯示為 online

### 測試場景 2: 快速重連測試
1. 建立 Gateway 連接
2. 快速斷開並重新連接 (間隔 < 5 秒)
3. 重複多次
4. 驗證每次重連後狀態都正確

### 測試場景 3: 並發連接測試
1. 同時啟動多個相同 gatewayId 的連接嘗試
2. 驗證只有一個連接成功，其他被正確拒絕或清理

## 實施步驟 ✅ 已完成

**文件**: `server/services/websocketService.js`
**修改位置**: `handleConnection` 函數 (第 131-157 行)

已在現有的 `handleConnection` 函數開頭添加舊連接清理邏輯：

```javascript
const handleConnection = async (ws, req) => {
  try {
    const { gatewayId, storeId, macAddress } = req;
    console.log(`網關 ${gatewayId} (門店: ${storeId}, MAC: ${macAddress}) 已連接`);

    // === 新增: 檢查並清理舊連接 ===
    const existingConnection = connectedGateways.get(gatewayId);
    if (existingConnection) {
      console.log(`檢測到網關 ${gatewayId} 的舊連接，正在清理...`);

      // 強制關閉舊連接
      try {
        existingConnection.terminate();
      } catch (error) {
        console.error(`關閉舊連接失敗:`, error);
      }

      // 記錄舊連接被替換的事件 (可選)
      logGatewayEvent(gatewayId, 'connection-replaced', {
        reason: 'new_connection_established',
        oldConnectionTime: existingConnection.connectionTime,
        newConnectionTime: Date.now()
      });
    }
    // === 新增結束 ===

    // 原有邏輯保持不變
    ws.gatewayId = gatewayId;
    ws.storeId = storeId;
    ws.macAddress = macAddress;
    ws.isAlive = true;
    ws.connectionTime = Date.now();
    ws.lastActivityTime = Date.now();

    // 存儲連接
    connectedGateways.set(gatewayId, ws);

    // 更新網關狀態為在線
    await updateGatewayStatus(gatewayId, 'online');

    // 其餘邏輯保持不變...
  } catch (error) {
    console.error('處理 WebSocket 連接時發生錯誤:', error);
    ws.close(1011, 'Server error');
  }
};
```

## 使用現有測試工具驗證

可以使用現有的測試客戶端來驗證修改效果：

```bash
# 使用現有的測試客戶端
node server/tests/test-ws-client-interactive.js [gatewayId] [storeId]

# 測試步驟:
# 1. 建立連接
# 2. 強制終止進程 (Ctrl+C) 模擬當機
# 3. 立即重新啟動
# 4. 檢查前端顯示的連接狀態是否正確為 online
```

## 風險評估

### 低風險
- 修改邏輯簡單，只在連接建立時檢查舊連接
- 不影響現有的心跳檢測和其他機制
- 不增加額外的定時器或後台任務

### 緩解措施
- 在測試環境充分驗證
- 保留詳細的日誌記錄
- 準備回滾方案

## 總結

這個最簡化的解決方案：

1. **只修改一個地方**: 在 `handleConnection` 函數開頭添加舊連接清理邏輯
2. **不增加服務器負擔**: 沒有額外的定時器或後台任務
3. **解決核心問題**: 確保新連接建立時舊連接被立即清理
4. **保持簡潔**: 不改變現有的心跳檢測和其他機制

**預期效果**: 當 gateway 當機重連時，新連接會立即清理舊連接並更新狀態為 online，前端將正確顯示連接狀態。

## 實施結果 ✅ 已完成

修改已成功完成！具體變更如下：

### 第一次修改 - 連接建立時清理舊連接
**位置**: `handleConnection` 函數開頭（第 136-157 行）
- 當檢測到相同 `gatewayId` 的舊連接時，會立即強制終止舊連接
- 記錄連接替換事件到日誌中，便於問題追蹤

### 第二次修改 - 修復 close 事件時序問題 🔧
**位置**: `close` 事件處理器（第 205-216 行）
- **問題**: 當 gateway 斷電重連時，舊連接的 `close` 事件可能延遲觸發，導致新連接建立後又被設為 offline
- **解決**: 在 `close` 事件中檢查當前 `connectedGateways` 中是否還是同一個連接
- **邏輯**: 只有當前活躍連接才執行狀態更新，舊連接關閉不影響新連接狀態

**修改代碼**:
```javascript
// 處理關閉連接
ws.on('close', async (code, reason) => {
  console.log(`網關 ${gatewayId} 已斷開連接，代碼: ${code}, 原因: ${reason || '未提供'}`);

  // === 修改: 檢查是否為當前活躍連接 ===
  const currentConnection = connectedGateways.get(gatewayId);
  if (currentConnection === ws) {
    // 只有當前連接才執行清理和狀態更新
    connectedGateways.delete(gatewayId);
    await updateGatewayStatus(gatewayId, 'offline');
    console.log(`已清理網關 ${gatewayId} 的連接並設為離線`);
  } else {
    // 如果不是當前連接，說明已經有新連接替代了
    console.log(`網關 ${gatewayId} 的舊連接關閉，但已有新連接，不更新狀態`);
  }

  // 記錄網關離線事件
  logGatewayEvent(gatewayId, 'disconnect', { code, reason });
});
```

**解決的問題**:
- ✅ Gateway 當機重連後顯示離線的問題
- ✅ 舊連接沒有被及時清理的問題
- ✅ 連接狀態不一致的問題
- ✅ close 事件時序導致的狀態覆蓋問題

**下一步**:
可以使用現有的測試工具驗證修改效果：
```bash
node server/tests/test-ws-client-interactive.js [gatewayId] [storeId]
```
