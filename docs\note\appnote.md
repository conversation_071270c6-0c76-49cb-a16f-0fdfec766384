npx expo -v
npx expo prebuild --clean
$env:JAVA_HOME = "C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-*********-hotspot\"

$env:Path = "$env:JAVA_HOME\bin;$env:Path"

cd android; .\gradlew --stop; cd ..

npm run android

====================

dbeb8a8664b929373874fb913b8140f1a7b11991

# 1. 確保你目前在自己的分支
git checkout feature-xyz

# 2. 把最新的 main 抓下來
git fetch origin

# 3. 再把最新 main 合併進來（避免合併回去時衝突）
git merge origin/main

# 4. 解完衝突後測試沒問題，再切回 main 合併
git checkout main
git pull origin main
git merge feature-xyz
git push origin main

================
# Android SDK 路徑設定
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
local.properties