import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { PermissionAssignment } from './PermissionAssignment';
import { Pagination } from '../ui/pagination';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../ui/table';
import { Button } from '../ui/button';
import { Checkbox } from '../ui/checkbox';
import { Badge } from '../ui/badge';
import { Pencil, Trash2 } from 'lucide-react';
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from '../ui/alert-dialog';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { PermissionForm } from './PermissionForm';

interface PermissionListProps {
  permissions: PermissionAssignment[];
  loading: boolean;
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
  onPageChange: (page: number) => void;
  onDelete: (id: string) => Promise<void>;
  onBatchDelete: (ids: string[]) => Promise<void>;
}

export const PermissionList: React.FC<PermissionListProps> = ({
  permissions,
  loading,
  pagination,
  onPageChange,
  onDelete,
  onBatchDelete
}) => {
  const { t } = useTranslation();
  
  // 狀態
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isBatchDeleteDialogOpen, setIsBatchDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentPermission, setCurrentPermission] = useState<PermissionAssignment | null>(null);
  
  // 處理選擇
  const handleSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds(prev => [...prev, id]);
    } else {
      setSelectedIds(prev => prev.filter(item => item !== id));
    }
  };
  
  // 處理全選
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(permissions.map(p => p._id));
    } else {
      setSelectedIds([]);
    }
  };
  
  // 打開編輯對話框
  const openEditDialog = (permission: PermissionAssignment) => {
    setCurrentPermission(permission);
    setIsEditDialogOpen(true);
  };
  
  // 打開刪除對話框
  const openDeleteDialog = (permission: PermissionAssignment) => {
    setCurrentPermission(permission);
    setIsDeleteDialogOpen(true);
  };
  
  // 處理刪除
  const handleDelete = async () => {
    if (currentPermission) {
      await onDelete(currentPermission._id);
      setIsDeleteDialogOpen(false);
      setCurrentPermission(null);
    }
  };
  
  // 處理批量刪除
  const handleBatchDelete = async () => {
    await onBatchDelete(selectedIds);
    setIsBatchDeleteDialogOpen(false);
    setSelectedIds([]);
  };
  
  return (
    <div>
      {/* 批量操作工具欄 */}
      {selectedIds.length > 0 && (
        <div className="flex items-center justify-between bg-gray-50 p-2 rounded-md mb-2">
          <span className="text-sm">
            {t('common.selected')}: {selectedIds.length}
          </span>
          <Button 
            variant="destructive" 
            size="sm" 
            onClick={() => setIsBatchDeleteDialogOpen(true)}
          >
            {t('permission.batchDelete')}
          </Button>
        </div>
      )}
      
      {/* 權限分配表格 */}
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox 
                  checked={permissions.length > 0 && selectedIds.length === permissions.length}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>{t('permission.username')}</TableHead>
              <TableHead>{t('permission.name')}</TableHead>
              <TableHead>{t('permission.role')}</TableHead>
              <TableHead>{t('permission.scopeType')}</TableHead>
              <TableHead>{t('permission.scope')}</TableHead>
              <TableHead className="w-24">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                  </div>
                  <p className="mt-2 text-sm text-gray-500">{t('common.loading')}</p>
                </TableCell>
              </TableRow>
            ) : permissions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <p className="text-gray-500">{t('common.noData')}</p>
                </TableCell>
              </TableRow>
            ) : (
              permissions.map(permission => (
                <TableRow key={permission._id}>
                  <TableCell>
                    <Checkbox 
                      checked={selectedIds.includes(permission._id)}
                      onCheckedChange={(checked) => handleSelect(permission._id, !!checked)}
                    />
                  </TableCell>
                  <TableCell className="font-medium">{permission.user?.username}</TableCell>
                  <TableCell>{permission.user?.name}</TableCell>
                  <TableCell>{permission.role?.name}</TableCell>
                  <TableCell>
                    <Badge variant={permission.scopeType === 'system' ? 'info' : 'success'}>
                      {permission.scopeType === 'system' ? t('permission.system') : t('permission.store')}
                    </Badge>
                  </TableCell>
                  <TableCell>{permission.scope}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="icon" onClick={() => openEditDialog(permission)}>
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => openDeleteDialog(permission)}>
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* 分頁 */}
      {!loading && permissions.length > 0 && (
        <div className="flex justify-between items-center mt-4">
          <div className="text-sm text-gray-500">
            {t('common.showing')} {(pagination.page - 1) * pagination.limit + 1} {t('common.to')}{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} {t('common.of')}{' '}
            {pagination.total} {t('common.entries')}
          </div>
          
          <Pagination
            currentPage={pagination.page}
            totalPages={Math.ceil(pagination.total / pagination.limit)}
            onPageChange={onPageChange}
          />
        </div>
      )}
      
      {/* 編輯權限分配對話框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('permission.editPermission')}</DialogTitle>
          </DialogHeader>
          {currentPermission && (
            <PermissionForm 
              permissionId={currentPermission._id}
              initialData={{
                userId: currentPermission.userId,
                roleId: currentPermission.roleId,
                scope: currentPermission.scope,
                scopeType: currentPermission.scopeType
              }}
              onClose={() => {
                setIsEditDialogOpen(false);
                setCurrentPermission(null);
              }}
              onSuccess={() => {
                setIsEditDialogOpen(false);
                setCurrentPermission(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>
      
      {/* 刪除權限分配確認對話框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('permission.confirmDelete')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('permission.confirmDeletePermission')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-500 hover:bg-red-600">
              {t('common.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* 批量刪除權限分配確認對話框 */}
      <AlertDialog open={isBatchDeleteDialogOpen} onOpenChange={setIsBatchDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('permission.confirmDelete')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('common.confirmDeleteMultiple', { count: selectedIds.length })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleBatchDelete} className="bg-red-500 hover:bg-red-600">
              {t('common.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
