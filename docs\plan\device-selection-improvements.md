# 設備管理頁面選取功能改進

## 問題描述

設備管理頁面的選取功能存在兩個主要問題：

1. **無法跨頁選取**：原本的全選功能只能選取當前頁的設備，無法實現跨頁選取
2. **自動刷新下選取會被刷新取消**：在即時更新時選取狀態容易丟失

## 用戶需求澄清

經過與用戶確認，選取功能的期望行為是：
- **全選按鈕**：控制當前頁的全部選項，不是直接控制所有跨頁項目
- **跨頁選取**：用戶可以在不同頁面間保持選取狀態，並提供額外的"全選所有"按鈕來實現跨頁全選

## 解決方案

### 1. 雙層選取機制

#### 全選按鈕（當前頁）
```typescript
// 選中當前頁的所有設備
const currentPageItems = getCurrentPageItems().map(device => device._id || '').filter(id => id !== '');
// 合併當前頁的項目到已選取的項目中
setSelectedItems(prev => {
  const newItems = [...prev];
  currentPageItems.forEach(id => {
    if (!newItems.includes(id)) {
      newItems.push(id);
    }
  });
  return newItems;
});
```

#### 全選所有按鈕（跨頁）
```typescript
// 全選所有過濾後的設備
const allFilteredDeviceIds = filteredDevices.map(device => device._id || '').filter(id => id !== '');
setSelectedItems(allFilteredDeviceIds);
```

### 2. 選取狀態保持機制

#### 智能全選狀態管理
```typescript
// 監控選取狀態變化，自動更新全選狀態（只檢查當前頁）
useEffect(() => {
  const currentPageItems = getCurrentPageItems();
  if (currentPageItems.length === 0) {
    setSelectAll(false);
    return;
  }

  // 檢查當前頁的所有設備是否都被選中
  const currentPageIds = currentPageItems.map(device => device._id || '').filter(id => id !== '');
  const allCurrentPageSelected = currentPageIds.length > 0 &&
    currentPageIds.every(id => selectedItems.includes(id));

  setSelectAll(allCurrentPageSelected);
}, [selectedItems, currentPage, filteredDevices]);
```

#### 改進篩選時的選取保持
```typescript
// 保持有效的選取狀態（只保留在篩選結果中的項目）
if (selectedItems.length > 0) {
  const filteredDeviceIds = result.map(device => device._id || '').filter(id => id !== '');
  const validSelectedItems = selectedItems.filter(id => filteredDeviceIds.includes(id));
  
  if (validSelectedItems.length !== selectedItems.length) {
    console.log(`[篩選選取保持] 保持有效選取項目: ${selectedItems.length} -> ${validSelectedItems.length}`);
    setSelectedItems(validSelectedItems);
  }
}
```

### 3. 用戶界面改進

#### 雙層按鈕設計
- **全選按鈕（表頭）**：控制當前頁的選取，工具提示為 "全選當前頁" / "取消當前頁全選"
- **全選所有按鈕（操作區）**：控制所有過濾結果的選取，紫色背景，工具提示為 "全選所有設備 (跨頁)" / "取消全選所有設備"

#### 選取狀態顯示
- 在分頁控制區域顯示當前選取的設備數量
- 當選取數量超過單頁顯示數量時，顯示 "(跨頁選取)" 提示
- 在全選按鈕下方顯示選取狀態：當頁/跨頁

#### 批量操作按鈕改進
- 顯示選取數量：如 "批量發送 (5)" 、"刪除 (3)"
- 沒有選取項目時按鈕禁用並顯示提示

## 技術實現細節

### 1. 狀態管理
- `selectedItems`: 存儲所有選中設備的ID數組
- `selectAll`: 控制全選複選框的狀態
- 通過 useEffect 自動同步兩者的狀態

### 2. 選取保持邏輯
- 在設備狀態更新時，過濾掉不存在的設備ID
- 在篩選條件變更時，保持在篩選結果中的選取項目
- 在設備刪除時，自動清理被刪除設備的選取狀態

### 3. 用戶體驗優化
- 清晰的視覺反饋顯示選取狀態
- 跨頁選取的明確提示
- 選取狀態在各種操作下的持久性

## 測試要點

1. **跨頁選取測試**
   - 點擊全選，驗證是否選中所有設備
   - 切換頁面，驗證選取狀態是否保持
   - 取消全選，驗證是否清空所有選取

2. **篩選條件下的選取測試**
   - 選取部分設備後應用篩選條件
   - 驗證只有符合篩選條件的選取項目被保留

3. **自動刷新下的選取保持測試**
   - 選取設備後等待自動刷新
   - 驗證選取狀態是否正確保持

4. **批量操作測試**
   - 跨頁選取設備後執行批量發送
   - 跨頁選取設備後執行批量刪除
   - 驗證操作是否正確應用到所有選中設備

## 應用範圍

此改進已應用到以下頁面：

### 1. 設備管理頁面 (DevicesPage.tsx)
- ✅ 跨頁選取功能
- ✅ 選取狀態保持機制
- ✅ 批量操作按鈕改進
- ✅ 選取狀態顯示
- ✅ 統一分頁顯示區塊

### 2. 網關管理頁面 (GatewaysPage.tsx)
- ✅ 跨頁選取功能
- ✅ 選取狀態保持機制
- ✅ 批量操作按鈕改進
- ✅ 選取狀態顯示
- ✅ 統一分頁顯示區塊

### 3. 門店資料管理頁面 (DatabasePage.tsx)
- ✅ 跨頁選取功能
- ✅ 選取狀態保持機制
- ✅ 批量操作按鈕改進
- ✅ 選取狀態顯示
- ✅ 統一分頁顯示區塊
- ✅ 新增分頁功能（原本沒有分頁）

### 4. 系統資料管理頁面 (SystemSpecificDataPage.tsx)
- ✅ 跨頁選取功能
- ✅ 選取狀態保持機制
- ✅ 批量操作按鈕改進
- ✅ 選取狀態顯示
- ✅ 統一分頁顯示區塊
- ✅ 新增分頁功能（原本沒有分頁）

### 5. 其他頁面
- 🔄 模板管理頁面 (TemplateList.tsx) - 待改進

## 兼容性說明

此修改完全向後兼容，不會影響現有的功能：
- 單頁選取功能仍然正常工作
- 批量操作API調用方式不變
- 現有的選取狀態保持邏輯得到增強而非替換

## 統一的改進模式

所有頁面都遵循相同的改進模式：

1. **雙層選取機制**：
   - 全選按鈕控制當前頁選取
   - "全選所有"按鈕實現跨頁全選

2. **智能狀態管理**：
   - 使用 useEffect 自動同步全選狀態
   - 只檢查當前頁的選取狀態來控制全選按鈕

3. **篩選保持**：
   - 在篩選條件變更時保持有效的選取項目
   - 重置分頁到第一頁

4. **視覺反饋**：
   - 顯示選取數量和跨頁選取提示
   - 全選按鈕下方顯示選取狀態（當頁/跨頁）

5. **按鈕改進**：
   - 批量操作按鈕顯示選取數量
   - 空選取時按鈕禁用並顯示提示
   - 新增紫色"全選所有"按鈕

6. **統一分頁顯示**：
   - 顯示當前頁範圍和總數
   - 顯示選取狀態信息
   - 統一的分頁控制按鈕
