// tests/fileApi.test.js
const request = require('supertest');
const express = require('express');
const createMockFileApi = require('./helpers/mockFileApi');

// 模擬 multer
jest.mock('multer', () => {
  const multerMock = jest.fn().mockReturnValue({
    single: jest.fn().mockReturnValue((req, res, next) => {
      req.file = { filename: 'mock-file.png', originalname: 'test-file.png' };
      next();
    })
  });

  // 添加 memoryStorage 方法
  multerMock.memoryStorage = jest.fn().mockReturnValue({});

  return multerMock;
});

describe('檔案 API 測試', () => {
  let app;

  beforeEach(() => {
    // 創建 Express 應用
    app = express();

    // 創建模擬的檔案 API 路由
    const router = createMockFileApi();

    // 使用模擬路由
    app.use(express.json());
    app.use('/api', router);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/upload', () => {
    test('應該能夠上傳檔案', async () => {
      const response = await request(app)
        .post('/api/upload')
        .attach('file', Buffer.from('mock file content'), 'test-file.png');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('filename');
    });
  });

  describe('GET /api/images', () => {
    test('應該返回所有圖片列表', async () => {
      // 模擬 fs 的 readdir 函數
      const fs = require('fs');
      const originalReaddir = fs.readdir;
      fs.readdir = jest.fn().mockImplementation((_, callback) => {
        callback(null, ['image1.png', 'image2.jpg']);
      });

      const response = await request(app).get('/api/images');

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
      expect(response.body).toContain('image1.png');
      expect(response.body).toContain('image2.jpg');

      // 恢復原始函數
      fs.readdir = originalReaddir;
    });

    test('應該處理獲取圖片列表時的錯誤', async () => {
      // 模擬 fs 的 readdir 函數出錯
      const fs = require('fs');
      const originalReaddir = fs.readdir;
      fs.readdir = jest.fn().mockImplementation((_, callback) => {
        callback(new Error('讀取目錄錯誤'), null);
      });

      const response = await request(app).get('/api/images');

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error');

      // 恢復原始函數
      fs.readdir = originalReaddir;
    });
  });
});
