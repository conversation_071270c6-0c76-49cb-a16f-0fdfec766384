# Bin檔案格式化工具

這個工具用於將原始bin檔案加上設備類型、功能類型、版本信息和校驗和，生成符合系統要求的格式化bin檔案。

## 功能特點

- 支援多種設備類型（Gateway、EPD等）
- 支援多種功能類型（WiFi、BLE等）
- 版本信息驗證（x.x.x.x格式）
- 自動生成CRC32校驗和
- 設備功能支援驗證
- 自動生成帶時間戳的輸出檔案

## 檔案格式規範

生成的bin檔案格式如下：

```
[2 bytes] 設備類型 (little endian)
[2 bytes] 裝置編號 (little endian)
[2 bytes] 功能類型 (little endian)
[4 bytes] 韌體版本信息 (little endian)
[4 bytes] 最小硬體版本 (little endian)
[4 bytes] 最大硬體版本 (little endian)
[N bytes] 原始bin檔案內容
[4 bytes] CRC32校驗和 (little endian)
```

### 設備類型代碼
- 0: Gateway
- 1: EPD
- (可擴充其他設備類型)

### 裝置編號代碼
- Gateway:
  - 0: GW-001
- EPD:
  - 0: EPD-001
- (可擴充其他裝置型號)

### 功能類型代碼
- 0: WiFi
- 1: BLE
- (可擴充其他功能類型)

### 設備功能支援表
- Gateway: 支援 WiFi、BLE
- EPD: 僅支援 BLE

## 使用方法

### 命令行使用

```bash
cd tools/bin-formatter
python bin_formatter.py
```

然後按照提示輸入：
1. bin檔案路徑
2. 設備類型 (gateway/epd)
3. 裝置編號 (0=GW-001/EPD-001等)
4. 功能類型 (wifi/ble)
5. 韌體版本信息 (x.x.x.x格式)
6. 最小硬體版本 (x.x.x.x格式，可選)
7. 最大硬體版本 (x.x.x.x格式，可選)

### 程式化使用

```python
from bin_formatter import BinFormatter

formatter = BinFormatter()

# 格式化bin檔案
output_path = formatter.format_bin_file(
    bin_path="path/to/original.bin",
    device_type="gateway",
    function_type="wifi",
    version="1.0.0.0",
    device_model=0,  # GW-001型號
    min_hw_version="1.0.0.0",  # 最小硬體版本
    max_hw_version="2.0.0.0"   # 最大硬體版本
)

print(f"輸出檔案: {output_path}")

# 獲取裝置型號名稱
model_name = formatter.get_device_model_name("gateway", 0)
print(f"裝置型號: {model_name}")  # 輸出: GW-001
```

## 輸出檔案

- 輸出目錄: `tools/bin-formatter/output/`
- 檔案命名格式: `{設備類型}_{功能類型}_{版本}_{時間戳}.bin`
- 例如: `gateway_wifi_1.0.0.0_20241220_143022.bin`
- 檔案內容包含裝置編號信息，可通過解析工具獲取型號名稱

## 錯誤處理

工具會驗證以下項目：
- bin檔案是否存在
- 設備類型是否支援
- 裝置編號是否有效
- 功能類型是否支援
- 設備是否支援指定功能
- 韌體版本格式是否正確
- 硬體版本格式是否正確

## 擴充說明

### 新增設備類型

在 `DEVICE_TYPES` 字典中新增：
```python
DEVICE_TYPES = {
    'gateway': 0,
    'epd': 1,
    'new_device': 2  # 新增設備類型
}
```

### 新增裝置型號

在 `DEVICE_MODELS` 字典中新增：
```python
DEVICE_MODELS = {
    'gateway': {
        0: 'GW-001',
        1: 'GW-002'  # 新增型號
    },
    'epd': {
        0: 'EPD-001',
        1: 'EPD-002'  # 新增型號
    }
}
```

### 新增功能類型

在 `FUNCTION_TYPES` 字典中新增：
```python
FUNCTION_TYPES = {
    'wifi': 0,
    'ble': 1,
    'new_function': 2  # 新增功能類型
}
```

### 更新設備功能支援

在 `DEVICE_FUNCTION_SUPPORT` 字典中更新：
```python
DEVICE_FUNCTION_SUPPORT = {
    'gateway': ['wifi', 'ble'],
    'epd': ['ble'],
    'new_device': ['new_function']  # 新增設備的支援功能
}
```

## 依賴項目

- Python 3.6+
- 標準庫模組：os, struct, hashlib, re, datetime, typing, zlib

無需安裝額外套件。
