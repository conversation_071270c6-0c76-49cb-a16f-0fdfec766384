# 設備數據綁定服務

## 概述

設備數據綁定服務是一個專門用於處理設備數據綁定更新的工具函數集合，它提供了一致的方式來更新設備的數據綁定關係，並確保在更新後通過 WebSocket 通知網關更新設備顯示。

## 功能特點

1. **統一的數據綁定更新**：提供統一的 API 來更新設備的數據綁定關係
2. **事件記錄**：自動記錄數據綁定變更事件，便於追蹤和審計
3. **WebSocket 通知**：自動通過 WebSocket 通知網關更新設備顯示
4. **向下兼容**：支持舊版本的單一數據 ID 綁定方式

## 架構設計

### 服務組件

設備數據綁定服務由以下組件組成：

1. **後端服務**：`deviceBindingService.js`
   - 提供核心的數據綁定更新功能
   - 記錄數據綁定變更事件
   - 通過 WebSocket 通知網關更新設備顯示

2. **API 端點**：`/devices/:id/update-data-bindings`
   - 提供 RESTful API 接口，供前端調用

3. **前端工具函數**：`updateDeviceDataBindings`
   - 提供前端調用 API 的統一接口

### 數據流程

1. 前端調用 `updateDeviceDataBindings` 函數，傳入設備 ID、綁定數據和門店 ID
2. 前端函數發送 HTTP 請求到 `/devices/:id/update-data-bindings` API 端點
3. API 端點調用 `deviceBindingService.updateDeviceDataBindings` 函數處理請求
4. 服務函數更新數據庫中的設備數據綁定關係
5. 服務函數記錄數據綁定變更事件
6. 服務函數通過 WebSocket 通知網關更新設備顯示
7. API 端點返回更新後的設備數據
8. 前端函數返回更新後的設備數據

## API 說明

### 後端服務 API

```javascript
/**
 * 更新設備數據綁定
 * @param {string} deviceId 設備ID
 * @param {Object} bindingData 綁定數據
 * @param {string} bindingData.templateId 模板ID
 * @param {Object|string} bindingData.dataBindings 數據綁定關係 (對象或JSON字符串)
 * @param {string} [bindingData.dataId] 向下兼容的單一數據ID
 * @param {string} [userId] 執行操作的用戶ID
 * @param {string} [storeId] 門店ID
 * @param {Object} [options] 選項
 * @param {boolean} [options.sendToGateway=true] 是否自動發送到網關
 * @returns {Promise<Object>} 更新後的設備數據
 */
updateDeviceDataBindings(deviceId, bindingData, userId, storeId, options)
```

### 前端 API

```typescript
/**
 * 更新設備數據綁定
 * @param deviceId 設備ID
 * @param bindingData 綁定數據
 * @param storeId 門店ID (可選)
 * @param options 選項 (可選)
 * @param options.sendToGateway 是否自動發送到網關 (默認為 true)
 * @returns Promise<Device> 更新後的設備
 */
updateDeviceDataBindings(
  deviceId: string,
  bindingData: {
    templateId?: string;
    dataBindings?: Record<string, string> | string;
    dataId?: string;
  },
  storeId?: string,
  options?: {
    sendToGateway?: boolean;
  }
): Promise<Device>
```

## 使用示例

### 前端使用示例

```typescript
// 更新設備數據綁定，並自動發送到網關
try {
  const updatedDevice = await updateDeviceDataBindings(
    deviceId,
    {
      templateId: 'template123',
      dataBindings: {
        'default': 'data123',
        'field1': 'data456',
        'field2': 'data789'
      },
      dataId: 'data123' // 向下兼容
    },
    storeId
  );

  console.log('設備數據綁定更新成功:', updatedDevice);
} catch (error) {
  console.error('設備數據綁定更新失敗:', error);
}

// 更新設備數據綁定，但不發送到網關
try {
  const updatedDevice = await updateDeviceDataBindings(
    deviceId,
    {
      templateId: 'template123',
      dataBindings: {
        'default': 'data123',
        'field1': 'data456',
        'field2': 'data789'
      },
      dataId: 'data123' // 向下兼容
    },
    storeId,
    { sendToGateway: false } // 不發送到網關
  );

  console.log('設備數據綁定更新成功 (未發送到網關):', updatedDevice);
} catch (error) {
  console.error('設備數據綁定更新失敗:', error);
}
```

### 後端使用示例

```javascript
// 更新設備數據綁定，並自動發送到網關
try {
  const updatedDevice = await deviceBindingService.updateDeviceDataBindings(
    deviceId,
    {
      templateId: 'template123',
      dataBindings: {
        'default': 'data123',
        'field1': 'data456',
        'field2': 'data789'
      },
      dataId: 'data123' // 向下兼容
    },
    userId,
    storeId
  );

  console.log('設備數據綁定更新成功:', updatedDevice);
} catch (error) {
  console.error('設備數據綁定更新失敗:', error);
}

// 更新設備數據綁定，但不發送到網關
try {
  const updatedDevice = await deviceBindingService.updateDeviceDataBindings(
    deviceId,
    {
      templateId: 'template123',
      dataBindings: {
        'default': 'data123',
        'field1': 'data456',
        'field2': 'data789'
      },
      dataId: 'data123' // 向下兼容
    },
    userId,
    storeId,
    { sendToGateway: false } // 不發送到網關
  );

  console.log('設備數據綁定更新成功 (未發送到網關):', updatedDevice);
} catch (error) {
  console.error('設備數據綁定更新失敗:', error);
}
```

## 事件記錄

設備數據綁定服務會自動記錄數據綁定變更事件，事件類型為 `data_binding_changed`，事件數據包含以下字段：

```javascript
{
  action: 'update_data_bindings',
  changes: {
    templateId: {
      old: '舊模板ID',
      new: '新模板ID'
    },
    dataId: {
      old: '舊數據ID',
      new: '新數據ID'
    },
    dataBindings: {
      updated: true
    }
  },
  userId: '操作用戶ID',
  userName: '操作用戶名稱',
  userEmail: '操作用戶郵箱'
}
```

## WebSocket 通知

設備數據綁定服務會自動將數據綁定轉換為預覽圖片，然後通過 WebSocket 發送到網關，消息格式如下：

```javascript
{
  type: 'update_preview',
  deviceMac: '設備MAC地址',
  imageData: '預覽圖片數據（Base64編碼）',
  imageCode: '圖片校驗碼',
  timestamp: '時間戳'
}
```

這種方式確保了網關收到的是已經渲染好的圖片，而不是原始的數據綁定信息，從而簡化了網關的處理邏輯，並確保顯示效果的一致性。

## 用戶界面

在設備數據綁定的用戶界面中，添加了一個「立即更新顯示」的選項，讓用戶可以選擇是否將數據綁定更新發送到網關：

```html
<div className="flex items-center mb-4">
  <input
    type="checkbox"
    id="updateDisplay"
    checked={formData.updateDisplay}
    onChange={(e) => setFormData({
      ...formData,
      updateDisplay: e.target.checked
    })}
    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
  />
  <label htmlFor="updateDisplay" className="ml-2 block text-sm text-gray-700">
    立即更新顯示 (將數據綁定更新發送到網關)
  </label>
</div>
```

這個選項默認為選中狀態，表示默認會將數據綁定更新發送到網關。如果用戶取消選中，則只會更新數據庫中的數據綁定關係，不會發送到網關。

## 總結

設備數據綁定服務提供了一個統一、可靠的方式來更新設備的數據綁定關係，並可以根據需要選擇是否通過 WebSocket 通知網關更新設備顯示。它簡化了設備數據綁定的管理，提高了代碼的可維護性和可擴展性，同時也提供了更多的靈活性，讓用戶可以根據需要選擇是否立即更新顯示。

## Todo
需要注意的是，由於預覽圖的生成需要前端渲染組件，服務端無法直接生成預覽圖。因此，如果設備沒有預覽圖，系統會拋出一個錯誤，提示需要先在前端生成預覽圖。在實際應用中，您可能需要考慮使用 headless browser 或其他服務端渲染方案來解決這個問題。

