# 設備管理綁定數據預覽功能修復記錄

## 問題概述

在設備管理功能中，當用戶嘗試綁定數據並預覽效果時，預覽圖無法正確顯示綁定的文字內容。這導致用戶無法直觀地確認綁定結果是否符合預期，影響了使用體驗。

### 問題表現

1. 在設備管理頁面中，點擊「綁定數據」後，選擇模板和數據欄位，預覽區域無法正確顯示綁定後的效果
2. 控制台日誌中出現「正在處理門店ID: 高雄分店商品A」的訊息，顯示系統錯誤地將商品ID當作門店ID使用
3. 預覽圖中的文字內容與實際綁定的數據不符，通常顯示為預設文字或空白

## 原因分析

經過代碼檢查和調試，發現問題主要出在數據綁定和預覽生成的處理邏輯上：

1. 在 `previewUtils.ts` 中的 `processTextBindings` 函數中，系統錯誤地將欄位ID (fieldId) 當作門店ID (storeId) 使用，導致無法找到正確的數據項
2. 在 `PreviewComponent.tsx` 中，數據項的查找邏輯優先順序不正確，沒有優先使用 uid 作為主要識別符
3. 在 `BindDeviceDataModal.tsx` 中，下拉選單的標籤沒有包含門店信息，使用戶難以區分不同門店的數據

這些問題導致系統在生成預覽圖時無法正確找到和顯示綁定的數據內容。

## 解決方案

針對上述問題，我們進行了以下修改：

### 1. 修正 previewUtils.ts 中的數據處理邏輯

- 更新 `processTextBindings` 函數，確保正確區分和使用門店ID與欄位ID
- 改進數據項查找邏輯，當 dataIndex 超出範圍時，使用更合理的備選方案
- 增加詳細的日誌輸出，便於追蹤數據處理流程

### 2. 優化 PreviewComponent.tsx 中的數據查找方式

- 調整數據項查找的優先順序，先使用 uid，再使用 id 和 _id
- 改進門店ID的設置邏輯，確保使用正確的門店ID而非商品ID
- 增強文本內容設置邏輯，處理欄位ID與數據鍵不直接匹配的情況

### 3. 改進 BindDeviceDataModal.tsx 中的用戶界面

- 在下拉選單中添加門店名稱作為前綴，幫助用戶識別數據所屬的門店
- 更新數據項的顯示邏輯，優先顯示更有意義的信息

## 代碼修改

### 1. previewUtils.ts 的關鍵修改

```typescript
// 修改前：日誌輸出不包含欄位ID信息
console.log(`正在處理門店ID: ${selectedStoreId}`);

// 修改後：增加欄位ID信息，便於調試
console.log(`正在處理門店ID: ${selectedStoreId}, 欄位ID: ${fieldId}`);
```

數據查找邏輯的改進：

```typescript
// 修改前：錯誤地使用欄位ID查找數據項
const matchingItem = storeSpecificData.find(item =>
  (item.id === fieldId) ||
  (item.uid === fieldId) ||
  (item._id === fieldId)
);

// 修改後：當索引超出範圍時，使用第一個可用的數據項
console.log(`dataIndex=${dataIndex} 超出範圍，嘗試查找任何可用的數據項`);
dataItem = storeSpecificData[0];
```

### 2. PreviewComponent.tsx 的關鍵修改

數據項查找邏輯的優化：

```typescript
// 修改前：查找順序不合理
const storeItem = storeData.find(item =>
  item.id === dataId ||
  item.uid === dataId ||
  (item._id && item._id.toString() === dataId)
);

// 修改後：優先使用uid作為匹配條件
const storeItem = storeData.find(item =>
  item.uid === dataId ||
  item.id === dataId ||
  (item._id && item._id.toString() === dataId)
);

// 增加詳細日誌
console.log(`查找數據ID為 ${dataId} 的門店數據項:`, storeItem ? {
  id: storeItem.id,
  uid: storeItem.uid,
  _id: storeItem._id,
  storeId: storeItem.storeId,
  _storeName: storeItem._storeName
} : '未找到');
```

門店ID設置邏輯的改進：

```typescript
// 修改前：直接使用id作為門店ID
elementDiv.setAttribute('data-store-id', storeItemsByDataIndex[dataIndex].id);

// 修改後：優先使用storeId屬性，如果沒有再使用id
if (storeItemsByDataIndex[dataIndex].storeId) {
  elementDiv.setAttribute('data-store-id', storeItemsByDataIndex[dataIndex].storeId);
  console.log(`設置元素 ${index} 的門店ID為: ${storeItemsByDataIndex[dataIndex].storeId}`);
} 
else if (storeItemsByDataIndex[dataIndex].id) {
  elementDiv.setAttribute('data-store-id', storeItemsByDataIndex[dataIndex].id);
  console.log(`設置元素 ${index} 的門店ID為: ${storeItemsByDataIndex[dataIndex].id} (從id屬性)`);
}
```

### 3. BindDeviceDataModal.tsx 的關鍵修改

下拉選單標籤的優化：

```typescript
// 修改前：標籤不包含門店信息
let label = '';
if (data.id) {
  label = data.id;
} else if (data.name) {
  label = data.name;
} else if (data.uid) {
  label = `數據 ${data.uid.substring(0, 8)}`;
} else {
  label = '未命名';
}

// 修改後：添加門店名稱作為前綴
let label = '';
const storePrefix = data._storeName ? `${data._storeName} - ` : '';

if (data.name) {
  label = `${storePrefix}${data.name}`;
} else if (data.id) {
  label = `${storePrefix}${data.id}`;
} else if (data.uid) {
  label = `${storePrefix}數據 ${data.uid.substring(0, 8)}`;
} else {
  label = `${storePrefix}未命名`;
}
```

## 測試驗證

為確保問題已被修復，我們進行了以下測試：

1. 在設備管理頁面選擇一個設備，點擊「綁定數據」按鈕
2. 選擇一個模板，並為模板中的數據欄位綁定不同的數據項
3. 檢查預覽圖是否正確顯示綁定的文字內容
4. 切換不同的數據項，確認預覽圖能即時更新
5. 檢查控制台日誌，確認「正在處理門店ID」的訊息顯示的是正確的門店ID
6. 測試不同門店的數據，確保系統能正確處理跨門店的數據綁定

測試結果表明，修改後的代碼能夠正確處理門店ID和數據ID，預覽圖能夠準確顯示綁定的文字內容。

## 總結

此次修復解決了設備管理中數據綁定預覽功能的問題，主要通過改進門店ID和數據ID的處理邏輯，以及優化用戶界面來實現。這些改進不僅修復了當前的問題，也提高了代碼的可維護性和系統的穩定性。

## 相關文件

- `src/utils/previewUtils.ts`
- `src/components/PreviewComponent.tsx`
- `src/components/BindDeviceDataModal.tsx`
