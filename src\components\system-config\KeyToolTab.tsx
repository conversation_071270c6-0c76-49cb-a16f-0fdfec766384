import React from 'react';
import { useTranslation } from 'react-i18next';
import { Key, RefreshCw, Download } from 'lucide-react';

export function KeyToolTab() {
  const { t } = useTranslation();
  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">{t('systemConfig.keyTool')}</h2>
      <div className="space-y-6">
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Key className="w-5 h-5 text-blue-600" />
              <span className="font-medium">{t('systemConfig.apiKey')}</span>
            </div>
            <div className="flex items-center gap-2">
              <button className="p-2 hover:bg-gray-200 rounded-lg">
                <RefreshCw className="w-4 h-4" />
              </button>
              <button className="p-2 hover:bg-gray-200 rounded-lg">
                <Download className="w-4 h-4" />
              </button>
            </div>
          </div>
          <div className="mt-2">
            <input
              type="text"
              className="w-full bg-white rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value="sk_test_51ABC123..."
              readOnly
            />
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Key className="w-5 h-5 text-green-600" />
              <span className="font-medium">{t('systemConfig.secretKey')}</span>
            </div>
            <div className="flex items-center gap-2">
              <button className="p-2 hover:bg-gray-200 rounded-lg">
                <RefreshCw className="w-4 h-4" />
              </button>
              <button className="p-2 hover:bg-gray-200 rounded-lg">
                <Download className="w-4 h-4" />
              </button>
            </div>
          </div>
          <div className="mt-2">
            <input
              type="text"
              className="w-full bg-white rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value="sk_live_51XYZ789..."
              readOnly
            />
          </div>
        </div>
      </div>
    </div>
  );
}