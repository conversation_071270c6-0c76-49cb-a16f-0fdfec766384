import { DisplayColorType } from '../types';
import { getScreenConfigs } from '../screens/screenSizeMap';

// 屏幕尺寸接口定義
export interface ScreenConfig {
  id: string;
  name: string;
  displayName: string;
  width: number;
  height: number;
  supportedColors: DisplayColorType[];
}

// 使用從 screenSizeMap 中獲取的配置
// 這樣就不需要在新增螢幕尺寸時修改這個檔案
export const screenConfigs: ScreenConfig[] = getScreenConfigs();

// 獲取屏幕配置通過ID
export const getScreenConfigById = (id: string): ScreenConfig | undefined => {
  return screenConfigs.find(screen => screen.id === id);
};

// 獲取屏幕尺寸的格式化字符串，如 "296x128"
export const getScreenDimensions = (screenId: string): string => {
  const screen = getScreenConfigById(screenId);
  if (screen) {
    return `${screen.width}x${screen.height}`;
  }
  return '';
};

// 從顯示名稱中解析屏幕ID
export const getScreenIdFromDisplayName = (displayName: string): string | undefined => {
  const screen = screenConfigs.find(s => s.displayName === displayName);
  return screen?.id;
};

// 從尺寸字符串中獲取屏幕配置 (如 "296x128")
export const getScreenConfigFromDimensions = (dimensions: string): ScreenConfig | undefined => {
  const [width, height] = dimensions.split('x').map(Number);
  return screenConfigs.find(
    screen => screen.width === width && screen.height === height
  );
};

// 解析螢幕尺寸，從"2.9" (296*128)"格式轉換為"296x128"
export const parseScreenSizeString = (screenSizeStr: string): string => {
  const match = screenSizeStr.match(/\((\d+)[*x](\d+)\)/);
  if (match && match[1] && match[2]) {
    return `${match[1]}x${match[2]}`;
  }
  return screenSizeStr; // 如果不能解析，返回原字符串
};

// 轉換方向為內部格式
export const parseOrientation = (orientationStr: string): string => {
  if (orientationStr.includes('90°') || orientationStr.includes('270°')) {
    return 'portrait';
  }
  return 'landscape'; // 默認和0°/180°都使用橫向
};