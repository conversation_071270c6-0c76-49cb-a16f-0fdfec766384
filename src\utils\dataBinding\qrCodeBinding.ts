import { DataField, DataFieldType, TemplateElement, BindingInfo } from '../../types';
import { bindingCore } from './bindingCore';

/**
 * QR Code 資料綁定處理器
 * 負責處理 QR Code 元件的資料綁定邏輯
 */
export class QRCodeBinding {
  /**
   * 獲取 QR Code 元件可綁定的資料欄位
   * 根據 QR Code 類型過濾適用的欄位
   * @param fields 所有資料欄位
   * @param qrCodeType QR Code 類型
   * @returns 可綁定的資料欄位
   */
  public static getBindableFields(fields: DataField[], qrCodeType?: string): DataField[] {
    // 根據 QR Code 類型只顯示對應的特定類型
    const supportedTypes: DataFieldType[] = [];

    if (qrCodeType === 'datamatrix') {
      supportedTypes.push(DataFieldType.QR_CODE_DATAMATRIX);
    } else if (qrCodeType === 'pdf417') {
      supportedTypes.push(DataFieldType.QR_CODE_PDF417);
    } else {
      // 預設為 qrcode 類型
      supportedTypes.push(DataFieldType.QR_CODE);
    }

    return fields.filter(field =>
      supportedTypes.includes(field.type as DataFieldType)
    );
  }

  /**
   * 處理 QR Code 元件的綁定和內容驗證
   * @param element 元素配置
   * @param fields 資料欄位定義
   * @param sampleData 範例數據
   * @returns 處理後的元素
   */
  public static processBinding(
    element: TemplateElement,
    fields: DataField[],
    sampleData?: Record<string, any>
  ): TemplateElement {
    // 如果沒有資料綁定，直接返回原元素
    if (!element.dataBinding || !element.dataBinding.fieldId) {
      return element;
    }

    // 獲取綁定的資料欄位
    const field = fields.find(f => f.id === element.dataBinding!.fieldId);
    if (!field) {
      console.warn(`QR Code 綁定: 找不到欄位 ${element.dataBinding.fieldId}`);
      return element;
    }

    // 檢查欄位類型是否相容
    const bindableFields = this.getBindableFields(fields, element.qrCodeType);
    if (!bindableFields.some(f => f.id === field.id)) {
      console.warn(`QR Code 綁定: 欄位類型 ${field.type} 與 QR Code 類型 ${element.qrCodeType} 不相容`);
      return element;
    }

    // 如果有範例數據，驗證內容
    if (sampleData && sampleData[field.id]) {
      const content = String(sampleData[field.id]);
      const validation = this.validateContent(content, element.qrCodeType || 'qrcode');
      
      if (!validation.isValid) {
        console.warn(`QR Code 綁定: 內容驗證失敗 - ${validation.error}`);
      }
    }

    return element;
  }

  /**
   * 驗證 QR Code 內容格式
   * @param content 要驗證的內容
   * @param qrCodeType QR Code 類型
   * @returns 驗證結果
   */
  public static validateContent(content: string, qrCodeType: string): {
    isValid: boolean;
    error?: string;
    sanitizedContent?: string;
  } {
    if (!content || typeof content !== 'string') {
      return {
        isValid: false,
        error: '內容不能為空'
      };
    }

    // QR Code 驗證規則
    const QR_CODE_LIMITS: Record<string, { maxLength: number; charset: string }> = {
      qrcode: { maxLength: 4296, charset: 'all' },
      datamatrix: { maxLength: 3116, charset: 'ascii' },
      pdf417: { maxLength: 2710, charset: 'all' }
    };

    const limits = QR_CODE_LIMITS[qrCodeType] || QR_CODE_LIMITS.qrcode;

    // 長度檢查
    if (content.length > limits.maxLength) {
      return {
        isValid: false,
        error: `內容長度超過限制 (${limits.maxLength} 字符)`
      };
    }

    // 字符集檢查
    if (limits.charset === 'ascii') {
      // ASCII 字符檢查
      if (!/^[\x00-\x7F]*$/.test(content)) {
        return {
          isValid: false,
          error: '僅支援 ASCII 字符',
          sanitizedContent: content.replace(/[^\x00-\x7F]/g, '?')
        };
      }
    }

    return {
      isValid: true,
      sanitizedContent: content
    };
  }

  /**
   * 獲取 QR Code 類型的顯示名稱
   * @param qrCodeType QR Code 類型
   * @returns 顯示名稱
   */
  public static getQRCodeTypeDisplayName(qrCodeType?: string): string {
    switch (qrCodeType) {
      case 'datamatrix': return 'Data Matrix';
      case 'pdf417': return 'PDF417';
      case 'qrcode':
      default: return 'QR Code';
    }
  }

  /**
   * 獲取支援的 QR Code 類型列表
   * @returns QR Code 類型選項
   */
  public static getSupportedQRCodeTypes(): Array<{ value: string; label: string; maxLength: number }> {
    return [
      { value: 'qrcode', label: 'QR Code', maxLength: 4296 },
      { value: 'datamatrix', label: 'Data Matrix', maxLength: 3116 },
      { value: 'pdf417', label: 'PDF417', maxLength: 2710 }
    ];
  }

  /**
   * 檢查欄位類型是否與 QR Code 類型相容
   * @param fieldType 欄位類型
   * @param qrCodeType QR Code 類型
   * @returns 是否相容
   */
  public static isFieldTypeCompatible(fieldType: string, qrCodeType?: string): boolean {
    const baseTypes = [
      DataFieldType.TEXT,
      DataFieldType.UNIQUE_IDENTIFIER,
      DataFieldType.QR_CODE
    ];

    // 檢查基本類型
    if (baseTypes.includes(fieldType as DataFieldType)) {
      return true;
    }

    // 檢查特定類型
    switch (qrCodeType) {
      case 'datamatrix':
        return fieldType === DataFieldType.QR_CODE_DATAMATRIX;
      case 'pdf417':
        return fieldType === DataFieldType.QR_CODE_PDF417;
      default:
        return false;
    }
  }

  /**
   * 根據欄位類型推薦 QR Code 類型
   * @param fieldType 欄位類型
   * @returns 推薦的 QR Code 類型
   */
  public static getRecommendedQRCodeType(fieldType: string): string {
    switch (fieldType) {
      case DataFieldType.QR_CODE_DATAMATRIX:
        return 'datamatrix';
      case DataFieldType.QR_CODE_PDF417:
        return 'pdf417';
      case DataFieldType.QR_CODE:
      case DataFieldType.TEXT:
      case DataFieldType.UNIQUE_IDENTIFIER:
      default:
        return 'qrcode';
    }
  }

  /**
   * 生成範例內容
   * @param qrCodeType QR Code 類型
   * @returns 範例內容
   */
  public static generateSampleContent(qrCodeType?: string): string {
    switch (qrCodeType) {
      case 'datamatrix':
        return 'DM123456789';
      case 'pdf417':
        return 'PDF417-SAMPLE-DATA';
      case 'qrcode':
      default:
        return 'https://example.com/qr-sample';
    }
  }

  /**
   * 為 QR Code 元件設置綁定並進行預處理
   * @param element QR Code 元件
   * @param dataIndex 數據索引
   * @param fieldId 欄位ID
   * @param selectedStoreId 預覽門店數據的選擇門店ID
   * @returns 處理後的 QR Code 元件
   */
  public static setBinding(
    element: TemplateElement,
    dataIndex: number,
    fieldId: string | null,
    selectedStoreId: string | null = null
  ): TemplateElement {
    // 保留現有的 selectedStoreId，如果沒有新值則使用現有值
    const currentStoreId = element.dataBinding?.selectedStoreId;
    const storeId = selectedStoreId !== null ? selectedStoreId : currentStoreId;

    // 使用綁定核心建立綁定信息
    const updatedElement = bindingCore.bindElementToField(element, dataIndex, fieldId);

    // 如果有綁定信息且有門店ID，才添加門店ID
    if (updatedElement.dataBinding && (storeId || selectedStoreId === null)) {
      return {
        ...updatedElement,
        dataBinding: {
          ...updatedElement.dataBinding,
          selectedStoreId: storeId
        }
      };
    }

    return updatedElement;
  }

  /**
   * 移除 QR Code 元件的資料綁定
   * @param element QR Code 元件
   * @returns 移除綁定後的元件
   */
  public static removeBinding(element: TemplateElement): TemplateElement {
    return bindingCore.unbindElement(element);
  }
}
