# 批量發送等待機制改進

## 概述

針對用戶反饋的問題：「日誌中可以看出，列隊循環使用的是無等待循環，這樣會造成如果發送時間較長，後面的等待循環會全部失敗」，我們實現了智能等待機制，當所有網關都忙碌時等待任一網關變為閒置後再觸發下一次循環。

## 問題分析

### 原有問題
1. **無等待循環**：任務隊列循環是連續的，當所有網關忙碌時會快速重試並失敗
2. **資源浪費**：大量無效的重試循環消耗系統資源
3. **任務失敗**：網關忙碌時任務會快速達到重試上限並失敗

### 日誌示例
```
[1] 🔄 任務隊列循環 2，剩餘任務: 1，正在處理: 1
[1] 🤖 設備 684926e7f210fc4c110fe961 智能模式 - 檢查主要網關 684147c661586764d12bfc45 狀態
[1] ⚠️ 主要網關 684147c661586764d12bfc45 忙碌，尋找備用網關
[1] 🔍 網關 684147c661586764d12bfc45 狀態檢查: 在線=true, 忙碌=true, 活躍傳輸數=3
[1] ❌ 沒有可用的備用網關，任務需要重新排隊
[1] 📊 任務 684926e7f210fc4c110fe961 處理完成，結果: 失敗 (可重試)
[1] 🔄 任務 684926e7f210fc4c110fe961 重新排隊 (重試 1/3): 網關忙碌或離線，任務重新排隊
```

## 解決方案

### 1. 網關狀態變化事件系統

**實現位置**: `server/services/websocketService.js`

```javascript
// 網關狀態變化事件發射器
const gatewayStatusEmitter = new EventEmitter();

// 修改 endChunkTransmission 函數，當網關變為閒置時發射事件
const endChunkTransmission = (gatewayId, chunkId) => {
  const gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
  if (gatewayTransmissions && gatewayTransmissions.has(chunkId)) {
    const wasBusy = gatewayTransmissions.size > 0;
    gatewayTransmissions.delete(chunkId);
    
    // 如果沒有任何傳輸了，清理網關記錄並發射事件
    if (gatewayTransmissions.size === 0) {
      activeChunkTransmissions.delete(gatewayId);
      
      // 如果網關從忙碌變為閒置，發射狀態變化事件
      if (wasBusy && isGatewayOnline(gatewayId)) {
        console.log(`🔔 網關 ${gatewayId} 從忙碌變為閒置，發射狀態變化事件`);
        gatewayStatusEmitter.emit('gatewayAvailable', gatewayId);
      }
    }
  }
};
```

### 2. 等待網關可用函數

```javascript
// 等待任一網關變為可用
const waitForAnyGatewayAvailable = (gatewayIds, maxWaitTime = 30000) => {
  return new Promise((resolve, reject) => {
    // 首先檢查是否已經有可用的網關
    const availableGateways = getAvailableGateways(gatewayIds);
    if (availableGateways.length > 0) {
      resolve(availableGateways[0]);
      return;
    }

    const timeout = setTimeout(() => {
      gatewayStatusEmitter.removeListener('gatewayAvailable', onGatewayAvailable);
      reject(new Error(`等待網關可用超時 (${maxWaitTime}ms)`));
    }, maxWaitTime);

    const onGatewayAvailable = (availableGatewayId) => {
      if (gatewayIds.includes(availableGatewayId)) {
        clearTimeout(timeout);
        gatewayStatusEmitter.removeListener('gatewayAvailable', onGatewayAvailable);
        resolve(availableGatewayId);
      }
    };

    gatewayStatusEmitter.on('gatewayAvailable', onGatewayAvailable);
  });
};
```

### 3. 智能任務隊列循環

**實現位置**: `server/services/sendPreviewToGateway.js`

#### 任務可處理性檢查
```javascript
// 檢查任務是否可以立即處理（有可用網關）
const checkTaskCanProcess = async (task) => {
  // 獲取設備信息
  const device = await deviceCollection.findOne({ _id: safeObjectId(task.deviceId) });
  
  if (device.gatewaySelectionMode === 'auto') {
    // 智能模式：檢查主要網關或備用網關是否可用
    const isPrimaryAvailable = websocketService.isGatewayOnline(primaryGatewayId) && 
                              !websocketService.isGatewayBusyWithChunk(primaryGatewayId);
    
    if (isPrimaryAvailable) return true;
    
    // 檢查備用網關
    const availableGateways = websocketService.getAvailableGateways(allGatewayIds);
    const backupGateway = availableGateways.find(gw => gw !== primaryGatewayId);
    return !!backupGateway;
  } else {
    // 非智能模式：只檢查主要網關
    return websocketService.isGatewayOnline(primaryGatewayId) && 
           !websocketService.isGatewayBusyWithChunk(primaryGatewayId);
  }
};
```

#### 智能循環邏輯
```javascript
while (taskQueue.length > 0 && queueCycles < maxQueueCycles) {
  // 檢查是否有可以立即處理的任務
  let foundProcessableTask = false;
  let taskIndex = 0;
  
  while (taskIndex < taskQueue.length && !foundProcessableTask) {
    const task = taskQueue[taskIndex];
    const canProcess = await checkTaskCanProcess(task);
    
    if (canProcess) {
      // 找到可以處理的任務，從隊列中移除並處理
      taskQueue.splice(taskIndex, 1);
      foundProcessableTask = true;
      processTask(task);
    } else {
      taskIndex++;
    }
  }
  
  // 如果沒有找到可以處理的任務，等待網關變為可用
  if (!foundProcessableTask && taskQueue.length > 0) {
    // 收集所有任務涉及的網關ID
    const allGatewayIds = collectAllGatewayIds(taskQueue);
    
    try {
      // 等待任一網關變為可用，最多等待10秒
      await websocketService.waitForAnyGatewayAvailable(allGatewayIds, 10000);
    } catch (waitError) {
      // 等待超時，繼續下一次循環
    }
  }
}
```

## 功能特點

### 1. 智能等待機制
- **事件驅動**：基於網關狀態變化事件，而非輪詢
- **精確觸發**：只有當網關從忙碌變為閒置時才觸發
- **超時保護**：設置最大等待時間，防止無限等待

### 2. 任務優先級處理
- **可處理性檢查**：優先處理有可用網關的任務
- **動態調整**：根據網關狀態動態調整任務處理順序
- **避免餓死**：確保所有任務都有機會被處理

### 3. 錯誤處理和保護機制
- **超時保護**：防止因網關錯誤導致循環卡住
- **最大等待次數**：限制等待循環次數，避免無限等待
- **異常恢復**：網關錯誤時自動清理狀態並繼續處理

## 性能優化

### 1. 減少無效重試
- **智能判斷**：只有在有可用網關時才處理任務
- **事件觸發**：避免連續的狀態檢查循環
- **資源節約**：減少CPU和網絡資源消耗

### 2. 提高成功率
- **等待機制**：給網關足夠時間完成當前任務
- **智能選擇**：優先使用可用的備用網關
- **重試優化**：減少因網關忙碌導致的任務失敗

## 配置參數

```javascript
const maxWaitCycles = 10;        // 最大等待循環次數
const waitTimeout = 10000;       // 單次等待超時時間 (10秒)
const maxQueueCycles = 50;       // 最大隊列循環次數
```

## 測試驗證

### 測試場景
1. **所有網關忙碌**：驗證等待機制是否正常工作
2. **部分網關忙碌**：驗證智能選擇是否生效
3. **網關錯誤**：驗證超時保護是否有效

### 預期結果
- 減少因網關忙碌導致的任務失敗
- 提高批量發送的整體成功率
- 優化系統資源使用效率

## 總結

新的等待機制完全解決了原有的問題：

1. ✅ **智能等待**：當所有網關忙碌時等待狀態變化
2. ✅ **事件驅動**：基於網關狀態變化事件觸發
3. ✅ **超時保護**：防止因網關錯誤導致循環卡住
4. ✅ **資源優化**：減少無效的重試循環
5. ✅ **成功率提升**：給網關足夠時間完成任務

這個實現完全符合用戶的需求：「當所有網關都忙碌時，等待任一網關變為閒置後再觸發下一次循環」。
