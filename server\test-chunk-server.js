// 測試分片傳輸功能的簡化服務器
const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const { URL } = require('url');

const app = express();
const server = http.createServer(app);
const port = 3001;
const jwtSecret = 'your_jwt_secret_here';

// 存儲Gateway能力信息
const gatewayCapabilities = new Map(); // macAddress -> chunkingSupport

// 用於存儲連接的網關
const connectedGateways = new Map();

// 模擬數據
const mockStores = [
  { id: 'store1', name: '測試門店1' },
  { id: 'store2', name: '測試門店2' }
];

const mockGateways = [
  { _id: 'gateway1', name: '測試網關1', macAddress: '7D:14:A7:7C:DA:34', storeId: 'store1' },
  { _id: 'gateway2', name: '測試網關2', macAddress: 'AA:BB:CC:DD:EE:FF', storeId: 'store2' }
];

app.use(express.json());

// CORS 中間件
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');

  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});

// API 路由
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  if (username === 'root' && password === '123456789') {
    const token = jwt.sign({ userId: 'admin', username: 'root' }, jwtSecret, { expiresIn: '24h' });
    res.json({ token, message: '登入成功' });
  } else {
    res.status(401).json({ error: '用戶名或密碼錯誤' });
  }
});

app.get('/api/stores', (req, res) => {
  res.json(mockStores);
});

app.get('/api/gateways', (req, res) => {
  const { storeId } = req.query;
  const gateways = storeId ? mockGateways.filter(g => g.storeId === storeId) : mockGateways;
  res.json(gateways);
});

app.post('/api/gateways', (req, res) => {
  const newGateway = {
    _id: `gateway_${Date.now()}`,
    ...req.body,
    createdAt: new Date()
  };
  mockGateways.push(newGateway);
  res.json(newGateway);
});

// 生成隨機MAC地址
function generateRandomMac() {
  const hexDigits = "0123456789ABCDEF";
  let mac = "";
  for (let i = 0; i < 6; i++) {
    let part = "";
    for (let j = 0; j < 2; j++) {
      part += hexDigits.charAt(Math.floor(Math.random() * 16));
    }
    mac += part;
    if (i < 5) mac += ":";
  }
  return mac;
}

// 生成隨機IP地址
function generateRandomIp() {
  return `192.168.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`;
}

// WebSocket 服務器
const wss = new WebSocket.Server({ 
  server,
  path: /^\/ws\/store\/(.+)\/gateway\/(.+)$/
});

wss.on('connection', (ws, req) => {
  const url = new URL(req.url, `http://${req.headers.host}`);
  const pathMatch = req.url.match(/^\/ws\/store\/(.+)\/gateway\/(.+)$/);
  
  if (!pathMatch) {
    console.error('無效的 WebSocket 路徑');
    ws.close(1008, '無效的路徑');
    return;
  }

  const storeId = pathMatch[1];
  const gatewayId = pathMatch[2];
  const token = url.searchParams.get('token');

  console.log(`WebSocket 連接請求: store=${storeId}, gateway=${gatewayId}`);

  // 驗證 token
  try {
    const decoded = jwt.verify(token, jwtSecret);
    ws.gatewayId = gatewayId;
    ws.storeId = storeId;
    ws.macAddress = decoded.macAddress || generateRandomMac(); // 從token中獲取MAC地址
    ws.isAlive = true;
    ws.lastActivityTime = Date.now();

    connectedGateways.set(gatewayId, ws);
    console.log(`網關 ${gatewayId} 已連接，MAC: ${ws.macAddress}`);

    // 發送歡迎消息
    ws.send(JSON.stringify({
      type: 'welcome',
      message: 'WebSocket 連接成功',
      timestamp: Date.now(),
      gatewayInfo: {
        gatewayId: gatewayId,
        storeId: storeId,
        macAddress: ws.macAddress
      }
    }));

  } catch (error) {
    console.error('Token 驗證失敗:', error);
    ws.close(1008, 'Token 無效');
    return;
  }

  // 處理消息
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      console.log(`收到來自網關 ${gatewayId} 的消息:`, data.type);

      switch (data.type) {
        case 'ping':
          ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
          break;

        case 'gatewayInfo':
          handleGatewayInfo(ws, data);
          break;

        case 'deviceStatus':
          console.log(`收到設備狀態更新，設備數量: ${data.devices?.length || 0}`);
          break;

        default:
          console.warn(`未知消息類型: ${data.type}`);
      }
    } catch (error) {
      console.error('解析消息失敗:', error);
    }
  });

  ws.on('close', () => {
    console.log(`網關 ${gatewayId} 斷開連接`);
    connectedGateways.delete(gatewayId);
  });

  ws.on('error', (error) => {
    console.error(`網關 ${gatewayId} WebSocket 錯誤:`, error);
  });
});

// 處理網關信息
function handleGatewayInfo(ws, data) {
  const info = data.info || {};
  const gatewayMac = ws.macAddress;

  console.log(`處理網關 ${ws.gatewayId} 的信息更新`);

  // 處理Gateway能力上報
  if (info.chunkingSupport) {
    const chunkingSupport = info.chunkingSupport;
    gatewayCapabilities.set(gatewayMac, chunkingSupport);
    console.log(`Gateway ${gatewayMac} 分片能力:`, {
      enabled: chunkingSupport.enabled,
      maxChunkSize: chunkingSupport.maxChunkSize,
      maxSingleMessageSize: chunkingSupport.maxSingleMessageSize,
      embeddedIndexSupport: chunkingSupport.embeddedIndex
    });
  } else {
    console.log(`Gateway ${gatewayMac} 未上報分片能力，視為不支援`);
  }

  // 發送確認回應
  ws.send(JSON.stringify({
    type: 'gatewayInfoAck',
    timestamp: Date.now(),
    success: true,
    message: '網關信息更新成功',
    serverCapabilities: {
      embeddedIndexChunking: true,
      maxImageSize: 1024 * 1024,
      supportedFormats: ['rawdata']
    }
  }));
}

// 分片傳輸相關函數
const shouldUseChunking = (dataSize, macAddress) => {
  const chunkingSupport = gatewayCapabilities.get(macAddress);

  if (!chunkingSupport) {
    console.warn(`Gateway ${macAddress} 分片能力未知，使用保守設定`);
    return dataSize > 512; // 保守的 512 bytes 門檻
  }

  const maxChunkSize = chunkingSupport.maxChunkSize || 200;
  const chunkingEnabled = chunkingSupport.enabled || false;

  console.log(`判斷分片需求: 資料=${dataSize} bytes, Gateway maxChunkSize=${maxChunkSize} bytes`);

  if (dataSize > maxChunkSize && chunkingEnabled) {
    console.log('✅ 啟用分片傳輸');
    return true;
  } else if (dataSize > maxChunkSize && !chunkingEnabled) {
    console.error('❌ 資料過大但 Gateway 不支援分片');
    throw new Error(`Data too large for Gateway ${macAddress}`);
  } else {
    console.log('✅ 直接傳輸');
    return false;
  }
};

// 測試分片傳輸的 API
app.post('/api/test-chunking', (req, res) => {
  const { gatewayId, dataSize = 9484 } = req.body;
  
  const ws = connectedGateways.get(gatewayId);
  if (!ws) {
    return res.status(404).json({ error: '網關未連接' });
  }

  const gatewayMac = ws.macAddress;
  console.log(`測試分片傳輸: gatewayId=${gatewayId}, gatewayMac=${gatewayMac}, dataSize=${dataSize}`);

  try {
    const useChunking = shouldUseChunking(dataSize, gatewayMac);
    const capability = gatewayCapabilities.get(gatewayMac);
    
    res.json({
      success: true,
      gatewayId,
      gatewayMac,
      dataSize,
      useChunking,
      capability,
      message: useChunking ? '將使用分片傳輸' : '將使用直接傳輸'
    });
  } catch (error) {
    res.status(400).json({
      error: error.message,
      gatewayId,
      gatewayMac,
      dataSize
    });
  }
});

// 啟動服務器
server.listen(port, '0.0.0.0', () => {
  console.log(`測試分片傳輸服務器運行在:`);
  console.log(`  - HTTP: http://localhost:${port}`);
  console.log(`  - WebSocket: ws://localhost:${port}/ws/store/{storeId}/gateway/{gatewayId}?token={jwt_token}`);
  console.log(`\n測試步驟:`);
  console.log(`1. 運行測試客戶端: node tests/test-ws-client-interactive.js`);
  console.log(`2. 等待網關上報能力信息`);
  console.log(`3. 測試分片判斷: POST /api/test-chunking`);
});

server.on('error', (error) => {
  console.error('服務器錯誤:', error);
});
