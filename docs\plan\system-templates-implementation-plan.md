# 系統模板頁面實現計劃

## 1. 概述

目前系統中已有完整的門店模板頁面實現，但系統模板頁面（SystemTemplatesPage）僅為佔位頁面，尚未實現實際功能。本計劃旨在通過共用門店模板頁面的代碼來實現系統模板頁面，以確保功能一致性並減少開發工作量。

## 2. 現狀分析

### 2.1 門店模板頁面

- 使用 `TemplateList` 組件實現完整的模板管理功能
- 支持顯示、搜索、過濾、分頁和操作模板
- 已包含系統模板和門店模板的區分邏輯
- 通過 `storeId` 和 `isSystemTemplate` 屬性區分模板類型

### 2.2 系統模板頁面

- 目前僅為佔位頁面，顯示「頁面開發中」訊息
- 尚未實現實際功能

### 2.3 後端 API

- 已支持根據 `storeId` 和 `isSystemTemplate` 屬性過濾模板
- 模板保存時已包含類型區分邏輯

## 3. 實現方案

### 3.1 共用 TemplateList 組件

修改 `TemplateList` 組件，使其可以根據參數調整行為，以支持系統模板頁面的需求：

1. 添加新的屬性：
   - `defaultTemplateTypeFilter`：默認的模板類型過濾器設置
   - `systemTemplatesOnly`：是否只顯示和操作系統模板

2. 根據 `systemTemplatesOnly` 屬性調整過濾邏輯和 UI 顯示

### 3.2 修改 SystemTemplatesPage 組件

將 `SystemTemplatesPage` 組件修改為使用 `TemplateList` 組件，並設置特定參數：

```tsx
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TemplateList } from './TemplateList';
import { AddTemplateModal } from './AddTemplateModal';

export const SystemTemplatesPage: React.FC = () => {
  const { t } = useTranslation();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  
  return (
    <>
      <main className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-4 py-2">
        <TemplateList 
          onAddTemplate={() => setIsAddModalOpen(true)} 
          store={null}
          defaultTemplateTypeFilter="system"
          systemTemplatesOnly={true}
        />
      </main>
      
      {/* 添加模板模態窗口 */}
      <AddTemplateModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        store={null}
        systemTemplatesOnly={true}
      />
    </>
  );
};
```

### 3.3 修改 TemplateList 組件

更新 `TemplateList` 組件以支持系統模板頁面的需求：

```tsx
interface TemplateListProps {
  onAddTemplate?: () => void;
  store?: { id: string; name: string } | null;
  defaultTemplateTypeFilter?: 'all' | 'system' | 'store';
  systemTemplatesOnly?: boolean; // 新增：是否只顯示系統模板
}

export const TemplateList: React.FC<TemplateListProps> = ({
  onAddTemplate,
  store,
  defaultTemplateTypeFilter = 'all',
  systemTemplatesOnly = false
}) => {
  // 初始化模板類型過濾器
  const [templateTypeFilter, setTemplateTypeFilter] = useState<'all' | 'system' | 'store'>(
    systemTemplatesOnly ? 'system' : defaultTemplateTypeFilter
  );
  
  // 其他代碼...
  
  // 搜尋和類型過濾邏輯
  useEffect(() => {
    // 如果設置了只顯示系統模板，強制使用系統模板過濾器
    if (systemTemplatesOnly && templateTypeFilter !== 'system') {
      setTemplateTypeFilter('system');
    }
    
    // 先確保只顯示當前門店的模板和系統模板
    let filteredTemplates = templates;
    
    if (systemTemplatesOnly) {
      // 如果是系統模板頁面，只顯示系統模板
      filteredTemplates = templates.filter(template => template.isSystemTemplate);
    } else {
      // 原有的過濾邏輯...
      filteredTemplates = templates.filter(template =>
        template.isSystemTemplate ||
        (template.storeId && store && template.storeId === store.id) ||
        (!template.storeId && !template.isSystemTemplate)
      );
      
      // 再按模板類型過濾
      if (templateTypeFilter === 'system') {
        filteredTemplates = filteredTemplates.filter(template => template.isSystemTemplate);
      } else if (templateTypeFilter === 'store') {
        filteredTemplates = filteredTemplates.filter(template =>
          (template.storeId && !template.isSystemTemplate) ||
          (!template.storeId && !template.isSystemTemplate)
        );
      }
    }
    
    // 其他過濾邏輯...
  }, [searchTerm, templates, templateTypeFilter, store, systemTemplatesOnly]);
  
  // 渲染 UI
  return (
    <>
      {/* Header 區塊 */}
      <header className="bg-gray-100 shadow sticky top-0 z-20 -mx-2 sm:-mx-6 lg:-mx-4 px-2 sm:px-6 lg:px-4 mb-4">
        <div className="max-w-7xl mx-auto py-2">
          <div className="flex justify-between items-center">
            <div className="flex gap-4">
              {/* 如果不是只顯示系統模板，才顯示過濾下拉選單 */}
              {!systemTemplatesOnly && (
                <select
                  className="border rounded-md px-1.5 py-1.5"
                  value={templateTypeFilter}
                  onChange={(e) => setTemplateTypeFilter(e.target.value as 'all' | 'system' | 'store')}
                >
                  <option value="all">{t('templates.allTemplates')}</option>
                  <option value="system">{t('templates.systemTemplate')}</option>
                  <option value="store">{t('templates.storeTemplate')}</option>
                </select>
              )}
              {/* 其他 UI 元素... */}
            </div>
          </div>
        </div>
      </header>
      {/* 其他內容... */}
    </>
  );
};
```

### 3.4 修改 AddTemplateModal 組件

更新 `AddTemplateModal` 組件以支持創建系統模板：

```tsx
interface AddTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  store?: { id: string; name: string } | null;
  systemTemplatesOnly?: boolean; // 新增：是否只創建系統模板
}

export const AddTemplateModal: React.FC<AddTemplateModalProps> = ({
  isOpen,
  onClose,
  store,
  systemTemplatesOnly = false
}) => {
  // 其他代碼...
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 創建新模板對象
    const newTemplate = {
      id: Date.now().toString(),
      name: formData.name,
      type: TemplateType.SINGLE_DATA,
      screenSize: adjustedScreenSize,
      color: formData.color,
      orientation: parsedOrientation,
      elements: [],
      // 如果是系統模板頁面，則設置為系統模板
      isSystemTemplate: systemTemplatesOnly ? true : false,
      // 如果是系統模板頁面，則不設置 storeId
      storeId: systemTemplatesOnly ? undefined : store?.id,
    };
    
    // 其他代碼...
  };
  
  // 其他代碼...
};
```

## 4. 權限控制

確保系統模板頁面受到適當的權限控制：

1. 在前端路由中添加權限檢查：

```tsx
<Route
  path="/system-templates"
  element={
    <ProtectedRoute permissions={['system:view', 'system-template:view']}>
      <SystemTemplatesPage />
    </ProtectedRoute>
  }
/>
```

2. 在後端 API 中添加權限檢查：

```javascript
// 獲取系統模板
router.get('/templates', authenticate, checkPermission(['system:view', 'system-template:view']), async (req, res) => {
  // API 邏輯...
});

// 創建/更新系統模板
router.post('/templates', authenticate, checkPermission(['system-template:create', 'system-template:update']), async (req, res) => {
  // API 邏輯...
});
```

## 5. 實施步驟

1. 修改 `TemplateList.tsx`，添加新的屬性和相應的邏輯
2. 修改 `AddTemplateModal.tsx`，添加系統模板創建支持
3. 更新 `SystemTemplatesPage.tsx`，使用 `TemplateList` 組件
4. 更新權限控制邏輯
5. 測試系統模板頁面功能

## 6. 測試計劃

1. 驗證系統模板頁面只顯示系統模板
2. 測試在系統模板頁面創建的模板是否正確設置為系統模板
3. 測試系統模板在門店模板頁面中是否可見
4. 測試權限控制是否正常工作
5. 測試所有模板操作（編輯、刪除、複製等）在系統模板上是否正常工作

## 7. 總結

通過共用 `TemplateList` 組件來實現系統模板頁面，可以確保功能一致性並減少開發工作量。只需要添加少量參數和邏輯調整，即可實現完整的系統模板管理功能。
