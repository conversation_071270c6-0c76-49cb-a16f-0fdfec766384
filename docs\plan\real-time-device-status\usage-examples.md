# 即時狀態更新使用示例

## 概述

本文檔提供了EPD管理系統中設備和網關即時狀態更新功能的使用示例，包括前端組件整合、自定義Hook使用、以及常見場景的實現方法。

## 基本使用

### 1. 設備狀態訂閱

```typescript
import { subscribeToDeviceStatus } from '../utils/websocketClient';

function DeviceMonitor({ storeId }: { storeId: string }) {
  const [devices, setDevices] = useState<Device[]>([]);

  useEffect(() => {
    // 訂閱設備狀態更新
    const unsubscribe = subscribeToDeviceStatus(
      storeId,
      (event) => {
        console.log('收到設備狀態更新:', event);
        
        // 更新設備狀態
        setDevices(prevDevices => 
          prevDevices.map(device => {
            const update = event.devices.find(d => d._id === device._id);
            return update ? { ...device, ...update } : device;
          })
        );
      },
      {
        includeImageStatus: true,
        includeBatteryInfo: true
      }
    );

    return unsubscribe;
  }, [storeId]);

  return (
    <div>
      {devices.map(device => (
        <DeviceCard key={device._id} device={device} />
      ))}
    </div>
  );
}
```

### 2. 網關狀態訂閱

```typescript
import { subscribeToGatewayStatus } from '../utils/websocketClient';

function GatewayMonitor({ storeId }: { storeId: string }) {
  const [gateways, setGateways] = useState<Gateway[]>([]);

  useEffect(() => {
    // 訂閱網關狀態更新
    const unsubscribe = subscribeToGatewayStatus(
      storeId,
      (event) => {
        console.log('收到網關狀態更新:', event);
        
        // 更新網關狀態
        setGateways(prevGateways => 
          prevGateways.map(gateway => {
            const update = event.gateways.find(g => g._id === gateway._id);
            return update ? { ...gateway, ...update } : gateway;
          })
        );
      },
      {
        includeConnectionInfo: true,
        includeFirmwareInfo: true
      }
    );

    return unsubscribe;
  }, [storeId]);

  return (
    <div>
      {gateways.map(gateway => (
        <GatewayCard key={gateway._id} gateway={gateway} />
      ))}
    </div>
  );
}
```

## 自定義Hook

### 1. 設備狀態Hook

```typescript
import { useState, useEffect } from 'react';
import { subscribeToDeviceStatus, DeviceStatusEvent } from '../utils/websocketClient';

interface UseDeviceStatusOptions {
  storeId: string;
  enabled?: boolean;
  includeImageStatus?: boolean;
  includeBatteryInfo?: boolean;
}

export function useDeviceStatus(options: UseDeviceStatusOptions) {
  const { storeId, enabled = true, includeImageStatus = true, includeBatteryInfo = true } = options;
  
  const [devices, setDevices] = useState<Device[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    if (!enabled || !storeId) return;

    console.log(`啟用設備狀態監控: ${storeId}`);
    setIsConnected(true);

    const handleStatusUpdate = (event: DeviceStatusEvent) => {
      setLastUpdate(new Date());
      
      setDevices(prevDevices => 
        prevDevices.map(device => {
          const update = event.devices.find(d => d._id === device._id);
          if (update) {
            return {
              ...device,
              status: update.status,
              lastSeen: new Date(update.lastSeen),
              imageUpdateStatus: update.imageUpdateStatus || device.imageUpdateStatus,
              data: {
                ...device.data,
                battery: update.data?.battery ?? device.data?.battery,
                rssi: update.data?.rssi ?? device.data?.rssi,
                imageCode: update.data?.imageCode ?? device.data?.imageCode
              }
            };
          }
          return device;
        })
      );
    };

    const unsubscribe = subscribeToDeviceStatus(
      storeId,
      handleStatusUpdate,
      {
        includeImageStatus,
        includeBatteryInfo
      }
    );

    return () => {
      console.log(`停用設備狀態監控: ${storeId}`);
      setIsConnected(false);
      unsubscribe();
    };
  }, [storeId, enabled, includeImageStatus, includeBatteryInfo]);

  return {
    devices,
    setDevices,
    isConnected,
    lastUpdate
  };
}
```

### 2. 網關狀態Hook

```typescript
interface UseGatewayStatusOptions {
  storeId: string;
  enabled?: boolean;
  includeConnectionInfo?: boolean;
  includeFirmwareInfo?: boolean;
}

export function useGatewayStatus(options: UseGatewayStatusOptions) {
  const { storeId, enabled = true, includeConnectionInfo = true, includeFirmwareInfo = true } = options;
  
  const [gateways, setGateways] = useState<Gateway[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    if (!enabled || !storeId) return;

    console.log(`啟用網關狀態監控: ${storeId}`);
    setIsConnected(true);

    const handleStatusUpdate = (event: GatewayStatusEvent) => {
      setLastUpdate(new Date());
      
      setGateways(prevGateways => 
        prevGateways.map(gateway => {
          const update = event.gateways.find(g => g._id === gateway._id);
          if (update) {
            return {
              ...gateway,
              status: update.status,
              lastSeen: new Date(update.lastSeen),
              name: update.name || gateway.name,
              model: update.model || gateway.model,
              wifiFirmwareVersion: update.wifiFirmwareVersion || gateway.wifiFirmwareVersion,
              btFirmwareVersion: update.btFirmwareVersion || gateway.btFirmwareVersion,
              ipAddress: update.ipAddress || gateway.ipAddress
            };
          }
          return gateway;
        })
      );
    };

    const unsubscribe = subscribeToGatewayStatus(
      storeId,
      handleStatusUpdate,
      {
        includeConnectionInfo,
        includeFirmwareInfo
      }
    );

    return () => {
      console.log(`停用網關狀態監控: ${storeId}`);
      setIsConnected(false);
      unsubscribe();
    };
  }, [storeId, enabled, includeConnectionInfo, includeFirmwareInfo]);

  return {
    gateways,
    setGateways,
    isConnected,
    lastUpdate
  };
}
```

## 狀態指示器組件

### 1. 連接狀態指示器

```typescript
interface ConnectionStatusProps {
  isConnected: boolean;
  lastUpdate: Date | null;
  onToggle: (enabled: boolean) => void;
  enabled: boolean;
}

export function ConnectionStatus({ isConnected, lastUpdate, onToggle, enabled }: ConnectionStatusProps) {
  const getStatusColor = () => {
    if (!enabled) return 'text-gray-400';
    return isConnected ? 'text-green-500' : 'text-red-500';
  };

  const getStatusText = () => {
    if (!enabled) return '即時更新已關閉';
    if (isConnected) {
      return lastUpdate 
        ? `最後更新: ${lastUpdate.toLocaleTimeString()}`
        : '即時更新已連接';
    }
    return '連接已斷開';
  };

  const getBgColor = () => {
    if (!enabled) return 'bg-gray-100 border-gray-300';
    return isConnected ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300';
  };

  return (
    <div className={`flex items-center gap-2 px-3 py-2 rounded-md border ${getBgColor()}`}>
      <div className={`w-2 h-2 rounded-full ${getStatusColor().replace('text-', 'bg-')}`} />
      <span className={`text-sm ${getStatusColor()}`}>
        {getStatusText()}
      </span>
      <button
        onClick={() => onToggle(!enabled)}
        className="text-sm text-blue-600 hover:text-blue-800 underline ml-2"
      >
        {enabled ? '關閉' : '開啟'}
      </button>
    </div>
  );
}
```

### 2. 統計面板組件

```typescript
interface StatsCardProps {
  title: string;
  value: number;
  color: string;
  description: string;
}

function StatsCard({ title, value, color, description }: StatsCardProps) {
  return (
    <div className="text-center">
      <div className={`text-2xl font-bold ${color}`}>
        {value}
      </div>
      <div className="text-sm text-gray-500">{description}</div>
    </div>
  );
}

interface DeviceStatsProps {
  devices: Device[];
}

export function DeviceStats({ devices }: DeviceStatsProps) {
  const onlineCount = devices.filter(d => d.status === 'online').length;
  const offlineCount = devices.filter(d => d.status === 'offline').length;
  const updatedCount = devices.filter(d => d.imageUpdateStatus === '已更新').length;
  const pendingCount = devices.filter(d => d.imageUpdateStatus === '未更新').length;

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">設備狀態統計</h3>
      <div className="grid grid-cols-4 gap-4">
        <StatsCard
          title="在線設備"
          value={onlineCount}
          color="text-green-600"
          description="在線設備"
        />
        <StatsCard
          title="離線設備"
          value={offlineCount}
          color="text-gray-600"
          description="離線設備"
        />
        <StatsCard
          title="已更新圖片"
          value={updatedCount}
          color="text-blue-600"
          description="已更新圖片"
        />
        <StatsCard
          title="待更新圖片"
          value={pendingCount}
          color="text-orange-600"
          description="待更新圖片"
        />
      </div>
    </div>
  );
}
```

## 完整頁面示例

### 設備監控頁面

```typescript
import React, { useState } from 'react';
import { useDeviceStatus } from '../hooks/useDeviceStatus';
import { ConnectionStatus } from '../components/ConnectionStatus';
import { DeviceStats } from '../components/DeviceStats';

interface DeviceMonitorPageProps {
  storeId: string;
}

export function DeviceMonitorPage({ storeId }: DeviceMonitorPageProps) {
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);
  
  const { devices, setDevices, isConnected, lastUpdate } = useDeviceStatus({
    storeId,
    enabled: realTimeEnabled,
    includeImageStatus: true,
    includeBatteryInfo: true
  });

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">設備監控</h1>
        
        {/* 連接狀態指示器 */}
        <ConnectionStatus
          isConnected={isConnected}
          lastUpdate={lastUpdate}
          onToggle={setRealTimeEnabled}
          enabled={realTimeEnabled}
        />
      </div>

      {/* 統計面板 */}
      <div className="mb-6">
        <DeviceStats devices={devices} />
      </div>

      {/* 設備列表 */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-4">
          <h2 className="text-lg font-medium text-gray-900 mb-4">設備列表</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {devices.map(device => (
              <DeviceCard key={device._id} device={device} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
```

## 錯誤處理示例

### 1. 連接錯誤處理

```typescript
export function useWebSocketConnection(storeId: string) {
  const [connectionState, setConnectionState] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    if (!storeId) return;

    let retryTimer: NodeJS.Timeout;

    const connect = () => {
      setConnectionState('connecting');
      setError(null);

      try {
        // 嘗試連接WebSocket
        const unsubscribe = subscribeToDeviceStatus(
          storeId,
          (event) => {
            setConnectionState('connected');
            setRetryCount(0);
            // 處理事件...
          }
        );

        return unsubscribe;
      } catch (err) {
        setConnectionState('disconnected');
        setError(err instanceof Error ? err.message : '連接失敗');
        
        // 自動重試
        if (retryCount < 5) {
          retryTimer = setTimeout(() => {
            setRetryCount(prev => prev + 1);
            connect();
          }, Math.pow(2, retryCount) * 1000); // 指數退避
        }
      }
    };

    const unsubscribe = connect();

    return () => {
      if (retryTimer) clearTimeout(retryTimer);
      if (unsubscribe) unsubscribe();
    };
  }, [storeId, retryCount]);

  return { connectionState, error, retryCount };
}
```

### 2. 數據驗證

```typescript
function validateDeviceStatusEvent(event: any): event is DeviceStatusEvent {
  return (
    event &&
    typeof event === 'object' &&
    event.type === 'device_status_update' &&
    typeof event.storeId === 'string' &&
    Array.isArray(event.devices) &&
    event.devices.every((device: any) => 
      device._id && 
      typeof device.status === 'string' &&
      typeof device.lastSeen === 'string'
    )
  );
}

function handleDeviceStatusUpdate(event: any) {
  if (!validateDeviceStatusEvent(event)) {
    console.error('收到無效的設備狀態事件:', event);
    return;
  }

  // 安全地處理事件
  console.log('處理設備狀態更新:', event);
  // ...
}
```

## 性能優化示例

### 1. 防抖更新

```typescript
import { debounce } from 'lodash';

export function useOptimizedDeviceStatus(storeId: string) {
  const [devices, setDevices] = useState<Device[]>([]);
  
  // 防抖更新，避免頻繁重新渲染
  const debouncedUpdate = useMemo(
    () => debounce((updates: Device[]) => {
      setDevices(prevDevices => {
        const updatedDevices = [...prevDevices];
        updates.forEach(update => {
          const index = updatedDevices.findIndex(d => d._id === update._id);
          if (index >= 0) {
            updatedDevices[index] = { ...updatedDevices[index], ...update };
          }
        });
        return updatedDevices;
      });
    }, 100),
    []
  );

  useEffect(() => {
    const unsubscribe = subscribeToDeviceStatus(
      storeId,
      (event) => {
        debouncedUpdate(event.devices);
      }
    );

    return () => {
      debouncedUpdate.cancel();
      unsubscribe();
    };
  }, [storeId, debouncedUpdate]);

  return { devices, setDevices };
}
```

### 2. 選擇性更新

```typescript
function updateDeviceSelectively(prevDevice: Device, update: Partial<Device>): Device {
  const hasChanges = Object.keys(update).some(key => {
    const updateValue = update[key as keyof Device];
    const prevValue = prevDevice[key as keyof Device];
    
    // 深度比較對象
    if (typeof updateValue === 'object' && typeof prevValue === 'object') {
      return JSON.stringify(updateValue) !== JSON.stringify(prevValue);
    }
    
    return updateValue !== prevValue;
  });

  return hasChanges ? { ...prevDevice, ...update } : prevDevice;
}
```

## 測試示例

### 1. Hook測試

```typescript
import { renderHook, act } from '@testing-library/react';
import { useDeviceStatus } from '../hooks/useDeviceStatus';

describe('useDeviceStatus', () => {
  it('should subscribe to device status updates', () => {
    const { result } = renderHook(() => 
      useDeviceStatus({ storeId: 'test-store' })
    );

    expect(result.current.isConnected).toBe(true);
    expect(result.current.devices).toEqual([]);
  });

  it('should handle device status updates', () => {
    const { result } = renderHook(() => 
      useDeviceStatus({ storeId: 'test-store' })
    );

    act(() => {
      // 模擬狀態更新
      const mockEvent = {
        type: 'device_status_update',
        storeId: 'test-store',
        devices: [
          {
            _id: 'device-1',
            status: 'online',
            lastSeen: new Date().toISOString()
          }
        ]
      };
      
      // 觸發更新...
    });

    expect(result.current.devices).toHaveLength(1);
  });
});
```

### 2. 組件測試

```typescript
import { render, screen } from '@testing-library/react';
import { ConnectionStatus } from '../components/ConnectionStatus';

describe('ConnectionStatus', () => {
  it('should display connected status', () => {
    render(
      <ConnectionStatus
        isConnected={true}
        lastUpdate={new Date()}
        onToggle={() => {}}
        enabled={true}
      />
    );

    expect(screen.getByText(/即時更新已連接/)).toBeInTheDocument();
  });

  it('should display disconnected status', () => {
    render(
      <ConnectionStatus
        isConnected={false}
        lastUpdate={null}
        onToggle={() => {}}
        enabled={true}
      />
    );

    expect(screen.getByText(/連接已斷開/)).toBeInTheDocument();
  });
});
```

## 總結

這些示例展示了如何在實際項目中使用即時狀態更新功能。通過合理的組件設計、Hook封裝和錯誤處理，可以構建出穩定、高效的即時監控界面。記住要始終考慮性能優化和用戶體驗，確保功能在各種網絡條件下都能正常工作。
