// 調試批量發送進度的工具
const { getWebSocketClient } = require('./src/utils/websocketClient');

// 模擬批量發送進度測試
async function testBatchProgress() {
  console.log('🔧 開始測試批量發送進度...');
  
  // 獲取 WebSocket 客戶端
  const wsClient = getWebSocketClient();
  
  // 檢查連接狀態
  console.log('WebSocket 連接狀態:', wsClient.isConnected());
  
  if (!wsClient.isConnected()) {
    console.log('⚠️ WebSocket 未連接，嘗試連接...');
    wsClient.reconnect();
    
    // 等待連接
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 生成測試批量ID
  const testBatchId = `test_batch_${Date.now()}`;
  console.log(`📊 測試批量ID: ${testBatchId}`);
  
  // 訂閱進度
  const unsubscribe = wsClient.subscribeToBatchProgress(testBatchId, (event) => {
    console.log('📈 收到進度事件:', event);
  });
  
  console.log('✅ 已訂閱批量進度，等待事件...');
  
  // 等待一段時間
  setTimeout(() => {
    console.log('🔚 測試結束，取消訂閱');
    unsubscribe();
  }, 10000);
}

// 如果直接運行此文件
if (require.main === module) {
  testBatchProgress().catch(console.error);
}

module.exports = { testBatchProgress };
