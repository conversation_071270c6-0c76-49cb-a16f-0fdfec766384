/**
 * 簡單的模組測試
 */

try {
  console.log('🧪 Testing rawdata compression module...');
  
  const compression = require('./index.js');
  
  // 測試基本功能
  console.log('📋 Supported formats:', compression.getSupportedFormats());
  
  // 測試 Run-Length 壓縮
  const testData = new Uint8Array([0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x02, 0x03]);
  console.log('📊 Test data:', Array.from(testData));
  
  const result = compression.processPixelData('runlendata', testData);
  console.log('✅ Compression result:', {
    success: result.success,
    originalSize: result.originalSize,
    processedSize: result.processedSize,
    ratio: Math.round(result.processingRatio * 100) + '%'
  });
  
  if (result.success) {
    console.log('📦 Compressed data:', Array.from(result.data));
    
    // 測試解壓縮
    const decompressed = compression.decompressData('runlendata', result.data);
    console.log('🔄 Decompressed data:', Array.from(decompressed));
    console.log('✅ Decompression match:', Array.from(decompressed).join(',') === Array.from(testData).join(','));
    
    // 測試一致性
    const consistencyTest = compression.testCompressionConsistency('runlendata', testData);
    console.log('🎯 Consistency test:', consistencyTest.consistent);
  }
  
  // 測試 rawdata 格式
  const rawResult = compression.processPixelData('rawdata', testData);
  console.log('📄 Raw data result:', {
    success: rawResult.success,
    originalSize: rawResult.originalSize,
    processedSize: rawResult.processedSize,
    ratio: Math.round(rawResult.processingRatio * 100) + '%'
  });
  
  compression.printModuleStatus();
  
  console.log('\n✅ All tests completed successfully!');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
  process.exit(1);
}
