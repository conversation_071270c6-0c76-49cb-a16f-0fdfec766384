const express = require('express');
const router = express.Router();
const { ObjectId } = require('mongodb');
const { authenticate } = require('../middleware/auth');
const { logDeviceEvent, broadcastDeviceStatus, broadcastDeviceCRUD } = require('../services/websocketService');
const sendPreviewToGateway = require('../services/sendPreviewToGateway');
const deviceBindingService = require('../services/deviceBindingService');

// MongoDB 連接信息 (從 index.js 共享)
const collectionName = 'devices';

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
  // 初始化發送預覽圖到網關服務
  sendPreviewToGateway.initDB(dbConnectionFunc);
  // 初始化設備數據綁定服務
  deviceBindingService.initDB(dbConnectionFunc);
  return getDbConnection;
};

// 獲取數據集合
const getCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection(collectionName);
  return { collection, client };
};

// 獲取所有設備 (需要身份驗證)
router.get('/devices', authenticate, async (req, res) => {
  try {
    const { storeId, userId } = req.query;
    const { collection } = await getCollection();

    // 構建查詢條件
    const query = {};

    // 如果提供了門店ID，則只返回該門店的設備
    if (storeId) {
      query.storeId = storeId;
    }

    // 如果提供了用戶ID，則只返回該用戶綁定的設備
    if (userId) {
      query.userId = new ObjectId(userId);
    }

    const devices = await collection.find(query).toArray();
    res.json(devices);
  } catch (error) {
    console.error('獲取設備列表失敗:', error);
    res.status(500).json({ error: '獲取設備列表失敗' });
  }
});

// 獲取單個設備
router.get('/devices/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { storeId } = req.query;
    const { collection } = await getCollection();

    // 構建查詢條件
    const query = { _id: new ObjectId(id) };

    // 如果提供了門店ID，則確保設備屬於該門店
    if (storeId) {
      query.storeId = storeId;
    }

    const device = await collection.findOne(query);

    if (!device) {
      return res.status(404).json({ error: '設備不存在或不屬於指定門店' });
    }

    res.json(device);
  } catch (error) {
    console.error('獲取設備失敗:', error);
    res.status(500).json({ error: '獲取設備失敗' });
  }
});

// 驗證 MAC 地址格式
const isValidMacAddress = (mac) => {
  return /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(mac);
};

// 驗證 RSSI 值
const isValidRssi = (rssi) => {
  return rssi >= -100 && rssi <= 0;
};

// 驗證電池值
const isValidBattery = (battery) => {
  return battery >= 0 && battery <= 100;
};

// 設置設備的主要網關
const setDevicePrimaryGateway = async (deviceId, gatewayId) => {
  // 獲取數據庫連接
  const { collection: deviceCollection } = await getCollection();
  const { db } = await getDbConnection();
  const gatewayCollection = db.collection('gateways');

  // 檢查設備是否存在
  const device = await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
  if (!device) {
    throw new Error('設備不存在');
  }

  // 檢查網關是否存在
  const gateway = await gatewayCollection.findOne({ _id: new ObjectId(gatewayId) });
  if (!gateway) {
    throw new Error('網關不存在');
  }

  // 檢查該網關是否已發現該設備
  const otherGateways = device.otherGateways || [];
  const isDiscoveredByGateway =
    (device.primaryGatewayId && device.primaryGatewayId.toString() === gatewayId) ||
    otherGateways.some(id => id.toString() === gatewayId);

  if (!isDiscoveredByGateway) {
    // 如果網關尚未發現設備，將該網關添加為發現此設備的網關
    await gatewayCollection.updateOne(
      { _id: new ObjectId(gatewayId) },
      { $addToSet: { devices: new ObjectId(deviceId) } }
    );
  }
  const oldPrimaryGatewayId = device.primaryGatewayId;

  // 準備更新數據
  const updateData = {
    primaryGatewayId: new ObjectId(gatewayId),
    initialized: true,  // 設置主要網關時將設備標記為已初始化
    updatedAt: new Date()
  };

  // 如果設備處於自動模式，則將其設置為手動模式
  if (device.gatewaySelectionMode === 'auto' || device.gatewaySelectionMode === undefined) {
    updateData.gatewaySelectionMode = 'manual';
  }

  // 執行更新操作，按正確的順序處理：

  // 步驟1: 先將當前選擇的新主要網關從其他網關列表中移除
  await deviceCollection.updateOne(
    { _id: new ObjectId(deviceId) },
    { $pull: { otherGateways: { $in: [gatewayId, new ObjectId(gatewayId)] } } }
  );

  // 步驟2: 如果已有主要網關且不是現在選擇的網關，將原有主要網關添加到其他網關列表
  if (oldPrimaryGatewayId && !oldPrimaryGatewayId.equals(new ObjectId(gatewayId))) {
    await deviceCollection.updateOne(
      { _id: new ObjectId(deviceId) },
      { $addToSet: { otherGateways: oldPrimaryGatewayId } }
    );
  }

  // 步驟3: 最後更新主要網關信息
  await deviceCollection.updateOne(
    { _id: new ObjectId(deviceId) },
    { $set: updateData }
  );

  // 記錄設備主要網關變更事件
  await logDeviceEvent(deviceId, 'gateway_changed', {
    action: 'set_primary_gateway',
    oldPrimaryGatewayId: oldPrimaryGatewayId ? oldPrimaryGatewayId.toString() : null,
    newPrimaryGatewayId: gatewayId,
    gatewayName: gateway.name || '未知',
    gatewayMode: 'manual'
  });

  // 返回更新後的設備
  return await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
};

// 綁定設備到用戶
const bindDeviceToUser = async (deviceId, userId) => {
  // 獲取數據庫連接
  const { collection: deviceCollection } = await getCollection();
  const { db } = await getDbConnection();
  const userCollection = db.collection('users');

  // 檢查設備是否存在
  const device = await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
  if (!device) {
    throw new Error('設備不存在');
  }

  // 檢查用戶是否存在
  const user = await userCollection.findOne({ _id: new ObjectId(userId) });
  if (!user) {
    throw new Error('用戶不存在');
  }

  // 檢查設備是否已綁定其他用戶
  if (device.userId && !device.userId.equals(new ObjectId(userId))) {
    throw new Error('設備已綁定其他用戶，請先解除綁定');
  }

  // 更新設備的用戶ID
  await deviceCollection.updateOne(
    { _id: new ObjectId(deviceId) },
    { $set: {
        userId: new ObjectId(userId),
        updatedAt: new Date()
      }
    }
  );

  // 記錄設備綁定用戶事件
  await logDeviceEvent(deviceId, 'user_binding', {
    action: 'bind',
    userId: userId,
    userName: user.name || user.username || '未知',
    userEmail: user.email || '未知'
  });

  // 返回更新後的設備
  return await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
};

// 解除設備與用戶的綁定
const unbindDeviceFromUser = async (deviceId) => {
  // 獲取數據庫連接
  const { collection: deviceCollection } = await getCollection();
  const { db } = await getDbConnection();

  // 檢查設備是否存在
  const device = await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
  if (!device) {
    throw new Error('設備不存在');
  }

  // 如果設備已綁定用戶，則解除綁定
  if (device.userId) {
    // 獲取用戶信息，用於記錄事件
    const userId = device.userId;
    const userCollection = db.collection('users');
    const user = await userCollection.findOne({ _id: userId });

    await deviceCollection.updateOne(
      { _id: new ObjectId(deviceId) },
      {
        $unset: { userId: "" },
        $set: { updatedAt: new Date() }
      }
    );

    // 記錄設備解除綁定事件
    await logDeviceEvent(deviceId, 'user_binding', {
      action: 'unbind',
      previousUserId: userId.toString(),
      previousUserName: user ? (user.name || user.username || '未知') : '未知',
      previousUserEmail: user ? (user.email || '未知') : '未知'
    });
  }

  // 返回更新後的設備
  return await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
};

// 創建新設備 (需要身份驗證)
router.post('/devices', authenticate, async (req, res) => {
  // 注意: 通過API創建的設備被標記為未初始化，需要通過網關WebSocket更新才能變為已初始化
  try {
    const deviceData = req.body;
    const { collection } = await getCollection();

    // 驗證必要欄位
    if (!deviceData.storeId) {
      return res.status(400).json({ error: '門店ID不能為空' });
    }

    // 驗證 MAC 地址格式
    if (!isValidMacAddress(deviceData.macAddress)) {
      return res.status(400).json({ error: 'MAC 地址格式不正確，應為 XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX 格式' });
    }

    // 將 rssi, battery, size 移到 data 對象中
    const deviceDataWithStructure = {
      ...deviceData,
      data: {
        ...(deviceData.data || {})
      }
    };

    // 如果直接提供了 rssi, battery, size，則移動到 data 對象中
    if (deviceData.rssi !== undefined) {
      if (!isValidRssi(deviceData.rssi)) {
        return res.status(400).json({ error: 'RSSI 值應在 -100 到 0 之間' });
      }
      deviceDataWithStructure.data.rssi = deviceData.rssi;
      delete deviceDataWithStructure.rssi;
    }

    if (deviceData.battery !== undefined) {
      if (!isValidBattery(deviceData.battery)) {
        return res.status(400).json({ error: '電池值應在 0 到 100 之間' });
      }
      deviceDataWithStructure.data.battery = deviceData.battery;
      delete deviceDataWithStructure.battery;
    }

    if (deviceData.size !== undefined) {
      deviceDataWithStructure.data.size = deviceData.size;
      delete deviceDataWithStructure.size;
    }

    // 驗證嵌套在 data 對象中的值
    if (deviceDataWithStructure.data.rssi !== undefined && !isValidRssi(deviceDataWithStructure.data.rssi)) {
      return res.status(400).json({ error: 'RSSI 值應在 -100 到 0 之間' });
    }

    if (deviceDataWithStructure.data.battery !== undefined && !isValidBattery(deviceDataWithStructure.data.battery)) {
      return res.status(400).json({ error: '電池值應在 0 到 100 之間' });
    }

    // 檢查 MAC 地址是否已存在
    const existingDevice = await collection.findOne({ macAddress: deviceDataWithStructure.macAddress });
    if (existingDevice) {
      return res.status(400).json({ error: 'MAC 地址已存在' });
    }    // 添加自動生成的欄位，確保符合規格書的結構
    // 透過API創建的設備標記為未初始化，需等待網關發現並通過WebSocket更新才會變為已初始化
    const newDevice = {
      ...deviceDataWithStructure,
      initialized: false,                // 新設備預設為未初始化
      primaryGatewayId: null,           // 初始沒有主要網關
      otherGateways: [],                // 初始時沒有其他網關
      userId: req.user._id,             // 自動關聯到當前登入的用戶（創建設備的用戶）
      lastSeen: new Date(),             // 確保 lastSeen 是 Date 類型
      note: deviceDataWithStructure.note || '',  // 確保 note 欄位存在
      dataId: deviceDataWithStructure.dataId || '',  // 確保 dataId 欄位存在
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 插入新設備
    const result = await collection.insertOne(newDevice);

    if (result.acknowledged) {
      // 記錄設備自動綁定用戶事件
      await logDeviceEvent(result.insertedId, 'user_binding', {
        action: 'auto_bind',
        userId: req.user._id.toString(),
        userName: req.user.name || req.user.username || '未知',
        userEmail: req.user.email || '未知',
        note: '設備創建時自動綁定到創建者'
      });

      // 推送WebSocket事件
      try {
        const deviceUpdate = {
          _id: result.insertedId.toString(),
          macAddress: newDevice.macAddress,
          status: newDevice.status,
          lastSeen: newDevice.lastSeen.toISOString(),
          imageUpdateStatus: newDevice.imageUpdateStatus,
          data: newDevice.data,
          updatedFields: ['macAddress', 'status', 'dataId', 'storeId', 'data']
        };

        // 推送設備CRUD事件（新增）
        broadcastDeviceCRUD(newDevice.storeId, [deviceUpdate], 'create');
      } catch (wsError) {
        console.error('推送設備創建事件失敗:', wsError);
        // 不影響API響應
      }

      // 返回插入的設備數據，包含MongoDB自動生成的_id
      res.status(201).json({
        ...newDevice,
        _id: result.insertedId
      });
    } else {
      throw new Error('設備創建失敗');
    }
  } catch (error) {
    console.error('創建設備失敗:', error);
    res.status(500).json({ error: '創建設備失敗' });
  }
});

// 更新設備 (需要身份驗證)
router.put('/devices/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { storeId } = req.query;
    const updateData = req.body;
    const { collection } = await getCollection();

    // 構建查詢條件
    const query = { _id: new ObjectId(id) };

    // 如果提供了門店ID，則確保設備屬於該門店
    if (storeId) {
      query.storeId = storeId;
    }

    // 檢查設備是否存在
    const existingDevice = await collection.findOne(query);
    if (!existingDevice) {
      return res.status(404).json({ error: '設備不存在或不屬於指定門店' });
    }

    // 檢查權限：只有設備的創建者或綁定用戶可以更新設備
    if (existingDevice && existingDevice.userId &&
        !existingDevice.userId.equals(req.user._id)) {
      return res.status(403).json({ error: '沒有權限更新此設備' });
    }

    // 不允許修改 storeId
    if (updateData.storeId && updateData.storeId !== existingDevice.storeId) {
      return res.status(400).json({ error: '不允許修改設備所屬的門店' });
    }

    // 驗證 MAC 地址格式
    if (updateData.macAddress && !isValidMacAddress(updateData.macAddress)) {
      return res.status(400).json({ error: 'MAC 地址格式不正確，應為 XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX 格式' });
    }

    // 構建更新對象
    const updatedFields = { ...updateData };

    // 處理資料結構，將 rssi、battery、size 移到 data 對象中
    if (updatedFields.rssi !== undefined) {
      if (!isValidRssi(updatedFields.rssi)) {
        return res.status(400).json({ error: 'RSSI 值應在 -100 到 0 之間' });
      }

      // 確保 data 對象存在
      if (!updatedFields.data) {
        updatedFields.data = { ...existingDevice.data } || {};
      }

      updatedFields.data.rssi = updatedFields.rssi;
      delete updatedFields.rssi;
    }

    if (updatedFields.battery !== undefined) {
      if (!isValidBattery(updatedFields.battery)) {
        return res.status(400).json({ error: '電池值應在 0 到 100 之間' });
      }

      // 確保 data 對象存在
      if (!updatedFields.data) {
        updatedFields.data = { ...existingDevice.data } || {};
      }

      updatedFields.data.battery = updatedFields.battery;
      delete updatedFields.battery;
    }

    if (updatedFields.size !== undefined) {
      // 確保 data 對象存在
      if (!updatedFields.data) {
        updatedFields.data = { ...existingDevice.data } || {};
      }

      updatedFields.data.size = updatedFields.size;
      delete updatedFields.size;
    }

    // 驗證嵌套在 data 對象中的值
    if (updatedFields.data) {
      if (updatedFields.data.rssi !== undefined && !isValidRssi(updatedFields.data.rssi)) {
        return res.status(400).json({ error: 'RSSI 值應在 -100 到 0 之間' });
      }

      if (updatedFields.data.battery !== undefined && !isValidBattery(updatedFields.data.battery)) {
        return res.status(400).json({ error: '電池值應在 0 到 100 之間' });
      }
    }

    // 如果 MAC 地址有變更，檢查是否與其他設備衝突
    if (updatedFields.macAddress && updatedFields.macAddress !== existingDevice.macAddress) {
      const duplicateMac = await collection.findOne({
        macAddress: updatedFields.macAddress,
        _id: { $ne: new ObjectId(id) }
      });

      if (duplicateMac) {
        return res.status(400).json({ error: 'MAC 地址已被其他設備使用' });
      }
    }

    // 更新設備資料
    const result = await collection.updateOne(
      query,
      {
        $set: {
          ...updatedFields,
          updatedAt: new Date()
        }
      }
    );

    if (result.matchedCount === 0) {
      return res.status(404).json({ error: '設備不存在' });
    }

    // 獲取更新後的設備資料
    const updatedDevice = await collection.findOne({ _id: new ObjectId(id) });

    // 推送WebSocket事件
    try {
      const deviceUpdate = {
        _id: updatedDevice._id.toString(),
        macAddress: updatedDevice.macAddress,
        status: updatedDevice.status,
        lastSeen: updatedDevice.lastSeen ? updatedDevice.lastSeen.toISOString() : null,
        imageUpdateStatus: updatedDevice.imageUpdateStatus,
        data: updatedDevice.data,
        updatedFields: Object.keys(updateData)
      };

      // 推送設備CRUD事件（更新）
      broadcastDeviceCRUD(updatedDevice.storeId, [deviceUpdate], 'update');
    } catch (wsError) {
      console.error('推送設備更新事件失敗:', wsError);
      // 不影響API響應
    }

    res.json(updatedDevice);
  } catch (error) {
    console.error('更新設備失敗:', error);
    res.status(500).json({ error: '更新設備失敗' });
  }
});

// 刪除設備 (需要身份驗證)
router.delete('/devices/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { storeId } = req.query;
    const { collection } = await getCollection();

    // 構建查詢條件
    const query = { _id: new ObjectId(id) };

    // 如果提供了門店ID，則確保設備屬於該門店
    if (storeId) {
      query.storeId = storeId;
    }

    // 檢查設備是否存在
    const device = await collection.findOne(query);
    if (!device) {
      return res.status(404).json({ error: '設備不存在或不屬於指定門店' });
    }

    // 檢查權限：只有設備的創建者或綁定用戶可以刪除設備
    if (device && device.userId &&
        !device.userId.equals(req.user._id)) {
      return res.status(403).json({ error: '沒有權限刪除此設備' });
    }    // 如果設備已綁定用戶，先記錄解除綁定事件
    if (device.userId) {
      try {
        const userCollection = await db.collection('users');
        const user = await userCollection.findOne({ _id: device.userId });

        await logDeviceEvent(id, 'user_binding', {
          action: 'auto_unbind',
          previousUserId: device.userId.toString(),
          previousUserName: user ? (user.name || user.username || '未知') : '未知',
          previousUserEmail: user ? (user.email || '未知') : '未知',
          note: '設備刪除時自動解除用戶綁定'
        });
      } catch (err) {
        console.warn('記錄設備刪除時解除綁定事件失敗:', err);
        // 繼續刪除流程，不因事件記錄失敗而中斷
      }
    }

    // 刪除設備
    const result = await collection.deleteOne(query);

    if (result.deletedCount === 0) {
      return res.status(404).json({ error: '設備不存在或不屬於指定門店' });
    }

    // 推送WebSocket事件
    try {
      const deviceUpdate = {
        _id: device._id.toString(),
        macAddress: device.macAddress,
        status: device.status,
        lastSeen: device.lastSeen ? device.lastSeen.toISOString() : null,
        imageUpdateStatus: device.imageUpdateStatus,
        data: device.data,
        updatedFields: []
      };

      // 推送設備CRUD事件（刪除）
      broadcastDeviceCRUD(device.storeId, [deviceUpdate], 'delete');
    } catch (wsError) {
      console.error('推送設備刪除事件失敗:', wsError);
      // 不影響API響應
    }

    res.status(204).send();
  } catch (error) {
    console.error('刪除設備失敗:', error);
    res.status(500).json({ error: '刪除設備失敗' });
  }
});

// 更新設備數據綁定 (需要身份驗證)
router.post('/devices/:id/update-data-bindings', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { storeId } = req.query;
    const { sendToGateway, ...bindingData } = req.body;

    // 驗證必要欄位
    if (!bindingData) {
      return res.status(400).json({ error: '綁定數據不能為空' });
    }

    // 使用設備數據綁定服務更新數據綁定
    // 根據請求中的 sendToGateway 參數決定是否自動發送到網關
    const updatedDevice = await deviceBindingService.updateDeviceDataBindings(
      id,
      bindingData,
      req.user._id.toString(),
      storeId,
      {
        // 如果 sendToGateway 參數存在且為 false，則不發送到網關
        // 否則默認為 true
        sendToGateway: sendToGateway !== false
      }
    );

    res.json({
      success: true,
      device: updatedDevice,
      message: '設備數據綁定更新成功',
      sentToGateway: sendToGateway !== false
    });
  } catch (error) {
    console.error('更新設備數據綁定失敗:', error);
    res.status(500).json({ error: '更新設備數據綁定失敗: ' + error.message });
  }
});

// 同步設備狀態 - 只從數據庫讀取最新狀態，不做模擬更新
router.post('/devices/sync', async (req, res) => {
  try {
    const { storeId } = req.query;
    const { collection } = await getCollection();

    // 構建查詢條件
    const query = {};

    // 如果提供了門店ID，則只同步該門店的設備
    if (storeId) {
      query.storeId = storeId;
    }

    // 僅讀取設備數據，不進行模擬更新
    const devices = await collection.find(query).toArray();

    // 記錄同步操作
    console.log(`執行設備狀態同步，${storeId ? `門店: ${storeId}, ` : ''}共有 ${devices.length} 個設備`);

    res.json({
      success: true,
      message: storeId ? `已同步門店 ${storeId} 的 ${devices.length} 個設備狀態` : `已同步 ${devices.length} 個設備狀態`,
      syncedAt: new Date(),
      devicesCount: devices.length
    });
  } catch (error) {
    console.error('同步設備狀態失敗:', error);
    res.status(500).json({ error: '同步設備狀態失敗' });
  }
});

// 導出路由器和初始化函數
// 設備綁定用戶 API (需要身份驗證)
router.post('/devices/:id/bind-user', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: '用戶ID不能為空' });
    }

    const updatedDevice = await bindDeviceToUser(id, userId);

    res.json({
      success: true,
      device: updatedDevice
    });
  } catch (error) {
    console.error('綁定設備到用戶失敗:', error);
    res.status(500).json({ error: '綁定設備到用戶失敗: ' + error.message });
  }
});

// 設備解除綁定 API (需要身份驗證)
router.post('/devices/:id/unbind-user', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    const updatedDevice = await unbindDeviceFromUser(id);

    res.json({
      success: true,
      device: updatedDevice
    });
  } catch (error) {
    console.error('解除設備綁定失敗:', error);
    res.status(500).json({ error: '解除設備綁定失敗: ' + error.message });
  }
});

// 設置設備主要網關 API (需要身份驗證)
router.post('/devices/:id/set-primary-gateway', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { gatewayId } = req.body;

    if (!gatewayId) {
      return res.status(400).json({ error: '網關ID不能為空' });
    }

    const updatedDevice = await setDevicePrimaryGateway(id, gatewayId);

    res.json({
      success: true,
      device: updatedDevice
    });
  } catch (error) {
    console.error('設置設備主要網關失敗:', error);
    res.status(500).json({ error: '設置設備主要網關失敗: ' + error.message });
  }
});

// 設置設備網關選擇模式
const setDeviceGatewayMode = async (deviceId, mode) => {
  if (mode !== 'auto' && mode !== 'manual') {
    throw new Error('無效的網關選擇模式，應為 auto 或 manual');
  }

  // 獲取數據庫連接
  const { collection: deviceCollection } = await getCollection();

  // 檢查設備是否存在
  const device = await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
  if (!device) {
    throw new Error('設備不存在');
  }

  // 準備更新數據
  const updateData = {
    gatewaySelectionMode: mode,
    updatedAt: new Date()
  };

  // 執行更新操作
  await deviceCollection.updateOne(
    { _id: new ObjectId(deviceId) },
    { $set: updateData }
  );

  // 記錄設備網關模式變更事件
  await logDeviceEvent(deviceId, 'setting_changed', {
    action: 'set_gateway_mode',
    oldMode: device.gatewaySelectionMode || 'auto', // 默認為自動
    newMode: mode,
    note: `網關選擇模式已變更為 ${mode === 'auto' ? '自動' : '手動'}`
  });

  // 返回更新後的設備
  return await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
};

// 設置設備網關選擇模式 API (需要身份驗證)
router.post('/devices/:id/set-gateway-mode', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { mode } = req.body;

    if (!mode || (mode !== 'auto' && mode !== 'manual')) {
      return res.status(400).json({ error: '無效的網關選擇模式，應為 auto 或 manual' });
    }

    const updatedDevice = await setDeviceGatewayMode(id, mode);

    res.json(updatedDevice);
  } catch (error) {
    console.error('設置設備網關選擇模式失敗:', error);
    res.status(500).json({ error: '設置設備網關選擇模式失敗: ' + error.message });
  }
});

// 獲取設備詳情，包括網關信息 (需要身份驗證)
router.get('/devices/:id/detail', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { collection } = await getCollection();
    const { db } = await getDbConnection();

    // 獲取設備信息
    const device = await collection.findOne({ _id: new ObjectId(id) });
    if (!device) {
      return res.status(404).json({ error: '設備不存在' });
    }

    // 獲取關聯的主要網關信息
    let primaryGateway = null;
    if (device.primaryGatewayId) {
      primaryGateway = await db.collection('gateways').findOne({ _id: device.primaryGatewayId });
    }

    // 獲取關聯的其他網關信息
    let otherGateways = [];
    if (device.otherGateways && device.otherGateways.length > 0) {
      otherGateways = await db.collection('gateways').find({
        _id: { $in: device.otherGateways }
      }).toArray();
    }

    // 獲取關聯的用戶信息
    let user = null;
    if (device.userId) {
      user = await db.collection('users').findOne(
        { _id: device.userId },
        { projection: { password: 0 } } // 不返回密碼欄位
      );
    }
      // 準備返回的數據
    const deviceDetail = {
      ...device,
      initialized: device.initialized !== undefined ? device.initialized : false,
      primaryGateway: primaryGateway ? {
        _id: primaryGateway._id,
        name: primaryGateway.name,
        macAddress: primaryGateway.macAddress,
        status: primaryGateway.status
      } : null,
      otherGateways: otherGateways.map(gateway => ({
        _id: gateway._id,
        name: gateway.name,
        macAddress: gateway.macAddress,
        status: gateway.status
      })),
      user: user ? {
        _id: user._id,
        username: user.username,
        email: user.email,
        name: user.name
      } : null
    };

    res.json(deviceDetail);
  } catch (error) {
    console.error('獲取設備詳情失敗:', error);
    res.status(500).json({ error: '獲取設備詳情失敗' });
  }
});

// 獲取設備事件記錄 API (需要身份驗證)
router.get('/devices/:id/events', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { limit = 50, skip = 0, eventType } = req.query;

    // 獲取數據庫連接
    const { db } = await getDbConnection();
    const eventCollection = db.collection('deviceEvents');

    // 構建查詢條件
    const query = { deviceId: new ObjectId(id) };

    // 如果指定了事件類型，則添加到查詢條件
    if (eventType) {
      query.eventType = eventType;
    }
      // 查詢設備事件並進行分頁
    const events = await eventCollection.find(query)
      .sort({ timestamp: -1 }) // 按時間降序排列
      .skip(parseInt(skip))
      .limit(parseInt(limit))
      .toArray();

    // 獲取總記錄數
    const total = await eventCollection.countDocuments(query);

    res.json({
      success: true,
      total,
      events
    });
  } catch (error) {
    console.error('獲取設備事件記錄失敗:', error);
    res.status(500).json({ error: '獲取設備事件記錄失敗: ' + error.message });
  }
});

// 從設備的其他網關列表中移除特定網關
const removeOtherGateway = async (deviceId, gatewayId) => {
  // 獲取數據庫連接
  const { collection: deviceCollection } = await getCollection();
  const { db } = await getDbConnection();
  const gatewayCollection = db.collection('gateways');

  // 檢查設備是否存在
  const device = await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
  if (!device) {
    throw new Error('設備不存在');
  }

  // 檢查網關是否存在
  const gateway = await gatewayCollection.findOne({ _id: new ObjectId(gatewayId) });
  if (!gateway) {
    throw new Error('網關不存在');
  }

  // 確保不是移除主要網關
  if (device.primaryGatewayId && device.primaryGatewayId.equals(new ObjectId(gatewayId))) {
    throw new Error('無法移除主要網關，請先將其他網關設為主要網關');
  }

  // 檢查網關是否在其他網關列表中
  const otherGateways = device.otherGateways || [];
  const gatewayObjectId = new ObjectId(gatewayId);
  const isGatewayInList = otherGateways.some(id =>
    (id && id.equals && id.equals(gatewayObjectId)) ||
    (id && id.toString() === gatewayId)
  );

  if (!isGatewayInList) {
    throw new Error('該網關不在設備的其他網關列表中');
  }

  // 從設備的其他網關列表中移除
  await deviceCollection.updateOne(
    { _id: new ObjectId(deviceId) },
    { $pull: { otherGateways: { $in: [gatewayId, gatewayObjectId] } } }
  );

  // 從網關的設備列表中移除
  await gatewayCollection.updateOne(
    { _id: gatewayObjectId },
    { $pull: { devices: new ObjectId(deviceId) } }
  );

  // 記錄設備其他網關移除事件
  await logDeviceEvent(deviceId, 'gateway_removed', {
    gatewayId: gatewayId,
    gatewayName: gateway.name || '未知',
    action: 'remove_other_gateway'
  });

  // 返回更新後的設備
  return await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
};

// 從設備的其他網關列表中移除網關 API (需要身份驗證)
router.post('/devices/:id/remove-other-gateway', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { gatewayId } = req.body;

    if (!gatewayId) {
      return res.status(400).json({ error: '網關ID不能為空' });
    }

    const updatedDevice = await removeOtherGateway(id, gatewayId);

    res.json(updatedDevice);
  } catch (error) {
    console.error('從設備的其他網關列表中移除網關失敗:', error);
    res.status(500).json({ error: '從設備的其他網關列表中移除網關失敗: ' + error.message });
  }
});

// 發送設備預覽圖到網關 API (需要身份驗證)
router.post('/devices/:id/send-preview', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const { sendToAllGateways = false } = req.body;

    // 檢查設備是否存在
    const { collection } = await getCollection();
    const device = await collection.findOne({ _id: new ObjectId(id) });

    if (!device) {
      return res.status(404).json({ error: '設備不存在' });
    }

    // 檢查權限
    if (device.userId && !device.userId.equals(req.user._id)) {
      return res.status(403).json({ error: '沒有權限操作此設備' });
    }

    // 發送預覽圖到網關
    const result = await sendPreviewToGateway.sendDevicePreviewToGateway(id, { sendToAllGateways });

    if (result.success) {
      res.json({
        success: true,
        message: '預覽圖已成功發送到網關',
        result
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || '發送預覽圖到網關失敗',
        result
      });
    }
  } catch (error) {
    console.error('發送預覽圖到網關失敗:', error);
    res.status(500).json({ error: '發送預覽圖到網關失敗: ' + error.message });
  }
});

// 批量發送多個設備預覽圖到網關 API (需要身份驗證)
router.post('/devices/send-multiple-previews', authenticate, async (req, res) => {
  try {
    // 從系統配置獲取默認併發數量
    const { getGatewayConcurrency } = require('../utils/sysConfigUtils');
    const defaultConcurrency = await getGatewayConcurrency();

    const {
      deviceIds,
      sendToAllGateways = false,
      concurrency = defaultConcurrency,
      enableSmartSelection = true,
      batchId
    } = req.body;

    if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
      return res.status(400).json({ error: '設備ID列表不能為空' });
    }

    // 檢查所有設備是否存在且用戶有權限
    const { collection } = await getCollection();
    const objectIds = deviceIds.map(id => new ObjectId(id));
    const devices = await collection.find({ _id: { $in: objectIds } }).toArray();

    if (devices.length !== deviceIds.length) {
      return res.status(404).json({
        error: '部分設備不存在',
        foundCount: devices.length,
        requestedCount: deviceIds.length
      });
    }

    // 檢查權限
    const unauthorizedDevices = devices.filter(device =>
      device.userId && !device.userId.equals(req.user._id)
    );

    if (unauthorizedDevices.length > 0) {
      return res.status(403).json({
        error: '沒有權限操作部分設備',
        unauthorizedCount: unauthorizedDevices.length,
        unauthorizedIds: unauthorizedDevices.map(d => d._id.toString())
      });
    }

    // 批量發送預覽圖到網關
    const result = await sendPreviewToGateway.sendMultipleDevicePreviewsToGateways(deviceIds, {
      sendToAllGateways,
      concurrency,
      enableSmartSelection,
      batchId
    });

    if (result.success) {
      res.json({
        success: true,
        message: `已成功發送 ${result.successCount}/${result.totalCount} 個設備的預覽圖到網關`,
        result
      });
    } else {
      res.json({
        success: false,
        error: `部分或全部設備預覽圖發送失敗 (${result.successCount}/${result.totalCount} 成功)`,
        result
      });
    }
  } catch (error) {
    console.error('批量發送預覽圖到網關失敗:', error);
    res.status(500).json({ error: '批量發送預覽圖到網關失敗: ' + error.message });
  }
});

// 導出 API
module.exports = {
  router,
  initDB,
};

// 導出路由
module.exports.deviceApiRouter = router;
