# RLE 算法驗證

## Go 參考實作分析

### 算法邏輯
```go
func (i *ImgConvCommon_t) RunLength(buf []byte) (result []byte) {
	n := len(buf)
	for inx := 0; inx < n; {
		// Check for repeated run
		runLength := 1
		for inx+runLength < n && buf[inx+runLength] == buf[inx] && runLength < 0x7F {
			runLength++
		}
		if runLength >= 2 {
			// Encode repeated run
			result = append(result, byte(runLength)) // bit7 = 0
			result = append(result, buf[inx])
			inx += runLength
		} else {
			// Non-repeated run
			start := inx
			for inx < n && (inx+1 >= n || buf[inx] != buf[inx+1]) && (inx-start) < 0x7F {
				inx++
			}
			length := byte(inx - start)
			result = append(result, 0x80|length) // bit7 = 1
			result = append(result, buf[start:inx]...)
		}
	}
	return
}
```

### 編碼格式
1. **重複序列** (runLength >= 2):
   - 格式: `[runLength, value]`
   - runLength 範圍: 2-127 (0x02-0x7F)
   - bit7 = 0

2. **非重複序列** (runLength = 1 或無重複):
   - 格式: `[0x80|length, data...]`
   - length 範圍: 1-127 (0x01-0x7F)
   - bit7 = 1

**重要說明**:
- bit7 是最高位元 (MSB)
- 壓縮的只有 EPD 像素數據，不包含 ImageInfo 結構 (12 bytes) 頭部
- 不包含 chunk 的 index 資訊

## JavaScript 實作

### 壓縮函數
```javascript
function compressRunLength(buf) {
  const result = [];
  const n = buf.length;
  let inx = 0;
  
  while (inx < n) {
    // 檢查重複序列
    let runLength = 1;
    while (inx + runLength < n && buf[inx + runLength] === buf[inx] && runLength < 0x7F) {
      runLength++;
    }
    
    if (runLength >= 2) {
      // 編碼重複序列：[runLength, value] (bit7 = 0)
      result.push(runLength);
      result.push(buf[inx]);
      inx += runLength;
    } else {
      // 非重複序列
      const start = inx;
      while (inx < n && 
             (inx + 1 >= n || buf[inx] !== buf[inx + 1]) && 
             (inx - start) < 0x7F) {
        inx++;
      }
      const length = inx - start;
      result.push(0x80 | length); // bit7 = 1
      for (let i = start; i < inx; i++) {
        result.push(buf[i]);
      }
    }
  }
  
  return new Uint8Array(result);
}
```

### 解壓縮函數
```javascript
function decompressRunLength(compressedData) {
  const decompressed = [];
  let i = 0;
  
  while (i < compressedData.length) {
    const header = compressedData[i];
    i++;
    
    if ((header & 0x80) === 0) {
      // 重複序列：bit7 = 0
      const runLength = header;
      if (i >= compressedData.length) {
        throw new Error('Incomplete RLE data: missing value byte');
      }
      const value = compressedData[i];
      i++;
      
      for (let j = 0; j < runLength; j++) {
        decompressed.push(value);
      }
    } else {
      // 非重複序列：bit7 = 1
      const length = header & 0x7F;
      if (i + length > compressedData.length) {
        throw new Error('Incomplete RLE data: insufficient data bytes');
      }
      
      for (let j = 0; j < length; j++) {
        decompressed.push(compressedData[i + j]);
      }
      i += length;
    }
  }
  
  return new Uint8Array(decompressed);
}
```

## 測試案例

### 測試案例 1: 簡單重複
```javascript
// 輸入: [0xFF, 0xFF, 0xFF, 0xFF]
// 預期輸出: [0x04, 0xFF]
const input1 = new Uint8Array([0xFF, 0xFF, 0xFF, 0xFF]);
const compressed1 = compressRunLength(input1);
const decompressed1 = decompressRunLength(compressed1);
console.log('Input:', Array.from(input1));
console.log('Compressed:', Array.from(compressed1));
console.log('Decompressed:', Array.from(decompressed1));
console.log('Match:', JSON.stringify(Array.from(input1)) === JSON.stringify(Array.from(decompressed1)));
```

### 測試案例 2: 非重複序列
```javascript
// 輸入: [0x01, 0x02, 0x03, 0x04]
// 預期輸出: [0x84, 0x01, 0x02, 0x03, 0x04]
const input2 = new Uint8Array([0x01, 0x02, 0x03, 0x04]);
const compressed2 = compressRunLength(input2);
const decompressed2 = decompressRunLength(compressed2);
console.log('Input:', Array.from(input2));
console.log('Compressed:', Array.from(compressed2));
console.log('Decompressed:', Array.from(decompressed2));
console.log('Match:', JSON.stringify(Array.from(input2)) === JSON.stringify(Array.from(decompressed2)));
```

### 測試案例 3: 混合模式
```javascript
// 輸入: [0x00, 0x00, 0x01, 0x02, 0xFF, 0xFF, 0xFF]
// 預期輸出: [0x02, 0x00, 0x82, 0x01, 0x02, 0x03, 0xFF]
const input3 = new Uint8Array([0x00, 0x00, 0x01, 0x02, 0xFF, 0xFF, 0xFF]);
const compressed3 = compressRunLength(input3);
const decompressed3 = decompressRunLength(compressed3);
console.log('Input:', Array.from(input3));
console.log('Compressed:', Array.from(compressed3));
console.log('Decompressed:', Array.from(decompressed3));
console.log('Match:', JSON.stringify(Array.from(input3)) === JSON.stringify(Array.from(decompressed3)));
```

### 測試案例 4: 單字節（邊界情況）
```javascript
// 輸入: [0x42]
// 預期輸出: [0x81, 0x42]
const input4 = new Uint8Array([0x42]);
const compressed4 = compressRunLength(input4);
const decompressed4 = decompressRunLength(compressed4);
console.log('Input:', Array.from(input4));
console.log('Compressed:', Array.from(compressed4));
console.log('Decompressed:', Array.from(decompressed4));
console.log('Match:', JSON.stringify(Array.from(input4)) === JSON.stringify(Array.from(decompressed4)));
```

### 測試案例 5: 最大重複長度
```javascript
// 輸入: 127 個 0x55
// 預期輸出: [0x7F, 0x55]
const input5 = new Uint8Array(127).fill(0x55);
const compressed5 = compressRunLength(input5);
const decompressed5 = decompressRunLength(compressed5);
console.log('Input length:', input5.length);
console.log('Compressed:', Array.from(compressed5));
console.log('Decompressed length:', decompressed5.length);
console.log('Match:', JSON.stringify(Array.from(input5)) === JSON.stringify(Array.from(decompressed5)));
```

### 測試案例 6: 超過最大重複長度
```javascript
// 輸入: 130 個 0xAA
// 預期輸出: [0x7F, 0xAA, 0x03, 0xAA]
const input6 = new Uint8Array(130).fill(0xAA);
const compressed6 = compressRunLength(input6);
const decompressed6 = decompressRunLength(compressed6);
console.log('Input length:', input6.length);
console.log('Compressed:', Array.from(compressed6));
console.log('Decompressed length:', decompressed6.length);
console.log('Match:', JSON.stringify(Array.from(input6)) === JSON.stringify(Array.from(decompressed6)));
```

## 壓縮效果分析

### EPD 典型數據模式
1. **大面積單色**: 壓縮比可達 50:1
2. **文字內容**: 壓縮比約 2:1 到 5:1
3. **複雜圖像**: 可能無壓縮效果或輕微膨脹
4. **混合內容**: 壓縮比約 1.5:1 到 3:1

### 性能特點
- **壓縮速度**: 線性時間複雜度 O(n)
- **記憶體使用**: 最壞情況下膨脹至 1.6 倍原始大小
- **適用場景**: 有重複模式的數據，特別是 EPD 圖像

## 實作注意事項

### 1. 邊界檢查
- 確保不會超出緩衝區邊界
- 處理空輸入和單字節輸入
- 驗證壓縮數據的完整性

### 2. 性能優化
- 使用 TypedArray 提高性能
- 避免頻繁的數組擴展操作
- 考慮使用預分配的緩衝區

### 3. 錯誤處理
- 檢測不完整的壓縮數據
- 處理格式錯誤的輸入
- 提供清晰的錯誤信息

### 4. 兼容性
- 確保與 Go 版本的輸出完全一致
- 支援所有可能的輸入模式
- 處理邊界情況和特殊值

## 驗證清單

- [ ] 簡單重複序列正確壓縮和解壓縮
- [ ] 非重複序列正確處理
- [ ] 混合模式數據正確處理
- [ ] 邊界情況（空輸入、單字節）正確處理
- [ ] 最大長度限制正確實作
- [ ] 超過最大長度的數據正確分割
- [ ] 壓縮和解壓縮結果完全一致
- [ ] 與 Go 版本輸出格式完全相同
- [ ] 錯誤情況正確檢測和處理
- [ ] 性能符合預期要求
