# 系統配置化實作計畫

## 📋 概述

基於重新評估的安全分析，實作系統配置化功能，讓 Token 過期時間、密鑰管理等安全設定可透過 Web 介面管理。

---

## 🎯 實作目標

### 1. Token 過期時間系統化配置
- 將硬編碼的過期時間改為資料庫配置
- 提供 Web 介面進行調整
- 支援即時生效，無需重啟

### 2. 密鑰管理自動化
- 首次啟動自動生成密鑰
- Web 介面一鍵刷新密鑰
- 密鑰變更的優雅處理

### 3. 安全設定集中管理
- 統一的安全配置介面
- 操作日誌記錄
- 權限控制

---

## 🏗️ 實作架構

### 1. 資料庫結構設計

#### 系統配置集合 (system_configs)
```javascript
{
  _id: "security_settings",
  category: "security",
  settings: {
    tokenExpiry: {
      user: "2h",           // 用戶 Token
      websocket: "4h",      // WebSocket Token  
      gateway: "24h",       // Gateway Token
      refresh: "7d"         // 刷新 Token
    },
    autoRefresh: {
      enabled: true,
      threshold: "5m"       // 過期前 5 分鐘自動刷新
    },
    security: {
      maxFailedLogins: 5,
      lockoutDuration: "15m",
      passwordPolicy: {
        minLength: 8,
        requireSpecialChar: true
      }
    }
  },
  lastUpdated: ISODate(),
  updatedBy: ObjectId("admin_user_id"),
  version: 1
}
```

#### 安全事件日誌 (security_logs)
```javascript
{
  _id: ObjectId(),
  timestamp: ISODate(),
  event: "JWT_SECRET_REFRESHED",
  category: "key_management",
  details: {
    adminUserId: ObjectId(),
    ip: "*************",
    userAgent: "Mozilla/5.0...",
    success: true
  },
  severity: "info"
}
```

### 2. 後端 API 設計

#### 配置管理 API (`server/routes/securityConfigApi.js`)
```javascript
const express = require('express');
const router = express.Router();
const { authenticate, checkPermission } = require('../middleware/auth');
const SecurityConfigService = require('../services/securityConfigService');

// 獲取安全配置
router.get('/security/config', 
  authenticate,
  checkPermission(['system:security:read']),
  async (req, res) => {
    try {
      const config = await SecurityConfigService.getSecurityConfig();
      res.json(config);
    } catch (error) {
      res.status(500).json({ error: '獲取配置失敗' });
    }
  }
);

// 更新 Token 過期配置
router.put('/security/config/token-expiry',
  authenticate,
  checkPermission(['system:security:write']),
  async (req, res) => {
    try {
      const { userExpiry, gatewayExpiry, websocketExpiry } = req.body;
      
      // 驗證過期時間格式
      const validExpiry = /^(\d+[smhd])$/;
      if (!validExpiry.test(userExpiry)) {
        return res.status(400).json({ error: '無效的過期時間格式' });
      }
      
      await SecurityConfigService.updateTokenExpiry({
        user: userExpiry,
        gateway: gatewayExpiry,
        websocket: websocketExpiry
      }, req.user._id);
      
      res.json({ message: 'Token 過期配置已更新' });
    } catch (error) {
      res.status(500).json({ error: '更新配置失敗' });
    }
  }
);

// 刷新 JWT 密鑰
router.post('/security/refresh-jwt-secret',
  authenticate,
  checkPermission(['system:security:admin']),
  async (req, res) => {
    try {
      const result = await SecurityConfigService.refreshJwtSecret(req.user._id, req.ip);
      
      res.json({
        message: '密鑰已刷新，建議通知用戶重新登入',
        timestamp: result.timestamp,
        affectedTokens: result.affectedTokens
      });
    } catch (error) {
      res.status(500).json({ error: '密鑰刷新失敗' });
    }
  }
);

// 獲取安全事件日誌
router.get('/security/logs',
  authenticate, 
  checkPermission(['system:security:audit']),
  async (req, res) => {
    try {
      const { page = 1, limit = 50, category } = req.query;
      const logs = await SecurityConfigService.getSecurityLogs({
        page: parseInt(page),
        limit: parseInt(limit),
        category
      });
      
      res.json(logs);
    } catch (error) {
      res.status(500).json({ error: '獲取日誌失敗' });
    }
  }
);

module.exports = router;
```

### 3. 服務層實作

#### 安全配置服務 (`server/services/securityConfigService.js`)
```javascript
const KeyManager = require('../utils/keyManager');

class SecurityConfigService {
  constructor() {
    this.configCollection = 'system_configs';
    this.logsCollection = 'security_logs';
  }
  
  async getSecurityConfig() {
    const { db } = await getDbConnection();
    const config = await db.collection(this.configCollection)
      .findOne({ _id: 'security_settings' });
    
    if (!config) {
      // 返回預設配置
      return this.getDefaultConfig();
    }
    
    return config.settings;
  }
  
  async updateTokenExpiry(expirySettings, adminUserId) {
    const { db } = await getDbConnection();
    
    const updateDoc = {
      $set: {
        'settings.tokenExpiry': expirySettings,
        lastUpdated: new Date(),
        updatedBy: adminUserId
      },
      $inc: { version: 1 }
    };
    
    await db.collection(this.configCollection).updateOne(
      { _id: 'security_settings' },
      updateDoc,
      { upsert: true }
    );
    
    // 記錄安全事件
    await this.logSecurityEvent('TOKEN_EXPIRY_UPDATED', {
      adminUserId,
      newSettings: expirySettings
    });
    
    // 通知其他服務配置已更新
    this.notifyConfigUpdate('tokenExpiry', expirySettings);
  }
  
  async refreshJwtSecret(adminUserId, ip) {
    // 使用 KeyManager 刷新密鑰
    const newSecret = KeyManager.refreshSecret(adminUserId);
    
    // 統計受影響的 Token 數量
    const affectedTokens = await this.countActiveTokens();
    
    // 記錄安全事件
    await this.logSecurityEvent('JWT_SECRET_REFRESHED', {
      adminUserId,
      ip,
      affectedTokens,
      timestamp: new Date()
    });
    
    return {
      timestamp: new Date(),
      affectedTokens
    };
  }
  
  async logSecurityEvent(event, details) {
    const { db } = await getDbConnection();
    
    const logEntry = {
      timestamp: new Date(),
      event,
      category: this.getCategoryByEvent(event),
      details,
      severity: this.getSeverityByEvent(event)
    };
    
    await db.collection(this.logsCollection).insertOne(logEntry);
  }
  
  async getSecurityLogs(options = {}) {
    const { db } = await getDbConnection();
    const { page = 1, limit = 50, category } = options;
    
    const query = category ? { category } : {};
    const skip = (page - 1) * limit;
    
    const logs = await db.collection(this.logsCollection)
      .find(query)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();
    
    const total = await db.collection(this.logsCollection).countDocuments(query);
    
    return {
      logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }
  
  getDefaultConfig() {
    return {
      tokenExpiry: {
        user: '2h',
        websocket: '4h',
        gateway: '24h',
        refresh: '7d'
      },
      autoRefresh: {
        enabled: true,
        threshold: '5m'
      },
      security: {
        maxFailedLogins: 5,
        lockoutDuration: '15m'
      }
    };
  }
  
  getCategoryByEvent(event) {
    const categories = {
      'JWT_SECRET_REFRESHED': 'key_management',
      'TOKEN_EXPIRY_UPDATED': 'configuration',
      'LOGIN_FAILED': 'authentication',
      'UNAUTHORIZED_ACCESS': 'security_violation'
    };
    return categories[event] || 'general';
  }
  
  getSeverityByEvent(event) {
    const severities = {
      'JWT_SECRET_REFRESHED': 'warning',
      'TOKEN_EXPIRY_UPDATED': 'info',
      'LOGIN_FAILED': 'warning',
      'UNAUTHORIZED_ACCESS': 'error'
    };
    return severities[event] || 'info';
  }
  
  notifyConfigUpdate(configType, newValue) {
    // 透過 WebSocket 通知前端配置已更新
    const { broadcastSystemUpdate } = require('./websocketService');
    broadcastSystemUpdate('config_updated', {
      type: configType,
      value: newValue,
      timestamp: new Date()
    });
  }
}

module.exports = new SecurityConfigService();
```

---

## 🎨 前端介面設計

### 1. 安全設定頁面結構
```
系統管理 > 安全設定
├── Token 配置
│   ├── 用戶 Token 過期時間
│   ├── Gateway Token 過期時間
│   └── WebSocket Token 過期時間
├── 密鑰管理
│   ├── 當前密鑰狀態
│   ├── 刷新密鑰按鈕
│   └── 密鑰變更歷史
└── 安全日誌
    ├── 事件列表
    ├── 篩選功能
    └── 匯出功能
```

### 2. React 組件實作
```typescript
// src/pages/admin/SecuritySettings.tsx
import React, { useState, useEffect } from 'react';
import { Card, Form, Button, Input, Select, Table, message } from 'antd';

const SecuritySettings: React.FC = () => {
  const [tokenConfig, setTokenConfig] = useState({
    user: '2h',
    gateway: '24h', 
    websocket: '4h'
  });
  
  const [loading, setLoading] = useState(false);
  
  const handleUpdateTokenConfig = async (values: any) => {
    setLoading(true);
    try {
      await fetch('/api/admin/security/config/token-expiry', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(values)
      });
      
      message.success('Token 配置已更新');
    } catch (error) {
      message.error('更新失敗');
    } finally {
      setLoading(false);
    }
  };
  
  const handleRefreshJwtSecret = async () => {
    const confirmed = window.confirm(
      '刷新 JWT 密鑰將使所有現有 Token 失效，用戶需要重新登入。確定要繼續嗎？'
    );
    
    if (!confirmed) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/admin/security/refresh-jwt-secret', {
        method: 'POST',
        credentials: 'include'
      });
      
      const data = await response.json();
      message.success(`密鑰已刷新，影響 ${data.affectedTokens} 個 Token`);
    } catch (error) {
      message.error('密鑰刷新失敗');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="security-settings">
      <Card title="Token 過期配置" className="mb-4">
        <Form
          initialValues={tokenConfig}
          onFinish={handleUpdateTokenConfig}
          layout="vertical"
        >
          <Form.Item label="用戶 Token 過期時間" name="user">
            <Select>
              <Select.Option value="1h">1 小時</Select.Option>
              <Select.Option value="2h">2 小時</Select.Option>
              <Select.Option value="4h">4 小時</Select.Option>
              <Select.Option value="8h">8 小時</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item label="Gateway Token 過期時間" name="gateway">
            <Select>
              <Select.Option value="12h">12 小時</Select.Option>
              <Select.Option value="24h">24 小時</Select.Option>
              <Select.Option value="48h">48 小時</Select.Option>
              <Select.Option value="7d">7 天</Select.Option>
            </Select>
          </Form.Item>
          
          <Button type="primary" htmlType="submit" loading={loading}>
            更新配置
          </Button>
        </Form>
      </Card>
      
      <Card title="密鑰管理" className="mb-4">
        <div className="mb-4">
          <p>當前密鑰狀態: <span className="text-green-600">正常</span></p>
          <p>上次更新時間: 2024-12-19 10:30:00</p>
        </div>
        
        <Button 
          type="primary" 
          danger 
          onClick={handleRefreshJwtSecret}
          loading={loading}
        >
          刷新 JWT 密鑰
        </Button>
      </Card>
    </div>
  );
};

export default SecuritySettings;
```

---

## 📋 實作步驟

### 階段一: 基礎架構 (1週)
1. [ ] 創建 KeyManager 工具類
2. [ ] 實作系統配置資料庫結構
3. [ ] 建立 SecurityConfigService 服務
4. [ ] 創建安全配置 API 端點

### 階段二: Web 介面 (1週)  
1. [ ] 設計安全設定頁面
2. [ ] 實作 Token 配置表單
3. [ ] 建立密鑰管理介面
4. [ ] 添加安全日誌查看功能

### 階段三: 整合測試 (3天)
1. [ ] 測試配置更新功能
2. [ ] 驗證密鑰刷新機制
3. [ ] 檢查權限控制
4. [ ] 進行安全性測試

---

## ✅ 驗收標準

1. **功能完整性**
   - Token 過期時間可透過 Web 介面調整
   - 密鑰可一鍵刷新且記錄操作日誌
   - 配置變更即時生效，無需重啟

2. **安全性**
   - 所有操作需要適當權限
   - 敏感操作有確認機制
   - 完整的操作審計日誌

3. **用戶體驗**
   - 介面直觀易用
   - 操作回饋明確
   - 錯誤處理完善

這個實作計畫將大幅提升系統的安全管理便利性和靈活性。
