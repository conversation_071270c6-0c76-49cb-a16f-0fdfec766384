import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Calendar, Clock, Activity, AlertCircle, CheckCircle, Pause, BarChart3, Grid3X3 } from 'lucide-react';
import { RefreshPlan } from '../types/refreshPlan';
import { refreshPlanApi, formatExecuteTime } from '../services/refreshPlanApi';
import { subscribeToRefreshPlanUpdate, RefreshPlanUpdateEvent } from '../utils/websocketClient';
import { Store } from '../types/store';

interface RefreshPlanTimelineProps {
  store: Store;
}

interface TimelineDay {
  date: Date;
  plans: RefreshPlan[];
  isToday: boolean;
  isWeekend: boolean;
}

export const RefreshPlanTimeline: React.FC<RefreshPlanTimelineProps> = ({ store }) => {
  const [currentStartDate, setCurrentStartDate] = useState(new Date());
  const [plans, setPlans] = useState<RefreshPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hoveredPlan, setHoveredPlan] = useState<RefreshPlan | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [viewDays, setViewDays] = useState(14); // 顯示天數

  // 載入刷圖計畫
  const loadPlans = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await refreshPlanApi.getPlans(store.id, {
        page: 1,
        limit: 1000,
      });

      if (response.success) {
        setPlans(response.data.plans);
      }
    } catch (err) {
      setError('載入刷圖計畫失敗');
      console.error('載入刷圖計畫失敗:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (store?.id) {
      loadPlans();
    }
  }, [store?.id]);

  // WebSocket 即時更新
  useEffect(() => {
    if (!store?.id) return;

    const handleRefreshPlanUpdate = (event: RefreshPlanUpdateEvent) => {
      if (event.storeId === store.id) {
        console.log('時間軸收到刷圖計畫更新事件:', event);
        
        if (event.updateType === 'delete') {
          setPlans(prevPlans => prevPlans.filter(plan => plan._id !== event.planId));
        } else if (event.updateType === 'create') {
          loadPlans();
        } else if (event.updateType === 'status_change' || event.updateType === 'update') {
          setPlans(prevPlans =>
            prevPlans.map(plan => {
              if (plan._id !== event.planId) {
                return plan;
              }
              
              return {
                ...plan,
                ...event.planData,
                _id: event.planId
              };
            })
          );
        }
      }
    };

    const unsubscribe = subscribeToRefreshPlanUpdate(
      store.id,
      handleRefreshPlanUpdate,
      {}
    );

    return () => {
      unsubscribe();
    };
  }, [store?.id]);

  // 生成時間軸天數
  const generateTimelineDays = (): TimelineDay[] => {
    const days: TimelineDay[] = [];
    const today = new Date();
    
    for (let i = 0; i < viewDays; i++) {
      const date = new Date(currentStartDate);
      date.setDate(currentStartDate.getDate() + i);
      
      const isToday = date.toDateString() === today.toDateString();
      const isWeekend = date.getDay() === 0 || date.getDay() === 6;
      
      const dayPlans = getPlansForDate(date);
      
      days.push({
        date,
        plans: dayPlans,
        isToday,
        isWeekend,
      });
    }
    
    return days;
  };

  // 獲取指定日期的計畫
  const getPlansForDate = (date: Date): RefreshPlan[] => {
    return plans.filter(plan => {
      if (!plan.enabled) return false;
      
      const { trigger } = plan;
      
      switch (trigger.type) {
        case 'once':
          if (trigger.executeDate) {
            const executeDate = new Date(trigger.executeDate);
            return executeDate.toDateString() === date.toDateString();
          }
          return false;
          
        case 'daily':
          return true;
          
        case 'weekly':
          if (trigger.weekDays && trigger.weekDays.length > 0) {
            return trigger.weekDays.includes(date.getDay());
          }
          return false;
          
        default:
          return false;
      }
    });
  };

  // 獲取計畫狀態顏色
  const getPlanStatusColor = (plan: RefreshPlan): string => {
    if (!plan.enabled) return 'bg-gray-400';
    
    switch (plan.status) {
      case 'running':
        return 'bg-blue-500';
      case 'active':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'inactive':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  // 獲取計畫狀態圖示
  const getPlanStatusIcon = (plan: RefreshPlan) => {
    if (!plan.enabled) return <Pause size={12} />;
    
    switch (plan.status) {
      case 'running':
        return <Activity size={12} className="animate-pulse" />;
      case 'active':
        return <CheckCircle size={12} />;
      case 'error':
        return <AlertCircle size={12} />;
      case 'inactive':
        return <Pause size={12} />;
      default:
        return <Clock size={12} />;
    }
  };

  // 時間軸導航
  const navigateTimeline = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentStartDate);
    const offset = direction === 'prev' ? -7 : 7;
    newDate.setDate(newDate.getDate() + offset);
    setCurrentStartDate(newDate);
  };

  // 回到今天
  const goToToday = () => {
    setCurrentStartDate(new Date());
  };

  const timelineDays = generateTimelineDays();

  if (loading) {
    return (
      <div className="relative mb-6">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-100/80 via-purple-50/60 to-pink-100/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>
        <div className="relative p-6">
          <div className="flex items-center justify-center h-32">
            <div className="text-gray-500">載入時間軸中...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="relative mb-6">
        <div className="absolute inset-0 bg-gradient-to-br from-red-100/80 via-pink-50/60 to-red-100/80 backdrop-blur-sm rounded-2xl border border-red-200/50 shadow-xl"></div>
        <div className="relative p-6">
          <div className="flex items-center justify-center h-32">
            <div className="text-red-600">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative mb-6">
      {/* 玻璃效果背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-100/80 via-purple-50/60 to-pink-100/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>
      
      {/* 動態光效背景 */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-2xl animate-pulse opacity-50"></div>
      
      {/* 內容區域 */}
      <div className="relative p-6">
        {/* 標題和控制項 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-lg flex items-center justify-center shadow-lg">
              <BarChart3 className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-lg font-bold text-gray-800">刷圖計畫時間軸</h3>
          </div>
          
          <div className="flex items-center gap-3">
            {/* 視圖天數選擇 */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setViewDays(7)}
                className={`px-3 py-1 rounded-lg text-sm transition-all duration-200 ${
                  viewDays === 7 
                    ? 'bg-indigo-500 text-white shadow-md' 
                    : 'bg-white/50 text-gray-600 hover:bg-white/70'
                }`}
              >
                7天
              </button>
              <button
                onClick={() => setViewDays(14)}
                className={`px-3 py-1 rounded-lg text-sm transition-all duration-200 ${
                  viewDays === 14 
                    ? 'bg-indigo-500 text-white shadow-md' 
                    : 'bg-white/50 text-gray-600 hover:bg-white/70'
                }`}
              >
                14天
              </button>
              <button
                onClick={() => setViewDays(30)}
                className={`px-3 py-1 rounded-lg text-sm transition-all duration-200 ${
                  viewDays === 30 
                    ? 'bg-indigo-500 text-white shadow-md' 
                    : 'bg-white/50 text-gray-600 hover:bg-white/70'
                }`}
              >
                30天
              </button>
            </div>
            
            {/* 導航控制 */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => navigateTimeline('prev')}
                className="p-2 rounded-lg bg-white/50 border border-white/30 hover:bg-white/70 transition-all duration-200 shadow-sm"
              >
                <ChevronLeft size={16} className="text-gray-600" />
              </button>
              
              <button
                onClick={goToToday}
                className="px-3 py-2 rounded-lg bg-white/50 border border-white/30 hover:bg-white/70 transition-all duration-200 shadow-sm text-sm font-medium text-gray-700"
              >
                今天
              </button>
              
              <button
                onClick={() => navigateTimeline('next')}
                className="p-2 rounded-lg bg-white/50 border border-white/30 hover:bg-white/70 transition-all duration-200 shadow-sm"
              >
                <ChevronRight size={16} className="text-gray-600" />
              </button>
            </div>
          </div>
        </div>

        {/* 時間軸 */}
        <div className="overflow-x-auto">
          <div className="flex gap-4 min-w-max pb-4">
            {timelineDays.map((day, index) => (
              <div
                key={day.date.toISOString()}
                className={`
                  flex-shrink-0 w-24 relative
                  ${day.isToday ? 'transform scale-105' : ''}
                `}
              >
                {/* 日期標題 */}
                <div className={`
                  text-center mb-3 p-2 rounded-lg
                  ${day.isToday 
                    ? 'bg-indigo-500 text-white shadow-lg' 
                    : day.isWeekend 
                      ? 'bg-red-100 text-red-700' 
                      : 'bg-white/60 text-gray-700'
                  }
                `}>
                  <div className="text-xs font-medium">
                    {day.date.toLocaleDateString('zh-TW', { weekday: 'short' })}
                  </div>
                  <div className="text-sm font-bold">
                    {day.date.getDate()}
                  </div>
                </div>

                {/* 計畫軌道 */}
                <div className="relative min-h-[200px]">
                  {/* 背景軌道線 */}
                  <div className={`
                    absolute left-1/2 transform -translate-x-1/2 w-1 h-full rounded-full
                    ${day.plans.length > 0 ? 'bg-gradient-to-b from-indigo-200 to-purple-200' : 'bg-gray-200'}
                  `}></div>

                  {/* 計畫節點 */}
                  {day.plans.map((plan, planIndex) => (
                    <div
                      key={plan._id}
                      className="absolute left-1/2 transform -translate-x-1/2"
                      style={{
                        top: `${(planIndex * 60) + 20}px`,
                      }}
                      onMouseEnter={(e) => {
                        setHoveredPlan(plan);
                        setMousePosition({ x: e.clientX, y: e.clientY });
                      }}
                      onMouseMove={(e) => {
                        if (hoveredPlan?._id === plan._id) {
                          setMousePosition({ x: e.clientX, y: e.clientY });
                        }
                      }}
                      onMouseLeave={() => setHoveredPlan(null)}
                    >
                      {/* 計畫圓點 */}
                      <div className={`
                        w-6 h-6 rounded-full border-2 border-white shadow-lg cursor-pointer
                        transform transition-all duration-200 hover:scale-125
                        ${getPlanStatusColor(plan)}
                      `}>
                        <div className="w-full h-full flex items-center justify-center text-white">
                          {getPlanStatusIcon(plan)}
                        </div>
                      </div>

                      {/* 計畫名稱標籤 */}
                      <div className={`
                        absolute left-8 top-1/2 transform -translate-y-1/2
                        px-2 py-1 rounded text-xs text-white shadow-sm
                        ${getPlanStatusColor(plan)}
                        whitespace-nowrap max-w-20 truncate
                      `}>
                        {plan.name}
                      </div>
                    </div>
                  ))}

                  {/* 無計畫提示 */}
                  {day.plans.length === 0 && (
                    <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <div className="w-4 h-4 rounded-full bg-gray-300 opacity-50"></div>
                    </div>
                  )}
                </div>

                {/* 計畫數量指示器 */}
                {day.plans.length > 0 && (
                  <div className="text-center mt-2">
                    <span className="inline-block px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full font-medium">
                      {day.plans.length} 個計畫
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 懸停詳情 */}
        {hoveredPlan && (
          <div 
            className="fixed z-50 p-4 bg-white rounded-lg shadow-xl border border-gray-200 max-w-sm pointer-events-none"
            style={{
              left: `${mousePosition.x + 10}px`,
              top: `${mousePosition.y - 10}px`,
              transform: mousePosition.x > window.innerWidth - 350 ? 'translateX(-100%)' : 'none'
            }}
          >
            <div className="flex items-center gap-2 mb-3">
              <div className={`w-3 h-3 rounded-full ${getPlanStatusColor(hoveredPlan)}`}></div>
              <h4 className="font-bold text-gray-800">{hoveredPlan.name}</h4>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Clock size={14} className="text-gray-500" />
                <span className="text-gray-700">{formatExecuteTime(hoveredPlan.trigger)}</span>
              </div>
              
              <div className="flex items-center gap-2">
                {getPlanStatusIcon(hoveredPlan)}
                <span className="text-gray-700 capitalize">{hoveredPlan.status}</span>
              </div>
              
              {hoveredPlan.description && (
                <div className="text-gray-600 mt-2">
                  <span className="font-medium">描述：</span>
                  {hoveredPlan.description}
                </div>
              )}
              
              <div className="flex justify-between text-xs text-gray-500 mt-3 pt-2 border-t">
                <span>優先級: {hoveredPlan.priority}</span>
                <span>
                  成功率: {hoveredPlan.statistics.totalRuns > 0 
                    ? Math.round((hoveredPlan.statistics.successRuns / hoveredPlan.statistics.totalRuns) * 100)
                    : 0}%
                </span>
              </div>
            </div>
          </div>
        )}

        {/* 圖例 */}
        <div className="mt-6 flex flex-wrap gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-gray-600">啟用中</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-gray-600">執行中</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span className="text-gray-600">錯誤</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-gray-400"></div>
            <span className="text-gray-600">停用</span>
          </div>
        </div>
      </div>
    </div>
  );
};
