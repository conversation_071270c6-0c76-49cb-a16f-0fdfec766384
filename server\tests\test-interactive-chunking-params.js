// 測試 test-ws-client-interactive.js 的分片參數功能
console.log('=== 測試 test-ws-client-interactive.js 分片參數功能 ===');

// 模擬 connectWebSocket 函數來測試參數傳遞
async function testConnectWebSocket(gateway, storeId, maxChunkSize = 200, maxSingleMessageSize = 2048, preferredFormat = 'rawdata') {
  console.log('\n📊 測試 connectWebSocket 函數參數:');
  console.log(`  Gateway: ${gateway.name} (${gateway._id})`);
  console.log(`  Store ID: ${storeId}`);
  console.log(`  maxChunkSize: ${maxChunkSize} bytes`);
  console.log(`  maxSingleMessageSize: ${maxSingleMessageSize} bytes`);
  console.log(`  preferredFormat: ${preferredFormat}`);

  // 模擬 gatewayInfo 消息構建
  const gatewayInfoMessage = {
    type: 'gatewayInfo',
    info: {
      macAddress: gateway.macAddress || 'AA:BB:CC:DD:EE:FF',
      model: gateway.model || 'Gateway Model 002',
      wifiFirmwareVersion: gateway.wifiFirmwareVersion || '1.0.0',
      btFirmwareVersion: gateway.btFirmwareVersion || '2.0.0',
      ipAddress: gateway.ipAddress || '*************',

      // 分片傳輸能力支援
      chunkingSupport: {
        enabled: true,
        maxChunkSize: maxChunkSize,
        maxSingleMessageSize: maxSingleMessageSize,
        embeddedIndex: true,
        jsonHeader: true,
        supportedFormat: preferredFormat
      }
    }
  };

  console.log('\n📤 構建的 gatewayInfo 消息:');
  console.log(JSON.stringify(gatewayInfoMessage, null, 2));

  return gatewayInfoMessage;
}

// 測試不同的參數組合
async function runTests() {
  const mockGateway = {
    _id: 'test_gateway_id',
    name: '測試網關',
    macAddress: '00:11:22:33:44:55',
    model: 'Gateway Model 003',
    wifiFirmwareVersion: '1.0.0',
    btFirmwareVersion: '2.0.0',
    ipAddress: '*************'
  };

  const mockStoreId = 'test_store_id';

  console.log('\n🧪 測試案例 1: 預設參數');
  await testConnectWebSocket(mockGateway, mockStoreId);

  console.log('\n🧪 測試案例 2: 小分片 + 小 JSON 限制');
  await testConnectWebSocket(mockGateway, mockStoreId, 20, 500, 'rawdata');

  console.log('\n🧪 測試案例 3: 大分片 + 大 JSON 限制 + 壓縮格式');
  await testConnectWebSocket(mockGateway, mockStoreId, 4096, 10240, 'runlendata');

  console.log('\n🧪 測試案例 4: 中等設定');
  await testConnectWebSocket(mockGateway, mockStoreId, 1024, 2048, 'rawdata');
}

// 測試分片決策邏輯說明
function explainChunkingLogic() {
  console.log('\n📋 分片決策邏輯說明:');
  console.log('');
  console.log('第一階段: rawdata 大小檢查');
  console.log('  - 如果 rawdata 大小 > maxChunkSize → 使用分片傳輸');
  console.log('  - 如果 rawdata 大小 <= maxChunkSize → 進入第二階段');
  console.log('');
  console.log('第二階段: JSON 訊息大小檢查');
  console.log('  - 構建完整的 update_preview JSON 訊息');
  console.log('  - 如果 JSON 訊息大小 > maxSingleMessageSize → 使用分片傳輸');
  console.log('  - 如果 JSON 訊息大小 <= maxSingleMessageSize → 直接傳輸');
  console.log('');
  console.log('測試建議:');
  console.log('  1. 小 maxChunkSize (20) + 任意 maxSingleMessageSize → 測試第一階段');
  console.log('  2. 大 maxChunkSize (1024+) + 小 maxSingleMessageSize (500) → 測試第二階段');
  console.log('  3. 大 maxChunkSize (1024+) + 大 maxSingleMessageSize (2048+) → 測試直接傳輸');
}

// 測試參數驗證
function validateParameters() {
  console.log('\n✅ 參數驗證測試:');
  
  const testCases = [
    { maxChunkSize: 20, maxSingleMessageSize: 500, valid: true, note: '小分片測試' },
    { maxChunkSize: 200, maxSingleMessageSize: 2048, valid: true, note: '標準設定' },
    { maxChunkSize: 1024, maxSingleMessageSize: 500, valid: true, note: '測試第二階段邏輯' },
    { maxChunkSize: 4096, maxSingleMessageSize: 10240, valid: true, note: '高性能設定' },
    { maxChunkSize: 2048, maxSingleMessageSize: 1024, valid: false, note: 'maxSingleMessageSize < maxChunkSize (不建議)' }
  ];

  testCases.forEach((testCase, index) => {
    const status = testCase.valid ? '✅' : '⚠️';
    console.log(`${status} 案例 ${index + 1}: maxChunkSize=${testCase.maxChunkSize}, maxSingleMessageSize=${testCase.maxSingleMessageSize}`);
    console.log(`   ${testCase.note}`);
    
    if (!testCase.valid) {
      console.log(`   建議: maxSingleMessageSize 應該 >= maxChunkSize`);
    }
  });
}

// 執行所有測試
async function main() {
  try {
    await runTests();
    explainChunkingLogic();
    validateParameters();
    
    console.log('\n🎉 所有測試完成！');
    console.log('\n📝 使用 test-ws-client-interactive.js 時:');
    console.log('1. 選擇合適的 maxChunkSize 根據硬體限制');
    console.log('2. 選擇合適的 maxSingleMessageSize 根據網路環境');
    console.log('3. 選擇偏好的 rawdata 格式');
    console.log('4. 觀察服務器日誌中的分片決策過程');
    
  } catch (error) {
    console.error('測試失敗:', error);
  }
}

// 啟動測試
main();
