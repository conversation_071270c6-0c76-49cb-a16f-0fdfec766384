<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EPD 旋轉處理測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .canvas-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .canvas-item {
            text-align: center;
        }
        .canvas-item canvas {
            border: 1px solid #ccc;
            background: white;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>EPD 旋轉處理測試</h1>
        <p>此測試頁面用於驗證預覽圖轉換為EPD格式時的旋轉處理邏輯是否正確。</p>

        <div class="test-section">
            <h3>測試控制</h3>
            <button onclick="runRotationTest()">執行尺寸匹配測試</button>
            <button onclick="runPaddingTest()">執行寬度對齊測試</button>
            <button onclick="clearResults()">清除結果</button>
        </div>

        <div class="test-section">
            <h3>測試畫布</h3>
            <div class="canvas-container" id="canvasContainer">
                <!-- 測試畫布將在這裡動態生成 -->
            </div>
        </div>

        <div class="test-section">
            <h3>測試結果</h3>
            <div id="testResults">
                <p>點擊上方按鈕開始測試...</p>
            </div>
        </div>
    </div>

    <script type="module">
        // 模擬 DisplayColorType 枚舉
        const DisplayColorType = {
            BW: 'Gray16',
            BWR: 'Black & White & Red',
            BWRY: 'Black & White & Red & Yellow'
        };

        // 創建測試用的 Canvas
        function createTestCanvas(width, height, label) {
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;

            const ctx = canvas.getContext('2d');

            // 清除背景為白色
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, width, height);

            // 繪製邊框
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 2;
            ctx.strokeRect(0, 0, width, height);

            // 繪製一個簡單的測試圖案
            ctx.fillStyle = 'black';
            ctx.fillRect(10, 10, 20, 20); // 左上角黑色方塊

            ctx.fillStyle = 'red';
            ctx.fillRect(width - 30, 10, 20, 20); // 右上角紅色方塊

            // 繪製文字標識方向
            ctx.fillStyle = 'blue';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('TOP', width / 2, 25);

            // 繪製尺寸標籤
            ctx.fillStyle = 'green';
            ctx.font = '10px Arial';
            ctx.fillText(`${width}x${height}`, width / 2, height - 10);

            return canvas;
        }

        // 模擬寬度對齊計算
        function calculatePaddedWidth(width, colorType) {
            switch (colorType) {
                case DisplayColorType.BW:
                    return Math.ceil(width / 2) * 2;  // 2的倍數
                case DisplayColorType.BWR:
                    return Math.ceil(width / 8) * 8;  // 8的倍數
                case DisplayColorType.BWRY:
                    return Math.ceil(width / 4) * 4;  // 4的倍數
                default:
                    return width;
            }
        }

        // 模擬旋轉後的尺寸計算
        function getRotatedDimensions(width, height, rotation) {
            if (rotation === 90 || rotation === 270) {
                return { width: height, height: width };
            }
            return { width, height };
        }

        // 執行尺寸匹配測試
        window.runRotationTest = function() {
            const resultsDiv = document.getElementById('testResults');
            const canvasContainer = document.getElementById('canvasContainer');

            resultsDiv.innerHTML = '<h4>尺寸匹配測試結果</h4>';
            canvasContainer.innerHTML = '';

            const testCases = [
                {
                    canvasSize: { width: 128, height: 296 },
                    deviceSize: { width: 128, height: 296 },
                    description: 'Canvas與Device尺寸一致 (直向)'
                },
                {
                    canvasSize: { width: 296, height: 128 },
                    deviceSize: { width: 296, height: 128 },
                    description: 'Canvas與Device尺寸一致 (橫向)'
                },
                {
                    canvasSize: { width: 128, height: 296 },
                    deviceSize: { width: 296, height: 128 },
                    description: 'Canvas直向，Device橫向 (需要旋轉)'
                },
                {
                    canvasSize: { width: 296, height: 128 },
                    deviceSize: { width: 128, height: 296 },
                    description: 'Canvas橫向，Device直向 (需要旋轉)'
                }
            ];

            testCases.forEach(testCase => {
                // 創建原始畫布
                const originalCanvas = createTestCanvas(testCase.canvasSize.width, testCase.canvasSize.height, testCase.description);

                // 判斷是否需要旋轉
                const canvasIsLandscape = testCase.canvasSize.width > testCase.canvasSize.height;
                const deviceIsLandscape = testCase.deviceSize.width > testCase.deviceSize.height;
                const needsRotation = canvasIsLandscape !== deviceIsLandscape;

                // 計算最終尺寸
                let finalWidth, finalHeight;
                if (needsRotation) {
                    finalWidth = testCase.canvasSize.height;
                    finalHeight = testCase.canvasSize.width;
                } else {
                    finalWidth = testCase.canvasSize.width;
                    finalHeight = testCase.canvasSize.height;
                }

                const paddedWidth = calculatePaddedWidth(finalWidth, DisplayColorType.BWR);

                // 顯示畫布
                const canvasItem = document.createElement('div');
                canvasItem.className = 'canvas-item';
                canvasItem.innerHTML = `
                    <div style="font-size: 12px; font-weight: bold;">${needsRotation ? '需要旋轉' : '無需旋轉'}</div>
                    <div style="font-size: 10px; color: #666;">Canvas: ${testCase.canvasSize.width}x${testCase.canvasSize.height}</div>
                    <div style="font-size: 10px; color: #666;">Device: ${testCase.deviceSize.width}x${testCase.deviceSize.height}</div>
                    <div style="font-size: 10px; color: #666;">最終: ${paddedWidth}x${finalHeight}</div>
                `;
                canvasItem.appendChild(originalCanvas);
                canvasContainer.appendChild(canvasItem);

                // 顯示結果
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>${testCase.description}</strong><br>
                    Canvas尺寸: ${testCase.canvasSize.width}x${testCase.canvasSize.height}<br>
                    Device尺寸: ${testCase.deviceSize.width}x${testCase.deviceSize.height}<br>
                    處理方式: ${needsRotation ? '將Canvas旋轉90度' : '無需旋轉'}<br>
                    最終尺寸: ${finalWidth}x${finalHeight}<br>
                    BWR對齊寬度: ${paddedWidth} (8的倍數)<br>
                    <strong>ImageInfo尺寸: ${paddedWidth}x${finalHeight}</strong><br>
                    ✅ 寬度對齊正確: ${finalWidth} → ${paddedWidth}
                `;
                resultsDiv.appendChild(resultDiv);
            });
        };

        // 執行寬度對齊測試
        window.runPaddingTest = function() {
            const resultsDiv = document.getElementById('testResults');
            const canvasContainer = document.getElementById('canvasContainer');

            resultsDiv.innerHTML = '<h4>寬度對齊測試結果</h4>';
            canvasContainer.innerHTML = '';

            const testSizes = [
                { width: 127, height: 296, colorType: DisplayColorType.BWR, expectedPadded: 128 },
                { width: 129, height: 296, colorType: DisplayColorType.BWR, expectedPadded: 136 },
                { width: 295, height: 128, colorType: DisplayColorType.BW, expectedPadded: 296 },
                { width: 297, height: 128, colorType: DisplayColorType.BW, expectedPadded: 298 },
                { width: 125, height: 200, colorType: DisplayColorType.BWRY, expectedPadded: 128 },
                { width: 127, height: 200, colorType: DisplayColorType.BWRY, expectedPadded: 128 }
            ];

            testSizes.forEach((testSize, index) => {
                // 創建測試畫布
                const canvas = createTestCanvas(testSize.width, testSize.height, `測試 ${index + 1}`);
                const actualPadded = calculatePaddedWidth(testSize.width, testSize.colorType);

                // 顯示畫布
                const canvasItem = document.createElement('div');
                canvasItem.className = 'canvas-item';
                canvasItem.innerHTML = `
                    <div>測試 ${index + 1}</div>
                    <div style="font-size: 10px; color: #666;">${testSize.colorType}</div>
                    <div style="font-size: 10px; color: #666;">${testSize.width} → ${actualPadded}</div>
                `;
                canvasItem.appendChild(canvas);
                canvasContainer.appendChild(canvasItem);

                // 顯示結果
                const resultDiv = document.createElement('div');
                const isCorrect = actualPadded === testSize.expectedPadded;
                resultDiv.className = `result ${isCorrect ? 'success' : 'error'}`;
                resultDiv.innerHTML = `
                    <strong>測試案例 ${index + 1}: ${testSize.width}x${testSize.height} (${testSize.colorType})</strong><br>
                    原始寬度: ${testSize.width}<br>
                    期望對齊寬度: ${testSize.expectedPadded}<br>
                    實際對齊寬度: ${actualPadded}<br>
                    ${isCorrect ? '✅ 對齊正確' : '❌ 對齊錯誤'}
                `;
                resultsDiv.appendChild(resultDiv);
            });
        };

        // 清除結果
        window.clearResults = function() {
            document.getElementById('testResults').innerHTML = '<p>點擊上方按鈕開始測試...</p>';
            document.getElementById('canvasContainer').innerHTML = '';
        };
    </script>
</body>
</html>
