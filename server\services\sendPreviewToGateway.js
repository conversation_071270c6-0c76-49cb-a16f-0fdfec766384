// filepath: c:\Users\<USER>\Desktop\code\git\epd-manager-lite\server\services\sendPreviewToGateway.js
/**
 * 發送設備預覽圖到網關的服務
 * 用於將設備中綁定的數據預覽圖發送到對應的主要網關
 */

const { ObjectId } = require('mongodb');
const { getScreenDimensionsMap } = require('../utils/screenConfigs');

// 等待網關變為可用狀態
const waitForGatewayAvailable = async (gatewayId, maxWaitTime = 30000) => {
  const startTime = Date.now();
  const checkInterval = 1000; // 每秒檢查一次

  console.log(`開始等待網關 ${gatewayId} 變為可用，最大等待時間: ${maxWaitTime}ms`);

  while (Date.now() - startTime < maxWaitTime) {
    if (!websocketService.isGatewayBusyWithChunk(gatewayId)) {
      const waitedTime = Date.now() - startTime;
      console.log(`網關 ${gatewayId} 已變為可用，等待時間: ${waitedTime}ms`);
      return true;
    }

    // 等待一段時間後再次檢查
    await new Promise(resolve => setTimeout(resolve, checkInterval));
  }

  const totalWaitTime = Date.now() - startTime;
  console.warn(`網關 ${gatewayId} 等待超時，總等待時間: ${totalWaitTime}ms`);
  return false;
};

// 創建 ObjectId 的安全函數，處理各種輸入類型
const safeObjectId = (id) => {
  if (id instanceof ObjectId) {
    return id;
  }

  // 確保輸入是字符串
  const idStr = String(id);

  if (!ObjectId.isValid(idStr)) {
    throw new Error(`無效的 ObjectId 格式: ${idStr}`);
  }

  return new ObjectId(idStr);
};

// 引入預覽服務
const previewService = require('./previewService');

// 引入 EPD 轉換模組
const epdConversion = require('../utils/epdConversion');

/**
 * 映射設備 colorType 到 DisplayColorType
 */
function mapDeviceColorType(deviceColorType) {
  if (!deviceColorType) return epdConversion.DisplayColorType.BW; // 默認值

  // 如果已經是正確的格式，直接返回
  if (Object.values(epdConversion.DisplayColorType).includes(deviceColorType)) {
    return deviceColorType;
  }

  // 處理簡短代碼
  switch (deviceColorType.toUpperCase()) {
    case 'BW':
    case 'GRAY16':
      return epdConversion.DisplayColorType.BW; // "Gray16"
    case 'BWR':
      return epdConversion.DisplayColorType.BWR; // "Black & White & Red"
    case 'BWRY':
      return epdConversion.DisplayColorType.BWRY; // "Black & White & Red & Yellow"
    default:
      console.warn(`未知的設備顏色類型: ${deviceColorType}，使用默認值`);
      return epdConversion.DisplayColorType.BW;
  }
}

/**
 * 解析設備尺寸
 */
function parseDeviceSize(sizeStr) {
  if (!sizeStr) {
    throw new Error('設備尺寸為空，無法解析');
  }

  // 首先嘗試解析 "寬x高" 格式
  const directMatch = sizeStr.match(/(\d+)x(\d+)/);
  if (directMatch && directMatch.length === 3) {
    return {
      width: parseInt(directMatch[1], 10),
      height: parseInt(directMatch[2], 10)
    };
  }

  // 嘗試解析尺寸標識符（如 "2.9"", "6"" 等）
  const sizeIdentifierMatch = sizeStr.match(/(\d+(?:\.\d+)?)"?/);
  if (sizeIdentifierMatch) {
    const sizeIdentifier = sizeIdentifierMatch[1];

    // 從螢幕配置動態獲取尺寸映射表
    const dimensionsMap = getScreenDimensionsMap();

    if (dimensionsMap[sizeIdentifier]) {
      const dimensions = dimensionsMap[sizeIdentifier];
      console.log(`解析設備尺寸: ${sizeStr} -> ${dimensions.width}x${dimensions.height}`);
      return dimensions;
    }
  }

  throw new Error(`無法解析設備尺寸: ${sizeStr}，請確認尺寸格式正確`);
}

/**
 * 生成 rawdata
 */
async function generateRawData(imageDataStr, device, imageCode, template) {
  try {
    // 創建臨時 Canvas
    const canvas = await epdConversion.createCanvasFromImageData(imageDataStr);

    // 獲取設備的 colorType 和尺寸
    const colorType = mapDeviceColorType(device.data?.colorType);
    const { width, height } = parseDeviceSize(device.data?.size);
    const templateRotation = epdConversion.getTemplateRotation(template);

    console.log(`設備 ${device.macAddress} - 模板旋轉角度: ${templateRotation}°`);

    // 轉換選項
    const options = {
      colorType,
      width,
      height,
      imagecode: parseInt(imageCode, 16), // 將 hex 字符串轉為數字
      x: 0,        // 圖片在設備上的 X 座標
      y: 0,        // 圖片在設備上的 Y 座標
      templateRotation // 模板旋轉角度，轉換時會反向旋轉
    };

    // 執行轉換
    const result = await epdConversion.convertImageToEPDRawData(canvas, options);

    if (result.success) {
      return Array.from(result.rawdata); // 轉為數組便於 JSON 傳輸
    } else {
      console.error('EPD 轉換失敗:', result.error);
      return null;
    }
  } catch (error) {
    console.error('生成 rawdata 時發生錯誤:', error);
    return null;
  }
}

// 共享的資料庫連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

/**
 * 獲取設備集合
 * @returns {Promise<{collection, client}>} 設備集合和資料庫客戶端
 */
const getDeviceCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('devices');
  return { collection, client };
};

/**
 * 獲取網關集合
 * @returns {Promise<{collection, client}>} 網關集合和資料庫客戶端
 */
const getGatewayCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('gateways');
  return { collection, client };
};

/**
 * 獲取模板集合
 * @returns {Promise<{collection, client}>} 模板集合和資料庫客戶端
 */
const getTemplateCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('templates');
  return { collection, client };
};

/**
 * 獲取門店集合
 * @returns {Promise<{collection, client}>} 門店集合和資料庫客戶端
 */
const getStoreCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('stores');
  return { collection, client };
};

/**
 * 獲取資料欄位集合
 * @returns {Promise<{collection, client}>} 資料欄位集合和資料庫客戶端
 */
const getDataFieldCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('dataFields');
  return { collection, client };
};

/**
 * 發送單個設備預覽圖到其主要網關
 * @param {string} deviceId 設備ID
 * @param {Object} options 可選參數
 * @param {boolean} options.sendToAllGateways 是否發送到所有發現此設備的網關
 * @returns {Promise<Object>} 結果
 */
const sendDevicePreviewToGateway = async (deviceId, options = {}) => {
  try {
    // 獲取設備資料
    const { collection: deviceCollection } = await getDeviceCollection();
    const { collection: gatewayCollection } = await getGatewayCollection();

    // 確保 deviceId 是有效的 ObjectId 格式
    if (!deviceId) {
      throw new Error(`設備ID不能為空`);
    }

    let objectId;
    try {
      objectId = safeObjectId(deviceId);
    } catch (error) {
      throw new Error(`無效的設備ID格式: ${deviceId}`);
    }
    const device = await deviceCollection.findOne({ _id: objectId });
    if (!device) {
      throw new Error(`找不到ID為 ${deviceId} 的設備`);
    }

    // 檢查設備是否有綁定的數據
    if (!device.dataBindings && !device.dataId) {
      throw new Error(`設備 ${deviceId} 沒有綁定數據`);
    }

    // 每次發送前都重新生成預覽圖，確保使用最新的數據
    // 不再檢查設備是否有預覽圖，而是始終重新生成
    console.log(`為設備 ${deviceId} 重新生成預覽圖...`);

      // 檢查設備是否有模板和綁定數據
      if (device.templateId && device.dataBindings) {
        try {
          // 獲取模板
          const { collection: templateCollection } = await getTemplateCollection();

          let template;

          // 嘗試使用不同的查詢方式找到模板
          console.log(`嘗試查找模板，設備ID: ${deviceId}, 模板ID: ${device.templateId}, 類型: ${typeof device.templateId}`);

          // 嘗試方法1: 使用 _id 查詢 (如果是有效的 ObjectId)
          if (ObjectId.isValid(device.templateId)) {
            console.log(`模板ID ${device.templateId} 是有效的 ObjectId 格式，使用 _id 查詢`);
            try {
              // 使用安全的 ObjectId 創建函數
              const templateObjectId = safeObjectId(device.templateId);
              template = await templateCollection.findOne({ _id: templateObjectId });
              console.log(`使用 _id 查詢結果: ${template ? '找到模板' : '未找到模板'}`);
            } catch (error) {
              console.warn(`創建模板ObjectId失敗:`, error.message);
            }
          }

          // 嘗試方法2: 使用 id 字段查詢 (字符串形式)
          if (!template) {
            console.log(`嘗試使用 id 字段(字符串)查詢模板: ${device.templateId}`);
            template = await templateCollection.findOne({ id: device.templateId });
            console.log(`使用 id 字段(字符串)查詢結果: ${template ? '找到模板' : '未找到模板'}`);
          }

          // 嘗試方法3: 使用 id 字段查詢 (數字形式)
          if (!template && !isNaN(device.templateId)) {
            const numericId = Number(device.templateId);
            console.log(`嘗試將模板ID轉換為數字: ${numericId}`);
            template = await templateCollection.findOne({ id: numericId });
            console.log(`使用 id 字段(數字)查詢結果: ${template ? '找到模板' : '未找到模板'}`);
          }

          // 嘗試方法4: 使用 templateId 字段查詢
          if (!template) {
            console.log(`嘗試使用 templateId 字段查詢`);
            template = await templateCollection.findOne({ templateId: device.templateId });
            console.log(`使用 templateId 字段查詢結果: ${template ? '找到模板' : '未找到模板'}`);

            // 如果還是找不到，嘗試將 templateId 轉換為數字再查詢
            if (!template && !isNaN(device.templateId)) {
              const numericId = Number(device.templateId);
              template = await templateCollection.findOne({ templateId: numericId });
              console.log(`使用 templateId 字段(數字)查詢結果: ${template ? '找到模板' : '未找到模板'}`);
            }
          }

          // 如果還是找不到，嘗試查詢所有模板並打印出來，幫助診斷
          if (!template) {
            console.log(`無法找到模板，嘗試列出所有模板以診斷問題`);
            const allTemplates = await templateCollection.find({}).limit(10).toArray();
            console.log(`數據庫中的前10個模板:`, JSON.stringify(allTemplates.map(t => ({
              _id: t._id.toString(),
              id: t.id,
              templateId: t.templateId,
              name: t.name
            })), null, 2));

            // 嘗試查詢模板的所有字段名稱
            if (allTemplates.length > 0) {
              console.log(`模板集合中的字段名稱:`, Object.keys(allTemplates[0]));
            }
          }

          if (!template) {
            throw new Error(`找不到設備 ${deviceId} 使用的模板 ${device.templateId}`);
          }

          // 確保模板數據包含必要的屬性
          if (!template.width || !template.height) {
            console.log(`模板 ${device.templateId} 缺少尺寸信息，嘗試從 screenSize 解析...`);

            // 嘗試從 screenSize 解析寬度和高度
            if (template.screenSize) {
              const sizeMatch = template.screenSize.match(/(\d+)x(\d+)/);
              if (sizeMatch && sizeMatch.length === 3) {
                template.width = parseInt(sizeMatch[1], 10);
                template.height = parseInt(sizeMatch[2], 10);
                console.log(`從 screenSize 解析出尺寸: ${template.width}x${template.height}`);
              }
            }

            // 如果仍然沒有尺寸信息，設置默認值
            if (!template.width || !template.height) {
              console.log(`無法從 screenSize 解析尺寸，使用默認值 296x128`);
              template.width = 296;
              template.height = 128;
            }
          }

          // 確保模板有 elements 屬性
          if (!template.elements || !Array.isArray(template.elements)) {
            console.log(`模板 ${device.templateId} 缺少 elements 屬性，初始化為空數組`);
            template.elements = [];
          }

          // 確保模板有 color 屬性
          if (!template.color) {
            console.log(`模板 ${device.templateId} 缺少 color 屬性，設置為默認值 'bwr'`);
            template.color = 'bwr';
          }

          console.log(`處理後的模板數據:`, {
            id: template.id,
            width: template.width,
            height: template.height,
            elementsCount: template.elements.length,
            color: template.color
          });

          // 獲取門店數據
          const { collection: storeCollection } = await getStoreCollection();
          // 查詢設備所屬的門店
          const store = await storeCollection.findOne({ id: device.storeId });

          // 準備門店數據數組
          let storeData = [];

          if (store) {
            // 將門店添加到數組中
            storeData.push(store);

            // 如果門店有 storeSpecificData，確保它可以被正確處理
            if (store.storeSpecificData && Array.isArray(store.storeSpecificData)) {
              console.log(`找到門店 ${device.storeId} 的 storeSpecificData，共 ${store.storeSpecificData.length} 項`);
            } else {
              console.log(`門店 ${device.storeId} 沒有 storeSpecificData 或格式不正確`);
            }
          } else {
            console.log(`找不到設備 ${deviceId} 所屬的門店 ${device.storeId}`);
          }

          // 獲取資料欄位數據
          const { collection: dataFieldCollection } = await getDataFieldCollection();
          const dataFields = await dataFieldCollection.find({}).toArray();
          console.log(`獲取到 ${dataFields.length} 個資料欄位定義`);

          // 檢查資料欄位是否包含 prefix
          const fieldsWithPrefix = dataFields.filter(field => field.prefix);
          console.log(`其中 ${fieldsWithPrefix.length} 個欄位有 prefix 定義`);

          // 輸出一些欄位的 prefix 示例
          if (fieldsWithPrefix.length > 0) {
            console.log('欄位 prefix 示例:');
            fieldsWithPrefix.slice(0, 3).forEach(field => {
              console.log(`- 欄位 ${field.id}: prefix = "${field.prefix}"`);
            });
          }

          // 處理數據綁定 - 確保它是一個對象而不是字符串
          let dataBindings = device.dataBindings;
          if (typeof dataBindings === 'string') {
            try {
              dataBindings = JSON.parse(dataBindings);
              console.log(`成功將數據綁定從字符串解析為對象，字段數: ${Object.keys(dataBindings).length}`);
            } catch (error) {
              console.error(`解析數據綁定字符串失敗:`, error);
              throw new Error(`無法解析數據綁定: ${error.message}`);
            }
          } else if (!dataBindings) {
            // 如果沒有數據綁定，創建一個空對象
            dataBindings = {};
            console.log(`設備沒有數據綁定，使用空對象`);
          }

          // 使用前端完全相同的渲染邏輯生成預覽圖
          console.log(`使用前端完全相同的渲染邏輯為設備 ${deviceId} 生成預覽圖...`);
          console.log(`數據綁定類型: ${typeof dataBindings}, 字段數: ${Object.keys(dataBindings).length}`);

          // 直接使用 regeneratePreviewBeforeSend 函數，與前端 src/utils/previewImageManager.ts 中的完全一致
          // 確保每次發送前都重新生成最新的預覽圖
          const previewData = await previewService.regeneratePreviewBeforeSend(
            {
              _id: deviceId,
              templateId: template.id,
              dataBindings: dataBindings,
              storeId: device.storeId
            },
            storeData,
            template,
            dataFields
          );

          // 如果直接渲染失敗，嘗試使用預覽服務
          if (!previewData) {
            console.log(`直接渲染失敗，嘗試使用預覽服務...`);
            try {
              const servicePreviewData = await previewService.generatePreviewFromService(
                template,
                dataBindings,
                storeData,
                { effectType: 'blackAndWhite', threshold: 128 },
                dataFields
              );

              if (servicePreviewData) {
                console.log(`預覽服務成功生成預覽圖`);
                return servicePreviewData;
              }
            } catch (previewServiceError) {
              console.error(`預覽服務調用失敗，錯誤:`, previewServiceError.message);
              console.log(`預覽服務不可用，無法使用預覽服務生成預覽圖`);
            }

            // 如果直接渲染和預覽服務都失敗，拋出錯誤
            throw new Error(`無法生成預覽圖：直接渲染和預覽服務都失敗了`);
          }

          if (previewData) {
            // 更新設備的預覽圖
            await deviceCollection.updateOne(
              { _id: objectId },
              { $set: { previewImage: previewData } }
            );

            // 更新本地設備對象
            device.previewImage = previewData;
            console.log(`已成功為設備 ${deviceId} 生成預覽圖`);
          } else {
            throw new Error(`預覽服務未能生成預覽圖`);
          }
        } catch (error) {
          console.error(`無法為設備 ${deviceId} 生成預覽圖:`, error);
          throw new Error(`無法生成預覽圖: ${error.message}`);
        }
      } else {
        throw new Error(`設備 ${deviceId} 缺少模板或綁定數據，無法生成預覽圖`);
      }

    // 檢查設備是否有主要網關
    if (!device.primaryGatewayId) {
      throw new Error(`設備 ${deviceId} 沒有主要網關`);
    }

    // 確保 primaryGatewayId 是有效的 ObjectId 對象
    let primaryGatewayObjectId;
    let primaryGatewayId;

    // 處理不同類型的 primaryGatewayId
    if (typeof device.primaryGatewayId === 'string') {
      // 如果是字符串，檢查並轉換為 ObjectId
      if (!ObjectId.isValid(device.primaryGatewayId)) {
        console.log(`警告: 設備 ${deviceId} 的主要網關ID不是有效的ObjectId格式: ${device.primaryGatewayId}`);

        // 嘗試查詢網關集合，看是否有匹配的 id 字段
        const gatewayWithId = await gatewayCollection.findOne({ id: device.primaryGatewayId });
        if (gatewayWithId) {
          console.log(`找到了使用id字段匹配的網關: ${gatewayWithId._id}`);
          primaryGatewayObjectId = gatewayWithId._id;
          primaryGatewayId = device.primaryGatewayId;
        } else {
          throw new Error(`設備 ${deviceId} 的主要網關ID格式無效且找不到匹配的網關: ${device.primaryGatewayId}`);
        }
      } else {
        try {
          // 使用安全的 ObjectId 創建函數
          primaryGatewayObjectId = safeObjectId(device.primaryGatewayId);
          primaryGatewayId = device.primaryGatewayId.toString();
        } catch (error) {
          throw new Error(`設備 ${deviceId} 的主要網關ID格式無效: ${error.message}`);
        }
      }
    } else if (device.primaryGatewayId instanceof ObjectId) {
      // 如果已經是 ObjectId 實例，直接使用
      primaryGatewayObjectId = device.primaryGatewayId;
      primaryGatewayId = device.primaryGatewayId.toString();
    } else {
      console.log(`警告: 設備 ${deviceId} 的主要網關ID類型異常: ${typeof device.primaryGatewayId}, 值: ${device.primaryGatewayId}`);

      // 嘗試將值轉換為字符串，然後檢查是否是有效的 ObjectId
      const gatewayIdStr = String(device.primaryGatewayId);
      if (ObjectId.isValid(gatewayIdStr)) {
        console.log(`成功將異常類型的網關ID轉換為有效的ObjectId字符串: ${gatewayIdStr}`);
        try {
          primaryGatewayObjectId = safeObjectId(gatewayIdStr);
          primaryGatewayId = gatewayIdStr;
        } catch (error) {
          console.warn(`創建網關ObjectId失敗:`, error.message);
        }
      } else {
        // 最後嘗試查詢網關集合
        const gatewayWithId = await gatewayCollection.findOne({ id: gatewayIdStr });
        if (gatewayWithId) {
          console.log(`找到了使用id字段匹配的網關: ${gatewayWithId._id}`);
          primaryGatewayObjectId = gatewayWithId._id;
          primaryGatewayId = gatewayIdStr;
        } else {
          throw new Error(`設備 ${deviceId} 的主要網關ID類型無效且無法轉換: ${typeof device.primaryGatewayId}`);
        }
      }
    }

    // 引入 websocketService
    const websocketService = require('./websocketService');

    // 檢查主要網關是否在線
    if (!websocketService.isGatewayOnline(primaryGatewayId)) {
      throw new Error(`設備 ${deviceId} 的主要網關 ${primaryGatewayId} 不在線`);
    }

    // 獲取網關資料
    const primaryGatewayData = await gatewayCollection.findOne({ _id: primaryGatewayObjectId });
    if (!primaryGatewayData) {
      throw new Error(`找不到ID為 ${primaryGatewayId} 的網關`);
    }
      // 準備發送資訊    // 計算 imageData 的 CRC 驗證碼
    const calculateCRC = (data) => {
      // 簡單的 CRC32 實現，確保與前端計算邏輯一致
      let crc = 0;
      const str = typeof data === 'string' ? data : JSON.stringify(data);
      for(let i = 0; i < str.length; i++) {
        crc = ((crc << 5) + crc) ^ str.charCodeAt(i);
      }
      // 轉換為 4 字節的十六進制字符串
      return (crc >>> 0).toString(16).padStart(8, '0');
    };

    // 準備 imageData
    const imageData = device.previewImage;

    // 檢查 imageData 是否有效
    if (!imageData) {
      throw new Error(`設備 ${deviceId} 的預覽圖數據為空`);
    }

    // 確保 imageData 是字符串
    const imageDataStr = typeof imageData === 'string' ? imageData : JSON.stringify(imageData);

    // 計算 imageCode
    const imageCode = calculateCRC(imageDataStr);

    // 檢查設備當前的 imageCode 是否與新計算的相同
    // 注意：imageCode 存儲在 device.data.imageCode 中
    const currentImageCode = device.data?.imageCode;
    const imageUpdateStatus = device.imageUpdateStatus;

    // 如果設備狀態為"未更新"，則不管 imageCode 是否相同都要發送圖片
    if (imageUpdateStatus !== '未更新' && currentImageCode && currentImageCode === imageCode) {
      console.log(`設備 ${deviceId} 的圖片內容未變化，imageCode 相同: ${imageCode}，且狀態不是"未更新"，跳過發送`);
      return {
        success: true,
        deviceId,
        skipped: true,
        message: '圖片內容未變化，跳過發送',
        timestamp: new Date().toISOString()
      };
    }

    // 如果狀態為"未更新"但 imageCode 相同，記錄日誌
    if (imageUpdateStatus === '未更新' && currentImageCode && currentImageCode === imageCode) {
      console.log(`設備 ${deviceId} 的圖片內容未變化，但狀態為"未更新"，將強制發送圖片`);
    } else {
      console.log(`設備 ${deviceId} 的圖片內容有變化或首次發送，舊 imageCode: ${currentImageCode || '無'}，新 imageCode: ${imageCode}，將發送更新`);
    }

    // 更新設備的 imageCode 並將 imageUpdateStatus 設置為"未更新"
    try {
      // 更新 imageCode 和 imageUpdateStatus
      // 注意：imageCode 存儲在 data.imageCode 中
      await deviceCollection.updateOne(
        { _id: objectId },
        {
          $set: {
            'data.imageCode': imageCode,
            'imageUpdateStatus': '未更新',
            updatedAt: new Date()
          }
        }
      );

      console.log(`已將 imageCode 保存到設備數據中: ${imageCode}，並將 imageUpdateStatus 設置為"未更新"`);
    } catch (error) {
      console.error('保存 imageCode 和 imageUpdateStatus 到設備數據失敗:', error);
      // 繼續執行，不因保存失敗而中斷整個流程
    }

    // 獲取模板信息以確定旋轉角度
    let template = null;
    try {
      const { collection: templateCollection } = await getTemplateCollection();

      // 使用之前已經查找到的模板邏輯
      if (ObjectId.isValid(device.templateId)) {
        const templateObjectId = safeObjectId(device.templateId);
        template = await templateCollection.findOne({ _id: templateObjectId });
      }

      if (!template) {
        template = await templateCollection.findOne({ id: device.templateId });
      }

      if (!template && !isNaN(device.templateId)) {
        const numericId = Number(device.templateId);
        template = await templateCollection.findOne({ id: numericId });
      }
    } catch (templateError) {
      console.warn('獲取模板信息失敗，將不包含 rawdata:', templateError.message);
    }

    // 生成 rawdata
    const rawdata = await generateRawData(imageDataStr, device, imageCode, template);

    // 智能網關選擇邏輯
    const connectedGateways = websocketService.getConnectedGateways();
    let selectedGatewayId = primaryGatewayId;
    let selectedGatewayWs = connectedGateways.get(primaryGatewayId);
    let isUsingBackupGateway = false;
    let backupGatewayReason = '';

    // 檢查是否有強制指定的網關ID（用於任務隊列）
    if (options.forceGatewayId) {
      selectedGatewayId = options.forceGatewayId;
      selectedGatewayWs = connectedGateways.get(options.forceGatewayId);
      isUsingBackupGateway = options.forceGatewayId !== primaryGatewayId;
      if (isUsingBackupGateway) {
        backupGatewayReason = '任務隊列指定使用備用網關';
      }
      console.log(`使用任務隊列指定的網關 ${options.forceGatewayId}${isUsingBackupGateway ? ' (備用)' : ' (主要)'}`);
    } else if (device.gatewaySelectionMode === 'auto') {
      // 如果設備處於自動網關選擇模式，實施智能選擇邏輯
      console.log(`設備 ${deviceId} 處於智能網關選擇模式，檢查主要網關是否忙碌`);

      // 檢查主要網關是否正在進行chunk傳輸
      if (websocketService.isGatewayBusyWithChunk(primaryGatewayId)) {
        console.log(`主要網關 ${primaryGatewayId} 正在進行chunk傳輸，尋找備用網關`);

        // 獲取所有可用的網關（包括主要網關和其他網關）
        const allGatewayIds = [primaryGatewayId];
        if (Array.isArray(device.otherGateways)) {
          // 將其他網關ID轉換為字符串格式
          const otherGatewayIds = device.otherGateways.map(gw => {
            if (typeof gw === 'string') return gw;
            if (gw instanceof ObjectId) return gw.toString();
            return String(gw);
          });
          allGatewayIds.push(...otherGatewayIds);
        }

        // 獲取空閒的網關
        const availableGateways = websocketService.getAvailableGateways(allGatewayIds);
        console.log(`可用的空閒網關: ${availableGateways.join(', ')}`);

        // 選擇第一個空閒的備用網關（排除主要網關）
        const backupGateway = availableGateways.find(gw => gw !== primaryGatewayId);

        if (backupGateway) {
          selectedGatewayId = backupGateway;
          selectedGatewayWs = connectedGateways.get(backupGateway);
          isUsingBackupGateway = true;
          backupGatewayReason = '主要網關正在進行chunk傳輸';
          console.log(`選擇備用網關 ${backupGateway} 進行傳輸`);
        } else {
          console.log(`沒有可用的備用網關，等待主要網關完成chunk傳輸`);
          // 等待主要網關完成當前傳輸
          await waitForGatewayAvailable(primaryGatewayId, 30000); // 最多等待30秒
        }
      } else {
        console.log(`主要網關 ${primaryGatewayId} 空閒，使用主要網關進行傳輸`);
      }
    }

    let primarySendResult;
    let usedGatewayId = selectedGatewayId;
    let usedGatewayName = '';

    // 獲取使用的網關信息
    try {
      const usedGatewayData = await gatewayCollection.findOne({
        _id: safeObjectId(selectedGatewayId)
      });
      usedGatewayName = usedGatewayData?.name || '未知';
    } catch (error) {
      console.warn(`獲取網關 ${selectedGatewayId} 信息失敗:`, error);
    }

    if (selectedGatewayWs) {
      // 生成預先的 chunkId 用於狀態追蹤
      const preChunkId = `pre_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      try {
        // 在開始傳輸前立即標記網關為忙碌（預防性標記）
        console.log(`🔒 預先標記網關 ${selectedGatewayId} 為忙碌，防止併發傳輸`);
        websocketService.startChunkTransmission(selectedGatewayId, preChunkId, device.macAddress);

        // 使用新的分片傳輸功能（包含 imageData 以確保向後兼容）
        await websocketService.sendImageToGateway(
          selectedGatewayWs,
          device.macAddress,
          imageCode,
          Buffer.from(rawdata),
          imageDataStr  // 新增：傳遞 imageData 以確保不支持分片的網關也能收到
        );

        // 傳輸成功，清理預先標記
        console.log(`🔓 清理網關 ${selectedGatewayId} 的預先標記`);
        websocketService.endChunkTransmission(selectedGatewayId, preChunkId);

        const successMessage = isUsingBackupGateway
          ? `圖片已通過備用網關 ${usedGatewayName} 智能傳輸發送 (${backupGatewayReason})`
          : '圖片已通過智能傳輸發送';

        primarySendResult = {
          success: true,
          message: successMessage,
          usedGatewayId,
          isUsingBackupGateway,
          backupGatewayReason
        };
      } catch (error) {
        // 傳輸失敗，也要清理預先標記
        console.log(`🔓 傳輸失敗，清理網關 ${selectedGatewayId} 的預先標記`);
        websocketService.endChunkTransmission(selectedGatewayId, preChunkId);

        console.error(`智能傳輸失敗:`, error);

        // 如果使用的是備用網關且失敗了，嘗試回退到主要網關
        const primaryGatewayWs = connectedGateways.get(primaryGatewayId);
        if (isUsingBackupGateway && primaryGatewayWs) {
          console.log(`備用網關傳輸失敗，回退到主要網關 ${primaryGatewayId}`);
          try {
            await websocketService.sendImageToGateway(
              primaryGatewayWs,
              device.macAddress,
              imageCode,
              Buffer.from(rawdata),
              imageDataStr
            );

            primarySendResult = {
              success: true,
              message: '備用網關失敗，已回退到主要網關智能傳輸發送',
              usedGatewayId: primaryGatewayId,
              isUsingBackupGateway: false,
              backupGatewayFailed: true
            };
            usedGatewayId = primaryGatewayId;
          } catch (fallbackError) {
            console.error(`主要網關回退也失敗，使用傳統方式:`, fallbackError);

            // 最後回退到傳統傳輸方式
            const message = {
              type: 'update_preview',
              deviceMac: device.macAddress,
              imageData: imageDataStr,
              rawdata: rawdata,
              imageCode: imageCode,
              timestamp: new Date().toISOString()
            };

            primarySendResult = await websocketService.sendCommandToGateway(primaryGatewayId, message);
            usedGatewayId = primaryGatewayId;
          }
        } else {
          // 回退到傳統傳輸方式
          const message = {
            type: 'update_preview',
            deviceMac: device.macAddress,
            imageData: imageDataStr,
            rawdata: rawdata,
            imageCode: imageCode,
            timestamp: new Date().toISOString()
          };

          primarySendResult = await websocketService.sendCommandToGateway(selectedGatewayId, message);
        }
      }
    } else {
      // 如果無法獲取WebSocket連接，使用傳統方式
      const message = {
        type: 'update_preview',
        deviceMac: device.macAddress,
        imageData: imageDataStr,
        rawdata: rawdata,
        imageCode: imageCode,
        timestamp: new Date().toISOString()
      };

      primarySendResult = await websocketService.sendCommandToGateway(selectedGatewayId, message);
    }

    // 記錄事件
    try {
      await websocketService.logDeviceEvent(objectId, 'preview_sent', {
        gatewayId: usedGatewayId,
        gatewayName: usedGatewayName,
        success: primarySendResult.success,
        message: primarySendResult.message || primarySendResult.error || '',
        isUsingBackupGateway: primarySendResult.isUsingBackupGateway || false,
        backupGatewayReason: primarySendResult.backupGatewayReason || '',
        originalPrimaryGatewayId: primaryGatewayId
      });
    } catch (logError) {
      console.error(`記錄設備事件失敗:`, logError);
      // 繼續執行，不因記錄失敗而中斷整個流程
    }

    // 如需發送到其他網關
    let otherResults = [];
    // 當使用 forceGatewayId 時（任務隊列模式），不應該發送到其他網關，避免重複發送
    if (options.forceGatewayId) {
      console.log(`🔒 使用強制指定網關 ${options.forceGatewayId}，跳過發送到其他網關以避免重複發送`);
    } else if (options.sendToAllGateways && Array.isArray(device.otherGateways) && device.otherGateways.length > 0) {
      console.log(`🔄 主要網關發送失敗，設備啟用智能網關功能，開始依序嘗試其他網關`);

      // 準備發送到其他網關的消息
      const otherGatewayMessage = {
        type: 'update_preview',
        deviceMac: device.macAddress,
        imageData: imageDataStr,
        rawdata: rawdata,
        imageCode: imageCode,
        timestamp: new Date().toISOString()
      };

      // 依序嘗試每個其他網關，直到成功或全部失敗
      for (const otherGateway of device.otherGateways) {
        try {
          // 確保 otherGateway 是有效的 ObjectId 對象或字符串
          let otherGatewayObjectId;
          let gatewayIdStr;

          if (typeof otherGateway === 'string') {
            // 如果是字符串，檢查並轉換為 ObjectId
            if (!ObjectId.isValid(otherGateway)) {
              console.log(`警告: 其他網關ID不是有效的ObjectId格式: ${otherGateway}`);

              // 嘗試查詢網關集合，看是否有匹配的 id 字段
              const gatewayWithId = await gatewayCollection.findOne({ id: otherGateway });
              if (gatewayWithId) {
                console.log(`找到了使用id字段匹配的其他網關: ${gatewayWithId._id}`);
                otherGatewayObjectId = gatewayWithId._id;
                gatewayIdStr = otherGateway;
              } else {
                console.warn(`跳過無效的其他網關ID格式且找不到匹配的網關: ${otherGateway}`);
                continue;
              }
            } else {
              // 使用安全的 ObjectId 創建函數
              try {
                otherGatewayObjectId = safeObjectId(otherGateway);
                gatewayIdStr = otherGateway.toString();
              } catch (error) {
                console.warn(`創建其他網關ObjectId失敗:`, error.message);
                continue;
              }
            }
          } else if (otherGateway instanceof ObjectId) {
            // 如果已經是 ObjectId 實例，直接使用
            otherGatewayObjectId = otherGateway;
            gatewayIdStr = otherGateway.toString();
          } else {
            console.log(`警告: 其他網關ID類型異常: ${typeof otherGateway}, 值: ${otherGateway}`);

            // 嘗試將值轉換為字符串，然後檢查是否是有效的 ObjectId
            const otherGatewayIdStr = String(otherGateway);
            if (ObjectId.isValid(otherGatewayIdStr)) {
              console.log(`成功將異常類型的其他網關ID轉換為有效的ObjectId字符串: ${otherGatewayIdStr}`);
              try {
                otherGatewayObjectId = safeObjectId(otherGatewayIdStr);
                gatewayIdStr = otherGatewayIdStr;
              } catch (error) {
                console.warn(`創建其他網關ObjectId失敗:`, error.message);
                continue;
              }
            } else {
              // 最後嘗試查詢網關集合
              const gatewayWithId = await gatewayCollection.findOne({ id: otherGatewayIdStr });
              if (gatewayWithId) {
                console.log(`找到了使用id字段匹配的其他網關: ${gatewayWithId._id}`);
                otherGatewayObjectId = gatewayWithId._id;
                gatewayIdStr = otherGatewayIdStr;
              } else {
                console.warn(`跳過無效的其他網關ID類型且無法轉換: ${typeof otherGateway}`);
                continue;
              }
            }
          }

          // 檢查其他網關是否在線
          if (websocketService.isGatewayOnline(gatewayIdStr)) {
            const otherGatewayData = await gatewayCollection.findOne({ _id: otherGatewayObjectId });
            if (otherGatewayData) {
              console.log(`🔄 嘗試發送到備用網關 ${gatewayIdStr} (${otherGatewayData.name})`);
              const otherSendResult = await websocketService.sendCommandToGateway(gatewayIdStr, otherGatewayMessage);

              otherResults.push({
                gatewayId: gatewayIdStr,
                gatewayName: otherGatewayData.name || '未知',
                success: otherSendResult.success,
                message: otherSendResult.message || otherSendResult.error || ''
              });

              // 記錄事件
              try {
                await websocketService.logDeviceEvent(objectId, 'preview_sent', {
                  gatewayId: gatewayIdStr,
                  gatewayName: otherGatewayData.name || '未知',
                  success: otherSendResult.success,
                  message: otherSendResult.message || otherSendResult.error || '',
                  isPrimaryGateway: false
                });
              } catch (logError) {
                console.error(`記錄設備事件失敗:`, logError);
                // 繼續執行，不因記錄失敗而中斷整個流程
              }

              // 記錄發送結果，但不停止循環（保持原有的發送到所有網關的邏輯）
            }
          } else {
            console.log(`⚠️ 備用網關 ${gatewayIdStr} 離線，跳過`);
          }
        } catch (otherGatewayError) {
          console.error(`處理其他網關時發生錯誤:`, otherGatewayError);
          // 繼續處理下一個網關，不中斷整個流程
        }
      }
    }

    // 回傳結果
    return {
      success: primarySendResult.success,
      deviceId,
      deviceMac: device.macAddress,
      primaryGateway: {
        gatewayId: primaryGatewayId,
        gatewayName: primaryGatewayData.name || '未知',
        success: primarySendResult.success,
        message: primarySendResult.message || primarySendResult.error || ''
      },
      actualUsedGateway: {
        gatewayId: usedGatewayId,
        gatewayName: usedGatewayName,
        isUsingBackupGateway: primarySendResult.isUsingBackupGateway || false,
        backupGatewayReason: primarySendResult.backupGatewayReason || '',
        backupGatewayFailed: primarySendResult.backupGatewayFailed || false
      },
      smartGatewaySelection: {
        enabled: device.gatewaySelectionMode === 'auto',
        primaryGatewayBusy: device.gatewaySelectionMode === 'auto' && websocketService.isGatewayBusyWithChunk(primaryGatewayId),
        usedBackupGateway: primarySendResult.isUsingBackupGateway || false
      },
      otherGateways: otherResults,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`發送設備 ${deviceId} 預覽圖到網關失敗:`, error);
    return {
      success: false,
      deviceId,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 批量發送多個設備預覽圖到其主要網關 - 使用任務隊列機制
 * @param {string[]} deviceIds 設備ID陣列
 * @param {Object} options 可選參數
 * @param {boolean} options.sendToAllGateways 是否發送到所有發現此設備的網關
 * @param {number} options.concurrency 並發數量，默認為2（考慮網關數量限制）
 * @param {boolean} options.enableSmartSelection 是否啟用智能網關選擇，默認為true
 * @returns {Promise<Object>} 結果
 */
const sendMultipleDevicePreviewsToGateways = async (deviceIds, options = {}) => {
  // 引入 websocketService 以獲取網關信息
  const websocketService = require('./websocketService');

  // 從系統配置獲取默認併發數量
  const { getGatewayConcurrency } = require('../utils/sysConfigUtils');
  const defaultConcurrency = await getGatewayConcurrency();

  // 動態調整並發數：不超過可用網關數量的2倍
  const connectedGateways = websocketService.getConnectedGateways();
  const maxConcurrency = Math.max(2, connectedGateways.size * 2);

  const {
    concurrency = Math.min(defaultConcurrency, maxConcurrency), // 使用系統配置的默認值，但不超過最大並發數
    enableSmartSelection = true,
    ...otherOptions
  } = options;

  console.log(`🌐 當前連接的網關數量: ${connectedGateways.size}，最大建議並發數: ${maxConcurrency}，實際使用並發數: ${concurrency}`);

  // 詳細列出連接的網關
  if (connectedGateways.size > 0) {
    console.log(`📋 已連接的網關列表:`);
    for (const [gatewayId] of connectedGateways) {
      const isBusy = websocketService.isGatewayBusyWithChunk(gatewayId);
      console.log(`  - 網關 ${gatewayId}: ${isBusy ? '忙碌' : '空閒'}`);
    }
  } else {
    console.log(`⚠️ 沒有網關連接，將立即返回失敗結果`);
  }

  const results = [];
  const failedDevices = [];
  const successDevices = [];
  const smartSelectionStats = {
    totalAutoModeDevices: 0,
    usedBackupGateway: 0,
    primaryGatewayBusy: 0
  };

  console.log(`🚀 開始批量發送 ${deviceIds.length} 個設備，使用任務隊列機制，智能選擇: ${enableSmartSelection}`);

  // 提取批量ID用於進度追蹤
  const batchId = options.batchId;
  if (batchId) {
    console.log(`📊 批量傳送ID: ${batchId}`);
    // 廣播初始進度
    websocketService.broadcastBatchProgress(batchId, {
      totalDevices: deviceIds.length,
      completedDevices: 0,
      failedDevices: 0,
      status: connectedGateways.size === 0 ? 'error' : 'preparing',
      startTime: Date.now(),
      error: connectedGateways.size === 0 ? '沒有網關連接，無法執行批量發送' : undefined
    });
  }

  // 如果沒有網關連接，立即返回失敗結果
  if (connectedGateways.size === 0) {
    console.warn(`⚠️ 沒有網關連接，無法執行批量發送`);

    // 將所有設備標記為失敗
    for (const deviceId of deviceIds) {
      const failureResult = {
        success: false,
        deviceId: deviceId,
        error: '沒有網關連接',
        timestamp: new Date().toISOString(),
        retryCount: 0
      };
      results.push(failureResult);
      failedDevices.push({
        deviceId: deviceId,
        reason: '沒有網關連接',
        retryCount: 0
      });
    }

    const finalResult = {
      success: false,
      totalCount: deviceIds.length,
      successCount: 0,
      failedCount: deviceIds.length,
      detailResults: results,
      successDevices: [],
      failedDevices,
      smartSelectionStats,
      performanceStats: {
        totalProcessingTime: 0,
        avgProcessingTime: 0,
        queueBased: true,
        concurrency
      },
      timestamp: new Date().toISOString()
    };

    // 廣播批量傳送完成事件
    if (batchId) {
      websocketService.broadcastBatchComplete(batchId, finalResult);
    }

    return finalResult;
  }

  // 檢查當前網關狀態
  const availableGateways = [];
  for (const [gatewayId] of connectedGateways) {
    const isBusy = websocketService.isGatewayBusyWithChunk(gatewayId);
    availableGateways.push({ gatewayId, isBusy });
    console.log(`🌐 網關 ${gatewayId}: ${isBusy ? '忙碌' : '空閒'}`);
  }

  const busyGatewayCount = availableGateways.filter(g => g.isBusy).length;
  if (busyGatewayCount > 0) {
    console.log(`⚠️ 檢測到 ${busyGatewayCount}/${availableGateways.length} 個網關正在忙碌，將使用任務隊列策略`);
  }

  // 創建任務隊列
  const taskQueue = deviceIds.map((deviceId, index) => ({
    deviceId,
    originalIndex: index,
    retryCount: 0,
    maxRetries: 3,
    lastAttemptTime: 0
  }));

  console.log(`📋 創建任務隊列，總任務數: ${taskQueue.length}`);

  // 檢查任務是否可以立即處理（有可用網關）
  const checkTaskCanProcess = async (task) => {
    try {
      const { collection: deviceCollection } = await getDeviceCollection();
      const device = await deviceCollection.findOne({ _id: safeObjectId(task.deviceId) });

      if (!device) {
        return false; // 設備不存在，無法處理
      }

      // 檢查設備是否離線 - 如果離線則不能處理，且不應重試
      if (device.status === 'offline') {
        console.log(`❌ 設備 ${task.deviceId} 狀態為離線，跳過處理`);
        return false;
      }

      const primaryGatewayId = device.primaryGatewayId?.toString() || device.primaryGatewayId;
      if (!primaryGatewayId) {
        return false; // 沒有配置主要網關，無法處理
      }

      if (device.gatewaySelectionMode === 'auto') {
        // 智能模式：檢查主要網關或備用網關是否可用
        const isPrimaryAvailable = websocketService.isGatewayOnline(primaryGatewayId) &&
                                  !websocketService.isGatewayBusyWithChunk(primaryGatewayId);

        if (isPrimaryAvailable) {
          return true; // 主要網關可用
        }

        // 檢查備用網關
        if (Array.isArray(device.otherGateways) && device.otherGateways.length > 0) {
          const allGatewayIds = [primaryGatewayId];
          const otherGatewayIds = device.otherGateways.map(gw => {
            if (typeof gw === 'string') return gw;
            if (gw instanceof ObjectId) return gw.toString();
            return String(gw);
          });
          allGatewayIds.push(...otherGatewayIds);

          const availableGateways = websocketService.getAvailableGateways(allGatewayIds);
          const backupGateway = availableGateways.find(gw => gw !== primaryGatewayId);

          return !!backupGateway; // 有備用網關可用
        }

        return false; // 沒有可用網關
      } else {
        // 非智能模式：檢查主要網關狀態
        const isPrimaryOnline = websocketService.isGatewayOnline(primaryGatewayId);
        const isPrimaryBusy = websocketService.isGatewayBusyWithChunk(primaryGatewayId);

        // 如果主要網關離線，允許處理這個任務（在processTask中會直接失敗）
        // 如果主要網關在線且空閒，允許處理
        // 只有主要網關在線但忙碌時，才不允許立即處理
        return !isPrimaryOnline || (isPrimaryOnline && !isPrimaryBusy);
      }
    } catch (error) {
      console.error(`檢查任務 ${task.deviceId} 是否可處理時發生錯誤:`, error);
      return false;
    }
  };

  // 任務隊列處理函數
  const processTaskQueue = async () => {
    let currentlyProcessing = 0;
    let completedTasks = 0;
    let queueCycles = 0;
    let waitCycles = 0;

    // 從系統配置獲取隊列循環上限數量和最大等待循環次數
    const { getMaxQueueCycles, getMaxWaitCycles } = require('../utils/sysConfigUtils');
    const maxQueueCycles = await getMaxQueueCycles(); // 從系統配置獲取，預設為100
    const maxWaitCycles = await getMaxWaitCycles(); // 從系統配置獲取，預設為10

    while (taskQueue.length > 0 && queueCycles < maxQueueCycles) {
      queueCycles++;
      console.log(`🔄 任務隊列循環 ${queueCycles}，剩餘任務: ${taskQueue.length}，正在處理: ${currentlyProcessing}`);

      // 限制並發數量
      if (currentlyProcessing >= concurrency) {
        await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms後再檢查
        continue;
      }

      // 檢查是否有可以立即處理的任務
      let foundProcessableTask = false;
      let taskIndex = 0;

      while (taskIndex < taskQueue.length && !foundProcessableTask) {
        const task = taskQueue[taskIndex];

        // 檢查這個任務是否可以立即處理（有可用網關）
        const canProcess = await checkTaskCanProcess(task);

        if (canProcess) {
          // 找到可以處理的任務，從隊列中移除
          taskQueue.splice(taskIndex, 1);
          foundProcessableTask = true;

          // 增加正在處理的任務計數
          currentlyProcessing++;

          // 異步處理任務
          processTask(task).then((result) => {
        currentlyProcessing--;
        completedTasks++;

        console.log(`📊 任務 ${task.deviceId} 處理完成，結果: ${result.success ? '成功' : '失敗'}${result.shouldRetry ? ' (可重試)' : ''}`);

        if (result.success) {
          // 任務成功
          results.push(result.result);
          successDevices.push(task.deviceId);

          // 收集智能選擇統計信息
          if (result.result.smartGatewaySelection) {
            if (result.result.smartGatewaySelection.enabled) {
              smartSelectionStats.totalAutoModeDevices++;
            }
            if (result.result.smartGatewaySelection.primaryGatewayBusy) {
              smartSelectionStats.primaryGatewayBusy++;
            }
            if (result.result.smartGatewaySelection.usedBackupGateway) {
              smartSelectionStats.usedBackupGateway++;
            }
          }

          console.log(`✅ 任務 ${task.deviceId} 完成 (${result.duration}ms)${result.result.actualUsedGateway?.isUsingBackupGateway ? ' [使用備用網關]' : ''}${task.retryCount > 0 ? ` [重試${task.retryCount}次]` : ''}`);
        } else if (result.shouldRetry && task.retryCount < task.maxRetries) {
          // 任務失敗但可以重試，重新加入隊列末尾
          task.retryCount++;
          task.lastAttemptTime = Date.now();
          taskQueue.push(task);
          console.log(`🔄 任務 ${task.deviceId} 重新排隊 (重試 ${task.retryCount}/${task.maxRetries}): ${result.error}`);
        } else {
          // 任務最終失敗
          const failureResult = {
            success: false,
            deviceId: task.deviceId,
            error: result.error,
            timestamp: new Date().toISOString(),
            retryCount: task.retryCount
          };
          results.push(failureResult);
          failedDevices.push({
            deviceId: task.deviceId,
            reason: result.error,
            retryCount: task.retryCount
          });
          console.log(`❌ 任務 ${task.deviceId} 最終失敗 (重試${task.retryCount}次): ${result.error}`);
        }

        // 廣播進度更新
        if (batchId) {
          const currentCompleted = successDevices.length;
          const currentFailed = failedDevices.length;
          websocketService.broadcastBatchProgress(batchId, {
            totalDevices: deviceIds.length,
            completedDevices: currentCompleted,
            failedDevices: currentFailed,
            status: 'running',
            queueCycles,
            waitCycles,
            smartSelectionStats: { ...smartSelectionStats }
          });
        }
      }).catch((error) => {
        currentlyProcessing--;
        console.error(`💥 任務 ${task.deviceId} 處理異常:`, error);
        console.error(`💥 異常堆棧:`, error.stack);

        // 處理異常的任務也加入失敗列表
        const failureResult = {
          success: false,
          deviceId: task.deviceId,
          error: error.message || '處理異常',
          timestamp: new Date().toISOString(),
          retryCount: task.retryCount
        };
        results.push(failureResult);
        failedDevices.push({
          deviceId: task.deviceId,
          reason: error.message || '處理異常',
          retryCount: task.retryCount
        });
      });
        } else {
          taskIndex++;
        }
      }

      // 如果沒有找到可以處理的任務，等待網關變為可用
      if (!foundProcessableTask && taskQueue.length > 0) {
        console.log(`⏳ 沒有可立即處理的任務，等待網關變為可用... (循環 ${queueCycles})`);

        // 只在特定循環次數時廣播等待狀態的進度更新，避免過度頻繁
        if (batchId && (queueCycles === 1 || queueCycles % 5 === 0)) {
          const currentCompleted = successDevices.length;
          const currentFailed = failedDevices.length;
          console.log(`📡 廣播等待狀態進度更新 (循環 ${queueCycles})`);
          websocketService.broadcastBatchProgress(batchId, {
            totalDevices: deviceIds.length,
            completedDevices: currentCompleted,
            failedDevices: currentFailed,
            status: 'running',
            queueCycles,
            waitCycles,
            smartSelectionStats: { ...smartSelectionStats },
            currentDevice: {
              id: 'waiting',
              name: `等待網關可用... (循環 ${queueCycles})`
            }
          });
        }

        // 收集所有任務涉及的網關ID
        const allGatewayIds = new Set();
        for (const task of taskQueue) {
          try {
            const { collection: deviceCollection } = await getDeviceCollection();
            const device = await deviceCollection.findOne({ _id: safeObjectId(task.deviceId) });
            if (device) {
              const primaryGatewayId = device.primaryGatewayId?.toString() || device.primaryGatewayId;
              if (primaryGatewayId) {
                allGatewayIds.add(primaryGatewayId);

                // 如果是智能模式，也添加備用網關
                if (device.gatewaySelectionMode === 'auto' && Array.isArray(device.otherGateways)) {
                  device.otherGateways.forEach(gw => {
                    const gwId = typeof gw === 'string' ? gw : gw.toString();
                    allGatewayIds.add(gwId);
                  });
                }
              }
            }
          } catch (error) {
            console.error(`獲取任務 ${task.deviceId} 的網關信息失敗:`, error);
          }
        }

        if (allGatewayIds.size > 0) {
          try {
            waitCycles++;
            console.log(`⏳ 等待循環 ${waitCycles}/${maxWaitCycles}，等待網關: [${Array.from(allGatewayIds).join(', ')}]`);

            // 等待任一網關變為可用，最多等待10秒
            await websocketService.waitForAnyGatewayAvailable(Array.from(allGatewayIds), 10000);
            console.log(`🔔 有網關變為可用，繼續處理任務`);
          } catch (waitError) {
            console.warn(`⚠️ 等待網關可用超時: ${waitError.message}`);

            // 如果等待次數過多，避免無限等待
            if (waitCycles >= maxWaitCycles) {
              console.warn(`⚠️ 達到最大等待循環次數 ${maxWaitCycles}，停止等待`);
              break;
            }
          }
        } else {
          console.warn(`⚠️ 無法獲取任務的網關信息，跳過等待`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 添加小延遲避免過度競爭
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 等待所有正在處理的任務完成
    while (currentlyProcessing > 0) {
      console.log(`⏳ 等待 ${currentlyProcessing} 個任務完成...`);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (queueCycles >= maxQueueCycles && taskQueue.length > 0) {
      console.warn(`⚠️ 達到最大隊列循環次數 ${maxQueueCycles}，剩餘 ${taskQueue.length} 個任務未完成`);
      // 將剩餘任務標記為失敗
      for (const remainingTask of taskQueue) {
        const failureResult = {
          success: false,
          deviceId: remainingTask.deviceId,
          error: '達到最大隊列循環次數',
          timestamp: new Date().toISOString(),
          retryCount: remainingTask.retryCount
        };
        results.push(failureResult);
        failedDevices.push({
          deviceId: remainingTask.deviceId,
          reason: '達到最大隊列循環次數',
          retryCount: remainingTask.retryCount
        });
      }

      // 廣播最終進度更新
      if (batchId) {
        const currentCompleted = successDevices.length;
        const currentFailed = failedDevices.length;
        websocketService.broadcastBatchProgress(batchId, {
          totalDevices: deviceIds.length,
          completedDevices: currentCompleted,
          failedDevices: currentFailed,
          status: 'error',
          queueCycles,
          waitCycles,
          smartSelectionStats: { ...smartSelectionStats },
          error: `達到最大隊列循環次數 ${maxQueueCycles}，剩餘 ${taskQueue.length} 個任務未完成`
        });
      }
    }

    console.log(`🎯 任務隊列處理完成，總循環: ${queueCycles}，完成任務: ${completedTasks}`);
  };

  // 單個任務處理函數 - 實現智能網關選擇邏輯
  const processTask = async (task) => {
    const startTime = Date.now();

    try {
      // 檢查設備是否需要智能網關選擇
      const { collection: deviceCollection } = await getDeviceCollection();
      const device = await deviceCollection.findOne({ _id: safeObjectId(task.deviceId) });

      if (!device) {
        return {
          success: false,
          shouldRetry: false,
          error: '設備不存在',
          duration: Date.now() - startTime
        };
      }

      // 檢查設備是否離線 - 如果離線則直接失敗，不重試
      if (device.status === 'offline') {
        console.log(`❌ 設備 ${task.deviceId} 狀態為離線，直接標記為失敗`);
        return {
          success: false,
          shouldRetry: false,
          error: '設備離線',
          duration: Date.now() - startTime
        };
      }

      // 獲取主要網關ID
      const primaryGatewayId = device.primaryGatewayId?.toString() || device.primaryGatewayId;
      if (!primaryGatewayId) {
        console.log(`🔍 設備 ${task.deviceId} 調試信息:`, {
          primaryGatewayId: device.primaryGatewayId,
          primaryGateway: device.primaryGateway,
          deviceKeys: Object.keys(device)
        });
        return {
          success: false,
          shouldRetry: false,
          error: '設備沒有配置主要網關',
          duration: Date.now() - startTime
        };
      }

      // 智能網關選擇邏輯
      let canProcessNow = false;
      let selectedGatewayId = primaryGatewayId;
      let useBackupGateway = false;

      if (device.gatewaySelectionMode === 'auto') {
        console.log(`🤖 設備 ${task.deviceId} 智能模式 - 檢查主要網關 ${primaryGatewayId} 狀態`);

        // 1. 檢查主要網關是否忙碌
        const isPrimaryBusy = websocketService.isGatewayBusyWithChunk(primaryGatewayId);
        const isPrimaryOnline = websocketService.isGatewayOnline(primaryGatewayId);

        if (!isPrimaryBusy && isPrimaryOnline) {
          // 主要網關空閒且在線，可以直接使用
          canProcessNow = true;
          console.log(`✅ 主要網關 ${primaryGatewayId} 空閒，直接使用`);
        } else {
          console.log(`⚠️ 主要網關 ${primaryGatewayId} ${isPrimaryBusy ? '忙碌' : '離線'}，尋找備用網關`);

          // 2. 尋找備用網關
          if (Array.isArray(device.otherGateways) && device.otherGateways.length > 0) {
            const allGatewayIds = [primaryGatewayId];
            const otherGatewayIds = device.otherGateways.map(gw => {
              if (typeof gw === 'string') return gw;
              if (gw instanceof ObjectId) return gw.toString();
              return String(gw);
            });
            allGatewayIds.push(...otherGatewayIds);

            // 獲取空閒的網關
            const availableGateways = websocketService.getAvailableGateways(allGatewayIds);
            const backupGateway = availableGateways.find(gw => gw !== primaryGatewayId);

            if (backupGateway) {
              selectedGatewayId = backupGateway;
              useBackupGateway = true;
              canProcessNow = true;
              console.log(`✅ 找到備用網關 ${backupGateway}，使用備用網關`);
            } else {
              // 智能模式：檢查是否所有網關都離線
              const allGatewaysOffline = allGatewayIds.every(gwId => !websocketService.isGatewayOnline(gwId));
              if (allGatewaysOffline) {
                console.log(`❌ 智能模式設備 ${task.deviceId} 所有網關都離線，直接標記為失敗`);
                return {
                  success: false,
                  shouldRetry: false,
                  error: '所有網關都離線',
                  duration: Date.now() - startTime
                };
              } else {
                console.log(`❌ 沒有可用的備用網關，任務需要重新排隊`);
                canProcessNow = false;
              }
            }
          } else {
            // 智能模式但沒有備用網關，檢查主要網關是否離線
            if (!isPrimaryOnline) {
              console.log(`❌ 智能模式設備 ${task.deviceId} 主要網關離線且無備用網關，直接標記為失敗`);
              return {
                success: false,
                shouldRetry: false,
                error: '主要網關離線且無備用網關',
                duration: Date.now() - startTime
              };
            } else {
              console.log(`❌ 設備沒有配置備用網關，任務需要重新排隊`);
              canProcessNow = false;
            }
          }
        }
      } else {
        // 非智能模式，檢查主要網關是否可用
        const isPrimaryOnline = websocketService.isGatewayOnline(primaryGatewayId);
        const isPrimaryBusy = websocketService.isGatewayBusyWithChunk(primaryGatewayId);

        if (isPrimaryOnline && !isPrimaryBusy) {
          canProcessNow = true;
          console.log(`✅ 設備 ${task.deviceId} 非智能模式 - 主要網關可用`);
        } else if (!isPrimaryOnline) {
          // 非智能模式：主要網關離線直接失敗
          console.log(`❌ 非智能模式設備 ${task.deviceId} 主要網關離線，直接標記為失敗`);
          return {
            success: false,
            shouldRetry: false,
            error: '主要網關離線',
            duration: Date.now() - startTime
          };
        } else {
          console.log(`❌ 設備 ${task.deviceId} 非智能模式 - 主要網關忙碌，任務需要重新排隊`);
          canProcessNow = false;
        }
      }

      // 3. 根據檢查結果決定是否處理任務
      if (!canProcessNow) {
        return {
          success: false,
          shouldRetry: true,
          error: `網關忙碌，任務重新排隊 (主要網關: ${primaryGatewayId})`,
          duration: Date.now() - startTime
        };
      }

      // 4. 預防性標記網關為忙碌
      const preTaskChunkId = `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      console.log(`🔒 任務隊列預先標記網關 ${selectedGatewayId} 為忙碌，防止併發`);
      websocketService.startChunkTransmission(selectedGatewayId, preTaskChunkId, device.macAddress);

      // 5. 執行任務
      console.log(`🚀 開始處理設備 ${task.deviceId}，使用網關 ${selectedGatewayId}${useBackupGateway ? ' (備用)' : ' (主要)'}`);

      try {
        const result = await sendDevicePreviewToGateway(task.deviceId, {
          ...otherOptions,
          forceGatewayId: selectedGatewayId // 強制使用指定的網關
        });

        // 任務完成，清理預防性標記
        console.log(`🔓 任務隊列清理網關 ${selectedGatewayId} 的預防性標記`);
        websocketService.endChunkTransmission(selectedGatewayId, preTaskChunkId);

        const duration = Date.now() - startTime;
        result.processingTime = duration;
        result.retryCount = task.retryCount;

        // 檢查結果是否成功
        if (result.success) {
          console.log(`✅ 設備 ${task.deviceId} 發送成功，使用網關 ${selectedGatewayId}`);
          return {
            success: true,
            shouldRetry: false,
            result,
            duration
          };
        } else {
          console.error(`❌ 設備 ${task.deviceId} 發送失敗:`, result.error || '未知錯誤');
          return {
            success: false,
            shouldRetry: true, // 發送失敗可以重試
            error: result.error || '設備發送失敗',
            duration
          };
        }
      } catch (sendError) {
        // 發送失敗，清理預防性標記
        console.log(`🔓 發送失敗，清理網關 ${selectedGatewayId} 的預防性標記`);
        websocketService.endChunkTransmission(selectedGatewayId, preTaskChunkId);

        console.error(`💥 設備 ${task.deviceId} 發送過程中發生異常:`, sendError);
        throw sendError; // 重新拋出異常，讓外層 catch 處理
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`💥 處理設備 ${task.deviceId} 時發生錯誤:`, error);

      // 如果有預防性標記，也要清理
      if (typeof selectedGatewayId !== 'undefined' && typeof preTaskChunkId !== 'undefined') {
        console.log(`🔓 異常處理，清理網關 ${selectedGatewayId} 的預防性標記`);
        websocketService.endChunkTransmission(selectedGatewayId, preTaskChunkId);
      }

      // 判斷是否應該重試
      const shouldRetry = (
        error.message.includes('網關忙碌') ||
        error.message.includes('chunk傳輸') ||
        error.message.includes('timeout') ||
        error.message.includes('連接') ||
        error.message.includes('傳輸失敗')
      ) && !(
        // 以下情況不應重試
        error.message.includes('設備離線') ||
        error.message.includes('主要網關離線') ||
        error.message.includes('所有網關都離線') ||
        error.message.includes('主要網關離線且無備用網關')
      );

      return {
        success: false,
        shouldRetry,
        error: error.message,
        duration
      };
    }
  };

  // 執行任務隊列處理
  await processTaskQueue();

  // 計算統計信息
  const totalProcessingTime = results.reduce((sum, r) => sum + (r.processingTime || 0), 0);
  const avgProcessingTime = results.length > 0 ? Math.round(totalProcessingTime / results.length) : 0;

  console.log(`🎯 任務隊列批量發送完成統計:`);
  console.log(`   總設備數: ${deviceIds.length}`);
  console.log(`   成功數: ${successDevices.length}`);
  console.log(`   失敗數: ${failedDevices.length}`);
  console.log(`   智能模式設備: ${smartSelectionStats.totalAutoModeDevices}`);
  console.log(`   使用備用網關: ${smartSelectionStats.usedBackupGateway}`);
  console.log(`   主要網關忙碌: ${smartSelectionStats.primaryGatewayBusy}`);
  console.log(`   平均處理時間: ${avgProcessingTime}ms`);

  // 構建結果對象
  const finalResult = {
    success: failedDevices.length === 0,
    totalCount: deviceIds.length,
    successCount: successDevices.length,
    failedCount: failedDevices.length,
    detailResults: results,
    successDevices,
    failedDevices,
    smartSelectionStats,
    performanceStats: {
      totalProcessingTime,
      avgProcessingTime,
      queueBased: true, // 標記為隊列處理模式
      concurrency
    },
    timestamp: new Date().toISOString()
  };

  // 廣播批量傳送完成事件
  if (batchId) {
    websocketService.broadcastBatchComplete(batchId, finalResult);
  }

  // 回傳整體結果
  return finalResult;
};

module.exports = {
  initDB,
  sendDevicePreviewToGateway,
  sendMultipleDevicePreviewsToGateways,
  getStoreCollection,
  getDataFieldCollection
};
