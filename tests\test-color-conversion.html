<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>顏色轉換測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .color-test {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .color-test label {
            width: 200px;
            font-weight: bold;
        }
        .color-test .result {
            margin-left: 20px;
            padding: 5px 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
            background: #f9f9f9;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>顏色轉換測試</h1>
        <p>測試各種顏色類型的轉換策略選擇</p>

        <div class="test-section">
            <h3>顏色類型轉換測試</h3>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h3>可能的問題檢查</h3>
            <div id="problem-check"></div>
        </div>
    </div>

    <script>
        // 模擬 DisplayColorType 枚舉
        const DisplayColorType = {
            BW: "Gray16",
            BWR: "Black & White & Red",
            BWRY: "Black & White & Red & Yellow",
            ALL: "All colors"
        };

        // 模擬顏色轉換策略函數
        function getConversionStrategy(colorType) {
            const normalizedColorType = typeof colorType === 'string' 
                ? colorType.toUpperCase() 
                : colorType;

            switch (normalizedColorType) {
                case 'GRAY16':
                case 'BW':
                    return { algorithm: 'dithering', name: '抖動算法' };
                case 'BLACK & WHITE & RED':
                case 'BWR':
                    return { algorithm: 'colorQuantization', name: '三色量化' };
                case 'BLACK & WHITE & RED & YELLOW':
                case 'BWRY':
                    return { algorithm: 'colorQuantization', name: '四色量化' };
                case 'ALL COLORS':
                case 'ALL':
                    return { algorithm: 'original', name: '保持原色' };
                default:
                    return { algorithm: 'blackAndWhite', name: '黑白二值化' };
            }
        }

        // 測試用例
        const testCases = [
            'Gray16',
            'BW',
            'Black & White & Red',
            'BWR',
            'Black & White & Red & Yellow',
            'BWRY',
            'All colors',
            'ALL',
            'UNKNOWN',
            'BERY', // 測試這個可能的錯誤值
            '',
            null,
            undefined
        ];

        function runTests() {
            const resultsContainer = document.getElementById('test-results');
            const problemContainer = document.getElementById('problem-check');
            
            resultsContainer.innerHTML = '';
            problemContainer.innerHTML = '';

            // 測試每個顏色類型
            testCases.forEach(colorType => {
                const testDiv = document.createElement('div');
                testDiv.className = 'color-test';
                
                const label = document.createElement('label');
                label.textContent = `輸入: "${colorType}"`;
                
                const result = document.createElement('div');
                result.className = 'result';
                
                try {
                    const strategy = getConversionStrategy(colorType);
                    result.textContent = `算法: ${strategy.algorithm}, 名稱: ${strategy.name}`;
                    result.className += ' success';
                } catch (error) {
                    result.textContent = `錯誤: ${error.message}`;
                    result.className += ' error';
                }
                
                testDiv.appendChild(label);
                testDiv.appendChild(result);
                resultsContainer.appendChild(testDiv);
            });

            // 檢查可能的問題
            const problems = [];
            
            // 檢查是否有字符串相似性問題
            const bwrSimilar = ['BERY', 'BWER', 'BERW', 'BEWRY'];
            bwrSimilar.forEach(similar => {
                const strategy = getConversionStrategy(similar);
                if (strategy.algorithm !== 'blackAndWhite') {
                    problems.push(`"${similar}" 被錯誤識別為 ${strategy.name}`);
                }
            });

            // 檢查大小寫問題
            const caseTests = ['bwr', 'Bwr', 'bWr', 'BWr'];
            caseTests.forEach(caseTest => {
                const strategy = getConversionStrategy(caseTest);
                if (strategy.algorithm !== 'colorQuantization') {
                    problems.push(`"${caseTest}" 大小寫處理有問題: ${strategy.name}`);
                }
            });

            if (problems.length === 0) {
                problemContainer.innerHTML = '<div class="success">沒有發現明顯的問題</div>';
            } else {
                problems.forEach(problem => {
                    const problemDiv = document.createElement('div');
                    problemDiv.className = 'error';
                    problemDiv.textContent = problem;
                    problemContainer.appendChild(problemDiv);
                });
            }
        }

        // 頁面載入時運行測試
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
