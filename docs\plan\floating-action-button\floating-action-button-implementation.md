# 懸浮球功能按鈕實現文檔

## 概述

本文檔描述了懸浮球功能按鈕的實現，包含Bug回報功能和AI助手功能佔位符。

## 功能特性

### Apple風格設計亮點
- **環形展開**: 子按鈕沿弧形路徑展開，角度從左上角(-126°)到右上角(-18°)
- **彈性動畫**: 使用cubic-bezier緩動函數實現Apple風格的彈性效果
- **磨砂玻璃**: backdrop-filter實現真實的磨砂玻璃質感
- **多層光暈**: 外圈、中圈光暈配合脈衝動畫
- **波紋反饋**: 點擊時的波紋擴散效果
- **背景遮罩**: 展開時的半透明背景遮罩

### 1. 懸浮球主按鈕 (FloatingActionButton) - Apple風格
- **位置**: 固定在頁面右下角
- **顯示條件**: 僅在開發模式啟用時顯示
- **交互**: 點擊展開/收起子功能按鈕
- **樣式**:
  - Apple風格漸變色背景
  - 磨砂玻璃效果 (backdrop-filter)
  - 多層光暈效果
  - 彈性動畫和脈衝效果
  - 展開指示器小圓點
- **展開方式**: 環形弧度展開，類似Apple Control Center

### 2. Bug回報功能
- **觸發**: 點擊紅色Bug圖標按鈕
- **功能**: 
  - 標題和詳細描述輸入
  - 優先級選擇（低/中/高/緊急）
  - 圖片上傳（最大10MB）
  - 自動記錄當前頁面路徑
  - 表單驗證和錯誤處理

### 3. AI助手功能
- **觸發**: 點擊藍色AI圖標按鈕
- **狀態**: 目前為佔位符，顯示功能開發中的信息
- **未來功能**: 智能問答、操作指導、問題診斷等

### 4. Bug管理頁面
- **位置**: 側邊欄 > Bug回報管理
- **保護**: 密碼保護（密碼：rd123456）
- **功能**:
  - Bug回報列表顯示
  - 狀態管理（開放/處理中/已解決/已關閉）
  - 優先級篩選
  - 詳情查看（包含圖片）
  - 刪除功能
  - CSV導出

## 開發模式控制

### 環境變數配置
```bash
# 在 .env 文件中設置
DEV_MODE=devmode
```

### 前端檢測邏輯
- **開發環境** (npm run dev): 自動啟用
- **生產環境**: 通過後端API檢查 `DEV_MODE` 環境變數

### API端點
```
GET /api/dev-mode/status
```
返回: `{ "enabled": boolean }`

## 文件結構

### 前端組件
```
src/components/
├── FloatingActionButton.tsx      # 主懸浮球組件
├── BugReportModal.tsx           # Bug回報彈窗
├── BugReportManagementPage.tsx  # Bug管理頁面
├── AIAssistantModal.tsx         # AI助手彈窗
└── PasswordProtectedPage.tsx    # 密碼保護組件
```

### 後端API
```
server/routes/
└── bugReportApi.js              # Bug回報API路由
```

### 類型定義
```
src/types/
└── bugReport.ts                 # Bug回報相關類型
```

### API工具
```
src/utils/api/
└── bugReportApi.ts              # Bug回報API調用函數
```

## API端點

### Bug回報相關
- `GET /api/bug-reports` - 獲取Bug回報列表
- `POST /api/bug-reports` - 創建Bug回報
- `PATCH /api/bug-reports/:id` - 更新Bug回報狀態
- `DELETE /api/bug-reports/:id` - 刪除Bug回報
- `GET /api/bug-reports/:id` - 獲取單個Bug回報詳情
- `GET /api/bug-reports/export/csv` - 導出CSV

### 開發模式檢查
- `GET /api/dev-mode/status` - 檢查開發模式狀態

## 數據庫結構

### bugReports 集合
```javascript
{
  _id: ObjectId,
  title: String,           // 標題
  content: String,         // 詳細描述
  priority: String,        // 優先級: low|medium|high|critical
  status: String,          // 狀態: open|in-progress|resolved|closed
  currentPage: String,     // 當前頁面路徑
  imageId: ObjectId,       // 圖片ID（GridFS）
  createdAt: Date,         // 創建時間
  updatedAt: Date,         // 更新時間
  createdBy: String,       // 創建者
  updatedBy: String,       // 更新者
  notes: String            // 備註
}
```

## 安全考慮

### 密碼保護
- Bug管理頁面使用固定密碼保護
- 密碼: `rd123456`
- 驗證狀態保存在 sessionStorage（僅當前會話有效）
- 嘗試次數限制（5次後鎖定30秒）

### 文件上傳
- 僅允許圖片文件
- 文件大小限制：10MB
- 使用GridFS存儲

### 權限控制
- Bug回報創建：無需特殊權限
- Bug管理：需要密碼驗證

## 使用說明

### 開發環境
1. 啟動開發服務器：`npm run dev`
2. 懸浮球自動顯示在右下角
3. 點擊展開功能按鈕

### 生產環境
1. 在 `.env` 文件中設置 `DEV_MODE=devmode`
2. 重啟服務
3. 懸浮球將顯示

### Bug回報流程
1. 點擊懸浮球展開
2. 點擊紅色Bug圖標
3. 填寫標題和描述
4. 選擇優先級
5. 可選上傳截圖
6. 提交回報

### Bug管理流程
1. 進入側邊欄 > Bug回報管理
2. 輸入密碼：rd123456
3. 查看、篩選、管理Bug回報
4. 更新狀態或刪除記錄
5. 導出CSV報告

## 未來擴展

### AI助手功能
- 智能問答系統
- 操作指導
- 問題診斷
- 自動化任務建議

### Bug回報增強
- 郵件通知
- 自動分類
- 優先級自動調整
- 與開發工具集成

## 注意事項

1. 懸浮球僅在開發模式下顯示
2. Bug管理頁面需要密碼驗證
3. 圖片上傳有大小和格式限制
4. 生產環境需要正確配置環境變數
