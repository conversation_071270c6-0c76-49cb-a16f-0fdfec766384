# AI助手功能實現規劃文件

## 概述

本文檔描述了基於Gemini API的AI助手功能實現規劃，主要透過本地MCP（Model Context Protocol）或A2A（Agent-to-Agent）架構來完成使用者需求。

## 功能目標

### 核心功能
1. **門店創建助手** - 依照使用者需求自動創建門店
2. **模板創建助手** - 依照使用者需求自動創建模板
3. **帳號創建助手** - 依照使用者需求自動創建帳號
4. **未來擴充功能** - 更強大的AI功能支援

### 技術架構
- **AI引擎**: Google Gemini API
- **用戶配置**: 系統設定中輸入Gemini API Key
- **執行架構**: 本地MCP或A2A協議
- **語言解析**: 透過Gemini解析使用者自然語言需求

## 系統架構設計

### 1. 配置管理

#### 1.1 AI設定頁面
在系統配置中新增AI設定分頁：
```typescript
// 新增AI配置介面
interface AIConfig {
  geminiApiKey: string;
  enabled: boolean;
  model: string; // gemini-pro, gemini-pro-vision等
  maxTokens: number;
  temperature: number;
  timeout: number;
}
```

#### 1.2 設定存儲
- 配置存儲在MongoDB的`sysConfig`集合中
- 配置鍵名：`ai-config`
- API Key加密存儲，確保安全性

### 2. AI服務架構

#### 2.1 後端AI服務
```javascript
// server/services/aiService.js
class AIService {
  constructor() {
    this.geminiClient = null;
    this.config = null;
  }

  async initialize() {
    // 初始化Gemini客戶端
  }

  async processUserRequest(request) {
    // 處理用戶請求
  }

  async createStore(requirements) {
    // 創建門店邏輯
  }

  async createTemplate(requirements) {
    // 創建模板邏輯
  }

  async createAccount(requirements) {
    // 創建帳號邏輯
  }
}
```

#### 2.2 MCP/A2A架構
```typescript
// AI代理架構
interface AIAgent {
  id: string;
  name: string;
  capabilities: string[];
  execute(task: AITask): Promise<AIResult>;
}

interface AITask {
  type: 'create_store' | 'create_template' | 'create_account';
  requirements: any;
  context: any;
}
```

### 3. 前端整合

#### 3.1 AI助手界面升級
將現有的`AIAssistantModal`從佔位符升級為功能完整的AI助手：

```typescript
// 新的AI助手界面
interface AIAssistantState {
  isConfigured: boolean;
  isProcessing: boolean;
  conversation: Message[];
  currentTask: AITask | null;
}
```

#### 3.2 對話界面
- 聊天式對話界面
- 支援文字輸入和語音輸入
- 實時顯示AI處理狀態
- 結果預覽和確認機制

## 功能實現詳細設計

### 1. 門店創建助手

#### 1.1 需求解析
```typescript
interface StoreCreationRequest {
  storeName: string;
  storeId?: string;
  address?: string;
  phone?: string;
  managerId?: string;
  importSystemData?: boolean;
  additionalRequirements?: string;
}
```

#### 1.2 AI處理流程
1. 解析用戶自然語言需求
2. 提取門店基本信息
3. 驗證信息完整性
4. 生成門店創建參數
5. 調用現有`createStore` API
6. 返回創建結果

#### 1.3 示例對話
```
用戶: "幫我創建一個名為'台北101店'的門店，地址是台北市信義區，電話是02-1234-5678"

AI: "我理解您要創建一個新門店，讓我為您整理信息：
- 門店名稱：台北101店
- 地址：台北市信義區
- 電話：02-1234-5678
- 門店ID：我建議使用'taipei-101-store'

是否需要導入系統數據？是否確認創建？"
```

### 2. 模板創建助手

#### 2.1 需求解析
```typescript
interface TemplateCreationRequest {
  templateName: string;
  screenSize: string;
  colorType: string;
  orientation: 'landscape' | 'portrait';
  elements?: TemplateElement[];
  isSystemTemplate?: boolean;
  storeId?: string;
}
```

#### 2.2 AI處理流程
1. 解析模板需求（尺寸、顏色、方向等）
2. 根據需求生成模板元素
3. 調用現有模板創建API
4. 生成預覽圖
5. 返回創建結果

### 3. 帳號創建助手

#### 3.1 需求解析
```typescript
interface AccountCreationRequest {
  username: string;
  password?: string;
  name?: string;
  email?: string;
  phone?: string;
  role?: string;
  permissions?: string[];
}
```

#### 3.2 AI處理流程
1. 解析帳號信息需求
2. 生成安全密碼（如未提供）
3. 設定適當的權限
4. 調用現有用戶創建API
5. 返回帳號信息

## 技術實現計劃

### Phase 1: 基礎架構（2週）
1. 設計AI配置管理系統
2. 實現Gemini API整合
3. 建立基礎AI服務架構
4. 升級AI助手界面

### Phase 2: 核心功能（3週）
1. 實現門店創建助手
2. 實現模板創建助手
3. 實現帳號創建助手
4. 建立MCP/A2A通信協議

### Phase 3: 優化與擴展（2週）
1. 性能優化
2. 錯誤處理完善
3. 用戶體驗優化
4. 準備未來功能擴展

## 安全考量

### 1. API Key安全
- 加密存儲Gemini API Key
- 僅管理員可配置
- 定期輪換機制

### 2. 權限控制
- AI操作需要相應權限
- 操作日誌記錄
- 敏感操作需要確認

### 3. 數據隱私
- 不向Gemini發送敏感數據
- 本地處理優先
- 符合數據保護法規

## 監控與日誌

### 1. AI操作日誌
- 記錄所有AI請求和響應
- 性能監控
- 錯誤追蹤

### 2. 使用統計
- AI功能使用頻率
- 成功率統計
- 用戶滿意度

## 未來擴展方向

### 1. 高級功能
- 智能數據分析
- 自動化運營建議
- 預測性維護

### 2. 多模態支持
- 圖像識別
- 語音交互
- 文檔處理

### 3. 個性化
- 學習用戶偏好
- 自定義工作流程
- 智能推薦

## 結論

本AI助手功能將大幅提升系統的易用性和智能化水平，通過自然語言交互簡化複雜操作，為用戶提供更好的使用體驗。
