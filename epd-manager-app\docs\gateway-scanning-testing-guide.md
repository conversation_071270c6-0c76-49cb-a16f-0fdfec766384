# 網關掃描功能測試指南

本文檔提供了測試網關掃描功能的詳細指南，包括如何模擬網關設備和如何測試掃描功能。

## 1. 準備工作

### 1.1 環境要求

- Node.js 環境 (v12 或更高版本)
- 本地網絡環境 (確保設備在同一局域網內)
- 至少兩台設備 (一台運行網關模擬器，一台運行測試客戶端)

### 1.2 文件準備

在 `epd-manager-app/docs` 目錄中，我們提供了以下測試文件：

1. **gateway-discovery-service.js**: 網關設備模擬器
2. **test-gateway-discovery.js**: 掃描測試客戶端

## 2. 模擬網關設備

### 2.1 啟動網關模擬器

1. 打開命令行終端
2. 導航到 `epd-manager-app/docs` 目錄
3. 執行以下命令啟動網關模擬器：

```bash
node gateway-discovery-service.js
```

4. 您應該會看到類似以下的輸出：

```
啟動網關發現服務...
UDP 發現服務已啟動，監聽 0.0.0.0:5000
網關設備已啟動，MAC 地址: AA:BB:CC:DD:EE:FF
網關信息: {
  macAddress: 'AA:BB:CC:DD:EE:FF',
  model: 'GW-2000',
  name: 'Gateway-1234',
  firmwareVersion: '1.0.0',
  ipAddress: '*************',
  capabilities: [ 'wifi', 'bluetooth' ],
  status: 'ready'
}
```

5. 網關模擬器現在已經在運行，等待掃描請求

### 2.2 運行多個網關模擬器 (可選)

如果您想模擬多個網關設備，可以：

1. 複製 `gateway-discovery-service.js` 文件，並重命名為 `gateway-discovery-service-2.js`
2. 編輯文件，修改 `gatewayInfo` 對象中的 `name` 和其他信息
3. 在另一個終端中運行第二個模擬器：

```bash
node gateway-discovery-service-2.js
```

## 3. 測試掃描功能

### 3.1 使用測試腳本

1. 打開新的命令行終端
2. 導航到 `epd-manager-app/docs` 目錄
3. 執行以下命令啟動測試腳本：

```bash
node test-gateway-discovery.js
```

4. 您應該會看到類似以下的輸出：

```
啟動網關掃描測試...
UDP 客戶端已啟動，準備發送廣播消息
已發送廣播消息: {
  type: 'discovery',
  protocol: 'EPD-GATEWAY-DISCOVERY',
  version: '1.0',
  timestamp: 1683270664000
}
等待網關回應 (10 秒超時)...
收到來自 *************:5000 的回應: {
  type: 'discovery-response',
  protocol: 'EPD-GATEWAY-DISCOVERY',
  version: '1.0',
  timestamp: 1683270664500,
  macAddress: 'AA:BB:CC:DD:EE:FF',
  model: 'GW-2000',
  name: 'Gateway-1234',
  firmwareVersion: '1.0.0',
  capabilities: [ 'wifi', 'bluetooth' ],
  status: 'ready'
}
收到有效的網關回應
發現新網關: Gateway-1234 (AA:BB:CC:DD:EE:FF)

--- 掃描結果 ---
發現 1 個網關:

1. Gateway-1234
   MAC 地址: AA:BB:CC:DD:EE:FF
   IP 地址: *************
   型號: GW-2000
   固件版本: 1.0.0
   功能: wifi, bluetooth
   狀態: ready

掃描完成
```

5. 測試腳本將在 10 秒後自動結束，並顯示掃描結果

### 3.2 使用 netcat 工具 (可選)

如果您想手動測試網關回應，可以使用 netcat 工具：

1. 在 Linux/Mac 系統上，打開終端並執行：

```bash
echo '{"type":"discovery","protocol":"EPD-GATEWAY-DISCOVERY","version":"1.0","timestamp":1683270664000}' | nc -u ************* 5000
```

2. 將 IP 地址替換為網關模擬器運行的設備 IP 地址

### 3.3 使用 Wireshark 分析 (可選)

1. 安裝並啟動 Wireshark
2. 選擇正確的網絡接口
3. 設置過濾器 `udp port 5000`
4. 開始捕獲
5. 運行測試腳本或使用 netcat 發送請求
6. 分析捕獲的數據包，查看請求和回應的格式

## 4. 在 React Native 應用中測試

### 4.1 安裝必要的依賴

在 EPD Manager App 專案中，確保安裝了以下依賴：

```bash
npm install react-native-udp react-native-network-info react-native-permissions
```

### 4.2 配置權限

在 Android 和 iOS 平台上，需要配置網絡相關權限：

#### Android (AndroidManifest.xml)

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
```

#### iOS (Info.plist)

```xml
<key>NSLocalNetworkUsageDescription</key>
<string>This app needs access to the local network to discover gateways</string>
```

### 4.3 運行應用

1. 啟動網關模擬器
2. 運行 React Native 應用：

```bash
npm start
```

3. 在設備或模擬器上啟動應用
4. 導航到網關掃描頁面
5. 確認應用能夠發現並顯示模擬的網關設備

## 5. 常見問題與解決方案

### 5.1 無法發現網關

如果測試腳本或應用無法發現網關，請檢查：

1. **網絡連接**：確保設備在同一局域網內
2. **防火牆設置**：確保 UDP 端口 5000 未被防火牆阻止
3. **廣播地址**：某些網絡可能需要特定的廣播地址，嘗試修改 `BROADCAST_ADDRESS` 變量
4. **權限問題**：確保應用有足夠的網絡權限

### 5.2 網關模擬器無法啟動

如果網關模擬器無法啟動，請檢查：

1. **端口衝突**：確保端口 5000 未被其他應用占用
2. **Node.js 版本**：確保 Node.js 版本足夠新
3. **依賴問題**：確保所有必要的 Node.js 模塊已安裝

### 5.3 回應格式錯誤

如果收到的回應格式不正確，請檢查：

1. **協議版本**：確保客戶端和服務器使用相同的協議版本
2. **JSON 格式**：確保 JSON 格式正確，沒有語法錯誤
3. **字段名稱**：確保字段名稱與協議規範一致

## 6. 進階測試

### 6.1 模擬多種網關型號

修改 `gateway-discovery-service.js` 中的 `gatewayInfo` 對象，創建不同型號的網關：

```javascript
const gatewayInfo = {
  macAddress: generateMacAddress(),
  model: "GW-3000", // 修改型號
  name: `Premium-Gateway-${Math.floor(1000 + Math.random() * 9000)}`,
  firmwareVersion: "2.0.0", // 修改固件版本
  ipAddress: getLocalIpAddress(),
  capabilities: ["wifi", "bluetooth", "zigbee"], // 添加更多功能
  status: "ready"
};
```

### 6.2 模擬網關狀態變化

修改 `gateway-discovery-service.js`，添加狀態變化邏輯：

```javascript
// 每 30 秒隨機變更網關狀態
setInterval(() => {
  const statuses = ["ready", "busy", "updating", "error"];
  gatewayInfo.status = statuses[Math.floor(Math.random() * statuses.length)];
  console.log(`網關狀態已更改為: ${gatewayInfo.status}`);
}, 30000);
```

### 6.3 測試網絡異常情況

1. **網絡斷開**：在測試過程中斷開網絡連接，觀察應用行為
2. **高延遲**：使用網絡模擬工具模擬高延遲環境
3. **數據包丟失**：模擬數據包丟失情況，測試重試機制

## 7. 總結

通過本指南中的測試步驟，您可以全面測試網關掃描功能的各個方面，確保其在各種環境和情況下都能正常工作。這些測試對於開發穩定、可靠的網關掃描功能至關重要。
