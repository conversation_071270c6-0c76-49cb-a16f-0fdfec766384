# 軟體管理系統欄位清理

## 清理概述

根據用戶需求，移除軟體管理系統中不必要的欄位，簡化數據結構和減少存儲開銷。

## 移除的欄位

### 1. versionHistory (版本歷史)
- **類型**: Array of Objects
- **原始用途**: 記錄軟體的版本變更歷史
- **移除原因**: 不需要維護版本歷史記錄，每個軟體版本都是獨立的

### 2. downloadCount (下載次數)
- **類型**: Number
- **原始用途**: 統計軟體被下載的次數
- **移除原因**: 不需要追蹤下載統計

### 3. deploymentCount (部署次數)
- **類型**: Number
- **原始用途**: 統計軟體被部署的次數
- **移除原因**: 不需要追蹤部署統計

## 修改的檔案

### 後端修改

#### 1. `server/models/Software.js`
**移除的內容:**
- 創建軟體時的 `versionHistory` 初始化
- 創建軟體時的 `downloadCount` 和 `deploymentCount` 初始化
- `incrementDownloadCount()` 方法
- `incrementDeploymentCount()` 方法
- 統計查詢中對這些欄位的聚合計算

**修改的方法:**
- `createSoftware()`: 移除不必要欄位的初始化
- `getStatistics()`: 移除下載和部署統計的計算

#### 2. `server/routes/softwareApi.js`
**移除的內容:**
- 下載 API 中對 `incrementDownloadCount()` 的調用

### 前端修改

#### 1. `src/utils/api/softwareApi.ts`
**修改的接口:**
- `Software` 接口：移除 `versionHistory`、`downloadCount`、`deploymentCount` 欄位
- `SoftwareStatistics` 接口：移除 `totalDownloads`、`totalDeployments` 欄位
- 移除 `VersionHistory` 接口定義

### 文檔修改

#### 1. `docs/plan/software-management/system-design.md`
**更新內容:**
- 移除數據庫 schema 中的相關欄位定義
- 更新系統設計文檔以反映新的數據結構

## 數據庫影響

### 現有數據處理
- **現有記錄**: 資料庫中現有的軟體記錄仍會保留這些欄位，但新的 API 不會使用它們
- **向後兼容**: 系統仍能正常運行，只是不再更新或使用這些欄位
- **清理建議**: 可以考慮在適當時機運行數據庫清理腳本移除這些欄位

### 建議的清理腳本
```javascript
// MongoDB 清理腳本 (可選)
db.software.updateMany(
  {},
  {
    $unset: {
      versionHistory: "",
      downloadCount: "",
      deploymentCount: ""
    }
  }
);
```

## 系統優勢

### 1. 簡化數據結構
- 減少不必要的欄位
- 降低數據複雜度
- 提高查詢效率

### 2. 減少存儲開銷
- 每個軟體記錄減少約 3-5 個欄位
- 減少索引需求
- 降低備份大小

### 3. 簡化維護
- 減少需要維護的統計邏輯
- 降低代碼複雜度
- 減少潛在的數據一致性問題

## 功能影響

### 不受影響的功能
- 軟體上傳和驗證
- 軟體列表和搜索
- 軟體詳細資訊查看
- 軟體編輯和狀態管理
- 軟體下載功能
- 軟體啟用/禁用控制

### 移除的功能
- 版本歷史追蹤
- 下載次數統計
- 部署次數統計
- 相關的統計報表功能

## 測試建議

### 1. 功能測試
- 驗證軟體上傳功能正常
- 確認軟體列表顯示正確
- 測試軟體編輯功能
- 驗證軟體下載功能

### 2. 數據完整性測試
- 確認新上傳的軟體不包含移除的欄位
- 驗證現有軟體記錄仍能正常讀取
- 測試統計 API 返回正確的數據

### 3. 性能測試
- 比較清理前後的查詢性能
- 驗證存儲空間的減少
- 測試大量軟體記錄的處理效率

## 後續改進

### 1. 數據庫優化
- 考慮移除相關的數據庫索引
- 優化查詢語句
- 清理歷史數據

### 2. 監控改進
- 如果需要統計功能，可以考慮使用外部分析工具
- 實現更輕量級的使用追蹤機制

### 3. 文檔更新
- 更新 API 文檔
- 修改用戶手冊
- 更新系統架構圖

## 回滾計劃

如果需要恢復這些功能：

1. **代碼回滾**: 從版本控制系統恢復相關代碼
2. **數據庫遷移**: 重新添加欄位定義
3. **數據重建**: 重新計算統計數據（如果需要）

## 總結

此次清理成功移除了軟體管理系統中不必要的欄位，簡化了數據結構，提高了系統效率。所有核心功能保持不變，系統更加精簡和高效。
