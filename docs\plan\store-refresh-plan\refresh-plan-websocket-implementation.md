# 刷圖計畫 WebSocket 即時更新實現

## 概述

本文檔描述了為刷圖計畫功能實現的 WebSocket 即時更新架構，該架構與 DevicesPage、GatewaysPage、DatabasePage、SystemSpecificDataPage、TemplateList 等頁面使用相同的即時更新模式。

## 實現架構

### 1. 前端 WebSocket 客戶端擴展

#### 新增事件類型
- `RefreshPlanUpdateEvent`: 刷圖計畫更新事件
- `StatusSubscriptionAck`: 新增刷圖計畫訂閱確認類型

#### 新增處理器
- `RefreshPlanUpdateEventHandler`: 刷圖計畫更新事件處理器
- `refreshPlanUpdateHandlers`: 事件處理器集合
- `subscribedRefreshPlanStores`: 已訂閱的刷圖計畫門店集合

#### 新增方法
- `subscribeRefreshPlanUpdate(storeId, options)`: 訂閱刷圖計畫更新
- `unsubscribeRefreshPlanUpdate(storeId)`: 取消訂閱刷圖計畫更新
- `addRefreshPlanUpdateListener(handler)`: 添加事件監聽器
- `removeRefreshPlanUpdateListener(handler)`: 移除事件監聽器

#### 便捷訂閱方法
- `subscribeToRefreshPlanUpdate(storeId, handler, options)`: 一鍵訂閱和清理

### 2. 後端 WebSocket 服務擴展

#### 新增訂閱管理
- `refreshPlanUpdateSubscribers`: Map<storeId, Set<WebSocket>>
- `subscribeRefreshPlanUpdate(ws, storeId, options)`: 處理訂閱請求
- `unsubscribeRefreshPlanUpdate(ws, storeId)`: 處理取消訂閱
- `getRefreshPlanUpdateSubscribers(storeId)`: 獲取訂閱者列表

#### 新增廣播功能
- `broadcastRefreshPlanUpdate(storeId, planUpdate, updateType)`: 廣播計畫更新

#### 消息處理
- `subscribe_refresh_plan_update`: 處理前端訂閱請求
- `unsubscribe_refresh_plan_update`: 處理前端取消訂閱請求

### 3. API 層 WebSocket 廣播集成

#### 刷圖計畫 API (refreshPlanApi.js)
- **創建計畫**: 廣播 `create` 事件
- **更新計畫**: 廣播 `update` 事件
- **刪除計畫**: 廣播 `delete` 事件

#### 執行引擎 (executionEngine.js)
- **狀態更新**: 廣播 `status_change` 事件
- **統計更新**: 廣播 `update` 事件（包含統計信息）

#### 任務調度器 (taskScheduler.js)
- **下次執行時間更新**: 廣播 `update` 事件

### 4. 前端組件集成

#### RefreshPlanManagement 組件
- 使用 `subscribeToRefreshPlanUpdate` 訂閱即時更新
- 處理不同類型的更新事件：
  - `create`: 重新獲取計畫列表
  - `update`: 更新特定計畫數據
  - `delete`: 從列表中移除計畫
  - `status_change`: 更新計畫狀態和運行狀態

## 事件類型和數據格式

### RefreshPlanUpdateEvent
```typescript
interface RefreshPlanUpdateEvent {
  type: 'refresh_plan_update';
  storeId: string;
  planId: string;
  planData: {
    _id: string;
    name: string;
    status: 'active' | 'inactive' | 'running' | 'error';
    enabled: boolean;
    lastRun?: string;
    nextRun?: string;
    statistics?: {
      totalRuns: number;
      successRuns: number;
      failedRuns: number;
      lastRunResult?: any;
    };
    updatedFields: string[];
  };
  timestamp: string;
  updateType: 'create' | 'update' | 'delete' | 'status_change';
}
```

### 更新類型說明
- `create`: 新建計畫
- `update`: 計畫信息更新（名稱、描述、配置等）
- `delete`: 計畫刪除
- `status_change`: 計畫狀態變更（運行中、完成、錯誤等）

## 廣播觸發點

### 1. CRUD 操作
- **POST** `/api/stores/:storeId/refresh-plans` → `create` 事件
- **PUT** `/api/stores/:storeId/refresh-plans/:planId` → `update` 事件
- **DELETE** `/api/stores/:storeId/refresh-plans/:planId` → `delete` 事件

### 2. 執行狀態變更
- 計畫開始執行 → `status_change` 事件 (status: 'running')
- 計畫執行完成 → `status_change` 事件 (status: 'active'/'error')
- 統計信息更新 → `update` 事件

### 3. 調度更新
- 下次執行時間計算 → `update` 事件

## 重連機制

### 自動重新訂閱
WebSocket 重連時會自動恢復刷圖計畫訂閱：
```javascript
// 恢復刷圖計畫訂閱
if (this.subscribedRefreshPlanStores.size > 0) {
  console.log(`恢復 ${this.subscribedRefreshPlanStores.size} 個刷圖計畫訂閱`);
  Array.from(this.subscribedRefreshPlanStores).forEach(storeId => {
    this.subscribeRefreshPlanUpdate(storeId);
  });
}
```

## 測試

### 測試文件
- `server/test-refresh-plan-websocket.js`: WebSocket 即時更新功能測試

### 測試流程
1. 建立 WebSocket 連接
2. 訂閱刷圖計畫更新
3. 測試創建計畫 → 驗證 `create` 事件
4. 測試更新計畫 → 驗證 `update` 事件
5. 測試刪除計畫 → 驗證 `delete` 事件

### 運行測試
```bash
cd server
node test-refresh-plan-websocket.js
```

## 與現有架構的一致性

本實現完全遵循現有的 WebSocket 即時更新架構：

1. **相同的訂閱模式**: 使用 Map<storeId, Set<WebSocket>> 管理訂閱者
2. **相同的消息格式**: 遵循現有的事件結構和命名規範
3. **相同的錯誤處理**: 使用 try-catch 包裝廣播操作
4. **相同的重連邏輯**: 自動恢復訂閱狀態
5. **相同的清理機制**: 連接關閉時自動清理訂閱

## 注意事項

1. **身份驗證**: WebSocket 連接需要有效的 JWT token
2. **門店隔離**: 每個門店的刷圖計畫更新是獨立的
3. **錯誤處理**: 廣播失敗不會影響 API 操作的正常執行
4. **性能考慮**: 只向訂閱了特定門店的客戶端發送更新

## 未來擴展

1. **批量操作支持**: 支持批量創建/更新/刪除的廣播
2. **執行進度追蹤**: 實時廣播計畫執行進度
3. **統計信息實時更新**: 更細粒度的統計信息更新
4. **計畫依賴關係**: 支持計畫間依賴關係的實時更新
