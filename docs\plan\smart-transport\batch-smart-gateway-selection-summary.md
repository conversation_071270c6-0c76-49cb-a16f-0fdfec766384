# 批量發送智能網關選擇功能總結 (v2.0)

## 概述

批量發送功能已經完全整合智能網關選擇機制和任務隊列系統，確保所有設置為 `gatewaySelectionMode: 'auto'` 的設備都能在批量發送時觸發智能選擇效果，並實現真正的任務重新排隊機制。

**v2.0 重大更新**：
- ✅ 新增智能等待機制，解決無等待循環問題
- ✅ 實現事件驅動的網關狀態監控
- ✅ 大幅提升批量發送成功率和性能
- ✅ 完善的超時保護和錯誤處理

## 實現的功能 (v2.0 完整版)

### 1. 任務隊列批量發送機制 (v2.0 智能等待版)

**核心改進**：
- **任務隊列系統**：將所有設備發送任務放入隊列，支持任務重新排隊
- **智能網關選擇**：每個任務都重新檢查網關狀態，動態選擇最佳網關
- **智能等待機制** (v2.0 新增)：當所有網關忙碌時等待狀態變化，而非無效重試
- **事件驅動處理** (v2.0 新增)：基於網關狀態變化事件觸發任務處理
- **並發控制**：可配置並發數量（默認2-3個設備同時處理）
- **預防性標記**：使用 `startChunkTransmission` 預先標記網關為忙碌，防止併發衝突
- **任務重試機制**：失敗任務自動重新排隊，最多重試3次
- **超時保護** (v2.0 新增)：多重超時保護防止系統卡住

**處理流程 (v2.0)**：
```
創建任務隊列 → 智能任務選擇 → 等待機制觸發 → 智能網關選擇 → 預防性標記 → 執行任務 → 清理標記並觸發事件 → 結果統計
```

### 2. 智能選擇觸發

每個任務在處理時都會進行智能網關選擇：

✅ **智能模式設備 (`gatewaySelectionMode: 'auto'`)**：
- 檢查主要網關是否正在進行chunk傳輸（使用 `isGatewayBusyWithChunk`）
- 如果主要網關忙碌，從 `otherGateways` 中選擇空閒的備用網關
- 使用 `getAvailableGateways` 獲取所有空閒網關
- 如果沒有可用網關，任務重新排隊等待下次處理

✅ **手動模式設備 (`gatewaySelectionMode: 'manual'`)**：
- 只使用主要網關
- 如果主要網關忙碌，任務重新排隊等待

✅ **混合模式支援**：
- 智能模式和手動模式設備可以在同一批次中混合處理
- 每個設備根據自己的模式獨立進行網關選擇

### 3. 任務隊列機制詳細說明

**任務隊列處理邏輯**：
```javascript
// 1. 創建任務隊列
const taskQueue = deviceIds.map((deviceId, index) => ({
  deviceId,
  originalIndex: index,
  retryCount: 0,
  maxRetries: 3,
  lastAttemptTime: 0
}));

// 2. 隊列循環處理
while (taskQueue.length > 0 && queueCycles < maxQueueCycles) {
  // 限制並發數量
  if (currentlyProcessing >= concurrency) {
    await new Promise(resolve => setTimeout(resolve, 500));
    continue;
  }

  // 取出隊列第一個任務並異步處理
  const task = taskQueue.shift();
  processTask(task).then(handleTaskResult);
}
```

**智能網關選擇邏輯**：
```javascript
// 檢查主要網關狀態
const isPrimaryBusy = websocketService.isGatewayBusyWithChunk(primaryGatewayId);

if (!isPrimaryBusy && websocketService.isGatewayOnline(primaryGatewayId)) {
  // 使用主要網關
  selectedGatewayId = primaryGatewayId;
} else {
  // 尋找備用網關
  const availableGateways = websocketService.getAvailableGateways(allGatewayIds);
  const backupGateway = availableGateways.find(gw => gw !== primaryGatewayId);

  if (backupGateway) {
    selectedGatewayId = backupGateway;
    useBackupGateway = true;
  } else {
    // 沒有可用網關，任務重新排隊
    return { success: false, shouldRetry: true };
  }
}
```

### 4. 統計信息收集

任務隊列系統會自動收集以下統計信息：

```javascript
{
  smartSelectionStats: {
    totalAutoModeDevices: 3,    // 智能模式設備數量
    usedBackupGateway: 2,       // 使用備用網關的次數
    primaryGatewayBusy: 2       // 主要網關忙碌的次數
  },
  performanceStats: {
    totalProcessingTime: 5000,  // 總處理時間(ms)
    avgProcessingTime: 1250,    // 平均處理時間(ms)
    queueCycles: 15,            // 隊列循環次數
    concurrency: 3,             // 並發數量
    retryCount: 5               // 總重試次數
  }
}
```

## 技術實現

### 1. 後端改進

**sendMultipleDevicePreviewsToGateways 函數**：
```javascript
const sendMultipleDevicePreviewsToGateways = async (deviceIds, options = {}) => {
  // 動態調整並發數：不超過可用網關數量的2倍
  const connectedGateways = websocketService.getConnectedGateways();
  const maxConcurrency = Math.max(2, connectedGateways.size * 2);

  const {
    concurrency = Math.min(2, maxConcurrency), // 默認為2
    enableSmartSelection = true,
    ...otherOptions
  } = options;

  // 任務隊列處理邏輯
  // 智能網關選擇
  // 統計信息收集
}
```

**processTask 函數（任務處理核心）**：
```javascript
const processTask = async (task) => {
  // 1. 獲取設備信息
  const device = await deviceCollection.findOne({ _id: safeObjectId(task.deviceId) });

  // 2. 智能網關選擇
  let selectedGatewayId = primaryGatewayId;
  let useBackupGateway = false;

  if (device.gatewaySelectionMode === 'auto') {
    const isPrimaryBusy = websocketService.isGatewayBusyWithChunk(primaryGatewayId);
    if (isPrimaryBusy) {
      // 尋找備用網關
      const availableGateways = websocketService.getAvailableGateways(allGatewayIds);
      const backupGateway = availableGateways.find(gw => gw !== primaryGatewayId);
      if (backupGateway) {
        selectedGatewayId = backupGateway;
        useBackupGateway = true;
      } else {
        // 沒有可用網關，任務重新排隊
        return { success: false, shouldRetry: true };
      }
    }
  }

  // 3. 預防性標記網關為忙碌
  const preTaskChunkId = `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  websocketService.startChunkTransmission(selectedGatewayId, preTaskChunkId, device.macAddress);

  // 4. 執行任務
  try {
    const result = await sendDevicePreviewToGateway(task.deviceId, {
      forceGatewayId: selectedGatewayId
    });

    // 5. 清理預防性標記
    websocketService.endChunkTransmission(selectedGatewayId, preTaskChunkId);

    return { success: true, result };
  } catch (error) {
    websocketService.endChunkTransmission(selectedGatewayId, preTaskChunkId);
    throw error;
  }
};
```

**API路由更新**：
```javascript
router.post('/devices/send-multiple-previews', authenticate, async (req, res) => {
  const {
    deviceIds,
    sendToAllGateways = false,
    concurrency = 3,
    enableSmartSelection = true
  } = req.body;

  const result = await sendPreviewToGateway.sendMultipleDevicePreviewsToGateways(deviceIds, {
    sendToAllGateways,
    concurrency,
    enableSmartSelection
  });
});
```

### 2. 前端改進

**API調用更新**：
```typescript
export async function sendMultipleDevicePreviewsToGateways(
  deviceIds: string[],
  options: {
    sendToAllGateways?: boolean;
    storeId?: string;
    concurrency?: number;        // 並發數量控制
    enableSmartSelection?: boolean; // 啟用智能選擇
  } = {}
): Promise<any>
```

**批量發送調用**：
```javascript
const response = await sendMultipleDevicePreviewsToGateways(deviceIds, {
  sendToAllGateways: hasAutoMode,
  storeId: store?.id,
  concurrency: 3,              // 並發數量
  enableSmartSelection: true   // 啟用智能選擇
});
```

**結果顯示優化**：
```javascript
// 顯示智能選擇統計信息
if (result.smartSelectionStats) {
  const stats = result.smartSelectionStats;
  if (stats.totalAutoModeDevices > 0) {
    message += `\n智能模式設備: ${stats.totalAutoModeDevices} 個`;
    if (stats.usedBackupGateway > 0) {
      message += `，使用備用網關: ${stats.usedBackupGateway} 個`;
    }
  }
}

// 顯示任務隊列統計信息
if (result.performanceStats) {
  const perf = result.performanceStats;
  message += `\n隊列循環: ${perf.queueCycles} 次`;
  if (perf.retryCount > 0) {
    message += `，重試: ${perf.retryCount} 次`;
  }
}
```

## 使用場景

### 場景1：任務隊列重新排隊

假設有以下情況：
- 3個設備需要發送，並發數=2
- device1: 主要網關忙碌，找到備用網關 → 立即處理
- device2: 主要網關忙碌，沒有備用網關 → 重新排隊
- device3: 主要網關空閒 → 立即處理

處理流程：
```
隊列循環1: [device1, device2, device3]
  → device1: 使用備用網關，開始處理
  → device2: 沒有可用網關，重新排隊
  → device3: 使用主要網關，開始處理

隊列循環2: [device2]
  → device2: 主要網關已空閒，開始處理
```

### 場景2：智能網關選擇與預防性標記

當多個任務同時檢查同一個網關時：
```
任務A檢查網關1: 空閒 → 預防性標記為忙碌 → 開始處理
任務B檢查網關1: 忙碌(被任務A標記) → 選擇網關2 → 開始處理
任務C檢查網關1: 忙碌 → 檢查網關2: 忙碌 → 重新排隊
```

這避免了多個任務同時使用同一個網關造成的衝突。

### 場景3：混合模式批量發送

假設有以下設備：
- device1: `gatewaySelectionMode: 'auto'`，主要網關忙碌
- device2: `gatewaySelectionMode: 'auto'`，主要網關空閒
- device3: `gatewaySelectionMode: 'manual'`，主要網關忙碌

處理結果：
- device1: 使用備用網關（智能選擇）
- device2: 使用主要網關（智能選擇）
- device3: 重新排隊等待主要網關（手動模式）

## 性能優勢

### 1. 任務隊列機制
- **避免阻塞等待**：網關忙碌時任務重新排隊，而不是等待
- **真正的並發處理**：多個任務可以同時使用不同的網關
- **智能重試**：失敗任務自動重新排隊，最多重試3次

### 2. 預防性標記機制
- **防止併發衝突**：使用 `startChunkTransmission` 預先標記網關為忙碌
- **精確狀態追蹤**：使用 `isGatewayBusyWithChunk` 檢查真實的網關狀態
- **自動清理**：任務完成後自動清理標記，確保狀態一致性

### 3. 負載均衡
- **動態網關選擇**：每個任務都重新檢查網關狀態
- **智能分散負載**：自動分散到不同網關，避免單點瓶頸
- **備用網關利用**：充分利用備用網關資源

### 4. 統計監控
- **詳細統計信息**：包括隊列循環次數、重試次數、智能選擇使用情況
- **性能數據**：處理時間、並發數量等性能指標
- **便於調優**：幫助優化並發數量和網關配置

## 測試驗證

已通過以下測試驗證功能：

✅ **任務隊列測試**：
- 任務重新排隊機制正常工作
- 並發控制正確限制同時處理的任務數量
- 隊列循環邏輯正確處理各種情況

✅ **智能網關選擇測試**：
- 智能選擇在任務隊列中正確觸發
- 預防性標記機制防止併發衝突
- auto和manual模式設備混合處理正常

✅ **錯誤處理和重試測試**：
- 任務失敗時正確重新排隊
- 重試次數限制正常工作
- 狀態清理機制確保一致性

✅ **性能測試**：
- 並發處理顯著提高批量發送效率
- 智能選擇有效分散網關負載
- 統計信息準確反映處理情況

## 配置建議

### 1. 並發數量配置
- **動態調整**：系統自動根據網關數量調整，最大並發數 = 網關數量 × 2
- **小批量（<10設備）**：concurrency = 2-3
- **中批量（10-50設備）**：concurrency = 3-5
- **大批量（>50設備）**：concurrency = 5-8
- **注意**：過高的並發數可能導致網關過載

### 2. 網關配置
- **備用網關**：每個設備建議配置2-3個備用網關
- **網關分布**：確保每個門店有足夠的網關資源
- **負載均衡**：將設備均勻分配到不同的主要網關

### 3. 任務隊列配置
- **重試次數**：默認最多重試3次，可根據網路環境調整
- **隊列循環限制**：默認最多50次循環，防止無限循環
- **並發控制**：根據網關數量和性能動態調整

### 4. 監控指標
- **隊列效率**：隊列循環次數、重試次數
- **智能選擇使用率**：備用網關使用頻率
- **網關負載分布**：各網關的使用情況
- **批量發送成功率**：整體成功率和失敗原因分析

## 結論

批量發送功能已經完全整合任務隊列機制和智能網關選擇，實現了：

1. **任務隊列系統**：真正的任務重新排隊，避免阻塞等待
2. **智能網關選擇**：每個任務動態選擇最佳網關
3. **預防性標記**：防止併發衝突，確保狀態一致性
4. **並發控制**：可配置的並發數量，提高處理效率
5. **重試機制**：失敗任務自動重新排隊，提高成功率
6. **詳細統計**：全面的性能和使用統計信息
7. **混合支援**：auto和manual模式設備可以混合處理
8. **向後兼容**：現有功能完全不受影響

這個實現確保了批量發送時智能網關選擇功能能夠充分發揮作用，通過任務隊列機制實現真正的負載均衡和高效處理，大幅提高系統的整體性能和可靠性。
