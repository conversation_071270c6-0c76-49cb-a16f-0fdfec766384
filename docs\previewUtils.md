# 生成預覽圖的 Function Call Flow

## 主要流程圖

```
generatePreviewImage()
    │
    ├── 獲取畫布尺寸
    │
    ├── 備份並移除選中狀態元素的 'selected' 類
    │
    ├── renderCanvasToImage() [從 canvasUtils.ts]
    │   └── 將畫布渲染為圖像
    │
    ├── 恢復選中狀態元素的 'selected' 類
    │
    ├── applyImageEffect() [在 previewUtils.ts 中]
    │   └── 根據效果類型調用不同的處理函數：
    │       ├── 'original': 保持原樣
    │       ├── 'blackAndWhite': imageEffects.convertToBlackAndWhite()
    │       ├── 'grayscale': imageEffects.convertToGrayscale()
    │       ├── 'inverted': imageEffects.convertToInverted()
    │       └── 'dithering': imageEffects.convertToDithering()
    │
    └── canvas.toDataURL() 
        └── 返回 base64 格式的圖像數據
```

## 詳細函數調用流程

1. **入口函數**: `generatePreviewImage(canvasElement, effectType, threshold)`
   - 參數: 
     - `canvasElement`: HTML 元素，表示畫布
     - `effectType`: 效果類型，默認為 'blackAndWhite'
     - `threshold`: 閾值，默認為 128

2. **獲取畫布尺寸**
   - 從 canvasElement 的 data 屬性獲取 width 和 height

3. **處理選中狀態**
   - 暫時移除元素的 'selected' 類，以生成乾淨的預覽
   - 完成渲染後恢復選中狀態

4. **渲染畫布**
   - 調用 `renderCanvasToImage()` 函數
   - 將畫布內容渲染到新的 Canvas 元素中

5. **應用圖像效果**
   - 調用 `applyImageEffect(renderedCanvas, effectType, threshold)`
   - 內部根據 effectType 選擇不同的處理函數:
     - 'blackAndWhite': 調用 `imageEffects.convertToBlackAndWhite()`
     - 'grayscale': 調用 `imageEffects.convertToGrayscale()`
     - 'inverted': 調用 `imageEffects.convertToInverted()`
     - 'dithering': 調用 `imageEffects.convertToDithering()`

6. **生成圖像數據**
   - 調用 `previewCanvas.toDataURL('image/png')`
   - 返回 base64 格式的圖像數據字符串

## 主要函數說明

### `generatePreviewImage`

```typescript
export const generatePreviewImage = async (
  canvasElement: HTMLElement,
  effectType: EffectType = 'blackAndWhite',
  threshold: number = 128
): Promise<string | null>
```

從畫布元素生成預覽圖，處理選中狀態，應用圖像效果，並返回 base64 圖像數據。

### `applyImageEffect`

```typescript
export const applyImageEffect = (
  canvas: HTMLCanvasElement, 
  effectType: EffectType = 'blackAndWhite', 
  threshold: number = 128
): HTMLCanvasElement
```

通用的圖像處理函數，根據指定的效果類型將不同的處理應用於畫布。

### 支援的效果類型

```typescript
export type EffectType = 'original' | 'blackAndWhite' | 'grayscale' | 'inverted' | 'dithering';
```

## 圖像效果處理函數 (從 imageEffects 模組)

這些函數都接收一個 Canvas 元素作為輸入，並返回一個新的處理後的 Canvas 元素：

1. `convertToBlackAndWhite(canvas, threshold)` - 黑白二值化處理
2. `convertToGrayscale(canvas)` - 灰階處理
3. `convertToInverted(canvas)` - 顏色反轉
4. `convertToDithering(canvas)` - 使用 Floyd-Steinberg 算法創建抖動效果

這個流程結合了畫布渲染和圖像處理功能，最終生成適合顯示的預覽圖像。每種效果處理都會建立新的 Canvas 元素，以避免修改原始數據。