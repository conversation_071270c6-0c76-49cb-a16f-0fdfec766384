import React, { useState, useEffect } from 'react';
import { Search, Import, Upload, Lightbulb, Plus, Trash2, Grid, Link, Trash, AlertCircle, RefreshCw } from 'lucide-react';
import { StoreData, DataField, DataFieldSectionType } from '../types';
import { getAllStoreData, deleteStoreData, syncDataFieldsToStoreData } from '../utils/api/storeDataApi';
import { getAllDataFields } from '../utils/api/dataFieldApi';
import { AddStoreDataModal } from './AddStoreDataModal';

export function DatabasePage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [storeData, setStoreData] = useState<StoreData[]>([]);
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [syncingFields, setSyncingFields] = useState(false);
  const [notification, setNotification] = useState<{ message: string, type: 'success' | 'error' } | null>(null);  const [showAddModal, setShowAddModal] = useState(false);
  const [showFieldManager, setShowFieldManager] = useState(false);
  const [visibleFields, setVisibleFields] = useState<Record<string, boolean>>({});
  
  // 載入資料
  useEffect(() => {
    fetchData();
  }, []);

  // 檢查是否所有欄位都被選中
  const areAllFieldsSelected = () => {
    const selectableFields = dataFields.filter(field => field.id !== 'sn' && field.id !== 'id');
    return selectableFields.length > 0 && 
      selectableFields.every(field => visibleFields[field.id]);
  };
  
  // 獲取門店資料和資料欄位定義
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // 同時獲取門店資料和資料欄位定義
      const [storeItems, fieldItems] = await Promise.all([
        getAllStoreData(),
        getAllDataFields()
      ]);
      
      setStoreData(storeItems);
      
      // 只保留一般資料欄位
      const ordinaryFields = fieldItems.filter(field => field.section === DataFieldSectionType.ORDINARY);
      setDataFields(ordinaryFields);
      
      // 初始化欄位顯示狀態，預設所有欄位都顯示，但排除 sn 和 id
      const initialVisibility = ordinaryFields.reduce((acc, field) => {
        if (field.id !== 'sn' && field.id !== 'id') {
          acc[field.id] = true;
        }
        return acc;
      }, {} as Record<string, boolean>);
      
      setVisibleFields(initialVisibility);
    } catch (err: any) {
      console.error('獲取資料失敗:', err);
      setError(err.message || '獲取資料失敗，請重試');
    } finally {
      setLoading(false);
    }
  };
  
  // 處理欄位顯示切換
  const toggleFieldVisibility = (fieldId: string) => {
    setVisibleFields(prev => ({
      ...prev,
      [fieldId]: !prev[fieldId]
    }));
  };
    // 全選或取消全選所有欄位
  const toggleAllFields = () => {
    const allSelected = areAllFieldsSelected();
    const newVisibility = dataFields.reduce((acc, field) => {
      if (field.id !== 'sn' && field.id !== 'id') {
        acc[field.id] = !allSelected;
      }
      return acc;
    }, {} as Record<string, boolean>);
    setVisibleFields(newVisibility);
  };

  // 同步資料欄位到門店資料
  const syncFields = async () => {
    try {
      setSyncingFields(true);
      const result = await syncDataFieldsToStoreData();
      if (result) {
        showNotification('資料欄位已成功同步到門店資料', 'success');
        // 重新獲取資料
        fetchData();
      }
    } catch (err: any) {
      console.error('同步資料欄位失敗:', err);
      showNotification(err.message || '同步資料欄位失敗，請重試', 'error');
    } finally {
      setSyncingFields(false);
    }
  };

  // 處理資料刪除
  const handleDelete = async (sn: number) => {
    if (window.confirm('確定要刪除此門店資料嗎？')) {
      try {
        await deleteStoreData(sn);
        showNotification('刪除門店資料成功', 'success');
        // 更新本地數據
        setStoreData(prevData => prevData.filter(item => item.sn !== sn));
      } catch (err: any) {
        showNotification(err.message || '刪除門店資料失敗', 'error');
      }
    }
  };

  // 顯示通知訊息
  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ message, type });
    setTimeout(() => {
      setNotification(null);
    }, 3000);
  };
  return (
    <div className="p-8 lg:px-4 py-2">
      <div className="max-w-7xl mx-auto">
        {/* 通知消息 */}
        {notification && (
          <div 
            className={`mb-4 p-4 rounded-md ${
              notification.type === 'success' ? 'bg-green-100 border-l-4 border-green-500 text-green-700' : 
              'bg-red-100 border-l-4 border-red-500 text-red-700'
            } flex items-center justify-between`}
          >
            <div className="flex items-center">
              {notification.type === 'success' ? null : <AlertCircle className="w-5 h-5 mr-2" />}
              <span>{notification.message}</span>
            </div>
            <button 
              className="text-gray-500 hover:text-gray-700"
              onClick={() => setNotification(null)}
            >
              &times;
            </button>
          </div>
        )}
        
        {/* 錯誤消息 */}
        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded flex items-center">
            <AlertCircle className="w-5 h-5 mr-2" />
            <span>{error}</span>
            <button 
              onClick={() => setError(null)} 
              className="ml-auto text-red-700 hover:text-red-900"
            >
              &times;
            </button>
          </div>
        )}
        
        {/* Search and Actions Bar */}
        <div className="mb-6 flex items-center space-x-4">
          <div className="relative flex-1">
            <input
              type="text"
              placeholder="搜尋門店資料..."
              className="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
          </div>
          
          <button className="flex items-center gap-2 px-4 py-2 bg-emerald-500 text-white rounded-md hover:bg-emerald-600">
            <Import className="w-5 h-5" />
            匯入
          </button>
          
          <button className="flex items-center gap-2 px-4 py-2 bg-cyan-500 text-white rounded-md hover:bg-cyan-600">
            <Upload className="w-5 h-5" />
            匯出
          </button>
          
          <button 
            className={`flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md ${
              syncingFields ? 'opacity-75 cursor-not-allowed' : 'hover:bg-blue-600'
            }`}
            onClick={syncFields}
            disabled={syncingFields}
          >
            <RefreshCw className={`w-5 h-5 ${syncingFields ? 'animate-spin' : ''}`} />
            同步欄位
          </button>
            <button 
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-violet-500 text-white rounded-md hover:bg-violet-600"
          >
            <Plus className="w-5 h-5" />
            新增
          </button>
          
          <button className="flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
            <Trash2 className="w-5 h-5" />
            刪除
          </button>
            <div className="relative">
            <button 
              className="flex items-center gap-2 px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
              onClick={() => setShowFieldManager(!showFieldManager)}
            >
              <Grid className="w-5 h-5" />
              欄位管理
            </button>

            {showFieldManager && (
              <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-10 py-2 px-3 border border-gray-200">                <div className="pb-2 border-b border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-sm text-gray-700">顯示欄位設置</h3>
                    <div>
                      <label className="text-xs flex items-center text-gray-600 cursor-pointer">
                        <input
                          type="checkbox"
                          className="mr-1 rounded"
                          checked={areAllFieldsSelected()}
                          onChange={toggleAllFields}
                        />
                        全選
                      </label>
                    </div>
                  </div>
                </div>
                <div className="pt-2 max-h-60 overflow-y-auto">
                  {dataFields
                    .filter(field => field.id !== 'sn' && field.id !== 'id')
                    .map(field => (
                      <label key={field.id} className="block py-1 px-2 text-sm text-gray-700 hover:bg-gray-100 rounded cursor-pointer">
                        <input
                          type="checkbox"
                          className="mr-2 rounded"
                          checked={visibleFields[field.id] || false}
                          onChange={() => toggleFieldVisibility(field.id)}
                        />
                        {field.name}
                      </label>
                    ))
                  }
                </div>
              </div>
            )}
          </div>
        </div>        {/* Table */}
        <div className="bg-white rounded-lg shadow overflow-x-auto relative">
          <table className="w-full table-auto">
            <colgroup>
              <col className="w-12" />  {/* 勾選框 */}
              <col className="w-20" />  {/* S/N */}
              {dataFields
                .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
                .filter(field => field.id === 'sn' || field.id === 'id' || visibleFields[field.id])
                .map(field => (
                  <col key={`col-${field.id}`} className="min-w-[120px]" />
                ))
              }
              <col className="w-24" />  {/* 操作列固定寬度 */}
            </colgroup>
            <thead className="bg-gray-100">
              <tr>
                <th className="w-12 px-4 py-3">
                  <input type="checkbox" className="rounded" />
                </th>
                <th className="px-4 py-3 text-left">S/N</th>
                {/* 完全動態生成表格標題，只顯示選中的欄位 */}
                {dataFields
                  .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
                  .filter(field => field.id === 'sn' || field.id === 'id' || visibleFields[field.id])
                  .map(field => (
                    <th key={field.id} className="px-4 py-3 text-left">{field.name}</th>
                  ))
                }
                {/* 操作欄位靠右顯示 */}
                <th className="px-4 py-3 text-left">操作</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={dataFields.length + 3} className="px-4 py-8 text-center text-gray-500">
                    <RefreshCw className="w-5 h-5 animate-spin mx-auto mb-2" />
                    載入中...
                  </td>
                </tr>              ) : storeData.length === 0 ? (
                <tr>
                  <td colSpan={dataFields.length + 3} className="px-4 py-8 text-center text-gray-500">
                    目前沒有門店資料，請點擊「新增」按鈕添加門店資料
                  </td>
                </tr>
              ) : (
                storeData.map((item) => (
                  <tr key={item.sn} className="border-t border-gray-200 hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <input type="checkbox" className="rounded" />
                    </td>                    
                    <td className="px-4 py-3">{item.sn}</td>                    {/* 完全動態生成表格內容，只顯示選中的欄位 */}
                    {dataFields
                      .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
                      .filter(field => field.id === 'sn' || field.id === 'id' || visibleFields[field.id])
                      .map(field => (
                        <td key={field.id} className="px-4 py-3">
                          {/* 顯示對應欄位的值，如果不存在則顯示空字符串 */}
                          {item[field.id] !== undefined ? String(item[field.id]) : ''}
                        </td>
                      ))                    }                    {/* 操作按鈕靠右顯示 */}
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        <button className="text-gray-500 hover:text-blue-600">
                          <Link className="w-5 h-5" />
                        </button>
                        <button 
                          className="text-gray-500 hover:text-red-600"
                          onClick={() => handleDelete(item.sn)}
                        >
                          <Trash className="w-5 h-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button 
              className="px-4 py-2 bg-violet-500 text-white rounded-md"
              onClick={() => setCurrentPage(1)}
            >
              1
            </button>
            <select 
              className="border rounded-md px-2 py-1"
              value={itemsPerPage}
            >
              <option value="10">10/page</option>
            </select>
            <span>1 pages in total</span>
          </div>
          <div className="flex items-center gap-2">
            <span>Go to</span>
            <input 
              type="number" 
              className="w-20 border rounded-md px-2 py-1"
              min={1}
              max={1}
              value={currentPage}
              onChange={(e) => setCurrentPage(Number(e.target.value))}
            />
            <span>page</span>
            <button className="px-4 py-1 border rounded-md hover:bg-gray-50">
              Confirm
            </button>
          </div>        </div>
      </div>
      
      {/* 新增門店資料模態窗口 */}
      <AddStoreDataModal 
        isOpen={showAddModal}
        dataFields={dataFields}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          // 重新加載門店資料
          fetchData();
          // 顯示成功訊息
          showNotification('門店資料新增成功', 'success');
        }}
      />
    </div>
  );
}