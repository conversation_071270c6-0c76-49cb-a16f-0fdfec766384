# Gateway模擬器實現規格書

## 1. 概述

本文檔定義了網關與設備通信架構中Gateway模擬器的實現規格。Gateway模擬器使用Python實現，用於模擬真實網關的行為，包括掃描模式和工作模式，以及與Server的WebSocket通信。

## 2. 負責範圍

Gateway模擬器開發人員負責以下功能的實現：

1. 網關掃描模式的模擬
2. 網關工作模式的模擬
3. HTTP服務器用於接收配置
4. WebSocket客戶端用於與Server通信
5. 設備模擬與狀態更新

## 3. 技術選擇

- **開發語言**：Python 3.8+
- **HTTP服務器**：Python標準庫 `http.server`
- **WebSocket客戶端**：`websockets`庫
- **異步處理**：`asyncio`庫
- **多線程**：`threading`庫
- **日誌記錄**：Python標準庫 `logging`

## 4. 詳細設計

### 4.1 模擬器架構

模擬器由以下主要組件組成：

1. **GatewayState**：存儲網關狀態
2. **Device**：模擬設備
3. **GatewayHTTPHandler**：處理HTTP請求
4. **GatewaySimulator**：主類，協調各組件

### 4.2 網關狀態

```python
class GatewayMode(Enum):
    SCAN = "scan"  # 掃描模式
    WORK = "work"  # 工作模式

class GatewayState:
    def __init__(self):
        self.mode = GatewayMode.SCAN  # 默認為掃描模式
        self.ws_url = None            # WebSocket URL
        self.ws_token = None          # WebSocket 認證令牌
        self.ws_connection = None     # WebSocket 連接
        self.mac_address = f"AA:BB:CC:DD:EE:{random.randint(10, 99)}"  # 模擬MAC地址
        self.ip_address = f"192.168.1.{random.randint(100, 200)}"      # 模擬IP地址
        self.model = "Gateway-Simulator-1.0"
        self.firmware_version = "1.0.0"
        self.connected_devices = []   # 連接的設備列表
        self.device_report_task = None  # 設備狀態報告任務
```

### 4.3 設備模擬

```python
class Device:
    def __init__(self, device_id=None):
        self.id = device_id or str(uuid.uuid4())
        self.mac_address = f"DD:EE:FF:00:11:{random.randint(10, 99)}"
        self.type = random.choice(["EPD", "LCD", "LED"])
        self.status = random.choice(["online", "offline", "error"])
        self.battery = random.randint(0, 100)
        self.signal_strength = random.randint(-90, -30)
        self.last_seen = time.time()

    def to_dict(self):
        return {
            "id": self.id,
            "macAddress": self.mac_address,
            "type": self.type,
            "status": self.status,
            "battery": self.battery,
            "signalStrength": self.signal_strength,
            "lastSeen": self.last_seen
        }

    def update_status(self):
        """更新設備狀態，模擬真實設備的狀態變化"""
        self.status = random.choice(["online", "online", "online", "offline", "error"])
        self.battery = max(0, min(100, self.battery + random.randint(-5, 3)))
        self.signal_strength = max(-90, min(-30, self.signal_strength + random.randint(-5, 5)))
        self.last_seen = time.time()
```

### 4.4 HTTP服務器

HTTP服務器需要支持以下API：

1. **GET /api/status**：返回網關狀態
2. **POST /api/connect**：接收WebSocket連接信息

```python
class GatewayHTTPHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """處理GET請求"""
        if self.path == "/api/status":
            # 返回網關狀態
            status = {
                "mode": self.gateway_state.mode.value,
                "macAddress": self.gateway_state.mac_address,
                "ipAddress": self.gateway_state.ip_address,
                "model": self.gateway_state.model,
                "firmwareVersion": self.gateway_state.firmware_version,
                "connectedDevicesCount": len(self.gateway_state.connected_devices)
            }
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(status).encode())
        else:
            self.send_response(404)
            self.end_headers()

    def do_POST(self):
        """處理POST請求"""
        if self.path == "/api/connect":
            # 接收WebSocket連接信息
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length).decode('utf-8')
            
            try:
                data = json.loads(post_data)
                
                # 提取WebSocket連接信息
                self.gateway_state.ws_url = data.get('wsUrl')
                self.gateway_state.ws_token = data.get('wsToken')
                
                # 切換到工作模式
                self.switch_to_work_mode()
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"success": True}).encode())
            except Exception as e:
                self.send_response(400)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": str(e)}).encode())
        else:
            self.send_response(404)
            self.end_headers()
```

### 4.5 WebSocket客戶端

WebSocket客戶端需要支持以下功能：

1. 連接到Server
2. 發送網關信息
3. 定期報告設備狀態
4. 處理來自Server的消息

```python
async def connect_websocket(self):
    """連接到WebSocket服務器"""
    ws_url = f"{self.gateway_state.ws_url}?token={self.gateway_state.ws_token}"
    
    try:
        async with websockets.connect(ws_url) as websocket:
            self.gateway_state.ws_connection = websocket
            
            # 發送網關信息
            await self.send_gateway_info(websocket)
            
            # 啟動設備狀態報告任務
            self.gateway_state.device_report_task = asyncio.create_task(
                self.report_device_status(websocket)
            )
            
            # 處理接收到的消息
            await self.handle_messages(websocket)
    except Exception as e:
        # 如果連接失敗，嘗試重新連接
        await asyncio.sleep(5)
        asyncio.create_task(self.connect_websocket())
```

### 4.6 模式切換

```python
def switch_to_work_mode(self):
    """切換到工作模式並啟動WebSocket連接"""
    if self.gateway_state.mode == GatewayMode.SCAN:
        self.gateway_state.mode = GatewayMode.WORK
        
        # 在新線程中啟動WebSocket連接
        threading.Thread(target=self.start_websocket_connection, daemon=True).start()
```

### 4.7 設備狀態報告

```python
async def report_device_status(self, websocket):
    """定期報告設備狀態"""
    while True:
        if self.gateway_state.mode == GatewayMode.WORK:
            # 模擬掃描連接的設備
            self.scan_connected_devices()
            
            # 更新現有設備的狀態
            for device in self.gateway_state.connected_devices:
                device.update_status()
            
            # 發送設備狀態
            devices_data = [device.to_dict() for device in self.gateway_state.connected_devices]
            message = {
                "type": "deviceStatus",
                "devices": devices_data
            }
            
            try:
                await websocket.send(json.dumps(message))
            except Exception as e:
                pass
        
        # 每10秒報告一次
        await asyncio.sleep(10)
```

## 5. 網關設備發現與上報

網關模擬器應實現設備發現和上報功能，以支持設備綁定與初始化邏輯：

### 5.1 設備發現流程

```python
def discover_devices():
    """模擬發現周圍的設備"""
    # 模擬固定設備列表
    fixed_devices = [
        {
            'macAddress': 'AA:BB:CC:11:22:33',
            'name': 'EPD Display 1',
            'type': 'EPD',
            'battery': random.randint(80, 100),
            'rssi': random.randint(-70, -50)
        },
        {
            'macAddress': 'AA:BB:CC:44:55:66',
            'name': 'EPD Display 2',
            'type': 'EPD',
            'battery': random.randint(70, 90),
            'rssi': random.randint(-65, -45)
        }
    ]
    
    # 隨機添加額外的設備
    extra_devices_count = random.randint(0, 3)
    for i in range(extra_devices_count):
        mac_suffix = ':'.join([f'{random.randint(0, 255):02X}' for _ in range(6)])
        fixed_devices.append({
            'macAddress': mac_suffix,
            'name': f'Random EPD {i+1}',
            'type': 'EPD',
            'battery': random.randint(50, 100),
            'rssi': random.randint(-80, -40)
        })
    
    return fixed_devices
```

### 5.2 設備狀態上報

```python
def report_device_status(gateway_id, store_id):
    """向服務器報告發現的設備狀態"""
    devices = discover_devices()
    
    # 構建上報消息
    message = {
        'type': 'deviceStatus',
        'gatewayId': gateway_id,
        'storeId': store_id,
        'devices': devices
    }
    
    # 發送到WebSocket服務器
    if ws.connected:
        ws.send(json.dumps(message))
        print(f"已上報 {len(devices)} 個設備的狀態")
    else:
        print("WebSocket未連接，無法上報設備狀態")
```

### 5.3 定期上報設備狀態

```python
def start_device_reporting(gateway_id, store_id, interval=30):
    """開始定期上報設備狀態"""
    def report_task():
        while True:
            report_device_status(gateway_id, store_id)
            time.sleep(interval)
    
    # 在背景線程中執行
    threading.Thread(target=report_task, daemon=True).start()
```

## 6. 接收網關配置

## 7. 與其他組件的接口

### 7.1 與App的接口

Gateway模擬器通過HTTP與App通信，提供以下API：

1. **GET /api/status**：返回網關狀態
   - 響應：
     ```json
     {
       "mode": "scan",
       "macAddress": "AA:BB:CC:DD:EE:FF",
       "ipAddress": "*************",
       "model": "Gateway-Simulator-1.0",
       "firmwareVersion": "1.0.0",
       "connectedDevicesCount": 3
     }
     ```

2. **POST /api/connect**：接收WebSocket連接信息
   - 請求體：
     ```json
     {
       "wsUrl": "ws://server-address:port/ws/store/store_id/gateway/gateway_id",
       "wsToken": "jwt_token_for_gateway",
       "wsProtocol": "json"
     }
     ```
   - 響應：
     ```json
     {
       "success": true
     }
     ```

### 7.2 與Server的接口

Gateway模擬器通過WebSocket與Server通信，支持以下消息類型：

#### 發送消息：
1. **gatewayInfo**：網關信息
   ```json
   {
     "type": "gatewayInfo",
     "info": {
       "macAddress": "AA:BB:CC:DD:EE:FF",
       "ipAddress": "*************",
       "model": "Gateway-Simulator-1.0",
       "firmwareVersion": "1.0.0"
     }
   }
   ```

2. **deviceStatus**：設備狀態
   ```json
   {
     "type": "deviceStatus",
     "devices": [
       {
         "id": "device_id",
         "macAddress": "DD:EE:FF:00:11:22",
         "type": "EPD",
         "status": "online",
         "battery": 85,
         "signalStrength": -65,
         "lastSeen": 1234567890
       }
     ]
   }
   ```

#### 接收消息：
1. **welcome**：歡迎消息
2. **pong**：心跳回應
3. **restart**：重啟命令
4. **updateFirmware**：固件更新命令

## 8. 安全考慮

1. 驗證接收到的WebSocket連接信息
2. 處理連接錯誤和異常
3. 實施日誌記錄，便於問題診斷

## 9. 測試計劃

1. 單元測試：測試各個功能模塊
2. 集成測試：測試與Server和App的通信
3. 模擬測試：模擬各種網絡條件和錯誤情況

## 10. 部署考慮

1. 支持命令行參數配置
2. 提供Docker容器化部署選項
3. 支持日誌級別配置

## 11. 開發時間表

1. 基本架構設計：1天
2. HTTP服務器實現：2天
3. WebSocket客戶端實現：2天
4. 設備模擬實現：2天
5. 測試：2天
6. 文檔和優化：1天

總計：10個工作日
