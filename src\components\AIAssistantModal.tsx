import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Bo<PERSON>, Send, Setting<PERSON>, Loader, AlertCircle, CheckCircle } from 'lucide-react';
import { getAIConfig, sendAIMessage, AIMessage } from '../utils/api/aiAssistantApi';
import { buildEndpointUrl } from '../utils/api/apiConfig';

// Message interface is now imported as AIMessage from aiAssistantApi
type Message = AIMessage;

interface AIAssistantModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AIAssistantModal: React.FC<AIAssistantModalProps> = ({ isOpen, onClose }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isConfigured, setIsConfigured] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      checkAIConfiguration();
      if (messages.length === 0) {
        addWelcomeMessage();
      }
      // 聚焦輸入框
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const checkAIConfiguration = async () => {
    try {
      setIsLoading(true);
      const config = await getAIConfig();
      setIsConfigured(config.enabled && !!config.geminiApiKey);
    } catch (error) {
      console.error('檢查AI配置失敗:', error);
      setIsConfigured(false);
    } finally {
      setIsLoading(false);
    }
  };

  const addWelcomeMessage = async () => {
    try {
      // 從後端獲取 AI 自我介紹
      const response = await fetch(buildEndpointUrl('ai-assistant', 'introduction'));
      let content = '您好！我是EPD Agent。我可以幫助您：\n\n1. 🏪 創建新門店\n2. 📄 創建模板\n3. 👤 創建用戶帳號\n\n請告訴我您需要什麼幫助？';

      if (response.ok) {
        const data = await response.json();
        content = data.introduction || content;
      }

      const welcomeMessage: Message = {
        id: Date.now().toString(),
        type: 'ai',
        content,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    } catch (error) {
      console.error('獲取AI介紹失敗:', error);
      // 使用默認介紹
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        type: 'ai',
        content: '您好！我是EPD Agent。我可以幫助您：\n\n1. 🏪 創建新門店\n2. 📄 創建模板\n3. 👤 創建用戶帳號\n\n請告訴我您需要什麼幫助？',
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isProcessing) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsProcessing(true);

    try {
      const aiResponse = await sendAIMessage({
        message: inputValue,
        context: { previousMessages: messages.slice(-5) }
      });

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.response,
        timestamp: new Date(),
        executionResult: aiResponse.executionResult
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('發送消息失敗:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: '抱歉，處理您的請求時發生錯誤，請稍後重試。',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
      // 發送完成後重新聚焦輸入框
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleQuickExample = async () => {
    try {
      const response = await fetch(buildEndpointUrl('ai-assistant', 'quick-example'));
      if (response.ok) {
        const data = await response.json();
        setInputValue(data.example || '');
        // 聚焦輸入框
        setTimeout(() => inputRef.current?.focus(), 100);
      }
    } catch (error) {
      console.error('獲取快速範例失敗:', error);
    }
  };

  const formatMessage = (content: string) => {
    return content.split('\n').map((line, index) => (
      <React.Fragment key={index}>
        {line}
        {index < content.split('\n').length - 1 && <br />}
      </React.Fragment>
    ));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full h-[600px] flex flex-col">
        {/* 標題欄 */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            <Bot className="text-blue-500" size={20} />
            <h2 className="text-lg font-semibold text-gray-900">EPD Agent</h2>
            {isConfigured && (
              <div className="flex items-center space-x-1 text-green-600">
                <CheckCircle size={16} />
                <span className="text-sm">已配置</span>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => window.open('/settings', '_blank')}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              title="AI設定"
            >
              <Settings size={16} />
            </button>
            <button
              onClick={onClose}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* 內容區域 */}
        {isLoading ? (
          <div className="flex-1 flex items-center justify-center">
            <Loader className="animate-spin h-8 w-8 text-blue-500" />
            <span className="ml-2 text-gray-600">載入中...</span>
          </div>
        ) : !isConfigured ? (
          <div className="flex-1 flex items-center justify-center p-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">EPD Agent 未配置</h3>
              <p className="text-gray-600 mb-4">
                請先在系統設定中配置Gemini API Key以啟用EPD Agent功能。
              </p>
              <button
                onClick={() => window.open('/settings', '_blank')}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                前往設定
              </button>
            </div>
          </div>
        ) : (
          <>
            {/* 消息列表 */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${
                      message.type === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}
                  >
                    <div className="text-sm">{formatMessage(message.content)}</div>
                    {message.executionResult && (
                      <div className={`mt-2 p-2 rounded text-xs ${
                        message.executionResult.success
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        <div className="font-medium">
                          {message.executionResult.success ? '✅ 執行成功' : '❌ 執行失敗'}
                        </div>
                        <div>{message.executionResult.message}</div>
                      </div>
                    )}
                    <div className="text-xs opacity-70 mt-1">
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
              {isProcessing && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 rounded-lg p-3">
                    <Loader className="animate-spin h-4 w-4 text-gray-500" />
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* 輸入區域 */}
            <div className="border-t p-4">
              {/* 快速範例按鈕 */}
              <div className="mb-3">
                <button
                  onClick={handleQuickExample}
                  disabled={isProcessing}
                  className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  📝 快速範例：建立門店
                </button>
              </div>

              <div className="flex space-x-2">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="輸入您的問題或需求..."
                  className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  disabled={isProcessing}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isProcessing}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Send size={16} />
                </button>
              </div>
              <div className="text-xs text-gray-500 mt-2">
                按 Enter 發送，Shift + Enter 換行
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
