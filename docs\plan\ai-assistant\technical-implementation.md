# AI助手技術實現詳細文件

## 1. 系統配置擴展

### 1.1 新增AI配置分頁

#### 前端組件
```typescript
// src/components/system-config/AISettingsTab.tsx
import React, { useState, useEffect } from 'react';
import { Bot, Key, Settings, TestTube } from 'lucide-react';

interface AIConfig {
  geminiApiKey: string;
  enabled: boolean;
  model: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
}

export function AISettingsTab() {
  const [config, setConfig] = useState<AIConfig>({
    geminiApiKey: '',
    enabled: false,
    model: 'gemini-pro',
    maxTokens: 2048,
    temperature: 0.7,
    timeout: 30000
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<string | null>(null);

  // 載入配置、保存配置、測試連接等方法
}
```

#### 後端API擴展
```javascript
// server/routes/sysConfigApi.js 擴展
// 新增AI配置驗證函數
function validateAIConfig(config) {
  const validatedConfig = { ...config };
  
  // 驗證API Key格式
  if (validatedConfig.geminiApiKey && !validatedConfig.geminiApiKey.startsWith('AIza')) {
    throw new Error('無效的Gemini API Key格式');
  }
  
  // 限制參數範圍
  validatedConfig.maxTokens = Math.min(Math.max(validatedConfig.maxTokens || 2048, 100), 8192);
  validatedConfig.temperature = Math.min(Math.max(validatedConfig.temperature || 0.7, 0), 2);
  validatedConfig.timeout = Math.min(Math.max(validatedConfig.timeout || 30000, 5000), 120000);
  
  return validatedConfig;
}
```

### 1.2 配置加密存儲

```javascript
// server/utils/encryption.js
const crypto = require('crypto');

class ConfigEncryption {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.secretKey = process.env.CONFIG_ENCRYPTION_KEY || this.generateKey();
  }

  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.secretKey);
    cipher.setAAD(Buffer.from('ai-config'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }

  decrypt(encryptedData) {
    const decipher = crypto.createDecipher(this.algorithm, this.secretKey);
    decipher.setAAD(Buffer.from('ai-config'));
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

## 2. Gemini API整合

### 2.1 AI服務核心

```javascript
// server/services/aiService.js
const { GoogleGenerativeAI } = require('@google/generative-ai');

class AIService {
  constructor() {
    this.genAI = null;
    this.model = null;
    this.config = null;
    this.isInitialized = false;
  }

  async initialize() {
    try {
      // 從配置中獲取AI設定
      const aiConfig = await this.getAIConfig();
      
      if (!aiConfig.enabled || !aiConfig.geminiApiKey) {
        throw new Error('AI功能未啟用或API Key未配置');
      }

      this.config = aiConfig;
      this.genAI = new GoogleGenerativeAI(aiConfig.geminiApiKey);
      this.model = this.genAI.getGenerativeModel({ model: aiConfig.model });
      this.isInitialized = true;
      
      console.log('AI服務初始化成功');
    } catch (error) {
      console.error('AI服務初始化失敗:', error);
      throw error;
    }
  }

  async processUserRequest(userInput, context = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const prompt = this.buildPrompt(userInput, context);
      const result = await this.model.generateContent(prompt);
      const response = result.response;
      
      return this.parseAIResponse(response.text());
    } catch (error) {
      console.error('AI請求處理失敗:', error);
      throw new Error('AI處理失敗，請稍後重試');
    }
  }

  buildPrompt(userInput, context) {
    const systemPrompt = `
你是一個EPD管理系統的AI助手。你可以幫助用戶：
1. 創建門店 - 需要門店名稱、ID、地址、電話等信息
2. 創建模板 - 需要模板名稱、螢幕尺寸、顏色類型、方向等
3. 創建帳號 - 需要用戶名、姓名、郵箱、電話、角色等

請根據用戶輸入，識別意圖並提取相關信息。
如果信息不完整，請詢問缺失的必要信息。
回應格式必須是JSON，包含：
{
  "intent": "create_store|create_template|create_account|ask_info|unknown",
  "confidence": 0.0-1.0,
  "extractedData": {},
  "missingFields": [],
  "response": "回應文字"
}

當前上下文：${JSON.stringify(context)}
用戶輸入：${userInput}
`;

    return systemPrompt;
  }

  parseAIResponse(responseText) {
    try {
      // 嘗試解析JSON回應
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // 如果無法解析JSON，返回默認格式
      return {
        intent: 'unknown',
        confidence: 0.5,
        extractedData: {},
        missingFields: [],
        response: responseText
      };
    } catch (error) {
      console.error('解析AI回應失敗:', error);
      return {
        intent: 'unknown',
        confidence: 0.0,
        extractedData: {},
        missingFields: [],
        response: '抱歉，我無法理解您的請求，請重新描述。'
      };
    }
  }
}

module.exports = new AIService();
```

### 2.2 任務執行器

```javascript
// server/services/taskExecutor.js
class TaskExecutor {
  constructor() {
    this.storeApi = require('../routes/storeApi');
    this.templateApi = require('../routes/templateApi');
    this.userApi = require('../routes/userApi');
  }

  async executeTask(task, user) {
    switch (task.intent) {
      case 'create_store':
        return await this.createStore(task.extractedData, user);
      case 'create_template':
        return await this.createTemplate(task.extractedData, user);
      case 'create_account':
        return await this.createAccount(task.extractedData, user);
      default:
        throw new Error('不支援的任務類型');
    }
  }

  async createStore(storeData, user) {
    try {
      // 驗證必要字段
      if (!storeData.name) {
        throw new Error('門店名稱為必填項');
      }

      // 生成門店ID（如果未提供）
      if (!storeData.id) {
        storeData.id = this.generateStoreId(storeData.name);
      }

      // 設定默認值
      const storePayload = {
        id: storeData.id,
        name: storeData.name,
        address: storeData.address || '',
        phone: storeData.phone || '',
        managerId: storeData.managerId || user._id,
        status: 'active',
        importSystemData: storeData.importSystemData !== false
      };

      // 調用現有的門店創建API
      const Store = require('../models/Store');
      const newStore = await Store.createStore(storePayload);

      return {
        success: true,
        data: newStore,
        message: `門店 "${storeData.name}" 創建成功`
      };
    } catch (error) {
      console.error('創建門店失敗:', error);
      return {
        success: false,
        error: error.message,
        message: '門店創建失敗'
      };
    }
  }

  async createTemplate(templateData, user) {
    try {
      // 驗證必要字段
      if (!templateData.name || !templateData.screenSize) {
        throw new Error('模板名稱和螢幕尺寸為必填項');
      }

      // 設定默認值
      const templatePayload = {
        id: Date.now().toString(),
        name: templateData.name,
        type: 'SINGLE_DATA',
        screenSize: templateData.screenSize,
        color: templateData.color || 'BW',
        orientation: templateData.orientation || 'landscape',
        elements: templateData.elements || [],
        isSystemTemplate: templateData.isSystemTemplate || false,
        storeId: templateData.storeId
      };

      // 調用現有的模板創建API
      const Template = require('../models/Template');
      const newTemplate = await Template.createTemplate(templatePayload);

      return {
        success: true,
        data: newTemplate,
        message: `模板 "${templateData.name}" 創建成功`
      };
    } catch (error) {
      console.error('創建模板失敗:', error);
      return {
        success: false,
        error: error.message,
        message: '模板創建失敗'
      };
    }
  }

  generateStoreId(storeName) {
    // 將中文店名轉換為英文ID
    const pinyin = require('pinyin');
    const pinyinResult = pinyin(storeName, {
      style: pinyin.STYLE_NORMAL,
      heteronym: false
    }).flat().join('-');
    
    return `${pinyinResult}-${Date.now()}`.toLowerCase();
  }
}

module.exports = new TaskExecutor();
```

## 3. API路由

### 3.1 AI助手API

```javascript
// server/routes/aiAssistantApi.js
const express = require('express');
const router = express.Router();
const aiService = require('../services/aiService');
const taskExecutor = require('../services/taskExecutor');
const { authenticate, checkPermission } = require('../middleware/auth');

// AI對話處理
router.post('/chat', authenticate, async (req, res) => {
  try {
    const { message, context } = req.body;
    const user = req.user;

    // 處理用戶輸入
    const aiResponse = await aiService.processUserRequest(message, context);

    // 如果AI識別出明確的任務且信息完整，執行任務
    if (aiResponse.intent !== 'unknown' && 
        aiResponse.intent !== 'ask_info' && 
        aiResponse.missingFields.length === 0 &&
        aiResponse.confidence > 0.8) {
      
      const executionResult = await taskExecutor.executeTask(aiResponse, user);
      aiResponse.executionResult = executionResult;
    }

    res.json(aiResponse);
  } catch (error) {
    console.error('AI對話處理失敗:', error);
    res.status(500).json({
      intent: 'error',
      response: '抱歉，處理您的請求時發生錯誤，請稍後重試。',
      error: error.message
    });
  }
});

// 測試AI連接
router.post('/test', authenticate, checkPermission('system:view'), async (req, res) => {
  try {
    await aiService.initialize();
    const testResponse = await aiService.processUserRequest('測試連接', {});
    
    res.json({
      success: true,
      message: 'AI服務連接正常',
      response: testResponse
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'AI服務連接失敗',
      error: error.message
    });
  }
});

module.exports = router;
```

## 4. 前端AI助手界面

### 4.1 升級後的AI助手組件

```typescript
// src/components/AIAssistantModal.tsx
import React, { useState, useEffect, useRef } from 'react';
import { Bot, Send, Settings, Loader } from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  executionResult?: any;
}

interface AIAssistantModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AIAssistantModal: React.FC<AIAssistantModalProps> = ({ isOpen, onClose }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isConfigured, setIsConfigured] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen) {
      checkAIConfiguration();
      // 添加歡迎消息
      if (messages.length === 0) {
        addWelcomeMessage();
      }
    }
  }, [isOpen]);

  const checkAIConfiguration = async () => {
    try {
      const response = await fetch('/api/configs/ai-config');
      if (response.ok) {
        const config = await response.json();
        setIsConfigured(config.enabled && config.geminiApiKey);
      }
    } catch (error) {
      console.error('檢查AI配置失敗:', error);
      setIsConfigured(false);
    }
  };

  const addWelcomeMessage = () => {
    const welcomeMessage: Message = {
      id: Date.now().toString(),
      type: 'ai',
      content: '您好！我是EPD Agent。我可以幫助您：\n\n1. 創建新門店\n2. 創建模板\n3. 創建用戶帳號\n\n請告訴我您需要什麼幫助？',
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isProcessing) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsProcessing(true);

    try {
      const response = await fetch('/api/ai-assistant/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: inputValue,
          context: { previousMessages: messages.slice(-5) }
        }),
        credentials: 'include'
      });

      const aiResponse = await response.json();

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.response,
        timestamp: new Date(),
        executionResult: aiResponse.executionResult
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('發送消息失敗:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: '抱歉，處理您的請求時發生錯誤，請稍後重試。',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  };

  // 渲染組件...
};
```

這個技術實現文件提供了：

1. **系統配置擴展** - AI設定分頁和加密存儲
2. **Gemini API整合** - 完整的AI服務架構
3. **任務執行器** - 自動執行AI識別的任務
4. **API路由** - 處理AI對話和測試連接
5. **前端界面** - 升級後的AI助手組件

這個架構支援未來的功能擴展，並確保了安全性和可維護性。
