import React from 'react';
import { getAvailableColorsForColorType, mapColorToAvailableColor } from '../../../utils/colorConversion';
import { DisplayColorType } from '../../../types';

interface NonTransparentColorInputProps {
  value: string;
  onChange: (value: string) => void;
  colorType?: string | DisplayColorType;
  disabled?: boolean;
}

/**
 * 不含透明選項的受限制顏色選擇器組件
 * 專門用於條碼等不支援透明的元件
 */
export const NonTransparentColorInput: React.FC<NonTransparentColorInputProps> = ({
  value,
  onChange,
  colorType,
  disabled = false
}) => {
  // 如果沒有指定 colorType，回退到普通的顏色選擇器
  if (!colorType) {
    return (
      <div className="flex items-center gap-2">
        <input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          className="bg-gray-700 border-none rounded h-8 w-8 disabled:opacity-50"
        />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          className="flex-1 bg-gray-700 text-white px-2 py-1 rounded text-sm disabled:opacity-50"
        />
      </div>
    );
  }

  // 獲取可用顏色列表（不包含透明選項）
  const availableColors = getAvailableColorsForColorType(colorType);

  // 確保當前值在可用範圍內，如果是透明則映射到黑色
  const currentValue = value === 'transparent' 
    ? '#000000' 
    : (availableColors.includes(value.toUpperCase())
        ? value.toUpperCase()
        : mapColorToAvailableColor(value, colorType));

  // 處理顏色選擇
  const handleColorSelect = (color: string) => {
    if (!disabled) {
      onChange(color);
    }
  };

  // 獲取顏色名稱（用於顯示）
  const getColorName = (color: string): string => {
    switch (color.toUpperCase()) {
      case '#000000': return '黑色';
      case '#FFFFFF': return '白色';
      case '#FF0000': return '紅色';
      case '#FFFF00': return '黃色';
      case '#00FF00': return '綠色';
      case '#0000FF': return '藍色';
      case '#FF00FF': return '洋紅';
      case '#00FFFF': return '青色';
      default: return color;
    }
  };

  return (
    <div className="space-y-2">
      {/* 當前選中的顏色顯示 */}
      <div className="flex items-center gap-2 p-2 bg-gray-700 rounded">
        <div
          className="w-6 h-6 rounded border border-gray-500"
          style={{ backgroundColor: currentValue }}
        />
        <span className="text-sm text-white flex-1">
          {getColorName(currentValue)}
        </span>
      </div>

      {/* 可用顏色選擇器 */}
      <div className="grid grid-cols-4 gap-1 p-2 bg-gray-800 rounded">
        {/* 顏色選項（不包含透明） */}
        {availableColors.map((color) => (
          <button
            key={color}
            type="button"
            disabled={disabled}
            onClick={() => handleColorSelect(color)}
            className={`
              w-8 h-8 rounded border-2 transition-all duration-200
              ${currentValue === color
                ? 'border-blue-400 ring-2 ring-blue-400 ring-opacity-50'
                : 'border-gray-500 hover:border-gray-300'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-110'}
            `}
            style={{ backgroundColor: color }}
            title={`${getColorName(color)} (${color})`}
          >
            {/* 選中標記 */}
            {currentValue === color && (
              <div className="w-full h-full flex items-center justify-center">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default NonTransparentColorInput;
