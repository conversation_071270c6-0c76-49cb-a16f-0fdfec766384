# 預覽圖統一管理機制

## 背景

在系統中，預覽圖生成有兩個關鍵點：
1. 在修改綁定數據時（`BindDeviceDataModal.tsx`）
2. 在預覽圖實際發送到網關之前（`sendPreviewToGateway.js`）

之前，這兩個地方沒有統一的預覽圖保存邏輯，導致某些情況下預覽圖可能無法正確同步到設備記錄。

## 解決方案

我們實現了統一的預覽圖管理機制，主要包含以下組件：

### 1. 預覽圖管理工具 (`previewImageManager.ts`)

這是一個專用工具，提供以下功能：
- `savePreviewImageToDevice()`: 保存預覽圖到設備記錄
- `handlePreviewImageGenerated()`: 統一處理預覽圖生成回調

### 2. 增強的 `PreviewComponent`

我們對 `PreviewComponent` 進行了增強：
- 添加了 `deviceId` 和 `storeId` 可選參數
- 在生成預覽圖後，自動將預覽圖保存到設備記錄中

### 3. 修改 `BindDeviceDataModal`

修改了 `BindDeviceDataModal.tsx`，使其使用新的管理機制：
- 傳遞 `deviceId` 和 `storeId` 到 `PreviewComponent`
- 使用 `savePreviewImageToDevice` 函數替代直接的 API 調用

## 使用方法

### 在 PreviewComponent 中自動保存預覽圖

只需向 `PreviewComponent` 傳遞 `deviceId` 和可選的 `storeId` 參數即可啟用自動保存功能：

```tsx
<PreviewComponent
  template={selectedTemplate}
  bindingData={formData.dataBindings}
  storeData={storeData}
  effectType="blackAndWhite"
  threshold={128}
  onPreviewGenerated={handlePreviewGenerated}
  deviceId={device._id}  // 啟用自動保存
  storeId={store?.id}    // 可選的門店ID
/>
```

### 手動保存預覽圖

在需要手動保存預覽圖的地方，可以使用 `savePreviewImageToDevice` 函數：

```tsx
import { savePreviewImageToDevice } from '../utils/previewImageManager';

// 在生成預覽圖後保存
const previewImage = await generatePreview();
if (previewImage && deviceId) {
  await savePreviewImageToDevice(deviceId, previewImage, storeId);
}
```

## 原理

1. 當 `PreviewComponent` 生成預覽圖後，如果提供了 `deviceId`，它將自動調用 `savePreviewImageToDevice` 來保存預覽圖
2. `savePreviewImageToDevice` 函數會使用 `updateDevice` API 將預覽圖寫入設備記錄
3. 這樣確保了無論在何處生成預覽圖，都能統一保存到設備記錄中

## 後續優化建議

1. 考慮添加預覽圖壓縮功能，以減少存儲和傳輸負擔
2. 添加預覽圖緩存機制，避免重複生成相同的預覽圖
3. 考慮將預覽圖與設備數據分離存儲，減輕主數據庫負擔
