/**
 * Node.js 環境適配器
 * 用於在後端 Node.js 環境中使用顏色轉換模組
 */

const { createCanvas } = require('canvas');

/**
 * Node.js 環境適配器實現
 */
const NodeAdapter = {
  createCanvas: (width, height) => {
    return createCanvas(width, height);
  },
  getContext2D: (canvas) => {
    return canvas.getContext('2d');
  }
};

/**
 * 顏色轉換選項介面（JavaScript 版本）
 */
const defaultColorConversionOptions = {
  threshold: 128,
  ditherStrength: 1.0,
  preserveAlpha: true
};

/**
 * DisplayColorType 枚舉（JavaScript 版本）
 */
const DisplayColorType = {
  BW: "Gray16",
  BWR: "Black & White & Red",
  BWRY: "Black & White & Red & Yellow",
  ALL: "All colors"
};

/**
 * 根據 colorType 獲取推薦的轉換策略
 */
function getConversionStrategy(colorType) {
  const normalizedColorType = typeof colorType === 'string'
    ? colorType.toUpperCase()
    : colorType;

  switch (normalizedColorType) {
    case DisplayColorType.BW:
    case 'GRAY16':
    case 'BW':
      return {
        algorithm: 'gray16',
        options: { levels: 16, preserveAlpha: true }
      };

    case DisplayColorType.BWR:
    case 'BLACK & WHITE & RED':
    case 'BWR':
      return {
        algorithm: 'colorQuantization',
        options: { threshold: 128, preserveAlpha: true }
      };

    case DisplayColorType.BWRY:
    case 'BLACK & WHITE & RED & YELLOW':
    case 'BWRY':
      return {
        algorithm: 'colorQuantization',
        options: { threshold: 128, preserveAlpha: true }
      };

    case DisplayColorType.ALL:
    case 'ALL COLORS':
    case 'ALL':
      return {
        algorithm: 'original',
        options: { preserveAlpha: true }
      };

    default:
      console.warn(`未知的顏色類型: ${colorType}，使用黑白二值化作為默認轉換`);
      return {
        algorithm: 'blackAndWhite',
        options: { threshold: 128, preserveAlpha: true }
      };
  }
}

/**
 * 複製畫布
 */
function cloneCanvas(sourceCanvas, adapter) {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);

  if (!ctx) {
    throw new Error('無法獲取畫布上下文');
  }

  ctx.drawImage(sourceCanvas, 0, 0);
  return newCanvas;
}

/**
 * 轉換為黑白二值化圖像
 */
function convertToBlackAndWhite(sourceCanvas, adapter, options) {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);
  const sourceCtx = adapter.getContext2D(sourceCanvas);

  if (!ctx || !sourceCtx) {
    throw new Error('無法獲取畫布上下文');
  }

  const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
  const data = imageData.data;
  const threshold = options.threshold || 128;

  for (let i = 0; i < data.length; i += 4) {
    const grayscale = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
    const color = grayscale < threshold ? 0 : 255;

    data[i] = data[i + 1] = data[i + 2] = color;

    if (!options.preserveAlpha) {
      data[i + 3] = 255;
    }
  }

  ctx.putImageData(imageData, 0, 0);
  return newCanvas;
}

/**
 * 轉換為灰階圖像
 */
function convertToGrayscale(sourceCanvas, adapter, options) {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);
  const sourceCtx = adapter.getContext2D(sourceCanvas);

  if (!ctx || !sourceCtx) {
    throw new Error('無法獲取畫布上下文');
  }

  const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
  const data = imageData.data;

  for (let i = 0; i < data.length; i += 4) {
    const grayscale = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
    data[i] = data[i + 1] = data[i + 2] = grayscale;

    if (!options.preserveAlpha) {
      data[i + 3] = 255;
    }
  }

  ctx.putImageData(imageData, 0, 0);
  return newCanvas;
}

/**
 * 轉換為16階灰度圖像
 */
function convertToGray16(sourceCanvas, adapter, options) {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);
  const sourceCtx = adapter.getContext2D(sourceCanvas);

  if (!ctx || !sourceCtx) {
    throw new Error('無法獲取畫布上下文');
  }

  const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
  const data = imageData.data;

  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];

    // 計算灰度值
    const gray = 0.299 * r + 0.587 * g + 0.114 * b;

    // 量化到16個級別
    const quantizedGray = Math.round(gray / 255 * 15) * 17; // 0, 17, 34, ..., 255

    data[i] = quantizedGray;
    data[i + 1] = quantizedGray;
    data[i + 2] = quantizedGray;

    if (!options.preserveAlpha) {
      data[i + 3] = 255;
    }
  }

  ctx.putImageData(imageData, 0, 0);
  return newCanvas;
}

/**
 * 轉換為抖動效果（Floyd-Steinberg 抖動算法）
 */
function convertToDithering(sourceCanvas, adapter, options) {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);
  const sourceCtx = adapter.getContext2D(sourceCanvas);

  if (!ctx || !sourceCtx) {
    throw new Error('無法獲取畫布上下文');
  }

  const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
  const data = new Uint8ClampedArray(imageData.data);
  const width = sourceCanvas.width;
  const height = sourceCanvas.height;

  // 轉換為灰階並保存在新數組
  const grayscale = new Uint8ClampedArray(width * height);
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = (y * width + x) * 4;
      grayscale[y * width + x] = Math.round(0.299 * data[idx] + 0.587 * data[idx + 1] + 0.114 * data[idx + 2]);
    }
  }

  // 應用 Floyd-Steinberg 抖動算法
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = y * width + x;
      const oldPixel = grayscale[idx];
      const newPixel = oldPixel < (options.threshold || 128) ? 0 : 255;
      grayscale[idx] = newPixel;

      const error = oldPixel - newPixel;
      const strength = options.ditherStrength || 1.0;

      // 將誤差分散到鄰近像素
      if (x + 1 < width) {
        grayscale[idx + 1] += error * strength * 7 / 16;
      }
      if (y + 1 < height) {
        if (x - 1 >= 0) {
          grayscale[(y + 1) * width + x - 1] += error * strength * 3 / 16;
        }
        grayscale[(y + 1) * width + x] += error * strength * 5 / 16;
        if (x + 1 < width) {
          grayscale[(y + 1) * width + x + 1] += error * strength * 1 / 16;
        }
      }
    }
  }

  // 將結果寫回圖像數據
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = (y * width + x) * 4;
      const grayIdx = y * width + x;
      const color = Math.max(0, Math.min(255, grayscale[grayIdx]));

      data[idx] = data[idx + 1] = data[idx + 2] = color;

      if (!options.preserveAlpha) {
        data[idx + 3] = 255;
      }
    }
  }

  // 在 Node.js 環境中，需要使用 createImageData 而不是 ImageData 構造函數
  const newImageData = ctx.createImageData(width, height);
  newImageData.data.set(data);
  ctx.putImageData(newImageData, 0, 0);
  return newCanvas;
}

/**
 * 根據顏色類型獲取調色板
 */
function getColorPalette(colorType) {
  const normalizedColorType = typeof colorType === 'string'
    ? colorType.toUpperCase()
    : colorType;

  switch (normalizedColorType) {
    case DisplayColorType.BWR:
    case 'BLACK & WHITE & RED':
    case 'BWR':
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 }      // 紅色
      ];

    case DisplayColorType.BWRY:
    case 'BLACK & WHITE & RED & YELLOW':
    case 'BWRY':
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 },     // 紅色
        { r: 255, g: 255, b: 0 }    // 黃色
      ];

    default:
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }  // 白色
      ];
  }
}

/**
 * 找到最接近的調色板顏色
 * 改進的算法，對電子紙顏色進行特殊處理
 */
function findClosestColor(r, g, b, palette) {
  // 對於四色電子紙（BWRY），使用特殊的顏色匹配邏輯
  if (palette.length === 4) {
    // 檢查是否為黃色（高紅色和綠色，低藍色）
    if (r > 180 && g > 180 && b < 100) {
      return { r: 255, g: 255, b: 0 }; // 黃色
    }
    // 檢查是否為紅色（高紅色，低綠色和藍色）
    if (r > 150 && g < 100 && b < 100) {
      return { r: 255, g: 0, b: 0 }; // 紅色
    }
    // 檢查是否為白色（高亮度）
    const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
    if (brightness > 128) {
      return { r: 255, g: 255, b: 255 }; // 白色
    } else {
      return { r: 0, g: 0, b: 0 }; // 黑色
    }
  }

  // 對於三色電子紙（BWR），使用特殊的顏色匹配邏輯
  if (palette.length === 3) {
    // 檢查是否為紅色（高紅色，低綠色和藍色）
    if (r > 150 && g < 100 && b < 100) {
      return { r: 255, g: 0, b: 0 }; // 紅色
    }
    // 檢查是否為白色（高亮度）
    const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
    if (brightness > 128) {
      return { r: 255, g: 255, b: 255 }; // 白色
    } else {
      return { r: 0, g: 0, b: 0 }; // 黑色
    }
  }

  // 對於其他情況，使用標準的歐幾里得距離
  let minDistance = Infinity;
  let closestColor = palette[0];

  for (const color of palette) {
    // 使用加權歐幾里得距離，對人眼敏感的綠色給予更高權重
    const distance = Math.sqrt(
      0.3 * Math.pow(r - color.r, 2) +
      0.59 * Math.pow(g - color.g, 2) +
      0.11 * Math.pow(b - color.b, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      closestColor = color;
    }
  }

  return closestColor;
}

/**
 * 轉換為顏色量化（用於多色電子紙屏幕）
 */
function convertToColorQuantization(sourceCanvas, colorType, adapter, options) {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);
  const sourceCtx = adapter.getContext2D(sourceCanvas);

  if (!ctx || !sourceCtx) {
    throw new Error('無法獲取畫布上下文');
  }

  const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
  const data = imageData.data;

  const palette = getColorPalette(colorType);

  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];

    const closestColor = findClosestColor(r, g, b, palette);

    data[i] = closestColor.r;
    data[i + 1] = closestColor.g;
    data[i + 2] = closestColor.b;

    if (!options.preserveAlpha) {
      data[i + 3] = 255;
    }
  }

  ctx.putImageData(imageData, 0, 0);
  return newCanvas;
}

/**
 * 主要的顏色轉換函數（Node.js 版本）
 */
function convertImageForColorType(sourceCanvas, colorType, customOptions = {}) {
  try {
    const strategy = getConversionStrategy(colorType);
    const options = { ...strategy.options, ...customOptions };

    let resultCanvas;

    switch (strategy.algorithm) {
      case 'original':
        resultCanvas = cloneCanvas(sourceCanvas, NodeAdapter);
        break;
      case 'blackAndWhite':
        resultCanvas = convertToBlackAndWhite(sourceCanvas, NodeAdapter, options);
        break;
      case 'grayscale':
        resultCanvas = convertToGrayscale(sourceCanvas, NodeAdapter, options);
        break;
      case 'gray16':
        resultCanvas = convertToGray16(sourceCanvas, NodeAdapter, options);
        break;
      case 'dithering':
        resultCanvas = convertToDithering(sourceCanvas, NodeAdapter, options);
        break;
      case 'colorQuantization':
        resultCanvas = convertToColorQuantization(sourceCanvas, colorType, NodeAdapter, options);
        break;
      default:
        throw new Error(`不支援的轉換算法: ${strategy.algorithm}`);
    }

    return {
      success: true,
      canvas: resultCanvas
    };
  } catch (error) {
    console.error('顏色轉換失敗:', error);
    return {
      success: false,
      error: error.message || '未知錯誤'
    };
  }
}

/**
 * 便利函數：直接根據 colorType 轉換圖像（Node.js 版本）
 */
function convertImageByColorType(sourceCanvas, colorType, customOptions = {}) {
  const result = convertImageForColorType(sourceCanvas, colorType, customOptions);

  if (result.success && result.canvas) {
    return result.canvas;
  } else {
    console.error('圖像轉換失敗:', result.error);
    return null;
  }
}

module.exports = {
  NodeAdapter,
  DisplayColorType,
  convertImageForColorType,
  convertImageByColorType,
  getConversionStrategy
};
