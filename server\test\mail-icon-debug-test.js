/**
 * Mail圖標調試測試 - 專門分析mail圖標的渲染問題
 */

const fs = require('fs');
const path = require('path');

/**
 * 測試Mail圖標的詳細渲染
 */
async function testMailIconDebug() {
  try {
    console.log('=== 開始Mail圖標調試測試 ===\n');

    // 引入預覽服務
    const { regeneratePreviewBeforeSend } = require('../services/previewService');

    // 測試案例：不同大小的mail圖標
    const testCases = [
      {
        name: 'mail-small',
        size: { width: 60, height: 60 },
        position: { x: 50, y: 50 },
        lineWidth: 2
      },
      {
        name: 'mail-medium',
        size: { width: 100, height: 100 },
        position: { x: 150, y: 50 },
        lineWidth: 2
      },
      {
        name: 'mail-large',
        size: { width: 140, height: 140 },
        position: { x: 280, y: 50 },
        lineWidth: 3
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n--- 調試測試: ${testCase.name} ---`);
      
      // 創建測試模板
      const testTemplate = {
        id: `mail-debug-${testCase.name}`,
        name: `Mail 調試測試 - ${testCase.name}`,
        width: 500,
        height: 250,
        screenSize: '500x250',
        elements: [
          {
            id: 'mail-icon',
            type: 'icon',
            x: testCase.position.x,
            y: testCase.position.y,
            width: testCase.size.width,
            height: testCase.size.height,
            iconType: 'mail',
            lineColor: '#000000',
            lineWidth: testCase.lineWidth
          }
        ]
      };

      // 創建測試設備數據
      const testDevice = {
        id: 'test-device',
        dataBindings: {}
      };

      // 創建測試門店數據
      const testStoreData = [
        {
          id: 'test-store',
          name: '測試門店',
          storeSpecificData: []
        }
      ];

      console.log(`生成Mail調試預覽圖: ${testCase.size.width}x${testCase.size.height}, 線寬: ${testCase.lineWidth}`);
      
      // 計算預期的實際icon尺寸和位置
      const containerWidth = testCase.size.width;
      const containerHeight = testCase.size.height;
      const expectedIconSize = Math.min(containerWidth, containerHeight) * 0.8;
      const expectedOffsetX = (containerWidth - expectedIconSize) / 2;
      const expectedOffsetY = (containerHeight - expectedIconSize) / 2;
      const expectedActualX = testCase.position.x + expectedOffsetX;
      const expectedActualY = testCase.position.y + expectedOffsetY;
      
      console.log(`預期位置調整:`);
      console.log(`  容器: (${testCase.position.x}, ${testCase.position.y}, ${containerWidth}x${containerHeight})`);
      console.log(`  實際: (${expectedActualX}, ${expectedActualY}, ${expectedIconSize}x${expectedIconSize})`);

      const result = await regeneratePreviewBeforeSend(testDevice, testStoreData, testTemplate, []);

      if (result) {
        // 將 base64 圖片保存到檔案
        const base64Data = result.replace(/^data:image\/png;base64,/, '');
        const outputPath = path.join(__dirname, `mail-debug-${testCase.name}-output.png`);
        fs.writeFileSync(outputPath, base64Data, 'base64');
        
        console.log(`✓ Mail調試預覽圖已保存到: ${outputPath}`);
        console.log(`  圖片大小: ${Math.round(base64Data.length * 0.75 / 1024)} KB`);
      } else {
        console.log(`✗ ${testCase.name} Mail調試預覽圖生成失敗`);
      }
    }

    // 創建綜合測試：同時顯示所有大小
    console.log(`\n--- 綜合測試: 所有大小的mail圖標 ---`);
    
    const comprehensiveTemplate = {
      id: 'mail-comprehensive',
      name: 'Mail 綜合測試',
      width: 500,
      height: 250,
      screenSize: '500x250',
      elements: testCases.map((testCase, index) => ({
        id: `mail-${index}`,
        type: 'icon',
        x: testCase.position.x,
        y: testCase.position.y,
        width: testCase.size.width,
        height: testCase.size.height,
        iconType: 'mail',
        lineColor: '#000000',
        lineWidth: testCase.lineWidth
      }))
    };

    const testDevice = {
      id: 'test-device',
      dataBindings: {}
    };

    const testStoreData = [
      {
        id: 'test-store',
        name: '測試門店',
        storeSpecificData: []
      }
    ];

    console.log('生成Mail綜合預覽圖...');
    const comprehensiveResult = await regeneratePreviewBeforeSend(testDevice, testStoreData, comprehensiveTemplate, []);

    if (comprehensiveResult) {
      const base64Data = comprehensiveResult.replace(/^data:image\/png;base64,/, '');
      const outputPath = path.join(__dirname, 'mail-comprehensive-output.png');
      fs.writeFileSync(outputPath, base64Data, 'base64');
      
      console.log(`✓ Mail綜合預覽圖已保存到: ${outputPath}`);
      console.log(`  圖片大小: ${Math.round(base64Data.length * 0.75 / 1024)} KB`);
    } else {
      console.log('✗ Mail綜合預覽圖生成失敗');
    }

    console.log('\n=== Mail圖標調試測試完成 ===');

  } catch (error) {
    console.error('測試過程中發生錯誤:', error);
    console.error('錯誤堆疊:', error.stack);
  }
}

// 執行測試
testMailIconDebug();
