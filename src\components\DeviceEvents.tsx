import React, { useEffect, useState } from 'react';
import { DeviceEvent } from '../types/deviceEvent';
import { getDeviceEvents } from '../utils/api/deviceApi';
import { getAllGateways } from '../utils/api/gatewayApi';
import { Gateway } from '../types/gateway';

interface DeviceEventsProps {
  deviceId: string;
}

interface GatewayMap {
  [key: string]: Gateway;
}

export function DeviceEvents({ deviceId }: DeviceEventsProps) {
  const [events, setEvents] = useState<DeviceEvent[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [limit] = useState(10);
  const [eventType, setEventType] = useState<string>('');
  const [gatewaysMap, setGatewaysMap] = useState<GatewayMap>({});

  // 獲取設備事件
  const fetchEvents = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const skip = page * limit;
      const response = await getDeviceEvents(deviceId, {
        limit,
        skip,
        ...(eventType ? { eventType } : {})
      });
      
      if (response.success) {
        setEvents(response.events);
        setTotal(response.total);
      } else {
        setError('獲取事件記錄失敗');
      }
    } catch (err: any) {
      setError(err.message || '獲取事件記錄失敗');
    } finally {
      setLoading(false);
    }
  };
  // 獲取網關信息並建立映射
  const fetchGateways = async () => {
    try {
      const gateways = await getAllGateways();
      
      // 建立網關ID到網關對象的映射
      const map: GatewayMap = {};
      gateways.forEach(gateway => {
        if (gateway._id) {
          map[gateway._id] = gateway;
        }
      });
      
      setGatewaysMap(map);
    } catch (err) {
      console.error('獲取網關信息失敗:', err);
    }
  };

  // 當設備ID、頁碼或事件類型變更時獲取事件
  useEffect(() => {
    if (deviceId) {
      fetchEvents();
    }
  }, [deviceId, page, eventType]);
  
  // 當設備ID變更時獲取網關信息
  useEffect(() => {
    if (deviceId) {
      fetchGateways();
    }
  }, [deviceId]);

  // 渲染事件類型篩選器
  const renderEventTypeFilter = () => (
    <div className="mb-4">
      <label className="mr-2">事件類型：</label>
      <select 
        value={eventType} 
        onChange={(e) => setEventType(e.target.value)}
        className="border rounded px-2 py-1"
      >
        <option value="">全部</option>
        <option value="discovered">設備被發現</option>
        <option value="user_binding">用戶綁定/解除綁定</option>
        <option value="gateway_changed">主要網關變更</option>
        <option value="data_update">數據更新</option>
        <option value="status_change">狀態變更</option>
      </select>
    </div>
  );
  // 獲取網關顯示名稱 (名稱 + MAC地址)
  const getGatewayDisplayName = (gatewayId: string) => {
    const gateway = gatewaysMap[gatewayId];
    if (gateway) {
      return `${gateway.name} (${gateway.macAddress})`;
    }
    return gatewayId; // 如果找不到網關，則顯示ID
  };

  // 格式化事件數據
  const formatEventData = (event: DeviceEvent) => {
    switch (event.eventType) {
      case 'discovered':
        // 如果事件中已經包含網關名稱，使用它，否則嘗試通過ID查找網關信息
        if (event.eventData.gatewayName) {
          return `設備被網關 ${event.eventData.gatewayName} 發現`;
        } else if (event.eventData.gatewayId) {
          return `設備被網關 ${getGatewayDisplayName(event.eventData.gatewayId)} 發現`;
        }
        return '設備被未知網關發現';
        
      case 'user_binding':
        if (event.eventData.action === 'bind') {
          return `設備綁定到用戶 ${event.eventData.userName || event.eventData.userId}`;
        } else if (event.eventData.action === 'unbind') {
          return `設備解除與用戶 ${event.eventData.previousUserName || '未知'} 的綁定`;
        }
        return `用戶綁定操作: ${event.eventData.action || '未知'}`;
        
      case 'gateway_changed':
        // 處理網關變更事件，顯示網關名稱和MAC
        const oldGateway = event.eventData.oldPrimaryGatewayId 
          ? getGatewayDisplayName(event.eventData.oldPrimaryGatewayId) 
          : '無';
        
        const newGateway = event.eventData.newPrimaryGatewayId 
          ? getGatewayDisplayName(event.eventData.newPrimaryGatewayId)
          : '無';
          
        return `設備主要網關從 ${oldGateway} 變更為 ${newGateway}`;
        
      case 'data_update':
        return `設備數據更新: ${JSON.stringify(event.eventData)}`;
        
      case 'status_change':
        return `設備狀態從 ${event.eventData.oldStatus || '未知'} 變更為 ${event.eventData.newStatus || '未知'}`;
        
      default:
        return `${event.eventType}: ${JSON.stringify(event.eventData)}`;
    }
  };

  // 渲染分頁
  const renderPagination = () => {
    const totalPages = Math.ceil(total / limit);
    return (
      <div className="flex justify-between items-center mt-4">
        <button 
          onClick={() => setPage(p => Math.max(0, p - 1))}
          disabled={page === 0 || loading}
          className={`px-3 py-1 border rounded ${page === 0 || loading ? 'bg-gray-200 text-gray-500' : 'bg-white'}`}
        >
          上一頁
        </button>
        
        <span>
          第 {page + 1} 頁，共 {totalPages} 頁，總計 {total} 條記錄
        </span>
        
        <button 
          onClick={() => setPage(p => p + 1)}
          disabled={page >= totalPages - 1 || loading}
          className={`px-3 py-1 border rounded ${page >= totalPages - 1 || loading ? 'bg-gray-200 text-gray-500' : 'bg-white'}`}
        >
          下一頁
        </button>
      </div>
    );
  };

  // 主要渲染
  return (
    <div className="mt-4">
      <h2 className="text-xl font-semibold mb-4">設備事件記錄</h2>
      
      {renderEventTypeFilter()}
      
      {loading && <p className="text-gray-500">載入中...</p>}
      
      {error && <p className="text-red-500">{error}</p>}
      
      {!loading && !error && events.length === 0 && (
        <p className="text-gray-500">沒有事件記錄</p>
      )}
      
      {events.length > 0 && (
        <div>
          <ul className="border rounded divide-y">
            {events.map(event => (
              <li key={event._id} className="p-3">
                <div className="flex justify-between">
                  <span className="font-semibold">
                    {new Date(event.timestamp).toLocaleString()}
                  </span>
                  <span className="px-2 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800">
                    {event.eventType}
                  </span>
                </div>
                <p className="mt-1">{formatEventData(event)}</p>
              </li>
            ))}
          </ul>
          
          {renderPagination()}
        </div>
      )}
    </div>
  );
}
