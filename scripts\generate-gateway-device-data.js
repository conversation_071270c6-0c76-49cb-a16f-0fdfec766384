// 生成網關和設備測試數據
import fetch from 'node-fetch';

// 配置
const API_BASE_URL = 'http://localhost:3001/api';
let TOKEN = '';

// 通用請求函數
async function makeRequest(endpoint, method = 'GET', data = null) {
  const url = `${API_BASE_URL}/${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': `token=${TOKEN}`
    },
    credentials: 'include'
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);

    // 檢查響應狀態
    if (!response.ok) {
      let errorMessage = `API error: ${response.status}`;

      try {
        const errorData = await response.json();
        errorMessage += ` - ${errorData.error || 'Unknown error'}`;
      } catch (jsonError) {
        errorMessage += ' - Could not parse error response';
      }

      throw new Error(errorMessage);
    }

    // 嘗試解析 JSON 響應
    try {
      const responseData = await response.json();
      return responseData;
    } catch (jsonError) {
      console.warn(`Warning: Could not parse JSON response from ${endpoint}`);
      return {}; // 返回空對象而不是拋出錯誤
    }
  } catch (error) {
    console.error(`Error making request to ${endpoint}:`, error.message);
    throw error;
  }
}

// 登入函數
async function login(username, password) {
  try {
    console.log(`Logging in as ${username}...`);
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.status}`);
    }

    // 嘗試從響應中獲取 token
    const data = await response.json();

    // 如果 headers 中沒有 token，嘗試從 response body 獲取
    if (data.token) {
      TOKEN = data.token;
      console.log('Login successful, token obtained from response body');
      return true;
    }

    throw new Error('Failed to obtain token from response');
  } catch (error) {
    console.error('Login error:', error.message);
    return false;
  }
}

// 獲取所有門店
async function getAllStores() {
  try {
    console.log('Getting all stores...');
    const response = await makeRequest('stores');

    // 檢查響應格式 - 處理不同的可能響應結構
    let stores = [];

    if (Array.isArray(response)) {
      // 如果響應直接是數組
      stores = response;
    } else if (response && typeof response === 'object') {
      // 如果響應是對象，嘗試獲取 stores 屬性
      if (Array.isArray(response.stores)) {
        stores = response.stores;
      } else if (response.data && Array.isArray(response.data)) {
        stores = response.data;
      }
    }

    console.log(`Found ${stores.length} stores`);
    return stores;
  } catch (error) {
    console.error('Failed to get stores:', error.message);
    return [];
  }
}

// 創建網關
async function createGateway(gatewayData) {
  console.log(`Creating gateway: ${gatewayData.name}`);
  try {
    // 檢查網關是否已存在
    try {
      const gateways = await makeRequest(`gateways?storeId=${gatewayData.storeId}`);
      const existingGateway = gateways.find(g => g.macAddress === gatewayData.macAddress);
      if (existingGateway) {
        console.log(`Gateway with MAC ${gatewayData.macAddress} already exists, skipping...`);
        return existingGateway;
      }
    } catch (err) {
      console.log('Error checking existing gateways, will try to create new one');
    }

    // 創建新網關
    const gateway = await makeRequest('gateways', 'POST', gatewayData);
    console.log(`Gateway created: ${gateway.name} (${gateway._id})`);
    return gateway;
  } catch (error) {
    console.error(`Failed to create gateway ${gatewayData.name}:`, error.message);
    return null;
  }
}

// 創建設備
async function createDevice(deviceData) {
  console.log(`Creating device with MAC: ${deviceData.macAddress} for store: ${deviceData.storeId}`);
  try {
    // 驗證必要欄位
    if (!deviceData.storeId) {
      throw new Error('門店ID不能為空');
    }

    // 確保 data 對象存在
    if (!deviceData.data) {
      deviceData.data = {};
    }

    // 按照新的設備模型結構添加缺少的字段
    // 通過API創建的設備標記為未初始化，需通過網關WebSocket更新才初始化
    deviceData.initialized = false;  // 新設備預設為未初始化
    
    if (deviceData.otherGateways === undefined) {
      deviceData.otherGateways = [];   // 初始時沒有其他網關
    }
    
    // 如果有 code 欄位，則移動到 data.imgcode
    if (deviceData.code !== undefined) {
      deviceData.data.imgcode = deviceData.code;
      delete deviceData.code;
    }

    // 檢查設備是否已存在
    try {
      const devices = await makeRequest(`devices?storeId=${deviceData.storeId}`);
      const existingDevice = devices.find(d => d.macAddress === deviceData.macAddress);
      if (existingDevice) {
        console.log(`Device with MAC ${deviceData.macAddress} already exists in store ${deviceData.storeId}, skipping...`);
        return existingDevice;
      }
    } catch (err) {
      console.log('Error checking existing devices, will try to create new one');
    }

    // 創建新設備
    const device = await makeRequest('devices', 'POST', deviceData);
    console.log(`Device created with MAC: ${device.macAddress} (${device._id}) for store: ${device.storeId}`);
    return device;
  } catch (error) {
    console.error(`Failed to create device with MAC ${deviceData.macAddress}:`, error.message);
    return null;
  }
}

// 生成隨機MAC地址
function generateRandomMac() {
  const hexDigits = "0123456789ABCDEF";
  let mac = "";
  for (let i = 0; i < 6; i++) {
    let part = "";
    for (let j = 0; j < 2; j++) {
      part += hexDigits.charAt(Math.floor(Math.random() * 16));
    }
    mac += part;
    if (i < 5) mac += ":";
  }
  return mac;
}

// 生成隨機IP地址
function generateRandomIp() {
  return `192.168.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`;
}

// 生成隨機RSSI值 (-30 到 -90)
function generateRandomRssi() {
  return -Math.floor(Math.random() * 60 + 30);
}

// 生成隨機電池電量 (10 到 100)
function generateRandomBattery() {
  return Math.floor(Math.random() * 90 + 10);
}

// 主函數
async function generateGatewayDeviceData() {
  try {
    console.log('Starting gateway and device test data generation...');

    // 先登入獲取 TOKEN
    const loginSuccess = await login('root', '123456789');
    if (!loginSuccess) {
      throw new Error('Failed to login, cannot proceed with data generation');
    }

    // 獲取所有門店
    const stores = await getAllStores();
    if (stores.length === 0) {
      throw new Error('No stores found, please run generate-test-data.js first');
    }

    // 找到測試門店
    const store1 = stores.find(s => s.id === 'TP001'); // 台北總店
    const store2 = stores.find(s => s.id === 'TC001'); // 台中分店
    const store3 = stores.find(s => s.id === 'KH001'); // 高雄分店

    if (!store1 || !store2 || !store3) {
      throw new Error('Test stores not found, please run generate-test-data.js first');
    }

    console.log('Found test stores:', {
      'TP001': store1.name,
      'TC001': store2.name,
      'KH001': store3.name
    });    // 為每個門店創建網關
    console.log('\n=== Creating Gateways ===');
    const gatewayModels = ['GW-2000', 'GW-3000', 'GW-4000', 'GW-5000', 'GW-6000'];
    const wifiFirmwareVersions = ['1.0.0', '1.1.0', '1.2.0', '2.0.0', '2.1.0'];
    const btFirmwareVersions = ['1.0.0', '1.1.0', '1.2.0', '2.0.0', '2.1.0'];
    
    // 為每個門店創建網關
    const gateways = [];
    const gatewaysPerStore = 5; // 每間門店至少5個網關
    const onlineGatewaysPerStore = 3; // 每間門店隨機3台為開機狀態    // 台北總店 - 5個網關，隨機3個在線
    console.log(`Creating ${gatewaysPerStore} gateways for ${store1.name}...`);
    
    // 先決定哪些索引的網關會是在線狀態
    const store1OnlineIndexes = [];
    while(store1OnlineIndexes.length < onlineGatewaysPerStore) {
      const randomIndex = Math.floor(Math.random() * gatewaysPerStore);
      if (!store1OnlineIndexes.includes(randomIndex)) {
        store1OnlineIndexes.push(randomIndex);
      }
    }
    
    for (let i = 0; i < gatewaysPerStore; i++) {
      const gateway = await createGateway({
        name: `${store1.name}-網關${i+1}`,
        macAddress: generateRandomMac(),
        status: store1OnlineIndexes.includes(i) ? 'online' : 'offline',
        model: gatewayModels[Math.floor(Math.random() * gatewayModels.length)],
        wifiFirmwareVersion: wifiFirmwareVersions[Math.floor(Math.random() * wifiFirmwareVersions.length)],
        btFirmwareVersion: btFirmwareVersions[Math.floor(Math.random() * btFirmwareVersions.length)],
        ipAddress: generateRandomIp(),
        storeId: store1.id,
        lastSeen: new Date() // 直接使用Date對象而非字符串
      });
      if (gateway) gateways.push(gateway);
    }    // 台中分店 - 5個網關，隨機3個在線
    console.log(`Creating ${gatewaysPerStore} gateways for ${store2.name}...`);
    
    // 先決定哪些索引的網關會是在線狀態
    const store2OnlineIndexes = [];
    while(store2OnlineIndexes.length < onlineGatewaysPerStore) {
      const randomIndex = Math.floor(Math.random() * gatewaysPerStore);
      if (!store2OnlineIndexes.includes(randomIndex)) {
        store2OnlineIndexes.push(randomIndex);
      }
    }
    
    for (let i = 0; i < gatewaysPerStore; i++) {
      const gateway = await createGateway({
        name: `${store2.name}-網關${i+1}`,
        macAddress: generateRandomMac(),
        status: store2OnlineIndexes.includes(i) ? 'online' : 'offline',
        model: gatewayModels[Math.floor(Math.random() * gatewayModels.length)],
        wifiFirmwareVersion: wifiFirmwareVersions[Math.floor(Math.random() * wifiFirmwareVersions.length)],
        btFirmwareVersion: btFirmwareVersions[Math.floor(Math.random() * btFirmwareVersions.length)],
        ipAddress: generateRandomIp(),
        storeId: store2.id,
        lastSeen: new Date() // 直接使用Date對象而非字符串
      });
      if (gateway) gateways.push(gateway);
    }    // 高雄分店 - 5個網關，隨機3個在線
    console.log(`Creating ${gatewaysPerStore} gateways for ${store3.name}...`);
    
    // 先決定哪些索引的網關會是在線狀態
    const store3OnlineIndexes = [];
    while(store3OnlineIndexes.length < onlineGatewaysPerStore) {
      const randomIndex = Math.floor(Math.random() * gatewaysPerStore);
      if (!store3OnlineIndexes.includes(randomIndex)) {
        store3OnlineIndexes.push(randomIndex);
      }
    }
    
    for (let i = 0; i < gatewaysPerStore; i++) {
      const gateway = await createGateway({
        name: `${store3.name}-網關${i+1}`,
        macAddress: generateRandomMac(),
        status: store3OnlineIndexes.includes(i) ? 'online' : 'offline',
        model: gatewayModels[Math.floor(Math.random() * gatewayModels.length)],
        wifiFirmwareVersion: wifiFirmwareVersions[Math.floor(Math.random() * wifiFirmwareVersions.length)],
        btFirmwareVersion: btFirmwareVersions[Math.floor(Math.random() * btFirmwareVersions.length)],
        ipAddress: generateRandomIp(),
        storeId: store3.id,
        lastSeen: new Date() // 直接使用Date對象而非字符串
      });
      if (gateway) gateways.push(gateway);
    }

    console.log(`Created ${gateways.length} gateways`);

    // 為每個門店創建設備
    console.log('\n=== Creating Devices ===');
    const deviceSizes = ['2.9"', '4.2"', '7.5"', '10.3"'];
    const dataIds = ['台北總店商品A', '台中分店商品A', '台中分店商品B', 'SYS001', 'SYS002', 'SYS003', 'SYS004', 'SYS005'];

    // 創建100個設備
    const devices = [];

    // 為每個門店分配設備數量
    const store1DeviceCount = 40; // 台北總店
    const store2DeviceCount = 30; // 台中分店
    const store3DeviceCount = 30; // 高雄分店    // 台北總店設備
    console.log(`\nCreating ${store1DeviceCount} devices for ${store1.name}...`);
    for (let i = 0; i < store1DeviceCount; i++) {
      const device = await createDevice({
        macAddress: generateRandomMac(),        data: {
          size: deviceSizes[Math.floor(Math.random() * deviceSizes.length)],
          rssi: generateRandomRssi(),
          battery: generateRandomBattery(),
          imgcode: `IMG${Math.floor(10000 + Math.random() * 90000)}` // 隨機圖片代碼
        },
        status: Math.random() > 0.2 ? 'online' : 'offline',
        dataId: dataIds[Math.floor(Math.random() * dataIds.length)],
        storeId: store1.id,  // 添加門店ID
        initialized: false,  // 預設未初始化
        primaryGatewayId: null, // 初始沒有主要網關
        otherGateways: [],  // 初始沒有其他網關
        lastSeen: new Date(), // 直接使用Date對象而非字符串
        note: `${store1.name} 設備 ${i+1}`
      });
      if (device) devices.push(device);
    }    // 台中分店設備
    console.log(`\nCreating ${store2DeviceCount} devices for ${store2.name}...`);
    for (let i = 0; i < store2DeviceCount; i++) {
      const device = await createDevice({
        macAddress: generateRandomMac(),        data: {
          size: deviceSizes[Math.floor(Math.random() * deviceSizes.length)],
          rssi: generateRandomRssi(),
          battery: generateRandomBattery(),
          imgcode: `IMG${Math.floor(10000 + Math.random() * 90000)}` // 隨機圖片代碼
        },
        status: Math.random() > 0.2 ? 'online' : 'offline',
        dataId: dataIds[Math.floor(Math.random() * dataIds.length)],
        storeId: store2.id,  // 添加門店ID
        initialized: false,  // 預設未初始化
        primaryGatewayId: null, // 初始沒有主要網關
        otherGateways: [],  // 初始沒有其他網關
        lastSeen: new Date(), // 直接使用Date對象而非字符串
        note: `${store2.name} 設備 ${i+1}`
      });
      if (device) devices.push(device);
    }    // 高雄分店設備
    console.log(`\nCreating ${store3DeviceCount} devices for ${store3.name}...`);
    for (let i = 0; i < store3DeviceCount; i++) {
      const device = await createDevice({
        macAddress: generateRandomMac(),        data: {
          size: deviceSizes[Math.floor(Math.random() * deviceSizes.length)],
          rssi: generateRandomRssi(),
          battery: generateRandomBattery(),
          imgcode: `IMG${Math.floor(10000 + Math.random() * 90000)}` // 隨機圖片代碼
        },
        status: Math.random() > 0.2 ? 'online' : 'offline',
        dataId: dataIds[Math.floor(Math.random() * dataIds.length)],
        storeId: store3.id,  // 添加門店ID
        initialized: false,  // 預設未初始化
        primaryGatewayId: null, // 初始沒有主要網關
        otherGateways: [],  // 初始沒有其他網關
        lastSeen: new Date(), // 直接使用Date對象而非字符串
        note: `${store3.name} 設備 ${i+1}`
      });
      if (device) devices.push(device);
    }    console.log(`\nCreated ${devices.length} devices`);
    
    // 綁定部分設備到網關和用戶
    console.log('\n=== Setting up Device Bindings ===');
    
    // 獲取用戶
    const users = await makeRequest('users');
    if (users && users.length > 0) {
      console.log(`Found ${users.length} users to use for binding devices`);
      
      // 選擇前兩個用戶進行測試
      const testUsers = users.slice(0, 2);
      console.log(`Will bind devices to users: ${testUsers.map(u => u.username).join(', ')}`);
      
      // 綁定部分設備到用戶
      console.log('\n--- Binding Devices to Users ---');
      let boundDevicesCount = 0;
      
      // 為每個測試用戶綁定幾個設備
      for (let i = 0; i < testUsers.length; i++) {
        const user = testUsers[i];
        // 為每個用戶綁定5個設備
        const devicesToBindCount = 5;
        const startIndex = i * devicesToBindCount;
        
        for (let j = 0; j < devicesToBindCount && (startIndex + j) < devices.length; j++) {
          const deviceId = devices[startIndex + j]._id;
          
          try {
            // 綁定設備到用戶
            console.log(`Binding device ${deviceId} to user ${user.username}`);
            await makeRequest(`devices/${deviceId}/bind-user`, 'POST', { userId: user._id });
            boundDevicesCount++;
          } catch (error) {
            console.error(`Failed to bind device ${deviceId} to user ${user.username}:`, error.message);
          }
        }
      }
      
      console.log(`Bound ${boundDevicesCount} devices to users`);
    } else {
      console.log('No users found, skipping device-user binding');
    }
      // 設置部分設備的主要網關和其他網關
    console.log('\n--- Setting Primary and Other Gateways for Devices ---');
    let primaryGatewaysSetCount = 0;
    let otherGatewaysSetCount = 0;
    
    // 為每個門店的設備設置主要網關和其他網關
    if (gateways.length > 0) {
      // 按門店分組網關
      const gatewaysByStore = {};
      gateways.forEach(gateway => {
        if (!gatewaysByStore[gateway.storeId]) {
          gatewaysByStore[gateway.storeId] = [];
        }
        gatewaysByStore[gateway.storeId].push(gateway);
      });
      
      // 為每個門店的設備設置網關
      for (const storeId in gatewaysByStore) {
        const storeGateways = gatewaysByStore[storeId];
        if (storeGateways.length > 0) {
          // 獲取該門店的設備
          const storeDevices = devices.filter(d => d.storeId === storeId);
          
          // 為所有設備處理網關關係
          for (let i = 0; i < storeDevices.length; i++) {
            const device = storeDevices[i];
              // 為大部分的設備（75%）設置主要網關
            const devicesToSetPrimaryCount = Math.floor(storeDevices.length * 0.75);
            
            if (i < devicesToSetPrimaryCount) {              // 隨機選擇一個網關作為主要網關，使網關的分布更加均勻
              const primaryIndex = Math.floor(Math.random() * storeGateways.length);
              const primaryGateway = storeGateways[primaryIndex];
              
              try {
                // 設置主要網關
                console.log(`Setting primary gateway ${primaryGateway._id} for device ${device._id}`);
                await makeRequest(`devices/${device._id}/set-primary-gateway`, 'POST', { 
                  gatewayId: primaryGateway._id 
                });
                primaryGatewaysSetCount++;
                  // 為此設備設置其他網關（除了主要網關之外的網關）
                if (storeGateways.length > 1) {
                  const otherGatewaysIds = [];
                  
                  // 隨機選擇2-4個其他網關，保證充分的測試覆蓋
                  const otherGatewayCount = storeGateways.length > 4 ?
                    Math.floor(Math.random() * 3) + 2 : // 如果網關總數>4，選擇2-4個
                    Math.min(2, storeGateways.length - 1); // 確保至少選擇2個，但不超過可用網關數量
                  
                  let selectedCount = 0;
                  while (selectedCount < otherGatewayCount) {
                    // 隨機選擇一個不是主要網關的網關
                    const randomIndex = Math.floor(Math.random() * storeGateways.length);
                    const randomGateway = storeGateways[randomIndex];
                    
                    if (randomGateway._id !== primaryGateway._id && !otherGatewaysIds.includes(randomGateway._id)) {
                      otherGatewaysIds.push(randomGateway._id);
                      selectedCount++;
                    }
                  }
                  
                  if (otherGatewaysIds.length > 0) {
                    // 更新設備的其他網關
                    console.log(`Setting ${otherGatewaysIds.length} other gateways for device ${device._id}`);
                    await makeRequest(`devices/${device._id}`, 'PUT', { 
                      otherGateways: otherGatewaysIds 
                    });
                    otherGatewaysSetCount += otherGatewaysIds.length;
                  }
                }
              } catch (error) {
                console.error(`Failed to set gateways for device ${device._id}:`, error.message);
              }
            } else {              // 對於沒有主要網關的設備，也設置一些其他網關
              try {
                // 隨機選擇2-4個網關作為其他網關
                const otherGatewayCount = Math.min(
                  Math.floor(Math.random() * 3) + 2, // 2-4個網關
                  storeGateways.length
                );
                const otherGatewaysIds = [];
                
                let selectedCount = 0;
                while (selectedCount < otherGatewayCount) {
                  const randomIndex = Math.floor(Math.random() * storeGateways.length);
                  const randomGateway = storeGateways[randomIndex];
                  
                  if (!otherGatewaysIds.includes(randomGateway._id)) {
                    otherGatewaysIds.push(randomGateway._id);
                    selectedCount++;
                  }
                }
                
                if (otherGatewaysIds.length > 0) {
                  // 更新設備的其他網關
                  console.log(`Setting ${otherGatewaysIds.length} other gateways for device ${device._id} (no primary)`);
                  await makeRequest(`devices/${device._id}`, 'PUT', { 
                    otherGateways: otherGatewaysIds 
                  });
                  otherGatewaysSetCount += otherGatewaysIds.length;
                }
              } catch (error) {
                console.error(`Failed to set other gateways for device ${device._id}:`, error.message);
              }
            }
          }
        }
      }
      
      console.log(`Set primary gateways for ${primaryGatewaysSetCount} devices`);
      console.log(`Set ${otherGatewaysSetCount} other gateway relationships`);
    } else {
      console.log('No gateways found, skipping device-gateway binding');
    }
    
    console.log('\nGateway and device test data generation completed!');
  } catch (error) {
    console.error('Error generating gateway and device test data:', error);
  }
}

// 執行
console.log('Starting script...');
generateGatewayDeviceData().catch(error => {
  console.error('Unhandled error in generateGatewayDeviceData:', error);
});

export {};
