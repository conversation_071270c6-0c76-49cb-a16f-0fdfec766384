# 預覽圖生成服務實現方案總結

## 方案概述

我們提出了一個完整的預覽圖生成服務實現方案，該方案結合了 Headless Browser 和共享渲染邏輯模塊，能夠在服務端生成與前端完全一致的預覽圖。這個方案解決了目前系統中服務端無法直接生成預覽圖的問題，為自動化預覽圖更新和批量處理提供了可能。

## 核心組件

1. **共享渲染邏輯模塊**：
   - 將現有的渲染邏輯抽取為平台無關的共享模塊
   - 設計環境適配器，確保在不同環境中都能正常工作
   - 提供統一的 API 接口

2. **預覽服務**：
   - 獨立的 Express 服務
   - 使用 Puppeteer 作為 Headless Browser
   - 提供 RESTful API 接口
   - 支持單個和批量預覽圖生成

3. **主服務器集成**：
   - 添加預覽服務客戶端
   - 修改現有代碼以調用預覽服務
   - 實現向後兼容性

## 技術選擇

1. **Headless Browser**：Puppeteer
   - 穩定性高，由 Google Chrome 團隊維護
   - 與 Chrome/Chromium 完全兼容，渲染效果一致
   - API 簡潔，易於集成

2. **服務端框架**：Express.js
   - 輕量級，適合微服務架構
   - 與現有後端技術棧一致
   - 易於擴展和維護

3. **共享模塊打包工具**：Rollup
   - 適合打包庫和模塊
   - 支持 ESM 和 CommonJS 輸出
   - Tree-shaking 效果好，輸出代碼體積小

## 實現步驟

1. **共享模塊開發**：
   - 抽取現有渲染邏輯
   - 設計環境適配器
   - 添加單元測試
   - 發布共享模塊包

2. **預覽服務開發**：
   - 搭建 Express 服務框架
   - 集成 Puppeteer
   - 實現預覽圖生成 API
   - 添加錯誤處理和日誌記錄

3. **主服務器整合**：
   - 添加預覽服務客戶端
   - 修改現有代碼以調用預覽服務
   - 實現向後兼容性

4. **部署和測試**：
   - 使用 Docker 部署預覽服務
   - 進行集成測試
   - 進行性能和負載測試
   - 監控和調優

## 優勢與效益

1. **一致性**：
   - 前端和服務端使用相同的渲染邏輯
   - 確保預覽圖效果完全一致
   - 減少維護成本

2. **自動化**：
   - 支持自動生成預覽圖
   - 支持批量處理
   - 減少人工操作

3. **性能**：
   - 使用瀏覽器實例池提高效率
   - 支持並行處理
   - 減少前端負擔

4. **擴展性**：
   - 易於添加新的渲染選項
   - 支持更多的預覽圖格式
   - 可以集成到其他系統中

## 實施時間表

| 階段 | 任務 | 時間估計 |
|------|------|----------|
| 階段一 | 準備工作 | 2天 |
| 階段二 | 共享模塊開發 | 5天 |
| 階段三 | 預覽服務開發 | 4天 |
| 階段四 | 主服務器整合 | 3天 |
| 階段五 | 前端整合 | 3天 |
| 階段六 | 部署和測試 | 3天 |
| **總計** | | **20天** |

## 實現總結

我們已經成功實現了預覽圖生成服務，該服務使用 Headless Browser (Puppeteer) 和共享渲染邏輯模組來在服務端生成與前端完全一致的預覽圖。以下是我們實現的主要組件：

1. **共享渲染邏輯模組 (shared-preview-renderer)**：
   - 將現有的渲染邏輯抽取為平台無關的共享模組
   - 設計環境適配器，確保在不同環境中都能正常工作
   - 提供統一的 API 接口
   - 使用 Rollup 打包，支持 ESM 和 CommonJS 輸出

2. **預覽服務 (preview-service)**：
   - 獨立的 Express 服務
   - 使用 Puppeteer 作為 Headless Browser
   - 提供 RESTful API 接口
   - 支持單個和批量預覽圖生成
   - 詳細的錯誤處理和日誌記錄

3. **主服務器整合**：
   - 添加預覽服務客戶端
   - 修改發送預覽圖到網關的服務，使其能夠使用預覽服務生成預覽圖
   - 實現向後兼容性

## 使用方法

### 啟動預覽服務

```bash
# 在項目根目錄下運行
node start-preview-service.js
```

或者使用 npm 腳本：

```bash
# 在項目根目錄下運行
npm run preview-service
```

### API 端點

預覽服務提供了以下 API 端點：

1. **生成單個預覽圖**

```
POST /api/preview/generate
```

請求體：
```json
{
  "template": {
    "id": "template-123",
    "name": "Template Name",
    "elements": [...],
    "width": 250,
    "height": 122
  },
  "bindingData": {
    "field1": "value1",
    "field2": "value2"
  },
  "storeData": [...],
  "effectType": "blackAndWhite",
  "threshold": 128
}
```

響應：
```json
{
  "success": true,
  "previewData": "data:image/png;base64,..."
}
```

2. **批量生成預覽圖**

```
POST /api/preview/batch-generate
```

請求體：
```json
{
  "items": [
    {
      "id": "device-1",
      "template": {...},
      "bindingData": {...},
      "storeData": [...]
    },
    ...
  ],
  "effectType": "blackAndWhite",
  "threshold": 128
}
```

響應：
```json
{
  "success": true,
  "results": [
    {
      "id": "device-1",
      "success": true,
      "previewData": "data:image/png;base64,..."
    },
    ...
  ]
}
```

3. **健康檢查**

```
GET /health
```

響應：
```json
{
  "status": "ok"
}
```

### 在主服務器中使用

在主服務器中，可以通過 `previewService.js` 中的函數調用預覽服務：

```javascript
const previewService = require('./services/previewService');

// 生成單個預覽圖
const previewData = await previewService.generatePreviewFromService(
  template,
  bindingData,
  storeData,
  { effectType: 'blackAndWhite', threshold: 128 }
);

// 批量生成預覽圖
const results = await previewService.batchGeneratePreviewsFromService(
  items,
  { effectType: 'blackAndWhite', threshold: 128 }
);

// 檢查預覽服務健康狀態
const isHealthy = await previewService.checkPreviewServiceHealth();
```

## 優勢與效益

1. **一致性**：
   - 前端和服務端使用相同的渲染邏輯，確保預覽圖效果完全一致
   - 共享模組確保渲染邏輯只需維護一次，減少代碼重複和不一致
   - 減少維護成本和潛在的錯誤

2. **自動化**：
   - 支持自動生成預覽圖，無需用戶手動操作
   - 支持批量處理，一次請求可處理多個預覽圖
   - 可與其他自動化流程集成，如設備數據更新時自動更新預覽圖
   - 減少人工操作，提高效率

3. **性能**：
   - 使用瀏覽器實例池提高效率，避免頻繁啟動和關閉瀏覽器
   - 支持並行處理，提高批量處理的效率
   - 減少前端負擔，將渲染工作轉移到服務端
   - 詳細的錯誤處理和日誌記錄，便於問題診斷和優化

4. **擴展性**：
   - 易於添加新的渲染選項，如不同的效果、分辨率等
   - 支持更多的預覽圖格式，如 PNG、JPEG、SVG 等
   - 可以集成到其他系統中，提供預覽圖生成服務
   - 模塊化設計，便於未來擴展和升級

5. **實用性**：
   - 解決了服務端無法直接生成預覽圖的問題
   - 支持在無頭環境中運行，適合服務器部署
   - 提供健康檢查 API，便於監控和管理
   - 詳細的文檔和示例，便於開發者使用和擴展

## 渲染到發送至WebSocket的流程圖

### 單一設備數據更新流程

```mermaid
flowchart TD
    A[設備數據更新] --> B[調用 updateDeviceDataBindings]
    B --> C{是否需要發送到網關?}
    C -->|是| D[獲取設備模板]
    C -->|否| Z[結束流程]
    D --> E[獲取綁定數據]
    E --> F[獲取門店數據]
    F --> G[調用預覽服務生成預覽圖]
    G --> H[將預覽圖轉換為設備格式]
    H --> I[通過WebSocket發送到網關]
    I --> J[網關轉發到設備]
    J --> K[設備顯示更新後的內容]
    K --> Z
```

### 批量設備數據更新流程

```mermaid
flowchart TD
    A[批量設備數據更新] --> B[調用 batchUpdateDeviceDataBindings]
    B --> C[遍歷設備列表]
    C --> D[獲取每個設備的模板]
    D --> E[獲取每個設備的綁定數據]
    E --> F[獲取門店數據]
    F --> G[調用預覽服務批量生成預覽圖]
    G --> H[將預覽圖轉換為設備格式]
    H --> I[通過WebSocket批量發送到網關]
    I --> J[網關轉發到各個設備]
    J --> K[設備顯示更新後的內容]
```

## Call Flow / Data Flow Chart

### 系統組件交互流程

```mermaid
sequenceDiagram
    participant Client as 前端客戶端
    participant Server as 主服務器
    participant Preview as 預覽服務
    participant Gateway as 網關
    participant Device as 電子紙設備

    Client->>Server: 更新設備數據綁定
    Server->>Server: 處理數據更新請求
    Server->>Preview: 請求生成預覽圖
    Preview->>Preview: 使用共享渲染邏輯生成預覽圖
    Preview-->>Server: 返回預覽圖數據
    Server->>Server: 將預覽圖轉換為設備格式
    Server->>Gateway: 通過WebSocket發送更新數據
    Gateway->>Device: 轉發數據到設備
    Device->>Device: 更新顯示內容
    Gateway-->>Server: 發送確認消息
    Server-->>Client: 返回更新結果
```

### 數據流程圖

```mermaid
flowchart LR
    subgraph Client[前端客戶端]
        A1[模板編輯器] --> A2[數據綁定界面]
        A2 --> A3[更新請求]
    end

    subgraph Server[主服務器]
        B1[API控制器] --> B2[數據處理服務]
        B2 --> B3[預覽服務客戶端]
        B3 --> B4[WebSocket管理器]
    end

    subgraph Preview[預覽服務]
        C1[API控制器] --> C2[渲染引擎]
        C2 --> C3[圖像處理]
    end

    subgraph Gateway[網關]
        D1[WebSocket客戶端] --> D2[設備管理器]
    end

    subgraph Device[電子紙設備]
        E1[通信模塊] --> E2[顯示控制器]
    end

    A3 --> B1
    B3 --> C1
    C3 --> B3
    B4 --> D1
    D2 --> E1
```

### 預覽圖生成和發送詳細流程

```mermaid
sequenceDiagram
    participant Server as 主服務器
    participant PreviewClient as 預覽服務客戶端
    participant PreviewService as 預覽服務
    participant WSManager as WebSocket管理器
    participant Gateway as 網關
    participant Device as 設備

    Server->>PreviewClient: generatePreviewFromService(template, bindingData, storeData, options)
    PreviewClient->>PreviewService: POST /api/preview/generate
    PreviewService->>PreviewService: 使用共享渲染邏輯生成預覽圖
    PreviewService-->>PreviewClient: 返回base64編碼的預覽圖
    PreviewClient-->>Server: 返回預覽圖數據
    Server->>Server: 將預覽圖轉換為設備格式
    Server->>WSManager: sendToGateway(gatewayId, deviceId, convertedData)
    WSManager->>WSManager: 查找網關連接
    WSManager->>Gateway: 發送WebSocket消息
    Gateway->>Gateway: 處理接收到的數據
    Gateway->>Device: 發送數據到設備
    Device->>Device: 更新顯示
    Gateway-->>WSManager: 發送確認消息
    WSManager-->>Server: 返回發送結果
```

## 系統架構與服務交互關係

### 系統服務架構圖

```mermaid
graph TB
    subgraph "系統服務架構"
        Client["前端服務 (Client)\n運行於瀏覽器\nReact + Vite"]
        MainServer["主服務器 (Server)\nExpress.js\n處理業務邏輯和數據存儲"]
        PreviewServer["預覽服務 (Preview Service)\nExpress.js + Puppeteer\n生成預覽圖"]

        Client <--> |"HTTP API 請求/響應"| MainServer
        MainServer <--> |"HTTP API 請求/響應"| PreviewServer
        Client <-.-> |"WebSocket連接\n(僅用於UI更新通知)"| MainServer

        Gateway["網關設備\n(外部)"]
        Device["電子紙設備\n(外部)"]

        MainServer <--> |"WebSocket連接"| Gateway
        Gateway <--> Device
    end
```

### 三個服務在更新流程中的工作關係

```mermaid
sequenceDiagram
    participant FE as 前端服務 (Client)
    participant MS as 主服務器 (Server)
    participant PS as 預覽服務 (Preview Service)
    participant GW as 網關 (Gateway)
    participant DEV as 設備 (Device)

    Note over FE, PS: 系統啟動階段
    FE->>FE: 啟動前端服務 (npm run client)
    MS->>MS: 啟動主服務器 (npm run server)
    PS->>PS: 啟動預覽服務 (npm run preview-service)
    MS->>MS: 建立WebSocket服務器等待網關連接

    Note over FE, PS: 網關連接階段
    GW->>MS: 通過WebSocket連接到主服務器
    MS->>MS: 註冊網關連接

    Note over FE, PS: 用戶操作階段
    FE->>FE: 用戶編輯模板或數據
    FE->>MS: 發送API請求更新設備數據綁定
    MS->>MS: 處理數據更新請求

    alt 直接更新數據 (不發送到設備)
        MS->>MS: 更新數據庫中的設備綁定數據
        MS-->>FE: 返回更新成功響應
    else 更新數據並發送到設備
        MS->>MS: 更新數據庫中的設備綁定數據
        MS->>MS: 獲取設備模板和綁定數據
        MS->>PS: 發送HTTP請求生成預覽圖
        PS->>PS: 使用Puppeteer和共享渲染邏輯生成預覽圖
        PS-->>MS: 返回base64編碼的預覽圖
        MS->>MS: 將預覽圖轉換為設備格式
        MS->>GW: 通過WebSocket發送更新數據
        GW->>DEV: 轉發數據到設備
        DEV->>DEV: 更新顯示
        GW-->>MS: 發送確認消息
        MS-->>FE: 返回更新成功響應
        MS->>FE: 可選：通過WebSocket通知前端更新已完成
    end
```

### 三個服務的數據流向圖

```mermaid
flowchart TD
    subgraph FE["前端服務 (Client)"]
        FE1[用戶界面] --> FE2[API客戶端]
        FE2 --> FE3[WebSocket客戶端]
    end

    subgraph MS["主服務器 (Server)"]
        MS1[API控制器] --> MS2[數據服務]
        MS2 --> MS3[預覽服務客戶端]
        MS2 --> MS4[WebSocket服務]
        MS5[數據庫連接器]
    end

    subgraph PS["預覽服務 (Preview Service)"]
        PS1[API控制器] --> PS2[Puppeteer控制器]
        PS2 --> PS3[共享渲染邏輯]
        PS3 --> PS4[圖像處理]
    end

    subgraph EXT["外部設備"]
        GW[網關] --> DEV[電子紙設備]
    end

    %% 服務間數據流向
    FE2 -->|HTTP請求| MS1
    MS1 -->|HTTP響應| FE2
    MS3 -->|HTTP請求| PS1
    PS1 -->|HTTP響應| MS3
    MS4 -->|WebSocket| GW
    FE3 <-.->|WebSocket通知| MS4

    %% 內部數據流向
    MS2 <-->|CRUD操作| MS5
```

## 結論

預覽圖生成服務的實現將顯著提升系統的功能和性能。通過將渲染邏輯抽取為共享模塊，並使用 Headless Browser 在服務端執行，我們可以確保前端和服務端生成的預覽圖完全一致，同時支持自動化和批量處理。

這個方案不僅解決了當前的問題，還為未來的擴展提供了基礎。隨著系統的發展，我們可以輕鬆添加更多的渲染選項和預覽圖格式，滿足不斷變化的需求。

實際測試表明，預覽服務能夠成功生成與前端完全一致的預覽圖，並且性能良好。這為系統提供了更多的可能性，如自動更新設備預覽圖、批量處理大量設備的預覽圖等。
