# WebSocket Localhost 修復報告

## 問題描述

在批量發送進度視窗中發現 WebSocket 連接使用了硬編碼的 `localhost`，這在真實產品環境中是不正確的。系統應該動態檢測服務器 IP 地址，而不是依賴 localhost。

## 問題影響

- 在非本機部署環境中，WebSocket 連接會失敗
- 批量發送進度無法正常顯示
- 網關連接可能受到影響

## 修復內容

### 1. 後端 API 修復

**文件**: `server/routes/authApi.js`

- 添加了 `buildWebSocketUrl` 工具函數的導入
- 修復了 `gateway-token` API 中的 WebSocket URL 構建邏輯
- 使用動態 IP 檢測替代硬編碼的 localhost

**修復前**:
```javascript
wsUrl: `ws://localhost:3001/ws/store/${storeId}/gateway/${gatewayId}?token=${gatewayToken}`
```

**修復後**:
```javascript
const wsPath = `/ws/store/${storeId}/gateway/${gatewayId}`;
const wsUrl = buildWebSocketUrl(wsPath, 3001);
const fullWsUrl = `${wsUrl}?token=${gatewayToken}`;
```

### 2. 測試文件修復

**文件**: `server/tests/test-ws-client-interactive.js`

- 添加了 `buildWebSocketUrl` 工具函數的導入
- 修復了回退邏輯中的 WebSocket URL 構建

**修復前**:
```javascript
url = `ws://localhost:3001/ws/store/${storeId}/gateway/${gatewayId}?token=${token}`;
```

**修復後**:
```javascript
const wsPath = `/ws/store/${storeId}/gateway/${gatewayId}`;
const wsBaseUrl = buildWebSocketUrl(wsPath, 3001);
url = `${wsBaseUrl}?token=${token}`;
```

**文件**: `server/tests/test-ws-client.js`

- 修復了基本測試客戶端的 WebSocket URL 構建

**文件**: `server/tests/generate-token.js`

- 修復了 token 生成測試中的 WebSocket URL 示例

### 3. 已確認正常的部分

以下部分已經正確使用動態 IP 檢測，無需修復：

- **前端 WebSocket 客戶端** (`src/utils/websocketClient.ts`): 使用 `window.location.hostname`
- **網關 API** (`server/routes/gatewayApi.js`): 已使用 `buildWebSocketUrl`
- **前端 API 配置** (`src/utils/api/apiConfig.ts`): 已有動態主機檢測

## 動態 IP 檢測機制

系統使用 `server/utils/networkUtils.js` 中的 `getServerIpAddress()` 函數：

1. **優先順序**：物理網卡 > 外部IP > 非虛擬內部IP
2. **排除虛擬網卡**：Docker、Hyper-V、VMware 等虛擬介面
3. **智能選擇**：優先選擇有預設閘道的網卡
4. **回退機制**：如果無法檢測到合適的 IP，回退到 localhost

## 測試結果

修復後的動態 IP 檢測測試：

```bash
$ node -e "const { getServerIpAddress, buildWebSocketUrl } = require('./server/utils/networkUtils'); console.log('檢測到的服務器IP:', getServerIpAddress()); console.log('WebSocket URL示例:', buildWebSocketUrl('/ws/test', 3001));"

檢測到的服務器IP: *************
WebSocket URL示例: ws://*************:3001/ws/test
```

## 修復驗證

1. ✅ 後端 API 不再返回 localhost URL
2. ✅ 測試文件使用動態 IP 檢測
3. ✅ 前端 WebSocket 客戶端已正確實現
4. ✅ 動態 IP 檢測功能正常運作

## 注意事項

- 此修復確保了系統在真實產品環境中的正確運行
- 保持了開發環境的兼容性（localhost 作為回退選項）
- 所有 WebSocket 連接現在都使用一致的動態 IP 檢測機制

## 相關文件

- `server/routes/authApi.js` - 主要修復
- `server/tests/test-ws-client-interactive.js` - 測試文件修復
- `server/tests/test-ws-client.js` - 基本測試修復
- `server/tests/generate-token.js` - Token 生成測試修復
- `server/utils/networkUtils.js` - 動態 IP 檢測工具（已存在）
