import React, { useState, useEffect, useRef } from 'react';
import { TemplateElement } from '../../../types';
import { ControlHandle } from './ShapeElement';
import { constrainElementToCanvas } from '../canvasUtils';
import { getAllDataFields } from '../../../utils/api/dataFieldApi';
import { getAllStoreData } from '../../../utils/api/storeDataApi';

// 根據旋轉角度計算控制點的 cursor 樣式
const getRotatedCursor = (handle: ControlHandle, rotation: number): string => {
  // 將旋轉角度標準化到 0-360 度
  const normalizedRotation = ((rotation % 360) + 360) % 360;

  // 根據控制點類型和旋轉角度計算 cursor
  switch (handle) {
    case ControlHandle.TopLeft:
    case ControlHandle.BottomRight:
      // 對角線控制點：nwse-resize 和 nesw-resize 之間切換
      if ((normalizedRotation >= 315 || normalizedRotation < 45) || (normalizedRotation >= 135 && normalizedRotation < 225)) {
        return 'nwse-resize';
      } else {
        return 'nesw-resize';
      }
    case ControlHandle.TopRight:
    case ControlHandle.BottomLeft:
      // 對角線控制點：nesw-resize 和 nwse-resize 之間切換
      if ((normalizedRotation >= 315 || normalizedRotation < 45) || (normalizedRotation >= 135 && normalizedRotation < 225)) {
        return 'nesw-resize';
      } else {
        return 'nwse-resize';
      }
    case ControlHandle.Top:
    case ControlHandle.Bottom:
      // 垂直控制點：ns-resize 和 ew-resize 之間切換
      if ((normalizedRotation >= 315 || normalizedRotation < 45) || (normalizedRotation >= 135 && normalizedRotation < 225)) {
        return 'ns-resize';
      } else {
        return 'ew-resize';
      }
    case ControlHandle.Left:
    case ControlHandle.Right:
      // 水平控制點：ew-resize 和 ns-resize 之間切換
      if ((normalizedRotation >= 315 || normalizedRotation < 45) || (normalizedRotation >= 135 && normalizedRotation < 225)) {
        return 'ew-resize';
      } else {
        return 'ns-resize';
      }
    default:
      return 'default';
  }
};

interface TextElementProps {
    element: TemplateElement;
    isSelected: boolean;
    onSelect: (id: string, e?: React.MouseEvent) => void;
    onUpdate: (id: string, updates: Partial<TemplateElement>) => void;
    zoom?: number;
    setSelectedTool?: (tool: string | null) => void;
    selectedElementIds?: string[];
    moveSelectedElements?: (dx: number, dy: number) => void;
    isMultiMoving?: boolean;
    onDragStart?: () => void;
    onDragEnd?: () => void;
}

export const TextElement: React.FC<TextElementProps> = ({
    element,
    isSelected,
    onSelect,
    onUpdate,
    zoom = 100,
    setSelectedTool,
    selectedElementIds = [],
    moveSelectedElements,
    isMultiMoving = false,
    onDragStart,
    onDragEnd
}) => {
    const elementRef = useRef<HTMLDivElement>(null);
    const textInputRef = useRef<HTMLInputElement>(null);

    const [isDragging, setIsDragging] = useState(false);
    const [startDragPosition, setStartDragPosition] = useState({ x: 0, y: 0 });
    const [isResizing, setIsResizing] = useState(false);
    const [activeHandle, setActiveHandle] = useState<ControlHandle | null>(null);
    const [isRotating, setIsRotating] = useState(false);
    const [rotationStartAngle, setRotationStartAngle] = useState(0);
    // 添加編輯狀態
    const [isEditing, setIsEditing] = useState(false);
    const [editValue, setEditValue] = useState('');
    const [previewContent, setPreviewContent] = useState<string | null>(null);

    // 文字相關屬性
    const fontSize = element.fontSize || 14;
    const fontFamily = element.fontFamily || 'Arial';
    const textColor = element.lineColor || '#000000';
    const content = previewContent !== null ? previewContent : (element.content || '');

    // 檢查是否綁定了資料欄位
    const hasBoundDataField = !!element.dataFieldId || !!element.dataBinding?.fieldId;

    // 是否為多選狀態
    const isMultiSelected = selectedElementIds.length > 1 && selectedElementIds.includes(element.id);
    // 元件是否被鎖定
    const isLocked = element.locked === true;

    // 處理雙擊事件 - 進入編輯模式
    const handleDoubleClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        // 如果已綁定資料欄位、多選模式下、或元件被鎖定，則不允許直接編輯
        if (!isMultiSelected && !hasBoundDataField && !isLocked) {
            setIsEditing(true);
            setEditValue(content);
            // 在下一個渲染循環中聚焦文字輸入框
            setTimeout(() => {
                if (textInputRef.current) {
                    textInputRef.current.focus();
                    textInputRef.current.select();
                }
            }, 0);
        }
    };

    // 計算文字寬度的函數
    const calculateTextWidth = (text: string, font: string): number => {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        if (context) {
            context.font = font;
            const metrics = context.measureText(text);
            return metrics.width;
        }
        return 0;
    };

    // 處理文字編輯完成
    const handleTextEditComplete = () => {
        if (editValue.trim() !== '') {
            // 根據文字內容自動調整寬度
            const fontStyle = `${fontSize}px ${fontFamily}`;
            const textWidth = calculateTextWidth(editValue, fontStyle);
            // 添加一些padding和額外空間以確保文字不會被截斷
            const newWidth = Math.max(textWidth + 20, 30);

            // 計算適合的高度（根據字體大小）
            // 單行文字的高度基本上是字體大小加上一些padding
            const lineHeight = fontSize * 1.1;
            const newHeight = Math.max(lineHeight + 4, 16);

            // 更新元素
            onUpdate(element.id, {
                content: editValue,
                width: newWidth,
                height: newHeight
            });
        }
        setIsEditing(false);
    };

    // 處理Enter鍵按下事件
    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleTextEditComplete();
        } else if (e.key === 'Escape') {
            setIsEditing(false);
        }
    };

    // 處理滑鼠按下事件 - 開始拖曳
    const handleMouseDown = (e: React.MouseEvent) => {
        if (!elementRef.current || isResizing || isRotating || isEditing || isLocked) return;

        e.stopPropagation();
        setIsDragging(true);
        setStartDragPosition({ x: e.clientX, y: e.clientY });

        // 通知開始拖曳
        if (onDragStart) {
            onDragStart();
        }

        // 確保元素被選中
        if (!isSelected) {
            onSelect(element.id);
        }
    };

    // 處理控制點滑鼠按下事件 - 開始調整大小或旋轉
    const handleControlPointMouseDown = (handle: ControlHandle, e: React.MouseEvent) => {
        e.stopPropagation();

        // 通知開始拖曳
        if (onDragStart) {
            onDragStart();
        }

        if (handle === ControlHandle.Rotate) {
            // 旋轉處理
            setIsRotating(true);
            // 計算元素中心點
            const rect = elementRef.current?.getBoundingClientRect();
            if (rect) {
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                // 計算滑鼠初始位置與中心點的角度
                const startAngle = Math.atan2(
                    e.clientY - centerY,
                    e.clientX - centerX
                ) * (180 / Math.PI);
                setRotationStartAngle(startAngle - (element.rotation || 0));
            }
        } else {
            // 調整大小處理
            setIsResizing(true);
            setActiveHandle(handle);
        }
    };

    // 處理滑鼠移動事件 - 拖曳、調整大小、旋轉
    useEffect(() => {
        const handleMouseMove = (e: MouseEvent) => {
            if (isDragging) {
                // 元素拖曳移動
                const deltaX = e.clientX - startDragPosition.x;
                const deltaY = e.clientY - startDragPosition.y;

                // 根據縮放比例調整移動量
                const scaledDeltaX = deltaX / (zoom / 100);
                const scaledDeltaY = deltaY / (zoom / 100);

                // 多選狀態下移動所有選中元素
                if (isMultiSelected && moveSelectedElements) {
                    moveSelectedElements(scaledDeltaX, scaledDeltaY);
                } else {
                    // 單選狀態下只移動當前元素
                    // 計算新位置
                    const newX = Math.round(element.x + scaledDeltaX);
                    const newY = Math.round(element.y + scaledDeltaY);

                    // 獲取畫布的寬高
                    const canvasElement = elementRef.current?.closest('[data-canvas-width]');
                    const canvasWidth = canvasElement ?
                        parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
                    const canvasHeight = canvasElement ?
                        parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

                    // 使用限制函數確保元素在畫布內
                    const constrainedUpdates = constrainElementToCanvas(
                        { x: newX, y: newY, width: Math.round(element.width), height: Math.round(element.height) },
                        canvasWidth,
                        canvasHeight
                    );

                    // 更新元素位置
                    onUpdate(element.id, constrainedUpdates);
                }

                setStartDragPosition({ x: e.clientX, y: e.clientY });
            } else if (isRotating && elementRef.current) {
                // 處理旋轉
                const rect = elementRef.current.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;

                // 計算當前角度
                const currentAngle = Math.atan2(
                    e.clientY - centerY,
                    e.clientX - centerX
                ) * (180 / Math.PI);

                // 計算旋轉差值（考慮初始偏移）
                let newRotation = currentAngle - rotationStartAngle;

                // 按住Shift鍵時，將角度限制為15度的倍數
                if (e.shiftKey) {
                    newRotation = Math.round(newRotation / 15) * 15;
                }

                // 更新元素旋轉角度
                onUpdate(element.id, { rotation: newRotation });
            } else if (isResizing && activeHandle) {
                // 獲取元素的旋轉角度
                const rotation = element.rotation || 0;
                // 將角度轉換為弧度
                const rotationRad = (rotation * Math.PI) / 180;
                const cos = Math.cos(rotationRad);
                const sin = Math.sin(rotationRad);

                // 獲取畫布的寬高
                const canvasElement = elementRef.current?.closest('[data-canvas-width]');
                const canvasWidth = canvasElement ?
                    parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
                const canvasHeight = canvasElement ?
                    parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

                // 根據縮放比例調整移動量
                let scaledMovementX = e.movementX / (zoom / 100);
                let scaledMovementY = e.movementY / (zoom / 100);

                // 如果元素已旋轉，需要根據控制點類型和旋轉角度來調整拖曳方向
                if (rotation !== 0) {
                    // 將滑鼠移動量從螢幕座標系轉換到元件的本地座標系
                    // 這裡使用逆旋轉矩陣：cos(θ) sin(θ); -sin(θ) cos(θ)
                    const localMovementX = scaledMovementX * cos + scaledMovementY * sin;
                    const localMovementY = -scaledMovementX * sin + scaledMovementY * cos;

                    scaledMovementX = localMovementX;
                    scaledMovementY = localMovementY;
                }

                // 儲存原始大小，用於計算縮放比例
                const originalHeight = element.height;
                const originalFontSize = element.fontSize || 14;

                // 根據控制點位置計算新的尺寸和位置
                let newX = Math.round(element.x);
                let newY = Math.round(element.y);
                let newWidth = Math.round(element.width);
                let newHeight = Math.round(element.height);

                // 根據不同的控制點處理不同的調整邏輯
                switch (activeHandle) {
                    case ControlHandle.TopLeft:
                        newX = Math.round(element.x + scaledMovementX);
                        newY = Math.round(element.y + scaledMovementY);
                        newWidth = Math.round(element.width - scaledMovementX);
                        newHeight = Math.round(element.height - scaledMovementY);
                        break;
                    case ControlHandle.TopRight:
                        newY = Math.round(element.y + scaledMovementY);
                        newWidth = Math.round(element.width + scaledMovementX);
                        newHeight = Math.round(element.height - scaledMovementY);
                        break;
                    case ControlHandle.BottomLeft:
                        newX = Math.round(element.x + scaledMovementX);
                        newWidth = Math.round(element.width - scaledMovementX);
                        newHeight = Math.round(element.height + scaledMovementY);
                        break;
                    case ControlHandle.BottomRight:
                        newWidth = Math.round(element.width + scaledMovementX);
                        newHeight = Math.round(element.height + scaledMovementY);
                        break;
                    case ControlHandle.Top:
                        newY = Math.round(element.y + scaledMovementY);
                        newHeight = Math.round(element.height - scaledMovementY);
                        break;
                    case ControlHandle.Right:
                        newWidth = Math.round(element.width + scaledMovementX);
                        break;
                    case ControlHandle.Bottom:
                        newHeight = Math.round(element.height + scaledMovementY);
                        break;
                    case ControlHandle.Left:
                        newX = Math.round(element.x + scaledMovementX);
                        newWidth = Math.round(element.width - scaledMovementX);
                        break;
                }

                // 確保最小尺寸
                if (newWidth < 5) newWidth = 5;
                if (newHeight < 5) newHeight = 5;

                // 使用限制函數確保元素在畫布內
                const constrainedElement = constrainElementToCanvas(
                    {
                        x: newX,
                        y: newY,
                        width: newWidth,
                        height: newHeight,
                        rotation: element.rotation
                    },
                    canvasWidth,
                    canvasHeight
                );                // 計算字體大小的縮放比例
                // 對於文字元素，我們主要使用高度的變化來調整字體大小
                const heightRatio = (constrainedElement.height ?? originalHeight) / originalHeight;

                // 計算新的字體大小，並確保有一個最小值
                const newFontSize = Math.max(Math.round(originalFontSize * heightRatio), 5);

                // 更新元素，包括新的字體大小
                onUpdate(element.id, {
                    ...constrainedElement,
                    fontSize: newFontSize
                });
            }
        };

        const handleMouseUp = (e: MouseEvent) => {
            // 阻止事件冒泡，避免觸發畫布的點擊事件導致選取狀態被清除
            e.stopPropagation();

            // 如果剛完成拖曳操作，調用拖曳完成回調
            if ((isDragging || isResizing || isRotating) && onDragEnd) {
                onDragEnd();
            }

            setIsDragging(false);
            setIsResizing(false);
            setIsRotating(false);
            setActiveHandle(null);
        };

        if (isDragging || isResizing || isRotating) {
            window.addEventListener('mousemove', handleMouseMove);
            window.addEventListener('mouseup', handleMouseUp);
        }

        return () => {
            window.removeEventListener('mousemove', handleMouseMove);
            window.removeEventListener('mouseup', handleMouseUp);
        };
    }, [
        isDragging,
        isResizing,
        isRotating,
        startDragPosition,
        activeHandle,
        rotationStartAngle,
        element,
        onUpdate,
        zoom,
        isMultiSelected,
        moveSelectedElements
    ]);

    // 當點擊其他區域時結束編輯
    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (isEditing && textInputRef.current && !textInputRef.current.contains(e.target as Node)) {
                handleTextEditComplete();
            }
        };

        if (isEditing) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isEditing, editValue]);

    // 當元素加載時加載資料欄位和門店數據
    useEffect(() => {
        // 如果元素沒有綁定資料欄位，則不需要加載數據
        if (!hasBoundDataField) {
            setPreviewContent(null);
            return;
        }

        const fetchData = async () => {
            try {
                // 取得綁定的欄位ID和門店ID
                const fieldId = element.dataBinding?.fieldId || element.dataFieldId;
                const selectedStoreId = element.dataBinding?.selectedStoreId;

                // 同時加載資料欄位和門店數據
                const [fields, storeSpecificData] = await Promise.all([
                    getAllDataFields(),
                    // 如果有門店ID，則直接使用它獲取特定門店的數據
                    selectedStoreId ? getAllStoreData(selectedStoreId) : getAllStoreData()
                ]);

                console.log('獲取到的門店數據:', storeSpecificData);

                if (fieldId && selectedStoreId) {
                    // 處理新的 storeSpecificData 結構
                    // 當使用 storeId 參數時，API 直接返回該門店的 storeSpecificData 數組
                    if (Array.isArray(storeSpecificData)) {
                        // 獲取元素中的 dataIndex，這是用於預覽數據的索引
                        const dataIndex = element.dataBinding?.dataIndex || 0;

                        // 獲取元素中可能存在的 storeItemUid，這是特定商品的唯一識別碼
                        const storeItemUid = (element as any).storeItemUid || (element as any).storeItemSn; // 兼容舊版本

                        console.log(`使用預覽數據索引: ${dataIndex}, 商品唯一識別碼: ${storeItemUid}, 門店數據項數量: ${storeSpecificData.length}`);

                        // 如果有特定的商品UID，則優先使用該商品
                        if (storeItemUid) {
                            // 查找特定UID的商品
                            const specificItem = storeSpecificData.find(item => item.uid === storeItemUid);

                            if (specificItem && specificItem[fieldId] !== undefined && specificItem[fieldId] !== null) {
                                // 如果找到特定商品且有欄位數據，更新預覽內容
                                let displayValue = String(specificItem[fieldId]);

                                // 根據前綴設置決定是否顯示前綴
                                const field = fields.find(f => f.id === fieldId);
                                if (field && element.dataBinding?.displayOptions?.showPrefix) {
                                    // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
                                    displayValue = field.prefix ? `${field.prefix}: ${displayValue}` : `: ${displayValue}`;
                                }

                                console.log('從特定商品更新文字元件預覽內容為:', displayValue);
                                setPreviewContent(displayValue);
                            } else {
                                // 如果找不到特定商品或沒有欄位數據，顯示無數據提示
                                const field = fields.find(f => f.id === fieldId);
                                const fieldName = field ? field.name : fieldId;
                                setPreviewContent(`(無 ${fieldName} 數據)`);
                            }
                        }
                        // 如果沒有特定商品序號，則使用 dataIndex 索引
                        else if (dataIndex >= 0 && dataIndex < storeSpecificData.length) {
                            // 使用 dataIndex 索引獲取數據項
                            const dataItem = storeSpecificData[dataIndex];

                            // 檢查該數據項是否包含指定欄位
                            if (dataItem && dataItem[fieldId] !== undefined && dataItem[fieldId] !== null) {
                                // 如果數據項包含指定欄位，更新預覽內容
                                let displayValue = String(dataItem[fieldId]);

                                // 根據前綴設置決定是否顯示前綴
                                const field = fields.find(f => f.id === fieldId);
                                if (field && element.dataBinding?.displayOptions?.showPrefix) {
                                    // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
                                    displayValue = field.prefix ? `${field.prefix}: ${displayValue}` : `: ${displayValue}`;
                                }

                                console.log('從索引更新文字元件預覽內容為:', displayValue);
                                setPreviewContent(displayValue);
                            } else {
                                // 如果數據項不包含指定欄位，顯示無數據提示
                                const field = fields.find(f => f.id === fieldId);
                                const fieldName = field ? field.name : fieldId;
                                setPreviewContent(`(無 ${fieldName} 數據)`);
                            }
                        } else {
                            // 如果 dataIndex 超出範圍，顯示錯誤提示
                            setPreviewContent(`(資料索引 ${dataIndex + 1} 超出範圍)`);
                        }
                    } else {
                        // 處理舊的數據結構或錯誤情況
                        console.warn('獲取到的門店數據格式不符合預期');
                        setPreviewContent(`(數據格式錯誤)`);
                    }
                } else if (fieldId) {
                    // 如果有欄位ID但沒有門店ID，顯示預設內容
                    setPreviewContent(null);
                }
            } catch (error) {
                console.error('加載資料欄位或門店數據失敗:', error);
                setPreviewContent(null);
            }
        };

        fetchData();
    }, [element.id, element.dataBinding, element.dataFieldId, hasBoundDataField]);

    // 當字體大小變更時調整高度
    useEffect(() => {
        if (!isEditing) {
            // 計算適合的高度（根據字體大小）
            const lineHeight = fontSize * 1.1;
            const newHeight = Math.max(Math.round(lineHeight + 4), 16);

            // 只有當計算出的高度與當前高度不同時才更新
            if (Math.abs(newHeight - element.height) > 2) {
                onUpdate(element.id, { height: newHeight });
            }
        }
    }, [fontSize, element.height, isEditing, onUpdate, element.id]);

    return (
        <div
            ref={elementRef}
            style={{
                position: 'absolute',
                left: element.x,
                top: element.y,
                width: element.width,
                height: element.height,
                cursor: isLocked ? 'not-allowed' : (isSelected ? 'move' : 'pointer'),
                backgroundColor: isMultiSelected ? 'rgba(59, 130, 246, 0.15)' : 'transparent',
                transform: element.rotation ? `rotate(${element.rotation}deg)` : undefined,
                transformOrigin: 'center center',
                outline: isSelected ? (isLocked ? '1px dashed #f97316' : '1px dashed #3b82f6') : 'none',
                outlineOffset: '2px',
                zIndex: isSelected ? 10 : 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-start', // 單行文字左對齊
                userSelect: 'none' // 防止文字被選取
            }}
            onClick={(e) => {
                e.stopPropagation();

                // 如果正在進行多選移動，則不處理點擊選擇事件
                if (isMultiMoving) {
                    console.log('處於多選移動狀態，忽略選擇操作');
                    return;
                }

                // 處理Shift多選
                if (e.shiftKey) {
                    console.log('執行Shift+點擊多選');
                    // 如果按住Shift，則在多選中切換該元素
                    if (selectedElementIds.includes(element.id)) {
                        // 如果元素已經在多選中，且多選數量大於1，則從多選中移除
                        if (selectedElementIds.length > 1) {
                            const updatedSelectedIds = selectedElementIds.filter(id => id !== element.id);
                            if (updatedSelectedIds.length === 1) {
                                onSelect(updatedSelectedIds[0]);
                            }
                        }
                    } else {
                        // 如果元素未在多選中，則添加到多選
                        onSelect(element.id, e);
                    }
                } else {
                    // 如果沒有按Shift，則單選該元素
                    console.log('執行單選元素');
                    onSelect(element.id, e);
                }

                // 選中元素時取消工具選擇
                if (setSelectedTool) {
                    setSelectedTool(null);
                }
            }}
            onMouseDown={handleMouseDown}
            onDoubleClick={handleDoubleClick}
            data-element-id={element.id}
            data-element-type="text"
            data-has-binding={hasBoundDataField ? 'true' : 'false'}
            data-field-id={element.dataBinding?.fieldId || element.dataFieldId || ''}
            data-store-id={element.dataBinding?.selectedStoreId || ''}
            data-show-prefix={element.dataBinding?.displayOptions?.showPrefix ? 'true' : 'false'}
            data-index={element.dataBinding?.dataIndex !== undefined ? String(element.dataBinding.dataIndex) : '0'}
            data-item-uid={(element as any).storeItemUid || ''}
            data-debug-info={`dataIndex:${element.dataBinding?.dataIndex},uid:${(element as any).storeItemUid || 'none'}`}
            data-original-content={element.content || 'TEXT'}
        >
            {/* 渲染文字內容或編輯框 */}
            {isEditing ? (
                <input
                    ref={textInputRef}
                    type="text"
                    value={editValue}
                    onChange={(e) => setEditValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    onBlur={handleTextEditComplete}
                    style={{
                        width: '100%',
                        height: '100%',
                        border: 'none',
                        outline: 'none',
                        backgroundColor: 'white',
                        padding: '0px 1px',
                        fontSize: `${fontSize}px`,
                        fontFamily,
                        color: textColor,
                        position: 'absolute',
                        zIndex: 101,
                        userSelect: 'text'
                    }}
                />
            ) : (                <div
                    className="text-element-content"
                    style={{
                        fontSize: `${fontSize}px`,
                        fontFamily,
                        color: textColor,
                        padding: '0px 1px',
                        width: '100%',
                        height: '100%',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        display: 'flex',
                        alignItems: 'flex-start',
                        userSelect: 'none',
                        backgroundColor: hasBoundDataField ? 'rgba(59, 130, 246, 0.1)' : 'transparent',
                        border: hasBoundDataField ? '1px dashed rgba(59, 130, 246, 0.5)' : 'none',
                        borderRadius: hasBoundDataField ? '2px' : '0'
                    }}
                    data-is-bound={hasBoundDataField ? 'true' : 'false'}
                >
                    {content || 'Text'}

                    {hasBoundDataField && (
                        <div
                            className="binding-indicator"
                            style={{
                                position: 'absolute',
                                top: 0,
                                right: isLocked ? 20 : 0,
                                fontSize: isSelected ? '2px' : '4px',
                                color: 'white',
                                backgroundColor: isSelected ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.7)',
                                padding: '2px 4px',
                                borderRadius: '0 0 0 4px',
                                pointerEvents: 'none',
                                userSelect: 'none',
                                zIndex: 5
                            }}
                        >
                            已綁定
                        </div>
                    )}

                    {/* 鎖定狀態指示器 */}
                    {isLocked && (
                        <div
                            className="lock-indicator"
                            style={{
                                position: 'absolute',
                                top: 0,
                                right: 0,
                                fontSize: '8px',
                                color: 'white',
                                backgroundColor: 'rgba(249, 115, 22, 0.8)',
                                padding: '2px 4px',
                                borderRadius: '0 0 0 4px',
                                pointerEvents: 'none',
                                userSelect: 'none',
                                zIndex: 6
                            }}
                        >
                            🔒
                        </div>
                    )}
                </div>
            )}

            {/* 只在單選狀態下顯示控制點，多選狀態下不顯示，編輯時也不顯示，鎖定時也不顯示 */}
            {isSelected && !isMultiSelected && !isEditing && !isLocked && (
                <>
                    {/* 角落控制點 */}
                    <div
                        style={{
                            position: 'absolute',
                            left: -4,
                            top: -4,
                            width: 8,
                            height: 8,
                            backgroundColor: '#3b82f6',
                            border: '1px solid white',
                            borderRadius: '50%',
                            cursor: getRotatedCursor(ControlHandle.TopLeft, element.rotation || 0),
                            zIndex: 100
                        }}
                        onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.TopLeft, e)}
                    />
                    <div
                        style={{
                            position: 'absolute',
                            right: -4,
                            top: -4,
                            width: 8,
                            height: 8,
                            backgroundColor: '#3b82f6',
                            border: '1px solid white',
                            borderRadius: '50%',
                            cursor: getRotatedCursor(ControlHandle.TopRight, element.rotation || 0),
                            zIndex: 100
                        }}
                        onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.TopRight, e)}
                    />
                    <div
                        style={{
                            position: 'absolute',
                            left: -4,
                            bottom: -4,
                            width: 8,
                            height: 8,
                            backgroundColor: '#3b82f6',
                            border: '1px solid white',
                            borderRadius: '50%',
                            cursor: getRotatedCursor(ControlHandle.BottomLeft, element.rotation || 0),
                            zIndex: 100
                        }}
                        onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.BottomLeft, e)}
                    />
                    <div
                        style={{
                            position: 'absolute',
                            right: -4,
                            bottom: -4,
                            width: 8,
                            height: 8,
                            backgroundColor: '#3b82f6',
                            border: '1px solid white',
                            borderRadius: '50%',
                            cursor: getRotatedCursor(ControlHandle.BottomRight, element.rotation || 0),
                            zIndex: 100
                        }}
                        onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.BottomRight, e)}
                    />

                    {/* 邊緣控制點 */}
                    <div
                        style={{
                            position: 'absolute',
                            left: '50%',
                            top: -4,
                            width: 8,
                            height: 8,
                            marginLeft: -4,
                            backgroundColor: '#3b82f6',
                            border: '1px solid white',
                            borderRadius: '50%',
                            cursor: getRotatedCursor(ControlHandle.Top, element.rotation || 0),
                            zIndex: 100
                        }}
                        onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Top, e)}
                    />
                    <div
                        style={{
                            position: 'absolute',
                            right: -4,
                            top: '50%',
                            width: 8,
                            height: 8,
                            marginTop: -4,
                            backgroundColor: '#3b82f6',
                            border: '1px solid white',
                            borderRadius: '50%',
                            cursor: getRotatedCursor(ControlHandle.Right, element.rotation || 0),
                            zIndex: 100
                        }}
                        onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Right, e)}
                    />
                    <div
                        style={{
                            position: 'absolute',
                            left: '50%',
                            bottom: -4,
                            width: 8,
                            height: 8,
                            marginLeft: -4,
                            backgroundColor: '#3b82f6',
                            border: '1px solid white',
                            borderRadius: '50%',
                            cursor: getRotatedCursor(ControlHandle.Bottom, element.rotation || 0),
                            zIndex: 100
                        }}
                        onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Bottom, e)}
                    />
                    <div
                        style={{
                            position: 'absolute',
                            left: -4,
                            top: '50%',
                            width: 8,
                            height: 8,
                            marginTop: -4,
                            backgroundColor: '#3b82f6',
                            border: '1px solid white',
                            borderRadius: '50%',
                            cursor: getRotatedCursor(ControlHandle.Left, element.rotation || 0),
                            zIndex: 100
                        }}
                        onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Left, e)}
                    />

                    {/* 旋轉控制點 */}
                    <div
                        style={{
                            position: 'absolute',
                            left: '50%',
                            top: -25,
                            width: 8, // 稍微調大一點，使虛線效果更明顯
                            height: 8,
                            marginLeft: -4,
                            backgroundColor: 'transparent', // 改為透明背景
                            border: '0.5px dashed #3b82f6', // 改為虛線邊框
                            borderRadius: '50%',
                            cursor: 'url("data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22%3E%3Cpath fill=%22%23000000%22 d=%22M7.11 8.53L5.7 7.11C4.8 8.27 4.24 9.61 4.07 11h2.02c.14-.87.49-1.72 1.02-2.47zM6.09 13H4.07c.17 1.39.72 2.73 1.62 3.89l1.41-1.42c-.52-.75-.87-1.59-1.01-2.47zm1.01 5.32c1.16.9 2.51 1.44 3.9 1.61V17.9c-.87-.15-1.71-.49-2.46-1.03L7.1 18.32zM13 4.07V1L8.45 5.55 13 10V6.09c2.84.48 5 2.94 5 5.91s-2.16 5.43-5 5.91v2.02c3.95-.49 7-3.85 7-7.93s-3.05-7.44-7-7.93z%22/%3E%3C/svg%3E") 12 12, auto',
                            zIndex: 100
                        }}
                        onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Rotate, e)}
                    />

                    {/* 旋轉控制點連接線 */}
                    <div
                        style={{
                            position: 'absolute',
                            left: '50%',
                            top: -15,
                            width: 1,
                            height: 15,
                            marginLeft: -0.5,
                            backgroundColor: '#3b82f6',
                            zIndex: 99
                        }}
                    />
                </>
            )}
        </div>
    );
};