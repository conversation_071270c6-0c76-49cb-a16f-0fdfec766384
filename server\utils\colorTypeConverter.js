// server/utils/colorTypeConverter.js
// 後端顏色類型轉換工具 - 與前端 src/types.ts 保持一致

// 顯示顏色類型枚舉 - 與前端 DisplayColorType 保持一致
const DisplayColorType = {
  BW: "Gray16",
  BWR: "Black & White & Red",
  BWRY: "Black & White & Red & Yellow"
};

/**
 * 將簡短的顏色類型代碼轉換為完整的顏色描述
 * 與前端 src/types.ts 中的 shortCodeToDisplayColorType 函數保持一致
 * @param {string} code - 短代碼 (如 'BW', 'BWR', 'BWRY')
 * @returns {string} - 完整的顏色描述
 */
const shortCodeToDisplayColorType = (code) => {
  if (!code) return "UNKNOWN";

  switch (code.toUpperCase()) {
    case 'BW':
      return DisplayColorType.BW;
    case 'BWR':
      return DisplayColorType.BWR;
    case 'BWRY':
    case 'BWY': // 支援 BWY 作為 BWRY 的別名
      return DisplayColorType.BWRY;
    default:
      console.warn(`未知的顏色類型代碼: ${code}，將顯示為 UNKNOWN`);
      return "UNKNOWN";
  }
};

/**
 * 將字符串轉換為對應的 DisplayColorType
 * 與前端 src/types.ts 中的 stringToDisplayColorType 函數保持一致
 * @param {string} color - 顏色描述
 * @returns {string|undefined} - DisplayColorType 值
 */
const stringToDisplayColorType = (color) => {
  switch (color) {
    case "Gray16": return DisplayColorType.BW;
    case "Black & White & Red": return DisplayColorType.BWR;
    case "Black & White & Red & Yellow": return DisplayColorType.BWRY;
    default: return undefined;
  }
};

/**
 * 獲取所有顯示顏色類型的值
 * 與前端 src/types.ts 中的 getAllDisplayColorTypes 函數保持一致
 * @returns {string[]} - 所有顏色類型的值
 */
const getAllDisplayColorTypes = () => {
  return Object.values(DisplayColorType);
};

module.exports = {
  DisplayColorType,
  shortCodeToDisplayColorType,
  stringToDisplayColorType,
  getAllDisplayColorTypes
};
