@echo off
setlocal enabledelayedexpansion

echo ===== Store 和 StoreData 合併測試工具 =====
echo.
echo 請選擇要運行的測試:
echo 1. 數據庫單元測試 (storeApiTest.js)
echo 2. 簡化版 API 測試 (simpleTest.ps1) - 推薦
echo 3. 詳細版 API 測試 (apiTestPowerShell.ps1)
echo 4. 運行所有測試
echo 5. 退出
echo.

set /p choice=請輸入選項 (1-5):

if "%choice%"=="1" (
    call :RunUnitTest
) else if "%choice%"=="2" (
    call :RunSimpleApiTest
) else if "%choice%"=="3" (
    call :RunDetailedApiTest
) else if "%choice%"=="4" (
    call :RunAllTests
) else if "%choice%"=="5" (
    exit /b 0
) else (
    echo 無效的選項，請重新運行腳本。
    pause
    exit /b 1
)

exit /b 0

:RunUnitTest
echo.
echo ===== 運行數據庫單元測試 =====
echo.
echo 這個測試直接操作數據庫，測試數據模型和操作邏輯。
echo 不需要啟動服務器即可運行。
echo.
node server/tests/storeApiTest.js
echo.
pause
exit /b 0

:RunSimpleApiTest
echo.
echo ===== 運行簡化版 API 測試 =====
echo.
echo 這個測試通過 HTTP 請求測試 API 端點，使用簡化的英文輸出。
echo 注意: 請確保服務器已經啟動 (npm run server)
echo.
powershell -ExecutionPolicy Bypass -File server/tests/simpleTest.ps1
echo.
pause
exit /b 0

:RunDetailedApiTest
echo.
echo ===== 運行詳細版 API 測試 =====
echo.
echo 這個測試通過 HTTP 請求測試 API 端點，提供更詳細的輸出。
echo 注意: 請確保服務器已經啟動 (npm run server)
echo.
powershell -ExecutionPolicy Bypass -File server/tests/apiTestPowerShell.ps1
echo.
pause
exit /b 0

:RunAllTests
echo.
echo ===== 運行所有測試 =====
echo.
echo 1. 運行數據庫單元測試
echo.
node server/tests/storeApiTest.js
echo.
echo.
echo 2. 運行簡化版 API 測試
echo 注意: 請確保服務器已經啟動 (npm run server)
echo.
powershell -ExecutionPolicy Bypass -File server/tests/simpleTest.ps1
echo.
echo.
echo 3. 運行詳細版 API 測試
echo.
powershell -ExecutionPolicy Bypass -File server/tests/apiTestPowerShell.ps1
echo.
echo.
echo ===== 所有測試完成 =====
pause
exit /b 0
