import {
  RefreshPlan,
  CreatePlanRequest,
  UpdatePlanRequest,
  PlanListResponse,
  PlanResponse,
  ExecutePlanResponse,
  ExecutionListResponse,
  StatisticsResponse,
  ExecutionRecord
} from '../types/refreshPlan';
import { buildEndpointUrl } from '../utils/api/apiConfig';
import { useAuthStore } from '../store/authStore';

// 通用請求函數
const apiRequest = async <T>(url: string, options: RequestInit = {}): Promise<T> => {
  // 獲取認證 token
  const { token } = useAuthStore.getState();

  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
      ...options.headers,
    },
    credentials: 'include', // 包含 cookie
  });

  if (!response.ok) {
    if (response.status === 401) {
      throw new Error('未登入或登入已過期');
    }
    const errorData = await response.json().catch(() => ({ error: 'Network error' }));
    throw new Error(errorData.error || `HTTP ${response.status}`);
  }

  // 對於 204 No Content 響應，不嘗試解析 JSON
  if (response.status === 204) {
    return undefined as T;
  }

  // 檢查響應是否有內容
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  }

  // 如果沒有 JSON 內容，返回空對象
  return {} as T;
};

// 刷圖計畫 API 服務
export const refreshPlanApi = {
  // 獲取門店的刷圖計畫列表
  async getPlans(
    storeId: string,
    params: {
      page?: number;
      limit?: number;
      status?: string;
      search?: string;
    } = {}
  ): Promise<PlanListResponse> {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.status) searchParams.append('status', params.status);
    if (params.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = buildEndpointUrl(`stores/${storeId}/refresh-plans${queryString ? `?${queryString}` : ''}`);

    return apiRequest<PlanListResponse>(url);
  },

  // 獲取單個刷圖計畫
  async getPlan(storeId: string, planId: string): Promise<PlanResponse> {
    return apiRequest<PlanResponse>(buildEndpointUrl(`stores/${storeId}/refresh-plans/${planId}`));
  },

  // 檢查計畫名稱是否重複
  async checkPlanName(storeId: string, name: string, excludePlanId?: string): Promise<{ success: boolean; isDuplicate: boolean }> {
    return apiRequest<{ success: boolean; isDuplicate: boolean }>(buildEndpointUrl(`stores/${storeId}/refresh-plans/check-name`), {
      method: 'POST',
      body: JSON.stringify({ name, excludePlanId }),
    });
  },

  // 創建刷圖計畫
  async createPlan(storeId: string, planData: CreatePlanRequest): Promise<PlanResponse> {
    return apiRequest<PlanResponse>(buildEndpointUrl(`stores/${storeId}/refresh-plans`), {
      method: 'POST',
      body: JSON.stringify(planData),
    });
  },

  // 更新刷圖計畫
  async updatePlan(
    storeId: string,
    planId: string,
    planData: UpdatePlanRequest
  ): Promise<PlanResponse> {
    return apiRequest<PlanResponse>(buildEndpointUrl(`stores/${storeId}/refresh-plans/${planId}`), {
      method: 'PUT',
      body: JSON.stringify(planData),
    });
  },

  // 刪除刷圖計畫
  async deletePlan(storeId: string, planId: string): Promise<void> {
    await apiRequest(buildEndpointUrl(`stores/${storeId}/refresh-plans/${planId}`), {
      method: 'DELETE',
    });
  },

  // 手動執行刷圖計畫
  async executePlan(storeId: string, planId: string): Promise<ExecutePlanResponse> {
    return apiRequest<ExecutePlanResponse>(buildEndpointUrl(`stores/${storeId}/refresh-plans/${planId}/execute`), {
      method: 'POST',
    });
  },

  // 獲取計畫執行記錄
  async getExecutions(
    storeId: string,
    planId: string,
    params: {
      page?: number;
      limit?: number;
      status?: string;
      startDate?: string;
      endDate?: string;
    } = {}
  ): Promise<ExecutionListResponse> {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.status) searchParams.append('status', params.status);
    if (params.startDate) searchParams.append('startDate', params.startDate);
    if (params.endDate) searchParams.append('endDate', params.endDate);

    const queryString = searchParams.toString();
    const url = buildEndpointUrl(`stores/${storeId}/refresh-plans/${planId}/executions${queryString ? `?${queryString}` : ''}`);

    return apiRequest<ExecutionListResponse>(url);
  },

  // 獲取執行記錄詳情
  async getExecutionDetail(storeId: string, executionId: string): Promise<{ success: boolean; data: ExecutionRecord }> {
    return apiRequest<{ success: boolean; data: ExecutionRecord }>(buildEndpointUrl(`stores/${storeId}/refresh-plans/executions/${executionId}`));
  },

  // 獲取計畫統計信息
  async getStatistics(
    storeId: string,
    params: {
      period?: 'day' | 'week' | 'month';
      planIds?: string[];
    } = {}
  ): Promise<StatisticsResponse> {
    const searchParams = new URLSearchParams();

    if (params.period) searchParams.append('period', params.period);
    if (params.planIds && params.planIds.length > 0) {
      params.planIds.forEach(id => searchParams.append('planIds', id));
    }

    const queryString = searchParams.toString();
    const url = buildEndpointUrl(`stores/${storeId}/refresh-plans/statistics${queryString ? `?${queryString}` : ''}`);

    return apiRequest<StatisticsResponse>(url);
  },

  // 切換計畫啟用狀態
  async togglePlan(storeId: string, planId: string, enabled: boolean): Promise<PlanResponse> {
    return this.updatePlan(storeId, planId, { enabled });
  },

  // 獲取調度器狀態和運行中的計畫
  async getSchedulerStatus(storeId: string): Promise<{
    success: boolean;
    data: {
      schedulerStatus: {
        isHealthy: boolean;
        schedulerStartTime: string;
        totalActiveTasks: number;
      };
      activeTasks: Array<{
        planId: string;
        planName: string;
        storeId: string;
        registeredAt: string;
        nextRun: string | null;
      }>;
      runningPlans: Array<{
        _id: string;
        name: string;
        status: string;
        lastRun?: string;
        statistics: any;
      }>;
    };
  }> {
    return apiRequest(buildEndpointUrl(`stores/${storeId}/refresh-plans/scheduler-status`));
  },

  // 重新載入調度器（管理員功能）
  async reloadScheduler(): Promise<{ success: boolean; message: string }> {
    return apiRequest(buildEndpointUrl('refresh-plans/scheduler/reload'), {
      method: 'POST',
    });
  },
};

// 設備和門店數據 API（用於計畫配置）
export const planConfigApi = {
  // 獲取門店設備列表（用於 MAC 地址選擇）
  async getStoreDevices(storeId: string): Promise<{
    success: boolean;
    data: {
      id: string;
      macAddress: string;
      name?: string;
      size?: string;
      colorType?: string;
      status?: string;
      code?: string;
      note?: string;
    }[];
  }> {
    try {
      // 獲取認證 token
      const { token } = useAuthStore.getState();

      const response = await fetch(buildEndpointUrl(`devices?storeId=${storeId}`), {
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        credentials: 'include', // 包含 cookie
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('未登入或登入已過期');
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const devices = await response.json();

      // 調試：打印設備數據結構
      console.log('設備API返回的原始數據:', devices.slice(0, 1)); // 只打印第一個設備的數據

      // 轉換設備數據格式
      const formattedDevices = devices.map((device: any) => {
        const formatted = {
          id: device._id,
          macAddress: device.macAddress,
          name: device.name || device.data?.name,
          size: device.data?.size,
          colorType: device.colorType,
          status: device.status,
          // code字段可能在根級別或data.imgcode中
          code: device.code || device.data?.imgcode || device.data?.code || '',
          note: device.note || ''
        };

        // 調試：打印格式化後的數據
        if (device._id === devices[0]?._id) {
          console.log('格式化後的設備數據:', formatted);
        }

        return formatted;
      });

      return {
        success: true,
        data: formattedDevices
      };
    } catch (error) {
      console.error('獲取設備列表失敗:', error);
      return {
        success: false,
        data: []
      };
    }
  },

  // 獲取門店數據列表（用於門店數據選擇）
  async getStoreData(storeId: string): Promise<{
    success: boolean;
    data: {
      id: string;
      name: string;
      description?: string;
      boundDeviceCount: number;
    }[];
  }> {
    try {
      // 獲取認證 token
      const { token } = useAuthStore.getState();

      const response = await fetch(buildEndpointUrl(`storeData?storeId=${storeId}`), {
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        credentials: 'include', // 包含 cookie
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('未登入或登入已過期');
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const storeDataList = await response.json();

      // 轉換門店數據格式
      const formattedStoreData = storeDataList.map((storeData: any) => ({
        id: storeData.id || storeData.uid || storeData._id, // 優先使用id欄位，這是門店數據表格顯示的ID
        name: storeData.name,
        description: storeData.description,
        boundDeviceCount: storeData.boundDeviceCount || 0
      }));

      return {
        success: true,
        data: formattedStoreData
      };
    } catch (error) {
      console.error('獲取門店數據失敗:', error);
      return {
        success: false,
        data: []
      };
    }
  },

  // 獲取系統配置（用於顯示執行策略參數）
  async getSystemConfig(): Promise<{
    success: boolean;
    data: {
      gatewayConcurrency: number;
      queueRetryAttempts: number;
      queueRetryInterval: number;
      gatewayTimeout: number;
    };
  }> {
    return apiRequest(buildEndpointUrl('configs/system'));
  },
};

// 錯誤處理工具
export const handleApiError = (error: any): string => {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (error?.error) {
    return error.error;
  }
  
  return '未知錯誤';
};

// 格式化日期時間
export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

// 格式化執行時間
export const formatExecuteTime = (trigger: any): string => {
  const { type, executeTime, executeDate, weekDays } = trigger;
  
  switch (type) {
    case 'once':
      return `${executeDate} ${executeTime}`;
    case 'daily':
      return `每天 ${executeTime}`;
    case 'weekly':
      const dayNames = ['週日', '週一', '週二', '週三', '週四', '週五', '週六'];
      const selectedDays = weekDays?.map((day: number) => dayNames[day]).join('、') || '';
      return `每週${selectedDays} ${executeTime}`;
    default:
      return executeTime;
  }
};

// 計算成功率
export const calculateSuccessRate = (successRuns: number, totalRuns: number): string => {
  if (totalRuns === 0) return '0%';
  return `${Math.round((successRuns / totalRuns) * 100)}%`;
};

export default refreshPlanApi;
