# EPD Manager Lite

電子紙顯示器管理系統

## 快速開始

```bash
git submodule update --init --recursive
```

### 1. 環境設置

複製環境變數範例文件並配置：

```bash
cp .env.example .env
```

編輯 `.env` 文件，設置必要的配置：
- `JWT_SECRET`: JWT 密鑰（請使用強密碼）
- 其他端口配置根據需要調整

### 2. 使用 Docker Compose 啟動

```bash
docker-compose up -d
```

### 3. 訪問應用

- 前端：http://localhost:5173
- 後端 API：http://localhost:3001
- MongoDB：localhost:27017

## 開發

[Edit in StackBlitz next generation editor ⚡️](https://stackblitz.com/~/github.com/mikefd097021/epd-manager)