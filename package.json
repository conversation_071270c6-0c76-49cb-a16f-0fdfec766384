{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run client\" \"npm run server\" ", "client": "vite --host 0.0.0.0", "server": "cd server && npm start", "preview-service": "cd preview-service && npm run dev", "build": "vite build", "build:preview-service": "cd shared-preview-renderer && npm run build && cd ../preview-service && npm run build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@types/js-cookie": "^3.0.6", "@types/qrcode": "^1.5.5", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "default-gateway": "^7.2.2", "html2canvas": "^1.4.1", "i18next": "^25.0.0", "i18next-browser-languagedetector": "^8.0.4", "js-cookie": "^3.0.5", "jsbarcode": "^3.11.6", "jsonwebtoken": "^9.0.2", "lucide": "^0.488.0", "lucide-react": "^0.344.0", "mongodb": "^6.15.0", "node-fetch": "^3.3.2", "qrcode": "^1.5.4", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-i18next": "^15.4.1", "react-router-dom": "^7.5.2", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "ws": "^8.18.2", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.14.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.1.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}