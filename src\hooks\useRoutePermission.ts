import { useCallback } from 'react';
import { usePermission } from './usePermission';
import { useNavigationStore } from '../store/navigationStore';

// 定義路由權限映射
// 格式: { 路由標識符: { permission?: string, anyPermissions?: string[], allPermissions?: string[] } }
const routePermissions: Record<string, {
  permission?: string;
  anyPermissions?: string[];
  allPermissions?: string[];
}> = {
  // 第一層選單
  'store-management': {
    anyPermissions: [
      'store:view',
      'store-data:view', 'store-data:create', 'store-data:update', 'store-data:delete',
      'store-template:view', 'store-template:create', 'store-template:update', 'store-template:delete',
      'gateway:view', 'gateway:create', 'gateway:update', 'gateway:delete',
      'device:view', 'device:create', 'device:update', 'device:delete',
      'store-settings:view', 'store-settings:update',
      'analytics:view', 'analytics:export'
    ]
  },
  'system-data': { anyPermissions: ['system:view', 'system-data:view'] },
  'system-templates': { permission: 'template:view' },
  'permission-management': { anyPermissions: ['role:view', 'user:view', 'permission:view'] },
  'system-logs': { permission: 'system:view' },
  'settings': { permission: 'system:view' },
  'change-password': {}, // 所有登入用戶都可以訪問

  // 第二層選單 (門店相關)
  'store-overview': { anyPermissions: ['store:view', 'store-data:view'] },
  'database': { anyPermissions: ['store:view', 'store-data:view'] },
  'templates': { anyPermissions: ['template:view', 'store-template:view'] },
  'deploy': { anyPermissions: ['gateway:view'] },
  'devices': { anyPermissions: ['device:view'] },
  'users': { anyPermissions: ['store:update', 'store-settings:view', 'store-settings:update'] },
  'analytics': { anyPermissions: ['system:view', 'analytics:view'] },
};

/**
 * 路由權限檢查Hook
 * 用於檢查當前路由是否有權限訪問
 */
export const useRoutePermission = () => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading, permissions } = usePermission();
  const { activeItem: currentActiveItem } = useNavigationStore();

  // 注意：我們現在使用 navigationStore 中的 activeItem 作為當前活動項目

  /**
   * 檢查當前路由是否有權限訪問
   * @returns 是否有權限訪問當前路由
   */
  const hasRoutePermission = useCallback((): boolean => {
    // 如果正在加載權限，暫時返回 true
    if (loading) return true;

    // 如果有 'all' 權限，則可以訪問所有路由
    if (permissions.includes('all')) return true;

    // 獲取當前路由的權限配置
    const routeConfig = routePermissions[currentActiveItem];

    // 如果沒有配置，默認允許訪問
    if (!routeConfig) return true;

    // 檢查權限
    let hasAccess = true;

    if (routeConfig.permission) {
      hasAccess = hasAccess && hasPermission(routeConfig.permission);
    }

    if (routeConfig.anyPermissions && routeConfig.anyPermissions.length > 0) {
      hasAccess = hasAccess && hasAnyPermission(routeConfig.anyPermissions);
    }

    if (routeConfig.allPermissions && routeConfig.allPermissions.length > 0) {
      hasAccess = hasAccess && hasAllPermissions(routeConfig.allPermissions);
    }

    return hasAccess;
  }, [currentActiveItem, hasPermission, hasAnyPermission, hasAllPermissions, loading, permissions]);

  /**
   * 獲取用戶有權限訪問的第一個路由
   * @returns 有權限訪問的路由標識符
   */
  const getFirstAccessibleRoute = useCallback((): string => {
    // 如果有 'all' 權限，則返回門店管理頁面
    if (permissions.includes('all')) return 'store-management';

    // 優先檢查用戶是否有門店相關權限，如果有，優先返回門店管理頁面
    const storeRelatedPermissions = [
      'store:view',
      'store-data:view', 'store-data:create', 'store-data:update', 'store-data:delete',
      'store-template:view', 'store-template:create', 'store-template:update', 'store-template:delete',
      'gateway:view', 'gateway:create', 'gateway:update', 'gateway:delete',
      'device:view', 'device:create', 'device:update', 'device:delete',
      'store-settings:view', 'store-settings:update',
      'analytics:view', 'analytics:export'
    ];

    if (hasAnyPermission(storeRelatedPermissions)) {
      return 'store-management';
    }

    // 檢查每個路由的權限
    for (const [routeId, config] of Object.entries(routePermissions)) {
      let hasAccess = true;

      if (config.permission) {
        hasAccess = hasAccess && hasPermission(config.permission);
      }

      if (config.anyPermissions && config.anyPermissions.length > 0) {
        hasAccess = hasAccess && hasAnyPermission(config.anyPermissions);
      }

      if (config.allPermissions && config.allPermissions.length > 0) {
        hasAccess = hasAccess && hasAllPermissions(config.allPermissions);
      }

      if (hasAccess) {
        return routeId;
      }
    }

    // 如果沒有找到有權限的路由，返回修改密碼頁面（所有用戶都可以訪問）
    return 'change-password';
  }, [hasPermission, hasAnyPermission, hasAllPermissions, permissions]);

  return {
    hasRoutePermission,
    getFirstAccessibleRoute,
    currentActiveItem
  };
};

export default useRoutePermission;
