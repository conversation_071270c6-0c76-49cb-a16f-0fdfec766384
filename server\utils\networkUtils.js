/**
 * 網絡工具函數
 * 用於獲取服務器的網絡信息
 */
const os = require('os');

/**
 * 獲取服務器的IP地址
 * 優先返回實體網卡上有預設閘道的IPv4地址
 * 排除虛擬網卡和虛擬機介面
 * @returns {string} 服務器IP地址
 */
function getServerIpAddress() {
  const networkInterfaces = os.networkInterfaces();
  let bestIpAddress = null;
  let externalIpv4 = null;
  let internalIpv4 = null;
  
  // 可能的虛擬網卡名稱前綴
  const virtualInterfacePrefixes = [
    'vEthernet', 'vmnet', 'vboxnet', 'docker', 'br-', 'veth', 'hyper-v'
  ];

  // 可能的虛擬IP區間
  const virtualIpRanges = [
    { start: '**********', end: '**************' }, // Docker 常用範圍
    { start: '***********', end: '***************' } // 一些虛擬機使用的範圍
  ];

  // 判斷是否為虛擬網卡
  function isVirtualInterface(name, address) {
    // 檢查網卡名稱
    const isVirtualName = virtualInterfacePrefixes.some(prefix => 
      name.toLowerCase().includes(prefix.toLowerCase())
    );
    
    if (isVirtualName) return true;
    
    // 檢查是否是已知的虛擬IP區間（簡化版，實際應該比較IP數值）
    if (address.startsWith('172.') && address.split('.')[1] >= 16 && address.split('.')[1] <= 31) {
      // 排除 Docker 和 Hyper-V 常用的 172.16-31.x.x 網段
      return true;
    }
    
    return false;
  }

  // 優先順序：物理網卡 > 外部IP > 非虛擬內部IP
  for (const interfaceName in networkInterfaces) {
    const interfaces = networkInterfaces[interfaceName];
    
    for (const iface of interfaces) {
      // 只處理IPv4地址
      if (iface.family !== 'IPv4' && iface.family !== 4) continue;
      
      // 忽略回環地址
      if (iface.internal) continue;
      
      // 忽略虛擬網卡的地址
      if (isVirtualInterface(interfaceName, iface.address)) continue;
      
      // 檢查是否有預設閘道
      if (iface.cidr && !iface.cidr.endsWith('/32')) {
        if (!isPrivateIp(iface.address)) {
          // 有閘道的外部地址是最佳選擇
          bestIpAddress = iface.address;
          break;
        } else {
          // 有閘道的內部地址是次佳選擇
          internalIpv4 = internalIpv4 || iface.address;
        }
      } else if (!isPrivateIp(iface.address)) {
        // 無閘道但是外部地址
        externalIpv4 = externalIpv4 || iface.address;
      } else {
        // 無閘道的內部地址
        internalIpv4 = internalIpv4 || iface.address;
      }
    }
    
    // 如果找到了最佳地址，跳出循環
    if (bestIpAddress) break;
  }

  // 如果以上方法都無法找到好的 IP，嘗試找到可能的物理網卡
  if (!bestIpAddress && !externalIpv4 && !internalIpv4) {
    for (const interfaceName in networkInterfaces) {
      // 排除明顯的虛擬網卡
      if (virtualInterfacePrefixes.some(prefix => 
        interfaceName.toLowerCase().includes(prefix.toLowerCase()))) {
        continue;
      }
      
      const interfaces = networkInterfaces[interfaceName];
      
      for (const iface of interfaces) {
        if (iface.family === 'IPv4' && !iface.internal) {
          internalIpv4 = iface.address;
          break;
        }
      }
      
      if (internalIpv4) break;
    }
  }

  // 按優先順序返回
  return bestIpAddress || externalIpv4 || internalIpv4 || 'localhost';
}

/**
 * 判斷IP地址是否為私有地址
 * @param {string} ip IP地址
 * @returns {boolean} 是否為私有地址
 */
function isPrivateIp(ip) {
  // 檢查是否為私有IP地址範圍
  // 10.0.0.0 - **************
  // ********** - **************
  // *********** - ***************
  const parts = ip.split('.').map(part => parseInt(part, 10));

  return (
    parts[0] === 10 ||
    (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) ||
    (parts[0] === 192 && parts[1] === 168)
  );
}

/**
 * 構建WebSocket URL
 * @param {string} path WebSocket路徑
 * @param {number} port 端口號
 * @returns {string} 完整的WebSocket URL
 */
function buildWebSocketUrl(path, port = 3001) {
  const serverIp = getServerIpAddress();
  return `ws://${serverIp}:${port}${path}`;
}

module.exports = {
  getServerIpAddress,
  buildWebSocketUrl
};
