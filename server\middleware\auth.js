const { verifyToken, getTokenFromRequest } = require('../utils/auth');
const User = require('../models/User');

/**
 * 認證中間件
 * 驗證用戶是否已登入
 */
const authenticate = async (req, res, next) => {
  try {
    // 獲取 token
    const token = getTokenFromRequest(req);

    if (!token) {
      return res.status(401).json({ error: '未授權，請登入' });
    }

    // 驗證 token
    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({ error: '無效的 token，請重新登入' });
    }

    // 獲取用戶信息
    const { db } = req;
    const user = await User.findById(db, decoded.userId);

    if (!user) {
      return res.status(401).json({ error: '用戶不存在，請重新登入' });
    }

    // 檢查用戶狀態
    if (user.status !== 'active') {
      return res.status(403).json({ error: '用戶已被禁用，請聯繫管理員' });
    }

    // 將用戶信息添加到請求對象
    req.user = user;

    next();
  } catch (error) {
    console.error('認證錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
};

/**
 * 權限檢查中間件
 * 檢查用戶是否有特定權限
 * @param {string|string[]} permission - 需要的權限或權限列表
 * @param {Object} options - 選項
 * @param {boolean} options.requireAll - 是否需要所有權限（默認為 false，即任意一個權限即可）
 */
const checkPermission = (permission, options = { requireAll: false }) => {
  return async (req, res, next) => {
    try {
      const { user, db } = req;

      if (!user) {
        return res.status(401).json({ error: '未授權，請登入' });
      }

      // 將單個權限轉換為數組
      const requiredPermissions = Array.isArray(permission) ? permission : [permission];

      if (requiredPermissions.length === 0) {
        // 如果沒有指定權限，則直接通過
        return next();
      }

      console.log(`檢查權限: ${requiredPermissions.join(', ')}`);
      console.log(`用戶 ID: ${user._id}, 用戶名: ${user.username}`);

      // 獲取用戶的所有權限分配
      const Permission = require('../models/Permission');
      const userPermissions = await Permission.findByUserId(db, user._id.toString());
      console.log(`用戶權限分配: ${userPermissions.length}`);

      if (userPermissions.length === 0) {
        console.log('用戶沒有任何權限分配');
        return res.status(403).json({ error: '沒有權限執行此操作' });
      }

      // 獲取用戶的所有角色
      const roleIds = userPermissions.map(p => p.roleId);
      console.log(`用戶角色 IDs: ${roleIds.join(', ')}`);

      const Role = require('../models/Role');

      // 獲取所有角色
      const roles = await Promise.all(
        roleIds.map(roleId => Role.findById(db, roleId.toString()))
      );

      // 過濾掉不存在的角色
      const validRoles = roles.filter(role => role !== null);

      if (validRoles.length === 0) {
        console.log('用戶沒有有效的角色');
        return res.status(403).json({ error: '沒有權限執行此操作' });
      }

      // 檢查是否有任何角色具有 'all' 權限
      const hasAllPermission = validRoles.some(role => role.permissions.includes('all'));

      if (hasAllPermission) {
        console.log('用戶具有 all 權限，權限檢查通過');
        return next();
      }

      // 獲取用戶的所有權限
      const userAllPermissions = validRoles.reduce((allPermissions, role) => {
        return [...allPermissions, ...role.permissions];
      }, []);

      // 去重
      const uniquePermissions = [...new Set(userAllPermissions)];

      // 檢查是否有所需權限
      let hasRequiredPermissions;

      if (options.requireAll) {
        // 需要所有權限
        hasRequiredPermissions = requiredPermissions.every(p => uniquePermissions.includes(p));
      } else {
        // 需要任意一個權限
        hasRequiredPermissions = requiredPermissions.some(p => uniquePermissions.includes(p));
      }

      if (!hasRequiredPermissions) {
        console.log(`沒有權限執行此操作: ${requiredPermissions.join(', ')}`);
        return res.status(403).json({
          error: `沒有權限執行此操作 (${requiredPermissions.join(', ')})`
        });
      }

      console.log(`權限檢查通過: ${requiredPermissions.join(', ')}`);
      next();
    } catch (error) {
      console.error('權限檢查錯誤:', error);
      res.status(500).json({ error: '伺服器錯誤: ' + error.message });
    }
  };
};

module.exports = {
  authenticate,
  checkPermission
};
