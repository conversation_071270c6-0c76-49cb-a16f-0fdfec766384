#!/usr/bin/env node

/**
 * 測試JWT工具模組
 */

const { 
  generateWebSocketToken, 
  generateGatewayToken,
  verifyToken,
  decodeToken,
  getJwtSecret
} = require('../utils/jwtUtils');

console.log('🔧 測試JWT工具模組');
console.log(`🔑 JWT密鑰: ${getJwtSecret().substring(0, 10)}...`);
console.log('');

// 測試用戶信息
const testUser = {
  userId: '507f1f77bcf86cd799439011',
  username: 'testuser'
};

// 測試Gateway信息
const testGateway = {
  gatewayId: 'test-gateway-001',
  storeId: 'test-store-001',
  macAddress: 'AA:BB:CC:DD:EE:FF'
};

try {
  // 1. 測試WebSocket token生成
  console.log('1️⃣ 測試WebSocket token生成...');
  const wsToken = generateWebSocketToken(testUser);
  console.log(`✅ WebSocket token生成成功: ${wsToken.substring(0, 30)}...`);
  
  // 2. 測試Gateway token生成
  console.log('');
  console.log('2️⃣ 測試Gateway token生成...');
  const gatewayToken = generateGatewayToken(testGateway);
  console.log(`✅ Gateway token生成成功: ${gatewayToken.substring(0, 30)}...`);
  
  // 3. 測試token驗證
  console.log('');
  console.log('3️⃣ 測試token驗證...');
  
  const wsDecoded = verifyToken(wsToken);
  console.log('✅ WebSocket token驗證成功');
  console.log(`📋 WebSocket token內容:`, {
    userId: wsDecoded.userId,
    username: wsDecoded.username,
    type: wsDecoded.type,
    iat: new Date(wsDecoded.iat * 1000).toLocaleString(),
    exp: new Date(wsDecoded.exp * 1000).toLocaleString()
  });
  
  const gatewayDecoded = verifyToken(gatewayToken);
  console.log('✅ Gateway token驗證成功');
  console.log(`📋 Gateway token內容:`, {
    gatewayId: gatewayDecoded.gatewayId,
    storeId: gatewayDecoded.storeId,
    macAddress: gatewayDecoded.macAddress,
    type: gatewayDecoded.type,
    iat: new Date(gatewayDecoded.iat * 1000).toLocaleString(),
    exp: new Date(gatewayDecoded.exp * 1000).toLocaleString()
  });
  
  // 4. 測試token解碼（不驗證簽名）
  console.log('');
  console.log('4️⃣ 測試token解碼...');
  
  const wsDecodedUnsafe = decodeToken(wsToken);
  console.log('✅ WebSocket token解碼成功');
  console.log(`📋 解碼結果類型: ${wsDecodedUnsafe.type}`);
  
  const gatewayDecodedUnsafe = decodeToken(gatewayToken);
  console.log('✅ Gateway token解碼成功');
  console.log(`📋 解碼結果類型: ${gatewayDecodedUnsafe.type}`);
  
  console.log('');
  console.log('🎉 所有JWT工具測試完成！');
  
  // 5. 輸出可用於測試的token
  console.log('');
  console.log('📋 可用於測試的tokens:');
  console.log('');
  console.log('WebSocket Token:');
  console.log(wsToken);
  console.log('');
  console.log('Gateway Token:');
  console.log(gatewayToken);
  console.log('');
  console.log('Gateway WebSocket URL:');
  console.log(`ws://localhost:3001/ws/store/${testGateway.storeId}/gateway/${testGateway.gatewayId}?token=${gatewayToken}`);
  
} catch (error) {
  console.error('❌ 測試失敗:', error.message);
  console.error(error.stack);
}
