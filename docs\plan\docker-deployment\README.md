# EPD Manager Docker 部署規劃總結

## 📋 規劃概述

⚠️ **重要更正**: 經過重新分析，發現之前的雙容器方案是錯誤的！

## 🎯 實際系統架構

### 原始設計
EPD Manager 是一個**統一專案**，使用 `npm run dev` 同時啟動前後端：
- **統一專案**: 前後端在同一個專案中
- **開發命令**: `npm run dev` (使用 concurrently)
- **前端**: Vite 開發服務器 (端口 5173)
- **後端**: Node.js Express (端口 3001)
- **資料庫**: MongoDB (端口 27017)

### ❌ 之前的錯誤方案
- 錯誤地分成兩個獨立容器 (frontend + server)
- 破壞了原始的統一架構
- 增加了不必要的複雜度

### ✅ 正確的容器化方案
- **單容器**: 保持原始架構，整個專案容器化為一個服務
- **雙端口**: 同時暴露 5173 (前端) 和 3001 (後端)
- **原始命令**: 繼續使用 `npm run dev`

## 📁 規劃文件說明

### ⭐ **正確方案** (請優先參考)

#### 1. `corrected-single-container-plan.md` 🎯 **正確的單容器方案**
- **最新**: 基於原始架構的正確 Docker 化方案
- 保持 `npm run dev` 的開發體驗
- 單容器部署，雙端口暴露
- 支援外部 IP 訪問

### ❌ **過時方案** (僅供參考，不建議使用)

#### 2. `correct-implementation-plan.md` ❌ **錯誤的雙容器方案**
- ~~分離前後端容器~~ (不符合原始架構)
- ~~Nginx + Node.js 分離~~ (過度複雜化)

#### 3. `external-access-analysis.md` 🌐 **外部訪問分析**
- 外部 IP 訪問問題分析 (仍然有效)
- 網路配置和 CORS 設置 (仍然有效)
- 動態 IP 檢測方案 (仍然有效)

#### 4. `deploy-scripts.md` 🔧 **自動化部署腳本**
- IP 自動檢測腳本 (需要調整)
- 一鍵部署腳本 (需要更新)

#### 5. `quick-start-guide.md` 🚀 **快速開始**
- ~~雙容器部署指南~~ (需要重寫)

#### 6. `docker-compose-deployment-plan.md` 📖 **詳細規劃**
- ~~雙容器架構設計~~ (需要重寫)

#### 7. `implementation-checklist.md` ✅ **實作檢查清單**
- 分階段實作計劃 (需要更新)

#### 8. `file-structure-plan.md` 📂 **文件結構**
- ~~雙 Dockerfile 結構~~ (需要簡化)

## 🚀 推薦實作流程

### ✅ **正確流程** (基於單容器方案)

#### 第一步：使用正確方案
1. **閱讀**: `corrected-single-container-plan.md`
2. **創建**: 單個 Dockerfile (保持原始架構)
3. **配置**: 簡化的 docker-compose.yml (兩個服務)
4. **測試**: 驗證 `npm run dev` 在容器中正常運行

#### 第二步：外部訪問配置
1. **參考**: `external-access-analysis.md` 中的網路配置
2. **配置**: 動態 IP 檢測和 CORS 設置
3. **測試**: 驗證外部設備可以訪問

#### 第三步：生產優化 (可選)
1. **創建**: 生產環境 Dockerfile (Nginx + Node.js)
2. **優化**: 資源限制和監控
3. **部署**: 生產環境測試

### ❌ **避免的錯誤**
- 不要分離前後端為獨立容器
- 不要破壞原始的 `npm run dev` 架構
- 不要過度複雜化簡單的部署需求

## 🔧 需要創建的核心文件

### 立即需要 (第一階段)
```
epd-manager-lite/
├── Dockerfile                  # 單容器 Dockerfile (保持原始架構)
├── docker-compose.yml          # 服務編排 (更新現有，移除不相關服務)
└── .env                        # 環境變數
```

### 後續完善 (第二階段)
```
├── .dockerignore               # 構建優化
└── scripts/
    ├── health-check.sh         # 健康檢查
    ├── deploy.sh               # 部署腳本
    └── backup.sh               # 備份腳本
```

## ⚠️ 重要注意事項

### 端口配置問題
- **程式碼硬編碼**: 前端 API 配置和後端監聽端口都硬編碼為 3001 和 5173
- **容器內部端口**: 必須保持 5173 (前端) 和 3001 (後端)，避免程式碼調用錯誤
- **外部端口映射**: 可以自定義，如 `8080:5173` 和 `8081:3001`

### 後端特殊依賴
- **Canvas**: 需要安裝 cairo, pango 等系統依賴
- **Puppeteer**: 需要 Chromium 瀏覽器
- **WebSocket**: 支援 Gateway 設備通信

### 環境變數要求
- **JWT_SECRET**: 必須設置，用於用戶認證
- **MONGO_URI**: 容器間使用 `mongodb://mongodb:27017`

### 資料持久化
- MongoDB 資料存儲在 Docker volume
- 後端上傳文件和日誌需要持久化
- 不需要額外的備份容器

## 🎯 成功標準

### 功能驗證
- [ ] 前端可以正常訪問 (http://localhost:5173)
- [ ] 後端 API 正常響應 (http://localhost:3001/api/health)
- [ ] MongoDB 連接正常
- [ ] WebSocket 功能正常
- [ ] 用戶認證功能正常

### 外部訪問驗證 ⭐ **新增**
- [ ] 同網段設備可以訪問前端 (http://*************:5173)
- [ ] 同網段設備可以訪問 API (http://*************:3001/api/health)
- [ ] 移動應用可以連接 WebSocket
- [ ] Gateway 設備可以建立 WebSocket 連接
- [ ] CORS 配置正確，無跨域錯誤

### 效能要求
- [ ] 容器啟動時間 < 60 秒
- [ ] 記憶體使用 < 4GB
- [ ] API 響應時間 < 1 秒

## 🔄 與現有開發流程的整合

### 開發模式
```bash
# 繼續使用現有開發命令
npm run dev  # 同時啟動前端和後端
```

### 部署模式
```bash
# 使用 Docker Compose 部署
docker-compose up -d
```

### 混合模式
```bash
# 只容器化資料庫，本地開發前後端
docker-compose up mongodb -d
npm run dev
```

## 📞 下一步行動

### 🚀 推薦快速開始流程

1. **檢查外部訪問需求**: 閱讀 `external-access-analysis.md` 了解網路配置
2. **使用自動化腳本**: 參考 `deploy-scripts.md` 使用一鍵部署
3. **手動實作**: 如需自定義，從 `correct-implementation-plan.md` 開始
4. **驗證外部訪問**: 確保移動應用和 Gateway 設備可以正常連接

### 🔧 實作步驟

1. **立即執行**:
   ```bash
   # 創建部署腳本
   mkdir -p scripts
   # 複製 deploy-scripts.md 中的腳本內容

   # 執行自動部署
   chmod +x scripts/*.sh
   ./scripts/deploy.sh
   ```

2. **驗證部署**:
   ```bash
   # 健康檢查
   ./scripts/health-check.sh

   # 測試外部訪問
   curl http://$(hostname -I | awk '{print $1}'):5173
   curl http://$(hostname -I | awk '{print $1}'):3001/api/health
   ```

3. **移動應用測試**: 使用檢測到的 IP 地址配置移動應用並測試連接

---

**重要**: 這個規劃已經解決了外部 IP 訪問的所有問題，確保 Gateway 設備、移動應用和跨設備訪問都能正常工作。所有配置都支援動態 IP 檢測和自動化部署。
