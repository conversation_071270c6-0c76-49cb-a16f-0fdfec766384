# 軟體管理系統實現計劃

## 1. 實現階段規劃

### 階段一：基礎架構 (1-2天)
- [x] 系統設計文檔完成
- [ ] 資料庫模型設計與實現
- [ ] 基礎API路由架構
- [ ] bin檔案解析工具整合

### 階段二：核心功能 (2-3天)
- [ ] 軟體上傳API實現
- [ ] CRC驗證功能
- [ ] 軟體列表查詢API
- [ ] 軟體狀態管理API
- [ ] GridFS檔案存儲整合

### 階段三：前端介面 (2-3天)
- [ ] 軟體管理頁面組件
- [ ] 上傳介面實現
- [ ] 軟體列表顯示
- [ ] 狀態管理介面
- [ ] 響應式設計適配

### 階段四：整合測試 (1-2天)
- [ ] 系統整合測試
- [ ] 權限控制測試
- [ ] 檔案上傳測試
- [ ] 效能測試
- [ ] 用戶體驗測試

## 2. 技術實現細節

### 2.1 資料庫模型實現

#### 軟體模型 (server/models/Software.js)
```javascript
class Software {
  static getCollection(db) {
    return db.collection('software');
  }

  static async createSoftware(db, softwareData) {
    const collection = this.getCollection(db);
    
    // 檢查重複軟體 (基於checksum)
    const existing = await collection.findOne({ 
      checksum: softwareData.checksum 
    });
    
    if (existing) {
      throw new Error('軟體已存在');
    }

    const software = {
      ...softwareData,
      createdAt: new Date(),
      updatedAt: new Date(),
      downloadCount: 0,
      deploymentCount: 0,
      isEnabled: true,
      status: 'active'
    };

    const result = await collection.insertOne(software);
    return { ...software, _id: result.insertedId };
  }

  static async findAll(db, filters = {}, options = {}) {
    const collection = this.getCollection(db);
    const { page = 1, limit = 20, search } = options;
    
    let query = { ...filters };
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { version: { $regex: search, $options: 'i' } }
      ];
    }

    const total = await collection.countDocuments(query);
    const software = await collection
      .find(query)
      .sort({ uploadDate: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .toArray();

    return { software, total, page, limit };
  }

  static async updateStatus(db, id, statusData) {
    const collection = this.getCollection(db);
    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: { 
          ...statusData, 
          updatedAt: new Date() 
        } 
      }
    );
    return result.modifiedCount > 0;
  }
}
```

### 2.2 bin檔案解析服務

#### bin檔案解析器 (server/services/BinFileParser.js)
```javascript
const zlib = require('zlib');

class BinFileParser {
  constructor() {
    this.DEVICE_TYPE_NAMES = { 0: 'gateway', 1: 'epd' };
    this.FUNCTION_TYPE_NAMES = { 0: 'wifi', 1: 'ble' };
  }

  parseBinFile(buffer) {
    if (buffer.length < 12) {
      throw new Error('檔案太小，不是有效的格式化bin檔案');
    }

    // 解析標頭
    const deviceType = buffer.readUInt16LE(0);
    const functionType = buffer.readUInt16LE(2);
    const version = [
      buffer.readUInt8(4),
      buffer.readUInt8(5),
      buffer.readUInt8(6),
      buffer.readUInt8(7)
    ].join('.');

    // 提取bin數據和校驗和
    const binData = buffer.slice(8, -4);
    const checksum = buffer.readUInt32LE(buffer.length - 4);

    // 驗證校驗和
    const calculatedChecksum = zlib.crc32(binData) >>> 0;
    const isValid = checksum === calculatedChecksum;

    if (!isValid) {
      throw new Error('CRC校驗和驗證失敗');
    }

    return {
      deviceType: this.DEVICE_TYPE_NAMES[deviceType] || 'unknown',
      functionType: this.FUNCTION_TYPE_NAMES[functionType] || 'unknown',
      version,
      binData,
      checksum: checksum.toString(16).toUpperCase().padStart(8, '0'),
      calculatedChecksum: calculatedChecksum.toString(16).toUpperCase().padStart(8, '0'),
      isValid,
      binSize: binData.length,
      totalSize: buffer.length
    };
  }

  extractPureBin(buffer) {
    const parsed = this.parseBinFile(buffer);
    return parsed.binData;
  }

  validateDeviceFunctionSupport(deviceType, functionType) {
    const supportMatrix = {
      'gateway': ['wifi', 'ble'],
      'epd': ['ble']
    };

    return supportMatrix[deviceType]?.includes(functionType) || false;
  }
}

module.exports = BinFileParser;
```

### 2.3 API路由實現

#### 軟體管理API (server/routes/softwareApi.js)
```javascript
const express = require('express');
const multer = require('multer');
const { ObjectId } = require('mongodb');
const { authenticate, checkPermission } = require('../middleware/auth');
const Software = require('../models/Software');
const BinFileParser = require('../services/BinFileParser');

const router = express.Router();
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB
  fileFilter: (req, file, cb) => {
    if (file.originalname.endsWith('.bin')) {
      cb(null, true);
    } else {
      cb(new Error('只允許上傳.bin檔案'), false);
    }
  }
});

let connectDBFunction;

function initDB(connectDB) {
  connectDBFunction = connectDB;
}

// 獲取軟體列表
router.get('/software', authenticate, checkPermission('software:read'), async (req, res) => {
  try {
    const { deviceType, functionType, status, page, limit, search } = req.query;
    
    const filters = {};
    if (deviceType) filters.deviceType = deviceType;
    if (functionType) filters.functionType = functionType;
    if (status) filters.status = status;

    const { db } = await connectDBFunction();
    const result = await Software.findAll(db, filters, { 
      page: parseInt(page) || 1, 
      limit: parseInt(limit) || 20, 
      search 
    });

    res.json({ success: true, data: result });
  } catch (error) {
    console.error('獲取軟體列表錯誤:', error);
    res.status(500).json({ 
      success: false, 
      error: { code: 'FETCH_ERROR', message: error.message } 
    });
  }
});

// 上傳軟體
router.post('/software/upload', 
  authenticate, 
  checkPermission('software:create'),
  upload.single('file'),
  async (req, res) => {
    try {
      const { name, description, category } = req.body;
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: { code: 'NO_FILE', message: '請選擇要上傳的檔案' }
        });
      }

      // 解析bin檔案
      const parser = new BinFileParser();
      const binInfo = parser.parseBinFile(file.buffer);

      // 驗證設備功能支援
      if (!parser.validateDeviceFunctionSupport(binInfo.deviceType, binInfo.functionType)) {
        return res.status(400).json({
          success: false,
          error: { 
            code: 'UNSUPPORTED_COMBINATION', 
            message: `${binInfo.deviceType} 不支援 ${binInfo.functionType} 功能` 
          }
        });
      }

      // 儲存檔案到GridFS
      const { gridFSBucket } = await connectDBFunction();
      
      // 儲存原始檔案
      const originalFileId = await saveToGridFS(gridFSBucket, file.buffer, file.originalname, {
        type: 'original_firmware',
        deviceType: binInfo.deviceType,
        functionType: binInfo.functionType
      });

      // 儲存純bin內容
      const pureBinId = await saveToGridFS(gridFSBucket, binInfo.binData, `${name}_pure.bin`, {
        type: 'pure_firmware',
        deviceType: binInfo.deviceType,
        functionType: binInfo.functionType
      });

      // 儲存軟體資訊
      const { db } = await connectDBFunction();
      const software = await Software.createSoftware(db, {
        name: name || file.originalname,
        description: description || '',
        version: binInfo.version,
        deviceType: binInfo.deviceType,
        functionType: binInfo.functionType,
        originalFilename: file.originalname,
        fileSize: file.size,
        checksum: binInfo.checksum,
        binFileId: originalFileId,
        extractedBinId: pureBinId,
        category: category || 'general',
        uploadedBy: req.user._id,
        uploadDate: new Date()
      });

      res.status(201).json({
        success: true,
        data: { id: software._id, message: '軟體上傳成功' }
      });

    } catch (error) {
      console.error('軟體上傳錯誤:', error);
      
      let errorCode = 'UPLOAD_ERROR';
      if (error.message.includes('CRC')) errorCode = 'CRC_VALIDATION_FAILED';
      if (error.message.includes('已存在')) errorCode = 'DUPLICATE_SOFTWARE';
      if (error.message.includes('不支援')) errorCode = 'UNSUPPORTED_DEVICE_TYPE';

      res.status(400).json({
        success: false,
        error: { code: errorCode, message: error.message }
      });
    }
  }
);

// 輔助函數：儲存到GridFS
async function saveToGridFS(gridFSBucket, buffer, filename, metadata = {}) {
  return new Promise((resolve, reject) => {
    const uploadStream = gridFSBucket.openUploadStream(filename, { metadata });
    
    uploadStream.write(buffer);
    uploadStream.end();
    
    uploadStream.on('finish', () => resolve(uploadStream.id));
    uploadStream.on('error', reject);
  });
}

module.exports = { router, initDB };
```
