# 批量傳送進度顯示功能 - 完整設計文檔

## 📋 功能概述

批量傳送進度顯示功能提供了一個優雅且美觀的實時進度追蹤界面，當用戶執行批量設備傳送操作時，系統會在右下角顯示一個玻璃效果的進度視窗，實時展示傳送進度而不影響用戶的其他操作。

## 🎯 設計目標

- **優雅美觀**: 使用玻璃效果和天藍色配色方案
- **不影響操作**: 顯示在右下角，不阻擋主要操作區域
- **實時反饋**: 提供詳細的進度信息和統計數據
- **用戶友好**: 支持收折、取消等交互功能

## 🏗️ 架構設計

### 前端組件架構
```
BatchSendProgress (主進度組件)
├── 進度顯示區域
│   ├── 標題欄 (狀態指示器 + 標題 + 操作按鈕)
│   ├── 主進度條 (百分比 + 漸變效果)
│   ├── 當前處理設備信息
│   ├── 統計信息網格 (成功/失敗)
│   ├── 詳細統計 (智能選擇、隊列循環)
│   ├── 時間信息 (已用時間/預計剩餘)
│   └── 操作按鈕 (取消傳送)
└── WebSocket客戶端 (實時通信)
```

### 後端服務架構
```
WebSocket服務
├── 前端客戶端連接處理
├── 批量進度事件廣播
├── 訂閱/取消訂閱管理
└── 進度數據收集與分發

批量發送服務
├── 進度追蹤機制
├── 任務隊列處理
├── 智能網關選擇統計
└── 實時進度廣播
```

## 🎨 UI設計規範

### 視覺設計
- **位置**: 固定在右下角 (`fixed bottom-4 right-4`)
- **尺寸**: 寬度 384px (`w-96`)，響應式最大寬度
- **層級**: z-index 50 (`z-50`)

### 玻璃效果實現
```css
backdrop-filter: blur(16px);
WebkitBackdropFilter: blur(16px);
background: rgba(255, 255, 255, 0.9);
border: 1px solid rgba(255, 255, 255, 0.3);
box-shadow: 
  0 8px 32px rgba(0, 0, 0, 0.1), 
  0 4px 16px rgba(0, 0, 0, 0.1);
```

### 色彩方案
- **主色調**: 天藍色系 (`blue-500`, `blue-600`)
- **輔助色**: 紫色漸變 (`purple-500`)
- **成功色**: 綠色系 (`green-500`, `green-600`, `green-700`)
- **錯誤色**: 紅色系 (`red-500`, `red-600`, `red-700`)
- **警告色**: 黃色系 (`yellow-500`)
- **中性色**: 灰色系 (`gray-200`, `gray-500`, `gray-800`)

### 進度條設計
```css
/* 主進度條 */
.progress-bar {
  height: 8px;
  background: linear-gradient(to right, #3b82f6, #10b981);
  border-radius: 9999px;
  transition: width 0.5s ease-out;
}

/* 背景軌道 */
.progress-track {
  background: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}
```

## 📊 數據結構設計

### ProgressData 接口
```typescript
interface ProgressData {
  totalDevices: number;           // 總設備數
  completedDevices: number;       // 已完成設備數
  failedDevices: number;          // 失敗設備數
  currentDevice?: string;         // 當前處理設備名稱
  currentDeviceMac?: string;      // 當前處理設備MAC地址
  status: 'preparing' | 'running' | 'paused' | 'completed' | 'cancelled' | 'error';
  startTime?: number;             // 開始時間戳
  estimatedTimeRemaining?: number; // 預計剩餘時間
  queueCycles?: number;           // 隊列循環次數
  waitCycles?: number;            // 等待循環次數
  smartSelectionStats?: {         // 智能選擇統計
    totalAutoModeDevices: number;
    usedBackupGateway: number;
    primaryGatewayBusy: number;
  };
}
```

### WebSocket事件結構
```typescript
interface BatchProgressEvent {
  type: 'batch_progress';
  batchId: string;
  totalDevices: number;
  completedDevices: number;
  failedDevices: number;
  currentDevice?: {
    id: string;
    macAddress: string;
    name?: string;
  };
  status: string;
  startTime?: number;
  estimatedTimeRemaining?: number;
  queueCycles?: number;
  waitCycles?: number;
  smartSelectionStats?: object;
  error?: string;
}

interface BatchCompleteEvent {
  type: 'batch_complete';
  batchId: string;
  result: {
    totalCount: number;
    successCount: number;
    failedCount: number;
    smartSelectionStats?: any;
    performanceStats?: any;
  };
}
```

## 🔧 技術實現細節

### 前端實現

#### 1. 組件狀態管理
```typescript
const [isCollapsed, setIsCollapsed] = useState(false);
const [progressData, setProgressData] = useState<ProgressData>({
  totalDevices: 0,
  completedDevices: 0,
  failedDevices: 0,
  status: 'preparing'
});
```

#### 2. 進度計算邏輯
```typescript
// 進度百分比計算
const progressPercentage = progressData.totalDevices > 0 
  ? Math.round((progressData.completedDevices / progressData.totalDevices) * 100)
  : 0;

// 成功率計算
const successRate = progressData.completedDevices > 0
  ? Math.round(((progressData.completedDevices - progressData.failedDevices) / progressData.completedDevices) * 100)
  : 100;

// 時間格式化
const formatTime = (seconds: number) => {
  if (seconds < 60) return `${seconds}秒`;
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}分${remainingSeconds}秒`;
};
```

#### 3. 狀態指示器
```typescript
const getStatusColor = () => {
  switch (progressData.status) {
    case 'preparing': return 'bg-blue-500';
    case 'running': return 'bg-green-500';
    case 'paused': return 'bg-yellow-500';
    case 'completed': return 'bg-green-600';
    case 'cancelled': return 'bg-gray-500';
    case 'error': return 'bg-red-500';
    default: return 'bg-blue-500';
  }
};
```

### 後端實現

#### 1. WebSocket服務增強
```javascript
// 批量進度訂閱管理
const batchProgressSubscribers = new Map(); // batchId -> Set of client websockets

// 廣播批量傳送進度
const broadcastBatchProgress = (batchId, progressData) => {
  const subscribers = batchProgressSubscribers.get(batchId);
  if (!subscribers || subscribers.size === 0) return;

  const message = JSON.stringify({
    type: 'batch_progress',
    batchId,
    ...progressData,
    timestamp: new Date().toISOString()
  });

  subscribers.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(message);
      } catch (error) {
        console.error('發送批量進度消息失敗:', error);
        subscribers.delete(ws);
      }
    }
  });
};
```

#### 2. 前端客戶端連接處理
```javascript
// 處理前端客戶端連接
if (req.clientType === 'frontend') {
  console.log(`前端客戶端已連接: ${req.username}`);
  
  ws.clientType = 'frontend';
  ws.userId = req.userId;
  ws.username = req.username;
  ws.isAlive = true;
  ws.connectionTime = Date.now();
  
  // 發送歡迎消息
  ws.send(JSON.stringify({
    type: 'welcome',
    message: '歡迎連接到EPD Manager WebSocket服務',
    timestamp: new Date().toISOString()
  }));
}
```

## 🚀 使用流程

### 1. 批量傳送啟動
```typescript
// 生成批量傳送ID
const batchId = `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
setCurrentBatchId(batchId);
setShowBatchProgress(true);

// 訂閱進度更新
const unsubscribe = subscribeToBatchProgress(batchId, handleWebSocketEvent);
```

### 2. 進度更新流程
```
用戶點擊批量發送
    ↓
生成唯一批量ID
    ↓
顯示進度組件
    ↓
訂閱WebSocket事件
    ↓
後端開始批量處理
    ↓
實時廣播進度更新
    ↓
前端接收並更新UI
    ↓
處理完成後顯示結果
    ↓
3秒後自動隱藏
```

### 3. 進度數據更新
```typescript
// 模擬進度更新邏輯
const interval = setInterval(() => {
  setProgressData(prev => {
    if (prev.status === 'completed') return prev;
    
    const newCompleted = Math.min(prev.completedDevices + 1, prev.totalDevices);
    const isCompleted = newCompleted >= prev.totalDevices;
    
    return {
      ...prev,
      completedDevices: newCompleted,
      status: isCompleted ? 'completed' : 'running',
      currentDevice: isCompleted ? undefined : `設備 ${newCompleted + 1}`,
      // ... 其他更新邏輯
    };
  });
}, 1500);
```

## 📱 響應式設計

### 移動端適配
- 最大寬度限制: `max-w-[calc(100vw-2rem)]`
- 觸摸友好的按鈕尺寸
- 適當的間距和字體大小

### 桌面端優化
- 固定寬度: 384px
- 精確的定位和陰影效果
- 鼠標懸停交互效果

## 🔄 動畫效果

### 進度條動畫
```css
transition: width 0.5s ease-out;
```

### 狀態指示器動畫
```css
animate-pulse /* 呼吸燈效果 */
```

### 組件顯示/隱藏
- 平滑的淡入淡出效果
- 適當的過渡時間

## 🎛️ 交互功能

### 收折功能
- 點擊箭頭圖標收折/展開內容
- 保持標題欄可見
- 節省屏幕空間

### 取消功能
- 運行中可取消批量傳送
- 確認對話框防止誤操作
- 清理相關狀態和訂閱

### 關閉功能
- 手動關閉進度視窗
- 自動清理資源
- 取消WebSocket訂閱

## 📈 性能優化

### 前端優化
- 使用React.memo減少不必要的重渲染
- 合理的useEffect依賴項
- 及時清理定時器和事件監聽器

### 後端優化
- WebSocket連接池管理
- 批量事件廣播優化
- 內存使用監控

## 🐛 錯誤處理

### WebSocket連接錯誤
- 自動重連機制
- 降級到輪詢模式
- 用戶友好的錯誤提示

### 進度數據異常
- 數據驗證和容錯
- 默認值處理
- 異常狀態顯示

## 🔮 未來擴展

### 計劃功能
- [ ] 實時WebSocket連接
- [ ] 進度歷史記錄
- [ ] 自定義通知設置
- [ ] 批量操作暫停/恢復
- [ ] 詳細的錯誤日誌

### 技術改進
- [ ] 更精確的時間預估算法
- [ ] 網絡狀態感知
- [ ] 離線模式支持
- [ ] 多語言支持

## 📝 維護說明

### 代碼位置
- 主組件: `src/components/BatchSendProgress.tsx`
- WebSocket客戶端: `src/utils/websocketClient.ts`
- 設備頁面集成: `src/components/DevicesPage.tsx`
- 後端WebSocket服務: `server/services/websocketService.js`
- 批量發送服務: `server/services/sendPreviewToGateway.js`

### 配置參數
- 進度更新間隔: 1.5秒
- 自動隱藏延遲: 3秒
- WebSocket重連次數: 5次
- 最大等待時間: 10分鐘

## 🛠️ 開發指南

### 本地開發設置
```bash
# 啟動開發服務器
npm run dev

# 前端: http://localhost:5173
# 後端: http://localhost:3001
# WebSocket: ws://localhost:3001
```

### 測試批量傳送進度
1. 登入系統 (用戶名: root, 密碼: 12345689)
2. 進入設備管理頁面
3. 選擇多個設備
4. 點擊"批量發送"按鈕
5. 觀察右下角進度視窗

### 調試WebSocket連接
```javascript
// 在瀏覽器控制台中檢查WebSocket狀態
const wsClient = getWebSocketClient();
console.log('WebSocket連接狀態:', wsClient.isConnected());

// 手動訂閱進度事件
wsClient.subscribeBatchProgress('test-batch-id');
```

## 📋 文件清單

### 新增文件
```
src/components/BatchSendProgress.tsx     # 主進度組件
src/utils/websocketClient.ts            # WebSocket客戶端
docs/batch-send-progress-feature.md     # 本文檔
```

### 修改文件
```
src/components/DevicesPage.tsx           # 集成進度組件
src/utils/api/deviceApi.ts              # 添加batchId參數
server/services/websocketService.js     # WebSocket服務增強
server/services/sendPreviewToGateway.js # 進度廣播功能
```

## 🔍 代碼示例

### 前端組件使用
```tsx
// 在DevicesPage.tsx中使用
<BatchSendProgress
  isVisible={showBatchProgress}
  onClose={handleCloseBatchProgress}
  onCancel={batchSending ? handleCancelBatchSend : undefined}
  batchId={currentBatchId || undefined}
/>
```

### WebSocket事件監聽
```typescript
// 訂閱批量進度
const unsubscribe = subscribeToBatchProgress(batchId, (event: WebSocketEvent) => {
  if (event.type === 'batch_progress') {
    console.log('進度更新:', event);
  } else if (event.type === 'batch_complete') {
    console.log('批量完成:', event);
  }
});

// 清理訂閱
unsubscribe();
```

### 後端進度廣播
```javascript
// 在批量發送過程中廣播進度
websocketService.broadcastBatchProgress(batchId, {
  totalDevices: deviceIds.length,
  completedDevices: currentCompleted,
  failedDevices: currentFailed,
  status: 'running',
  queueCycles,
  waitCycles,
  smartSelectionStats: { ...smartSelectionStats }
});
```

## 🎯 設計原則

### 用戶體驗原則
1. **非侵入性**: 不阻擋主要操作區域
2. **信息豐富**: 提供詳細但不冗餘的進度信息
3. **視覺一致**: 與整體設計風格保持一致
4. **響應迅速**: 實時更新，無明顯延遲

### 技術設計原則
1. **模組化**: 組件獨立，易於維護
2. **可擴展**: 預留接口，支持功能擴展
3. **容錯性**: 優雅處理異常情況
4. **性能優化**: 避免不必要的渲染和計算

## 🔧 故障排除

### 常見問題

#### 1. 進度視窗不顯示
**可能原因**:
- `showBatchProgress` 狀態未正確設置
- `batchId` 為空或無效

**解決方案**:
```typescript
// 檢查狀態設置
console.log('showBatchProgress:', showBatchProgress);
console.log('currentBatchId:', currentBatchId);
```

#### 2. 進度不更新
**可能原因**:
- WebSocket連接失敗
- 事件監聽器未正確設置
- 後端進度廣播異常

**解決方案**:
```typescript
// 檢查WebSocket連接
const wsClient = getWebSocketClient();
console.log('WebSocket狀態:', wsClient.isConnected());

// 檢查事件監聽
wsClient.addEventListener((event) => {
  console.log('收到WebSocket事件:', event);
});
```

#### 3. 樣式顯示異常
**可能原因**:
- Tailwind CSS類名衝突
- 瀏覽器兼容性問題
- CSS優先級問題

**解決方案**:
```css
/* 檢查CSS是否正確應用 */
.batch-progress-container {
  backdrop-filter: blur(16px) !important;
  -webkit-backdrop-filter: blur(16px) !important;
}
```

## 📊 性能指標

### 目標性能
- 進度更新延遲: < 100ms
- 組件渲染時間: < 16ms
- 內存使用: < 10MB
- WebSocket連接建立: < 1s

### 監控方法
```javascript
// 性能監控示例
console.time('progress-update');
setProgressData(newData);
console.timeEnd('progress-update');

// 內存使用監控
console.log('內存使用:', performance.memory?.usedJSHeapSize);
```

---

**創建日期**: 2025-06-12
**版本**: 1.0.0
**作者**: EPD Manager Development Team
**狀態**: 已實現 (模擬模式)
**最後更新**: 2025-06-12
