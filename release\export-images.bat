@echo off
chcp 65001 >nul
echo ========================================
echo EPD Manager Docker Image Export Tool
echo ========================================
echo.

:: Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not running or not installed. Please start Docker Desktop first.
    pause
    exit /b 1
)

:: Check if in correct directory
if not exist "docker-compose.yml" (
    echo [ERROR] Please run this script from the project root directory
    echo Current directory: %cd%
    pause
    exit /b 1
)

echo [INFO] Building Docker images...
docker-compose build
if %errorlevel% neq 0 (
    echo [ERROR] Docker image build failed
    pause
    exit /b 1
)

echo.
echo [INFO] Exporting all images to single file...
docker save -o release\epd-manager-all.tar ^
  epd-manager-lite-epd-manager:latest ^
  mongo:7-jammy

if %errorlevel% neq 0 (
    echo [ERROR] Image export failed
    pause
    exit /b 1
)

echo.
echo [SUCCESS] All images exported to single file:
echo   - epd-manager-all.tar (Contains EPD Manager App + MongoDB Database)
echo.
echo [TIP] You can now copy the release folder to target host and run deployment
echo.

:: Show file size
if exist "release\epd-manager-all.tar" (
    echo File size:
    dir "release\epd-manager-all.tar" | findstr /C:"epd-manager-all.tar"
)

echo.
echo Press any key to exit...
pause >nul
