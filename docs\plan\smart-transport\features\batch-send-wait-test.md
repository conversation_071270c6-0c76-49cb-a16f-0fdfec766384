# 批量發送等待機制測試

## 測試目的

驗證新的等待機制是否能有效解決「列隊循環使用的是無等待循環，這樣會造成如果發送時間較長，後面的等待循環會全部失敗」的問題。

## 測試場景

### 場景1：所有網關忙碌時的等待機制

**測試步驟**：
1. 準備4個設備，都配置為智能模式
2. 確保所有網關都在進行chunk傳輸（忙碌狀態）
3. 執行批量發送
4. 觀察日誌輸出

**預期結果**：
```
🔄 任務隊列循環 1，剩餘任務: 4，正在處理: 0
🔍 檢查任務是否可以立即處理...
❌ 沒有可立即處理的任務，等待網關變為可用...
⏳ 等待循環 1/10，等待網關: [gateway1, gateway2, gateway3]
🔔 網關 gateway1 從忙碌變為閒置，發射狀態變化事件
🔔 有網關變為可用，繼續處理任務
✅ 找到可處理的任務，開始處理設備 device1
```

### 場景2：部分網關忙碌時的智能選擇

**測試步驟**：
1. 準備4個設備，混合智能模式和手動模式
2. 部分網關忙碌，部分網關空閒
3. 執行批量發送
4. 觀察任務處理順序

**預期結果**：
- 有可用網關的任務優先處理
- 沒有可用網關的任務等待
- 智能模式設備能使用備用網關

### 場景3：等待超時保護

**測試步驟**：
1. 模擬網關長時間忙碌（超過10秒）
2. 執行批量發送
3. 觀察超時保護是否生效

**預期結果**：
```
⏳ 等待循環 1/10，等待網關: [gateway1]
⚠️ 等待網關可用超時: 等待網關可用超時 (10000ms)
🔄 任務隊列循環 2，剩餘任務: 1，正在處理: 0
```

## 測試代碼示例

```javascript
// 測試批量發送等待機制
const testBatchSendWaitMechanism = async () => {
  console.log('🧪 開始測試批量發送等待機制');
  
  // 準備測試設備ID
  const deviceIds = [
    '674926e7f210fc4c110fe961',
    '674926e7f210fc4c110fe962', 
    '674926e7f210fc4c110fe963',
    '674926e7f210fc4c110fe964'
  ];
  
  try {
    // 執行批量發送
    const result = await sendMultipleDevicePreviewsToGateways(deviceIds, {
      concurrency: 3,
      enableSmartSelection: true,
      sendToAllGateways: false
    });
    
    console.log('📊 測試結果:', {
      totalCount: result.totalCount,
      successCount: result.successCount,
      failedCount: result.failedCount,
      smartSelectionStats: result.smartSelectionStats,
      performanceStats: result.performanceStats
    });
    
    // 驗證等待機制是否生效
    if (result.performanceStats.waitCycles > 0) {
      console.log('✅ 等待機制已生效');
    } else {
      console.log('ℹ️ 沒有觸發等待機制（所有任務都能立即處理）');
    }
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
  }
};
```

## 關鍵指標監控

### 1. 等待機制指標
- `waitCycles`: 等待循環次數
- `waitTimeout`: 等待超時次數
- `gatewayAvailableEvents`: 網關可用事件觸發次數

### 2. 性能指標
- `queueCycles`: 隊列循環次數（應該減少）
- `avgProcessingTime`: 平均處理時間
- `successRate`: 成功率（應該提高）

### 3. 資源使用指標
- CPU使用率（應該降低）
- 網絡請求次數（應該減少無效重試）
- 內存使用情況

## 預期改進效果

### 改進前（無等待循環）
```
🔄 任務隊列循環 1，剩餘任務: 4，正在處理: 0
❌ 任務失敗，重新排隊 (重試 1/3)
🔄 任務隊列循環 2，剩餘任務: 4，正在處理: 0  
❌ 任務失敗，重新排隊 (重試 2/3)
🔄 任務隊列循環 3，剩餘任務: 4，正在處理: 0
❌ 任務失敗，重新排隊 (重試 3/3)
❌ 任務最終失敗
```

### 改進後（智能等待）
```
🔄 任務隊列循環 1，剩餘任務: 4，正在處理: 0
⏳ 等待網關變為可用...
🔔 網關變為可用，繼續處理任務
✅ 任務處理成功
```

## 測試環境要求

1. **網關狀態**：至少2個網關，能模擬忙碌和空閒狀態
2. **設備配置**：混合智能模式和手動模式設備
3. **並發設置**：concurrency設置為2-3，便於觀察等待效果
4. **日誌級別**：設置為詳細模式，便於觀察等待過程

## 成功標準

1. ✅ **等待機制生效**：當所有網關忙碌時能正確等待
2. ✅ **事件觸發正常**：網關狀態變化能正確觸發繼續處理
3. ✅ **超時保護有效**：等待超時時能正確處理
4. ✅ **成功率提升**：相比改進前，任務成功率明顯提高
5. ✅ **資源使用優化**：減少無效的重試循環

## 故障排除

### 常見問題
1. **等待機制未觸發**：檢查網關狀態事件是否正確發射
2. **等待超時過頻**：調整等待時間或檢查網關性能
3. **任務處理順序異常**：檢查任務可處理性判斷邏輯

### 調試方法
1. 啟用詳細日誌輸出
2. 監控網關狀態變化事件
3. 檢查任務隊列處理流程
4. 驗證超時保護機制
