# 圖片URL動態IP修復測試

## 測試場景

### 1. 新上傳圖片測試
- ✅ 上傳新圖片後，右側應顯示 "文件名: xxx.jpg"
- ✅ 確認選擇後，傳遞給 onSelect 的應該是文件ID而不是完整URL
- ✅ 圖片預覽應該正常顯示

### 2. 選擇現有圖片測試
- ✅ 從資源庫選擇圖片後，右側應顯示 "文件名: xxx.jpg"
- ✅ 確認選擇後，傳遞給 onSelect 的應該是文件ID
- ✅ 圖片預覽應該正常顯示

### 3. 向後相容性測試
- ✅ 現有的完整URL應該能正確提取文件ID
- ✅ 相對路徑 `/api/files/xxx` 應該能正確提取文件ID
- ✅ 已經是文件ID格式的應該直接使用

### 4. 圖片屬性面板測試
- ✅ 當 imageUrl 是文件ID時，應顯示實際的文件名（如 "image.jpg"）
- ✅ 當 imageUrl 是完整URL時，應顯示檔案名
- ✅ 預覽圖片應該使用 processImageUrl 處理後的URL
- ✅ 如果無法獲取文件名，回退顯示 "文件ID: xxx..."

## 修改的文件

### 前端文件
1. `src/utils/imageUrlUtils.ts` - 新增統一的圖片URL處理工具
2. `src/components/editor/properties/ImageSelectorModal.tsx` - 修改選擇邏輯
3. `src/components/editor/properties/ImageProperties.tsx` - 修改顯示邏輯
4. `src/components/editor/elements/ImageElement.tsx` - 使用統一工具函數
5. `src/components/editor/canvasUtils.tsx` - 使用統一工具函數
6. `src/components/PreviewComponent.tsx` - 使用統一工具函數

### 後端工具
1. `server/utils/imageUrlMigration.js` - 資料庫遷移工具
2. `server/scripts/migrate-image-urls.js` - 遷移執行腳本

## 預期行為

### 圖片選擇流程
1. 用戶點擊"選擇圖片"按鈕
2. 在圖片選擇器中選擇或上傳圖片
3. 右側預覽區顯示：
   - 圖片預覽（使用完整URL）
   - 文件信息顯示 "文件名: xxx.jpg"（而不是完整URL或文件ID）
4. 點擊"確認選擇"
5. 傳遞給 onSelect 的是文件ID
6. 儲存到資料庫的是文件ID

### 圖片顯示流程
1. 從資料庫讀取 imageUrl（文件ID格式）
2. 使用 processImageUrl() 動態組裝完整URL
3. 顯示圖片

## 測試步驟

1. **測試新上傳**：
   - 打開圖片選擇器
   - 切換到"本地上傳"標籤
   - 選擇並上傳一張圖片
   - 檢查右側是否顯示 "文件名: xxx.jpg"
   - 點擊確認選擇
   - 檢查元素的 imageUrl 屬性是否為文件ID格式

2. **測試資源庫選擇**：
   - 打開圖片選擇器
   - 在"資源庫"標籤中選擇一張圖片
   - 檢查右側是否顯示 "文件名: xxx.jpg"
   - 點擊確認選擇
   - 檢查元素的 imageUrl 屬性是否為文件ID格式

3. **測試圖片顯示**：
   - 確認圖片在編輯器中正常顯示
   - 確認圖片在預覽組件中正常顯示
   - 確認圖片在屬性面板中正常顯示

4. **測試預覽圖生成**：
   - 創建包含圖片元素的模板
   - 在"綁定資料"中選擇該模板
   - 確認預覽圖正確生成，圖片正常顯示
   - 檢查控制台是否有圖片載入錯誤

4. **測試資料庫遷移**（可選）：
   ```bash
   node server/scripts/migrate-image-urls.js
   ```

## 解決的問題

1. ✅ **IP固定問題**：圖片URL不再包含固定IP
2. ✅ **部署靈活性**：適合各種部署環境
3. ✅ **向後相容性**：支援現有的URL格式
4. ✅ **程式碼重複**：統一了圖片URL處理邏輯
5. ✅ **用戶體驗**：顯示有意義的文件名而不是文件ID
6. ✅ **預覽圖生成**：修復了綁定資料時圖片載入失敗導致預覽圖生成失敗的問題
