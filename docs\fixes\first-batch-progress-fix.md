# 第一次批量發送進度不更新問題修復報告

## 問題描述

用戶反映每次新進入網頁的第一次批量發送，進度視窗內容數據都不會更新，第二次批量就會正常進度。

## 問題分析

從日誌分析發現問題的根本原因是 **WebSocket 連接時序問題**：

### 時序問題流程
1. **用戶點擊批量發送**
2. **BatchSendProgress 組件嘗試訂閱進度事件**
3. **此時 WebSocket 還沒有連接**（從日誌看到連接建立在批量發送過程中）
4. **訂閱消息發送失敗**（WebSocket 未連接）
5. **WebSocket 連接建立後，已經錯過了初始的進度事件**

### 日誌證據
```
websocketClient.ts:85 成功獲取WebSocket token
websocketClient.ts:108 正在連接WebSocket: ws://*************:3001
websocketClient.ts:112 WebSocket連接已建立
websocketClient.ts:179 收到服務器歡迎消息: 歡迎連接到EPD Manager WebSocket服務
```

這些日誌出現在批量發送過程中，說明 WebSocket 連接是延遲建立的。

## 修復方案

### 1. 待處理訂閱機制

**文件**: `src/utils/websocketClient.ts`

添加了待處理訂閱機制，確保在 WebSocket 連接建立後能夠重新發送訂閱消息：

```typescript
class WebSocketClient {
  private pendingSubscriptions = new Set<string>(); // 待處理的訂閱
  
  // 在連接建立後處理待處理的訂閱
  private processPendingSubscriptions() {
    if (this.pendingSubscriptions.size > 0) {
      console.log(`處理 ${this.pendingSubscriptions.size} 個待處理的訂閱`);
      for (const batchId of this.pendingSubscriptions) {
        this.send({
          type: 'subscribe_batch_progress',
          batchId,
          timestamp: new Date().toISOString()
        });
      }
    }
  }
}
```

### 2. 智能訂閱邏輯

修改 `subscribeBatchProgress` 方法：

```typescript
public subscribeBatchProgress(batchId: string) {
  // 添加到待處理訂閱列表
  this.pendingSubscriptions.add(batchId);
  
  // 如果已連接，立即發送訂閱消息
  if (this.ws?.readyState === WebSocket.OPEN) {
    this.send({
      type: 'subscribe_batch_progress',
      batchId,
      timestamp: new Date().toISOString()
    });
  } else {
    console.log(`WebSocket未連接，將 ${batchId} 添加到待處理訂閱列表`);
  }
}
```

### 3. 智能取消訂閱邏輯

修改 `unsubscribeBatchProgress` 方法：

```typescript
public unsubscribeBatchProgress(batchId: string) {
  // 從待處理訂閱列表中移除
  this.pendingSubscriptions.delete(batchId);
  
  // 如果已連接，發送取消訂閱消息
  if (this.ws?.readyState === WebSocket.OPEN) {
    this.send({
      type: 'unsubscribe_batch_progress',
      batchId,
      timestamp: new Date().toISOString()
    });
  }
}
```

### 4. 應用啟動時初始化 WebSocket

**文件**: `src/main.tsx`

在應用啟動時就建立 WebSocket 連接，而不是等到第一次使用時：

```typescript
// 初始化 WebSocket 客戶端
import { getWebSocketClient } from './utils/websocketClient';

// 在應用啟動時就建立 WebSocket 連接
console.log('初始化 WebSocket 客戶端...');
getWebSocketClient();
```

### 5. 連接建立後處理待處理訂閱

在 `onopen` 事件處理器中添加：

```typescript
this.ws.onopen = () => {
  console.log('WebSocket連接已建立');
  // ... 其他邏輯
  
  // 處理待處理的訂閱
  this.processPendingSubscriptions();
};
```

## 修復效果

### 修復前的問題
1. ❌ 第一次批量發送時 WebSocket 未連接
2. ❌ 訂閱消息發送失敗，無法接收進度事件
3. ❌ 進度視窗顯示初始狀態，沒有數據更新
4. ❌ 第二次批量發送才能正常工作（因為 WebSocket 已連接）

### 修復後的改進
1. ✅ 應用啟動時就建立 WebSocket 連接
2. ✅ 待處理訂閱機制確保不會遺漏訂閱
3. ✅ 智能訂閱邏輯，連接後自動重新發送訂閱
4. ✅ 第一次批量發送就能正常顯示進度

## 工作流程

### 修復後的正常流程
1. **應用啟動** → WebSocket 連接建立
2. **用戶點擊批量發送** → 生成 batchId
3. **BatchSendProgress 組件訂閱** → 立即發送訂閱消息（WebSocket 已連接）
4. **後端發送進度事件** → 前端正常接收並更新 UI
5. **進度視窗正常顯示** → 實時更新進度數據

### 異常情況處理
1. **WebSocket 斷線重連** → 自動重新發送待處理的訂閱
2. **連接延遲** → 訂閱消息暫存，連接後自動發送
3. **多次訂閱同一 batchId** → 去重處理，避免重複訂閱

## 技術細節

### 待處理訂閱的生命週期
1. **添加**: 調用 `subscribeBatchProgress` 時添加到 `pendingSubscriptions`
2. **處理**: WebSocket 連接建立後自動發送所有待處理訂閱
3. **移除**: 調用 `unsubscribeBatchProgress` 時從 `pendingSubscriptions` 移除
4. **清理**: WebSocket 客戶端銷毀時清空所有待處理訂閱

### 重連機制
- 待處理訂閱在重連後會自動重新發送
- 確保即使在網路不穩定的情況下也能正常工作

### 性能考慮
- 使用 `Set` 數據結構，確保 batchId 唯一性
- 避免重複訂閱同一個 batchId
- 及時清理不需要的訂閱

## 相關文件

### 修改的文件
- `src/utils/websocketClient.ts` - 添加待處理訂閱機制
- `src/main.tsx` - 應用啟動時初始化 WebSocket

### 測試建議
1. **清除瀏覽器緩存**，模擬新用戶首次訪問
2. **第一次批量發送**，確認進度視窗正常更新
3. **網路斷線重連**，確認訂閱能夠恢復
4. **多次批量發送**，確認沒有重複訂閱問題

## 驗證步驟

1. 清除瀏覽器緩存或使用無痕模式
2. 打開應用，確認 WebSocket 連接建立
3. 立即進行第一次批量發送
4. 觀察進度視窗是否正常顯示和更新
5. 檢查瀏覽器控制台，確認沒有"WebSocket未連接"的警告

## 後續改進建議

1. **連接狀態指示器**: 在 UI 中顯示 WebSocket 連接狀態
2. **重連通知**: 當 WebSocket 重連時通知用戶
3. **離線模式**: 在無網路時提供離線功能提示
4. **連接超時處理**: 為 WebSocket 連接添加超時機制

這個修復確保了第一次批量發送就能正常顯示進度，解決了用戶體驗問題。
