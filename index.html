<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#ffffff" />
    <title>EPD Manager</title>

    <!-- crypto.randomUUID polyfill - 必須在所有其他腳本之前執行 -->
    <script>
      (function() {
        'use strict';

        // 確保 crypto 對象存在
        if (!window.crypto) {
          window.crypto = {};
        }

        // 如果 randomUUID 不存在，添加 polyfill
        if (!window.crypto.randomUUID) {
          window.crypto.randomUUID = function() {
            // 使用 crypto.getRandomValues 如果可用
            if (window.crypto && window.crypto.getRandomValues) {
              const randomValues = new Uint8Array(16);
              window.crypto.getRandomValues(randomValues);

              // 設置版本位 (4) 和變體位
              randomValues[6] = (randomValues[6] & 0x0f) | 0x40; // 版本 4
              randomValues[8] = (randomValues[8] & 0x3f) | 0x80; // 變體位

              // 轉換為 UUID 字符串格式
              const hex = Array.from(randomValues, function(byte) {
                return byte.toString(16).padStart(2, '0');
              }).join('');

              return [
                hex.slice(0, 8),
                hex.slice(8, 12),
                hex.slice(12, 16),
                hex.slice(16, 20),
                hex.slice(20, 32)
              ].join('-');
            } else {
              // 回退到 Math.random（不夠安全，但可以工作）
              return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
              });
            }
          };

          console.log('crypto.randomUUID polyfill 已載入');
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
