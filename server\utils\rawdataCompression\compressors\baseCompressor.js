/**
 * 基礎壓縮器抽象類
 * 所有具體壓縮器都應該繼承此類
 */

const { createCompressionResult } = require('../types');

/**
 * 基礎壓縮器抽象類
 */
class BaseCompressor {
  /**
   * 構造函數
   * @param {string} formatName - 格式名稱
   */
  constructor(formatName) {
    if (new.target === BaseCompressor) {
      throw new Error('BaseCompressor is abstract and cannot be instantiated directly');
    }
    
    this.formatName = formatName;
    this.isInitialized = false;
  }
  
  /**
   * 初始化壓縮器
   * 子類可以重寫此方法進行特定初始化
   */
  initialize() {
    this.isInitialized = true;
    console.log(`Compressor ${this.formatName} initialized`);
  }
  
  /**
   * 壓縮像素數據（不包含 ImageInfo）
   * 子類必須實作此方法
   * @param {Uint8Array} pixelData - 像素數據
   * @returns {CompressionResult} 壓縮結果
   */
  compress(pixelData) {
    throw new Error('compress method must be implemented by subclass');
  }
  
  /**
   * 解壓縮數據
   * 子類必須實作此方法
   * @param {Uint8Array} compressedData - 壓縮數據
   * @returns {Uint8Array} 解壓縮後的數據
   */
  decompress(compressedData) {
    throw new Error('decompress method must be implemented by subclass');
  }
  
  /**
   * 快速估算壓縮比（不實際壓縮）
   * 子類必須實作此方法
   * @param {Uint8Array} pixelData - 像素數據
   * @returns {number} 預估壓縮比 (0.0 - 1.0)
   */
  estimateCompressionRatio(pixelData) {
    throw new Error('estimateCompressionRatio method must be implemented by subclass');
  }
  
  /**
   * 檢查數據是否適合此壓縮格式
   * 子類可以重寫此方法提供更精確的判斷
   * @param {Uint8Array} pixelData - 像素數據
   * @returns {boolean} 是否適合
   */
  isSuitableFor(pixelData) {
    if (!pixelData || pixelData.length === 0) {
      return false;
    }
    
    // 預設實作：估算壓縮比，如果小於 0.9 則認為適合
    try {
      return this.estimateCompressionRatio(pixelData) < 0.9;
    } catch (error) {
      console.warn(`Error estimating compression ratio for ${this.formatName}:`, error.message);
      return false;
    }
  }
  
  /**
   * 獲取格式名稱
   * @returns {string} 格式名稱
   */
  getFormatName() {
    return this.formatName;
  }
  
  /**
   * 檢查是否已初始化
   * @returns {boolean} 是否已初始化
   */
  isReady() {
    return this.isInitialized;
  }
  
  /**
   * 創建壓縮結果對象
   * @param {boolean} success - 是否成功
   * @param {Uint8Array} originalData - 原始數據
   * @param {Uint8Array} compressedData - 壓縮數據
   * @param {number} processingTime - 處理時間
   * @param {string} error - 錯誤信息
   * @returns {CompressionResult} 壓縮結果
   */
  createResult(success, originalData, compressedData = null, processingTime = 0, error = null) {
    return createCompressionResult(success, this.formatName, originalData, compressedData, processingTime, error);
  }
  
  /**
   * 驗證輸入數據
   * @param {Uint8Array} data - 輸入數據
   * @param {string} operation - 操作名稱（用於錯誤信息）
   * @throws {Error} 如果數據無效
   */
  validateInput(data, operation = 'operation') {
    if (!data) {
      throw new Error(`Invalid input for ${operation}: data is null or undefined`);
    }
    
    if (!(data instanceof Uint8Array)) {
      throw new Error(`Invalid input for ${operation}: data must be Uint8Array`);
    }
    
    if (data.length === 0) {
      throw new Error(`Invalid input for ${operation}: data is empty`);
    }
  }
  
  /**
   * 安全的性能測量
   * @returns {number} 當前時間戳（毫秒）
   */
  getTimestamp() {
    if (typeof performance !== 'undefined' && performance.now) {
      return performance.now();
    } else {
      return Date.now();
    }
  }
  
  /**
   * 記錄壓縮統計信息
   * @param {CompressionResult} result - 壓縮結果
   */
  logCompressionStats(result) {
    if (result.success) {
      console.log(`${this.formatName} compression stats:`, {
        originalSize: result.originalSize,
        compressedSize: result.compressedSize,
        ratio: `${(result.compressionRatio * 100).toFixed(1)}%`,
        processingTime: `${result.processingTime.toFixed(2)}ms`,
        savings: result.originalSize - result.compressedSize
      });
    } else {
      console.error(`${this.formatName} compression failed:`, result.error);
    }
  }
}

module.exports = BaseCompressor;
