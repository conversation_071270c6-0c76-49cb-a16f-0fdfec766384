/**
 * 智能網關選擇功能測試腳本
 * 
 * 測試場景：
 * 1. 主要網關空閒時使用主要網關
 * 2. 主要網關忙碌時選擇備用網關
 * 3. 備用網關失敗時回退到主要網關
 * 4. 狀態追蹤和清理機制
 */

const websocketService = require('../services/websocketService');

// 模擬測試數據
const testGateways = {
  'gateway1': { id: 'gateway1', name: '主要網關', online: true },
  'gateway2': { id: 'gateway2', name: '備用網關1', online: true },
  'gateway3': { id: 'gateway3', name: '備用網關2', online: true }
};

const testDevice = {
  _id: 'device123',
  macAddress: 'AA:BB:CC:DD:EE:FF',
  primaryGatewayId: 'gateway1',
  otherGateways: ['gateway2', 'gateway3'],
  gatewaySelectionMode: 'auto'
};

// 測試函數
async function testSmartGatewaySelection() {
  console.log('🧪 開始智能網關選擇功能測試\n');

  // 測試1: 檢查網關忙碌狀態
  console.log('📋 測試1: 網關忙碌狀態檢查');
  
  // 初始狀態 - 所有網關都應該是空閒的
  console.log(`Gateway1 忙碌狀態: ${websocketService.isGatewayBusyWithChunk('gateway1')}`);
  console.log(`Gateway2 忙碌狀態: ${websocketService.isGatewayBusyWithChunk('gateway2')}`);
  console.log(`Gateway3 忙碌狀態: ${websocketService.isGatewayBusyWithChunk('gateway3')}`);

  // 測試2: 模擬chunk傳輸開始
  console.log('\n📋 測試2: 模擬chunk傳輸狀態');
  
  // 模擬gateway1開始chunk傳輸
  console.log('開始模擬 Gateway1 的chunk傳輸...');
  // 注意：這裡我們無法直接調用內部函數，需要通過實際的chunk傳輸來測試
  
  // 測試3: 獲取可用網關
  console.log('\n📋 測試3: 獲取可用網關');
  
  const allGatewayIds = ['gateway1', 'gateway2', 'gateway3'];
  const availableGateways = websocketService.getAvailableGateways(allGatewayIds);
  console.log(`所有網關: ${allGatewayIds.join(', ')}`);
  console.log(`可用網關: ${availableGateways.join(', ')}`);

  // 測試4: 智能選擇邏輯模擬
  console.log('\n📋 測試4: 智能選擇邏輯模擬');
  
  function simulateSmartSelection(device, primaryGatewayBusy = false) {
    console.log(`\n設備: ${device._id}`);
    console.log(`網關選擇模式: ${device.gatewaySelectionMode}`);
    console.log(`主要網關: ${device.primaryGatewayId}`);
    console.log(`其他網關: ${device.otherGateways.join(', ')}`);
    console.log(`主要網關忙碌: ${primaryGatewayBusy}`);
    
    if (device.gatewaySelectionMode === 'auto') {
      if (primaryGatewayBusy) {
        // 尋找備用網關
        const availableBackups = device.otherGateways.filter(gw => 
          websocketService.isGatewayOnline && !websocketService.isGatewayBusyWithChunk(gw)
        );
        
        if (availableBackups.length > 0) {
          const selectedBackup = availableBackups[0];
          console.log(`✅ 選擇備用網關: ${selectedBackup}`);
          return {
            selectedGateway: selectedBackup,
            isUsingBackup: true,
            reason: '主要網關正在進行chunk傳輸'
          };
        } else {
          console.log(`⚠️ 沒有可用備用網關，等待主要網關`);
          return {
            selectedGateway: device.primaryGatewayId,
            isUsingBackup: false,
            reason: '無可用備用網關'
          };
        }
      } else {
        console.log(`✅ 使用主要網關: ${device.primaryGatewayId}`);
        return {
          selectedGateway: device.primaryGatewayId,
          isUsingBackup: false,
          reason: '主要網關空閒'
        };
      }
    } else {
      console.log(`✅ 固定模式，使用主要網關: ${device.primaryGatewayId}`);
      return {
        selectedGateway: device.primaryGatewayId,
        isUsingBackup: false,
        reason: '固定網關模式'
      };
    }
  }

  // 場景1: 主要網關空閒
  console.log('\n🎯 場景1: 主要網關空閒');
  const result1 = simulateSmartSelection(testDevice, false);
  console.log(`結果: ${JSON.stringify(result1, null, 2)}`);

  // 場景2: 主要網關忙碌
  console.log('\n🎯 場景2: 主要網關忙碌');
  const result2 = simulateSmartSelection(testDevice, true);
  console.log(`結果: ${JSON.stringify(result2, null, 2)}`);

  // 場景3: 固定模式設備
  console.log('\n🎯 場景3: 固定模式設備');
  const manualDevice = { ...testDevice, gatewaySelectionMode: 'manual' };
  const result3 = simulateSmartSelection(manualDevice, true);
  console.log(`結果: ${JSON.stringify(result3, null, 2)}`);

  console.log('\n✅ 智能網關選擇功能測試完成');
}

// 測試chunk傳輸狀態管理
function testChunkTransmissionTracking() {
  console.log('\n🧪 測試chunk傳輸狀態管理\n');

  // 這裡我們只能測試公開的API
  console.log('📋 測試可用網關獲取功能');
  
  const testGatewayIds = ['gateway1', 'gateway2', 'gateway3'];
  const available = websocketService.getAvailableGateways(testGatewayIds);
  
  console.log(`測試網關ID: ${testGatewayIds.join(', ')}`);
  console.log(`可用網關: ${available.join(', ')}`);
  
  // 測試忙碌狀態檢查
  console.log('\n📋 測試忙碌狀態檢查');
  testGatewayIds.forEach(gatewayId => {
    const isBusy = websocketService.isGatewayBusyWithChunk(gatewayId);
    console.log(`${gatewayId} 忙碌狀態: ${isBusy}`);
  });
}

// 主測試函數
async function runTests() {
  try {
    console.log('🚀 開始智能網關選擇功能測試套件\n');
    
    await testSmartGatewaySelection();
    testChunkTransmissionTracking();
    
    console.log('\n🎉 所有測試完成！');
    
    // 輸出使用說明
    console.log('\n📖 使用說明:');
    console.log('1. 確保設備的 gatewaySelectionMode 設置為 "auto"');
    console.log('2. 配置設備的 otherGateways 陣列');
    console.log('3. 調用 sendDevicePreviewToGateway() 函數');
    console.log('4. 檢查返回結果中的 actualUsedGateway 和 smartGatewaySelection 字段');
    
  } catch (error) {
    console.error('❌ 測試過程中發生錯誤:', error);
  }
}

// 如果直接運行此腳本
if (require.main === module) {
  runTests();
}

module.exports = {
  testSmartGatewaySelection,
  testChunkTransmissionTracking,
  runTests
};
