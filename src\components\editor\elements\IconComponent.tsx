import React from 'react';
import { 
  Star, 
  Heart, 
  Square, 
  Circle, 
  Triangle, 
  AlertCircle, 
  CheckCircle, 
  Info, 
  XCircle, 
  ArrowUp, 
  ArrowDown, 
  ArrowLeft, 
  ArrowRight,
  ShoppingCart,
  Truck,
  Package,
  Home,
  User,
  Mail,
  Phone,
  Calendar,
  Clock,
  Settings,
  Bookmark,
  Bell,
  Camera
} from 'lucide-react';

// 圖標類型定義
export type IconType = 
  | 'star' 
  | 'heart' 
  | 'square' 
  | 'circle' 
  | 'triangle' 
  | 'alert-circle'
  | 'check-circle'
  | 'info'
  | 'x-circle'
  | 'arrow-up'
  | 'arrow-down'
  | 'arrow-left'
  | 'arrow-right'
  | 'shopping-cart'
  | 'truck'
  | 'package'
  | 'home'
  | 'user'
  | 'mail'
  | 'phone'
  | 'calendar'
  | 'clock'
  | 'settings'
  | 'bookmark'
  | 'bell'
  | 'camera';

interface IconProps {
  iconType: IconType;
  size?: number;
  color?: string;
  strokeWidth?: number;
}

// 圖標組件映射表
const iconMap: Record<IconType, React.ElementType> = {
  'star': Star,
  'heart': Heart,
  'square': Square,
  'circle': Circle,
  'triangle': Triangle,
  'alert-circle': AlertCircle,
  'check-circle': CheckCircle,
  'info': Info,
  'x-circle': XCircle,
  'arrow-up': ArrowUp,
  'arrow-down': ArrowDown,
  'arrow-left': ArrowLeft,
  'arrow-right': ArrowRight,
  'shopping-cart': ShoppingCart,
  'truck': Truck,
  'package': Package,
  'home': Home,
  'user': User,
  'mail': Mail,
  'phone': Phone,
  'calendar': Calendar,
  'clock': Clock,
  'settings': Settings,
  'bookmark': Bookmark,
  'bell': Bell,
  'camera': Camera
};

// 圖標組件
export const Icon: React.FC<IconProps> = ({ 
  iconType = 'star', 
  size = 24, 
  color = 'currentColor',
  strokeWidth = 2
}) => {
  const IconComponent = iconMap[iconType] || Star;

  return (
    <IconComponent 
      size={size} 
      color={color} 
      strokeWidth={strokeWidth}
    />
  );
};

// 獲取所有可用圖標類型
export const getAllIconTypes = (): IconType[] => {
  return Object.keys(iconMap) as IconType[];
};

// 獲取圖標元素
export const getIconElement = (iconType: IconType, props: any) => {
  const IconComponent = iconMap[iconType] || Star;
  return <IconComponent {...props} />;
};