const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// JWT 密鑰，應該存儲在環境變量中
const JWT_SECRET = process.env.JWT_SECRET || 'epd-manager-jwt-secret-key';
// Token 過期時間
const TOKEN_EXPIRES_IN = '24h';

/**
 * 生成 JWT token
 * @param {Object} payload - Token 負載
 * @returns {string} - JWT token
 */
const generateToken = (payload) => {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: TOKEN_EXPIRES_IN });
};

/**
 * 驗證 JWT token
 * @param {string} token - JWT token
 * @returns {Object|null} - 解碼後的 payload 或 null
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
};

/**
 * 加密密碼
 * @param {string} password - 原始密碼
 * @returns {string} - 加密後的密碼
 */
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
};

/**
 * 比較密碼
 * @param {string} password - 原始密碼
 * @param {string} hashedPassword - 加密後的密碼
 * @returns {boolean} - 密碼是否匹配
 */
const comparePassword = async (password, hashedPassword) => {
  return await bcrypt.compare(password, hashedPassword);
};

/**
 * 從請求中獲取 token
 * @param {Object} req - Express 請求對象
 * @returns {string|null} - JWT token 或 null
 */
const getTokenFromRequest = (req) => {
  // 從 Authorization 頭部獲取
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // 從 cookie 獲取
  if (req.cookies && req.cookies.token) {
    return req.cookies.token;
  }
  
  return null;
};

module.exports = {
  generateToken,
  verifyToken,
  hashPassword,
  comparePassword,
  getTokenFromRequest
};
