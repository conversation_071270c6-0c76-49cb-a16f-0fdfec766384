EPD Manager Lite - Release Package
===================================

版本: 1.0.2
建置日期: 2025-06-19
Docker 版本: 20.10+
Docker Compose 版本: 1.29+

包含組件:
- EPD Manager 前端 (React + TypeScript)
- EPD Manager 後端 (Node.js + Express)
- MongoDB 7.x 資料庫
- 單一鏡像檔案 (epd-manager-all.tar)

支援平台:
- Windows 10/11 (Docker Desktop)
- Linux (Ubuntu 18.04+, CentOS 7+, Debian 9+)
- macOS (Docker Desktop)

系統需求:
- CPU: 2核心以上
- 記憶體: 4GB以上
- 硬碟: 10GB可用空間
- 網路: 需要網際網路連接（首次部署）

預設端口:
- 前端: 5173
- 後端: 3001
- MongoDB: 27017

安全提醒:
- 請務必修改預設的 JWT_SECRET
- 首次訪問時請設置強管理員密碼
- 生產環境建議修改預設端口

技術支援:
- 查看 README.md 獲取詳細部署說明
- 使用 check-status 腳本檢查服務狀態
- 查看 docker-compose logs 獲取詳細日誌
