import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, AlertCircle } from 'lucide-react';
import { createGateway } from '../utils/api/gatewayApi';

interface AddGatewayModalProps {
  isOpen: boolean;
  storeId: string; // 當前選中的門店ID
  onClose: () => void;
  onSuccess: () => void;
}

export const AddGatewayModal: React.FC<AddGatewayModalProps> = ({ isOpen, storeId, onClose, onSuccess }) => {
  const { t } = useTranslation();
  const [name, setName] = useState('');
  const [macAddress, setMacAddress] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // MAC 地址格式驗證
  const isValidMacAddress = (mac: string) => {
    // 允許 XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX 格式
    const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
    return macRegex.test(mac);
  };

  // 處理表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 驗證表單
    if (!name.trim()) {
      setError(t('gateways.nameRequired'));
      return;
    }

    if (!macAddress.trim()) {
      setError(t('gateways.macAddressRequired'));
      return;
    }

    if (!isValidMacAddress(macAddress)) {
      setError(t('gateways.invalidMacAddress'));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // 創建新網關
      await createGateway({
        name,
        macAddress,
        model: 'Unknown', // 使用預設值
        ipAddress: '', // 使用預設值
        status: 'offline',
        lastSeen: null,
        wifiFirmwareVersion: '',
        btFirmwareVersion: '',
        storeId, // 添加門店ID
      });

      // 重置表單
      setName('');
      setMacAddress('');

      // 通知成功
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('新增網關失敗:', err);
      setError(err.message || t('gateways.addFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 如果模態窗口未開啟，不渲染任何內容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">{t('gateways.addGateway')}</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          {/* 錯誤提示 */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700 flex items-center">
              <AlertCircle className="mr-2" size={20} />
              <span>{error}</span>
            </div>
          )}

          {/* 網關名稱 */}
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              {t('gateways.name')} <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={t('gateways.namePlaceholder')}
              required
            />
          </div>

          {/* MAC 地址 */}
          <div className="mb-4">
            <label htmlFor="macAddress" className="block text-sm font-medium text-gray-700 mb-1">
              {t('gateways.macAddress')} <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="macAddress"
              value={macAddress}
              onChange={(e) => setMacAddress(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="XX:XX:XX:XX:XX:XX"
              required
            />
            <p className="mt-1 text-xs text-gray-500">
              {t('gateways.macAddressFormat')}
            </p>
          </div>

          {/* 按鈕 */}
          <div className="flex justify-end gap-2 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={loading}
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-75 disabled:cursor-not-allowed"
              disabled={loading}
            >
              {loading ? t('common.processing') : t('common.confirm')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
