<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gray16 16階灰度轉換測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .canvas-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 15px;
        }
        .canvas-item {
            text-align: center;
        }
        .canvas-item canvas {
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .canvas-item p {
            margin: 10px 0 0 0;
            font-size: 14px;
            color: #666;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .controls button {
            padding: 8px 16px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .controls button:hover {
            background: #005a87;
        }
        .gray-levels {
            display: flex;
            gap: 5px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        .gray-level {
            width: 30px;
            height: 30px;
            border: 1px solid #ccc;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }
        .controls label {
            margin-right: 10px;
            font-weight: bold;
        }
        .controls input[type="range"] {
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gray16 16階灰度轉換測試</h1>
        <p>測試 Gray16 電子紙的 16 階灰度轉換效果</p>

        <div class="controls">
            <button onclick="createTestImage()">創建測試圖像</button>
            <button onclick="testGray16Conversion()">測試 Gray16 轉換</button>
            <button onclick="testDifferentLevels()">測試不同灰度級別</button>
            <br><br>
            <label for="levelSlider">灰度級別:</label>
            <input type="range" id="levelSlider" min="2" max="32" value="16" oninput="updateLevelDisplay()">
            <span id="levelDisplay">16</span>
            <button onclick="testCustomLevels()">測試自定義級別</button>
        </div>

        <div class="test-section">
            <h3>16階灰度色階</h3>
            <div class="gray-levels" id="grayLevels">
                <!-- 灰度級別將在這裡顯示 -->
            </div>
        </div>

        <div class="test-section">
            <h3>原始測試圖像</h3>
            <div class="canvas-container">
                <div class="canvas-item">
                    <canvas id="originalCanvas" width="300" height="200"></canvas>
                    <p>原始彩色圖像</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>轉換結果</h3>
            <div class="canvas-container" id="resultsContainer">
                <!-- 轉換結果將在這裡顯示 -->
            </div>
        </div>
    </div>

    <script>
        // 模擬 Gray16 轉換算法
        function convertToGray16(sourceCanvas, levels = 16) {
            const newCanvas = document.createElement('canvas');
            newCanvas.width = sourceCanvas.width;
            newCanvas.height = sourceCanvas.height;
            const ctx = newCanvas.getContext('2d');
            
            // 複製原始圖像
            ctx.drawImage(sourceCanvas, 0, 0);
            
            const imageData = ctx.getImageData(0, 0, newCanvas.width, newCanvas.height);
            const data = imageData.data;
            
            // 計算每個灰度級別的步長
            const step = 255 / (levels - 1);
            
            // 統計使用的灰度級別
            const usedLevels = new Set();
            
            for (let i = 0; i < data.length; i += 4) {
                // 使用標準灰度轉換公式
                const grayscale = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                
                // 量化到指定的灰度級別
                const level = Math.round(grayscale / step);
                const quantizedGray = Math.min(255, level * step);
                
                data[i] = data[i + 1] = data[i + 2] = quantizedGray;
                usedLevels.add(quantizedGray);
            }
            
            ctx.putImageData(imageData, 0, 0);
            
            console.log(`Gray${levels} 轉換完成，實際使用了 ${usedLevels.size} 個灰度級別:`, Array.from(usedLevels).sort((a, b) => a - b));
            
            return newCanvas;
        }

        function createTestImage() {
            const canvas = document.getElementById('originalCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清除畫布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 創建一個包含各種灰度的測試圖像
            
            // 1. 水平漸變（從黑到白）
            const gradient1 = ctx.createLinearGradient(0, 0, canvas.width, 0);
            gradient1.addColorStop(0, '#000000');
            gradient1.addColorStop(1, '#ffffff');
            ctx.fillStyle = gradient1;
            ctx.fillRect(0, 0, canvas.width, 50);
            
            // 2. 彩色漸變
            const gradient2 = ctx.createLinearGradient(0, 50, canvas.width, 50);
            gradient2.addColorStop(0, '#ff0000');
            gradient2.addColorStop(0.33, '#00ff00');
            gradient2.addColorStop(0.66, '#0000ff');
            gradient2.addColorStop(1, '#ffff00');
            ctx.fillStyle = gradient2;
            ctx.fillRect(0, 50, canvas.width, 50);
            
            // 3. 灰度色塊
            const grayLevels = [0, 17, 34, 51, 68, 85, 102, 119, 136, 153, 170, 187, 204, 221, 238, 255];
            const blockWidth = canvas.width / grayLevels.length;
            
            grayLevels.forEach((gray, index) => {
                ctx.fillStyle = `rgb(${gray}, ${gray}, ${gray})`;
                ctx.fillRect(index * blockWidth, 100, blockWidth, 50);
            });
            
            // 4. 複雜圖像區域
            ctx.fillStyle = '#ff8080';
            ctx.fillRect(10, 160, 60, 30);
            
            ctx.fillStyle = '#80ff80';
            ctx.fillRect(80, 160, 60, 30);
            
            ctx.fillStyle = '#8080ff';
            ctx.fillRect(150, 160, 60, 30);
            
            ctx.fillStyle = '#ffff80';
            ctx.fillRect(220, 160, 60, 30);
            
            console.log('測試圖像創建完成');
        }

        function displayGrayLevels(levels = 16) {
            const container = document.getElementById('grayLevels');
            container.innerHTML = '';
            
            const step = 255 / (levels - 1);
            
            for (let i = 0; i < levels; i++) {
                const grayValue = Math.round(i * step);
                const div = document.createElement('div');
                div.className = 'gray-level';
                div.style.backgroundColor = `rgb(${grayValue}, ${grayValue}, ${grayValue})`;
                div.style.color = grayValue > 128 ? 'black' : 'white';
                div.textContent = grayValue;
                div.title = `級別 ${i}: RGB(${grayValue}, ${grayValue}, ${grayValue})`;
                container.appendChild(div);
            }
        }

        function testGray16Conversion() {
            const originalCanvas = document.getElementById('originalCanvas');
            if (!originalCanvas.getContext('2d').getImageData(0, 0, 1, 1)) {
                alert('請先創建測試圖像');
                return;
            }
            
            const convertedCanvas = convertToGray16(originalCanvas, 16);
            displayResult(convertedCanvas, 'Gray16 (16階灰度)');
        }

        function testDifferentLevels() {
            const originalCanvas = document.getElementById('originalCanvas');
            if (!originalCanvas.getContext('2d').getImageData(0, 0, 1, 1)) {
                alert('請先創建測試圖像');
                return;
            }
            
            // 清除之前的結果
            document.getElementById('resultsContainer').innerHTML = '';
            
            // 測試不同的灰度級別
            const levelTests = [2, 4, 8, 16, 32];
            
            levelTests.forEach(levels => {
                const convertedCanvas = convertToGray16(originalCanvas, levels);
                displayResult(convertedCanvas, `Gray${levels} (${levels}階灰度)`);
            });
        }

        function testCustomLevels() {
            const originalCanvas = document.getElementById('originalCanvas');
            const levels = parseInt(document.getElementById('levelSlider').value);
            
            if (!originalCanvas.getContext('2d').getImageData(0, 0, 1, 1)) {
                alert('請先創建測試圖像');
                return;
            }
            
            const convertedCanvas = convertToGray16(originalCanvas, levels);
            displayResult(convertedCanvas, `Gray${levels} (${levels}階灰度)`);
            
            // 更新灰度色階顯示
            displayGrayLevels(levels);
        }

        function updateLevelDisplay() {
            const slider = document.getElementById('levelSlider');
            const display = document.getElementById('levelDisplay');
            display.textContent = slider.value;
            
            // 即時更新灰度色階顯示
            displayGrayLevels(parseInt(slider.value));
        }

        function displayResult(canvas, title) {
            const resultsContainer = document.getElementById('resultsContainer');
            
            const canvasItem = document.createElement('div');
            canvasItem.className = 'canvas-item';
            
            const clonedCanvas = document.createElement('canvas');
            clonedCanvas.width = canvas.width;
            clonedCanvas.height = canvas.height;
            clonedCanvas.style.border = '1px solid #ccc';
            clonedCanvas.style.borderRadius = '4px';
            
            const ctx = clonedCanvas.getContext('2d');
            ctx.drawImage(canvas, 0, 0);
            
            const description = document.createElement('p');
            description.textContent = title;
            
            canvasItem.appendChild(clonedCanvas);
            canvasItem.appendChild(description);
            resultsContainer.appendChild(canvasItem);
        }

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            createTestImage();
            displayGrayLevels(16);
            console.log('Gray16 轉換測試頁面初始化完成');
        });
    </script>
</body>
</html>
