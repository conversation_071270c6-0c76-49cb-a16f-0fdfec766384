# WebSocket 安全增強：MAC 地址不匹配強制中斷連線

## 安全問題描述

當網關發送的 `gatewayInfo` 消息中的 MAC 地址與 JWT token 中的 MAC 地址不匹配時，這可能表示：

1. **身份偽造攻擊**：惡意客戶端嘗試冒充其他網關
2. **配置錯誤**：網關配置不正確或使用了錯誤的 token
3. **中間人攻擊**：可能存在網絡攻擊

## 原有處理方式的問題

**之前的處理方式**：
- 僅拒絕更新網關信息
- 發送錯誤回應但保持連線
- 允許其他操作繼續進行

**安全風險**：
- 惡意客戶端可以繼續發送設備狀態等消息
- 可能造成數據污染或其他安全問題
- 無法有效阻止潛在的攻擊行為

## 新的安全處理機制

### 🔒 **強制中斷連線**

當檢測到 MAC 地址不匹配時：

1. **記錄安全事件**：
   ```javascript
   await logGatewayEvent(gatewayId, 'security-violation', {
     type: 'mac_address_mismatch',
     reportedMac: info.macAddress,
     tokenMac: tokenMacAddress,
     clientIP: ws.clientIP || 'unknown',
     timestamp: new Date().toISOString()
   });
   ```

2. **發送致命錯誤回應**：
   ```javascript
   ws.send(JSON.stringify({
     type: 'gatewayInfoAck',
     timestamp: Date.now(),
     success: false,
     message: 'MAC地址不匹配，連線已中斷',
     fatal: true  // 標記為致命錯誤
   }));
   ```

3. **強制終止連線**：
   ```javascript
   setTimeout(() => {
     ws.terminate();
   }, 100); // 給一點時間讓錯誤消息發送出去
   ```

### 📊 **安全事件記錄**

- **事件類型**：`security-violation`
- **子類型**：`mac_address_mismatch`
- **記錄信息**：
  - 報告的 MAC 地址
  - Token 中的 MAC 地址
  - 客戶端 IP 地址
  - 時間戳
  - 網關 ID 和名稱

### 🚨 **日誌警告**

安全事件會觸發特殊的警告日誌：
```
安全警告: 網關 [gatewayId] 發送的MAC地址 ([reportedMac]) 與token中的MAC地址 ([tokenMac]) 不匹配，強制中斷連線
```

## 客戶端處理

測試客戶端已更新以處理致命錯誤：

```javascript
if (message.type === 'gatewayInfoAck') {
  if (message.success === false && message.fatal === true) {
    console.error('嚴重錯誤: 服務器因安全問題強制中斷連線');
    console.error('錯誤原因:', message.message);
    console.error('連線將被終止，請檢查網關配置');
  }
}
```

## 安全效益

### ✅ **立即阻止威脅**
- 防止惡意客戶端繼續操作
- 避免數據污染和系統破壞

### ✅ **完整的審計追蹤**
- 記錄所有安全違規事件
- 提供詳細的攻擊信息用於分析

### ✅ **快速響應機制**
- 100ms 內終止可疑連線
- 最小化潛在損害

### ✅ **清晰的錯誤反饋**
- 明確告知客戶端問題原因
- 區分致命錯誤和一般錯誤

## 部署注意事項

1. **確保正確配置**：
   - 網關 MAC 地址必須與 server 端記錄一致
   - JWT token 必須包含正確的 MAC 地址

2. **監控安全事件**：
   - 定期檢查 `gatewayEvents` 集合中的 `security-violation` 事件
   - 設置告警機制監控異常活動

3. **測試驗證**：
   - 使用測試腳本驗證正常流程
   - 模擬 MAC 地址不匹配情況測試安全機制

## 向後兼容性

- 現有的正常網關不受影響
- 只有發送不匹配 MAC 地址的連線會被中斷
- 保持所有現有的 API 和消息格式

這個安全增強確保了系統能夠快速識別和阻止潛在的安全威脅，同時保持對合法網關的正常服務。
