#!/bin/bash

# EPD Manager 停止工具 (Linux/macOS)
echo "========================================"
echo "EPD Manager 停止工具 (Linux/macOS)"
echo "========================================"
echo

# 檢查Docker是否運行
if ! docker info &> /dev/null; then
    echo "[錯誤] Docker 未運行，請先啟動 Docker 服務"
    exit 1
fi

# 檢查docker-compose是否安裝
if ! command -v docker-compose &> /dev/null; then
    echo "[錯誤] docker-compose 未安裝"
    exit 1
fi

# 檢查docker-compose.yml是否存在
if [ ! -f "docker-compose.yml" ]; then
    echo "[錯誤] 找不到 docker-compose.yml 檔案"
    echo "請在包含 docker-compose.yml 的目錄中執行此腳本"
    exit 1
fi

echo "[信息] 正在停止 EPD Manager 服務..."
docker-compose down

if [ $? -ne 0 ]; then
    echo "[錯誤] 停止服務失敗"
    exit 1
fi

echo
echo "[成功] EPD Manager 服務已停止"
echo

# 詢問是否清理資料
read -p "是否要清理所有資料（包括資料庫）？這將刪除所有資料！(y/N): " cleanup
if [[ $cleanup =~ ^[Yy]$ ]]; then
    echo "[警告] 正在清理所有資料..."
    docker-compose down -v
    echo "[完成] 所有資料已清理"
else
    echo "[信息] 資料已保留，下次啟動時將恢復"
fi

echo
echo "管理命令:"
echo "  重新啟動: docker-compose up -d"
echo "  查看狀態: docker-compose ps"
echo "  查看日誌: docker-compose logs -f"
echo
