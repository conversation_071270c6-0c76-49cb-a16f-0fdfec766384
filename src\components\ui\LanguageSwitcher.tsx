import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';

interface LanguageSwitcherProps {
  className?: string;
}

const LanguageSwitcher = ({ className = '' }: LanguageSwitcherProps) => {
  const { i18n, t } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language || 'zh-TW');
  const [isOpen, setIsOpen] = useState(false);

  // 監聽語言變化
  useEffect(() => {
    const handleLanguageChanged = (lng: string) => {
      console.log('語言已變更為:', lng);
      setCurrentLanguage(lng);
    };

    // 添加語言變更事件監聽
    i18n.on('languageChanged', handleLanguageChanged);

    // 清除函數
    return () => {
      i18n.off('languageChanged', handleLanguageChanged);
    };
  }, [i18n]);

  const changeLanguage = (lang: string) => {
    console.log('切換語言:', lang);
    try {
      // 在切換語言前輸出目前的語言資源
      console.log('切換前的語言資源:', i18n.options.resources);
      console.log('切換前的語言:', i18n.language);

      // 切換語言
      i18n.changeLanguage(lang);

      // 在切換語言後輸出新的語言
      console.log('切換後的語言:', i18n.language);

      // 測試翻譯是否正確
      console.log('測試翻譯 - 添加:', i18n.t('common.add'));
      console.log('測試翻譯 - 編輯:', i18n.t('common.edit'));

      // 保存用戶的語言偏好
      localStorage.setItem('i18nextLng', lang);
      console.log('已將語言設定保存到 localStorage:', lang);

      // 不再強制刷新頁面，而是直接更新當前語言狀態
      setCurrentLanguage(lang);
      console.log('語言已切換為:', lang);
    } catch (error) {
      console.error('切換語言時出錯:', error);
    }
    setIsOpen(false);
  };

  // 獲取當前語言顯示文字
  const getCurrentLanguageDisplay = () => {
    console.log('當前語言代碼:', currentLanguage);
    switch(currentLanguage) {
      case 'zh-TW': return '中文';
      case 'ja': return '日本語';
      case 'en': return 'English';
      default:
        console.log('未識別的語言代碼:', currentLanguage, '預設顯示為英文');
        return 'English';
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`px-3 py-1 rounded border border-gray-300 hover:bg-gray-100 ${className}`}
      >
        {getCurrentLanguageDisplay()}
      </button>

      {isOpen && (
        <div className="absolute right-0 bottom-full mb-1 bg-white border border-gray-300 rounded shadow-lg z-50">
          <button
            onClick={() => changeLanguage('en')}
            className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-black"
          >
            English
          </button>
          <button
            onClick={() => changeLanguage('zh-TW')}
            className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-black"
          >
            中文
          </button>
          <button
            onClick={() => changeLanguage('ja')}
            className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-black"
          >
            日本語
          </button>
        </div>
      )}
    </div>
  );
};

// 添加默認導出以兼容現有引用
export default LanguageSwitcher;
// 保留命名導出以便將來使用
export { LanguageSwitcher };
