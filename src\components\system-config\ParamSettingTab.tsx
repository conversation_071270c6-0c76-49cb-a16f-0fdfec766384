import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { bindingCore } from '../../utils/dataBinding/bindingCore';
import { getSysConfig, updateSysConfig } from '../../utils/api/sysConfigApi';
import { AlertCircle } from 'lucide-react';

export function ParamSettingTab() {
  const { t } = useTranslation();
  const [timeout, setTimeout] = useState<number>(5000);
  const [retryCount, setRetryCount] = useState<number>(3);
  const [bufferSize, setBufferSize] = useState<number>(1024);
  const [maxBindingDataCount, setMaxBindingDataCount] = useState<number>(8);
  const [gatewayConcurrency, setGatewayConcurrency] = useState<number>(20);
  const [maxQueueCycles, setMaxQueueCycles] = useState<number>(100);
  const [maxWaitCycles, setMaxWaitCycles] = useState<number>(10);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 加載系統配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setIsLoading(true);
        const config = await getSysConfig();
        if (config) {
          setTimeout(config.timeout || 5000);
          setRetryCount(config.retryCount || 3);
          setBufferSize(config.bufferSize || 1024);
          setMaxBindingDataCount(config.maxBindingDataCount || 8);
          setGatewayConcurrency(config.gatewayConcurrency || 20);
          setMaxQueueCycles(config.maxQueueCycles || 100);
          setMaxWaitCycles(config.maxWaitCycles || 10);
        }
      } catch (err) {
        console.error('載入系統配置失敗:', err);
        setError('載入系統配置失敗，請重試');
      } finally {
        setIsLoading(false);
      }
    };

    loadConfig();
  }, []);

  // 保存系統配置
  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);
      setSuccess(null);

      await updateSysConfig({
        timeout,
        retryCount,
        bufferSize,
        maxBindingDataCount,
        gatewayConcurrency,
        maxQueueCycles,
        maxWaitCycles
      });

      // 同時更新綁定核心的最大綁定數量
      bindingCore.setMaxBindingDataCount(maxBindingDataCount);

      setSuccess('參數設定已保存');
      window.setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('保存系統配置失敗:', err);
      setError('保存系統配置失敗，請重試');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 頁面標題和描述 */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">系統參數設定</h2>
        <p className="text-gray-600">配置系統運行的核心參數，包括連接設定、資源管理和併發控制等。</p>
      </div>

      {/* 錯誤提示 */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg flex items-center shadow-sm">
          <AlertCircle className="w-5 h-5 mr-3 text-red-500" />
          <span className="flex-1">{error}</span>
          <button
            onClick={() => setError(null)}
            className="ml-3 text-red-500 hover:text-red-700 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      )}

      {/* 成功提示 */}
      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 text-green-700 rounded-lg flex items-center shadow-sm">
          <svg className="w-5 h-5 mr-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <span>{success}</span>
        </div>
      )}

      <div className="space-y-8">
        {/* 基礎連接設定 */}
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <div className="flex items-center mb-4">
            <div className="w-2 h-6 bg-blue-500 rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold text-gray-800">基礎連接設定</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{t('systemConfig.connectionTimeout')}</label>
              <input
                type="number"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                placeholder="5000"
                min={1000}
                max={60000}
                value={timeout}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 5000;
                  const clampedValue = Math.min(Math.max(value, 1000), 60000);
                  setTimeout(clampedValue);
                }}
                disabled={isLoading || isSaving}
              />
              <p className="mt-1 text-xs text-gray-500">連接逾時時間，單位毫秒 (可設定範圍: 1000-60000)</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{t('systemConfig.retryCount')}</label>
              <input
                type="number"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                placeholder="3"
                min={1}
                max={10}
                value={retryCount}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 3;
                  const clampedValue = Math.min(Math.max(value, 1), 10);
                  setRetryCount(clampedValue);
                }}
                disabled={isLoading || isSaving}
              />
              <p className="mt-1 text-xs text-gray-500">連接重試次數 (可設定範圍: 1-10)</p>
            </div>
          </div>
        </div>

        {/* 系統資源設定 */}
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <div className="flex items-center mb-4">
            <div className="w-2 h-6 bg-green-500 rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold text-gray-800">系統資源設定</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{t('systemConfig.bufferSize')}</label>
              <input
                type="number"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                placeholder="1024"
                min={256}
                max={8192}
                value={bufferSize}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 1024;
                  const clampedValue = Math.min(Math.max(value, 256), 8192);
                  setBufferSize(clampedValue);
                }}
                disabled={isLoading || isSaving}
              />
              <p className="mt-1 text-xs text-gray-500">緩衝區大小，單位KB (可設定範圍: 256-8192)</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">{t('systemConfig.maxBindingDataCount')}</label>
              <input
                type="number"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                placeholder="8"
                min={1}
                max={20}
                value={maxBindingDataCount}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 8;
                  const clampedValue = Math.min(Math.max(value, 1), 20);
                  setMaxBindingDataCount(clampedValue);
                }}
                disabled={isLoading || isSaving}
              />
              <p className="mt-1 text-xs text-gray-500">最大資料綁定數量 (可設定範圍: 1-20)</p>
            </div>
          </div>
        </div>

        {/* Gateway併發控制設定 */}
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <div className="flex items-center mb-4">
            <div className="w-2 h-6 bg-purple-500 rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold text-gray-800">Gateway併發控制設定</h3>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Gateway併發數量</label>
              <input
                type="number"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                placeholder="20"
                min={1}
                max={100}
                value={gatewayConcurrency}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 20;
                  const clampedValue = Math.min(Math.max(value, 1), 100);
                  setGatewayConcurrency(clampedValue);
                }}
                disabled={isLoading || isSaving}
              />
              <p className="mt-1 text-xs text-gray-500">Gateway批量傳輸時的併發數量 (可設定範圍: 1-100)</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">隊列上限數量</label>
              <input
                type="number"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                placeholder="100"
                min={10}
                max={1000}
                value={maxQueueCycles}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 100;
                  const clampedValue = Math.min(Math.max(value, 10), 1000);
                  setMaxQueueCycles(clampedValue);
                }}
                disabled={isLoading || isSaving}
              />
              <p className="mt-1 text-xs text-gray-500">併發發送隊列的最大數量 (可設定範圍: 10-1000)</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">最大等待循環次數</label>
              <input
                type="number"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                placeholder="10"
                min={1}
                max={100}
                value={maxWaitCycles}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 10;
                  const clampedValue = Math.min(Math.max(value, 1), 100);
                  setMaxWaitCycles(clampedValue);
                }}
                disabled={isLoading || isSaving}
              />
              <p className="mt-1 text-xs text-gray-500">等待網關可用的最大循環次數 (可設定範圍: 1-100)</p>
            </div>
          </div>
        </div>

        {/* 操作按鈕區域 */}
        <div className="flex justify-end pt-6 border-t border-gray-200">
          <button
            onClick={handleSave}
            disabled={isLoading || isSaving}
            className={`px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg font-medium shadow-md hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 ${(isLoading || isSaving) ? 'opacity-50 cursor-not-allowed transform-none' : ''}`}
          >
            {isSaving ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('common.saving')}
              </div>
            ) : (
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                {t('systemConfig.saveSettings')}
              </div>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}