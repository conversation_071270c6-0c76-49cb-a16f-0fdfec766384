# 批量發送功能調試指南

## 問題描述
批量發送功能顯示 "批量發送部分成功：0/4 個設備發送成功"，表示所有設備都發送失敗。

## 已修復的問題

### 1. 數據庫連接問題 ✅
**問題**: `processTask` 函數中直接使用 `db.collection('devices')`，但 `db` 變量未正確初始化。

**修復**:
```javascript
// 修復前
const deviceCollection = db.collection('devices');

// 修復後
const { collection: deviceCollection } = await getDeviceCollection();
```

### 2. 主要網關字段名錯誤 ✅
**問題**: `processTask` 函數中使用了錯誤的字段名 `device.primaryGateway`，正確的字段名應該是 `device.primaryGatewayId`。

**修復**:
```javascript
// 修復前
const primaryGatewayId = device.primaryGateway?.toString() || device.primaryGateway;

// 修復後
const primaryGatewayId = device.primaryGatewayId?.toString() || device.primaryGatewayId;
```

### 3. 錯誤處理改進 ✅
**問題**: 缺乏詳細的錯誤日誌和異常處理。

**修復**:
- 添加了更詳細的日誌記錄
- 改進了異常處理邏輯
- 添加了錯誤堆棧輸出
- 添加了設備字段調試信息

## 調試步驟

### 1. 檢查服務器日誌
查看服務器控制台輸出，尋找以下關鍵信息：

```
🚀 開始批量發送 X 個設備，使用任務隊列機制
🌐 網關 gatewayX: 空閒/忙碌
📋 創建任務隊列，總任務數: X
🔄 任務隊列循環 X，剩餘任務: X，正在處理: X
🤖 設備 deviceX 智能模式 - 檢查主要網關 gatewayX 狀態
🚀 開始處理設備 deviceX，使用網關 gatewayX
📊 任務 deviceX 處理完成，結果: 成功/失敗
```

### 2. 常見錯誤模式

#### 錯誤模式 1: 數據庫連接失敗
```
💥 任務 deviceX 處理異常: 資料庫連接函數尚未初始化
```
**解決方案**: 確保在調用批量發送前正確初始化數據庫連接。

#### 錯誤模式 2: 設備不存在
```
❌ 任務 deviceX 最終失敗: 設備不存在
```
**解決方案**: 檢查設備ID是否正確，設備是否存在於數據庫中。

#### 錯誤模式 3: 主要網關字段問題 ✅ 已修復
```
❌ 任務 deviceX 最終失敗: 設備沒有配置主要網關
🔍 設備 deviceX 調試信息: {
  primaryGatewayId: undefined,
  primaryGateway: '6848f7387a89b5d00e928156',
  deviceKeys: ['_id', 'macAddress', 'primaryGateway', ...]
}
```
**原因**: 代碼中使用了錯誤的字段名 `primaryGateway`，正確的字段名是 `primaryGatewayId`。
**解決方案**: 已修復字段名問題，現在會正確讀取 `primaryGatewayId` 字段。

#### 錯誤模式 4: 網關配置問題
```
❌ 任務 deviceX 最終失敗: 設備沒有配置主要網關
```
**解決方案**: 檢查設備的 `primaryGatewayId` 字段是否正確配置。

#### 錯誤模式 4: 網關離線
```
❌ 設備 deviceX 非智能模式 - 主要網關不可用，任務需要重新排隊
```
**解決方案**: 檢查網關是否在線，WebSocket 連接是否正常。

#### 錯誤模式 5: 預覽圖生成失敗
```
❌ 設備 deviceX 發送失敗: 無法生成預覽圖
```
**解決方案**: 檢查設備的模板和數據綁定配置。

### 3. 檢查清單

#### 數據庫相關
- [ ] 數據庫連接是否正常
- [ ] 設備集合是否存在
- [ ] 設備記錄是否完整
- [ ] 設備ID格式是否正確

#### 網關相關
- [ ] 網關是否在線
- [ ] WebSocket 連接是否正常
- [ ] 網關配置是否正確
- [ ] 設備的 primaryGateway 字段是否設置

#### 設備配置相關
- [ ] 設備是否有模板ID
- [ ] 設備是否有數據綁定
- [ ] 模板是否存在
- [ ] 數據綁定格式是否正確

#### 權限相關
- [ ] 用戶是否有設備操作權限
- [ ] 設備是否屬於用戶的門店

### 4. 調試命令

#### 檢查設備狀態
```javascript
// 在瀏覽器控制台中執行
fetch('/api/devices/DEVICE_ID')
  .then(r => r.json())
  .then(console.log);
```

#### 檢查網關狀態
```javascript
// 在瀏覽器控制台中執行
fetch('/api/gateways')
  .then(r => r.json())
  .then(console.log);
```

#### 檢查 WebSocket 連接
```javascript
// 在瀏覽器控制台中執行
fetch('/api/gateways/stats')
  .then(r => r.json())
  .then(console.log);
```

### 5. 手動測試步驟

1. **單個設備發送測試**
   - 選擇一個設備
   - 點擊單個發送按鈕
   - 觀察是否成功

2. **檢查設備配置**
   - 確認設備有主要網關
   - 確認設備有模板和數據綁定
   - 確認網關在線

3. **檢查批量發送日誌**
   - 打開瀏覽器開發者工具
   - 查看 Network 標籤
   - 執行批量發送
   - 檢查 API 響應

### 6. 常見解決方案

#### 重新啟動服務
```bash
# 重新啟動後端服務
npm run dev
```

#### 重新連接網關
- 重新啟動網關設備
- 檢查網關配置
- 確認 WebSocket 連接

#### 重新配置設備
- 檢查設備的主要網關設置
- 確認設備的模板配置
- 驗證數據綁定設置

### 7. 聯繫支持

如果以上步驟都無法解決問題，請提供以下信息：

1. 完整的服務器日誌
2. 瀏覽器控制台錯誤
3. 設備配置截圖
4. 網關狀態信息
5. 重現問題的具體步驟

## 最新修復狀態

- ✅ 修復了 processTask 函數中的數據庫連接問題
- ✅ 修復了主要網關字段名錯誤（primaryGateway → primaryGatewayId）
- ✅ 添加了詳細的錯誤日誌記錄
- ✅ 改進了異常處理邏輯
- ✅ 增強了任務隊列的調試信息
- ✅ 添加了設備字段調試輸出

## 下一步

如果問題仍然存在，建議：

1. 檢查實際的服務器日誌輸出
2. 驗證設備和網關的配置
3. 測試單個設備發送功能
4. 檢查數據庫連接狀態
