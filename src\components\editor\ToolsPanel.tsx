import React from 'react';
import {
  ChevronDown,
  ChevronRight,
  Image,
  Type,
  QrCode,
  Barcode,
  Star,
  Square,
  Circle,
  Minus,
  Text,
  GripVertical, // 添加拖拽圖標
  Eye,
  EyeOff,
  Lock,
  Unlock
} from 'lucide-react';
import { ToolButton } from './CommonComponents';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { TemplateElement } from '../../types';

interface ToolsPanelProps {
  expandedSections: {
    dynamic: boolean;
    static: boolean;
    layer: boolean;
  };
  toggleSection: (section: 'dynamic' | 'static' | 'layer') => void;
  selectStaticTool: (tool: string) => void;
  selectedTool: string | null;
  localElements: TemplateElement[];
  showElementId: string | null;
  toggleElementIdDisplay: (id: string) => void;
  // 添加新屬性：用於處理圖層順序變化
  onLayerOrderChange: (newElements: TemplateElement[]) => void;
  // 添加選擇元素的屬性
  setSelectedElementId: (id: string | null) => void;
  setSelectedElementIds: (ids: string[]) => void;
  selectedElementId: string | null;
  // 新增：控制元件可見性的函數
  onToggleElementVisibility: (id: string, visible: boolean) => void;
  // 新增：控制全部元件可見性的函數
  onToggleAllVisibility: (visible: boolean) => void;
  // 新增：控制元件鎖定狀態的函數
  onToggleElementLock: (id: string, locked: boolean) => void;
  // 新增：控制全部元件鎖定狀態的函數
  onToggleAllLock: (locked: boolean) => void;
}

// 可拖曳的圖層項組件
interface SortableLayerItemProps {
  element: TemplateElement;
  isSelected: boolean;
  showElementId: string | null;
  toggleElementIdDisplay: (id: string) => void;
  onSelect: (id: string) => void;
  // 添加選擇畫布元素的函數
  setSelectedElementId: (id: string | null) => void;
  setSelectedElementIds: (ids: string[]) => void;
  selectedElementId: string | null;
  // 新增：控制元件可見性的函數
  onToggleVisibility: (id: string, visible: boolean) => void;
  // 新增：控制元件鎖定狀態的函數
  onToggleLock: (id: string, locked: boolean) => void;
}

const SortableLayerItem: React.FC<SortableLayerItemProps> = ({
  element,
  isSelected,
  showElementId,
  toggleElementIdDisplay,
  onSelect,
  setSelectedElementId,
  setSelectedElementIds,
  selectedElementId,
  onToggleVisibility,
  onToggleLock
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: element.id });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    backgroundColor: selectedElementId === element.id ? 'rgba(59, 130, 246, 0.5)' : undefined,
    zIndex: isDragging ? 100 : 1,
  };

  const getElementTypeName = (type: string) => {
    switch(type) {
      case 'text': return '文字';
      case 'multiline-text': return '多行文字';
      case 'rectangle': return '矩形';
      case 'square': return '正方形';
      case 'circle': return '圓形';
      case 'ellipse': return '橢圓';
      case 'line': return '直線';
      case 'image': return '圖片';
      case 'icon': return '圖標';
      case 'qr-code': return 'QR碼';
      case 'barcode': return '條碼';
      default: return type;
    }
  };
  // 添加防止文字選擇的樣式
  const preventTextSelectionStyle = {
    userSelect: 'none' as 'none',
    WebkitUserSelect: 'none' as 'none',
    MozUserSelect: 'none' as 'none',
    msUserSelect: 'none' as 'none',
    cursor: isDragging ? 'grabbing' : 'grab',
    ...style
  };
  // 元件的可見性狀態（預設為 true）
  const isVisible = element.visible !== false;
  // 元件的鎖定狀態（預設為 false）
  const isLocked = element.locked === true;

  return (
    <div
      ref={setNodeRef}
      style={preventTextSelectionStyle}
      className={`text-sm p-1 hover:bg-gray-600 rounded cursor-grab relative flex items-center ${
        selectedElementId === element.id ? 'bg-blue-500/30 text-white' : 'text-gray-300'
      } ${!isVisible ? 'opacity-50' : ''} ${isLocked ? 'border-l-2 border-orange-400' : ''}`}
      onClick={() => {
        // 同時觸發 ID 顯示和元素選擇兩種功能
        onSelect(element.id);

        // 選中畫布上的元素
        setSelectedElementId(element.id);
        setSelectedElementIds([element.id]);
      }}
      onMouseDown={(e) => {
        // 防止拖曳時產生文字選擇
        e.preventDefault();
      }}
      {...attributes}
      {...listeners}
    >
      <div
        className="mr-2 px-1 hover:bg-gray-500 rounded"
      >
        <GripVertical size={14} />
      </div>
      <div className="flex-grow">
        {getElementTypeName(element.type)} ({element.id.slice(0, 5)}...)
      </div>

      {/* 鎖定控制按鈕 */}
      <button
        className="ml-1 p-1 hover:bg-gray-500 rounded transition-colors"
        onClick={(e) => {
          e.stopPropagation(); // 防止觸發父元素的點擊事件
          onToggleLock(element.id, !isLocked);
        }}
        title={isLocked ? '解鎖元件' : '鎖定元件'}
      >
        {isLocked ? (
          <Lock size={14} className="text-orange-400 hover:text-orange-300" />
        ) : (
          <Unlock size={14} className="text-gray-400 hover:text-white" />
        )}
      </button>

      {/* 可見性控制按鈕 */}
      <button
        className="ml-1 p-1 hover:bg-gray-500 rounded transition-colors"
        onClick={(e) => {
          e.stopPropagation(); // 防止觸發父元素的點擊事件
          onToggleVisibility(element.id, !isVisible);
        }}
        title={isVisible ? '隱藏元件' : '顯示元件'}
      >
        {isVisible ? (
          <Eye size={14} className="text-gray-300 hover:text-white" />
        ) : (
          <EyeOff size={14} className="text-gray-500 hover:text-gray-300" />
        )}
      </button>

      {/* 顯示完整元素 ID 的彈出層 */}
      {/*{showElementId === element.id && (
        <div className="absolute z-10 bg-gray-800 border border-gray-600 rounded p-2 left-0 right-0 mt-1 shadow-lg text-xs break-all" style={{ top: '100%' }}>
          <p className="font-semibold mb-1">完整 ID：</p>
          <p>{element.id}</p>
        </div>
      )}*/}
    </div>
  );
};

export const ToolsPanel: React.FC<ToolsPanelProps> = ({
  expandedSections,
  toggleSection,
  showElementId,
  toggleElementIdDisplay,
  selectStaticTool,
  selectedTool,
  localElements,
  onLayerOrderChange,
  setSelectedElementId,
  setSelectedElementIds,
  selectedElementId,
  onToggleElementVisibility,
  onToggleAllVisibility,
  onToggleElementLock,
  onToggleAllLock
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: { distance: 5 },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
    const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // 獲取在圖層面板顯示的反轉後的索引
      const reversedElements = [...localElements].reverse();
      const displayOldIndex = reversedElements.findIndex(element => element.id === active.id);
      const displayNewIndex = reversedElements.findIndex(element => element.id === over.id);

      if (displayOldIndex !== -1 && displayNewIndex !== -1) {
        // 移動反轉後的數組中的元素
        const newReversedElements = arrayMove(reversedElements, displayOldIndex, displayNewIndex);
        // 再反轉回來得到真正的元素順序
        const newElements = [...newReversedElements].reverse();
        // 直接更新元素順序，不需要再次反轉
        onLayerOrderChange(newElements);
      }
    }
  };

  return (
    <div className="w-64 bg-gray-800 text-white flex flex-col h-full">
      <div className="space-y-1 flex flex-col flex-grow">
        {/* 動態元素部分 */}
        <div>
          <button
            onClick={() => toggleSection('dynamic')}
            className="w-full flex items-center gap-2 px-4 py-3 hover:bg-gray-700 transition-colors"
          >
            {expandedSections.dynamic ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
            <span>動態元素</span>
          </button>
          {expandedSections.dynamic && (
            <div className="grid grid-cols-3 gap-2 p-4">
              <ToolButton icon={<Type />} label="單行文字" onClick={() => selectStaticTool('text')} isActive={selectedTool === 'text'} />
              <ToolButton icon={<Text />} label="多行文字" onClick={() => selectStaticTool('multiline-text')} isActive={selectedTool === 'multiline-text'} />
              <ToolButton icon={<Image />} label="圖片" onClick={() => selectStaticTool('image')} isActive={selectedTool === 'image'} />
              <ToolButton icon={<QrCode />} label="QR 碼" onClick={() => selectStaticTool('qr-code')} isActive={selectedTool === 'qr-code'} />
              <ToolButton icon={<Barcode />} label="條碼" onClick={() => selectStaticTool('barcode')} isActive={selectedTool === 'barcode'} />
              <ToolButton icon={<Star />} label="圖標" onClick={() => selectStaticTool('icon')} isActive={selectedTool === 'icon'} />
            </div>
          )}
        </div>

        {/* 靜態元素部分 */}
        <div>
          <button
            onClick={() => toggleSection('static')}
            className="w-full flex items-center gap-2 px-4 py-3 hover:bg-gray-700 transition-colors"
          >
            {expandedSections.static ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
            <span>靜態元素</span>
          </button>
          {expandedSections.static && (
            <div className="grid grid-cols-3 gap-2 p-4">
              <ToolButton icon={<Square />} label="矩形" onClick={() => selectStaticTool('rectangle')} isActive={selectedTool === 'rectangle'} />
              <ToolButton icon={<Square />} label="方形" onClick={() => selectStaticTool('square')} isActive={selectedTool === 'square'} />
              <ToolButton icon={<Circle />} label="圓形" onClick={() => selectStaticTool('circle')} isActive={selectedTool === 'circle'} />
              <ToolButton icon={<Circle />} label="橢圓" onClick={() => selectStaticTool('ellipse')} isActive={selectedTool === 'ellipse'} />
              <ToolButton icon={<Minus />} label="線段" onClick={() => selectStaticTool('line')} isActive={selectedTool === 'line'} />
              <ToolButton icon={<Image />} label="圖片" onClick={() => selectStaticTool('image')} isActive={selectedTool === 'image'} />
              <ToolButton icon={<Star />} label="圖標" onClick={() => selectStaticTool('icon')} isActive={selectedTool === 'icon'} />
            </div>
          )}
        </div>

        {/* 圖層部分 */}
        <div className="flex-grow flex flex-col min-h-0">
          <div className="flex items-center">
            <button
              onClick={() => toggleSection('layer')}
              className="flex-grow flex items-center gap-2 px-4 py-3 hover:bg-gray-700 transition-colors"
            >
              {expandedSections.layer ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
              <span>圖層</span>
            </button>
            {expandedSections.layer && localElements.length > 0 && (
              <div className="flex flex-col gap-1 px-2">
                {/* 可見性控制按鈕組 */}
                <div className="flex gap-1">
                  <button
                    onClick={() => onToggleAllVisibility(true)}
                    className="p-1 hover:bg-gray-500 rounded transition-colors"
                    title="全部顯示"
                  >
                    <Eye size={14} className="text-gray-300 hover:text-white" />
                  </button>
                  <button
                    onClick={() => onToggleAllLock(false)}
                    className="p-1 hover:bg-gray-500 rounded transition-colors"
                    title="全部解鎖"
                  >
                    <Unlock size={14} className="text-gray-400 hover:text-white" />
                  </button>
                </div>
                {/* 鎖定控制按鈕組 */}
                <div className="flex gap-1">
                  <button
                    onClick={() => onToggleAllVisibility(false)}
                    className="p-1 hover:bg-gray-500 rounded transition-colors"
                    title="全部隱藏"
                  >
                    <EyeOff size={14} className="text-gray-300 hover:text-white" />
                  </button>
                  <button
                    onClick={() => onToggleAllLock(true)}
                    className="p-1 hover:bg-gray-500 rounded transition-colors"
                    title="全部鎖定"
                  >
                    <Lock size={14} className="text-orange-400 hover:text-orange-300" />
                  </button>
                </div>
              </div>
            )}
          </div>
          {expandedSections.layer && (
            <div className="p-4 flex-grow flex flex-col min-h-0">
              <div className="rounded p-2 flex-grow flex flex-col min-h-0">
                {localElements.length > 0 ? (
                  <div className="space-y-1 overflow-y-auto overflow-x-hidden pr-1 flex-grow h-full max-h-[calc(100vh-300px)]" style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#4B5563 #1F2937'
                  }}>
                    <DndContext
                      sensors={sensors}
                      collisionDetection={closestCenter}
                      onDragEnd={handleDragEnd}
                    >
                      <SortableContext
                        items={localElements.map(el => el.id)}
                        strategy={verticalListSortingStrategy}
                      >
                        {/* 這裡我們需要反轉元素的順序，因為在畫布中，數組末尾的元素會顯示在最上面 */}                        {[...localElements].reverse().map((element) => (
                          <SortableLayerItem
                            key={element.id}
                            element={element}
                            isSelected={showElementId === element.id}
                            showElementId={showElementId}
                            toggleElementIdDisplay={toggleElementIdDisplay}
                            onSelect={toggleElementIdDisplay}
                            setSelectedElementId={setSelectedElementId}
                            setSelectedElementIds={setSelectedElementIds}
                            selectedElementId={selectedElementId}
                            onToggleVisibility={onToggleElementVisibility}
                            onToggleLock={onToggleElementLock}
                          />
                        ))}
                      </SortableContext>
                    </DndContext>
                  </div>
                ) : (
                  <p className="text-sm text-gray-300">尚未添加任何元素</p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};