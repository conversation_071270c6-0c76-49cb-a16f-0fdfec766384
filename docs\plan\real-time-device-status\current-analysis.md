# 現狀分析

## 1. 現有更新機制

### 1.1 手動刷新機制
- **觸發方式**：用戶點擊"同步設備"按鈕
- **API調用**：`syncDevices(storeId)` 
- **實現位置**：`src/utils/api/deviceApi.ts`
- **更新範圍**：全量刷新當前門店的所有設備狀態

```typescript
// 現有同步機制
const handleSync = async () => {
  try {
    setSyncingDevices(true);
    const success = await syncDevices(store?.id);
    if (success) {
      showNotification('設備狀態同步成功', 'success');
      await fetchDevices(); // 重新獲取設備列表
    }
  } catch (err: any) {
    showNotification(err.message || '設備狀態同步失敗', 'error');
  } finally {
    setSyncingDevices(false);
  }
};
```

### 1.2 後端定時檢查機制
- **檢查頻率**：每10秒檢查一次 (`CHECK_INTERVAL = 10 * 1000`)
- **離線閾值**：30秒未更新標記為離線 (`OFFLINE_THRESHOLD = 30 * 1000`)
- **實現位置**：`server/services/deviceStatusService.js`
- **處理邏輯**：自動將超時設備標記為離線

```javascript
// 現有定時檢查邏輯
const checkAndUpdateDeviceStatus = async () => {
  const now = new Date();
  const threshold = new Date(now.getTime() - OFFLINE_THRESHOLD);
  
  const result = await collection.updateMany(
    {
      status: 'online',
      lastSeen: { $lt: threshold } 
    },
    {
      $set: {
        status: 'offline',
        updatedAt: now
      }
    }
  );
};
```

### 1.3 網關管理更新機制
- **觸發方式**：用戶點擊"同步網關"按鈕
- **API調用**：`syncGateways(storeId)`
- **實現位置**：`src/utils/api/gatewayApi.ts`
- **更新範圍**：全量刷新當前門店的所有網關狀態

```typescript
// 現有網關同步機制
const handleSync = async () => {
  try {
    setSyncingGateways(true);
    const result = await syncGateways(store.id);
    if (result.success && result.gateways) {
      setGateways(result.gateways);
      applyFilters(result.gateways, searchTerm, statusFilter, modelFilter);
    } else {
      fetchGateways(); // 重新獲取網關列表
    }
  } catch (err: any) {
    showNotification(err.message || '同步網關狀態失敗', 'error');
  } finally {
    setSyncingGateways(false);
  }
};
```

### 1.4 WebSocket現有用途
- **主要用途**：批量傳送進度更新 + 網關設備連接管理
- **連接類型**：前端客戶端 + 網關設備
- **實現位置**：
  - 服務端：`server/services/websocketService.js`
  - 客戶端：`src/utils/websocketClient.ts`
- **功能範圍**：批量傳送相關事件 + 網關連接狀態管理

## 2. 問題識別

### 2.1 即時性問題
- **設備狀態延遲**：設備狀態變更到前端顯示有明顯延遲
- **網關狀態延遲**：網關連接/斷開狀態無法即時反映
- **手動依賴**：需要用戶手動點擊同步才能獲取最新狀態
- **數據不一致**：前端顯示狀態可能與數據庫實際狀態不符

### 2.2 用戶體驗問題
- **操作繁瑣**：用戶需要頻繁手動刷新查看最新狀態
- **狀態滯後**：在設備和網關管理場景下，狀態滯後影響操作決策
- **視覺反饋不足**：無法直觀感知設備和網關狀態的實時變化
- **網關連接監控困難**：無法即時知道網關的WebSocket連接狀態

### 2.3 系統架構問題
- **WebSocket利用不足**：現有WebSocket僅用於批量進度，未充分利用即時推送能力
- **推送機制缺失**：缺乏主動推送設備和網關狀態變更的機制
- **更新粒度粗糙**：只能全量刷新，無法精確更新變更的設備或網關
- **網關狀態監控空白**：雖然網關通過WebSocket連接，但前端無法監控連接狀態變化

## 3. 現有WebSocket架構評估

### 3.1 架構優勢
- **雙重連接支持**：同時支援前端客戶端和網關設備連接
- **認證機制完善**：支援JWT token認證和開發模式
- **重連機制健全**：具備自動重連和錯誤處理機制
- **事件處理完整**：已有完整的事件分發和處理機制

### 3.2 可擴展性分析
- **消息類型擴展**：現有架構支援新增消息類型
- **訂閱機制**：已有批量進度訂閱機制，可參考實現設備和網關狀態訂閱
- **廣播能力**：具備向特定客戶端廣播消息的能力
- **門店隔離**：已支援按門店ID進行數據隔離
- **網關連接管理**：已有完整的網關WebSocket連接管理機制

### 3.3 現有事件類型
```typescript
// 現有WebSocket事件類型
interface BatchProgressEvent {
  type: 'batch_progress';
  batchId: string;
  totalDevices: number;
  completedDevices: number;
  // ...
}

interface BatchCompleteEvent {
  type: 'batch_complete';
  batchId: string;
  result: { /* ... */ };
}
```

## 4. 技術債務分析

### 4.1 性能考量
- **頻繁查詢**：手動刷新導致不必要的API調用
- **全量更新**：每次同步都是全量數據傳輸
- **重複渲染**：設備列表可能因為不必要的狀態變更而重複渲染

### 4.2 維護成本
- **代碼重複**：設備狀態更新邏輯分散在多個地方
- **狀態管理複雜**：前端需要手動管理設備狀態的同步
- **測試困難**：手動觸發的更新機制難以進行自動化測試

## 5. 改進機會

### 5.1 即時推送機會
- **WebSocket基礎**：現有WebSocket架構為即時推送提供了基礎
- **事件驅動**：可以在設備和網關狀態變更時主動推送更新
- **精確更新**：可以只推送變更的設備和網關狀態，減少數據傳輸
- **連接狀態監控**：可以即時監控網關WebSocket連接狀態變化

### 5.2 用戶體驗提升
- **自動更新**：無需用戶手動操作即可獲取最新狀態
- **視覺反饋**：可以提供更豐富的狀態變更視覺反饋
- **響應速度**：大幅提升狀態更新的響應速度
- **統一體驗**：設備和網關管理提供一致的即時更新體驗

### 5.3 系統架構優化
- **統一WebSocket**：將設備和網關狀態更新整合到現有WebSocket架構
- **事件統一**：統一管理所有即時事件類型
- **性能優化**：通過精確推送減少不必要的數據傳輸和處理
- **監控增強**：提供網關連接狀態的即時監控能力
