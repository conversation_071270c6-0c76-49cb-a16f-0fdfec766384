/**
 * SVG路徑分析測試 - 詳細分析star和heart的路徑解析問題
 */

const fs = require('fs');
const path = require('path');

/**
 * 測試SVG路徑解析
 */
async function testSvgPathAnalysis() {
  try {
    console.log('=== 開始SVG路徑分析測試 ===\n');

    const { parsePathData } = require('../utils/simpleSvgRenderer');

    // 測試star路徑
    const starPath = "M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z";

    console.log('--- 分析 Star 路徑 ---');
    console.log('原始路徑:', starPath);
    console.log('');

    // 手動分析star路徑的預期命令
    console.log('預期的命令序列:');
    console.log('1. M 11.525 2.295 - 移動到起點');
    console.log('2. a .53 .53 0 0 1 .95 0 - 小弧形');
    console.log('3. l 2.31 4.679 - 相對線條');
    console.log('4. a 2.123 2.123 0 0 0 1.595 1.16 - 小弧形');
    console.log('5. l 5.166 .756 - 相對線條');
    console.log('6. a .53 .53 0 0 1 .294 .904 - 小弧形');
    console.log('7. l -3.736 3.638 - 相對線條');
    console.log('8. a 2.123 2.123 0 0 0 -.611 1.878 - 小弧形');
    console.log('9. l .882 5.14 - 相對線條');
    console.log('10. a .53 .53 0 0 1 -.771 .56 - 小弧形');
    console.log('11. l -4.618 -2.428 - 相對線條');
    console.log('12. a 2.122 2.122 0 0 0 -1.973 0 - 小弧形');
    console.log('13. L 6.396 21.01 - 絕對線條');
    console.log('14. a .53 .53 0 0 1 -.77 -.56 - 小弧形');
    console.log('15. l .881 -5.139 - 相對線條');
    console.log('16. a 2.122 2.122 0 0 0 -.611 -1.879 - 小弧形');
    console.log('17. L 2.16 9.795 - 絕對線條');
    console.log('18. a .53 .53 0 0 1 .294 -.906 - 小弧形');
    console.log('19. l 5.165 -.755 - 相對線條');
    console.log('20. a 2.122 2.122 0 0 0 1.597 -1.16 - 小弧形');
    console.log('21. z - 關閉路徑');
    console.log('');

    // 測試heart路徑
    const heartPath = "M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z";

    console.log('--- 分析 Heart 路徑 ---');
    console.log('原始路徑:', heartPath);
    console.log('');

    console.log('預期的命令序列:');
    console.log('1. M 19 14 - 移動到起點');
    console.log('2. c 1.49 -1.46 3 -3.21 3 -5.5 - 相對三次貝塞爾曲線');
    console.log('3. A 5.5 5.5 0 0 0 16.5 3 - 絕對弧形');
    console.log('4. c -1.76 0 -3 .5 -4.5 2 - 相對三次貝塞爾曲線');
    console.log('5. c -1.5 -1.5 -2.74 -2 -4.5 -2 - 相對三次貝塞爾曲線');
    console.log('6. A 5.5 5.5 0 0 0 2 8.5 - 絕對弧形');
    console.log('7. c 0 2.3 1.5 4.05 3 5.5 - 相對三次貝塞爾曲線');
    console.log('8. l 7 7 - 相對線條');
    console.log('9. Z - 關閉路徑');
    console.log('');

    // 現在測試我們的解析器
    console.log('=== 測試我們的解析器 ===\n');

    console.log('--- Star 路徑解析結果 ---');
    // 由於parsePathData不是導出的，我們需要直接測試
    // 讓我們創建一個簡單的測試
    
    console.log('測試完成');

  } catch (error) {
    console.error('測試過程中發生錯誤:', error);
    console.error('錯誤堆疊:', error.stack);
  }
}

// 執行測試
testSvgPathAnalysis();
