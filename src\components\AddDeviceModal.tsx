import React, { useState } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { createDevice, checkMacAddressExists } from '../utils/api/deviceApi';
import { getScreenConfigs } from '../screens/screenSizeMap';

interface AddDeviceModalProps {
  isOpen: boolean;
  storeId: string; // 當前選中的門店ID
  onClose: () => void;
  onSuccess: () => void;
}

export function AddDeviceModal({ isOpen, storeId, onClose, onSuccess }: AddDeviceModalProps) {
  // 獲取螢幕配置
  const screenConfigs = getScreenConfigs();

  const [formData, setFormData] = useState({
    macAddress: '',
    size: '',
    rssi: -70,
    battery: 100,
    status: 'offline' as 'online' | 'offline',
    dataId: '',
    note: '',
    code: '',
    storeId: storeId, // 添加門店ID
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!isOpen) return null;

  const validateForm = async () => {
    const newErrors: Record<string, string> = {};

    // 驗證 MAC 地址
    if (!formData.macAddress) {
      newErrors.macAddress = 'MAC 地址不能為空';
    } else if (!/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(formData.macAddress)) {
      newErrors.macAddress = 'MAC 地址格式不正確 (例如: 00:1A:2B:3C:4D:5E)';
    } else {
      // 檢查 MAC 地址是否已存在於該門店
      try {
        const exists = await checkMacAddressExists(formData.macAddress, storeId);
        if (exists) {
          newErrors.macAddress = '此 MAC 地址已存在於該門店';
        }
      } catch (error) {
        console.error('檢查 MAC 地址時出錯:', error);
        newErrors.macAddress = '無法驗證 MAC 地址唯一性';
      }
    }

    // 驗證尺寸
    if (!formData.size) {
      newErrors.size = '尺寸不能為空';
    }

    // 驗證 RSSI
    if (formData.rssi < -100 || formData.rssi > 0) {
      newErrors.rssi = 'RSSI 值應在 -100 到 0 之間';
    }

    // 驗證電量
    if (formData.battery < 0 || formData.battery > 100) {
      newErrors.battery = '電量應在 0 到 100 之間';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement & HTMLTextAreaElement;

    // 根據輸入類型處理值
    let processedValue: string | number = value;
    if (type === 'number') {
      processedValue = value === '' ? '' : Number(value);
    }

    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const isValid = await validateForm();
      if (!isValid) {
        setIsSubmitting(false);
        return;
      }

      // 創建設備
      await createDevice({
        ...formData,
        rssi: Number(formData.rssi),
        battery: Number(formData.battery),
        lastSeen: new Date()
      });

      onSuccess();
      onClose();      // 重置表單
      setFormData({
        macAddress: '',
        size: '',
        rssi: -70,
        battery: 100,
        status: 'offline',
        dataId: '',
        note: '',
        code: '',
        storeId: storeId, // 保持門店ID
      });
      setErrors({});
    } catch (error) {
      console.error('新增設備失敗:', error);
      setErrors(prev => ({
        ...prev,
        submit: '新增設備失敗，請稍後再試'
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">新增設備</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        {errors.submit && (
          <div className="p-4 bg-red-50 border-l-4 border-red-500 text-red-700 flex items-center">
            <AlertCircle className="w-5 h-5 mr-2" />
            <span>{errors.submit}</span>
          </div>
        )}

        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            {/* MAC 地址 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                MAC 地址 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="macAddress"
                value={formData.macAddress}
                onChange={handleChange}
                placeholder="例如: 00:1A:2B:3C:4D:5E"
                className={`w-full p-2 border rounded-md ${
                  errors.macAddress ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.macAddress && (
                <p className="mt-1 text-sm text-red-500">{errors.macAddress}</p>
              )}
            </div>

            {/* 尺寸 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                尺寸 <span className="text-red-500">*</span>
              </label>
              <select
                name="size"
                value={formData.size}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md ${
                  errors.size ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">請選擇尺寸</option>
                {screenConfigs.map(config => (
                  <option key={config.id} value={config.name}>
                    {config.displayName}
                  </option>
                ))}
              </select>
              {errors.size && (
                <p className="mt-1 text-sm text-red-500">{errors.size}</p>
              )}
            </div>

            {/* RSSI 值 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                RSSI 值
              </label>
              <input
                type="number"
                name="rssi"
                value={formData.rssi}
                onChange={handleChange}
                min="-100"
                max="0"
                className={`w-full p-2 border rounded-md ${
                  errors.rssi ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.rssi && (
                <p className="mt-1 text-sm text-red-500">{errors.rssi}</p>
              )}
            </div>

            {/* 電量 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                電量 (%)
              </label>
              <input
                type="number"
                name="battery"
                value={formData.battery}
                onChange={handleChange}
                min="0"
                max="100"
                className={`w-full p-2 border rounded-md ${
                  errors.battery ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.battery && (
                <p className="mt-1 text-sm text-red-500">{errors.battery}</p>
              )}
            </div>

            {/* 狀態 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                狀態
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="online">在線</option>
                <option value="offline">離線</option>
              </select>
            </div>            {/* 關聯數據 ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                關聯數據 ID
              </label>
              <input
                type="text"
                name="dataId"
                value={formData.dataId}
                onChange={handleChange}
                placeholder="關聯的數據 ID"
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* 編號 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                編號
              </label>
              <input
                type="text"
                name="code"
                value={formData.code}
                onChange={handleChange}
                placeholder="設備編號"
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* 備註 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                備註
              </label>
              <textarea
                name="note"
                value={formData.note}
                onChange={handleChange}
                placeholder="輸入備註"
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={isSubmitting}
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
              disabled={isSubmitting}
            >
              {isSubmitting ? '處理中...' : '新增'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
