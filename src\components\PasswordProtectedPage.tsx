import React, { useState, useEffect } from 'react';
import { Lock, Eye, EyeOff } from 'lucide-react';

interface PasswordProtectedPageProps {
  children: React.ReactNode;
  requiredPassword?: string;
  title?: string;
  description?: string;
}

export const PasswordProtectedPage: React.FC<PasswordProtectedPageProps> = ({
  children,
  requiredPassword = 'rd123456',
  title = '受保護的頁面',
  description = '請輸入密碼以訪問此頁面'
}) => {
  const [password, setPassword] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [attempts, setAttempts] = useState(0);

  // 檢查是否已經通過驗證（使用sessionStorage）
  useEffect(() => {
    const sessionKey = `password_auth_${title}`;
    const isAuth = sessionStorage.getItem(sessionKey) === 'true';
    setIsAuthenticated(isAuth);
  }, [title]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (password === requiredPassword) {
      setIsAuthenticated(true);
      setError('');
      // 將驗證狀態保存到sessionStorage（僅在當前會話有效）
      const sessionKey = `password_auth_${title}`;
      sessionStorage.setItem(sessionKey, 'true');
    } else {
      setError('密碼錯誤，請重試');
      setAttempts(prev => prev + 1);
      setPassword('');
      
      // 如果嘗試次數過多，可以添加額外的安全措施
      if (attempts >= 4) {
        setError('嘗試次數過多，請稍後再試');
        setTimeout(() => {
          setAttempts(0);
          setError('');
        }, 30000); // 30秒後重置
      }
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    setPassword('');
    setError('');
    setAttempts(0);
    // 清除sessionStorage中的驗證狀態
    const sessionKey = `password_auth_${title}`;
    sessionStorage.removeItem(sessionKey);
  };

  if (isAuthenticated) {
    return (
      <div className="relative">
        {/* 登出按鈕 - 放在右上角但避開其他按鈕的位置 */}
        <div className="absolute top-4 right-4 z-[60]">
          <button
            onClick={handleLogout}
            className="flex items-center space-x-1 px-2 py-1 bg-red-500/90 backdrop-blur-sm text-white rounded-md hover:bg-red-600/90 transition-all duration-200 text-xs shadow-lg border border-red-400/20 ml-2"
            title="登出密碼保護頁面"
          >
            <Lock size={14} />
            <span>登出</span>
          </button>
        </div>
        {/* 為頁面內容添加頂部間距，避免被登出按鈕遮擋 */}
        <div className="pt-12">
          {children}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
        {/* 標題 */}
        <div className="text-center mb-6">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-blue-100 rounded-full">
              <Lock className="text-blue-600" size={24} />
            </div>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
          <p className="text-gray-600">{description}</p>
        </div>

        {/* 密碼輸入表單 */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              密碼
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={attempts >= 5}
                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
                placeholder="請輸入密碼"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                {showPassword ? (
                  <EyeOff className="text-gray-400" size={20} />
                ) : (
                  <Eye className="text-gray-400" size={20} />
                )}
              </button>
            </div>
          </div>

          {/* 錯誤信息 */}
          {error && (
            <div className="text-red-600 text-sm text-center">
              {error}
            </div>
          )}

          {/* 嘗試次數提示 */}
          {attempts > 0 && attempts < 5 && (
            <div className="text-yellow-600 text-sm text-center">
              已嘗試 {attempts} 次，還有 {5 - attempts} 次機會
            </div>
          )}

          {/* 提交按鈕 */}
          <button
            type="submit"
            disabled={!password.trim() || attempts >= 5}
            className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {attempts >= 5 ? '請稍後再試' : '驗證'}
          </button>
        </form>

        {/* 安全提示 */}
        <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-sm text-yellow-800">
            <strong>安全提示：</strong> 請確保您有權限訪問。
          </p>
        </div>
      </div>
    </div>
  );
};
