# EPD 尺寸匹配處理流程說明

## 修改後的處理流程

### 1. 新的邏輯概述
當 canvasSize 跟 deviceSize 長寬相反時，將預覽圖轉90度，使其寬高與設備一致，然後用轉90度的預覽圖來生成EPD資料並補齊寬度倍數。

### 2. 處理步驟

#### 步驟 1: 比較 canvasSize 和 deviceSize
```javascript
const canvasIsLandscape = canvasWidth > canvasHeight;
const deviceIsLandscape = deviceWidth > deviceHeight;
const needsRotation = canvasIsLandscape !== deviceIsLandscape;
```

#### 步驟 2: 決定是否旋轉
- **如果方向一致**: 無需旋轉，直接使用原始預覽圖
- **如果方向相反**: 將預覽圖轉90度以匹配設備方向

#### 步驟 3: 使用處理後的圖片生成EPD資料
```javascript
// 使用旋轉後的尺寸進行寬度對齊
const finalWidth = rotatedImageData.width;
const finalHeight = rotatedImageData.height;
const paddedWidth = calculatePaddedWidth(finalWidth, colorType);
```

#### 步驟 4: 創建 ImageInfo
```javascript
const imageInfo = {
  imagecode: 0x12345678,
  x: 0,
  y: 0,
  width: paddedWidth,  // 使用處理後的對齊寬度
  height: finalHeight  // 使用處理後的高度
};
```

### 3. 不同尺寸匹配的處理示例

| Canvas尺寸 | Device尺寸 | 是否需要旋轉 | 最終尺寸 | BWR對齊寬度 | ImageInfo尺寸 |
|-----------|-----------|------------|---------|------------|-------------|
| 128x296   | 128x296   | 否         | 128x296 | 128        | 128x296     |
| 296x128   | 296x128   | 否         | 296x128 | 296        | 296x128     |
| 128x296   | 296x128   | 是         | 296x128 | 296        | 296x128     |
| 296x128   | 128x296   | 是         | 128x296 | 128        | 128x296     |

### 4. 關鍵改進點

#### 修改前的問題:
- 基於模板旋轉角度進行反向旋轉，邏輯複雜
- 沒有考慮 Canvas 和 Device 尺寸的匹配關係
- 可能產生不必要的旋轉操作

#### 修改後的解決方案:
1. **尺寸匹配優先**: 根據 Canvas 和 Device 的尺寸關係決定是否旋轉
2. **簡化邏輯**: 只在長寬方向不一致時才旋轉90度
3. **動態尺寸計算**: 根據處理後的實際尺寸重新計算對齊寬度
4. **正確的 ImageInfo**: 使用處理後的尺寸填寫 ImageInfo

### 5. 代碼實現要點

#### 前端 (BaseConverter.ts):
```typescript
convert(imageData: ImageData): EPDConversionResult {
  // 1. 記錄原始尺寸
  const originalWidth = imageData.width;
  const originalHeight = imageData.height;

  // 2. 檢查並應用旋轉以匹配設備尺寸
  const rotatedImageData = this.applyRotationForDeviceMatch(imageData);

  // 3. 根據處理後的圖像重新初始化轉換器尺寸和緩衝區
  this.initializeForRotatedImage(rotatedImageData);

  // 4. 創建 ImageInfo (使用處理後的尺寸)
  const imageInfo = this.createImageInfo();

  // imageInfo.width = this.paddedWidth (處理後的對齊寬度)
  // imageInfo.height = this.height (處理後的高度)
}

// 尺寸匹配檢查
protected needsRotationForDeviceMatch(imageData: ImageData): boolean {
  const canvasIsLandscape = imageData.width > imageData.height;
  const deviceIsLandscape = this.options.width > this.options.height;
  return canvasIsLandscape !== deviceIsLandscape;
}

// 應用旋轉以匹配設備尺寸
protected applyRotationForDeviceMatch(imageData: ImageData): ImageData {
  if (!this.needsRotationForDeviceMatch(imageData)) {
    return imageData; // 無需旋轉
  }
  return rotateImageData(imageData, 90); // 旋轉90度
}
```

#### 後端 (epdConversion.js):
```javascript
// 1. 檢查是否需要旋轉以匹配設備尺寸
const needsRotation = needsRotationForDeviceMatch(imageData, options);
let rotatedImageData = imageData;

if (needsRotation) {
  console.log('Canvas與Device長寬相反，將預覽圖轉90度');
  rotatedImageData = rotateImageData(imageData, 90);
} else {
  console.log('Canvas與Device方向一致，無需旋轉');
}

// 2. 使用處理後的尺寸創建轉換器
const rotatedWidth = rotatedImageData.width;
const rotatedHeight = rotatedImageData.height;

const actualOptions = {
  ...options,
  width: rotatedWidth,   // 處理後的寬度
  height: rotatedHeight  // 處理後的高度
};

// 3. 創建 ImageInfo
const imageInfo = {
  imagecode: options.imagecode,
  x: options.x || 0,
  y: options.y || 0,
  width: converter.paddedWidth,  // 處理後的對齊寬度
  height: converter.height       // 處理後的高度
};

// 尺寸匹配檢查函數
function needsRotationForDeviceMatch(imageData, options) {
  const canvasIsLandscape = imageData.width > imageData.height;
  const deviceIsLandscape = options.width > options.height;
  return canvasIsLandscape !== deviceIsLandscape;
}
```

### 6. 驗證方法

可以通過以下方式驗證修改是否正確:

1. **檢查日誌輸出**: 查看 "Canvas與Device方向一致" 或 "長寬相反，將預覽圖轉90度" 的日誌
2. **驗證 ImageInfo 尺寸**: 確保 ImageInfo 中的寬高是處理後的正確值
3. **測試不同尺寸組合**: 測試直向/橫向Canvas與直向/橫向Device的各種組合
4. **驗證寬度對齊**: 確保對齊計算使用處理後的正確寬度值

### 7. 實際應用場景

#### 場景 1: 2.9吋直向螢幕 (128x296)
- Canvas: 128x296 (直向)
- Device: 128x296 (直向)
- 處理: 無需旋轉
- 結果: ImageInfo 128x296

#### 場景 2: 2.9吋螢幕橫向使用 (296x128)
- Canvas: 128x296 (直向)
- Device: 296x128 (橫向)
- 處理: 將Canvas旋轉90度
- 結果: ImageInfo 296x128

#### 場景 3: 6吋橫向螢幕 (1024x758)
- Canvas: 758x1024 (直向)
- Device: 1024x758 (橫向)
- 處理: 將Canvas旋轉90度
- 結果: ImageInfo 1024x758

這樣的修改確保了 EPD 格式轉換過程中，無論Canvas和Device的尺寸關係如何，都能正確處理並生成匹配的EPD資料。
