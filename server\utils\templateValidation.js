// server/utils/templateValidation.js
const crypto = require('crypto');

// 安全配置
const securityConfig = {
  // 主密鑰 - 在生產環境中應存儲在環境變數或安全的密鑰管理系統中
  PRIMARY_KEY: 'EPD-MANAGER-TEMPLATE-SECURITY-PRIMARY-KEY-2025',
  // 二級密鑰 - 用於多層次加密
  SECONDARY_KEY: 'SECONDARY-VALIDATION-KEY-FOR-TEMPLATE-INTEGRITY',
  // 版本識別號 - 用於未來升級加密算法時的兼容性
  VERSION: 'v1',
  // 算法強度
  ITERATIONS: 1000,
  // 鹽長度
  SALT_BYTES: 16,
  // 輸出長度
  DIGEST_LENGTH: 64
};

/**
 * 生成基於時間的臨時密鑰組件
 * 使用日期作為種子，確保同一天內生成的密鑰相同
 * @returns {String} 臨時密鑰組件
 */
function generateTemporaryKeyComponent() {
  const today = new Date();
  const dateStr = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;
  return crypto.createHash('sha256')
    .update(dateStr + securityConfig.SECONDARY_KEY)
    .digest('hex')
    .substring(0, 32);
}

/**
 * 計算模板內容的強化檢查碼
 * @param {Object|String} templateContent - 模板內容對象或JSON字串
 * @returns {String} - 強化的檢查碼
 */
function calculateChecksum(templateContent) {
  // 確保輸入是字符串
  const contentString = typeof templateContent === 'string'
    ? templateContent
    : JSON.stringify(templateContent);

  // 生成隨機鹽值
  const salt = crypto.randomBytes(securityConfig.SALT_BYTES);

  // 獲取臨時密鑰組件
  const tempKeyComponent = generateTemporaryKeyComponent();

  // 組合主密鑰和臨時密鑰
  const combinedKey = crypto.createHmac('sha256', securityConfig.PRIMARY_KEY)
    .update(tempKeyComponent)
    .digest();

  // 第一層 HMAC
  const firstLayerHmac = crypto.createHmac('sha256', combinedKey)
    .update(contentString)
    .digest();

  // 第二層 PBKDF2 (Password-Based Key Derivation Function 2)
  const derivedKey = crypto.pbkdf2Sync(
    firstLayerHmac,
    salt,
    securityConfig.ITERATIONS,
    securityConfig.DIGEST_LENGTH,
    'sha512'
  );

  // 組合最終檢查碼：版本 + 鹽值(hex) + 衍生密鑰(hex)
  const checksumComponents = {
    version: securityConfig.VERSION,
    salt: salt.toString('hex'),
    hash: derivedKey.toString('hex')
  };

  // 返回 Base64 編碼的 JSON
  return Buffer.from(JSON.stringify(checksumComponents)).toString('base64');
}

/**
 * 驗證模板內容的完整性
 * @param {Object|String} templateContent - 模板內容對象或JSON字串
 * @param {String} providedChecksum - 提供的檢查碼
 * @returns {Boolean} - 驗證結果
 */
function validateChecksum(templateContent, providedChecksum) {
  try {
    console.log('開始驗證模板完整性');

    // 確保輸入是字符串
    const contentString = typeof templateContent === 'string'
      ? templateContent
      : JSON.stringify(templateContent);

    // 解碼檢查碼
    const decodedChecksum = JSON.parse(Buffer.from(providedChecksum, 'base64').toString());

    // 驗證版本
    if (decodedChecksum.version !== securityConfig.VERSION) {
      console.warn(`檢查碼版本不匹配: 期望 ${securityConfig.VERSION}, 實際 ${decodedChecksum.version}`);
      return false;
    }

    // 從檢查碼中提取鹽值
    const salt = Buffer.from(decodedChecksum.salt, 'hex');

    // 獲取臨時密鑰組件
    const tempKeyComponent = generateTemporaryKeyComponent();

    // 組合主密鑰和臨時密鑰
    const combinedKey = crypto.createHmac('sha256', securityConfig.PRIMARY_KEY)
      .update(tempKeyComponent)
      .digest();

    // 第一層 HMAC
    const firstLayerHmac = crypto.createHmac('sha256', combinedKey)
      .update(contentString)
      .digest();

    // 第二層 PBKDF2
    const derivedKey = crypto.pbkdf2Sync(
      firstLayerHmac,
      salt,
      securityConfig.ITERATIONS,
      securityConfig.DIGEST_LENGTH,
      'sha512'
    );

    // 比較哈希值
    const isValid = derivedKey.toString('hex') === decodedChecksum.hash;
    console.log('驗證結果:', isValid ? '成功' : '失敗');
    return isValid;
  } catch (error) {
    console.error('檢查碼驗證過程中發生錯誤:', error);
    return false;
  }
}

/**
 * 模板結構驗證函數
 * @param {Object} template - 模板對象
 * @returns {Object} - { valid: boolean, errors: string[] }
 */
function validateTemplate(template) {
  const errors = [];

  if (!template.id) {
    errors.push('模板缺少 ID');
  }

  if (!template.name) {
    errors.push('模板缺少名稱');
  }

  // 如果有更多的驗證邏輯，可以在這裡添加

  return {
    valid: errors.length === 0,
    errors
  };
}

// 創建模板驗證中間件
function createTemplateValidatorMiddleware() {
  return (req, res, next) => {
    // 只有特定路徑才運行驗證邏輯
    if (req.path.startsWith('/api/templates') &&
        (req.method === 'POST' || req.method === 'PUT') &&
        !req.path.includes('/calculate-checksum') &&
        !req.path.includes('/validate-checksum')) {

      const templates = Array.isArray(req.body) ? req.body : [req.body];

      for (const template of templates) {
        const validationResult = validateTemplate(template);
        if (!validationResult.valid) {
          return res.status(400).json({
            error: '模板驗證失敗',
            details: validationResult.errors
          });
        }
      }
    }

    next();
  };
}

// 導出所有功能
module.exports = {
  calculateChecksum,
  validateChecksum,
  validateTemplate,
  createTemplateValidatorMiddleware
};
