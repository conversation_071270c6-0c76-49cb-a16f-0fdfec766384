# 批量傳送邏輯修正

## 修正概述

根據用戶要求，對批量傳送功能進行了三個關鍵邏輯修正：

1. **裝置離線檢查**：如果裝置狀態為"離線"，該裝置直接標記為失敗，不進入重新列隊
2. **非智能模式Gateway檢查**：如果裝置為"非智能"模式，只要主Gateway不在線，該裝置判斷失敗，不進入重新列隊
3. **智能模式Gateway檢查**：如果裝置為"智能"模式，所有隸屬該裝置的Gateway都不在線，該裝置判斷失敗，不進入重新列隊

## 修改文件

**文件路徑**: `server/services/sendPreviewToGateway.js`

## 具體修改內容

### 1. checkTaskCanProcess 函數修改

**位置**: 行 1154-1215

**修改內容**:
- 添加了裝置狀態檢查邏輯
- 如果裝置狀態為 'offline'，直接返回 false，不允許處理
- 修改非智能模式邏輯，允許主gateway離線的任務被處理（然後在processTask中直接失敗）

```javascript
// 檢查設備是否離線 - 如果離線則不能處理，且不應重試
if (device.status === 'offline') {
  console.log(`❌ 設備 ${task.deviceId} 狀態為離線，跳過處理`);
  return false;
}

// 非智能模式：檢查主要網關狀態
const isPrimaryOnline = websocketService.isGatewayOnline(primaryGatewayId);
const isPrimaryBusy = websocketService.isGatewayBusyWithChunk(primaryGatewayId);

// 如果主要網關離線，允許處理這個任務（在processTask中會直接失敗）
// 如果主要網關在線且空閒，允許處理
// 只有主要網關在線但忙碌時，才不允許立即處理
return !isPrimaryOnline || (isPrimaryOnline && !isPrimaryBusy);
```

### 2. processTask 函數修改

**位置**: 行 1462-1598

**修改內容**:

#### 2.1 裝置離線檢查
```javascript
// 檢查設備是否離線 - 如果離線則直接失敗，不重試
if (device.status === 'offline') {
  console.log(`❌ 設備 ${task.deviceId} 狀態為離線，直接標記為失敗`);
  return {
    success: false,
    shouldRetry: false,
    error: '設備離線',
    duration: Date.now() - startTime
  };
}
```

#### 2.2 智能模式Gateway檢查增強
```javascript
if (device.gatewaySelectionMode === 'auto') {
  // ... 主要網關檢查邏輯 ...
  
  // 智能模式：檢查是否所有網關都離線
  const allGatewaysOffline = allGatewayIds.every(gwId => !websocketService.isGatewayOnline(gwId));
  if (allGatewaysOffline) {
    console.log(`❌ 智能模式設備 ${task.deviceId} 所有網關都離線，直接標記為失敗`);
    return {
      success: false,
      shouldRetry: false,
      error: '所有網關都離線',
      duration: Date.now() - startTime
    };
  }
  
  // 智能模式但沒有備用網關，檢查主要網關是否離線
  if (!isPrimaryOnline) {
    console.log(`❌ 智能模式設備 ${task.deviceId} 主要網關離線且無備用網關，直接標記為失敗`);
    return {
      success: false,
      shouldRetry: false,
      error: '主要網關離線且無備用網關',
      duration: Date.now() - startTime
    };
  }
}
```

#### 2.3 非智能模式Gateway檢查增強
```javascript
else {
  // 非智能模式，檢查主要網關是否可用
  const isPrimaryOnline = websocketService.isGatewayOnline(primaryGatewayId);
  const isPrimaryBusy = websocketService.isGatewayBusyWithChunk(primaryGatewayId);
  
  if (!isPrimaryOnline) {
    // 非智能模式：主要網關離線直接失敗
    console.log(`❌ 非智能模式設備 ${task.deviceId} 主要網關離線，直接標記為失敗`);
    return {
      success: false,
      shouldRetry: false,
      error: '主要網關離線',
      duration: Date.now() - startTime
    };
  }
}
```

### 3. shouldRetry 邏輯修改

**位置**: 行 1669-1682

**修改內容**:
- 修改了重試判斷邏輯，排除特定失敗情況不允許重試

```javascript
// 判斷是否應該重試
const shouldRetry = (
  error.message.includes('網關忙碌') ||
  error.message.includes('chunk傳輸') ||
  error.message.includes('timeout') ||
  error.message.includes('連接') ||
  error.message.includes('傳輸失敗')
) && !(
  // 以下情況不應重試
  error.message.includes('設備離線') ||
  error.message.includes('主要網關離線') ||
  error.message.includes('所有網關都離線') ||
  error.message.includes('主要網關離線且無備用網關')
);
```

## 修正邏輯總結

### 修正前的問題
1. 裝置離線時仍會嘗試重新列隊
2. 非智能模式下主Gateway離線時會重新列隊而不是直接失敗
3. 智能模式下所有Gateway離線時會重新列隊而不是直接失敗

### 修正後的行為
1. **裝置離線**:
   - `checkTaskCanProcess`: 返回 false（不處理）
   - `processTask`: 直接標記為失敗，shouldRetry = false
2. **非智能模式 + 主Gateway離線**:
   - `checkTaskCanProcess`: 返回 true（允許處理）
   - `processTask`: 直接標記為失敗，shouldRetry = false
3. **非智能模式 + 主Gateway忙碌**:
   - `checkTaskCanProcess`: 返回 false（等待重試）
   - `processTask`: 重新列隊，shouldRetry = true
4. **智能模式 + 所有Gateway離線**:
   - `processTask`: 直接標記為失敗，shouldRetry = false
5. **智能模式 + 主Gateway離線但有備用Gateway**:
   - 嘗試使用備用Gateway
6. **Gateway忙碌**: 繼續重新列隊邏輯，shouldRetry = true

## 測試建議

建議測試以下場景：
1. 批量傳送包含離線裝置的情況
2. 批量傳送非智能模式裝置，主Gateway離線的情況
3. 批量傳送智能模式裝置，所有Gateway都離線的情況
4. 混合模式批量傳送（包含智能和非智能裝置）
5. Gateway忙碌時的重新列隊行為是否正常

## 影響範圍

此修改只影響批量傳送功能，不影響單個裝置的傳送邏輯。修改確保了：
- 提高批量傳送效率（減少無效重試）
- 更準確的錯誤報告
- 符合用戶期望的失敗處理邏輯

---

# 智能模式完整實現文檔

## 智能模式概述

智能模式 (`gatewaySelectionMode: 'auto'`) 允許裝置在主要網關不可用時自動選擇備用網關，提高傳輸成功率和系統可靠性。

## 核心組件

### 1. 裝置配置結構
```javascript
{
  _id: "deviceId",
  gatewaySelectionMode: "auto",        // 智能模式標識
  primaryGatewayId: "gateway1",        // 主要網關ID
  otherGateways: ["gateway2", "gateway3"], // 備用網關ID陣列
  status: "online" | "offline"         // 裝置狀態
}
```

### 2. 網關狀態檢查函數
- `websocketService.isGatewayOnline(gatewayId)` - 檢查網關是否在線
- `websocketService.isGatewayBusyWithChunk(gatewayId)` - 檢查網關是否忙碌
- `websocketService.getAvailableGateways(gatewayIds)` - 獲取可用網關列表

## 智能模式處理邏輯

### checkTaskCanProcess 函數邏輯 (行 1175-1200)

```javascript
if (device.gatewaySelectionMode === 'auto') {
  // 1. 檢查主要網關是否可用
  const isPrimaryAvailable = websocketService.isGatewayOnline(primaryGatewayId) &&
                            !websocketService.isGatewayBusyWithChunk(primaryGatewayId);

  if (isPrimaryAvailable) {
    return true; // 主要網關可用，可以立即處理
  }

  // 2. 檢查備用網關
  if (Array.isArray(device.otherGateways) && device.otherGateways.length > 0) {
    const allGatewayIds = [primaryGatewayId, ...device.otherGateways];
    const availableGateways = websocketService.getAvailableGateways(allGatewayIds);
    const backupGateway = availableGateways.find(gw => gw !== primaryGatewayId);

    return !!backupGateway; // 有備用網關可用
  }

  return false; // 沒有可用網關
}
```

### processTask 函數智能邏輯 (行 1517-1581)

#### 階段1: 主要網關檢查
```javascript
const isPrimaryBusy = websocketService.isGatewayBusyWithChunk(primaryGatewayId);
const isPrimaryOnline = websocketService.isGatewayOnline(primaryGatewayId);

if (!isPrimaryBusy && isPrimaryOnline) {
  // 主要網關空閒且在線，直接使用
  canProcessNow = true;
  selectedGatewayId = primaryGatewayId;
}
```

#### 階段2: 備用網關搜尋
```javascript
else {
  // 主要網關忙碌或離線，尋找備用網關
  if (Array.isArray(device.otherGateways) && device.otherGateways.length > 0) {
    const allGatewayIds = [primaryGatewayId];
    const otherGatewayIds = device.otherGateways.map(gw => {
      if (typeof gw === 'string') return gw;
      if (gw instanceof ObjectId) return gw.toString();
      return String(gw);
    });
    allGatewayIds.push(...otherGatewayIds);

    const availableGateways = websocketService.getAvailableGateways(allGatewayIds);
    const backupGateway = availableGateways.find(gw => gw !== primaryGatewayId);

    if (backupGateway) {
      selectedGatewayId = backupGateway;
      useBackupGateway = true;
      canProcessNow = true;
    }
  }
}
```

#### 階段3: 失敗條件檢查
```javascript
// 情況1: 有備用網關但都不可用，檢查是否所有網關都離線
if (!canProcessNow && Array.isArray(device.otherGateways) && device.otherGateways.length > 0) {
  const allGatewaysOffline = allGatewayIds.every(gwId => !websocketService.isGatewayOnline(gwId));
  if (allGatewaysOffline) {
    return {
      success: false,
      shouldRetry: false,
      error: '所有網關都離線'
    };
  }
}

// 情況2: 沒有備用網關且主要網關離線
if (!canProcessNow && (!Array.isArray(device.otherGateways) || device.otherGateways.length === 0)) {
  if (!isPrimaryOnline) {
    return {
      success: false,
      shouldRetry: false,
      error: '主要網關離線且無備用網關'
    };
  }
}
```

## 智能模式決策矩陣

| 主要網關狀態 | 備用網關配置 | 備用網關狀態 | 處理結果 | shouldRetry |
|-------------|-------------|-------------|----------|-------------|
| 在線+空閒 | 任意 | 任意 | 使用主要網關 | N/A |
| 在線+忙碌 | 無 | N/A | 重新排隊 | true |
| 在線+忙碌 | 有 | 有可用 | 使用備用網關 | N/A |
| 在線+忙碌 | 有 | 全忙碌 | 重新排隊 | true |
| 離線 | 無 | N/A | 直接失敗 | false |
| 離線 | 有 | 有可用 | 使用備用網關 | N/A |
| 離線 | 有 | 全離線 | 直接失敗 | false |
| 離線 | 有 | 全忙碌 | 重新排隊 | true |

## 錯誤處理和重試邏輯

### 不可重試的錯誤條件
1. **裝置離線**: `device.status === 'offline'`
2. **所有網關都離線**: `allGatewayIds.every(gwId => !websocketService.isGatewayOnline(gwId))`
3. **主要網關離線且無備用網關**: `!isPrimaryOnline && (!device.otherGateways || device.otherGateways.length === 0)`

### 可重試的錯誤條件
1. **網關忙碌**: 主要網關忙碌但有備用網關也忙碌
2. **傳輸失敗**: 網路連接問題、超時等
3. **chunk傳輸衝突**: 併發傳輸衝突

## 預防性標記機制

### 目的
防止併發任務選擇同一個網關造成衝突

### 實現
```javascript
// 預防性標記
const preTaskChunkId = `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
websocketService.startChunkTransmission(selectedGatewayId, preTaskChunkId, device.macAddress);

try {
  // 執行傳輸
  const result = await sendDevicePreviewToGateway(task.deviceId, {
    forceGatewayId: selectedGatewayId
  });
} finally {
  // 清理標記
  websocketService.endChunkTransmission(selectedGatewayId, preTaskChunkId);
}
```

## 統計信息收集

### smartSelectionStats 結構
```javascript
{
  totalAutoModeDevices: 0,      // 智能模式裝置總數
  usedBackupGateway: 0,         // 使用備用網關次數
  primaryGatewayBusy: 0         // 主要網關忙碌次數
}
```

### 收集時機
- 任務成功完成時收集統計信息
- 根據 `result.smartGatewaySelection` 對象更新計數器

## 配置參數

### 系統配置
- `maxQueueCycles`: 最大隊列循環次數 (預設: 100)
- `maxWaitCycles`: 最大等待循環次數 (預設: 10)
- `concurrency`: 並發處理任務數 (預設: 3)

### 任務配置
- `maxRetries`: 單個任務最大重試次數 (預設: 3)
- `enableSmartSelection`: 是否啟用智能選擇 (預設: true)

## 日誌和調試

### 關鍵日誌點
1. **智能模式啟動**: `🤖 設備 ${deviceId} 智能模式 - 檢查主要網關 ${primaryGatewayId} 狀態`
2. **主要網關狀態**: `✅ 主要網關 ${primaryGatewayId} 空閒，直接使用`
3. **備用網關搜尋**: `⚠️ 主要網關 ${primaryGatewayId} 忙碌，尋找備用網關`
4. **備用網關選擇**: `✅ 找到備用網關 ${backupGateway}，使用備用網關`
5. **失敗條件**: `❌ 智能模式設備 ${deviceId} 所有網關都離線，直接標記為失敗`

### 調試信息
- 網關狀態檢查結果
- 可用網關列表
- 選擇的網關ID和原因
- 預防性標記的建立和清理
