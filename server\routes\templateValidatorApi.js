// server/routes/templateValidatorApi.js
const express = require('express');
const router = express.Router();
const { calculateChecksum, validateChecksum } = require('../utils/templateValidation');

// API路由：計算模板檢查碼
router.post('/templates/calculate-checksum', (req, res) => {
  try {
    const templateContent = req.body;

    if (!templateContent) {
      return res.status(400).json({ error: '未提供模板內容' });
    }

    const checksum = calculateChecksum(templateContent);

    res.status(200).json({
      message: '檢查碼計算成功',
      checksum,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('計算檢查碼時發生錯誤:', error);
    res.status(500).json({ error: error.message });
  }
});

// API路由：驗證模板檢查碼
router.post('/templates/validate-checksum', (req, res) => {
  try {
    const { templateContent, checksum } = req.body;

    if (!templateContent) {
      return res.status(400).json({ error: '未提供模板內容' });
    }

    if (!checksum) {
      return res.status(400).json({ error: '未提供檢查碼' });
    }

    // 過濾掉無效的模板，確保每個模板都有 ID 和名稱
    let validContent = templateContent;

    // 如果是數組，過濾掉無效的模板
    if (Array.isArray(templateContent)) {
      console.log('收到模板數組，原始數量:', templateContent.length);

      validContent = templateContent.filter(template => {
        const isValid = template && template.id && template.name;
        if (!isValid) {
          console.warn('跳過無效模板:', JSON.stringify(template));
        }
        return isValid;
      });

      console.log('過濾後有效模板數量:', validContent.length);

      // 如果沒有有效模板，返回失敗
      if (validContent.length === 0) {
        return res.status(400).json({
          error: '模板驗證失敗',
          details: ['沒有有效的模板可供驗證']
        });
      }
    }

    const isValid = validateChecksum(validContent, checksum);

    res.status(200).json({
      valid: isValid,
      message: isValid ? '模板驗證成功' : '模板已被修改，驗證失敗'
    });
  } catch (error) {
    console.error('驗證檢查碼時發生錯誤:', error);
    res.status(500).json({ error: error.message });
  }
});

// 沒有初始化數據庫的需求，直接導出路由
module.exports = router;
