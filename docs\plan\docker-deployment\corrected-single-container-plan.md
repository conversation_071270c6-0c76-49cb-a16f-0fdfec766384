# EPD Manager 正確的 Docker 部署方案

## 🎯 **原始架構分析**

EPD Manager 的實際架構是：
- **統一專案**: 前後端在同一個專案中
- **開發命令**: `npm run dev` 同時啟動前後端
- **前端**: Vite 開發服務器 (5173 端口)
- **後端**: Node.js Express (3001 端口)
- **資料庫**: MongoDB (27017 端口)

## ❌ **之前的錯誤方案**

我錯誤地將其分成兩個獨立的 Docker 服務：
- `epd-manager-frontend` (Nginx + 靜態文件)
- `epd-manager-server` (Node.js 後端)

這完全不符合原始設計！

## ✅ **正確的 Docker 化方案**

### 方案一：單容器方案 (推薦)

保持原始架構，將整個專案容器化為一個服務：

#### Dockerfile
```dockerfile
FROM node:18-alpine

# 安裝系統依賴 (Canvas 和 Puppeteer 需要)
RUN apk add --no-cache \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    giflib-dev \
    librsvg-dev \
    pixman-dev \
    pkgconfig \
    make \
    g++ \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates

# 設置 Puppeteer 環境變數
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

WORKDIR /app

# 複製根目錄的 package.json (前端)
COPY package*.json ./
RUN npm ci

# 複製後端的 package.json
COPY server/package*.json ./server/
RUN cd server && npm ci

# 複製所有源碼
COPY . .

# 創建必要目錄
RUN mkdir -p server/logs server/uploads

# 暴露端口 (固定端口，避免程式碼調用錯誤)
EXPOSE 5173 3001

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3001/api/health || exit 1

# 使用原始的開發命令
CMD ["npm", "run", "dev"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  # EPD Manager 統一服務 (前端 + 後端)
  epd-manager:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: epd-manager
    restart: unless-stopped
    ports:
      # 注意：容器內端口固定為 5173 和 3001，避免程式碼調用錯誤
      - "${FRONTEND_PORT:-5173}:5173"  # 外部端口可自定義，內部固定 5173
      - "${SERVER_PORT:-3001}:3001"    # 外部端口可自定義，內部固定 3001
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - MONGO_URI=mongodb://mongodb:27017  # 容器間通信，固定端口
      - MONGO_DB=${MONGO_DB:-resourceManagement}
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - server_uploads:/app/server/uploads
      - server_logs:/app/server/logs
    depends_on:
      mongodb:
        condition: service_started
    networks:
      - epd-network

  # MongoDB 資料庫
  mongodb:
    image: mongo:7-jammy
    container_name: epd-manager-mongodb
    restart: unless-stopped
    ports:
      - "${MONGO_PORT:-27017}:27017"
    environment:
      - MONGO_INITDB_DATABASE=${MONGO_DB:-resourceManagement}
    volumes:
      - mongo_data:/data/db
    command: mongod --wiredTigerCacheSizeGB 1.5 --bind_ip_all
    networks:
      - epd-network

volumes:
  mongo_data:
  server_uploads:
  server_logs:

networks:
  epd-network:
    driver: bridge
```

### 方案二：生產環境優化方案

如果需要生產環境優化，可以使用構建版本：

#### Dockerfile.production
```dockerfile
# 構建階段
FROM node:18-alpine AS builder

WORKDIR /app

# 安裝前端依賴並構建
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# 安裝後端依賴
COPY server/package*.json ./server/
RUN cd server && npm ci --only=production

# 生產階段
FROM node:18-alpine

# 安裝系統依賴
RUN apk add --no-cache \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    giflib-dev \
    librsvg-dev \
    pixman-dev \
    chromium \
    nginx

# 設置 Puppeteer 環境變數
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

WORKDIR /app

# 複製後端代碼和依賴
COPY --from=builder /app/server ./server
COPY --from=builder /app/node_modules ./node_modules

# 複製前端構建產物到 Nginx
COPY --from=builder /app/dist /usr/share/nginx/html

# 複製 Nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

# 創建啟動腳本
COPY start.sh /start.sh
RUN chmod +x /start.sh

EXPOSE 80 3001

CMD ["/start.sh"]
```

#### start.sh
```bash
#!/bin/sh

# 啟動 Nginx (前端)
nginx &

# 啟動 Node.js 服務器 (後端)
cd /app/server && npm start &

# 等待所有進程
wait
```

## 🔧 **環境變數配置**

### .env
```env
# === 必須設置 ===
JWT_SECRET=your-super-secret-jwt-key

# === 端口配置 ===
# 注意：這些是外部訪問端口，容器內部端口固定為 5173 和 3001
FRONTEND_PORT=5173  # 外部訪問前端的端口
SERVER_PORT=3001    # 外部訪問後端的端口
MONGO_PORT=27017    # 外部訪問 MongoDB 的端口

# === 基本配置 ===
NODE_ENV=development  # development | production
MONGO_DB=resourceManagement

# === 重要說明 ===
# 程式碼中的端口配置：
# - 前端 API 配置 (src/utils/api/apiConfig.ts): port: 3001 (固定)
# - 後端監聽端口 (server/index.js): const port = 3001 (固定)
# - 前端開發服務器: vite --host 0.0.0.0 (默認 5173)
#
# 如果要修改端口，需要同時修改：
# 1. 程式碼中的硬編碼端口
# 2. Docker 容器內部端口
# 3. docker-compose.yml 中的端口映射
```

## 🚀 **部署命令**

### 開發環境 (推薦)
```bash
# 使用開發模式 Dockerfile
docker-compose up -d

# 查看日誌
docker-compose logs -f epd-manager
```

### 生產環境
```bash
# 使用生產模式 Dockerfile
docker-compose -f docker-compose.prod.yml up -d
```

## 🎯 **優勢**

### 1. 保持原始架構
- ✅ 使用原始的 `npm run dev` 命令
- ✅ 前後端在同一個容器中
- ✅ 開發體驗與本地一致

### 2. 簡化部署
- ✅ 只需要兩個服務：epd-manager + mongodb
- ✅ 減少容器間通信複雜度
- ✅ 更容易除錯和維護

### 3. 外部訪問支援
- ✅ 同時暴露 5173 和 3001 端口
- ✅ 支援動態 IP 檢測
- ✅ CORS 配置支援外部訪問

## 📋 **與開發流程的整合**

### 本地開發 (不變)
```bash
npm run dev  # 繼續使用原始命令
```

### Docker 開發
```bash
docker-compose up -d  # 容器化開發
```

### 生產部署
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🔄 **遷移步驟**

1. **移除錯誤的雙容器配置**
2. **創建單容器 Dockerfile**
3. **更新 docker-compose.yml**
4. **測試開發和生產模式**

## ⚠️ **端口自定義注意事項**

### 問題分析
當前程式碼中有多處硬編碼端口：

1. **前端 API 配置** (`src/utils/api/apiConfig.ts`):
   ```typescript
   export const API_CONFIG = {
     host: getServerHost(),
     port: 3001,  // 硬編碼
     useHttps: false,
   };
   ```

2. **後端監聽端口** (`server/index.js`):
   ```javascript
   const port = 3001;  // 硬編碼
   ```

3. **CORS 配置** (`server/index.js`):
   ```javascript
   const allowedOrigins = [
     'http://localhost:5173',  // 硬編碼
     `http://${localIp}:5173`, // 硬編碼
     // ...
   ];
   ```

### 解決方案
為了避免端口自定義造成的問題，建議：

#### 方案 A：保持固定端口 (推薦)
- 容器內部端口固定：5173 (前端) 和 3001 (後端)
- 只允許外部端口映射自定義
- 程式碼無需修改

#### 方案 B：支援動態端口 (需要修改程式碼)
如果確實需要自定義內部端口，需要修改：

1. **後端端口環境變數化**:
   ```javascript
   // server/index.js
   const port = process.env.PORT || 3001;
   ```

2. **前端 API 配置環境變數化**:
   ```typescript
   // src/utils/api/apiConfig.ts
   export const API_CONFIG = {
     host: getServerHost(),
     port: parseInt(import.meta.env.VITE_API_PORT || '3001'),
     useHttps: false,
   };
   ```

3. **CORS 配置動態化**:
   ```javascript
   // server/index.js
   const frontendPort = process.env.FRONTEND_PORT || 5173;
   const allowedOrigins = [
     `http://localhost:${frontendPort}`,
     `http://${localIp}:${frontendPort}`,
   ];
   ```

### 當前建議
**使用方案 A**，保持程式碼簡潔，避免不必要的複雜度。

這個方案完全符合原始架構，保持了 `npm run dev` 的開發體驗，同時支援 Docker 部署和外部訪問。
