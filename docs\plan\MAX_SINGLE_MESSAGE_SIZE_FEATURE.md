# maxSingleMessageSize 功能說明

## 功能概述

在 `chunkingSupport` 中新增 `maxSingleMessageSize` 參數，用於讓 gateway 告知 server 單次發送 JSON 訊息的最大大小限制。這個限制針對的是 JSON 訊息本身的大小，而不是分片傳輸的總數據大小。當 JSON 訊息大小超過該限制時，server 不會發送給該 gateway，但會記錄該次任務。

## 參數說明

### maxSingleMessageSize
- **類型**: `number` (可選)
- **單位**: bytes
- **用途**: 設定 gateway 能接受的單次發送 JSON 訊息的最大大小
- **適用範圍**:
  - `update_preview` JSON 訊息（包含 imageData 和 rawdata）
  - 各種 ACK 回應訊息
  - 其他所有 JSON 格式的 WebSocket 訊息
- **不適用範圍**:
  - 分片傳輸的二進制數據（chunk 已經分片，不需要此限制）
  - 分片傳輸的總數據大小
- **行為**:
  - 當 JSON 訊息大小超過此限制時，server 拒絕發送
  - 任務會被記錄到對應的設備或 gateway 事件中
  - 如果未設定此參數，則不進行此項檢查

## gatewayInfo 結構更新

```json
{
  "type": "gatewayInfo",
  "info": {
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "model": "Gateway Model 003",
    "wifiFirmwareVersion": "1.0.0",
    "btFirmwareVersion": "2.0.0",
    "ipAddress": "*************",
    
    "chunkingSupport": {
      "enabled": true,                 // 是否支援分片傳輸
      "maxChunkSize": 200,            // 每個分片的最大大小（4 bytes - 512KB）
      "maxSingleMessageSize": 10240,  // 新增：單次發送訊息的最大數據量限制（bytes）
      "embeddedIndex": true,          // 是否支援嵌入式 Index 模式
      "jsonHeader": true              // 是否支援 JSON Header 模式（向後兼容）
    }
  }
}
```

## 實現邏輯

### 1. JSON 訊息大小檢查
新增 `checkJsonMessageSize` 函數來檢查 JSON 訊息大小：

```javascript
const checkJsonMessageSize = (message, macAddress) => {
  const chunkingSupport = gatewayCapabilities.get(macAddress);

  if (!chunkingSupport || !chunkingSupport.maxSingleMessageSize) {
    return { canSend: true };
  }

  const maxSingleMessageSize = chunkingSupport.maxSingleMessageSize;
  const messageStr = JSON.stringify(message);
  const messageSize = Buffer.byteLength(messageStr, 'utf8');

  if (messageSize > maxSingleMessageSize) {
    return {
      canSend: false,
      messageSize,
      maxSingleMessageSize,
      error: `JSON message size ${messageSize} exceeds maxSingleMessageSize ${maxSingleMessageSize}`
    };
  }

  return { canSend: true, messageSize };
};
```

### 2. 應用場景

#### 場景 1: update_preview 訊息檢查
在 `sendDirectRawdata` 函數中檢查 update_preview JSON 訊息大小：

```javascript
const directMessage = {
  type: 'update_preview',
  deviceMac: deviceMac,
  imageCode: imageCode,
  rawdata: Array.from(rawBuffer),
  imageData: imageData, // 可能很大的 base64 字符串
  timestamp: new Date().toISOString()
};

const sizeCheck = checkJsonMessageSize(directMessage, gatewayMac);
if (!sizeCheck.canSend) {
  // 記錄事件並拒絕發送
  throw new Error(sizeCheck.error);
}
```

#### 場景 2: 一般命令訊息檢查
在 `sendCommandToGateway` 函數中檢查所有 JSON 訊息：

```javascript
const sizeCheck = checkJsonMessageSize(message, gatewayMac);
if (!sizeCheck.canSend) {
  // 記錄事件並拒絕發送
  await logGatewayEvent(gatewayId, 'message_rejected', {
    reason: 'json_message_exceeds_max_single_message_size',
    messageType: message.type,
    messageSize: sizeCheck.messageSize,
    maxSingleMessageSize: sizeCheck.maxSingleMessageSize
  });
  throw new Error(sizeCheck.error);
}
```

### 3. 任務記錄機制
當發送被拒絕時，會記錄相關事件：

#### 設備事件記錄（針對 update_preview）
```javascript
await logDeviceEvent(device._id, 'image_update_rejected', {
  reason: 'update_preview_json_exceeds_max_single_message_size',
  messageSize: sizeCheck.messageSize,
  maxSingleMessageSize: sizeCheck.maxSingleMessageSize,
  gatewayMac: gatewayMac,
  imageCode: imageCode,
  timestamp: new Date().toISOString(),
  errorMessage: sizeCheck.error
});
```

#### Gateway 事件記錄
```javascript
await logGatewayEvent(gatewayId, 'message_rejected', {
  reason: 'json_message_exceeds_max_single_message_size',
  messageType: message.type,
  messageSize: sizeCheck.messageSize,
  maxSingleMessageSize: sizeCheck.maxSingleMessageSize,
  timestamp: new Date().toISOString(),
  errorMessage: sizeCheck.error
});
```

## 使用場景

### 場景 1: 低記憶體 Gateway（嚴格限制 JSON 訊息大小）
```json
{
  "chunkingSupport": {
    "enabled": true,
    "maxChunkSize": 200,
    "maxSingleMessageSize": 1024,  // 限制 JSON 訊息為 1KB
    "embeddedIndex": true,
    "jsonHeader": true
  }
}
```
**說明**: 適用於記憶體極度受限的 Gateway，無法處理包含大量 imageData 的 update_preview 訊息。

### 場景 2: 網路頻寬限制（中等限制）
```json
{
  "chunkingSupport": {
    "enabled": true,
    "maxChunkSize": 1024,
    "maxSingleMessageSize": 8192,  // 限制 JSON 訊息為 8KB
    "embeddedIndex": true,
    "jsonHeader": true
  }
}
```
**說明**: 適用於網路頻寬有限的環境，可以處理中等大小的 JSON 訊息。

### 場景 3: 無限制 Gateway（高性能）
```json
{
  "chunkingSupport": {
    "enabled": true,
    "maxChunkSize": 4096,
    // 不設定 maxSingleMessageSize，表示無 JSON 訊息大小限制
    "embeddedIndex": true,
    "jsonHeader": true
  }
}
```
**說明**: 適用於高性能 Gateway，可以處理任意大小的 JSON 訊息。

## 錯誤處理

### 錯誤類型
- **錯誤訊息**: `JSON message size {messageSize} exceeds maxSingleMessageSize {maxSingleMessageSize} for Gateway {macAddress}`
- **錯誤原因**: `json_message_exceeds_max_single_message_size`

### 日誌輸出
```
❌ JSON 訊息大小 15360 bytes 超過 Gateway AA:BB:CC:DD:EE:FF 的限制 10240 bytes，拒絕發送
📝 記錄因 update_preview JSON 訊息過大而被拒絕的任務
已記錄設備 507f1f77bcf86cd799439011 的 update_preview 訊息被拒絕事件
已記錄網關 507f1f77bcf86cd799439012 的 update_preview 訊息被拒絕事件
```

## 向後兼容性

- 如果 gateway 未提供 `maxSingleMessageSize` 參數，系統會跳過此項檢查
- 現有的分片傳輸邏輯不受影響（分片傳輸不檢查此限制）
- 現有的 gateway 可以繼續正常工作，無需強制升級
- 只有 JSON 訊息會被檢查，二進制分片數據不受影響

## 測試案例

### 測試 1: 小 JSON 訊息正常發送
- JSON 訊息大小: 500 bytes
- maxSingleMessageSize: 1024 bytes
- 預期結果: 正常發送

### 測試 2: 大 JSON 訊息被拒絕
- JSON 訊息大小: 2048 bytes（包含大量 imageData）
- maxSingleMessageSize: 1024 bytes
- 預期結果: 拒絕發送，記錄事件

### 測試 3: 未設定限制
- JSON 訊息大小: 任意
- maxSingleMessageSize: 未設定
- 預期結果: 正常處理，不進行此項檢查

### 測試 4: 分片傳輸不受影響
- 分片傳輸總數據: 50KB
- maxSingleMessageSize: 1KB
- 預期結果: 分片傳輸正常進行（不檢查總數據大小）

## 相關文件

- `server/services/websocketService.js` - 主要實現邏輯
- `docs/plan/Gateway-Device-Quick-Reference.md` - API 參考文檔
- `docs/plan/Gateway-Device-Implementation-Guide.md` - 實現指南
- `server/tests/ws-client-from-copied-info.js` - 測試客戶端
- `test-chunk-functions.cjs` - 測試函數

## 更新日誌

- **2024-12-19**: 新增 maxSingleMessageSize 功能
  - 添加數據大小檢查邏輯
  - 實現任務記錄機制
  - 更新相關文檔和測試文件
