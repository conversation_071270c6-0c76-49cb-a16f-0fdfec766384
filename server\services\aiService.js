// 導入必要的模組
const fetch = require('node-fetch');
const { getScreenConfigs } = require('../utils/screenConfigs');

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

class AIService {
  constructor() {
    this.genAI = null;
    this.model = null;
    this.config = null;
    this.isInitialized = false;
  }

  async initialize() {
    try {
      // 從配置中獲取AI設定
      const aiConfig = await this.getAIConfig();
      
      if (!aiConfig.enabled || !aiConfig.geminiApiKey) {
        throw new Error('AI功能未啟用或API Key未配置');
      }

      this.config = aiConfig;

      // 不再使用 Google Generative AI SDK，直接使用 REST API
      this.apiKey = aiConfig.geminiApiKey;
      this.modelName = aiConfig.model || 'gemini-2.0-flash';
      
      this.isInitialized = true;
      console.log('AI服務初始化成功');
    } catch (error) {
      console.error('AI服務初始化失敗:', error);
      throw error;
    }
  }

  async getAIConfig() {
    try {
      // 從系統配置中獲取AI配置
      if (!getDbConnection) {
        throw new Error('資料庫連接函數尚未初始化');
      }
      const { db } = await getDbConnection();
      const collection = db.collection('sysConfigs');
      
      const config = await collection.findOne({ key: 'ai-config' });
      
      if (!config) {
        return {
          enabled: false,
          geminiApiKey: '',
          model: 'gemini-2.0-flash',
          maxTokens: 2048,
          temperature: 0.7,
          timeout: 30000
        };
      }
      
      return config.value;
    } catch (error) {
      console.error('獲取AI配置失敗:', error);
      throw error;
    }
  }

  async processUserRequest(userInput, context = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const prompt = await this.buildPrompt(userInput, context);

      // 使用 REST API 調用 Gemini
      const response = await this.callGeminiAPI(prompt);

      return this.parseAIResponse(response);
    } catch (error) {
      console.error('AI請求處理失敗:', error);
      throw new Error(`AI處理失敗: ${error.message}`);
    }
  }

  async callGeminiAPI(prompt) {
    try {
      const url = `https://generativelanguage.googleapis.com/v1beta/models/${this.modelName}:generateContent?key=${this.apiKey}`;

      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          maxOutputTokens: this.config.maxTokens || 2048,
          temperature: this.config.temperature || 0.7,
        }
      };

      // 設置請求超時
      const timeoutMs = this.config.timeout || 30000;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Gemini API 錯誤 ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      // 提取回應文本
      if (data.candidates && data.candidates.length > 0 &&
          data.candidates[0].content && data.candidates[0].content.parts &&
          data.candidates[0].content.parts.length > 0) {
        return data.candidates[0].content.parts[0].text;
      } else {
        throw new Error('Gemini API 回應格式異常');
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('AI請求超時');
      }
      throw error;
    }
  }

  async buildPrompt(userInput, context) {
    // 構建對話歷史
    let conversationHistory = '';
    if (context.previousMessages && Array.isArray(context.previousMessages)) {
      conversationHistory = '\n對話歷史：\n';
      context.previousMessages.forEach((msg, index) => {
        if (msg.type === 'user') {
          conversationHistory += `用戶: ${msg.content}\n`;
        } else if (msg.type === 'ai') {
          conversationHistory += `AI: ${msg.content}\n`;
        }
      });
      conversationHistory += '\n';
    }

    const systemPrompt = `
你是一個EPD管理系統的AI助手。你可以幫助用戶：

1. 創建門店 - 協助建立新門店
2. 創建模板 - 協助建立EPD模板
3. 創建帳號 - 協助建立新用戶帳號
4. 建立刷圖計畫 - 協助設定定時觸發的設備刷新計畫
5. 建立條件刷圖 - 協助建立自動執行設備刷新任務條件
6. 建立數據欄位/資料 - 協助設計和建立門店專屬的數據結構
7. 協助模板設計 - 提供模板佈局建議和設計指導並自動化建立

重要：請根據對話歷史理解用戶的意圖並累積信息。如果用戶在回應你之前的問題，請結合之前的對話內容來理解用戶的回答。

關鍵規則：
1. 從對話歷史中提取已經收集到的信息，並在extractedData中包含這些信息
2. 如果用戶在回應你的問題，將用戶的回答與對話上下文結合理解
3. 累積所有已收集的信息，不要丟失之前獲得的數據

例如：
- 對話歷史顯示你問了"請問模板名稱是什麼？"，用戶回答"gag"，那麼extractedData應包含 templateName: "gag"
- 如果之前已經收集到screen_size: "2.9"，現在用戶提供template_type: "store"，那麼extractedData應包含所有三個字段
- 始終保持已收集信息的完整性，不要在新的回應中丟失之前的數據

請根據用戶輸入和對話歷史，識別意圖並提取相關信息。
如果缺少必填信息，請在missingFields中列出缺失的必填字段，並詢問用戶提供。

回應格式必須是JSON，包含：
{
  "intent": "create_store|create_template|create_account|ask_info|unknown",
  "confidence": 0.0-1.0,
  "extractedData": {},
  "missingFields": [],
  "response": "回應文字"
}

必填字段說明：
- 創建門店：storeName（門店名稱）
- 創建模板：templateName（模板名稱）、screen_size（螢幕尺寸）、template_type（模板類型：system 或 store）
  * 如果template_type是"store"，還需要：store_name（門店名稱）或 store_id（門店ID）
- 創建帳號：username（用戶名）

模板類型說明：
- system：系統模板（所有門店都可以使用）
- store：門店模板（只有指定門店可以使用，必須提供 store_name 或 store_id）

重要：如果template_type是"store"，必須在missingFields中包含store_name，直到用戶提供門店信息為止！

螢幕尺寸選項：
${this.getScreenSizeOptions()}

顏色類型選項（會根據螢幕尺寸自動判定，通常不需要用戶指定）：
- BW: 16級灰階
- BWR: 黑白紅
- BWY: 黑白黃

注意：每個螢幕尺寸支持的顏色類型不同，系統會自動選擇合適的顏色。
在我們的系統中，BW代表16級灰階顯示。

可用門店列表：
${await this.getAvailableStores()}

${conversationHistory}

數據累積示例：
如果對話歷史中包含：
AI: "請問模板名稱是什麼？螢幕尺寸是多少？"
用戶: "門店模板"
AI: "好的，模板類型是門店模板。請問模板名稱是什麼？"
用戶: "grgrg"

那麼extractedData應該包含：
{
  "template_type": "store",
  "templateName": "grgrg"
}

但是missingFields應該包含：["store_name"]
因為門店模板還需要指定門店！

必填字段檢查規則：
1. 如果template_type是"store"但沒有store_name或store_id，必須在missingFields中包含"store_name"
2. 只有當所有必填字段都收集完成時，missingFields才能為空
3. 不要丟失之前收集到的任何信息！

當前用戶輸入：${userInput}
`;

    return systemPrompt;
  }

  getScreenSizeOptions() {
    const screenConfigs = getScreenConfigs();
    return screenConfigs.map(config => {
      const sizeIdentifier = config.name.replace(/["\s]/g, '');
      return `- ${sizeIdentifier}寸: ${config.width}x${config.height}`;
    }).join('\n');
  }

  async getAvailableStores() {
    try {
      if (!getDbConnection) {
        return '（無法獲取門店列表）';
      }
      const { db } = await getDbConnection();
      const storesCollection = db.collection('stores');
      const stores = await storesCollection.find({ status: 'active' }).limit(10).toArray();

      if (stores.length === 0) {
        return '（暫無可用門店）';
      }

      return stores.map(store => `- ${store.name} (ID: ${store.id})`).join('\n');
    } catch (error) {
      console.error('獲取門店列表失敗:', error);
      return '（獲取門店列表失敗）';
    }
  }

  parseAIResponse(responseText) {
    try {
      // 嘗試解析JSON回應
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        
        // 驗證回應格式
        if (!parsed.intent || !parsed.response) {
          throw new Error('AI回應格式不完整');
        }
        
        return {
          intent: parsed.intent,
          confidence: parsed.confidence || 0.5,
          extractedData: parsed.extractedData || {},
          missingFields: parsed.missingFields || [],
          response: parsed.response
        };
      }
      
      // 如果無法解析JSON，返回默認格式
      return {
        intent: 'unknown',
        confidence: 0.5,
        extractedData: {},
        missingFields: [],
        response: responseText
      };
    } catch (error) {
      console.error('解析AI回應失敗:', error);
      return {
        intent: 'unknown',
        confidence: 0.0,
        extractedData: {},
        missingFields: [],
        response: '抱歉，我無法理解您的請求，請重新描述。'
      };
    }
  }

  async testConnection() {
    try {
      console.log('開始AI連接測試...');

      // 重新獲取最新配置
      const aiConfig = await this.getAIConfig();
      console.log('獲取到AI配置:', {
        enabled: aiConfig.enabled,
        hasApiKey: !!aiConfig.geminiApiKey,
        model: aiConfig.model
      });

      if (!aiConfig.enabled) {
        throw new Error('AI功能未啟用');
      }

      if (!aiConfig.geminiApiKey) {
        throw new Error('API Key未配置');
      }

      if (!aiConfig.geminiApiKey.startsWith('AIza')) {
        throw new Error('API Key格式不正確');
      }

      // 強制重新初始化以使用最新配置
      this.isInitialized = false;
      this.config = null;
      this.apiKey = null;
      this.modelName = null;

      console.log('重新初始化AI服務...');
      await this.initialize();

      console.log('發送測試請求到Gemini API...');
      const response = await this.callGeminiAPI('請回應"連接測試成功"');
      console.log('收到Gemini回應:', response);

      return {
        success: true,
        message: 'AI服務連接正常',
        response: response
      };
    } catch (error) {
      console.error('AI連接測試失敗:', error);
      return {
        success: false,
        message: `AI服務連接失敗: ${error.message}`,
        response: null
      };
    }
  }

  // 生成門店ID的輔助方法
  generateStoreId(storeName) {
    // 簡單的ID生成邏輯
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const cleanName = storeName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '').toLowerCase();
    return `store-${cleanName}-${timestamp}-${randomSuffix}`;
  }

  // 驗證門店數據
  validateStoreData(data) {
    const errors = [];
    
    if (!data.name || typeof data.name !== 'string') {
      errors.push('門店名稱為必填項');
    }
    
    if (data.phone && !/^[\d\-\+\(\)\s]+$/.test(data.phone)) {
      errors.push('電話號碼格式不正確');
    }
    
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push('郵箱格式不正確');
    }
    
    return errors;
  }

  // 驗證模板數據
  validateTemplateData(data) {
    const errors = [];
    
    if (!data.name || typeof data.name !== 'string') {
      errors.push('模板名稱為必填項');
    }
    
    if (!data.screenSize) {
      errors.push('螢幕尺寸為必填項');
    }
    
    const validScreenSizes = ['200x200', '250x122', '296x128', '400x300', '800x480'];
    if (data.screenSize && !validScreenSizes.includes(data.screenSize)) {
      errors.push('無效的螢幕尺寸');
    }
    
    const validColors = ['BW', 'BWR', 'BWY', 'Gray16'];
    if (data.color && !validColors.includes(data.color)) {
      errors.push('無效的顏色類型');
    }
    
    return errors;
  }

  // 驗證用戶數據
  validateUserData(data) {
    const errors = [];

    if (!data.username || typeof data.username !== 'string') {
      errors.push('用戶名為必填項');
    }

    if (data.username && data.username.length < 3) {
      errors.push('用戶名至少需要3個字符');
    }

    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push('郵箱格式不正確');
    }

    if (data.phone && !/^[\d\-\+\(\)\s]+$/.test(data.phone)) {
      errors.push('電話號碼格式不正確');
    }

    return errors;
  }

  // 生成建立門店的快速範例
  generateStoreExample() {
    const randomNum = Math.floor(Math.random() * 1000);
    const examples = [
      {
        name: `星巴克咖啡店${randomNum}`,
        address: `台北市信義區信義路五段${randomNum}號`,
        phone: `02-${Math.floor(Math.random() * 9000) + 1000}-${Math.floor(Math.random() * 9000) + 1000}`
      },
      {
        name: `全家便利商店${randomNum}`,
        address: `新北市板橋區中山路一段${randomNum}號`,
        phone: `02-${Math.floor(Math.random() * 9000) + 1000}-${Math.floor(Math.random() * 9000) + 1000}`
      },
      {
        name: `麥當勞${randomNum}店`,
        address: `台中市西屯區台灣大道三段${randomNum}號`,
        phone: `04-${Math.floor(Math.random() * 9000) + 1000}-${Math.floor(Math.random() * 9000) + 1000}`
      },
      {
        name: `7-ELEVEN ${randomNum}門市`,
        address: `高雄市前鎮區中華五路${randomNum}號`,
        phone: `07-${Math.floor(Math.random() * 9000) + 1000}-${Math.floor(Math.random() * 9000) + 1000}`
      },
      {
        name: `屈臣氏${randomNum}店`,
        address: `桃園市中壢區中正路${randomNum}號`,
        phone: `03-${Math.floor(Math.random() * 9000) + 1000}-${Math.floor(Math.random() * 9000) + 1000}`
      }
    ];

    const selectedExample = examples[Math.floor(Math.random() * examples.length)];

    return `請幫我建立一個新門店：
門店名稱：${selectedExample.name}
地址：${selectedExample.address}
電話：${selectedExample.phone}`;
  }
}

// 導出AI服務實例和初始化函數
const aiServiceInstance = new AIService();
module.exports = aiServiceInstance;
module.exports.initDB = initDB;
