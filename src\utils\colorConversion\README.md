# 統一顏色轉換模組

這個模組提供了前後端統一的顏色轉換功能，根據設備或模板的 `colorType` 自動選擇最適合的圖像處理算法。

## 特點

- **統一性**: 前後端使用相同的轉換邏輯，確保一致性
- **自動化**: 根據 `colorType` 自動選擇最佳轉換算法
- **擴展性**: 易於添加新的顏色類型和轉換算法
- **向後兼容**: 支援舊的 `effectType` 方式，便於逐步遷移

## 支援的顏色類型

| 顏色類型 | 描述 | 推薦算法 |
|---------|------|---------|
| `Gray16` / `BW` | 16階灰度屏幕 | 16級灰度量化 |
| `Black & White & Red` / `BWR` | 黑白紅三色屏幕 | 三色量化 |
| `Black & White & Red & Yellow` / `BWRY` | 黑白紅黃四色屏幕 | 四色量化 |
| `All colors` / `ALL` | 全彩屏幕 | 保持原色 |

## 使用方式

### 前端 (TypeScript)

```typescript
import { convertImageByColorType, convertImageByEffectType } from './utils/colorConversion';

// 根據 colorType 自動轉換（推薦方式）
const convertedCanvas = convertImageByColorType(sourceCanvas, 'Black & White & Red');

// 向後兼容的 effectType 方式
const convertedCanvas2 = convertImageByEffectType(sourceCanvas, 'blackAndWhite', 128);

// 高級用法：自定義選項
import { convertImageForColorType, BrowserAdapter } from './utils/colorConversion';

const result = convertImageForColorType(
  sourceCanvas,
  'Gray16',
  BrowserAdapter,
  { threshold: 100, ditherStrength: 0.8 }
);

if (result.success) {
  const convertedCanvas = result.canvas;
}
```

### 後端 (Node.js)

```javascript
const { convertImageByColorType } = require('./src/utils/colorConversion/nodeAdapter');

// 根據 colorType 轉換
const convertedCanvas = convertImageByColorType(sourceCanvas, 'Black & White & Red');
```

## API 參考

### 主要函數

#### `convertImageByColorType(sourceCanvas, colorType, customOptions?)`

根據顏色類型自動選擇最佳轉換算法。

**參數:**
- `sourceCanvas`: 源畫布 (HTMLCanvasElement)
- `colorType`: 目標顏色類型 (string | DisplayColorType)
- `customOptions`: 自定義選項 (可選)

**返回:** 轉換後的畫布或 null

#### `convertImageByEffectType(sourceCanvas, effectType, threshold)`

向後兼容的效果類型轉換。

**參數:**
- `sourceCanvas`: 源畫布 (HTMLCanvasElement)
- `effectType`: 效果類型 ('original' | 'blackAndWhite' | 'grayscale' | 'inverted' | 'dithering')
- `threshold`: 閾值 (0-255)

**返回:** 轉換後的畫布

### 選項介面

```typescript
interface ColorConversionOptions {
  threshold?: number;           // 二值化閾值 (0-255)，默認 128
  ditherStrength?: number;      // 抖動強度 (0-1)，默認 1.0
  preserveAlpha?: boolean;      // 是否保留透明度，默認 true
}
```

## 轉換算法詳解

### 1. 16級灰度量化 (gray16)
將圖像轉換為 16 個灰度級別，從純白 (255) 到純黑 (0)，適合 Gray16 電子紙屏幕。

### 2. 黑白二值化 (blackAndWhite)
使用標準灰度轉換公式將圖像轉換為純黑白。

### 3. 灰階轉換 (grayscale)
保留完整的灰度信息，不進行量化。

### 4. 抖動算法 (dithering)
使用 Floyd-Steinberg 抖動算法，適合黑白屏幕顯示灰階效果。

### 5. 顏色量化 (colorQuantization)
將圖像顏色限制在特定調色板內，適合多色電子紙屏幕。

### 6. 原色保持 (original)
不進行任何轉換，保持原始顏色。

## 環境適配

模組支援不同的運行環境：

- **瀏覽器環境**: 使用 `BrowserAdapter`
- **Node.js 環境**: 使用 `NodeAdapter`

## 測試

打開 `test.html` 文件在瀏覽器中測試不同顏色類型的轉換效果。

## 遷移指南

### 從舊的 imageEffects 遷移

**舊代碼:**
```typescript
import { convertToBlackAndWhite } from './imageEffects';
const result = convertToBlackAndWhite(canvas, 128);
```

**新代碼:**
```typescript
import { convertImageByEffectType } from './colorConversion';
const result = convertImageByEffectType(canvas, 'blackAndWhite', 128);
```

### 從 applyImageEffect 遷移

**舊代碼:**
```typescript
import { applyImageEffect } from './previewUtils';
const result = applyImageEffect(canvas, 'blackAndWhite', 128);
```

**新代碼:**
```typescript
import { convertImageByColorType } from './colorConversion';
// 如果有 colorType 信息
const result = convertImageByColorType(canvas, template.color);
// 或者繼續使用 effectType
const result = convertImageByEffectType(canvas, 'blackAndWhite', 128);
```

## 擴展新的顏色類型

要添加新的顏色類型支援：

1. 在 `getConversionStrategy` 函數中添加新的 case
2. 如果需要新的算法，實現對應的轉換函數
3. 在 `getColorPalette` 中定義新的調色板（如果使用顏色量化）
4. 更新 TypeScript 類型定義

## 注意事項

- 所有轉換都是非破壞性的，會創建新的畫布
- 建議在生產環境中使用 `colorType` 驅動的轉換方式
- 後端轉換需要安裝 `canvas` npm 包
- 轉換結果的質量取決於源圖像的質量和選擇的算法
