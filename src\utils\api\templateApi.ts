import { Template } from '../../types';
import { generatePreviewImage, EffectType } from '../previewUtils';
import { convertImageByColorType } from '../colorConversion';
import { buildEndpointUrl } from './apiConfig';
import { compressTemplateImages, checkTemplateSize } from '../imageCompression';

/**
 * 將模板的 colorType 轉換為對應的 EffectType
 * 如果模板有 colorType，優先使用顏色轉換；否則使用效果轉換
 */
const getPreviewEffectFromTemplate = (template: Template): { useColorConversion: boolean, effectType?: EffectType, colorType?: string } => {
  // 如果模板有 colorType，使用顏色轉換
  if (template.color && template.color !== 'original') {
    return {
      useColorConversion: true,
      colorType: template.color
    };
  }

  // 否則使用默認的黑白效果
  return {
    useColorConversion: false,
    effectType: 'blackAndWhite'
  };
};

// 共用的保存模板到服務器功能
export const saveTemplateToServer = async (template: Template): Promise<{success: boolean, result?: any, error?: Error}> => {
  try {
    console.log('templateApi: 開始保存模板，模板ID:', template.id);
    console.log('templateApi: 請求體數據摘要:', {
      id: template.id,
      name: template.name,
      type: template.type,
      elementsCount: template.elements?.length || 0
    });

    // 尋找畫布元素來生成預覽圖
    const canvasElements = document.querySelectorAll('[data-canvas-width]');
    let updatedTemplate = { ...template }; // 創建模板副本以避免修改原始模板

    if (canvasElements && canvasElements.length > 0) {
      const canvasRef = canvasElements[0] as HTMLElement;

      // 根據模板的 colorType 決定使用哪種預覽生成方式
      const previewConfig = getPreviewEffectFromTemplate(template);
      let previewImageData: string | null = null;

      if (previewConfig.useColorConversion && previewConfig.colorType) {
        console.log(`templateApi: 使用模板顏色類型生成預覽圖: ${previewConfig.colorType}`);

        // 使用顏色轉換生成預覽圖
        try {
          // 先生成原始預覽圖
          const originalPreviewData = await generatePreviewImage(canvasRef, 'original', 128);

          if (originalPreviewData) {
            // 創建臨時 canvas 來應用顏色轉換
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            const img = new Image();

            await new Promise<void>((resolve, reject) => {
              img.onload = () => {
                tempCanvas.width = img.width;
                tempCanvas.height = img.height;
                tempCtx?.drawImage(img, 0, 0);

                // 應用顏色轉換
                const convertedCanvas = convertImageByColorType(tempCanvas, previewConfig.colorType!, { threshold: 128 });
                previewImageData = convertedCanvas.toDataURL('image/png');
                resolve();
              };
              img.onerror = reject;
              img.src = originalPreviewData;
            });
          }
        } catch (error) {
          console.error('templateApi: 顏色轉換失敗，使用默認效果:', error);
          // 如果顏色轉換失敗，回退到默認效果
          previewImageData = await generatePreviewImage(canvasRef, 'blackAndWhite', 128);
        }
      } else {
        console.log(`templateApi: 使用效果類型生成預覽圖: ${previewConfig.effectType}`);
        // 使用效果轉換生成預覽圖
        previewImageData = await generatePreviewImage(canvasRef, previewConfig.effectType!, 128);
      }

      if (previewImageData) {
        // 添加預覽圖到模板數據
        updatedTemplate = {
          ...updatedTemplate,
          previewImage: previewImageData
        };

        console.log('templateApi: 已生成預覽圖並添加到模板數據');
      }
    }

    // 檢查模板大小
    const sizeCheck = checkTemplateSize(updatedTemplate);
    console.log('templateApi: 模板大小檢查:', sizeCheck);

    // 如果模板太大，嘗試壓縮圖片
    if (sizeCheck.needsCompression || sizeCheck.sizeInMB > 5) {
      console.log('templateApi: 模板較大，正在壓縮圖片...');

      try {
        const compressedTemplate = await compressTemplateImages(updatedTemplate, 0.7);
        const compressedSizeCheck = checkTemplateSize(compressedTemplate);

        console.log('templateApi: 壓縮後大小:', compressedSizeCheck);

        // 如果壓縮效果顯著（減少超過20%），使用壓縮後的模板
        if (compressedSizeCheck.sizeInMB < sizeCheck.sizeInMB * 0.8) {
          updatedTemplate = compressedTemplate;
          console.log('templateApi: 使用壓縮後的模板');
        } else {
          console.log('templateApi: 壓縮效果不明顯，使用原始模板');
        }
      } catch (error) {
        console.error('templateApi: 圖片壓縮失敗，使用原始模板:', error);
      }
    }

    // 發送到服務器
    console.log('templateApi: 正在發送模板到服務器...');
    const response = await fetch(buildEndpointUrl('templates'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updatedTemplate)
    });

    console.log('templateApi: 服務器響應狀態碼:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('templateApi: 服務器返回錯誤:', response.status, errorText);
      throw new Error(`服務器錯誤: ${response.status} - ${errorText}`);
    }

    const serverResult = await response.json();

    // 返回成功結果，包含帶有預覽圖的更新後模板
    console.log('templateApi: 模板保存成功');
    return {
      success: true,
      result: {
        ...serverResult,
        template: updatedTemplate // 返回包含預覽圖的更新後模板
      }
    };
  } catch (error) {
    console.error('templateApi: 保存模板失敗:', error);
    return { success: false, error: error as Error };
  }
};