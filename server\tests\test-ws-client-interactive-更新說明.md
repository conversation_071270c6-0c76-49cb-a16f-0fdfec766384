# test-ws-client-interactive.js 更新說明

## 版本 2.2.0 - 分片決策邏輯增強

### 主要更新內容

#### 1. 新增互動式分片參數設定

**新增功能**：
- 互動式選擇 `maxChunkSize`（每個分片的最大大小）
- 互動式選擇 `maxSingleMessageSize`（單次 JSON 訊息的最大大小）
- 互動式選擇偏好的 `rawdata` 格式

**選項說明**：

**maxChunkSize 選項**：
1. 20 bytes - 極小分片，測試用
2. 200 bytes - 小型設備（預設）
3. 1024 bytes - 中型設備
4. 4096 bytes - 大型設備
5. 自定義大小

**maxSingleMessageSize 選項**：
1. 500 bytes - 小值，用於測試 JSON 訊息大小檢查
2. 1024 bytes - 中等值
3. 2048 bytes - 標準值（預設）
4. 4096 bytes - 大值
5. 自定義大小

**rawdata 格式選項**：
1. rawdata - 未壓縮格式（預設）
2. runlendata - Run-Length 壓縮格式

#### 2. 更新 gatewayInfo 消息結構

**新的 chunkingSupport 結構**：
```json
{
  "chunkingSupport": {
    "enabled": true,
    "maxChunkSize": 200,                    // 用戶選擇的值
    "maxSingleMessageSize": 2048,           // 用戶選擇的值
    "embeddedIndex": true,
    "jsonHeader": true,
    "supportedFormat": "rawdata"            // 用戶選擇的格式
  }
}
```

#### 3. 增強的使用說明

**新增說明內容**：
- 顯示當前設定的分片參數
- 說明兩階段分片決策邏輯
- 提供分片決策測試指導

### 使用方法

#### 1. 啟動測試客戶端
```bash
node test-ws-client-interactive.js
```

#### 2. 按照提示進行設定
1. 登入系統（預設：root/123456789）
2. 選擇門店
3. 選擇或創建網關
4. **新增**：設定分片傳輸參數
   - 選擇 maxChunkSize
   - 選擇 maxSingleMessageSize  
   - 選擇偏好的 rawdata 格式
5. 開始 WebSocket 連接

#### 3. 測試不同的分片場景

**測試場景 1：第一階段分片觸發**
- 設定：maxChunkSize = 20 bytes
- 預期：任何 rawdata > 20 bytes 都會觸發分片傳輸
- 用途：測試基本的分片功能

**測試場景 2：第二階段分片觸發**
- 設定：maxChunkSize = 1024 bytes, maxSingleMessageSize = 500 bytes
- 預期：小 rawdata 但大 JSON 訊息會觸發分片傳輸
- 用途：測試新的 JSON 訊息大小檢查邏輯

**測試場景 3：直接傳輸**
- 設定：maxChunkSize = 1024 bytes, maxSingleMessageSize = 4096 bytes
- 預期：大部分情況下使用直接傳輸
- 用途：測試正常的直接傳輸路徑

### 觀察要點

#### 1. 服務器日誌
觀察服務器端的分片決策日誌：
```
判斷分片需求: 資料=164 bytes, Gateway maxChunkSize=200 bytes
檢查 JSON 訊息大小: 742 bytes, maxSingleMessageSize: 500 bytes
✅ 啟用分片傳輸 (JSON 訊息超過 maxSingleMessageSize)
```

#### 2. 客戶端輸出
觀察客戶端的分片接收過程：
```
📦 開始接收分片: chunk_12345, 總分片數: 4, 設備: 00:11:22:33:44:55
📥 收到分片 0: 200 bytes 數據 + 4 bytes index
📥 收到分片 1: 200 bytes 數據 + 4 bytes index
...
🎯 所有分片接收完成，開始重組圖片
```

### 測試建議

#### 1. 參數組合測試
- **小分片測試**：maxChunkSize=20, maxSingleMessageSize=2048
- **JSON 大小測試**：maxChunkSize=1024, maxSingleMessageSize=500
- **高性能測試**：maxChunkSize=4096, maxSingleMessageSize=10240

#### 2. 格式測試
- 測試 rawdata 和 runlendata 格式的差異
- 觀察壓縮效果和傳輸行為

#### 3. 邊界條件測試
- 測試 maxSingleMessageSize < maxChunkSize 的情況（不建議）
- 測試極小和極大的參數值

### 注意事項

#### 1. 參數設定建議
- `maxSingleMessageSize` 應該 >= `maxChunkSize`
- 根據實際硬體和網路環境選擇合適的值
- 測試時可以使用極端值來驗證邏輯

#### 2. 相容性
- 所有現有功能保持不變
- 向後兼容舊版本的測試腳本
- 新參數都有合理的預設值

#### 3. 調試技巧
- 使用小的 maxSingleMessageSize 值來強制觸發第二階段檢查
- 觀察服務器和客戶端的日誌輸出
- 檢查 saved_images 目錄中的檔案

### 相關文件

- [Gateway-Device-Implementation-Guide.md](../../docs/plan/Gateway-Device-Implementation-Guide.md)
- [Gateway-Device-Quick-Reference.md](../../docs/plan/Gateway-Device-Quick-Reference.md)
- [test-interactive-chunking-params.js](./test-interactive-chunking-params.js) - 參數測試腳本

---

**更新日期**：2024年1月
**版本**：2.2.0
**更新者**：EPD Manager 開發團隊
