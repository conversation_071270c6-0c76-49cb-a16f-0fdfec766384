# Gateway/Device 文檔更新總結

## 📋 更新概述

已根據目前調整後的發送機制架構，全面更新了 Gateway/Device 開發人員使用的文檔：

1. **Gateway-Device-Quick-Reference.md** - 快速參考手冊
2. **Gateway-Device-Implementation-Guide.md** - 完整實作指南

## 🆕 主要新增功能

### 1. 分片傳輸支援
- **嵌入式 Index 模式**：每個分片前 4 bytes 包含 chunkIndex (little-endian)
- **Gateway 能力上報**：在 gatewayInfo 中上報 chunkingSupport 能力
- **自動判斷機制**：Server 根據 Gateway 能力自動選擇傳輸方式
- **硬體限制支援**：支援 4 bytes - 512KB 的分片大小範圍
- **性能警告系統**：當分片數量 > 100 時發出警告

### 2. 數據格式處理更新 (v2.1.0)
- **dataType 欄位統一**：使用 `dataType` 取代 `rawdataFormat` 欄位
- **RLE 壓縮支援**：完整的 Run-Length Encoding 實作指南
- **壓縮範圍明確**：只壓縮像素數據，不包含 ImageInfo 結構和 chunk index
- **解壓縮實作**：提供完整的解壓縮算法和範例代碼

### 3. 新增消息類型
#### 發送消息 (Gateway → Server)
- `chunk_start_ack`: 分片開始確認
- `chunk_ack`: 分片確認

#### 接收消息 (Server → Gateway)
- `image_chunk_start`: 分片傳輸開始
- 二進制分片數據：嵌入式 Index 格式

#### 更新的消息格式
- `update_preview`: 新增 `dataType` 欄位
- `image_chunk_start`: 使用 `dataType` 取代 `rawdataFormat`

### 4. 更新的 gatewayInfo 格式
```json
{
  "type": "gatewayInfo",
  "info": {
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "model": "Gateway Model 003",
    "wifiFirmwareVersion": "1.0.0",
    "btFirmwareVersion": "2.0.0",
    "ipAddress": "*************",
    
    // 新增：分片傳輸能力支援
    "chunkingSupport": {
      "enabled": true,                 // 是否支援分片傳輸
      "maxChunkSize": 200,            // 每個分片的最大大小（4 bytes - 512KB）
      "maxSingleMessageSize": 10240,  // 單次發送訊息的最大數據量限制（bytes），超過則拒絕發送
      "embeddedIndex": true,          // 是否支援嵌入式 Index 模式
      "jsonHeader": true              // 是否支援 JSON Header 模式（向後兼容）
    }
  }
}
```

## 🔧 技術細節

### 分片數據格式
```
[4 bytes: chunkIndex][N bytes: 實際數據]
```
- **chunkIndex**: 32位無符號整數，little-endian 格式
- **實際數據**: EPD 原始數據的一部分

### RLE 編碼格式
1. **重複序列** (runLength >= 2):
   - 格式: `[runLength, value]`
   - runLength 範圍: 2-127 (0x02-0x7F)
   - bit7 = 0

2. **非重複序列** (runLength = 1 或無重複):
   - 格式: `[0x80|length, data...]`
   - length 範圍: 1-127 (0x01-0x7F)
   - bit7 = 1

**重要說明**:
- bit7 是最高位元 (MSB)
- 壓縮的只有 EPD 像素數據，不包含 ImageInfo 結構 (12 bytes) 頭部
- 不包含 chunk 的 index 資訊

### 數據處理流程
```python
def process_data(rawdata, data_type):
    if data_type == "rawdata":
        return rawdata  # 直接使用
    elif data_type == "runlendata":
        # 分離 ImageInfo (12 bytes) 和壓縮的像素數據
        image_info = rawdata[:12]
        compressed_pixels = rawdata[12:]
        # 解壓縮像素數據
        decompressed_pixels = decompress_rle(compressed_pixels)
        # 重新組合
        return image_info + decompressed_pixels
```

### 處理流程
1. **能力上報**: Gateway 在連接時上報分片能力
2. **自動判斷**: Server 根據數據大小和 Gateway 能力決定傳輸方式
3. **分片傳輸**: 
   - Server 發送 `image_chunk_start`
   - Gateway 回應 `chunk_start_ack`
   - Server 逐個發送分片數據
   - Gateway 對每個分片回應 `chunk_ack`
   - Gateway 重組完整數據

### 性能考量
- **流量優化**: 嵌入式 Index 相比傳統方案節省 38% 流量
- **記憶體效率**: 支援極小硬體限制（最小 4 bytes 分片）
- **可靠性**: ACK 機制確保每個分片都被正確接收

## 📚 文檔結構更新

### Gateway-Device-Quick-Reference.md
- ✅ 新增分片傳輸消息格式
- ✅ 更新 gatewayInfo 格式
- ✅ 新增分片傳輸注意事項
- ✅ 新增分片相關常見問題
- ✅ 更新實作檢查清單
- ✅ **新增數據格式處理章節** (v2.1.0)
- ✅ **更新消息格式使用 dataType** (v2.1.0)
- ✅ **新增 RLE 編碼格式說明** (v2.1.0)
- ✅ 版本更新至 2.1.0

### Gateway-Device-Implementation-Guide.md
- ✅ 新增完整的分片傳輸實作範例
- ✅ 新增 ChunkReceiver 類別實作
- ✅ 更新消息處理邏輯
- ✅ 新增二進制數據處理
- ✅ 更新時序圖包含分片流程
- ✅ **新增數據格式處理章節** (v2.1.0)
- ✅ **完整的 RLE 解壓縮實作範例** (v2.1.0)
- ✅ **更新實作範例支援 dataType** (v2.1.0)
- ✅ 版本更新至 2.1.0

## 🎯 開發人員指南

### 必須實作的功能
1. **能力上報**: 在 gatewayInfo 中正確上報 chunkingSupport
2. **消息處理**: 實作 chunk_start_ack 和 chunk_ack 消息
3. **二進制處理**: 正確解析嵌入式 Index 分片數據
4. **數據重組**: 按 chunkIndex 順序重組完整數據
5. **錯誤處理**: 處理分片超時和重傳
6. **數據格式處理**: 支援 dataType 欄位解析 (v2.1.0)
7. **RLE 解壓縮**: 實作 Run-Length Encoding 解壓縮算法 (v2.1.0)

### 配置建議
- **小型設備**: maxChunkSize = 200 bytes
- **中型設備**: maxChunkSize = 512 bytes
- **大型設備**: maxChunkSize = 1024 bytes
- **極限硬體**: maxChunkSize >= 4 bytes（系統支援）

### 性能優化
- 及時發送 ACK 確認，避免 Server 重傳
- 合理設置 maxChunkSize，平衡記憶體使用和性能
- 注意分片數量警告，避免過多小分片

## ✅ 測試建議

### 基本功能測試
1. **連接測試**: 驗證 gatewayInfo 能力上報
2. **直接傳輸**: 測試小圖像的直接傳輸
3. **分片傳輸**: 測試大圖像的分片傳輸
4. **錯誤處理**: 測試網絡中斷和重連

### 分片傳輸測試
1. **正常流程**: 完整的分片接收和重組
2. **重複分片**: 處理重複接收的分片
3. **缺失分片**: 處理分片丟失的情況
4. **順序錯亂**: 處理分片順序錯亂的情況

### 性能測試
1. **不同分片大小**: 測試各種 maxChunkSize 設置
2. **大數據傳輸**: 測試大圖像的傳輸性能
3. **記憶體使用**: 監控分片接收時的記憶體使用
4. **網絡效率**: 測量實際的網絡傳輸效率

## 🔗 相關資源

### 文檔連結
- [Gateway-Device-Quick-Reference.md](./docs/plan/Gateway-Device-Quick-Reference.md)
- [Gateway-Device-Implementation-Guide.md](./docs/plan/Gateway-Device-Implementation-Guide.md)

### 測試工具
- [WebSocket 測試腳本](./server/tests/test-ws-client-interactive.js)
- [分片傳輸測試](./server/tests/test-chunk-functions.cjs)

### 技術規範
- [分片傳輸計劃](./plan/websocket-image-chunk-transmission-plan.md)
- [實作總結](./plan/CHUNK_TRANSMISSION_IMPLEMENTATION_SUMMARY.md)

## 📝 後續維護

### 版本控制
- 當前版本: 2.1.0
- 主要變更:
  - v2.0.0: 新增分片傳輸支援
  - v2.1.0: 數據格式處理更新，dataType 欄位統一，RLE 壓縮支援
- 向後兼容: 完全支援舊版 Gateway

### 未來規劃
1. **更多壓縮格式**: 添加其他數據壓縮格式支援
2. **批量傳輸**: 支援多設備批量更新
3. **斷點續傳**: 支援大文件斷點續傳
4. **動態調整**: 根據網路狀況動態調整分片大小

---

**更新完成**: 2024年1月
**文檔版本**: 2.1.0
**更新範圍**:
- v2.0.0: 完整的分片傳輸支援
- v2.1.0: 數據格式處理統一，RLE 編碼支援
**兼容性**: 向後兼容，支援舊版 Gateway
