const express = require('express');
const router = express.Router();
const { ObjectId } = require('mongodb');
const { authenticate, checkPermission } = require('../middleware/auth');
const { broadcastStoreDataUpdate } = require('../services/websocketService');

// MongoDB 連接信息 (從 index.js 共享)
const storesCollectionName = 'stores';
const dataFieldsCollection = 'dataFields';

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取數據集合
const getCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();

  // 使用 stores 集合
  const storesCollection = db.collection(storesCollectionName);
  const dataFieldsCol = db.collection(dataFieldsCollection);
  return { storesCollection, dataFieldsCol, client };
};

// 計算使用指定門店數據ID的設備數量
const calculateBoundDeviceCount = async (storeId, storeDataId) => {
  try {
    const { client, db } = await getDbConnection();
    const deviceCollection = db.collection('devices');

    // 獲取該門店的所有設備
    const allDevices = await deviceCollection.find({ storeId: storeId }).toArray();

    console.log(`開始計算門店 ${storeId} 中使用數據ID ${storeDataId} 的設備數量`);
    console.log(`該門店共有 ${allDevices.length} 個設備`);

    let boundCount = 0;

    for (const device of allDevices) {
      let isMatched = false;

      // 調試：打印設備信息
      console.log(`檢查設備 ${device.macAddress}:`, {
        dataId: device.dataId,
        dataBindings: device.dataBindings,
        dataBindingsType: typeof device.dataBindings
      });

      // 檢查舊版本的單一dataId綁定
      if (device.dataId === storeDataId) {
        isMatched = true;
        console.log(`設備 ${device.macAddress} 通過 dataId 匹配: ${device.dataId} === ${storeDataId}`);
      }

      // 檢查新版本的dataBindings
      if (!isMatched && device.dataBindings) {
        try {
          let bindings = device.dataBindings;

          // 如果是字符串，嘗試解析為JSON
          if (typeof bindings === 'string') {
            console.log(`解析設備 ${device.macAddress} 的 dataBindings 字符串:`, bindings);
            bindings = JSON.parse(bindings);
          }

          // 檢查綁定對象中是否包含該數據ID
          if (typeof bindings === 'object' && bindings !== null) {
            console.log(`檢查設備 ${device.macAddress} 的 dataBindings 對象:`, bindings);
            for (const [key, value] of Object.entries(bindings)) {
              console.log(`  檢查綁定 ${key}: ${value} === ${storeDataId}?`);
              if (value === storeDataId) {
                isMatched = true;
                console.log(`設備 ${device.macAddress} 通過 dataBindings 匹配: ${key} -> ${value}`);
                break;
              }
            }
          }
        } catch (error) {
          console.log(`解析設備 ${device.macAddress} 的 dataBindings 失敗:`, error.message);
          // 如果解析失敗，嘗試字符串匹配
          if (typeof device.dataBindings === 'string' && device.dataBindings.includes(storeDataId)) {
            isMatched = true;
            console.log(`設備 ${device.macAddress} 通過字符串匹配`);
          }
        }
      }

      if (isMatched) {
        boundCount++;
        console.log(`設備 ${device.macAddress} 匹配成功，當前計數: ${boundCount}`);
      }
    }

    console.log(`門店 ${storeId} 中使用數據ID ${storeDataId} 的設備有 ${boundCount} 個`);
    return boundCount;
  } catch (error) {
    console.error('計算設備綁定數量失敗:', error);
    return 0;
  }
};

// 獲取所有門店資料
router.get('/storeData', authenticate, checkPermission(['store:view', 'store-data:view']), async (req, res) => {
  try {
    const { storeId } = req.query;
    const { storesCollection } = await getCollection();

    console.log('獲取門店專屬數據請求，storeId:', storeId);

    if (storeId) {
      // 獲取特定門店的專屬數據
      const store = await storesCollection.findOne({ id: storeId });
      if (!store) {
        return res.status(404).json({ error: '門店不存在' });
      }

      // 計算每個門店數據的設備綁定數量
      const storeSpecificData = store.storeSpecificData || [];
      const dataWithBoundCount = await Promise.all(
        storeSpecificData.map(async (item) => {
          // 使用門店數據的ID字段來匹配設備綁定
          const dataId = item.id;
          console.log(`處理門店數據項目:`, {
            id: item.id,
            name: item.name,
            uid: item.uid
          });

          const boundDeviceCount = await calculateBoundDeviceCount(storeId, dataId);
          return {
            ...item,
            boundDeviceCount
          };
        })
      );

      // 返回門店專屬數據（包含設備綁定數量）
      res.json(dataWithBoundCount);
    } else {
      // 獲取所有門店的專屬數據（扁平化）
      // 注意：在新的數據結構中，我們不應該在返回的數據中添加 storeId
      // 但為了保持向後兼容，我們仍然需要提供一種方式讓前端知道數據屬於哪個門店
      // 這裡我們使用一個包裝對象，包含門店 ID 和門店專屬數據
      const stores = await storesCollection.find().toArray();
      const storeDataByStore = stores.map(store => ({
        storeId: store.id,
        storeName: store.name,
        data: store.storeSpecificData || []
      }));

      console.log(`找到 ${storeDataByStore.length} 個門店的專屬數據`);
      res.json(storeDataByStore);
    }
  } catch (error) {
    console.error('獲取門店專屬數據失敗:', error);
    res.status(500).json({ error: '獲取門店專屬數據失敗' });
  }
});

// 獲取單個門店專屬數據
router.get('/storeData/:uid', authenticate, checkPermission(['store:view', 'store-data:view']), async (req, res) => {
  try {
    const { uid } = req.params;
    const { storeId } = req.query;
    const { storesCollection } = await getCollection();

    if (!storeId) {
      return res.status(400).json({ error: '必須提供 storeId 參數' });
    }

    // 獲取門店
    const store = await storesCollection.findOne({ id: storeId });
    if (!store) {
      return res.status(404).json({ error: '門店不存在' });
    }

    // 查找指定 uid 的門店專屬數據
    const storeSpecificData = store.storeSpecificData || [];
    const storeItem = storeSpecificData.find(item => item.uid === uid);

    if (!storeItem) {
      return res.status(404).json({ error: '門店專屬數據不存在' });
    }

    // 直接返回門店專屬數據項目
    // 不包含 storeId，因為這與新的數據結構不符
    res.json(storeItem);
  } catch (error) {
    console.error('獲取門店專屬數據失敗:', error);
    res.status(500).json({ error: '獲取門店專屬數據失敗' });
  }
});

// 創建門店專屬數據
router.post('/storeData', authenticate, checkPermission(['store:create', 'store-data:create']), async (req, res) => {
  try {
    // 從查詢參數中獲取 storeId
    const { storeId } = req.query;

    // 從請求體中獲取數據
    const storeData = req.body;

    if (!storeId) {
      return res.status(400).json({ error: '必須提供 storeId 查詢參數' });
    }

    const { storesCollection, dataFieldsCol } = await getCollection();

    // 獲取門店
    const store = await storesCollection.findOne({ id: storeId });
    if (!store) {
      return res.status(404).json({ error: '門店不存在' });
    }

    // 檢查 ID 是否已存在於此門店的 storeSpecificData 中
    if (storeData.id) {
      const storeSpecificData = store.storeSpecificData || [];
      const idExists = storeSpecificData.some(item => item.id === storeData.id);

      if (idExists) {
        return res.status(400).json({
          error: '門店專屬數據 ID 已存在',
          message: `ID "${storeData.id}" 已存在於此門店中，請使用其他 ID`,
          code: 'DUPLICATE_STORE_SPECIFIC_DATA_ID',
          field: 'id'
        });
      }
    }

    // 獲取一般資料欄位定義
    const dataFields = await dataFieldsCol.find({ section: "ordinary" }).toArray();

    // 生成唯一識別碼
    const uid = new ObjectId().toString();

    // 初始化門店專屬數據
    const newStoreData = {
      _id: new ObjectId(),
      uid: uid
    };

    // 根據資料欄位定義添加欄位
    dataFields.forEach(field => {
      newStoreData[field.id] = storeData[field.id] !== undefined ? storeData[field.id] : null;
    });

    // 添加到門店的 storeSpecificData 陣列
    await storesCollection.updateOne(
      { id: storeId },
      { $push: { storeSpecificData: newStoreData } }
    );

    // 推送WebSocket事件
    try {
      broadcastStoreDataUpdate(storeId, [{
        uid: newStoreData.uid,
        data: newStoreData,
        updatedFields: Object.keys(newStoreData).filter(key => key !== '_id' && key !== 'uid')
      }], 'create');
    } catch (wsError) {
      console.error('推送門店資料創建事件失敗:', wsError);
      // 不影響API響應
    }

    // 返回新創建的門店專屬數據
    // 不包含 storeId，因為這與新的數據結構不符
    // 前端需要知道這個數據屬於哪個門店，但這個關聯應該在前端維護
    res.status(201).json(newStoreData);
  } catch (error) {
    console.error('創建門店專屬數據失敗:', error);
    res.status(500).json({ error: '創建門店專屬數據失敗' });
  }
});

// 更新門店專屬數據
router.put('/storeData/:uid', authenticate, checkPermission(['store:update', 'store-data:update']), async (req, res) => {
  try {
    const { uid } = req.params;
    const { storeId } = req.query;
    const updateData = req.body;

    if (!storeId) {
      return res.status(400).json({ error: '必須提供 storeId 參數' });
    }

    const { storesCollection } = await getCollection();

    // 獲取門店
    const store = await storesCollection.findOne({ id: storeId });
    if (!store) {
      return res.status(404).json({ error: '門店不存在' });
    }

    // 檢查門店專屬數據是否存在
    const storeSpecificData = store.storeSpecificData || [];
    const itemIndex = storeSpecificData.findIndex(item => item.uid === uid);

    if (itemIndex === -1) {
      return res.status(404).json({ error: '門店專屬數據不存在' });
    }

    // 如果更新包含 ID 字段，檢查 ID 是否已存在於其他數據項中
    if (updateData.id && updateData.id !== storeSpecificData[itemIndex].id) {
      // 檢查除了當前項目外的其他項目是否有相同的 ID
      const idExists = storeSpecificData.some((item, idx) =>
        idx !== itemIndex && item.id === updateData.id
      );

      if (idExists) {
        return res.status(400).json({
          error: '門店專屬數據 ID 已存在',
          message: `ID "${updateData.id}" 已存在於此門店中，請使用其他 ID`,
          code: 'DUPLICATE_STORE_SPECIFIC_DATA_ID',
          field: 'id'
        });
      }
    }

    // 更新門店專屬數據
    const updatedItem = {
      ...storeSpecificData[itemIndex],
      ...updateData
    };

    // 使用 $set 更新特定索引的元素
    await storesCollection.updateOne(
      { id: storeId },
      { $set: { [`storeSpecificData.${itemIndex}`]: updatedItem } }
    );

    // 獲取更新後的門店
    const updatedStore = await storesCollection.findOne({ id: storeId });
    const updatedStoreData = updatedStore.storeSpecificData[itemIndex];

    // 推送WebSocket事件
    try {
      broadcastStoreDataUpdate(storeId, [{
        uid: updatedStoreData.uid,
        data: updatedStoreData,
        updatedFields: Object.keys(updateData)
      }], 'update');
    } catch (wsError) {
      console.error('推送門店資料更新事件失敗:', wsError);
      // 不影響API響應
    }

    // 直接返回更新後的門店專屬數據項目
    // 不包含 storeId，因為這與新的數據結構不符
    res.json(updatedStoreData);
  } catch (error) {
    console.error('更新門店專屬數據失敗:', error);
    res.status(500).json({ error: '更新門店專屬數據失敗' });
  }
});

// 刪除門店專屬數據
router.delete('/storeData/:uid', authenticate, checkPermission(['store:delete', 'store-data:delete']), async (req, res) => {
  try {
    const { uid } = req.params;
    const { storeId } = req.query;

    if (!storeId) {
      return res.status(400).json({ error: '必須提供 storeId 參數' });
    }

    const { storesCollection } = await getCollection();

    // 獲取門店
    const store = await storesCollection.findOne({ id: storeId });
    if (!store) {
      return res.status(404).json({ error: '門店不存在' });
    }

    // 檢查門店專屬數據是否存在
    const storeSpecificData = store.storeSpecificData || [];
    const itemIndex = storeSpecificData.findIndex(item => item.uid === uid);

    if (itemIndex === -1) {
      return res.status(404).json({ error: '門店專屬數據不存在' });
    }

    // 保存被刪除項目的信息用於WebSocket事件
    const deletedItem = storeSpecificData[itemIndex];

    // 使用 $pull 操作符從陣列中刪除元素
    await storesCollection.updateOne(
      { id: storeId },
      { $pull: { storeSpecificData: { uid: uid } } }
    );

    // 推送WebSocket事件
    try {
      broadcastStoreDataUpdate(storeId, [{
        uid: deletedItem.uid,
        data: deletedItem,
        updatedFields: []
      }], 'delete');
    } catch (wsError) {
      console.error('推送門店資料刪除事件失敗:', wsError);
      // 不影響API響應
    }

    res.status(204).send();
  } catch (error) {
    console.error('刪除門店專屬數據失敗:', error);
    res.status(500).json({ error: '刪除門店專屬數據失敗' });
  }
});

// 同步資料欄位到門店專屬數據結構
router.post('/storeData/sync-fields', authenticate, checkPermission(['store:update', 'store-data:update']), async (_, res) => {
  try {
    const { storesCollection, dataFieldsCol } = await getCollection();

    // 獲取一般資料欄位
    const dataFields = await dataFieldsCol.find({ section: "ordinary" }).toArray();

    // 獲取所有門店
    const stores = await storesCollection.find().toArray();
    let totalUpdatedItems = 0;

    // 對每個門店進行處理
    for (const store of stores) {
      if (!store.storeSpecificData || store.storeSpecificData.length === 0) {
        continue;
      }

      // 對每個門店專屬數據項目進行欄位同步
      const updatedStoreSpecificData = store.storeSpecificData.map(item => {
        // 創建更新對象，只保留基本欄位
        const updatedItem = {
          ...item,
          uid: item.uid || new ObjectId().toString() // 確保每個項目都有 uid
        };

        // 添加資料欄位定義的欄位，值保留原來的值或設為null
        dataFields.forEach(field => {
          updatedItem[field.id] = item[field.id] !== undefined ? item[field.id] : null;
        });

        return updatedItem;
      });

      // 更新門店的 storeSpecificData 字段
      await storesCollection.updateOne(
        { _id: store._id },
        { $set: { storeSpecificData: updatedStoreSpecificData } }
      );

      totalUpdatedItems += updatedStoreSpecificData.length;
    }

    res.json({
      success: true,
      message: `已同步 ${totalUpdatedItems} 筆門店專屬數據與 ${dataFields.length} 個資料欄位`,
      lastSynced: new Date().toISOString()
    });
  } catch (error) {
    console.error('同步資料欄位失敗:', error);
    res.status(500).json({ error: '同步資料欄位失敗' });
  }
});

// 導出路由器和初始化函數
module.exports = { router, initDB };
