import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Calendar, Clock, Activity, AlertCircle, CheckCircle, Pause } from 'lucide-react';
import { RefreshPlan } from '../types/refreshPlan';
import { refreshPlanApi, formatExecuteTime } from '../services/refreshPlanApi';
import { subscribeToRefreshPlanUpdate, RefreshPlanUpdateEvent } from '../utils/websocketClient';
import { Store } from '../types/store';

interface RefreshPlanCalendarProps {
  store: Store;
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  plans: RefreshPlan[];
}



export const RefreshPlanCalendar: React.FC<RefreshPlanCalendarProps> = ({ store }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [plans, setPlans] = useState<RefreshPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [hoveredDay, setHoveredDay] = useState<CalendarDay | null>(null);
  const [hoveredPlan, setHoveredPlan] = useState<RefreshPlan | null>(null);
  const [showDateDetails, setShowDateDetails] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // 載入刷圖計畫
  const loadPlans = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await refreshPlanApi.getPlans(store.id, {
        page: 1,
        limit: 1000,
      });

      if (response.success) {
        setPlans(response.data.plans);
      }
    } catch (err) {
      setError('載入刷圖計畫失敗');
      console.error('載入刷圖計畫失敗:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (store?.id) {
      loadPlans();
    }
  }, [store?.id]);

  // WebSocket 即時更新
  useEffect(() => {
    if (!store?.id) return;

    const handleRefreshPlanUpdate = (event: RefreshPlanUpdateEvent) => {
      if (event.storeId === store.id) {
        console.log('行事曆收到刷圖計畫更新事件:', event);

        if (event.updateType === 'delete') {
          setPlans(prevPlans => prevPlans.filter(plan => plan._id !== event.planId));
        } else if (event.updateType === 'create') {
          loadPlans(); // 重新載入所有計畫
        } else if (event.updateType === 'status_change' || event.updateType === 'update') {
          setPlans(prevPlans =>
            prevPlans.map(plan => {
              if (plan._id !== event.planId) {
                return plan;
              }

              // 更新計畫資料
              return {
                ...plan,
                ...event.planData,
                _id: event.planId
              };
            })
          );
        }
      }
    };

    const unsubscribe = subscribeToRefreshPlanUpdate(
      store.id,
      handleRefreshPlanUpdate,
      {}
    );

    return () => {
      unsubscribe();
    };
  }, [store?.id]);

  // 生成日曆天數
  const generateCalendarDays = (): CalendarDay[] => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    const firstDay = new Date(year, month, 1);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const days: CalendarDay[] = [];
    const today = new Date();
    
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      
      const isCurrentMonth = date.getMonth() === month;
      const isToday = date.toDateString() === today.toDateString();
      
      // 獲取當天的計畫
      const dayPlans = getPlansForDate(date);
      
      days.push({
        date,
        isCurrentMonth,
        isToday,
        plans: dayPlans,
      });
    }
    
    return days;
  };

  // 獲取指定日期的計畫
  const getPlansForDate = (date: Date): RefreshPlan[] => {
    return plans.filter(plan => {
      if (!plan.enabled) return false;
      
      const { trigger } = plan;
      
      switch (trigger.type) {
        case 'once':
          if (trigger.executeDate) {
            const executeDate = new Date(trigger.executeDate);
            return executeDate.toDateString() === date.toDateString();
          }
          return false;
          
        case 'daily':
          return true; // 每天都執行
          
        case 'weekly':
          if (trigger.weekDays && trigger.weekDays.length > 0) {
            return trigger.weekDays.includes(date.getDay());
          }
          return false;
          
        default:
          return false;
      }
    });
  };

  // 獲取計畫狀態顏色
  const getPlanStatusColor = (plan: RefreshPlan): string => {
    if (!plan.enabled) return 'bg-gray-400';
    
    switch (plan.status) {
      case 'running':
        return 'bg-blue-500';
      case 'active':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'inactive':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  // 獲取計畫狀態圖示
  const getPlanStatusIcon = (plan: RefreshPlan) => {
    if (!plan.enabled) return <Pause size={12} />;
    
    switch (plan.status) {
      case 'running':
        return <Activity size={12} className="animate-pulse" />;
      case 'active':
        return <CheckCircle size={12} />;
      case 'error':
        return <AlertCircle size={12} />;
      case 'inactive':
        return <Pause size={12} />;
      default:
        return <Clock size={12} />;
    }
  };

  // 月份導航
  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  // 格式化月份年份
  const formatMonthYear = (date: Date): string => {
    return date.toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: 'long',
    });
  };

  const calendarDays = generateCalendarDays();
  const weekDays = ['日', '一', '二', '三', '四', '五', '六'];

  if (loading) {
    return (
      <div className="relative mb-6">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-100/80 via-blue-50/60 to-indigo-100/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>
        <div className="relative p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-500">載入行事曆中...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="relative mb-6">
        <div className="absolute inset-0 bg-gradient-to-br from-red-100/80 via-pink-50/60 to-red-100/80 backdrop-blur-sm rounded-2xl border border-red-200/50 shadow-xl"></div>
        <div className="relative p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-red-600">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative mb-6">
      {/* 玻璃效果背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-100/80 via-blue-50/60 to-indigo-100/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>
      
      {/* 動態光效背景 */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-2xl animate-pulse opacity-50"></div>
      
      {/* 內容區域 */}
      <div className="relative p-6">
        {/* 標題和導航 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-indigo-500 rounded-lg flex items-center justify-center shadow-lg">
              <Calendar className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-lg font-bold text-gray-800">刷圖計畫行事曆</h3>
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigateMonth('prev')}
              className="p-2 rounded-lg bg-white/50 border border-white/30 hover:bg-white/70 transition-all duration-200 shadow-sm"
            >
              <ChevronLeft size={16} className="text-gray-600" />
            </button>
            
            <span className="text-lg font-semibold text-gray-800 min-w-[120px] text-center">
              {formatMonthYear(currentDate)}
            </span>
            
            <button
              onClick={() => navigateMonth('next')}
              className="p-2 rounded-lg bg-white/50 border border-white/30 hover:bg-white/70 transition-all duration-200 shadow-sm"
            >
              <ChevronRight size={16} className="text-gray-600" />
            </button>
          </div>
        </div>

        {/* 週標題 */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {weekDays.map((day) => (
            <div key={day} className="p-2 text-center text-sm font-medium text-gray-600">
              {day}
            </div>
          ))}
        </div>

        {/* 日曆格子 */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((day, index) => (
            <div
              key={index}
              className={`
                relative p-2 min-h-[80px] rounded-lg border transition-all duration-200 cursor-pointer
                ${day.isCurrentMonth 
                  ? 'bg-white/60 border-white/40 hover:bg-white/80' 
                  : 'bg-gray-50/40 border-gray-200/30 text-gray-400'
                }
                ${day.isToday ? 'ring-2 ring-blue-400 ring-opacity-50' : ''}
                ${selectedDate?.toDateString() === day.date.toDateString() ? 'bg-blue-50/80 border-blue-300' : ''}
              `}
              onClick={() => {
                setSelectedDate(day.date);
                if (day.plans.length > 0) {
                  // 開啟整日詳情時，關閉所有懸停詳情
                  setHoveredDay(null);
                  setHoveredPlan(null);
                  setShowDateDetails(true);
                }
              }}
              onMouseEnter={(e) => {
                // 如果整日詳情模態窗口已開啟，則不顯示懸停詳情
                if (!showDateDetails && !hoveredPlan) {
                  setHoveredDay(day);
                  setMousePosition({ x: e.clientX, y: e.clientY });
                }
              }}
              onMouseMove={(e) => {
                // 只有在沒有開啟整日詳情且沒有懸停單個計畫時才更新滑鼠位置
                if (!showDateDetails && !hoveredPlan) {
                  setMousePosition({ x: e.clientX, y: e.clientY });
                }
              }}
              onMouseLeave={() => {
                // 只有在沒有開啟整日詳情時才清除懸停狀態
                if (!showDateDetails) {
                  setHoveredDay(null);
                }
              }}
            >
              {/* 日期數字 */}
              <div className={`text-sm font-medium mb-1 ${day.isToday ? 'text-blue-600' : ''}`}>
                {day.date.getDate()}
              </div>
              
              {/* 計畫指示器 */}
              <div className="space-y-1">
                {day.plans.slice(0, 3).map((plan) => (
                  <div
                    key={plan._id}
                    className={`
                      flex items-center gap-1 px-1 py-0.5 rounded text-xs text-white cursor-pointer
                      ${getPlanStatusColor(plan)}
                    `}
                    onMouseEnter={(e) => {
                      if (!showDateDetails) {
                        setHoveredPlan(plan);
                        setHoveredDay(null); // 清除日期懸停
                        setMousePosition({ x: e.clientX, y: e.clientY });
                      }
                    }}
                    onMouseMove={(e) => {
                      if (!showDateDetails && hoveredPlan?._id === plan._id) {
                        setMousePosition({ x: e.clientX, y: e.clientY });
                      }
                    }}
                    onMouseLeave={() => {
                      if (!showDateDetails) {
                        setHoveredPlan(null);
                      }
                    }}
                    onClick={(e) => {
                      e.stopPropagation(); // 防止觸發日期點擊事件
                      // 可以在這裡添加單個計畫的點擊處理邏輯
                    }}
                  >
                    {getPlanStatusIcon(plan)}
                    <span className="truncate flex-1 text-xs">
                      {plan.name.length > 8 ? `${plan.name.substring(0, 8)}...` : plan.name}
                    </span>
                  </div>
                ))}
                
                {day.plans.length > 3 && (
                  <div className="text-xs text-gray-500 text-center">
                    +{day.plans.length - 3} 更多
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* 日期懸停詳情 - 顯示當日所有計畫概覽 */}
        {hoveredDay && hoveredDay.plans.length > 0 && !showDateDetails && !hoveredPlan && (
          <div
            className="fixed z-50 p-3 bg-white rounded-lg shadow-xl border border-gray-200 max-w-xs pointer-events-none"
            style={{
              left: `${mousePosition.x + 10}px`,
              top: `${mousePosition.y - 10}px`,
              transform: mousePosition.x > window.innerWidth - 300 ? 'translateX(-100%)' : 'none'
            }}
          >
            <div className="font-medium text-gray-800 mb-2">
              {hoveredDay.date.toLocaleDateString('zh-TW')} - 當日計畫
            </div>
            <div className="space-y-1">
              {hoveredDay.plans.map((plan) => (
                <div key={plan._id} className="flex items-center gap-2 text-sm">
                  <div className={`w-2 h-2 rounded-full ${getPlanStatusColor(plan)}`}></div>
                  <span className="font-medium">{plan.name}</span>
                  <span className="text-gray-500">{formatExecuteTime(plan.trigger)}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 單個計畫懸停詳情 - 顯示計畫詳細資訊 */}
        {hoveredPlan && !showDateDetails && (
          <div
            className="fixed z-50 p-4 bg-white rounded-lg shadow-xl border border-gray-200 max-w-sm pointer-events-none"
            style={{
              left: `${mousePosition.x + 10}px`,
              top: `${mousePosition.y - 10}px`,
              transform: mousePosition.x > window.innerWidth - 350 ? 'translateX(-100%)' : 'none'
            }}
          >
            <div className="flex items-center gap-2 mb-3">
              <div className={`w-3 h-3 rounded-full ${getPlanStatusColor(hoveredPlan)}`}></div>
              <h4 className="font-bold text-gray-800">{hoveredPlan.name}</h4>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Clock size={14} className="text-gray-500" />
                <span className="text-gray-700">{formatExecuteTime(hoveredPlan.trigger)}</span>
              </div>

              <div className="flex items-center gap-2">
                {getPlanStatusIcon(hoveredPlan)}
                <span className="text-gray-700 capitalize">{hoveredPlan.status}</span>
              </div>

              {hoveredPlan.description && (
                <div className="text-gray-600 mt-2">
                  <span className="font-medium">描述：</span>
                  {hoveredPlan.description}
                </div>
              )}

              <div className="flex justify-between text-xs text-gray-500 mt-3 pt-2 border-t">
                <span>優先級: {hoveredPlan.priority}</span>
                <span>
                  成功率: {hoveredPlan.statistics.totalRuns > 0
                    ? Math.round((hoveredPlan.statistics.successRuns / hoveredPlan.statistics.totalRuns) * 100)
                    : 0}%
                </span>
              </div>
            </div>
          </div>
        )}

        {/* 圖例 */}
        <div className="mt-6 flex flex-wrap gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-gray-600">啟用中</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-gray-600">執行中</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span className="text-gray-600">錯誤</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-gray-400"></div>
            <span className="text-gray-600">停用</span>
          </div>
        </div>
      </div>

      {/* 日期詳情模態窗口 */}
      {showDateDetails && selectedDate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800">
                {selectedDate.toLocaleDateString('zh-TW', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  weekday: 'long'
                })}
              </h3>
              <button
                onClick={() => {
                  setShowDateDetails(false);
                  // 關閉整日詳情時，清除選中的日期
                  setSelectedDate(null);
                }}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                ✕
              </button>
            </div>

            <div className="space-y-3">
              {getPlansForDate(selectedDate).map((plan) => (
                <div
                  key={plan._id}
                  className="p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${getPlanStatusColor(plan)}`}></div>
                      <h4 className="font-medium text-gray-800">{plan.name}</h4>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      {getPlanStatusIcon(plan)}
                      <span className="capitalize">{plan.status}</span>
                    </div>
                  </div>

                  <div className="text-sm text-gray-600 mb-2">
                    <div className="flex items-center gap-1">
                      <Clock size={14} />
                      <span>{formatExecuteTime(plan.trigger)}</span>
                    </div>
                  </div>

                  {plan.description && (
                    <p className="text-sm text-gray-500 mb-2">{plan.description}</p>
                  )}

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>優先級: {plan.priority}</span>
                    <span>
                      成功率: {plan.statistics.totalRuns > 0
                        ? Math.round((plan.statistics.successRuns / plan.statistics.totalRuns) * 100)
                        : 0}%
                    </span>
                  </div>
                </div>
              ))}

              {getPlansForDate(selectedDate).length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Calendar size={48} className="mx-auto mb-2 opacity-50" />
                  <p>此日期沒有安排刷圖計畫</p>
                </div>
              )}
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={() => {
                  setShowDateDetails(false);
                  // 關閉整日詳情時，清除選中的日期
                  setSelectedDate(null);
                }}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                關閉
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
