# EPD Manager Token 安全優化指南

> **重要更新**: 經過重新評估，硬編碼密鑰僅用於開發環境，生產環境透過 Docker 配置已有基本保護。
> 本指南重點轉向系統配置化和管理便利性改善。

## 🎯 優化目標 (系統配置化)

### 1. 實作動態密鑰管理

#### 創建自動密鑰生成機制 `server/utils/keyManager.js`
```javascript
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class KeyManager {
  constructor() {
    this.secretPath = path.join(__dirname, '../config/jwt.secret');
    this.configPath = path.join(__dirname, '../config/security.json');
  }

  // 首次啟動自動生成密鑰
  initializeSecret() {
    if (!fs.existsSync(this.secretPath)) {
      const newSecret = crypto.randomBytes(64).toString('base64');

      // 確保目錄存在
      const dir = path.dirname(this.secretPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // 寫入密鑰檔案 (僅擁有者可讀)
      fs.writeFileSync(this.secretPath, newSecret, { mode: 0o600 });

      console.log('✅ 自動生成 JWT 密鑰');
      this.logSecurityEvent('JWT_SECRET_GENERATED', { auto: true });

      return newSecret;
    }

    return fs.readFileSync(this.secretPath, 'utf8');
  }

  // 刷新密鑰 (透過 Web 介面觸發)
  refreshSecret(adminUserId) {
    const oldSecret = this.getSecret();
    const newSecret = crypto.randomBytes(64).toString('base64');

    // 備份舊密鑰 (用於過渡期驗證)
    const backupPath = `${this.secretPath}.backup`;
    fs.writeFileSync(backupPath, oldSecret);

    // 寫入新密鑰
    fs.writeFileSync(this.secretPath, newSecret, { mode: 0o600 });

    this.logSecurityEvent('JWT_SECRET_REFRESHED', {
      adminUserId,
      timestamp: new Date()
    });

    console.log('🔄 JWT 密鑰已刷新');
    return newSecret;
  }

  getSecret() {
    if (process.env.JWT_SECRET) {
      return process.env.JWT_SECRET; // Docker 環境優先
    }

    if (fs.existsSync(this.secretPath)) {
      return fs.readFileSync(this.secretPath, 'utf8');
    }

    // 開發環境回退
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ 使用開發環境預設密鑰');
      return 'epd-manager-jwt-secret-key';
    }

    throw new Error('JWT_SECRET not found. Please run initialization.');
  }

  logSecurityEvent(event, details) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      details
    };

    const logPath = path.join(__dirname, '../logs/security.log');
    fs.appendFileSync(logPath, JSON.stringify(logEntry) + '\n');
  }
}

module.exports = new KeyManager();
```

### 2. 移除硬編碼密鑰

**需要修改的檔案**:

#### `server/utils/auth.js`
```javascript
// 修改前
const JWT_SECRET = process.env.JWT_SECRET || 'epd-manager-jwt-secret-key';

// 修改後
const JWT_SECRET = (() => {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET environment variable is required');
  }
  if (secret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }
  return secret;
})();
```

#### `server/utils/jwtUtils.js`
```javascript
// 修改前
const getJwtSecret = () => {
  return process.env.JWT_SECRET || 'your_jwt_secret_here';
};

// 修改後
const getJwtSecret = () => {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET environment variable is required');
  }
  if (secret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }
  return secret;
};
```

#### `server/index.js`
```javascript
// 修改前
const jwtSecret = process.env.JWT_SECRET || 'your_jwt_secret_here';

// 修改後
const jwtSecret = (() => {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('FATAL: JWT_SECRET environment variable is required');
    process.exit(1);
  }
  return secret;
})();
```

#### `server/routes/gatewayApi.js`
```javascript
// 修改前
let jwtSecret = 'your_jwt_secret';

// 修改後
let jwtSecret = null;

const setJwtSecret = (secret) => {
  if (!secret) {
    throw new Error('JWT secret is required');
  }
  jwtSecret = secret;
};

// 在使用前檢查
const getJwtSecret = () => {
  if (!jwtSecret) {
    throw new Error('JWT secret not initialized');
  }
  return jwtSecret;
};
```

### 3. 縮短 Gateway Token 有效期

#### `server/utils/jwtUtils.js`
```javascript
// 修改前
const generateGatewayToken = (gatewayInfo, expiresInSeconds = 30 * 24 * 60 * 60) => {

// 修改後
const generateGatewayToken = (gatewayInfo, expiresInSeconds = 24 * 60 * 60) => { // 24小時
```

#### `server/routes/gatewayApi.js`
```javascript
// 修改前
{ expiresIn: '30d' }

// 修改後
{ expiresIn: '24h' }
```

---

## ⚠️ 1週內執行 (高風險問題)

### 4. 加密資料庫 Token 存儲

#### 創建加密工具 `server/utils/encryption.js`
```javascript
const crypto = require('crypto');

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || crypto.randomBytes(32);
const IV_LENGTH = 16;

const encrypt = (text) => {
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipher('aes-256-cbc', ENCRYPTION_KEY);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
};

const decrypt = (text) => {
  const textParts = text.split(':');
  const iv = Buffer.from(textParts.shift(), 'hex');
  const encryptedText = textParts.join(':');
  const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY);
  let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

module.exports = { encrypt, decrypt };
```

#### 修改 Gateway API 存儲
```javascript
const { encrypt, decrypt } = require('../utils/encryption');

// 存儲時加密
const wsInfo = {
  url: wsUrl,
  path: wsPath,
  token: encrypt(token), // 加密存儲
  protocol: 'json'
};

// 讀取時解密
if (gateway.websocket && gateway.websocket.token) {
  gateway.websocket.token = decrypt(gateway.websocket.token);
}
```

### 5. 實作 Token 撤銷機制

#### 創建黑名單管理 `server/utils/tokenBlacklist.js`
```javascript
class TokenBlacklist {
  constructor() {
    this.blacklist = new Map();
  }
  
  revoke(tokenId, expiryTime) {
    this.blacklist.set(tokenId, expiryTime);
    console.log(`Token ${tokenId} revoked`);
  }
  
  isRevoked(tokenId) {
    return this.blacklist.has(tokenId);
  }
  
  cleanup() {
    const now = Math.floor(Date.now() / 1000);
    for (const [tokenId, expiryTime] of this.blacklist.entries()) {
      if (expiryTime < now) {
        this.blacklist.delete(tokenId);
      }
    }
  }
}

module.exports = new TokenBlacklist();
```

#### 修改認證中間件
```javascript
const tokenBlacklist = require('../utils/tokenBlacklist');

const authenticate = async (req, res, next) => {
  try {
    const token = getTokenFromRequest(req);
    const decoded = verifyToken(token);
    
    // 檢查是否被撤銷
    if (decoded.jti && tokenBlacklist.isRevoked(decoded.jti)) {
      return res.status(401).json({ error: 'Token has been revoked' });
    }
    
    // 其他驗證邏輯...
  } catch (error) {
    // 錯誤處理...
  }
};
```

---

## 🟡 1個月內執行 (中風險問題)

### 6. 改善前端 Token 存儲

#### 使用 sessionStorage 替代 localStorage
```typescript
// src/store/authStore.ts
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 狀態定義...
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => sessionStorage), // 改為 sessionStorage
    }
  )
);
```

#### 實作 Token 自動清理
```typescript
// src/utils/tokenManager.ts
class TokenManager {
  private static readonly TOKEN_KEY = 'auth_token';
  
  static setToken(token: string, expiryTime: number): void {
    sessionStorage.setItem(this.TOKEN_KEY, token);
    
    // 設置自動清理
    const delay = (expiryTime * 1000) - Date.now();
    if (delay > 0) {
      setTimeout(() => {
        this.clearToken();
      }, delay);
    }
  }
  
  static clearToken(): void {
    sessionStorage.removeItem(this.TOKEN_KEY);
  }
}
```

### 7. 實作 Token 自動刷新

#### 後端刷新端點
```javascript
// server/routes/authApi.js
router.post('/auth/refresh', authenticate, async (req, res) => {
  try {
    const { user } = req;
    
    // 生成新 Token
    const newToken = generateToken({ userId: user._id.toString() });
    
    // 設置新 Cookie
    res.cookie('token', newToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });
    
    res.json({
      token: newToken,
      expiresAt: Math.floor(Date.now() / 1000) + (2 * 60 * 60) // 2小時後過期
    });
  } catch (error) {
    res.status(500).json({ error: 'Token refresh failed' });
  }
});
```

#### 前端自動刷新
```typescript
// src/utils/tokenRefresh.ts
class TokenRefreshManager {
  private refreshTimer: NodeJS.Timeout | null = null;
  
  scheduleRefresh(expiryTime: number): void {
    const refreshTime = (expiryTime * 1000) - (5 * 60 * 1000); // 5分鐘前刷新
    const delay = refreshTime - Date.now();
    
    if (delay > 0) {
      this.refreshTimer = setTimeout(async () => {
        await this.refreshToken();
      }, delay);
    }
  }
  
  private async refreshToken(): Promise<void> {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include'
      });
      
      if (response.ok) {
        const data = await response.json();
        // 更新 Token 並重新排程
        TokenManager.setToken(data.token, data.expiresAt);
        this.scheduleRefresh(data.expiresAt);
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }
  }
}
```

---

## 🔧 驗證修復

### 1. 執行安全檢查腳本
```bash
node docs/plan/secure/security-check-script.js
```

### 2. 測試 Token 功能
```bash
# 測試登入
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"root","password":"12345689"}'

# 測試認證
curl -X GET http://localhost:3001/api/auth/check \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 檢查環境變數
```bash
# 確認密鑰已設置
echo $JWT_SECRET | wc -c  # 應該 > 32

# 檢查 Docker 環境
docker exec epd-manager env | grep JWT_SECRET
```

### 4. 監控日誌
```bash
# 檢查是否有錯誤
docker logs epd-manager | grep -i "jwt\|token\|auth"

# 檢查安全事件
tail -f server/logs/security.log
```

---

## 📋 修復檢查清單

### 立即修復 ✅
- [ ] 生成並設置強 JWT 密鑰
- [ ] 移除所有硬編碼預設密鑰
- [ ] 縮短 Gateway Token 有效期至 24 小時
- [ ] 添加密鑰驗證邏輯

### 1週內修復 ✅
- [ ] 實作 Token 加密存儲
- [ ] 建立 Token 撤銷機制
- [ ] 加強認證中間件安全檢查
- [ ] 實作安全事件日誌

### 1個月內修復 ✅
- [ ] 改善前端 Token 存儲方式
- [ ] 實作 Token 自動刷新機制
- [ ] 建立安全監控系統
- [ ] 進行全面安全測試

### 驗證步驟 ✅
- [ ] 執行安全檢查腳本
- [ ] 測試所有認證功能
- [ ] 確認環境變數配置
- [ ] 監控系統日誌

---

**重要提醒**: 
1. 修復前請備份系統
2. 在測試環境先驗證修改
3. 修復後重新啟動所有服務
4. 通知所有用戶重新登入
