import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Save, Calendar, Target, Clock, Settings } from 'lucide-react';
import { RefreshPlan, CreatePlanRequest } from '../types/refreshPlan';
import { refreshPlanApi, planConfigApi, handleApiError } from '../services/refreshPlanApi';
import { Store } from '../types/store';

// 文字截斷工具函數
const truncateText = (text: string, maxLength: number = 10): string => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

interface EditRefreshPlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  store: Store;
  plan: RefreshPlan;
  onSuccess: () => void;
}

export const EditRefreshPlanModal: React.FC<EditRefreshPlanModalProps> = ({
  isOpen,
  onClose,
  store,
  plan,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [nameError, setNameError] = useState<string | null>(null);
  const [isCheckingName, setIsCheckingName] = useState(false);
  
  // 表單數據
  const [formData, setFormData] = useState<CreatePlanRequest>({
    name: '',
    description: '',
    enabled: true,
    priority: 'medium',
    trigger: {
      type: 'once',
      executeTime: '09:00',
      executeDate: '',
      weekDays: []
    },
    targetSelection: {
      type: 'mac_addresses',
      macAddresses: [],
      storeDataIds: []
    },
    execution: {
      useSystemSettings: true
    }
  });

  // 可選數據
  const [devices, setDevices] = useState<any[]>([]);
  const [storeDataList, setStoreDataList] = useState<any[]>([]);

  // 搜尋狀態
  const [deviceSearchTerm, setDeviceSearchTerm] = useState('');
  const [storeDataSearchTerm, setStoreDataSearchTerm] = useState('');

  // 初始化表單數據
  useEffect(() => {
    if (isOpen && plan) {
      console.log('初始化編輯表單數據:', plan);
      setFormData({
        name: plan.name,
        description: plan.description || '',
        enabled: plan.enabled,
        priority: plan.priority || 'medium',
        trigger: {
          type: plan.trigger.type,
          executeTime: plan.trigger.executeTime,
          executeDate: plan.trigger.executeDate || '',
          weekDays: plan.trigger.weekDays ? [...plan.trigger.weekDays] : []
        },
        targetSelection: {
          type: plan.targetSelection.type,
          macAddresses: plan.targetSelection.macAddresses ? [...plan.targetSelection.macAddresses] : [],
          storeDataIds: plan.targetSelection.storeDataIds ? [...plan.targetSelection.storeDataIds] : []
        },
        execution: plan.execution || {
          useSystemSettings: true
        }
      });
      loadSelectOptions();
    }
  }, [isOpen, plan]);

  // 檢查名稱是否重複
  const checkNameDuplicate = async (name: string) => {
    if (!name || name.trim() === '' || name.trim() === plan.name) {
      setNameError(null);
      return;
    }

    try {
      setIsCheckingName(true);
      const response = await refreshPlanApi.checkPlanName(store.id, name.trim(), plan._id);

      if (response.success && response.isDuplicate) {
        setNameError('計畫名稱已存在，請使用其他名稱');
      } else {
        setNameError(null);
      }
    } catch (err) {
      console.error('檢查名稱重複失敗:', err);
      setNameError('檢查名稱失敗，請稍後再試');
    } finally {
      setIsCheckingName(false);
    }
  };

  // 載入選項數據
  const loadSelectOptions = async () => {
    try {
      const [devicesResult, storeDataResult] = await Promise.all([
        planConfigApi.getStoreDevices(store.id),
        planConfigApi.getStoreData(store.id)
      ]);

      if (devicesResult.success) {
        setDevices(devicesResult.data);
      }

      if (storeDataResult.success) {
        setStoreDataList(storeDataResult.data);
      }
    } catch (err) {
      console.error('載入選項數據失敗:', err);
    }
  };

  // 處理表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // 檢查名稱是否重複
      if (nameError) {
        throw new Error(nameError);
      }

      // 如果名稱有變更，再次檢查名稱重複
      if (formData.name.trim() !== plan.name) {
        const nameCheckResponse = await refreshPlanApi.checkPlanName(store.id, formData.name.trim(), plan._id);
        if (nameCheckResponse.success && nameCheckResponse.isDuplicate) {
          throw new Error('計畫名稱已存在，請使用其他名稱');
        }
      }

      console.log('提交編輯表單數據:', formData);
      await refreshPlanApi.updatePlan(store.id, plan._id, formData);
      onSuccess();
      onClose();
    } catch (err) {
      console.error('編輯計畫失敗:', err);
      setError(handleApiError(err));
    } finally {
      setLoading(false);
    }
  };

  // 處理輸入變更
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 處理觸發器變更
  const handleTriggerChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      trigger: {
        ...prev.trigger,
        [field]: value
      }
    }));
  };

  // 處理目標選擇變更
  const handleTargetChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      targetSelection: {
        ...prev.targetSelection,
        [field]: value
      }
    }));
  };

  // 過濾設備選項
  const filteredDevices = devices.filter(device => {
    if (!deviceSearchTerm) return true;
    const searchLower = deviceSearchTerm.toLowerCase();
    return (
      device.macAddress.toLowerCase().includes(searchLower) ||
      (device.code && device.code.toLowerCase().includes(searchLower)) ||
      (device.note && device.note.toLowerCase().includes(searchLower))
    );
  });

  // 過濾門店數據選項
  const filteredStoreDataList = storeDataList.filter(storeData => {
    if (!storeDataSearchTerm) return true;
    const searchLower = storeDataSearchTerm.toLowerCase();
    return storeData.id.toLowerCase().includes(searchLower);
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* 標題欄 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Calendar className="w-6 h-6 text-blue-500 mr-2" />
            <h2 className="text-xl font-semibold text-gray-900">編輯刷圖計畫</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 表單內容 */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* 錯誤提示 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              基本信息
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                計畫名稱 *
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => {
                    handleInputChange('name', e.target.value);
                    // 清除名稱錯誤，等待失去焦點時重新檢查
                    if (nameError) {
                      setNameError(null);
                    }
                  }}
                  onBlur={(e) => {
                    checkNameDuplicate(e.target.value);
                  }}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                    nameError
                      ? 'border-red-500 focus:ring-red-500'
                      : 'border-gray-300 focus:ring-blue-500'
                  }`}
                  required
                  disabled={isCheckingName}
                />
                {isCheckingName && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                  </div>
                )}
              </div>
              {nameError && (
                <p className="mt-1 text-sm text-red-600">{nameError}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                描述
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="enabled"
                checked={formData.enabled}
                onChange={(e) => handleInputChange('enabled', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="enabled" className="ml-2 block text-sm text-gray-900">
                啟用計畫
              </label>
            </div>
          </div>

          {/* 執行時間 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              執行時間
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                執行類型
              </label>
              <select
                value={formData.trigger.type}
                onChange={(e) => handleTriggerChange('type', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="once">單次執行</option>
                <option value="daily">每日執行</option>
                <option value="weekly">每週執行</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                執行時間
              </label>
              <input
                type="time"
                value={formData.trigger.executeTime}
                onChange={(e) => handleTriggerChange('executeTime', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            {formData.trigger.type === 'once' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  執行日期
                </label>
                <input
                  type="date"
                  value={formData.trigger.executeDate}
                  onChange={(e) => handleTriggerChange('executeDate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            )}

            {formData.trigger.type === 'weekly' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  執行星期
                </label>
                <div className="flex flex-wrap gap-2">
                  {['週日', '週一', '週二', '週三', '週四', '週五', '週六'].map((day, index) => (
                    <label key={index} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.trigger.weekDays?.includes(index) || false}
                        onChange={(e) => {
                          const weekDays = formData.trigger.weekDays || [];
                          if (e.target.checked) {
                            handleTriggerChange('weekDays', [...weekDays, index]);
                          } else {
                            handleTriggerChange('weekDays', weekDays.filter(d => d !== index));
                          }
                        }}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-1 text-sm text-gray-700">{day}</span>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 目標選擇 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Target className="w-5 h-5 mr-2" />
              目標選擇
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                選擇類型
              </label>
              <select
                value={formData.targetSelection.type}
                onChange={(e) => handleTargetChange('type', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="mac_addresses">MAC 地址</option>
                <option value="store_data">門店數據</option>
              </select>
            </div>

            {formData.targetSelection.type === 'mac_addresses' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  選擇設備 ({devices.length} 個可用設備)
                </label>
                {devices.length === 0 ? (
                  <div className="text-center py-4 text-gray-500 border border-gray-300 rounded-md">
                    沒有找到設備，請確認門店中有設備數據
                  </div>
                ) : (
                  <div className="space-y-2">
                    {/* 搜尋框 */}
                    <input
                      type="text"
                      placeholder="搜尋設備 (MAC地址、編號、備註)"
                      value={deviceSearchTerm}
                      onChange={(e) => setDeviceSearchTerm(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />

                    <div className="border border-gray-300 rounded-md">
                      {/* 全選控制 */}
                      <div className="p-2 border-b border-gray-200 flex items-center justify-between bg-gray-50">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={filteredDevices.length > 0 && filteredDevices.every(device => formData.targetSelection.macAddresses?.includes(device.macAddress))}
                            onChange={(e) => {
                              const currentMacAddresses = formData.targetSelection.macAddresses || [];
                              const filteredMacAddresses = filteredDevices.map(device => device.macAddress);

                              if (e.target.checked) {
                                // 全選當前過濾的設備，保留不在過濾結果中的已選項目
                                const existingNonFiltered = currentMacAddresses.filter(mac => !filteredMacAddresses.includes(mac));
                                handleTargetChange('macAddresses', [...existingNonFiltered, ...filteredMacAddresses]);
                              } else {
                                // 取消選擇當前過濾的設備，保留不在過濾結果中的已選項目
                                const remainingMacAddresses = currentMacAddresses.filter(mac => !filteredMacAddresses.includes(mac));
                                handleTargetChange('macAddresses', remainingMacAddresses);
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                          />
                          <span className="text-sm font-medium">全選</span>
                        </label>
                        <span className="text-sm text-gray-500">
                          已選擇: {formData.targetSelection.macAddresses?.length || 0} / {filteredDevices.length}
                        </span>
                      </div>

                      {/* 設備列表 */}
                      <div className="max-h-40 overflow-y-auto p-2">
                        {filteredDevices.length === 0 ? (
                          <div className="text-center py-4 text-gray-500">
                            沒有找到符合條件的設備
                          </div>
                        ) : (
                          filteredDevices.map((device) => (
                            <label key={device.id} className="flex items-center p-2 hover:bg-gray-50 rounded">
                              <input
                                type="checkbox"
                                checked={formData.targetSelection.macAddresses?.includes(device.macAddress) || false}
                                onChange={(e) => {
                                  const macAddresses = formData.targetSelection.macAddresses || [];
                                  if (e.target.checked) {
                                    handleTargetChange('macAddresses', [...macAddresses, device.macAddress]);
                                  } else {
                                    handleTargetChange('macAddresses', macAddresses.filter(mac => mac !== device.macAddress));
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                              />
                              <span
                                className="text-sm text-gray-700"
                                title={`${device.macAddress}-(${device.code || ''})-(${device.note || ''})`}
                              >
                                {device.macAddress}-({truncateText(device.code || '', 8)})-({truncateText(device.note || '', 8)})
                              </span>
                            </label>
                          ))
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {formData.targetSelection.type === 'store_data' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  選擇門店數據 ({storeDataList.length} 個可用數據)
                </label>
                {storeDataList.length === 0 ? (
                  <div className="text-center py-4 text-gray-500 border border-gray-300 rounded-md">
                    沒有找到門店數據，請先在門店設置中創建門店專屬資料
                  </div>
                ) : (
                  <div className="space-y-2">
                    {/* 搜尋框 */}
                    <input
                      type="text"
                      placeholder="搜尋門店數據 (ID)"
                      value={storeDataSearchTerm}
                      onChange={(e) => setStoreDataSearchTerm(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />

                    <div className="border border-gray-300 rounded-md">
                      {/* 全選控制 */}
                      <div className="p-2 border-b border-gray-200 flex items-center justify-between bg-gray-50">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={filteredStoreDataList.length > 0 && filteredStoreDataList.every(storeData => formData.targetSelection.storeDataIds?.includes(storeData.id))}
                            onChange={(e) => {
                              const currentStoreDataIds = formData.targetSelection.storeDataIds || [];
                              const filteredStoreDataIds = filteredStoreDataList.map(storeData => storeData.id);

                              if (e.target.checked) {
                                // 全選當前過濾的門店數據，保留不在過濾結果中的已選項目
                                const existingNonFiltered = currentStoreDataIds.filter(id => !filteredStoreDataIds.includes(id));
                                handleTargetChange('storeDataIds', [...existingNonFiltered, ...filteredStoreDataIds]);
                              } else {
                                // 取消選擇當前過濾的門店數據，保留不在過濾結果中的已選項目
                                const remainingStoreDataIds = currentStoreDataIds.filter(id => !filteredStoreDataIds.includes(id));
                                handleTargetChange('storeDataIds', remainingStoreDataIds);
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                          />
                          <span className="text-sm font-medium">全選</span>
                        </label>
                        <span className="text-sm text-gray-500">
                          已選擇: {formData.targetSelection.storeDataIds?.length || 0} / {filteredStoreDataList.length}
                        </span>
                      </div>

                      {/* 門店數據列表 */}
                      <div className="max-h-40 overflow-y-auto p-2">
                        {filteredStoreDataList.length === 0 ? (
                          <div className="text-center py-4 text-gray-500">
                            沒有找到符合條件的門店數據
                          </div>
                        ) : (
                          filteredStoreDataList.map((storeData) => (
                            <label key={storeData.id} className="flex items-center p-2 hover:bg-gray-50 rounded">
                              <input
                                type="checkbox"
                                checked={formData.targetSelection.storeDataIds?.includes(storeData.id) || false}
                                onChange={(e) => {
                                  const storeDataIds = formData.targetSelection.storeDataIds || [];
                                  if (e.target.checked) {
                                    handleTargetChange('storeDataIds', [...storeDataIds, storeData.id]);
                                  } else {
                                    handleTargetChange('storeDataIds', storeDataIds.filter(id => id !== storeData.id));
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                              />
                              <span className="text-sm text-gray-700">
                                {storeData.id} ({storeData.boundDeviceCount} 個設備)
                              </span>
                            </label>
                          ))
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 操作按鈕 */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <Save className="w-4 h-4 mr-2" />
              {loading ? '保存中...' : '保存'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditRefreshPlanModal;
