import React from 'react';
import { DisplayColorType } from '../../types';

interface ColorTypeGradientProps {
  colorType: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * 顯示顏色類型的漸層圖示組件
 *
 * @param colorType 顏色類型，如 "Gray16", "Black & White & Red" 等
 * @param size 圖示大小，可選 'sm'(小), 'md'(中), 'lg'(大)，默認為 'md'
 */
export const ColorTypeGradient: React.FC<ColorTypeGradientProps> = ({
  colorType,
  size = 'md'
}) => {
  // 根據大小設置尺寸 - 調整為填滿格子
  const dimensions = {
    sm: { width: '100%', height: 20 },
    md: { width: '100%', height: 24 },
    lg: { width: '100%', height: 28 }
  };

  const { width, height } = dimensions[size];

  // 標準化顏色類型字符串以進行比較
  const normalizedColorType = colorType?.toUpperCase() || '';

  // 檢查是否為未知顏色類型
  if (!colorType || normalizedColorType === 'UNKNOWN' || normalizedColorType === '未指定') {
    // 對於未知顏色類型，只顯示紅色文字提示，不顯示漸層圖
    return (
      <span className="text-red-500 font-medium">未知顏色類型</span>
    );
  }

  // 根據顏色類型設置漸層
  let gradient = '';

  // 添加調試信息
  console.log('ColorTypeGradient 接收到的顏色類型:', colorType);
  console.log('標準化後的顏色類型:', normalizedColorType);

  // 檢查是否包含 BWRY 關鍵字（優先檢查，因為它包含 BWR）
  if (normalizedColorType.includes('BWRY') ||
      normalizedColorType.includes('BLACK & WHITE & RED & YELLOW') ||
      normalizedColorType === DisplayColorType.BWRY) {
    // BWRY - 黑白紅黃四色 - 修正為四等分的黑白紅黃
    gradient = 'linear-gradient(to right, #FFFFFF 0%, #FFFFFF 25%, #000000 25%, #000000 50%, #FF0000 50%, #FF0000 75%, #FFFF00 75%, #FFFF00 100%)';
  } else if (normalizedColorType.includes('GRAY16') || normalizedColorType === 'BW' || normalizedColorType === DisplayColorType.BW) {
    // GRAY16 - 從白色到黑色的16階漸層，使用明確的階梯式漸層
    gradient = 'linear-gradient(to right, ' +
      '#FFFFFF 0%, #FFFFFF 6.25%, ' +   // 1/16
      '#EEEEEE 6.25%, #EEEEEE 12.5%, ' + // 2/16
      '#DDDDDD 12.5%, #DDDDDD 18.75%, ' + // 3/16
      '#CCCCCC 18.75%, #CCCCCC 25%, ' + // 4/16
      '#BBBBBB 25%, #BBBBBB 31.25%, ' + // 5/16
      '#AAAAAA 31.25%, #AAAAAA 37.5%, ' + // 6/16
      '#999999 37.5%, #999999 43.75%, ' + // 7/16
      '#888888 43.75%, #888888 50%, ' + // 8/16
      '#777777 50%, #777777 56.25%, ' + // 9/16
      '#666666 56.25%, #666666 62.5%, ' + // 10/16
      '#555555 62.5%, #555555 68.75%, ' + // 11/16
      '#444444 68.75%, #444444 75%, ' + // 12/16
      '#333333 75%, #333333 81.25%, ' + // 13/16
      '#222222 81.25%, #222222 87.5%, ' + // 14/16
      '#111111 87.5%, #111111 93.75%, ' + // 15/16
      '#000000 93.75%, #000000 100%)'; // 16/16
  } else if (normalizedColorType.includes('BWR') ||
             normalizedColorType.includes('BLACK & WHITE & RED') ||
             normalizedColorType === DisplayColorType.BWR) {
    // BWR - 黑白紅三色
    gradient = 'linear-gradient(to right, #FFFFFF 0%, #FFFFFF 33%, #000000 33%, #000000 66%, #FF0000 66%, #FF0000 100%)';
  } else if (normalizedColorType.includes('ALL COLORS') || normalizedColorType === 'ALL' || normalizedColorType === DisplayColorType.ALL) {
    // ALL - 彩色漸層
    gradient = 'linear-gradient(to right, #FF0000, #FF7F00, #FFFF00, #00FF00, #0000FF, #4B0082, #9400D3)';
  } else {
    // 其他未識別的顏色類型 - 顯示紅色文字提示
    return (
      <span className="text-red-500 font-medium">{colorType} (未識別的顏色類型)</span>
    );
  }

  return (
    <div
      className="rounded-sm border border-gray-300 shadow-sm w-full"
      style={{
        width: width,
        height: `${height}px`,
        background: gradient
      }}
      title={colorType}
    />
  );
};

export default ColorTypeGradient;
