# AI助手API文檔

## 概述

本文檔描述了AI助手功能的API接口、使用方法和配置指南。

## API端點

### 1. AI配置管理

#### 1.1 獲取AI配置
```http
GET /api/configs/ai-config
Authorization: Bearer <token>
```

**響應示例:**
```json
{
  "geminiApiKey": "AIza***",
  "enabled": true,
  "model": "gemini-pro",
  "maxTokens": 2048,
  "temperature": 0.7,
  "timeout": 30000
}
```

#### 1.2 更新AI配置
```http
PUT /api/configs/ai-config
Authorization: Bearer <token>
Content-Type: application/json

{
  "geminiApiKey": "AIzaSyC...",
  "enabled": true,
  "model": "gemini-pro",
  "maxTokens": 2048,
  "temperature": 0.7,
  "timeout": 30000
}
```

### 2. AI對話接口

#### 2.1 發送消息
```http
POST /api/ai-assistant/chat
Authorization: Bearer <token>
Content-Type: application/json

{
  "message": "幫我創建一個名為'台北101店'的門店",
  "context": {
    "previousMessages": [],
    "currentPage": "/stores",
    "userPreferences": {}
  }
}
```

**響應示例:**
```json
{
  "intent": "create_store",
  "confidence": 0.95,
  "extractedData": {
    "name": "台北101店",
    "id": "taipei-101-store",
    "address": "",
    "phone": ""
  },
  "missingFields": ["address", "phone"],
  "response": "我理解您要創建名為'台北101店'的門店。為了完成創建，我還需要以下信息：\n\n1. 門店地址\n2. 聯繫電話\n\n請提供這些信息，我將為您完成門店創建。",
  "executionResult": null
}
```

#### 2.2 測試AI連接
```http
POST /api/ai-assistant/test
Authorization: Bearer <token>
```

**響應示例:**
```json
{
  "success": true,
  "message": "AI服務連接正常",
  "response": {
    "intent": "test",
    "confidence": 1.0,
    "response": "AI服務運行正常"
  }
}
```

### 3. 系統狀態監控

#### 3.1 獲取AI系統狀態
```http
GET /api/ai-assistant/status
Authorization: Bearer <token>
```

**響應示例:**
```json
{
  "mcp": {
    "tools": ["create_store", "create_template", "create_user"],
    "sessions": 3
  },
  "agents": {
    "StoreAgent": {
      "name": "StoreAgent",
      "status": "idle",
      "capabilities": ["create_store", "update_store", "delete_store"],
      "currentTask": null
    },
    "TemplateAgent": {
      "name": "TemplateAgent", 
      "status": "busy",
      "capabilities": ["create_template", "update_template"],
      "currentTask": {
        "type": "create_template",
        "startTime": 1703123456789
      }
    }
  },
  "timestamp": 1703123456789
}
```

## 使用指南

### 1. 配置AI助手

#### 1.1 獲取Gemini API Key
1. 訪問 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登入Google帳號
3. 創建新的API Key
4. 複製API Key

#### 1.2 在系統中配置
1. 登入系統管理界面
2. 進入「系統配置」→「AI設定」
3. 輸入Gemini API Key
4. 調整其他參數（可選）
5. 點擊「測試連接」確認配置正確
6. 保存配置

### 2. 使用AI助手

#### 2.1 開啟AI助手
1. 點擊右下角的懸浮球
2. 選擇「AI助手」按鈕
3. 在對話框中輸入需求

#### 2.2 創建門店示例
```
用戶輸入: "幫我創建一個台北的門店"

AI回應: "我可以幫您創建門店。請提供以下信息：
1. 門店名稱
2. 門店地址  
3. 聯繫電話
4. 管理員（可選，默認為您）"

用戶輸入: "門店名稱是台北101店，地址是台北市信義區信義路五段7號，電話是02-8101-8800"

AI回應: "好的，我為您整理的門店信息如下：
- 門店名稱：台北101店
- 門店ID：taipei-101-store-1703123456
- 地址：台北市信義區信義路五段7號
- 電話：02-8101-8800
- 管理員：當前用戶

確認創建嗎？"

用戶輸入: "確認"

AI回應: "門店創建成功！門店'台北101店'已成功創建，您可以在門店管理頁面查看詳細信息。"
```

#### 2.3 創建模板示例
```
用戶輸入: "創建一個2.9寸黑白橫向的價格標籤模板"

AI回應: "我將為您創建模板，信息如下：
- 模板名稱：價格標籤模板
- 螢幕尺寸：2.9寸 (296x128)
- 顏色類型：黑白
- 方向：橫向

確認創建嗎？"

用戶輸入: "確認，模板名稱改為'商品價格標籤'"

AI回應: "模板'商品價格標籤'創建成功！您可以在模板管理頁面進一步編輯模板內容。"
```

### 3. 高級功能

#### 3.1 批量操作
```
用戶輸入: "幫我創建3個門店：台北店、台中店、高雄店，地址我稍後提供"

AI回應: "我理解您要創建3個門店。我將為每個門店創建基本信息，您可以稍後補充地址：

1. 台北店 (ID: taipei-store-xxx)
2. 台中店 (ID: taichung-store-xxx)  
3. 高雄店 (ID: kaohsiung-store-xxx)

確認創建嗎？"
```

#### 3.2 智能建議
```
用戶輸入: "我需要為服裝店創建一個模板"

AI回應: "為服裝店創建模板，我建議：

1. 螢幕尺寸：4.2寸 (400x300) - 適合顯示商品信息
2. 顏色類型：三色 (黑白紅) - 突出價格和促銷信息
3. 方向：縱向 - 適合服裝標籤

模板可包含：
- 商品名稱
- 價格
- 尺寸
- 品牌
- 促銷信息

是否按此建議創建？"
```

## 錯誤處理

### 常見錯誤碼

| 錯誤碼 | 說明 | 解決方案 |
|--------|------|----------|
| `AI_NOT_CONFIGURED` | AI未配置 | 在系統設定中配置Gemini API Key |
| `INVALID_API_KEY` | API Key無效 | 檢查API Key是否正確 |
| `QUOTA_EXCEEDED` | API配額超限 | 檢查Gemini API使用量 |
| `PERMISSION_DENIED` | 權限不足 | 確認用戶有相應操作權限 |
| `VALIDATION_FAILED` | 參數驗證失敗 | 檢查輸入參數格式 |

### 錯誤響應格式
```json
{
  "intent": "error",
  "response": "抱歉，處理您的請求時發生錯誤：API Key無效",
  "error": {
    "code": "INVALID_API_KEY",
    "message": "提供的Gemini API Key無效或已過期",
    "details": {
      "timestamp": 1703123456789,
      "requestId": "req_123456"
    }
  }
}
```

## 性能優化

### 1. 請求優化
- 使用適當的`maxTokens`限制響應長度
- 調整`temperature`參數控制創造性
- 設置合理的`timeout`避免長時間等待

### 2. 緩存策略
- 常用查詢結果緩存
- 用戶偏好設定緩存
- 模板建議緩存

### 3. 監控指標
- API響應時間
- 成功率統計
- 用戶滿意度
- 資源使用量

## 安全考量

### 1. API Key保護
- 加密存儲API Key
- 定期輪換API Key
- 限制API Key權限範圍

### 2. 輸入驗證
- 過濾惡意輸入
- 限制請求頻率
- 驗證用戶權限

### 3. 數據隱私
- 不向AI發送敏感數據
- 本地處理優先
- 符合隱私法規

## 開發指南

### 1. 擴展新功能
1. 創建新的MCP工具
2. 實現對應的A2A代理
3. 更新AI提示詞
4. 添加測試用例

### 2. 自定義代理
```javascript
// 創建自定義代理
class CustomAgent extends BaseAgent {
  constructor() {
    super('CustomAgent', ['custom_task']);
  }

  async executeTask(task) {
    // 實現自定義邏輯
    return { success: true, data: result };
  }
}

// 註冊代理
agentManager.registerAgent(new CustomAgent());
```

### 3. 添加新工具
```javascript
// 創建新工具
class CustomTool {
  constructor() {
    this.name = 'custom_tool';
    this.description = '自定義工具';
  }

  async execute(params, context) {
    // 實現工具邏輯
    return result;
  }
}

// 註冊工具
mcpServer.registerTool('custom_tool', new CustomTool());
```

這個API文檔提供了完整的使用指南，包括配置、使用示例、錯誤處理和開發指南，幫助用戶和開發者更好地使用和擴展AI助手功能。
