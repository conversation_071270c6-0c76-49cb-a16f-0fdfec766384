import React from 'react';
import { TemplateElement, DisplayColorType } from '../../../types';
import { getAllIconTypes, IconType, Icon } from '../../editor/elements/IconComponent';
import { RestrictedColorInput } from './FormComponents';

interface IconPropertiesProps {
  element: TemplateElement;
  updateElement: (updates: Partial<TemplateElement>) => void;
  colorType?: string | DisplayColorType; // 新增：模板的顏色類型
}

export const IconProperties: React.FC<IconPropertiesProps> = ({
  element,
  updateElement,
  colorType
}) => {
  // 只有當元素類型為圖標時才顯示
  if (element.type !== 'icon') {
    return null;
  }

  // 獲取所有可用的圖標類型
  const iconTypes = getAllIconTypes();

  // 處理圖標類型變更
  const handleIconTypeChange = (iconType: IconType) => {
    updateElement({ iconType });
  };

  return (
    <div className="mb-6">
      <h4 className="text-sm font-medium mb-2 pb-1 border-b border-gray-700">圖標選項</h4>

      <div className="mb-4">
        <p className="text-xs text-gray-400 mb-2">圖標類型</p>
        <div className="grid grid-cols-6 gap-2">
          {iconTypes.map((iconType) => (
            <div
              key={iconType}
              onClick={() => handleIconTypeChange(iconType)}
              className={`p-2 rounded flex items-center justify-center cursor-pointer ${
                element.iconType === iconType
                  ? 'bg-blue-600'
                  : 'bg-gray-700 hover:bg-gray-600'
              }`}
              title={iconType}
            >
              <Icon
                iconType={iconType}
                size={20}
                color="white"
              />
            </div>
          ))}
        </div>
      </div>

      <div className="mb-4">
        <label className="text-xs text-gray-400 block mb-1">線條粗細</label>
        <div className="flex items-center">
          <input
            type="range"
            min="0.5"
            max="5"
            step="0.5"
            value={element.lineWidth || 2}
            onChange={(e) => updateElement({ lineWidth: parseFloat(e.target.value) })}
            className="w-3/4 mr-2"
          />
          <span className="text-xs">{element.lineWidth || 2}</span>
        </div>
      </div>

      <div className="mb-4">
        <label className="text-xs text-gray-400 block mb-1">圖標顏色</label>
        <RestrictedColorInput
          value={element.lineColor || '#000000'}
          onChange={(value) => updateElement({ lineColor: value })}
          colorType={colorType}
        />
      </div>
    </div>
  );
};