# 測試計畫

## 1. 功能測試

### 1.1 基礎功能測試

#### 1.1.1 WebSocket連接測試
```javascript
// 測試WebSocket連接建立
describe('WebSocket連接測試', () => {
  test('前端客戶端應該能成功連接到WebSocket服務', async () => {
    const client = new WebSocketClient();
    await waitForConnection(client);
    expect(client.isConnected()).toBe(true);
  });

  test('網關設備應該能成功連接到WebSocket服務', async () => {
    const gatewayWs = new WebSocket(gatewayWsUrl);
    await waitForOpen(gatewayWs);
    expect(gatewayWs.readyState).toBe(WebSocket.OPEN);
  });

  test('連接斷開後應該自動重連', async () => {
    const client = new WebSocketClient();
    await waitForConnection(client);
    
    // 模擬連接斷開
    client.ws.close();
    
    // 等待重連
    await waitForReconnection(client);
    expect(client.isConnected()).toBe(true);
  });
});
```

#### 1.1.2 設備狀態訂閱測試
```javascript
describe('設備狀態訂閱測試', () => {
  test('應該能成功訂閱特定門店的設備狀態', async () => {
    const client = getWebSocketClient();
    const storeId = 'test-store-1';
    
    let subscriptionAck = null;
    client.addEventListener((event) => {
      if (event.type === 'device_status_subscription_ack') {
        subscriptionAck = event;
      }
    });

    client.subscribeDeviceStatus(storeId);
    
    await waitFor(() => subscriptionAck !== null);
    expect(subscriptionAck.storeId).toBe(storeId);
    expect(subscriptionAck.subscribed).toBe(true);
  });

  test('應該能取消設備狀態訂閱', async () => {
    const client = getWebSocketClient();
    const storeId = 'test-store-1';
    
    // 先訂閱
    client.subscribeDeviceStatus(storeId);
    await waitForSubscription(client, storeId);
    
    // 再取消訂閱
    client.unsubscribeDeviceStatus(storeId);
    
    // 驗證訂閱已取消
    expect(client.subscribedStores.has(storeId)).toBe(false);
  });
});
```

#### 1.1.3 設備狀態更新測試
```javascript
describe('設備狀態更新測試', () => {
  test('設備上線時應該推送狀態更新', async () => {
    const storeId = 'test-store-1';
    const deviceMac = 'AA:BB:CC:DD:EE:01';
    
    // 訂閱設備狀態
    const client = getWebSocketClient();
    let receivedUpdate = null;
    
    client.addDeviceStatusListener((event) => {
      if (event.storeId === storeId) {
        receivedUpdate = event;
      }
    });
    
    client.subscribeDeviceStatus(storeId);
    await waitForSubscription(client, storeId);
    
    // 模擬設備上線
    await simulateDeviceOnline(storeId, deviceMac);
    
    // 驗證收到狀態更新
    await waitFor(() => receivedUpdate !== null);
    expect(receivedUpdate.devices).toHaveLength(1);
    expect(receivedUpdate.devices[0].macAddress).toBe(deviceMac);
    expect(receivedUpdate.devices[0].status).toBe('online');
  });

  test('設備離線時應該推送狀態更新', async () => {
    const storeId = 'test-store-1';
    const deviceMac = 'AA:BB:CC:DD:EE:01';
    
    // 先讓設備上線
    await simulateDeviceOnline(storeId, deviceMac);
    
    // 訂閱設備狀態
    const client = getWebSocketClient();
    let receivedUpdate = null;
    
    client.addDeviceStatusListener((event) => {
      if (event.storeId === storeId && 
          event.devices.some(d => d.status === 'offline')) {
        receivedUpdate = event;
      }
    });
    
    client.subscribeDeviceStatus(storeId);
    
    // 模擬設備離線（通過定時檢查）
    await simulateDeviceOffline(deviceMac);
    
    // 驗證收到離線狀態更新
    await waitFor(() => receivedUpdate !== null);
    expect(receivedUpdate.devices[0].status).toBe('offline');
  });

  test('圖片更新狀態變更應該推送更新', async () => {
    const storeId = 'test-store-1';
    const deviceId = 'device-test-1';

    const client = getWebSocketClient();
    let receivedUpdate = null;

    client.addDeviceStatusListener((event) => {
      if (event.devices.some(d => d.updatedFields.includes('imageUpdateStatus'))) {
        receivedUpdate = event;
      }
    });

    client.subscribeDeviceStatus(storeId);

    // 模擬圖片更新狀態變更
    await updateDeviceImageStatus(deviceId, '已更新');

    // 驗證收到圖片狀態更新
    await waitFor(() => receivedUpdate !== null);
    expect(receivedUpdate.devices[0].imageUpdateStatus).toBe('已更新');
  });
});
```

#### 1.1.4 網關狀態更新測試
```javascript
describe('網關狀態更新測試', () => {
  test('網關連接時應該推送狀態更新', async () => {
    const storeId = 'test-store-1';
    const gatewayId = 'gateway-test-1';

    // 訂閱網關狀態
    const client = getWebSocketClient();
    let receivedUpdate = null;

    client.addGatewayStatusListener((event) => {
      if (event.storeId === storeId) {
        receivedUpdate = event;
      }
    });

    client.subscribeGatewayStatus(storeId);
    await waitForSubscription(client, storeId);

    // 模擬網關連接
    await simulateGatewayConnection(storeId, gatewayId);

    // 驗證收到狀態更新
    await waitFor(() => receivedUpdate !== null);
    expect(receivedUpdate.gateways).toHaveLength(1);
    expect(receivedUpdate.gateways[0]._id).toBe(gatewayId);
    expect(receivedUpdate.gateways[0].status).toBe('online');
    expect(receivedUpdate.updateType).toBe('connection');
  });

  test('網關斷開連接時應該推送狀態更新', async () => {
    const storeId = 'test-store-1';
    const gatewayId = 'gateway-test-1';

    // 先讓網關連接
    await simulateGatewayConnection(storeId, gatewayId);

    // 訂閱網關狀態
    const client = getWebSocketClient();
    let receivedUpdate = null;

    client.addGatewayStatusListener((event) => {
      if (event.storeId === storeId &&
          event.gateways.some(g => g.status === 'offline')) {
        receivedUpdate = event;
      }
    });

    client.subscribeGatewayStatus(storeId);

    // 模擬網關斷開連接
    await simulateGatewayDisconnection(gatewayId);

    // 驗證收到離線狀態更新
    await waitFor(() => receivedUpdate !== null);
    expect(receivedUpdate.gateways[0].status).toBe('offline');
    expect(receivedUpdate.updateType).toBe('connection');
  });

  test('網關信息更新應該推送更新', async () => {
    const storeId = 'test-store-1';
    const gatewayId = 'gateway-test-1';

    const client = getWebSocketClient();
    let receivedUpdate = null;

    client.addGatewayStatusListener((event) => {
      if (event.gateways.some(g => g.updatedFields.includes('wifiFirmwareVersion'))) {
        receivedUpdate = event;
      }
    });

    client.subscribeGatewayStatus(storeId);

    // 模擬網關信息更新
    await updateGatewayInfo(gatewayId, {
      wifiFirmwareVersion: '2.1.0',
      btFirmwareVersion: '1.5.0'
    });

    // 驗證收到信息更新
    await waitFor(() => receivedUpdate !== null);
    expect(receivedUpdate.gateways[0].wifiFirmwareVersion).toBe('2.1.0');
    expect(receivedUpdate.updateType).toBe('info');
  });
});
```

### 1.2 門店隔離測試

#### 1.2.1 多門店環境測試
```javascript
describe('門店隔離測試', () => {
  test('不同門店的設備狀態更新應該隔離', async () => {
    const store1 = 'store-1';
    const store2 = 'store-2';

    // 創建兩個客戶端，分別訂閱不同門店
    const client1 = new WebSocketClient();
    const client2 = new WebSocketClient();

    let store1Updates = [];
    let store2Updates = [];

    client1.addDeviceStatusListener((event) => {
      store1Updates.push(event);
    });

    client2.addDeviceStatusListener((event) => {
      store2Updates.push(event);
    });

    client1.subscribeDeviceStatus(store1);
    client2.subscribeDeviceStatus(store2);

    // 在store1中添加設備
    await simulateDeviceOnline(store1, 'AA:BB:CC:DD:EE:01');

    // 在store2中添加設備
    await simulateDeviceOnline(store2, 'AA:BB:CC:DD:EE:02');

    await waitFor(() => store1Updates.length > 0 && store2Updates.length > 0);

    // 驗證隔離性
    expect(store1Updates[0].storeId).toBe(store1);
    expect(store2Updates[0].storeId).toBe(store2);
    expect(store1Updates[0].devices[0].macAddress).toBe('AA:BB:CC:DD:EE:01');
    expect(store2Updates[0].devices[0].macAddress).toBe('AA:BB:CC:DD:EE:02');
  });

  test('不同門店的網關狀態更新應該隔離', async () => {
    const store1 = 'store-1';
    const store2 = 'store-2';

    // 創建兩個客戶端，分別訂閱不同門店的網關狀態
    const client1 = new WebSocketClient();
    const client2 = new WebSocketClient();

    let store1GatewayUpdates = [];
    let store2GatewayUpdates = [];

    client1.addGatewayStatusListener((event) => {
      store1GatewayUpdates.push(event);
    });

    client2.addGatewayStatusListener((event) => {
      store2GatewayUpdates.push(event);
    });

    client1.subscribeGatewayStatus(store1);
    client2.subscribeGatewayStatus(store2);

    // 在store1中連接網關
    await simulateGatewayConnection(store1, 'gateway-store1-01');

    // 在store2中連接網關
    await simulateGatewayConnection(store2, 'gateway-store2-01');

    await waitFor(() => store1GatewayUpdates.length > 0 && store2GatewayUpdates.length > 0);

    // 驗證隔離性
    expect(store1GatewayUpdates[0].storeId).toBe(store1);
    expect(store2GatewayUpdates[0].storeId).toBe(store2);
    expect(store1GatewayUpdates[0].gateways[0]._id).toBe('gateway-store1-01');
    expect(store2GatewayUpdates[0].gateways[0]._id).toBe('gateway-store2-01');
  });
});
```

## 2. 性能測試

### 2.1 大量設備測試

#### 2.1.1 批量設備狀態更新測試
```javascript
describe('大量設備性能測試', () => {
  test('應該能處理100個設備同時上線', async () => {
    const storeId = 'performance-test-store';
    const deviceCount = 100;
    
    const client = getWebSocketClient();
    let receivedUpdates = [];
    
    client.addDeviceStatusListener((event) => {
      receivedUpdates.push(event);
    });
    
    client.subscribeDeviceStatus(storeId);
    
    const startTime = Date.now();
    
    // 模擬100個設備同時上線
    const devices = Array.from({ length: deviceCount }, (_, i) => ({
      macAddress: `AA:BB:CC:DD:EE:${i.toString().padStart(2, '0')}`,
      status: 'online',
      data: { battery: 80 + (i % 20) }
    }));
    
    await simulateBatchDeviceUpdate(storeId, devices);
    
    // 等待所有更新完成
    await waitFor(() => {
      const totalDevices = receivedUpdates.reduce(
        (sum, update) => sum + update.devices.length, 0
      );
      return totalDevices >= deviceCount;
    });
    
    const endTime = Date.now();
    const processingTime = endTime - startTime;
    
    // 驗證性能要求（應該在2秒內完成）
    expect(processingTime).toBeLessThan(2000);
    
    // 驗證所有設備都收到更新
    const totalDevices = receivedUpdates.reduce(
      (sum, update) => sum + update.devices.length, 0
    );
    expect(totalDevices).toBe(deviceCount);
  });

  test('防抖機制應該合併頻繁更新', async () => {
    const storeId = 'debounce-test-store';
    const deviceMac = 'AA:BB:CC:DD:EE:99';
    
    const client = getWebSocketClient();
    let receivedUpdates = [];
    
    client.addDeviceStatusListener((event) => {
      receivedUpdates.push(event);
    });
    
    client.subscribeDeviceStatus(storeId);
    
    // 在短時間內多次更新同一設備
    for (let i = 0; i < 10; i++) {
      await simulateDeviceUpdate(storeId, deviceMac, {
        battery: 90 - i,
        rssi: -50 - i
      });
      await sleep(50); // 50ms間隔
    }
    
    // 等待防抖完成
    await sleep(1000);
    
    // 驗證更新被合併（應該收到的更新數量少於10次）
    expect(receivedUpdates.length).toBeLessThan(10);
    
    // 驗證最終狀態正確
    const lastUpdate = receivedUpdates[receivedUpdates.length - 1];
    const device = lastUpdate.devices.find(d => d.macAddress === deviceMac);
    expect(device.data.battery).toBe(81); // 最後一次更新的值
  });
});
```

### 2.2 併發連接測試

#### 2.2.1 多客戶端併發測試
```javascript
describe('併發連接測試', () => {
  test('應該支持多個客戶端同時訂閱', async () => {
    const storeId = 'concurrent-test-store';
    const clientCount = 20;
    
    // 創建多個客戶端
    const clients = Array.from({ length: clientCount }, () => new WebSocketClient());
    const receivedUpdates = Array.from({ length: clientCount }, () => []);
    
    // 為每個客戶端設置事件監聽
    clients.forEach((client, index) => {
      client.addDeviceStatusListener((event) => {
        receivedUpdates[index].push(event);
      });
      client.subscribeDeviceStatus(storeId);
    });
    
    // 等待所有客戶端連接完成
    await Promise.all(clients.map(client => waitForConnection(client)));
    
    // 模擬設備狀態更新
    await simulateDeviceOnline(storeId, 'AA:BB:CC:DD:EE:FF');
    
    // 等待所有客戶端收到更新
    await waitFor(() => {
      return receivedUpdates.every(updates => updates.length > 0);
    });
    
    // 驗證所有客戶端都收到了更新
    receivedUpdates.forEach(updates => {
      expect(updates).toHaveLength(1);
      expect(updates[0].devices[0].macAddress).toBe('AA:BB:CC:DD:EE:FF');
    });
    
    // 清理連接
    clients.forEach(client => client.disconnect());
  });
});
```

## 3. 穩定性測試

### 3.1 網路中斷測試

#### 3.1.1 重連機制測試
```javascript
describe('網路穩定性測試', () => {
  test('網路中斷後應該自動重連並恢復訂閱', async () => {
    const storeId = 'stability-test-store';
    const client = getWebSocketClient();
    
    let receivedUpdates = [];
    client.addDeviceStatusListener((event) => {
      receivedUpdates.push(event);
    });
    
    client.subscribeDeviceStatus(storeId);
    await waitForSubscription(client, storeId);
    
    // 模擬網路中斷
    await simulateNetworkDisconnection();
    expect(client.isConnected()).toBe(false);
    
    // 恢復網路連接
    await simulateNetworkReconnection();
    
    // 等待自動重連
    await waitForReconnection(client);
    expect(client.isConnected()).toBe(true);
    
    // 驗證訂閱已恢復
    await simulateDeviceOnline(storeId, 'AA:BB:CC:DD:EE:88');
    
    await waitFor(() => receivedUpdates.length > 0);
    expect(receivedUpdates[0].devices[0].macAddress).toBe('AA:BB:CC:DD:EE:88');
  });
});
```

### 3.2 長時間運行測試

#### 3.2.1 記憶體洩漏測試
```javascript
describe('長時間運行測試', () => {
  test('長時間運行不應該出現記憶體洩漏', async () => {
    const storeId = 'memory-test-store';
    const client = getWebSocketClient();
    
    const initialMemory = process.memoryUsage().heapUsed;
    
    client.subscribeDeviceStatus(storeId);
    
    // 模擬長時間運行（1小時的設備狀態更新）
    for (let i = 0; i < 3600; i++) {
      await simulateDeviceUpdate(storeId, `device-${i % 100}`, {
        battery: Math.floor(Math.random() * 100),
        rssi: -30 - Math.floor(Math.random() * 60)
      });
      
      if (i % 100 === 0) {
        // 每100次更新檢查一次記憶體使用
        const currentMemory = process.memoryUsage().heapUsed;
        const memoryIncrease = currentMemory - initialMemory;
        
        // 記憶體增長不應該超過50MB
        expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
      }
      
      await sleep(10); // 10ms間隔
    }
  });
});
```

## 4. 整合測試

### 4.1 與批量傳送功能整合測試

#### 4.1.1 同時使用兩種WebSocket功能
```javascript
describe('功能整合測試', () => {
  test('設備狀態更新和批量傳送進度應該能同時工作', async () => {
    const storeId = 'integration-test-store';
    const client = getWebSocketClient();
    
    let deviceStatusUpdates = [];
    let batchProgressUpdates = [];
    
    // 訂閱設備狀態
    client.addDeviceStatusListener((event) => {
      deviceStatusUpdates.push(event);
    });
    client.subscribeDeviceStatus(storeId);
    
    // 訂閱批量進度
    const batchId = 'test-batch-123';
    client.addEventListener((event) => {
      if (event.type === 'batch_progress') {
        batchProgressUpdates.push(event);
      }
    });
    client.subscribeBatchProgress(batchId);
    
    // 同時觸發兩種事件
    await Promise.all([
      simulateDeviceOnline(storeId, 'AA:BB:CC:DD:EE:77'),
      simulateBatchProgress(batchId, { completedDevices: 5, totalDevices: 10 })
    ]);
    
    // 驗證兩種事件都能正常接收
    await waitFor(() => 
      deviceStatusUpdates.length > 0 && batchProgressUpdates.length > 0
    );
    
    expect(deviceStatusUpdates[0].type).toBe('device_status_update');
    expect(batchProgressUpdates[0].type).toBe('batch_progress');
  });
});
```

## 5. 測試工具和輔助函數

### 5.1 測試輔助函數
```javascript
// 測試輔助函數
const TestHelpers = {
  // 等待WebSocket連接
  waitForConnection: (client, timeout = 5000) => {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => reject(new Error('連接超時')), timeout);
      
      const checkConnection = () => {
        if (client.isConnected()) {
          clearTimeout(timer);
          resolve();
        } else {
          setTimeout(checkConnection, 100);
        }
      };
      
      checkConnection();
    });
  },

  // 模擬設備上線
  simulateDeviceOnline: async (storeId, macAddress) => {
    const websocketService = require('../server/services/websocketService');
    await websocketService.updateDeviceStatus('test-gateway', [{
      macAddress,
      status: 'online',
      data: { battery: 85, rssi: -45 }
    }], storeId);
  },

  // 模擬設備離線
  simulateDeviceOffline: async (macAddress) => {
    const deviceStatusService = require('../server/services/deviceStatusService');
    // 修改設備的lastSeen時間使其超過離線閾值
    await deviceStatusService.forceDeviceOffline(macAddress);
  },

  // 等待條件滿足
  waitFor: (condition, timeout = 5000) => {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => reject(new Error('等待超時')), timeout);
      
      const check = () => {
        if (condition()) {
          clearTimeout(timer);
          resolve();
        } else {
          setTimeout(check, 100);
        }
      };
      
      check();
    });
  }
};
```
