import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';
import { usePermissionStore } from '../../store/permissionStore';
import { useRoutePermission } from '../../hooks/useRoutePermission';
import { useNavigationStore } from '../../store/navigationStore';

interface AuthGuardProps {
  children: React.ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { isAuthenticated, checkAuth, loading } = useAuthStore();
  const { fetchPermissions } = usePermissionStore();
  const [isChecking, setIsChecking] = useState(true);
  const [permissionsLoaded, setPermissionsLoaded] = useState(false);
  const location = useLocation();
  const { hasRoutePermission, getFirstAccessibleRoute, currentActiveItem } = useRoutePermission();
  const { setActiveItem } = useNavigationStore();

  // 檢查登入狀態
  useEffect(() => {
    const verifyAuth = async () => {
      const isLoggedIn = await checkAuth();
      setIsChecking(false);

      // 如果已登入，則獲取權限
      if (isLoggedIn) {
        try {
          await fetchPermissions();
        } finally {
          setPermissionsLoaded(true);
        }
      }
    };

    verifyAuth();
  }, [checkAuth, fetchPermissions]);

  // 檢查路由權限
  useEffect(() => {
    // 只有在權限加載完成後才檢查路由權限
    if (isAuthenticated && permissionsLoaded && !hasRoutePermission()) {
      // 如果沒有權限訪問當前路由，則跳轉到有權限的第一個路由
      const accessibleRoute = getFirstAccessibleRoute();
      console.log(`用戶沒有權限訪問當前活動項目 ${currentActiveItem}，跳轉到 ${accessibleRoute}`);

      // 更新 navigationStore 中的 activeItem
      setActiveItem(accessibleRoute);

      // 使用 window.location.hash 進行跳轉，適應基於狀態的路由管理
      window.location.hash = `#${accessibleRoute}`;

      // 通知用戶已被重定向
      alert(`您沒有權限訪問該頁面，已為您跳轉到有權限的頁面。`);
    }
  }, [isAuthenticated, permissionsLoaded, hasRoutePermission, getFirstAccessibleRoute, currentActiveItem, setActiveItem]);

  if (isChecking || loading) {
    // 顯示載入中狀態
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          <p className="mt-4 text-gray-700">載入中...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    // 重定向到登入頁面，並保存當前位置
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 用戶已認證且有權限訪問當前路由，顯示子組件
  return <>{children}</>;
};

export default AuthGuard;
