# 門店刷圖計畫系統 - 參數規格詳細說明

## 概述

本文檔詳細說明門店刷圖計畫系統的四個核心參數設計，確保系統能夠滿足實際業務需求並與現有系統保持一致性。

## 1. 執行類型參數

### 1.1 參數選項
- **單次執行** (`once`)：在指定的日期和時間執行一次
- **每日執行** (`daily`)：每天在指定時間重複執行
- **每週執行** (`weekly`)：每週在指定的星期和時間執行

### 1.2 數據結構
```typescript
interface TriggerConfig {
  type: 'once' | 'daily' | 'weekly';
  executeTime: string;        // 執行時間，格式: "HH:mm"
  executeDate?: string;       // 單次執行日期，格式: "YYYY-MM-DD"
  weekDays?: number[];        // 週執行的星期，0=週日, 1=週一, ..., 6=週六
}
```

### 1.3 使用示例
```javascript
// 單次執行
{
  type: 'once',
  executeTime: '14:30',
  executeDate: '2024-01-15'
}

// 每日執行
{
  type: 'daily',
  executeTime: '08:00'
}

// 每週執行（週一、三、五）
{
  type: 'weekly',
  executeTime: '09:00',
  weekDays: [1, 3, 5]
}
```

### 1.4 調度實現
```javascript
// 使用 node-schedule 實現
switch (triggerConfig.type) {
  case 'once':
    const executeDateTime = new Date(`${triggerConfig.executeDate} ${triggerConfig.executeTime}`);
    schedule.scheduleJob(executeDateTime, () => executePlan(plan));
    break;
    
  case 'daily':
    const [hour, minute] = triggerConfig.executeTime.split(':');
    const dailyRule = new schedule.RecurrenceRule();
    dailyRule.hour = parseInt(hour);
    dailyRule.minute = parseInt(minute);
    schedule.scheduleJob(dailyRule, () => executePlan(plan));
    break;
    
  case 'weekly':
    const [weekHour, weekMinute] = triggerConfig.executeTime.split(':');
    const weeklyRule = new schedule.RecurrenceRule();
    weeklyRule.dayOfWeek = triggerConfig.weekDays;
    weeklyRule.hour = parseInt(weekHour);
    weeklyRule.minute = parseInt(weekMinute);
    schedule.scheduleJob(weeklyRule, () => executePlan(plan));
    break;
}
```

## 2. 執行時間點參數

### 2.1 時間格式
- **格式**：24小時制，"HH:mm" 格式
- **範圍**：00:00 - 23:59
- **精度**：分鐘級別

### 2.2 時區處理
- 使用服務器本地時區
- 支援時區配置（未來擴展）

### 2.3 用戶界面
```html
<!-- 時間選擇器 -->
<div class="time-picker">
  <select name="hour">
    <option value="00">00</option>
    <option value="01">01</option>
    <!-- ... -->
    <option value="23">23</option>
  </select>
  :
  <select name="minute">
    <option value="00">00</option>
    <option value="15">15</option>
    <option value="30">30</option>
    <option value="45">45</option>
  </select>
</div>
```

## 3. 刷圖對象參數

### 3.1 對象類型

#### 3.1.1 指定 MAC 地址 (`mac_addresses`)
- **描述**：直接選擇特定設備的 MAC 地址
- **行為**：所有選中的設備都會執行刷圖，不考慮數據綁定狀態
- **適用場景**：需要對特定設備進行定期維護或測試

#### 3.1.2 指定門店數據 (`store_data`)
- **描述**：選擇特定的門店數據項目
- **行為**：只有同時滿足以下條件的設備才會執行刷圖：
  - 設備已綁定模板 (`templateId` 存在且不為空)
  - 設備的數據綁定包含選中的門店數據項目
- **適用場景**：需要更新特定商品或促銷信息的顯示

### 3.2 數據結構
```typescript
interface TargetSelection {
  type: 'mac_addresses' | 'store_data';
  macAddresses?: string[];     // MAC 地址列表
  storeDataIds?: string[];     // 門店數據 ID 列表
}
```

### 3.3 設備篩選邏輯

#### 3.3.1 MAC 地址模式
```javascript
async function getDevicesByMacAddresses(storeId, macAddresses) {
  const query = {
    storeId: storeId,
    macAddress: { $in: macAddresses }
  };
  return await deviceCollection.find(query).toArray();
}
```

#### 3.3.2 門店數據模式
```javascript
async function getDevicesByStoreData(storeId, storeDataIds) {
  const query = {
    storeId: storeId,
    templateId: { $exists: true, $ne: null },           // 必須綁定模板
    'dataBindings.dataId': { $in: storeDataIds }        // 數據綁定包含指定項目
  };
  return await deviceCollection.find(query).toArray();
}
```

### 3.4 選擇限制
- **互斥性**：每個計畫只能選擇一種對象類型
- **多選支援**：在選定的類型內可以多選具體項目
- **動態更新**：門店數據模式下，設備列表會根據數據綁定狀態動態變化

### 3.5 用戶界面設計
```typescript
// 對象類型選擇
const [targetType, setTargetType] = useState<'mac_addresses' | 'store_data'>('mac_addresses');
const [selectedMacs, setSelectedMacs] = useState<string[]>([]);
const [selectedDataIds, setSelectedDataIds] = useState<string[]>([]);

// 切換對象類型時清空選擇
const handleTypeChange = (newType) => {
  setTargetType(newType);
  setSelectedMacs([]);
  setSelectedDataIds([]);
};
```

## 4. 傳送邏輯參數

### 4.1 系統設定整合
所有傳送相關的參數都從系統設定中讀取，確保與設備管理的批量傳送功能保持一致：

```typescript
interface SystemConfig {
  gatewayConcurrency: number;      // 網關並發數（預設: 20）
  queueRetryAttempts: number;      // 隊列重試次數（預設: 3）
  queueRetryInterval: number;      // 重試間隔（毫秒，預設: 5000）
  gatewayTimeout: number;          // 網關超時時間（毫秒，預設: 30000）
}
```

### 4.2 計畫級別設定
```typescript
interface ExecutionConfig {
  useSystemSettings: boolean;      // 是否使用系統設定（強制為 true，不可關閉）
}
```

**說明：**
- 刷圖計畫強制使用系統設定，確保與設備管理批量傳送功能保持一致
- 智能網關選擇功能取決於裝置本身的設定，不再在計畫層級配置
- 所有傳送參數（並發數、重試次數、超時時間等）都從系統配置中讀取

### 4.3 傳送邏輯實現
```javascript
async function executeBatchRefresh(plan, devices) {
  // 從系統設定讀取參數
  const systemConfig = await getSystemConfig();

  // 調用現有的批量發送服務
  // 智能網關選擇取決於裝置本身的設定，不再從計畫配置中讀取
  const result = await sendMultipleDevicePreviewsToGateways(
    deviceIds,
    {
      sendToAllGateways: false,  // 固定為 false，使用智能選擇
      storeId: plan.storeId,
      concurrency: systemConfig.gatewayConcurrency,
      enableSmartSelection: true, // 固定啟用，具體行為取決於裝置設定
      timeout: systemConfig.gatewayTimeout,
      retryAttempts: systemConfig.queueRetryAttempts,
      retryInterval: systemConfig.queueRetryInterval
    }
  );

  return result;
}
```

### 4.4 智能網關邏輯
- **智能網關選擇**：系統會根據設備本身的網關配置自動選擇最佳網關
- **裝置設定優先**：是否使用智能網關取決於裝置本身的設定，不再在計畫層級配置
- **重試機制**：使用與設備管理相同的重試邏輯和參數
- **系統一致性**：確保刷圖計畫與設備管理批量傳送使用相同的邏輯

### 4.5 與現有系統的一致性
```javascript
// 確保使用相同的服務和參數
const sendPreviewService = require('./sendPreviewToGateway');

// 使用相同的批量發送函數
const result = await sendPreviewService.sendMultipleDevicePreviewsToGateways(
  deviceIds,
  options  // 使用相同的選項結構
);
```

## 5. 參數驗證

### 5.1 前端驗證
```typescript
function validatePlanConfig(config: RefreshPlanConfig): ValidationResult {
  const errors: string[] = [];
  
  // 驗證執行時間格式
  if (!/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(config.trigger.executeTime)) {
    errors.push('執行時間格式不正確');
  }
  
  // 驗證對象選擇
  if (config.targetSelection.type === 'mac_addresses') {
    if (!config.targetSelection.macAddresses?.length) {
      errors.push('請至少選擇一個 MAC 地址');
    }
  } else if (config.targetSelection.type === 'store_data') {
    if (!config.targetSelection.storeDataIds?.length) {
      errors.push('請至少選擇一項門店數據');
    }
  }
  
  return { isValid: errors.length === 0, errors };
}
```

### 5.2 後端驗證
```javascript
function validatePlanData(planData) {
  // 驗證必填欄位
  if (!planData.name || !planData.storeId) {
    throw new Error('計畫名稱和門店ID為必填項');
  }
  
  // 驗證觸發配置
  if (!planData.trigger || !planData.trigger.type || !planData.trigger.executeTime) {
    throw new Error('觸發配置不完整');
  }
  
  // 驗證對象選擇
  if (!planData.targetSelection || !planData.targetSelection.type) {
    throw new Error('必須選擇刷圖對象');
  }
  
  return true;
}
```

## 6. 測試用例

### 6.1 執行類型測試
```javascript
describe('執行類型測試', () => {
  test('單次執行應在指定時間觸發', async () => {
    const plan = {
      trigger: {
        type: 'once',
        executeTime: '14:30',
        executeDate: '2024-01-15'
      }
    };
    
    const scheduledTime = await scheduler.registerPlan(plan);
    expect(scheduledTime).toBe(new Date('2024-01-15 14:30'));
  });
  
  test('每日執行應每天觸發', async () => {
    const plan = {
      trigger: {
        type: 'daily',
        executeTime: '08:00'
      }
    };
    
    const nextRuns = await scheduler.getNextRuns(plan, 3);
    expect(nextRuns).toHaveLength(3);
    expect(nextRuns[0].getHours()).toBe(8);
  });
});
```

### 6.2 對象選擇測試
```javascript
describe('對象選擇測試', () => {
  test('MAC地址模式應返回指定設備', async () => {
    const plan = {
      storeId: 'store1',
      targetSelection: {
        type: 'mac_addresses',
        macAddresses: ['AA:BB:CC:DD:EE:01', 'AA:BB:CC:DD:EE:02']
      }
    };
    
    const devices = await executionEngine.getTargetDevices(plan);
    expect(devices).toHaveLength(2);
    expect(devices.map(d => d.macAddress)).toEqual(plan.targetSelection.macAddresses);
  });
  
  test('門店數據模式應只返回綁定設備', async () => {
    const plan = {
      storeId: 'store1',
      targetSelection: {
        type: 'store_data',
        storeDataIds: ['data1', 'data2']
      }
    };
    
    const devices = await executionEngine.getTargetDevices(plan);
    devices.forEach(device => {
      expect(device.templateId).toBeTruthy();
      expect(device.dataBindings.some(binding => 
        plan.targetSelection.storeDataIds.includes(binding.dataId)
      )).toBe(true);
    });
  });
});
```

## 7. 性能考量

### 7.1 設備查詢優化
- 為 `macAddress` 和 `dataBindings.dataId` 建立索引
- 使用分頁查詢處理大量設備
- 快取常用的查詢結果

### 7.2 調度性能
- 限制同時運行的計畫數量
- 使用任務隊列避免資源競爭
- 監控系統負載並動態調整

### 7.3 記憶體管理
- 定期清理過期的執行記錄
- 限制單次處理的設備數量
- 使用流式處理處理大批量操作

這個參數規格文檔確保了門店刷圖計畫系統能夠精確滿足您提出的四個核心需求，並與現有系統保持完全一致的傳送邏輯。
