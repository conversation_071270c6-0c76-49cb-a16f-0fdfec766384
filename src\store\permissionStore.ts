import { create } from 'zustand';
import { getCurrentUserPermissions } from '../utils/api/userApi';

interface PermissionState {
  // 權限列表
  permissions: string[];
  // 角色列表
  roles: any[];
  // 加載狀態
  loading: boolean;
  // 錯誤信息
  error: string | null;
  
  // 獲取當前用戶權限
  fetchPermissions: () => Promise<void>;
  // 檢查是否有特定權限
  hasPermission: (permission: string) => boolean;
  // 檢查是否有多個權限中的任意一個
  hasAnyPermission: (permissionList: string[]) => boolean;
  // 檢查是否有所有指定的權限
  hasAllPermissions: (permissionList: string[]) => boolean;
  // 清除錯誤
  clearError: () => void;
}

// 創建權限狀態管理
export const usePermissionStore = create<PermissionState>()((set, get) => ({
  permissions: [],
  roles: [],
  loading: false,
  error: null,
  
  // 獲取當前用戶權限
  fetchPermissions: async () => {
    try {
      set({ loading: true, error: null });
      
      const data = await getCurrentUserPermissions();
      
      set({
        permissions: data.permissions,
        roles: data.roles,
        loading: false
      });
    } catch (error: any) {
      console.error('獲取用戶權限錯誤:', error);
      set({
        permissions: [],
        roles: [],
        loading: false,
        error: error.message || '獲取權限失敗'
      });
    }
  },
  
  // 檢查是否有特定權限
  hasPermission: (permission: string) => {
    const { permissions } = get();
    
    // 如果有 'all' 權限，則擁有所有權限
    if (permissions.includes('all')) return true;
    
    // 檢查是否有特定權限
    return permissions.includes(permission);
  },
  
  // 檢查是否有多個權限中的任意一個
  hasAnyPermission: (permissionList: string[]) => {
    const { permissions } = get();
    
    // 如果有 'all' 權限，則擁有所有權限
    if (permissions.includes('all')) return true;
    
    // 檢查是否有任意一個權限
    return permissionList.some(permission => permissions.includes(permission));
  },
  
  // 檢查是否有所有指定的權限
  hasAllPermissions: (permissionList: string[]) => {
    const { permissions } = get();
    
    // 如果有 'all' 權限，則擁有所有權限
    if (permissions.includes('all')) return true;
    
    // 檢查是否有所有權限
    return permissionList.every(permission => permissions.includes(permission));
  },
  
  // 清除錯誤
  clearError: () => {
    set({ error: null });
  }
}));

export default usePermissionStore;
