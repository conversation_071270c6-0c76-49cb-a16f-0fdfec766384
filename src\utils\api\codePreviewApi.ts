/**
 * QR Code 和條碼預覽 API 服務
 * 提供前端快取機制和錯誤處理
 */

interface QRCodePreviewOptions {
  content: string;
  qrCodeType?: 'qrcode' | 'datamatrix' | 'pdf417';
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
  quietZone?: number;
  moduleSize?: number;
  foregroundColor?: string;
  backgroundColor?: string;
  width?: number;
  height?: number;
}

interface BarcodePreviewOptions {
  content: string;
  barcodeType?: 'code128' | 'ean13' | 'upc-a' | 'code39' | 'code93';
  quietZone?: number;
  foregroundColor?: string;
  backgroundColor?: string;
  width?: number;
  height?: number;
}

interface PreviewResponse {
  success: boolean;
  data?: string; // base64 圖片數據
  error?: string;
}

// 快取管理
class PreviewCache {
  private cache = new Map<string, { data: string; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分鐘

  private generateKey(options: any): string {
    return JSON.stringify(options);
  }

  get(options: any): string | null {
    const key = this.generateKey(options);
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    // 檢查是否過期
    if (Date.now() - cached.timestamp > this.CACHE_DURATION) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  set(options: any, data: string): void {
    const key = this.generateKey(options);
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clear(): void {
    this.cache.clear();
  }

  // 清理過期的快取項目
  cleanup(): void {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.CACHE_DURATION) {
        this.cache.delete(key);
      }
    }
  }
}

// 全域快取實例
const qrCodeCache = new PreviewCache();
const barcodeCache = new PreviewCache();

// 定期清理快取
setInterval(() => {
  qrCodeCache.cleanup();
  barcodeCache.cleanup();
}, 60 * 1000); // 每分鐘清理一次

/**
 * 生成 QR Code 預覽
 * @param options QR Code 選項
 * @returns Promise<string | null> base64 圖片數據或 null
 */
export async function generateQRCodePreview(options: QRCodePreviewOptions): Promise<string | null> {
  try {
    // 檢查快取
    const cached = qrCodeCache.get(options);
    if (cached) {
      console.log('QR Code 預覽快取命中');
      return cached;
    }

    // 發送 API 請求
    const response = await fetch('/api/code-preview/qr', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(options)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('QR Code 預覽 API 錯誤:', response.status, errorText);
      return null;
    }

    const result: PreviewResponse = await response.json();
    
    if (!result.success || !result.data) {
      console.error('QR Code 預覽生成失敗:', result.error);
      return null;
    }

    // 快取結果
    qrCodeCache.set(options, result.data);
    
    return result.data;

  } catch (error) {
    console.error('QR Code 預覽請求失敗:', error);
    return null;
  }
}

/**
 * 生成條碼預覽
 * @param options 條碼選項
 * @returns Promise<string | null> base64 圖片數據或 null
 */
export async function generateBarcodePreview(options: BarcodePreviewOptions): Promise<string | null> {
  try {
    // 檢查快取
    const cached = barcodeCache.get(options);
    if (cached) {
      console.log('條碼預覽快取命中');
      return cached;
    }

    // 發送 API 請求
    const response = await fetch('/api/code-preview/barcode', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(options)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('條碼預覽 API 錯誤:', response.status, errorText);
      return null;
    }

    const result: PreviewResponse = await response.json();
    
    if (!result.success || !result.data) {
      console.error('條碼預覽生成失敗:', result.error);
      return null;
    }

    // 快取結果
    barcodeCache.set(options, result.data);
    
    return result.data;

  } catch (error) {
    console.error('條碼預覽請求失敗:', error);
    return null;
  }
}

/**
 * 清除所有預覽快取
 */
export function clearPreviewCache(): void {
  qrCodeCache.clear();
  barcodeCache.clear();
  console.log('預覽快取已清除');
}

/**
 * 驗證 QR Code 內容
 * @param content 內容
 * @param qrCodeType QR Code 類型
 * @returns 驗證結果
 */
export function validateQRCodeContent(content: string, qrCodeType: string = 'qrcode'): {
  isValid: boolean;
  error?: string;
} {
  if (!content || typeof content !== 'string') {
    return {
      isValid: false,
      error: '內容不能為空'
    };
  }

  // QR Code 驗證規則
  const QR_CODE_LIMITS: Record<string, { maxLength: number; charset: string }> = {
    qrcode: { maxLength: 4296, charset: 'all' },
    datamatrix: { maxLength: 3116, charset: 'ascii' },
    pdf417: { maxLength: 2710, charset: 'all' }
  };

  const limits = QR_CODE_LIMITS[qrCodeType] || QR_CODE_LIMITS.qrcode;

  // 長度檢查
  if (content.length > limits.maxLength) {
    return {
      isValid: false,
      error: `內容長度超過限制 (${limits.maxLength} 字符)`
    };
  }

  // 字符集檢查
  if (limits.charset === 'ascii') {
    if (!/^[\x00-\x7F]*$/.test(content)) {
      return {
        isValid: false,
        error: '僅支援 ASCII 字符'
      };
    }
  }

  return { isValid: true };
}

/**
 * 驗證條碼內容
 * @param content 內容
 * @param barcodeType 條碼類型
 * @returns 驗證結果
 */
export function validateBarcodeContent(content: string, barcodeType: string = 'code128'): {
  isValid: boolean;
  error?: string;
} {
  if (!content || typeof content !== 'string') {
    return {
      isValid: false,
      error: '內容不能為空'
    };
  }

  // 條碼驗證規則
  const BARCODE_LIMITS: Record<string, { length?: number; maxLength?: number; charset: string }> = {
    code128: { maxLength: 80, charset: 'ascii' },
    ean13: { length: 13, charset: 'numeric' },
    'upc-a': { length: 12, charset: 'numeric' },
    code39: { maxLength: 43, charset: 'code39' },
    code93: { maxLength: 47, charset: 'ascii' }
  };

  const limits = BARCODE_LIMITS[barcodeType] || BARCODE_LIMITS.code128;

  // 長度檢查
  if (limits.length) {
    if (content.length !== limits.length) {
      return {
        isValid: false,
        error: `${barcodeType.toUpperCase()} 需要 ${limits.length} 位數字`
      };
    }
  } else if (limits.maxLength) {
    if (content.length > limits.maxLength) {
      return {
        isValid: false,
        error: `內容長度超過限制 (${limits.maxLength} 字符)`
      };
    }
  }

  // 字符集檢查
  switch (limits.charset) {
    case 'numeric':
      if (!/^\d+$/.test(content)) {
        return {
          isValid: false,
          error: '僅支援數字字符'
        };
      }
      break;
    case 'ascii':
      if (!/^[\x00-\x7F]*$/.test(content)) {
        return {
          isValid: false,
          error: '僅支援 ASCII 字符'
        };
      }
      break;
    case 'code39':
      if (!/^[A-Z0-9 \-.$\/+%*]*$/.test(content.toUpperCase())) {
        return {
          isValid: false,
          error: 'Code39 僅支援 A-Z, 0-9, 空格, -, ., $, /, +, %, * 字符'
        };
      }
      break;
  }

  return { isValid: true };
}
