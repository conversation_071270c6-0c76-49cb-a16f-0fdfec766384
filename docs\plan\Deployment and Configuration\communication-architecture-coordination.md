# 網關與設備通信架構協調文檔

## 1. 概述

本文檔定義了網關與設備通信架構中三個主要組件（Server、App、Gateway模擬器）之間的協調和通信方式。這三個組件由不同的開發人員負責，但需要協同工作以實現完整的系統功能。

## 2. 系統架構

系統由三個主要組件組成：

1. **Server端**：提供WebSocket服務和API接口
2. **App端**：配網工具，用於掃描和註冊網關
3. **Gateway模擬器**：模擬真實網關的行為

![系統架構圖](../images/system-architecture.png)

## 3. 通信流程

### 3.1 完整流程

1. App登入Server獲取認證Token
2. App掃描本地網絡中的網關
3. App將網關註冊到Server
4. Server生成網關的WebSocket連接信息
5. App將WebSocket連接信息發送給網關
6. 網關切換到工作模式並連接到Server的WebSocket服務
7. 網關定期向Server發送設備狀態
8. Server處理並存儲設備數據

### 3.2 序列圖

```
+-------+                  +--------+                  +--------+
|  App  |                  | Server |                  | Gateway|
+-------+                  +--------+                  +--------+
    |                           |                           |
    | 1. 登入                   |                           |
    |-------------------------->|                           |
    | 認證Token                 |                           |
    |<--------------------------|                           |
    |                           |                           |
    | 2. 掃描本地網關            |                           |
    |------------------------------------------------------>|
    | 網關信息                   |                           |
    |<------------------------------------------------------|
    |                           |                           |
    | 3. 註冊網關                |                           |
    |-------------------------->|                           |
    | 網關ID + WebSocket信息     |                           |
    |<--------------------------|                           |
    |                           |                           |
    | 4. 發送WebSocket連接信息    |                           |
    |------------------------------------------------------>|
    | 成功響應                   |                           |
    |<------------------------------------------------------|
    |                           |                           |
    |                           | 5. WebSocket連接          |
    |                           |<--------------------------|
    |                           | 網關信息                  |
    |                           |<--------------------------|
    |                           |                           |
    |                           | 6. 設備狀態報告（定期）     |
    |                           |<--------------------------|
    |                           | 命令（如需要）             |
    |                           |-------------------------->|
    |                           |                           |
```

## 3. 設備與網關綁定邏輯

系統中設備與網關的綁定關係按照以下邏輯進行管理：

### 3.1 設備初始化流程

1. **設備新增階段**
   - 當設備被添加到系統時，初始狀態為離線（`status: 'offline'`）且未初始化（`initialized: false`）
   - 此時設備沒有主要網關（`primaryGatewayId: null`）和其他網關列表（`otherGateways: []`）

2. **設備初始化階段**
   - 當任何網關首次發現未初始化的設備時（通過上報設備MAC地址）
   - 系統將該網關設定為該設備的主要網關（`primaryGatewayId: gatewayId`）
   - 設備狀態變為已初始化（`initialized: true`）

3. **設備多網關發現階段**
   - 當已初始化的設備被非主要網關發現時
   - 該網關會被添加到設備的其他網關列表中（`otherGateways: [...otherGateways, gatewayId]`）

### 3.2 用戶手動設定主要網關

用戶可以通過UI介面手動變更設備的主要網關：

1. 用戶從設備詳情頁面可以查看當前主要網關以及其他發現該設備的網關列表
2. 用戶可以從其他網關列表中選擇一個網關，設置為新的主要網關
3. 系統執行以下邏輯：
   - 原主要網關會被移動到其他網關列表中
   - 新選擇的網關會從其他網關列表中移除，並設置為主要網關

### 3.3 設備與用戶綁定

1. 系統支持將設備綁定到特定用戶：
   - 設備可以關聯到一個用戶（`userId: userId`）
   - 用戶可以關聯多個設備（`devices: [deviceId1, deviceId2, ...]`）

2. 綁定過程：
   - 通過API將設備綁定到用戶
   - 同時更新設備的用戶ID和用戶的設備列表

3. 解除綁定過程：
   - 通過API解除設備與用戶的綁定
   - 同時清除設備的用戶ID和從用戶的設備列表中移除該設備

## 4. 組件間數據交換

### 4.1 數據交換協議

各組件間通信使用基於JSON的協議，消息格式統一如下：

```javascript
{
  "type": "消息類型",
  "timestamp": 1683270664000,  // 時間戳，毫秒
  "data": { /* 消息內容 */ },
  "id": "唯一消息ID",   // 選填，用於消息追踪
  "version": "1.0"     // 協議版本，選填
}
```

### 4.2 常用消息類型與格式

#### 4.2.1 網關發現設備通知

```javascript
// 網關 -> Server
{
  "type": "deviceStatus",
  "timestamp": 1683270664000,
  "data": {
    "gatewayId": "gateway_id",
    "storeId": "store_id",
    "devices": [
      {
        "macAddress": "AA:BB:CC:DD:EE:FF",
        "name": "EPD Display 1",
        "type": "EPD",
        "battery": 85,
        "rssi": -65,
        "status": "online",
        "data": {
          "temperature": 25.5,
          "humidity": 60
        }
      }
    ]
  }
}
```

#### 4.2.2 設備狀態應答

```javascript
// Server -> 網關
{
  "type": "deviceStatusAck",
  "timestamp": 1683270664100,
  "data": {
    "success": true,
    "message": "設備狀態更新成功",
    "detailStatus": [
      {
        "macAddress": "AA:BB:CC:DD:EE:FF",
        "status": "success",
        "deviceId": "device_id"
      }
    ]
  }
}
```

#### 4.2.3 網關註冊

```javascript
// App -> Server
{
  "type": "registerGateway",
  "timestamp": 1683270500000,
  "data": {
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "name": "Store Gateway 1",
    "storeId": "store_id",
    "ipAddress": "*************"
  }
}
```

#### 4.2.4 網關註冊響應

```javascript
// Server -> App
{
  "type": "registerGatewayAck",
  "timestamp": 1683270500100,
  "data": {
    "success": true,
    "gateway": {
      "_id": "gateway_id",
      "name": "Store Gateway 1",
      "macAddress": "AA:BB:CC:DD:EE:FF",
      "websocket": {
        "url": "wss://server.example.com/ws/store/store_id/gateway/gateway_id",
        "token": "jwt_token",
        "protocol": "json"
      }
    }
  }
}
```

#### 4.2.5 網關配置

```javascript
// App -> 網關
{
  "type": "config",
  "timestamp": 1683270600000,
  "data": {
    "websocket": {
      "url": "wss://server.example.com/ws/store/store_id/gateway/gateway_id",
      "token": "jwt_token",
      "protocol": "json"
    },
    "reportInterval": 30,  // 設備狀態上報間隔，秒
    "scanInterval": 60     // 設備掃描間隔，秒
  }
}
```

#### 4.2.6 網關認證

```javascript
// 網關 -> Server
{
  "type": "auth",
  "timestamp": 1683270700000,
  "data": {
    "gatewayId": "gateway_id",
    "token": "jwt_token"
  }
}
```

#### 4.2.7 網關心跳

```javascript
// 網關 -> Server
{
  "type": "ping",
  "timestamp": 1683270800000
}

// Server -> 網關
{
  "type": "pong",
  "timestamp": 1683270800100,
  "data": {
    "serverTime": 1683270800100
  }
}
```

## 5. 協調機制

### 5.1 初始配置流程

為確保系統各組件正確協同工作，初始配置流程如下：

1. **網關註冊流程**:

```
┌─────────┐          ┌─────────┐          ┌─────────┐
│         │          │         │          │         │
│   App   │          │ Server  │          │ Gateway │
│         │          │         │          │         │
└────┬────┘          └────┬────┘          └────┬────┘
     │                    │                    │
     │ 掃描本地網絡         │                    │
     ├─────────────────────┼────────────────────►
     │                    │                    │
     │ 發現網關            │                    │
     │◄────────────────────┼────────────────────┤
     │                    │                    │
     │ 註冊網關            │                    │
     ├──────────────────►│                    │
     │                    │                    │
     │ 返回網關註冊信息     │                    │
     │◄───────────────────┤                    │
     │                    │                    │
     │ 發送WebSocket配置   │                    │
     ├────────────────────────────────────────►│
     │                    │                    │
     │                    │ 連接WebSocket       │
     │                    │◄───────────────────┤
     │                    │                    │
     │                    │ 認證               │
     │                    │◄───────────────────┤
     │                    │                    │
     │                    │ 認證成功           │
     │                    ├───────────────────►│
     │                    │                    │
```

2. **設備發現與初始化流程**:

```
┌─────────┐          ┌─────────┐          ┌─────────┐
│         │          │         │          │         │
│   App   │          │ Server  │          │ Gateway │
│         │          │         │          │         │
└────┬────┘          └────┬────┘          └────┬────┘
     │                    │                    │
     │                    │                    │ 掃描周邊設備
     │                    │                    ├──────┐
     │                    │                    │      │
     │                    │                    │◄─────┘
     │                    │                    │
     │                    │ 上報設備狀態        │
     │                    │◄───────────────────┤
     │                    │                    │
     │                    │ 處理設備初始化      │
     │                    ├──────┐             │
     │                    │      │             │
     │                    │◄─────┘             │
     │                    │                    │
     │                    │ 設備狀態更新確認     │
     │                    ├───────────────────►│
     │                    │                    │
     │ 查詢設備列表         │                    │
     ├───────────────────►│                    │
     │                    │                    │
     │ 返回設備列表         │                    │
     │◄───────────────────┤                    │
     │                    │                    │
```

3. **設備綁定用戶流程**:

```
┌─────────┐          ┌─────────┐          ┌─────────┐
│         │          │         │          │         │
│   App   │          │ Server  │          │ Gateway │
│         │          │         │          │         │
└────┬────┘          └────┬────┘          └────┬────┘
     │                    │                    │
     │ 查詢未綁定設備        │                    │
     ├───────────────────►│                    │
     │                    │                    │
     │ 返回未綁定設備列表     │                    │
     │◄───────────────────┤                    │
     │                    │                    │
     │ 綁定設備到用戶        │                    │
     ├───────────────────►│                    │
     │                    │                    │
     │ 處理綁定邏輯         │                    │
     │                    ├──────┐             │
     │                    │      │             │
     │                    │◄─────┘             │
     │                    │                    │
     │ 返回綁定結果         │                    │
     │◄───────────────────┤                    │
     │                    │                    │
```

### 5.2 設備主要網關變更流程

當用戶需要更改設備的主要網關時，流程如下：

```
┌─────────┐          ┌─────────┐          ┌─────────┐
│         │          │         │          │         │
│   App   │          │ Server  │          │ Gateway │
│         │          │         │          │         │
└────┬────┘          └────┬────┘          └────┬────┘
     │                    │                    │
     │ 查詢設備詳情         │                    │
     ├───────────────────►│                    │
     │                    │                    │
     │ 返回設備詳情(含網關)  │                    │
     │◄───────────────────┤                    │
     │                    │                    │
     │ 設置主要網關         │                    │
     ├───────────────────►│                    │
     │                    │                    │
     │ 處理網關變更邏輯      │                    │
     │                    ├──────┐             │
     │                    │      │             │
     │                    │◄─────┘             │
     │                    │                    │
     │ 返回變更結果         │                    │
     │◄───────────────────┤                    │
     │                    │                    │
```

### 5.3 協調注意事項

為確保各組件正確協作，需注意以下幾點：

1. **消息格式統一**：
   - 所有組件間通信必須遵循定義的JSON格式
   - 消息必須包含必要的字段，如type和timestamp

2. **失敗處理與重試**：
   - App應處理網關註冊失敗的情況，適當時進行重試
   - 網關應處理WebSocket連接失敗的情況，實現指數退避重連
   - Server應處理數據庫操作失敗的情況，確保數據一致性

3. **狀態同步**：
   - 網關應定期上報設備狀態，確保Server端數據為最新
   - Server在收到設備狀態後應立即處理並更新數據庫
   - App在顯示設備信息前應先查詢最新狀態

4. **安全性**：
   - App與Server間通信使用HTTPS和JWT認證
   - 網關與Server間WebSocket連接使用WSS和特定Token
   - 本地通信（App與網關）應在安全的內部網絡中進行

## 6. 開發與測試協調

### 6.1 開發流程協調

三個組件由不同團隊開發，需要協調開發流程：

1. **接口先行**：
   - 首先定義並凍結API接口和通信協議
   - 各團隊基於凍結的接口進行獨立開發

2. **模擬器輔助**：
   - Server團隊開發簡單的網關模擬器，用於測試WebSocket服務
   - App團隊開發簡單的Server API模擬器，用於測試網關註冊流程
   - Gateway團隊開發簡單的Server WebSocket模擬器，用於測試連接和消息處理

3. **階段性整合**：
   - 每兩周安排一次整合測試，驗證三個組件的兼容性
   - 發現問題及時調整，確保接口一致性

4. **版本控制**：
   - 統一使用語義化版本號(Semantic Versioning)
   - 接口變更需要提前通知並協商

### 6.2 測試環境設置

1. **獨立測試環境**：
   - 每個團隊配置獨立的測試環境
   - 提供Docker容器化配置，確保環境一致性

2. **集成測試環境**：
   - 設置專用的集成測試環境
   - 各團隊的最新代碼定期整合到該環境

3. **模擬數據生成**：
   - 準備標準的測試數據集
   - 實現自動化數據生成腳本

### 6.3 驗收測試標準

以下是各組件的驗收測試標準，確保系統功能完整：

#### 6.3.1 Server端驗收標準

- WebSocket服務能正確處理網關連接和認證
- 能正確處理設備初始化和網關綁定邏輯
- API接口符合規範並正確處理請求
- 數據庫結構完整，索引設置合理
- 異常情況處理得當，有合適的錯誤日誌
- 滿足性能要求：支持同時處理至少100個網關連接

#### 6.3.2 App端驗收標準

- 能正確掃描本地網絡中的網關
- 能成功註冊網關到Server
- 能將WebSocket配置正確發送給網關
- 用戶界面完整展示設備和網關信息
- 支持設備綁定用戶和更改主要網關操作
- 異常處理得當，用戶體驗流暢

#### 6.3.3 Gateway端驗收標準

- 能在掃描模式下被App發現
- 能接收並保存WebSocket配置
- 能成功連接到Server的WebSocket服務並完成認證
- 能定期掃描周邊設備並上報狀態
- 能正確處理服務器的指令和配置變更
- 斷線重連機制可靠，能在網絡恢復後自動重連

### 6.4 驗收測試場景

1. **基本功能測試**：
   - 網關註冊和WebSocket連接
   - 設備發現和初始化
   - 設備綁定用戶
   - 更改設備主要網關

2. **異常情況測試**：
   - 網關斷線重連
   - 網關IP地址變更
   - Server重啟後的狀態恢復
   - 網絡延遲和不穩定情況下的表現

3. **安全性測試**：
   - Token過期和續期
   - 無效Token的處理
   - 未授權請求的處理
   - 防止跨站請求偽造(CSRF)

4. **性能測試**：
   - 高並發連接測試
   - 大量設備數據上報處理
   - 數據庫查詢性能
   - 資源使用情況監控

## 7. 總結

本文檔詳細定義了網關與設備通信架構中三個主要組件（Server、App、Gateway）之間的協調和通信機制。通過明確的協議定義、清晰的流程說明和全面的測試標準，能確保各組件開發團隊有效協作，最終實現一個功能完整、運行穩定的系統。

### 7.1 核心設計總結

1. **設備與網關綁定機制**:
   - 設備初始狀態為未初始化
   - 首次被網關發現時自動設置主要網關
   - 支持通過UI界面手動更改主要網關
   - 保留其他發現設備的網關列表

2. **通信協議**:
   - 基於JSON的統一消息格式
   - 清晰定義的消息類型和數據結構
   - 支持網關註冊、設備發現、狀態上報等功能

3. **協調機制**:
   - 明確的初始配置流程
   - 完善的設備發現與初始化流程
   - 支持設備綁定用戶和變更主要網關

### 7.2 後續優化方向

隨著系統的實施和運行，可以考慮以下方向的優化：

1. **協議擴展**:
   - 增加對設備分組的支持
   - 增加設備批量操作指令
   - 支持更豐富的設備數據類型

2. **安全增強**:
   - 實現通信加密
   - 增強網關認證機制
   - 引入設備級別的安全標識

3. **性能優化**:
   - 引入消息隊列處理大量設備數據
   - 優化數據庫查詢和索引
   - 實現數據分片和分區存儲

4. **用戶體驗提升**:
   - 優化設備發現和初始化的用戶界面
   - 提供更豐富的設備狀態可視化
   - 增加自動化配置和故障診斷功能

通過本協調文檔的實施，各團隊可以在明確的框架下高效協作，同時為未來的系統擴展和優化預留了靈活空間。
