# WebSocket API 規範 - 即時狀態更新

## 概述

本文檔定義了EPD管理系統中設備和網關即時狀態更新的WebSocket API規範。前端通過這些API訂閱狀態更新，後端通過相應的事件推送狀態變化。

## 連接信息

- **WebSocket URL**: `ws://[server]/ws` 或 `wss://[server]/ws`
- **協議**: WebSocket
- **認證**: 通過JWT token或cookie
- **心跳**: 每30秒發送ping/pong

## 消息格式

所有WebSocket消息都使用JSON格式，包含以下基本結構：

```json
{
  "type": "message_type",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "data": {}
}
```

## 設備狀態更新API

### 訂閱設備狀態

**客戶端發送:**
```json
{
  "type": "subscribe_device_status",
  "storeId": "store_id_here",
  "options": {
    "includeImageStatus": true,
    "includeBatteryInfo": true
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**參數說明:**
- `storeId`: 門店ID，可選。如果不提供則訂閱所有門店
- `options.includeImageStatus`: 是否包含圖片更新狀態
- `options.includeBatteryInfo`: 是否包含電池和信號信息

**服務器回應:**
```json
{
  "type": "device_status_subscription_ack",
  "storeId": "store_id_here",
  "subscribed": true,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 取消訂閱設備狀態

**客戶端發送:**
```json
{
  "type": "unsubscribe_device_status",
  "storeId": "store_id_here",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**服務器回應:**
```json
{
  "type": "device_status_subscription_ack",
  "storeId": "store_id_here",
  "subscribed": false,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 設備狀態更新事件

**服務器推送:**
```json
{
  "type": "device_status_update",
  "storeId": "store_id_here",
  "devices": [
    {
      "_id": "device_id_1",
      "status": "online",
      "lastSeen": "2024-01-01T00:00:00.000Z",
      "imageUpdateStatus": "已更新",
      "data": {
        "battery": 85,
        "rssi": -65,
        "imageCode": "abc123"
      }
    },
    {
      "_id": "device_id_2",
      "status": "offline",
      "lastSeen": "2024-01-01T00:00:00.000Z",
      "imageUpdateStatus": "未更新",
      "data": {
        "battery": 45,
        "rssi": -78,
        "imageCode": "def456"
      }
    }
  ],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**字段說明:**
- `devices`: 設備狀態數組
- `devices[].status`: 設備狀態 ("online" | "offline")
- `devices[].lastSeen`: 最後見到時間 (ISO 8601格式)
- `devices[].imageUpdateStatus`: 圖片更新狀態 ("已更新" | "未更新" | "更新中")
- `devices[].data.battery`: 電池電量 (0-100)
- `devices[].data.rssi`: 信號強度 (dBm)
- `devices[].data.imageCode`: 圖片代碼

## 網關狀態更新API

### 訂閱網關狀態

**客戶端發送:**
```json
{
  "type": "subscribe_gateway_status",
  "storeId": "store_id_here",
  "options": {
    "includeConnectionInfo": true,
    "includeFirmwareInfo": true
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**參數說明:**
- `storeId`: 門店ID，可選
- `options.includeConnectionInfo`: 是否包含連接信息
- `options.includeFirmwareInfo`: 是否包含固件信息

**服務器回應:**
```json
{
  "type": "gateway_status_subscription_ack",
  "storeId": "store_id_here",
  "subscribed": true,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 取消訂閱網關狀態

**客戶端發送:**
```json
{
  "type": "unsubscribe_gateway_status",
  "storeId": "store_id_here",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 網關狀態更新事件

**服務器推送:**
```json
{
  "type": "gateway_status_update",
  "storeId": "store_id_here",
  "gateways": [
    {
      "_id": "gateway_id_1",
      "status": "online",
      "lastSeen": "2024-01-01T00:00:00.000Z",
      "name": "Gateway-001",
      "model": "EPD-GW-V2",
      "wifiFirmwareVersion": "1.2.3",
      "btFirmwareVersion": "2.1.0",
      "ipAddress": "*************"
    },
    {
      "_id": "gateway_id_2",
      "status": "offline",
      "lastSeen": "2024-01-01T00:00:00.000Z",
      "name": "Gateway-002",
      "model": "EPD-GW-V1",
      "wifiFirmwareVersion": "1.1.5",
      "btFirmwareVersion": "2.0.8",
      "ipAddress": "*************"
    }
  ],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**字段說明:**
- `gateways`: 網關狀態數組
- `gateways[].status`: 網關狀態 ("online" | "offline")
- `gateways[].lastSeen`: 最後見到時間
- `gateways[].name`: 網關名稱
- `gateways[].model`: 網關型號
- `gateways[].wifiFirmwareVersion`: WiFi固件版本
- `gateways[].btFirmwareVersion`: 藍牙固件版本
- `gateways[].ipAddress`: IP地址

## 通用消息

### 歡迎消息

**服務器發送:**
```json
{
  "type": "welcome",
  "message": "WebSocket connection established",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 心跳檢測

**客戶端發送:**
```json
{
  "type": "ping",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**服務器回應:**
```json
{
  "type": "pong",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 錯誤消息

**服務器發送:**
```json
{
  "type": "error",
  "code": "SUBSCRIPTION_FAILED",
  "message": "Failed to subscribe to device status",
  "details": {
    "storeId": "invalid_store_id",
    "reason": "Store not found"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**錯誤代碼:**
- `AUTHENTICATION_FAILED`: 認證失敗
- `SUBSCRIPTION_FAILED`: 訂閱失敗
- `INVALID_MESSAGE`: 無效消息格式
- `STORE_NOT_FOUND`: 門店不存在
- `PERMISSION_DENIED`: 權限不足

## 實現要求

### 服務器端要求

1. **連接管理**
   - 支持多個並發WebSocket連接
   - 每個連接維護獨立的訂閱狀態
   - 連接斷開時自動清理訂閱

2. **訂閱管理**
   - 支持按門店ID過濾訂閱
   - 支持選項配置（包含哪些信息）
   - 避免重複訂閱同一資源

3. **狀態監控**
   - 定期檢查設備和網關狀態
   - 檢測狀態變化並推送更新
   - 支持批量狀態更新

4. **性能優化**
   - 只推送有變化的狀態
   - 支持批量推送多個設備/網關
   - 合理的推送頻率控制

### 客戶端要求

1. **連接管理**
   - 自動重連機制
   - 連接狀態監控
   - 心跳檢測

2. **訂閱恢復**
   - 重連後自動恢復訂閱
   - 維護訂閱狀態
   - 避免重複訂閱

3. **錯誤處理**
   - 處理各種錯誤情況
   - 用戶友好的錯誤提示
   - 降級到手動刷新

## 安全考慮

### 認證授權
- 所有WebSocket連接必須經過認證
- 用戶只能訂閱有權限的門店數據
- 定期驗證token有效性

### 數據保護
- 敏感信息加密傳輸
- 避免洩露其他門店數據
- 記錄訪問日誌

### 防護措施
- 限制連接數量
- 防止訂閱濫用
- 監控異常行為

## 測試建議

### 功能測試
- 訂閱/取消訂閱流程
- 狀態更新推送
- 錯誤處理

### 性能測試
- 大量並發連接
- 高頻狀態更新
- 內存使用情況

### 穩定性測試
- 長時間運行
- 網絡中斷恢復
- 異常情況處理

## 版本兼容性

### 當前版本: 1.0
- 支持設備狀態更新
- 支持網關狀態更新
- 基本訂閱管理

### 未來版本計劃
- 1.1: 增加歷史狀態查詢
- 1.2: 支持自定義更新頻率
- 2.0: 支持更多設備類型
