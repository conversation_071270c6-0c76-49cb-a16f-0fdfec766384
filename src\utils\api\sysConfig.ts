/**
 * 系統配置默認值
 * 集中管理所有系統配置的默認值，方便日後修改
 */

import { SystemConfig } from "./sysConfigApi";

/**
 * 系統默認配置
 * 所有系統配置的默認值都在此定義
 */
export const DEFAULT_SYSTEM_CONFIG: SystemConfig = {
  // 連接逾時時間 (毫秒)
  timeout: 5000,

  // 連接重試次數
  retryCount: 3,

  // 緩衝區大小 (KB)
  bufferSize: 1024,

  // 最大資料綁定數量
  maxBindingDataCount: 8,

  // Gateway併發數量
  gatewayConcurrency: 20,

  // 隊列循環上限數量
  maxQueueCycles: 100,

  // 最大等待循環次數
  maxWaitCycles: 10
};

/**
 * 欄位視圖配置默認值
 */
export const DEFAULT_FIELDS_VIEW_CONFIG = {
  visibleFields: [],
  columnOrder: [],
  columnWidths: {}
};

/**
 * 獲取配置的默認值
 * @param configKey 配置鍵名
 * @returns 對應的默認配置值
 */
export function getDefaultConfig(configKey: string): any {
  switch(configKey) {
    case 'system':
      return DEFAULT_SYSTEM_CONFIG;
    case 'fieldsView':
      return DEFAULT_FIELDS_VIEW_CONFIG;
    default:
      return {};
  }
}
