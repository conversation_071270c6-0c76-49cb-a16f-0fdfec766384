/**
 * 設備數據綁定服務
 * 提供設備數據綁定的更新、記錄和通知功能
 */

const { ObjectId } = require('mongodb');
const { logDeviceEvent } = require('./websocketService');

// 共享的數據庫連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取設備集合
const getDeviceCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('devices');
  return { collection, client };
};

/**
 * 更新設備數據綁定
 * @param {string} deviceId 設備ID
 * @param {Object} bindingData 綁定數據
 * @param {string} bindingData.templateId 模板ID
 * @param {Object|string} bindingData.dataBindings 數據綁定關係 (對象或JSON字符串)
 * @param {string} [bindingData.dataId] 向下兼容的單一數據ID
 * @param {string} [userId] 執行操作的用戶ID
 * @param {string} [storeId] 門店ID
 * @param {Object} [options] 選項
 * @param {boolean} [options.sendToGateway=true] 是否自動發送到網關
 * @returns {Promise<Object>} 更新後的設備數據
 */
const updateDeviceDataBindings = async (deviceId, bindingData, userId = null, storeId = null, options = {}) => {
  // 設置默認選項
  const { sendToGateway = true } = options;
  try {
    // 驗證參數
    if (!deviceId) {
      throw new Error('設備ID不能為空');
    }

    if (!bindingData || typeof bindingData !== 'object') {
      throw new Error('綁定數據必須是有效的對象');
    }

    // 獲取設備集合
    const { collection } = await getDeviceCollection();

    // 構建查詢條件
    const query = { _id: new ObjectId(deviceId) };

    // 如果提供了門店ID，則確保設備屬於該門店
    if (storeId) {
      query.storeId = storeId;
    }

    // 檢查設備是否存在
    const existingDevice = await collection.findOne(query);
    if (!existingDevice) {
      throw new Error('設備不存在或不屬於指定門店');
    }

    // 準備更新數據
    const updateData = {
      updatedAt: new Date()
    };

    // 處理模板ID
    if (bindingData.templateId !== undefined) {
      updateData.templateId = bindingData.templateId;
    }

    // 處理數據綁定
    if (bindingData.dataBindings !== undefined) {
      // 如果是對象，轉換為JSON字符串
      if (typeof bindingData.dataBindings === 'object') {
        updateData.dataBindings = JSON.stringify(bindingData.dataBindings);
      } else if (typeof bindingData.dataBindings === 'string') {
        // 驗證是否為有效的JSON字符串
        try {
          JSON.parse(bindingData.dataBindings);
          updateData.dataBindings = bindingData.dataBindings;
        } catch (e) {
          throw new Error('數據綁定必須是有效的JSON字符串或對象');
        }
      } else {
        throw new Error('數據綁定必須是有效的JSON字符串或對象');
      }
    }

    // 處理向下兼容的單一數據ID
    if (bindingData.dataId !== undefined) {
      updateData.dataId = bindingData.dataId;
    }

    // 更新設備數據
    await collection.updateOne(
      { _id: new ObjectId(deviceId) },
      { $set: updateData }
    );

    // 獲取更新後的設備數據
    const updatedDevice = await collection.findOne({ _id: new ObjectId(deviceId) });

    // 記錄數據綁定變更事件
    await logDataBindingChangeEvent(deviceId, existingDevice, updatedDevice, userId);

    // 根據 sendToGateway 參數決定是否通過 WebSocket 通知網關更新設備顯示
    if (sendToGateway) {
      console.log(`自動發送設備 ${deviceId} 的數據綁定更新到網關...`);

      // 不再清除設備的預覽圖，而是使用已保存的預覽圖
      // 預覽圖的更新應該由客戶端控制，只有在用戶選擇了"立即更新"時才會更新
      console.log(`使用設備 ${deviceId} 的現有預覽圖發送到網關`);

      // 獲取更新後的設備數據
      const deviceToUpdate = await collection.findOne({ _id: new ObjectId(deviceId) });

      // 通知網關更新設備顯示
      await notifyGatewayToUpdateDevice(deviceToUpdate);
    } else {
      console.log(`跳過發送設備 ${deviceId} 的數據綁定更新到網關 (sendToGateway=false)`);
    }

    return updatedDevice;
  } catch (error) {
    console.error('更新設備數據綁定失敗:', error);
    throw error;
  }
};

/**
 * 記錄數據綁定變更事件
 * @param {string} deviceId 設備ID
 * @param {Object} oldDevice 舊設備數據
 * @param {Object} newDevice 新設備數據
 * @param {string} [userId] 執行操作的用戶ID
 * @returns {Promise<Object>} 事件記錄
 */
const logDataBindingChangeEvent = async (deviceId, oldDevice, newDevice, userId = null) => {
  try {
    // 準備事件數據
    const eventData = {
      action: 'update_data_bindings',
      changes: {}
    };

    // 記錄模板ID變更
    if (oldDevice.templateId !== newDevice.templateId) {
      eventData.changes.templateId = {
        old: oldDevice.templateId || null,
        new: newDevice.templateId || null
      };
    }

    // 記錄數據ID變更
    if (oldDevice.dataId !== newDevice.dataId) {
      eventData.changes.dataId = {
        old: oldDevice.dataId || null,
        new: newDevice.dataId || null
      };
    }

    // 記錄數據綁定變更
    if (oldDevice.dataBindings !== newDevice.dataBindings) {
      eventData.changes.dataBindings = {
        updated: true
      };
    }

    // 如果有用戶ID，添加到事件數據
    if (userId) {
      eventData.userId = userId;

      // 嘗試獲取用戶信息
      try {
        const { db } = await getDbConnection();
        const userCollection = db.collection('users');
        const user = await userCollection.findOne({ _id: new ObjectId(userId) });

        if (user) {
          eventData.userName = user.name || user.username || '未知';
          eventData.userEmail = user.email || '未知';
        }
      } catch (userError) {
        console.warn('獲取用戶信息失敗:', userError);
      }
    }

    // 記錄事件
    return await logDeviceEvent(deviceId, 'data_binding_changed', eventData);
  } catch (error) {
    console.error('記錄數據綁定變更事件失敗:', error);
    // 不拋出錯誤，避免中斷主流程
    return null;
  }
};

/**
 * 通知網關更新設備顯示
 * @param {Object} device 設備數據
 * @returns {Promise<Object>} 通知結果
 */
const notifyGatewayToUpdateDevice = async (device) => {
  try {
    // 檢查設備是否有主要網關
    if (!device.primaryGatewayId) {
      console.warn(`設備 ${device._id} 沒有主要網關，無法發送更新通知`);
      return { success: false, message: '設備沒有主要網關' };
    }

    // 使用 sendPreviewToGateway 服務來生成和發送預覽圖片
    // 這會將數據綁定轉換為圖片，然後通過 WebSocket 發送到網關
    const sendPreviewToGateway = require('./sendPreviewToGateway');

    // 記錄日誌
    console.log(`正在為設備 ${device._id} 生成預覽圖並發送到網關...`);

    // 調用 sendDevicePreviewToGateway 函數
    // 如果設備沒有預覽圖，這個函數會嘗試生成一個
    const result = await sendPreviewToGateway.sendDevicePreviewToGateway(device._id.toString(), {
      sendToAllGateways: false // 只發送到主要網關
    });

    // 記錄結果
    if (result.success) {
      console.log(`成功發送設備 ${device._id} 的預覽圖到網關 ${device.primaryGatewayId}`);
    } else {
      console.error(`發送設備 ${device._id} 的預覽圖到網關失敗:`, result.error);
    }

    return result;
  } catch (error) {
    console.error('通知網關更新設備顯示失敗:', error);
    return { success: false, error: error.message };
  }
};

// 公開的 API
module.exports = {
  initDB,
  updateDeviceDataBindings,
  logDataBindingChangeEvent,
  notifyGatewayToUpdateDevice
};
