/**
 * 加密與校驗相關工具函數
 */

/**
 * 計算字符串或數據的 CRC32 校驗碼
 * @param data 要計算的數據，可以是字符串或任何可被JSON序列化的數據
 * @returns 4字節的十六進制校驗碼字符串
 */
export function calculateCRC32(data: any): string {
  // 將任何數據轉換為字符串
  const str = typeof data === 'string' ? data : JSON.stringify(data);
  
  // 計算簡單的CRC32
  let crc = 0;
  for (let i = 0; i < str.length; i++) {
    crc = ((crc << 5) + crc) ^ str.charCodeAt(i);
  }
  
  // 轉換為4字節的十六進制字符串
  return (crc >>> 0).toString(16).padStart(8, '0');
}
