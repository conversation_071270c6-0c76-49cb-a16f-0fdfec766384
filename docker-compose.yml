name: epd-manager-lite
version: '3.8'

services:
  # EPD Manager 統一服務 (前端 + 後端)
  epd-manager:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: epd-manager
    restart: unless-stopped
    ports:
      # 注意：容器內端口固定為 5173 和 3001，避免程式碼調用錯誤
      - "${FRONTEND_PORT:-5173}:5173"  # 外部端口可自定義，內部固定 5173
      - "${SERVER_PORT:-3001}:3001"    # 外部端口可自定義，內部固定 3001
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - MONGO_URI=mongodb://mongodb:27017  # 容器間通信，固定端口
      - MONGO_DB=${MONGO_DB:-resourceManagement}
      - JWT_SECRET=${JWT_SECRET}
      - TEST_MODE=${TEST_MODE}
      - FLOATING_BUTTON=${FLOATING_BUTTON}
    volumes:
      - server_uploads:/app/server/uploads
      - server_logs:/app/server/logs
    depends_on:
      mongodb:
        condition: service_started
    networks:
      - epd-network

  # MongoDB 資料庫
  mongodb:
    image: mongo:7-jammy
    container_name: epd-manager-mongodb
    restart: unless-stopped
    ports:
      - "${MONGO_PORT:-27017}:27017"
    environment:
      - MONGO_INITDB_DATABASE=${MONGO_DB:-resourceManagement}
    volumes:
      - mongo_data:/data/db
    command: mongod --wiredTigerCacheSizeGB 1.5 --bind_ip_all
    networks:
      - epd-network

volumes:
  mongo_data:
    driver: local
  server_uploads:
    driver: local
  server_logs:
    driver: local

networks:
  epd-network:
    driver: bridge