const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { authenticate, checkPermission } = require('../middleware/auth');
const { hashPassword } = require('../utils/auth');
const { ObjectId } = require('mongodb');

// 數據庫連接函數引用
let connectDBFunction;

// 初始化數據庫連接
function initDB(connectDB) {
  connectDBFunction = connectDB;
  return connectDBFunction;
}

/**
 * 測試路由 (無需認證)
 * GET /api/users/test
 */
router.get('/users/test', async (req, res) => {
  try {
    console.log('收到測試請求');

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 檢查數據庫連接
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);

    // 檢查用戶集合
    const usersCount = await db.collection('users').countDocuments();

    console.log('測試成功:', { collections: collectionNames, usersCount });

    res.json({
      status: 'ok',
      message: '數據庫連接正常',
      collections: collectionNames,
      usersCount
    });
  } catch (error) {
    console.error('測試路由錯誤:', error);
    res.status(500).json({ error: '測試失敗: ' + error.message });
  }
});

/**
 * 獲取所有用戶
 * GET /api/users
 */
router.get('/users', authenticate, async (req, res) => {
  try {
    // 獲取查詢參數
    const { status, search, page = 1, limit = 10 } = req.query;

    // 構建過濾條件
    const filter = {};

    if (status) {
      filter.status = status;
    }

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    let users;

    // 如果有搜索關鍵詞，使用搜索功能
    if (search) {
      users = await User.searchUsers(db, search);
    } else {
      // 分頁查詢
      const skip = (page - 1) * limit;
      users = await User.findAll(db, filter, { skip, limit: parseInt(limit) });
    }

    // 獲取總數
    const total = await db.collection('users').countDocuments(filter);

    // 移除密碼字段
    const usersWithoutPassword = users.map(user => {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });

    res.json({
      users: usersWithoutPassword,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total
      }
    });
  } catch (error) {
    console.error('獲取用戶列表錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 獲取單個用戶
 * GET /api/users/:id
 */
router.get('/users/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 查找用戶
    const user = await User.findById(db, id);

    if (!user) {
      return res.status(404).json({ error: '用戶不存在' });
    }

    // 移除密碼字段
    const { password, ...userWithoutPassword } = user;

    res.json(userWithoutPassword);
  } catch (error) {
    console.error('獲取用戶錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 創建用戶
 * POST /api/users
 */
router.post('/users', authenticate, checkPermission('user:create'), async (req, res) => {
  try {
    console.log('收到創建用戶請求:', req.body);
    const { username, password, name, email, phone, status } = req.body;

    if (!username || !password) {
      console.log('用戶名或密碼為空');
      return res.status(400).json({ error: '用戶名和密碼不能為空' });
    }

    // 獲取數據庫連接
    console.log('獲取數據庫連接');
    const { db } = await connectDBFunction();

    // 加密密碼
    console.log('加密密碼');
    const hashedPassword = await hashPassword(password);

    // 創建用戶
    console.log('創建用戶');
    const user = await User.createUser(db, {
      username,
      password: hashedPassword,
      name,
      email,
      phone,
      status: status || 'active'
    });

    console.log('用戶創建成功:', user._id);

    // 移除密碼字段
    const { password: _, ...userWithoutPassword } = user;

    res.status(201).json(userWithoutPassword);
  } catch (error) {
    console.error('創建用戶錯誤:', error);

    if (error.message === '用戶名已存在') {
      return res.status(400).json({ error: error.message });
    }

    res.status(500).json({ error: '伺服器錯誤: ' + error.message });
  }
});

/**
 * 更新用戶
 * PUT /api/users/:id
 */
router.put('/users/:id', authenticate, checkPermission('user:update'), async (req, res) => {
  try {
    const { id } = req.params;
    const { username, name, email, phone, status } = req.body;

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 檢查用戶是否存在
    const existingUser = await User.findById(db, id);

    if (!existingUser) {
      return res.status(404).json({ error: '用戶不存在' });
    }

    // 更新用戶
    await User.updateUser(db, id, {
      username,
      name,
      email,
      phone,
      status
    });

    // 獲取更新後的用戶
    const updatedUser = await User.findById(db, id);

    // 移除密碼字段
    const { password, ...userWithoutPassword } = updatedUser;

    res.json(userWithoutPassword);
  } catch (error) {
    console.error('更新用戶錯誤:', error);

    if (error.message === '用戶名已存在') {
      return res.status(400).json({ error: error.message });
    }

    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 刪除用戶
 * DELETE /api/users/:id
 */
router.delete('/users/:id', authenticate, checkPermission('user:delete'), async (req, res) => {
  try {
    const { id } = req.params;

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 檢查用戶是否存在
    const existingUser = await User.findById(db, id);

    if (!existingUser) {
      return res.status(404).json({ error: '用戶不存在' });
    }

    // 檢查是否嘗試刪除自己
    if (id === req.user._id.toString()) {
      return res.status(400).json({ error: '不能刪除當前登入的用戶' });
    }

    // 刪除用戶
    await User.deleteUser(db, id);

    // 刪除用戶的權限分配
    const Permission = require('../models/Permission');
    await db.collection('permissions').deleteMany({ userId: new ObjectId(id) });

    res.json({ message: '用戶刪除成功' });
  } catch (error) {
    console.error('刪除用戶錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 重設用戶密碼
 * POST /api/users/:id/reset-password
 */
router.post('/users/:id/reset-password', authenticate, checkPermission('user:update'), async (req, res) => {
  try {
    const { id } = req.params;

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 檢查用戶是否存在
    const existingUser = await User.findById(db, id);

    if (!existingUser) {
      return res.status(404).json({ error: '用戶不存在' });
    }

    // 生成新密碼
    const newPassword = '123456789'; // 預設密碼

    // 加密新密碼
    const hashedPassword = await hashPassword(newPassword);

    // 更新密碼
    await User.resetPassword(db, id, hashedPassword);

    res.json({
      message: '密碼重設成功',
      defaultPassword: newPassword
    });
  } catch (error) {
    console.error('重設密碼錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 獲取用戶綁定的設備
 * GET /api/users/:id/devices
 */
router.get('/users/:id/devices', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    // 獲取數據庫連接
    const { db } = await connectDBFunction();
    
    // 檢查用戶是否存在
    const user = await User.findById(db, id);
    
    if (!user) {
      return res.status(404).json({ error: '用戶不存在' });
    }
    
    // 獲取用戶綁定的所有設備
    const devices = await db.collection('devices').find({
      userId: new ObjectId(id)
    }).toArray();
    
    res.json({
      user: {
        _id: user._id,
        username: user.username,
        name: user.name
      },
      devices: devices
    });
  } catch (error) {
    console.error('獲取用戶設備錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 批量刪除用戶
 * DELETE /api/users
 */
router.delete('/users', authenticate, checkPermission('user:delete'), async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ error: '請提供要刪除的用戶 ID 列表' });
    }

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 檢查是否嘗試刪除自己
    if (ids.includes(req.user._id.toString())) {
      return res.status(400).json({ error: '不能刪除當前登入的用戶' });
    }

    // 批量刪除用戶
    const objectIds = ids.map(id => new ObjectId(id));

    await db.collection('users').deleteMany({ _id: { $in: objectIds } });

    // 刪除用戶的權限分配
    await db.collection('permissions').deleteMany({ userId: { $in: objectIds } });

    res.json({ message: '用戶批量刪除成功' });
  } catch (error) {
    console.error('批量刪除用戶錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 獲取當前用戶的權限
 * GET /api/users/me/permissions
 */
router.get('/users/me/permissions', authenticate, async (req, res) => {
  try {
    const { user, db } = req;

    // 獲取用戶的所有權限分配
    const Permission = require('../models/Permission');
    const userPermissions = await Permission.findByUserId(db, user._id.toString());

    // 獲取用戶的所有角色
    const roleIds = userPermissions.map(p => p.roleId);
    const Role = require('../models/Role');

    // 獲取角色詳情
    const roles = [];
    const permissions = new Set();

    for (const roleId of roleIds) {
      const role = await Role.findById(db, roleId.toString());

      if (role) {
        roles.push(role);

        // 如果角色有 'all' 權限，則添加所有權限
        if (role.permissions.includes('all')) {
          permissions.add('all');
        } else {
          // 否則添加角色的特定權限
          role.permissions.forEach(p => permissions.add(p));
        }
      }
    }

    res.json({
      roles,
      permissions: Array.from(permissions)
    });
  } catch (error) {
    console.error('獲取用戶權限錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

module.exports = { router, initDB };
