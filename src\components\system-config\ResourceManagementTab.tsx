import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Search, Upload, Video, MoreHorizontal, X } from 'lucide-react';
import { buildEndpointUrl } from '../../utils/api/apiConfig';

interface FileItem {
  _id: string;
  filename: string;
  size: number;
}

// API 函數，替代直接的MongoDB操作
const apiService = {
  async uploadFile(file: File) {
    // 創建FormData對象進行文件上傳
    const formData = new FormData();
    formData.append('file', file);
      try {
      // 使用 API 配置系統構建 URL
      const response = await fetch(buildEndpointUrl('files', 'upload'), {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`上傳失敗: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('上傳文件時出錯:', error);
      throw error;
    }
  },

  async uploadMultipleFiles(files: File[]) {
    // 創建FormData對象進行多文件上傳
    const formData = new FormData();

    // 添加多個文件，使用相同的字段名 'files'
    files.forEach(file => {
      formData.append('files', file);
    });
      try {
      // 使用 API 配置系統構建 URL
      const response = await fetch(buildEndpointUrl('files', 'upload-multiple'), {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`上傳失敗: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('上傳多個文件時出錯:', error);
      throw error;
    }
  },
    async getFiles() {
    try {
      const response = await fetch(buildEndpointUrl('files'));

      if (!response.ok) {
        throw new Error(`獲取文件失敗: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('獲取文件列表時出錯:', error);
      return [];
    }
  },
    async deleteFile(fileId: string) {
    try {
      const response = await fetch(buildEndpointUrl('files', fileId), {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`刪除文件失敗: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('刪除文件時出錯:', error);
      throw error;
    }
  }
};

export function ResourceManagementTab() {
  const { t } = useTranslation();
  const [files, setFiles] = useState<FileItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]); // 新增狀態用於存儲選擇的文件
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 組件加載時獲取文件列表
  useEffect(() => {
    const loadFiles = async () => {
      try {
        const fetchedFiles = await apiService.getFiles();
        setFiles(fetchedFiles);
      } catch (error) {
        console.error('加載文件列表時出錯:', error);
      }
    };

    loadFiles();
  }, []);

  // 處理文件選擇後的上傳
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const newFiles = event.target.files;
    if (!newFiles || newFiles.length === 0) return;

    // 將 FileList 轉換為數組並添加到已選文件中
    const filesArray = Array.from(newFiles);
    setSelectedFiles(prevFiles => [...prevFiles, ...filesArray]);
  };

  // 處理移除單個選定的文件
  const handleRemoveSelectedFile = (indexToRemove: number) => {
    setSelectedFiles(prevFiles =>
      prevFiles.filter((_, index) => index !== indexToRemove)
    );
  };

  // 處理上傳多個文件
  const handleUploadFiles = async () => {
    if (selectedFiles.length === 0) return;

    try {
      await apiService.uploadMultipleFiles(selectedFiles);

      // 刷新文件列表
      const updatedFiles = await apiService.getFiles();
      setFiles(updatedFiles);

      // 重置選擇的文件並關閉模態框
      setSelectedFiles([]);
      setShowUploadModal(false);
    } catch (error) {
      console.error('上傳文件時出錯:', error);
    }
  };

  // 處理文件刪除
  const handleDelete = async (fileId: string) => {
    try {
      await apiService.deleteFile(fileId);
      const updatedFiles = await apiService.getFiles();
      setFiles(updatedFiles);
    } catch (error) {
      console.error('刪除文件時出錯:', error);
    }
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <select className="px-4 py-2 border rounded-lg">
            <option>Select</option>
          </select>
          <div className="relative">
            <input
              type="text"
              placeholder={t('systemConfig.searchName')}
              className="pl-10 pr-4 py-2 border rounded-lg"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowUploadModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            <Upload className="w-5 h-5" />
            {t('systemConfig.uploadImage')}
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
            <Video className="w-5 h-5" />
            Upload video
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600">
            <MoreHorizontal className="w-5 h-5" />
            More
          </button>
        </div>
      </div>

      {/* File Grid */}
      <div className="grid grid-cols-4 gap-4">
        {files.map((file) => (
          <div key={file._id} className="relative group">
            <input type="checkbox" className="absolute top-2 left-2 z-10" />            <div className="bg-gray-100 aspect-square rounded-lg overflow-hidden">
              <img
                src={buildEndpointUrl('files', file._id)}
                alt={file.filename}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="mt-2">
              <p className="text-sm">{file.filename}</p>
              <p className="text-xs text-gray-500">{(file.size / 1024).toFixed(1)}kb</p>
            </div>
            <button
              onClick={() => handleDelete(file._id)}
              className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="mt-4 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button className="px-4 py-2 bg-blue-500 text-white rounded-lg">1</button>
          <select className="px-2 py-1 border rounded">
            <option>6/page</option>
          </select>
          <span>1 {t('common.pagesTotal')}</span>
        </div>
        <div className="flex items-center gap-2">
          <span>{t('common.goTo')}</span>
          <input
            type="number"
            className="w-16 px-2 py-1 border rounded"
            value={currentPage}
            onChange={(e) => setCurrentPage(Number(e.target.value))}
          />
          <span>{t('common.page')}</span>
          <button className="px-4 py-1 border rounded hover:bg-gray-50">
            {t('common.confirm')}
          </button>
        </div>
      </div>

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg w-96">
            <h3 className="text-lg font-semibold mb-4">{t('systemConfig.uploadImage')}</h3>
            <div className="flex flex-col items-center gap-4">
              <button
                onClick={() => fileInputRef.current?.click()}
                className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                {t('systemConfig.selectImage')}
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple // 添加 multiple 屬性以支持多選
                className="hidden"
                onChange={handleFileSelect}
              />

              {/* 顯示已選圖片列表 */}
              {selectedFiles.length > 0 && (
                <div className="w-full mt-2">
                  <p className="text-sm text-gray-600 mb-2">已選擇 {selectedFiles.length} 個檔案：</p>
                  <div className="max-h-40 overflow-y-auto">
                    {selectedFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between py-1 px-2 border-b">
                        <span className="text-sm truncate max-w-[200px]">{file.name}</span>
                        <button
                          onClick={() => handleRemoveSelectedFile(index)}
                          className="ml-2 p-1 text-red-500 hover:bg-red-50 rounded"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex gap-4">
                <button
                  onClick={() => {
                    setSelectedFiles([]);
                    setShowUploadModal(false);
                  }}
                  className="px-6 py-2 bg-gray-200 rounded-lg hover:bg-gray-300"
                >
                  {t('common.cancel')}
                </button>
                <button
                  onClick={handleUploadFiles}
                  disabled={selectedFiles.length === 0}
                  className={`px-6 py-2 rounded-lg ${
                    selectedFiles.length === 0
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                  }`}
                >
                  {t('common.upload')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}