const { renderIconSvg } = require('../utils/iconRenderer');

console.log('=== 測試 package 和 clock icon 渲染修復 ===\n');

// 測試參數
const testIcons = ['package', 'clock'];
const testOptions = {
  size: 24,
  color: '#ff0000',
  strokeWidth: 3
};

testIcons.forEach(iconType => {
  console.log(`\n--- 測試 ${iconType} icon ---`);
  
  try {
    const svgContent = renderIconSvg(iconType, testOptions);
    
    console.log('生成的 SVG 內容:');
    console.log(svgContent);
    
    // 檢查是否包含所有必要的元素
    const hasCircle = svgContent.includes('<circle');
    const hasPath = svgContent.includes('<path');
    const hasPolyline = svgContent.includes('<polyline');
    
    console.log('\n元素檢查:');
    console.log(`- 包含 circle: ${hasCircle}`);
    console.log(`- 包含 path: ${hasPath}`);
    console.log(`- 包含 polyline: ${hasPolyline}`);
    
    // 檢查顏色設定
    const rootStrokeMatches = svgContent.match(/<svg[^>]*stroke="[^"]*"/g);
    const allStrokeMatches = svgContent.match(/stroke="[^"]*"/g);
    
    console.log('\n顏色設定檢查:');
    console.log(`- SVG 根元素 stroke 設定: ${rootStrokeMatches ? rootStrokeMatches.length : 0} 個`);
    console.log(`- 所有 stroke 設定: ${allStrokeMatches ? allStrokeMatches.length : 0} 個`);
    
    if (rootStrokeMatches) {
      console.log(`- 根元素 stroke 值: ${rootStrokeMatches[0]}`);
    }
    
    // 檢查是否有內部元素的 stroke 被保留
    if (iconType === 'package') {
      // package icon 應該有多個 path 元素
      const pathCount = (svgContent.match(/<path/g) || []).length;
      console.log(`- package icon path 元素數量: ${pathCount} (預期: 4)`);
    }
    
    if (iconType === 'clock') {
      // clock icon 應該有 circle 和 polyline
      console.log(`- clock icon 包含 circle: ${hasCircle} (預期: true)`);
      console.log(`- clock icon 包含 polyline: ${hasPolyline} (預期: true)`);
    }
    
    console.log(`\n✓ ${iconType} icon 渲染完成`);
    
  } catch (error) {
    console.error(`✗ ${iconType} icon 渲染失敗:`, error.message);
  }
});

console.log('\n=== 測試完成 ===');
