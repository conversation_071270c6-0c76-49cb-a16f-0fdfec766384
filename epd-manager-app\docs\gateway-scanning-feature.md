# 網關掃描功能設計

## 1. 功能概述

網關掃描功能允許用戶在移動應用中掃描本地網絡中的網關設備，並將其添加到服務器。該功能在以下兩種情況下特別重要：

1. **初次使用**：當用戶帳號內沒有任何網關時，需要掃描並添加網關
2. **添加新網關**：當用戶需要添加新的網關設備到已有的帳號時

## 2. 技術實現

### 2.1 UDP 廣播掃描

使用 UDP 廣播是發現本地網絡設備的有效方法。在 React Native 中，我們需要使用原生模塊來實現 UDP 通信。

#### 2.1.1 技術選擇

- **react-native-udp**：用於 UDP 通信的 React Native 模塊
- **react-native-network-info**：獲取設備的 IP 地址和子網掩碼
- **react-native-permissions**：處理網絡權限請求

#### 2.1.2 UDP 掃描流程

1. 獲取設備當前的 IP 地址和子網掩碼
2. 計算廣播地址（通常是子網的最後一個地址，如 *************）
3. 創建 UDP socket
4. 發送特定格式的廣播消息到廣播地址的特定端口（如 UDP 端口 5000）
5. 監聽回應，收集回應的網關信息
6. 解析回應數據，提取網關的 MAC 地址、IP 地址、型號等信息

### 2.2 代碼實現

```javascript
// services/gatewayScanner.js
import UdpSocket from 'react-native-udp';
import { NetworkInfo } from 'react-native-network-info';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';

class GatewayScanner {
  constructor() {
    this.socket = null;
    this.discoveredGateways = [];
    this.isScanning = false;
    this.scanTimeout = null;
    this.listeners = [];
  }

  // 檢查並請求網絡權限
  async checkPermissions() {
    const result = await check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);

    if (result !== RESULTS.GRANTED) {
      const permissionResult = await request(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
      return permissionResult === RESULTS.GRANTED;
    }

    return true;
  }

  // 開始掃描
  async startScan(timeout = 10000) {
    if (this.isScanning) return false;

    // 檢查權限
    const hasPermission = await this.checkPermissions();
    if (!hasPermission) {
      throw new Error('需要位置權限來掃描網關');
    }

    this.isScanning = true;
    this.discoveredGateways = [];

    // 獲取本機 IP 地址
    const ipAddress = await NetworkInfo.getIPAddress();

    // 獲取子網掩碼
    const subnet = await NetworkInfo.getSubnet();

    // 計算廣播地址
    const broadcastAddress = this.calculateBroadcastAddress(ipAddress, subnet);

    // 創建 UDP socket
    this.socket = UdpSocket.createSocket({ type: 'udp4' });

    // 綁定到任意端口
    this.socket.bind(0);

    // 監聽回應
    this.socket.on('message', (msg, rinfo) => {
      this.handleResponse(msg, rinfo);
    });

    // 發送廣播消息
    const scanMessage = this.createScanMessage();
    this.socket.send(scanMessage, 0, scanMessage.length, 5000, broadcastAddress);

    // 設置超時
    this.scanTimeout = setTimeout(() => {
      this.stopScan();
    }, timeout);

    return true;
  }

  // 停止掃描
  stopScan() {
    if (!this.isScanning) return;

    this.isScanning = false;

    if (this.scanTimeout) {
      clearTimeout(this.scanTimeout);
      this.scanTimeout = null;
    }

    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    // 通知監聽器掃描完成
    this.notifyListeners('scanComplete', this.discoveredGateways);
  }

  // 處理網關回應
  handleResponse(msg, rinfo) {
    try {
      // 將 buffer 轉換為字符串
      const response = msg.toString('utf8');

      // 解析 JSON 回應
      const gatewayInfo = JSON.parse(response);

      // 驗證是否為有效的網關回應
      if (this.isValidGatewayResponse(gatewayInfo)) {
        // 添加來源 IP 地址
        gatewayInfo.ipAddress = rinfo.address;

        // 檢查是否已經發現該網關
        const existingIndex = this.discoveredGateways.findIndex(
          g => g.macAddress === gatewayInfo.macAddress
        );

        if (existingIndex === -1) {
          // 添加新發現的網關
          this.discoveredGateways.push(gatewayInfo);

          // 通知監聽器發現新網關
          this.notifyListeners('gatewayDiscovered', gatewayInfo);
        }
      }
    } catch (error) {
      console.error('處理網關回應時出錯:', error);
    }
  }

  // 創建掃描消息
  createScanMessage() {
    const message = {
      type: 'discovery',
      protocol: 'EPD-GATEWAY-DISCOVERY',
      version: '1.0'
    };

    return Buffer.from(JSON.stringify(message));
  }

  // 驗證網關回應
  isValidGatewayResponse(response) {
    return (
      response &&
      response.type === 'discovery-response' &&
      response.protocol === 'EPD-GATEWAY-DISCOVERY' &&
      response.macAddress &&
      response.model
    );
  }

  // 計算廣播地址
  calculateBroadcastAddress(ipAddress, subnet) {
    // 簡單實現：將 IP 地址的最後一個八位元組設為 255
    // 實際應用中應該根據子網掩碼計算
    const ipParts = ipAddress.split('.');
    ipParts[3] = '255';
    return ipParts.join('.');
  }

  // 添加事件監聽器
  addListener(listener) {
    this.listeners.push(listener);
    return () => this.removeListener(listener);
  }

  // 移除事件監聽器
  removeListener(listener) {
    const index = this.listeners.indexOf(listener);
    if (index !== -1) {
      this.listeners.splice(index, 1);
    }
  }

  // 通知所有監聽器
  notifyListeners(event, data) {
    this.listeners.forEach(listener => {
      if (typeof listener[event] === 'function') {
        listener[event](data);
      }
    });
  }

  // 獲取已發現的網關
  getDiscoveredGateways() {
    return [...this.discoveredGateways];
  }
}

export default new GatewayScanner();
```

## 3. 網關設備回應規範

為了使掃描功能正常工作，網關設備需要實現特定的 UDP 回應機制。

### 3.1 回應格式

網關設備應監聽 UDP 端口 5000，並對特定的掃描消息做出回應。回應應使用 JSON 格式，包含以下字段：

```json
{
  "type": "discovery-response",
  "protocol": "EPD-GATEWAY-DISCOVERY",
  "version": "1.0",
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "model": "GW-2000",
  "name": "Gateway-1234",
  "firmwareVersion": "1.0.0",
  "capabilities": ["wifi", "bluetooth"],
  "status": "ready"
}
```

### 3.2 必要字段說明

- **type**: 固定為 "discovery-response"，表示這是對掃描請求的回應
- **protocol**: 固定為 "EPD-GATEWAY-DISCOVERY"，表示協議類型
- **version**: 協議版本，當前為 "1.0"
- **macAddress**: 網關的 MAC 地址，格式為 XX:XX:XX:XX:XX:XX
- **model**: 網關型號
- **name**: 網關名稱（可選）
- **firmwareVersion**: 固件版本（可選）
- **capabilities**: 網關功能列表（可選）
- **status**: 網關狀態（可選）

### 3.3 網關設備實現要點

1. 網關設備應在啟動時開始監聽 UDP 端口 5000
2. 當收到類型為 "discovery" 且協議為 "EPD-GATEWAY-DISCOVERY" 的消息時，應立即回應
3. 回應應發送到請求的源 IP 地址和源端口
4. 為避免網絡擁塞，可以添加隨機延遲（如 0-500ms）
5. 網關應該能夠處理多次掃描請求，每次都做出回應

### 3.4 網關設備 JavaScript 實現範例

以下是使用 Node.js 實現網關設備 UDP 監聽和回應功能的範例代碼：

```javascript
// gateway-discovery-service.js
const dgram = require('dgram');
const os = require('os');

// 配置
const UDP_PORT = 5000;
const PROTOCOL = "EPD-GATEWAY-DISCOVERY";
const VERSION = "1.0";

// 生成隨機 MAC 地址（實際應用中應使用設備的真實 MAC 地址）
function generateMacAddress() {
  return Array.from({ length: 6 }, () =>
    Math.floor(Math.random() * 256).toString(16).padStart(2, '0')
  ).join(':').toUpperCase();
}

// 獲取本機 IP 地址
function getLocalIpAddress() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return '*************'; // 默認 IP 地址
}

// 網關信息
const gatewayInfo = {
  macAddress: generateMacAddress(),
  model: "GW-2000",
  name: `Gateway-${Math.floor(1000 + Math.random() * 9000)}`,
  firmwareVersion: "1.0.0",
  ipAddress: getLocalIpAddress(),
  capabilities: ["wifi", "bluetooth"],
  status: "ready"
};

// 創建 UDP 服務器
const server = dgram.createSocket('udp4');

// 處理錯誤
server.on('error', (err) => {
  console.error(`UDP 服務器錯誤: ${err.stack}`);
  server.close();
});

// 處理消息
server.on('message', (msg, rinfo) => {
  try {
    // 解析 JSON 數據
    const request = JSON.parse(msg.toString());

    console.log(`收到來自 ${rinfo.address}:${rinfo.port} 的消息:`, request);

    // 驗證消息格式和協議
    if (request.type === 'discovery' &&
        request.protocol === PROTOCOL &&
        request.version === VERSION) {

      console.log('收到有效的發現請求，準備回應');

      // 創建回應消息
      const response = {
        type: "discovery-response",
        protocol: PROTOCOL,
        version: VERSION,
        timestamp: Date.now(),
        ...gatewayInfo
      };

      // 添加隨機延遲，避免網絡擁塞
      const delay = Math.random() * 400 + 100; // 100-500ms
      setTimeout(() => {
        // 發送回應
        const responseBuffer = Buffer.from(JSON.stringify(response));
        server.send(responseBuffer, 0, responseBuffer.length, rinfo.port, rinfo.address, (err) => {
          if (err) {
            console.error(`發送回應時出錯: ${err}`);
          } else {
            console.log(`已回應發現請求: ${rinfo.address}:${rinfo.port}`);
            console.log('回應內容:', response);
          }
        });
      }, delay);
    } else {
      console.log('收到無效的消息，忽略');
    }
  } catch (error) {
    console.error(`處理消息時出錯: ${error}`);
  }
});

// 啟動服務器
server.on('listening', () => {
  const address = server.address();
  console.log(`UDP 發現服務已啟動，監聽 ${address.address}:${address.port}`);
  console.log(`網關設備已啟動，MAC 地址: ${gatewayInfo.macAddress}`);
  console.log(`網關信息:`, gatewayInfo);
});

// 綁定端口
server.bind(UDP_PORT);

// 處理進程終止
process.on('SIGINT', () => {
  console.log('網關設備已停止');
  server.close();
  process.exit();
});

console.log('啟動網關發現服務...');
```

### 3.5 使用方法

1. 將上述代碼保存為 `gateway-discovery-service.js`
2. 確保已安裝 Node.js 環境
3. 在命令行中執行以下命令啟動服務：

```bash
node gateway-discovery-service.js
```

4. 服務啟動後，將在控制台顯示網關信息和監聽狀態
5. 當收到移動應用發送的 UDP 廣播消息時，將自動回應網關信息

### 3.6 測試方法

可以使用以下方法測試網關發現服務：

1. **使用 EPD Manager App**：
   - 啟動 EPD Manager App
   - 進入網關掃描頁面
   - 確認是否能發現運行此服務的設備

2. **使用命令行工具**：
   - 在另一台電腦上使用 netcat 發送 UDP 消息：
   ```bash
   echo '{"type":"discovery","protocol":"EPD-GATEWAY-DISCOVERY","version":"1.0","timestamp":1683270664000}' | nc -u [網關IP地址] 5000
   ```

3. **使用 Wireshark**：
   - 使用 Wireshark 捕獲 UDP 數據包
   - 過濾端口 5000 的數據包
   - 分析請求和回應消息

## 4. 用戶界面設計

### 4.1 網關掃描頁面

網關掃描頁面應提供直觀的界面，顯示掃描進度和結果，並允許用戶選擇要添加的網關。

#### 4.1.1 頁面布局

```
+----------------------------------+
|           網關掃描               |
|  +----------------------------+  |
|  |                            |  |
|  |       [掃描中圖標]          |  |
|  |                            |  |
|  |  正在掃描本地網絡中的網關...  |  |
|  |                            |  |
|  +----------------------------+  |
|                                  |
|  已發現的網關:                    |
|  +----------------------------+  |
|  | Gateway-1234               |  |
|  | MAC: AA:BB:CC:DD:EE:FF     |  |
|  | IP: *************          |  |
|  | 型號: GW-2000              |  |
|  | [添加到服務器]              |  |
|  +----------------------------+  |
|  +----------------------------+  |
|  | Gateway-5678               |  |
|  | MAC: AA:BB:CC:DD:EE:GG     |  |
|  | IP: *************          |  |
|  | 型號: GW-2000              |  |
|  | [添加到服務器]              |  |
|  +----------------------------+  |
|                                  |
|  [重新掃描]        [完成]        |
+----------------------------------+
```

#### 4.1.2 交互流程

1. 用戶進入頁面後，自動開始掃描
2. 掃描過程中顯示加載動畫和提示文字
3. 發現網關後，在列表中顯示網關信息
4. 用戶可以點擊特定網關旁的按鈕將其添加到服務器
5. 用戶可以點擊"重新掃描"按鈕重新開始掃描
6. 用戶可以點擊"完成"按鈕返回上一頁

### 4.2 網關添加頁面

當用戶選擇添加特定網關時，顯示網關添加頁面，允許用戶輸入額外信息。

#### 4.2.1 頁面布局

```
+----------------------------------+
|         添加網關到服務器          |
|  +----------------------------+  |
|  | 網關信息:                   |  |
|  | MAC: AA:BB:CC:DD:EE:FF     |  |
|  | IP: *************          |  |
|  | 型號: GW-2000              |  |
|  +----------------------------+  |
|                                  |
|  網關名稱:                       |
|  +----------------------------+  |
|  | Gateway-1234               |  |
|  +----------------------------+  |
|                                  |
|  選擇門店:                       |
|  +----------------------------+  |
|  | [下拉選擇門店]              |  |
|  +----------------------------+  |
|                                  |
|  備註:                          |
|  +----------------------------+  |
|  | [輸入備註]                  |  |
|  +----------------------------+  |
|                                  |
|  [取消]            [確認添加]    |
+----------------------------------+
```

#### 4.2.2 交互流程

1. 用戶從掃描頁面選擇添加特定網關後，進入此頁面
2. 頁面預先填充網關的基本信息（MAC 地址、IP 地址、型號）
3. 用戶可以修改網關名稱
4. 用戶需要選擇將網關添加到哪個門店
5. 用戶可以添加可選的備註信息
6. 用戶點擊"確認添加"按鈕將網關添加到服務器
7. 添加成功後顯示成功消息，並返回網關列表頁面

## 5. 實現流程

### 5.1 掃描與添加流程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant LocalGW as 本地網關
    participant Server as 後端服務器

    User->>App: 進入網關掃描頁面
    App->>App: 檢查網絡權限
    App->>LocalGW: 發送 UDP 廣播掃描消息
    LocalGW-->>App: 回應掃描消息
    App->>App: 顯示發現的網關

    User->>App: 選擇添加特定網關
    App->>App: 顯示網關添加頁面
    User->>App: 填寫網關信息並確認

    App->>Server: 發送添加網關請求
    Server->>Server: 創建網關記錄
    Server->>Server: 生成 WebSocket 連接信息
    Server-->>App: 返回添加結果

    App-->>User: 顯示添加成功消息
    App->>App: 返回網關列表頁面
```

### 5.2 網關註冊 API

添加網關到服務器時，應使用以下 API：

```javascript
// api/gateway.js
import axios from 'axios';
import { API_BASE_URL } from '../constants/api';

export const registerGateway = async (gatewayData, token) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/gateways`, gatewayData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

## 6. 安全考慮

1. **網關認證**：考慮在掃描協議中添加認證機制，防止惡意設備冒充網關
2. **數據加密**：敏感信息（如網關憑證）應加密存儲和傳輸
3. **權限控制**：確保只有授權用戶才能添加網關到服務器
4. **網關驗證**：服務器應驗證網關的合法性，如檢查 MAC 地址是否已註冊

## 7. 性能優化

1. **掃描超時**：設置合理的掃描超時時間（如 10 秒），避免長時間等待
2. **結果緩存**：緩存掃描結果，允許用戶在不重新掃描的情況下查看之前的結果
3. **批量操作**：支持批量添加網關，提高效率
4. **後台掃描**：允許掃描在後台進行，用戶可以同時執行其他操作
