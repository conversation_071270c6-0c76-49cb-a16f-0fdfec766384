import React, { useState } from 'react';
import { X, Upload, AlertCircle, CheckCircle } from 'lucide-react';
import { createBugReport } from '../utils/api/bugReportApi';
import { CreateBugReportRequest } from '../types/bugReport';

interface BugReportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const BugReportModal: React.FC<BugReportModalProps> = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    priority: 'medium' as const,
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  if (!isOpen) return null;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // 檢查文件大小（10MB限制）
      if (file.size > 10 * 1024 * 1024) {
        setErrorMessage('圖片文件大小不能超過10MB');
        return;
      }
      
      // 檢查文件類型
      if (!file.type.startsWith('image/')) {
        setErrorMessage('只能上傳圖片文件');
        return;
      }
      
      setSelectedFile(file);
      setErrorMessage('');
    }
  };

  const getCurrentPageInfo = () => {
    // 獲取當前頁面信息
    const path = window.location.pathname;
    const search = window.location.search;
    const hash = window.location.hash;
    return `${path}${search}${hash}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.content.trim()) {
      setErrorMessage('標題和內容為必填項');
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const requestData: CreateBugReportRequest = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        priority: formData.priority,
        currentPage: getCurrentPageInfo(),
        image: selectedFile || undefined,
      };

      await createBugReport(requestData);
      
      setSubmitStatus('success');
      
      // 2秒後自動關閉
      setTimeout(() => {
        onClose();
        // 重置表單
        setFormData({
          title: '',
          content: '',
          priority: 'medium',
        });
        setSelectedFile(null);
        setSubmitStatus('idle');
      }, 2000);
      
    } catch (error) {
      console.error('提交bug回報失敗:', error);
      setSubmitStatus('error');
      setErrorMessage(error instanceof Error ? error.message : '提交失敗，請稍後重試');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* 標題欄 */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold text-gray-900">回報Bug</h2>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors disabled:opacity-50"
          >
            <X size={20} />
          </button>
        </div>

        {/* 表單內容 */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* 標題 */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
              標題 *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              disabled={isSubmitting}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              placeholder="簡短描述問題"
              maxLength={100}
            />
          </div>

          {/* 優先級 */}
          <div>
            <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
              優先級
            </label>
            <select
              id="priority"
              name="priority"
              value={formData.priority}
              onChange={handleInputChange}
              disabled={isSubmitting}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
            >
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
              <option value="critical">緊急</option>
            </select>
          </div>

          {/* 內容 */}
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
              詳細描述 *
            </label>
            <textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              disabled={isSubmitting}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              placeholder="請詳細描述遇到的問題，包括重現步驟..."
              maxLength={1000}
            />
            <div className="text-xs text-gray-500 mt-1">
              {formData.content.length}/1000
            </div>
          </div>

          {/* 圖片上傳 */}
          <div>
            <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-1">
              截圖（可選）
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="file"
                id="image"
                accept="image/*"
                onChange={handleFileChange}
                disabled={isSubmitting}
                className="hidden"
              />
              <label
                htmlFor="image"
                className="flex items-center px-3 py-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50 disabled:opacity-50"
              >
                <Upload size={16} className="mr-2" />
                選擇圖片
              </label>
              {selectedFile && (
                <span className="text-sm text-gray-600 truncate">
                  {selectedFile.name}
                </span>
              )}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              支持 JPG、PNG、GIF 格式，最大 10MB
            </div>
          </div>

          {/* 錯誤信息 */}
          {errorMessage && (
            <div className="flex items-center space-x-2 text-red-600 text-sm">
              <AlertCircle size={16} />
              <span>{errorMessage}</span>
            </div>
          )}

          {/* 成功信息 */}
          {submitStatus === 'success' && (
            <div className="flex items-center space-x-2 text-green-600 text-sm">
              <CheckCircle size={16} />
              <span>Bug回報提交成功！</span>
            </div>
          )}

          {/* 按鈕 */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !formData.title.trim() || !formData.content.trim()}
              className="flex-1 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? '提交中...' : '提交'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
