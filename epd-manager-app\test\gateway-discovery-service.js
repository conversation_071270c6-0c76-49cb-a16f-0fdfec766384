// gateway-discovery-service.js
// 網關設備模擬器 - 用於測試網關掃描功能
// 此腳本模擬網關設備，監聽 UDP 端口並回應掃描請求

const dgram = require('dgram');
const os = require('os');

// 配置
const UDP_PORT = 5000;
const PROTOCOL = "EPD-GATEWAY-DISCOVERY";
const VERSION = "1.0";

// 生成隨機 MAC 地址（實際應用中應使用設備的真實 MAC 地址）
function generateMacAddress() {
  return Array.from({ length: 6 }, () => 
    Math.floor(Math.random() * 256).toString(16).padStart(2, '0')
  ).join(':').toUpperCase();
}

// 獲取本機 IP 地址
function getLocalIpAddress() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return '*************'; // 默認 IP 地址
}

// 網關信息
const gatewayInfo = {
  macAddress: generateMacAddress(),
  model: "GW-2000",
  name: `Gateway-${Math.floor(1000 + Math.random() * 9000)}`,
  firmwareVersion: "1.0.0",
  ipAddress: getLocalIpAddress(),
  capabilities: ["wifi", "bluetooth"],
  status: "ready"
};

// 創建 UDP 服務器
const server = dgram.createSocket('udp4');

// 處理錯誤
server.on('error', (err) => {
  console.error(`UDP 服務器錯誤: ${err.stack}`);
  server.close();
});

// 處理消息
server.on('message', (msg, rinfo) => {
  try {
    // 解析 JSON 數據
    const request = JSON.parse(msg.toString());
    
    console.log(`收到來自 ${rinfo.address}:${rinfo.port} 的消息:`, request);
    
    // 驗證消息格式和協議
    if (request.type === 'discovery' && 
        request.protocol === PROTOCOL && 
        request.version === VERSION) {
      
      console.log('收到有效的發現請求，準備回應');
      
      // 創建回應消息
      const response = {
        type: "discovery-response",
        protocol: PROTOCOL,
        version: VERSION,
        timestamp: Date.now(),
        ...gatewayInfo
      };
      
      // 添加隨機延遲，避免網絡擁塞
      const delay = Math.random() * 400 + 100; // 100-500ms
      setTimeout(() => {
        // 發送回應
        const responseBuffer = Buffer.from(JSON.stringify(response));
        server.send(responseBuffer, 0, responseBuffer.length, rinfo.port, rinfo.address, (err) => {
          if (err) {
            console.error(`發送回應時出錯: ${err}`);
          } else {
            console.log(`已回應發現請求: ${rinfo.address}:${rinfo.port}`);
            console.log('回應內容:', response);
          }
        });
      }, delay);
    } else {
      console.log('收到無效的消息，忽略');
    }
  } catch (error) {
    console.error(`處理消息時出錯: ${error}`);
  }
});

// 啟動服務器
server.on('listening', () => {
  const address = server.address();
  console.log(`UDP 發現服務已啟動，監聽 ${address.address}:${address.port}`);
  console.log(`網關設備已啟動，MAC 地址: ${gatewayInfo.macAddress}`);
  console.log(`網關信息:`, gatewayInfo);
});

// 綁定端口
server.bind(UDP_PORT);

// 處理進程終止
process.on('SIGINT', () => {
  console.log('網關設備已停止');
  server.close();
  process.exit();
});

console.log('啟動網關發現服務...');
