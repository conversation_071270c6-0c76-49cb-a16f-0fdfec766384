/**
 * Run-Length 壓縮器單元測試
 */

const RunLengthCompressor = require('../compressors/runLengthCompressor');
const { RAWDATA_FORMATS } = require('../types');

describe('RunLengthCompressor', () => {
  let compressor;
  
  beforeEach(() => {
    compressor = new RunLengthCompressor();
    compressor.initialize();
  });
  
  describe('基本功能測試', () => {
    test('應該正確初始化', () => {
      expect(compressor.getFormatName()).toBe(RAWDATA_FORMATS.RUNLENDATA);
      expect(compressor.isReady()).toBe(true);
    });
    
    test('應該有正確的常數設定', () => {
      expect(compressor.MIN_RUN_LENGTH).toBe(2);
      expect(compressor.MAX_RUN_LENGTH).toBe(0x7F);
      expect(compressor.MAX_LITERAL_LENGTH).toBe(0x7F);
      expect(compressor.LITERAL_FLAG).toBe(0x80);
    });
  });
  
  describe('壓縮功能測試', () => {
    test('應該正確壓縮簡單重複序列', () => {
      // 輸入: [0xFF, 0xFF, 0xFF, 0xFF]
      // 預期輸出: [0x04, 0xFF]
      const input = new Uint8Array([0xFF, 0xFF, 0xFF, 0xFF]);
      const result = compressor.compress(input);
      
      expect(result.success).toBe(true);
      expect(Array.from(result.data)).toEqual([0x04, 0xFF]);
    });
    
    test('應該正確壓縮非重複序列', () => {
      // 輸入: [0x01, 0x02, 0x03, 0x04]
      // 預期輸出: [0x84, 0x01, 0x02, 0x03, 0x04]
      const input = new Uint8Array([0x01, 0x02, 0x03, 0x04]);
      const result = compressor.compress(input);
      
      expect(result.success).toBe(true);
      expect(Array.from(result.data)).toEqual([0x84, 0x01, 0x02, 0x03, 0x04]);
    });
    
    test('應該正確處理混合模式', () => {
      // 輸入: [0x00, 0x00, 0x01, 0x02, 0xFF, 0xFF, 0xFF]
      // 預期輸出: [0x02, 0x00, 0x82, 0x01, 0x02, 0x03, 0xFF]
      const input = new Uint8Array([0x00, 0x00, 0x01, 0x02, 0xFF, 0xFF, 0xFF]);
      const result = compressor.compress(input);
      
      expect(result.success).toBe(true);
      expect(Array.from(result.data)).toEqual([0x02, 0x00, 0x82, 0x01, 0x02, 0x03, 0xFF]);
    });
    
    test('應該正確處理單字節', () => {
      // 輸入: [0x42]
      // 預期輸出: [0x81, 0x42]
      const input = new Uint8Array([0x42]);
      const result = compressor.compress(input);
      
      expect(result.success).toBe(true);
      expect(Array.from(result.data)).toEqual([0x81, 0x42]);
    });
    
    test('應該正確處理最大重複長度', () => {
      // 輸入: 127 個 0x55
      // 預期輸出: [0x7F, 0x55]
      const input = new Uint8Array(127).fill(0x55);
      const result = compressor.compress(input);
      
      expect(result.success).toBe(true);
      expect(Array.from(result.data)).toEqual([0x7F, 0x55]);
    });
    
    test('應該正確處理超過最大重複長度', () => {
      // 輸入: 130 個 0xAA
      // 預期輸出: [0x7F, 0xAA, 0x03, 0xAA]
      const input = new Uint8Array(130).fill(0xAA);
      const result = compressor.compress(input);
      
      expect(result.success).toBe(true);
      expect(Array.from(result.data)).toEqual([0x7F, 0xAA, 0x03, 0xAA]);
    });
    
    test('應該拒絕無效輸入', () => {
      expect(() => compressor.compress(null)).toThrow();
      expect(() => compressor.compress(undefined)).toThrow();
      expect(() => compressor.compress(new Uint8Array(0))).toThrow();
    });
  });
  
  describe('解壓縮功能測試', () => {
    test('應該正確解壓縮重複序列', () => {
      const compressed = new Uint8Array([0x04, 0xFF]);
      const result = compressor.decompress(compressed);
      
      expect(Array.from(result)).toEqual([0xFF, 0xFF, 0xFF, 0xFF]);
    });
    
    test('應該正確解壓縮非重複序列', () => {
      const compressed = new Uint8Array([0x84, 0x01, 0x02, 0x03, 0x04]);
      const result = compressor.decompress(compressed);
      
      expect(Array.from(result)).toEqual([0x01, 0x02, 0x03, 0x04]);
    });
    
    test('應該正確解壓縮混合模式', () => {
      const compressed = new Uint8Array([0x02, 0x00, 0x82, 0x01, 0x02, 0x03, 0xFF]);
      const result = compressor.decompress(compressed);
      
      expect(Array.from(result)).toEqual([0x00, 0x00, 0x01, 0x02, 0xFF, 0xFF, 0xFF]);
    });
    
    test('應該檢測不完整的壓縮數據', () => {
      expect(() => compressor.decompress(new Uint8Array([0x04]))).toThrow('missing value byte');
      expect(() => compressor.decompress(new Uint8Array([0x82, 0x01]))).toThrow('insufficient data bytes');
    });
  });
  
  describe('壓縮一致性測試', () => {
    const testCases = [
      new Uint8Array([0xFF, 0xFF, 0xFF, 0xFF]),
      new Uint8Array([0x01, 0x02, 0x03, 0x04]),
      new Uint8Array([0x00, 0x00, 0x01, 0x02, 0xFF, 0xFF, 0xFF]),
      new Uint8Array([0x42]),
      new Uint8Array(127).fill(0x55),
      new Uint8Array(130).fill(0xAA),
      new Uint8Array([0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99]),
      new Uint8Array(Array.from({length: 100}, (_, i) => i % 256))
    ];
    
    testCases.forEach((testData, index) => {
      test(`壓縮解壓縮一致性測試 ${index + 1}`, () => {
        const compressed = compressor.compress(testData);
        expect(compressed.success).toBe(true);
        
        const decompressed = compressor.decompress(compressed.data);
        expect(Array.from(decompressed)).toEqual(Array.from(testData));
      });
    });
  });
  
  describe('壓縮比估算測試', () => {
    test('應該正確估算重複數據的壓縮比', () => {
      const input = new Uint8Array(100).fill(0xFF);
      const ratio = compressor.estimateCompressionRatio(input);
      
      expect(ratio).toBeLessThan(0.1); // 應該有很好的壓縮比
    });
    
    test('應該正確估算隨機數據的壓縮比', () => {
      const input = new Uint8Array(Array.from({length: 100}, (_, i) => i % 256));
      const ratio = compressor.estimateCompressionRatio(input);
      
      expect(ratio).toBeGreaterThan(0.9); // 隨機數據壓縮比應該接近 1
    });
    
    test('應該處理空數據', () => {
      const ratio = compressor.estimateCompressionRatio(new Uint8Array(0));
      expect(ratio).toBe(1.0);
    });
  });
  
  describe('適用性檢查測試', () => {
    test('應該識別適合壓縮的數據', () => {
      const input = new Uint8Array(100).fill(0xFF);
      expect(compressor.isSuitableFor(input)).toBe(true);
    });
    
    test('應該識別不適合壓縮的數據', () => {
      const input = new Uint8Array(Array.from({length: 100}, (_, i) => i % 256));
      expect(compressor.isSuitableFor(input)).toBe(false);
    });
    
    test('應該拒絕太小的數據', () => {
      const input = new Uint8Array([0x01, 0x02]);
      expect(compressor.isSuitableFor(input)).toBe(false);
    });
  });
  
  describe('數據分析測試', () => {
    test('應該正確分析重複數據', () => {
      const input = new Uint8Array(100).fill(0xFF);
      const analysis = compressor.analyzeData(input);
      
      expect(analysis.suitable).toBe(true);
      expect(analysis.consecutiveRatio).toBeGreaterThan(90);
      expect(analysis.uniqueValues).toBe(1);
      expect(analysis.maxRunLength).toBe(100);
    });
    
    test('應該正確分析隨機數據', () => {
      const input = new Uint8Array(Array.from({length: 100}, (_, i) => i % 256));
      const analysis = compressor.analyzeData(input);
      
      expect(analysis.suitable).toBe(false);
      expect(analysis.consecutiveRatio).toBeLessThan(10);
      expect(analysis.uniqueValues).toBeGreaterThan(50);
    });
  });
});
