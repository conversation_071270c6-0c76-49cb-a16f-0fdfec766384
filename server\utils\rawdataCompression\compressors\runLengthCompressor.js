/**
 * Run-Length Encoding 壓縮器
 * 實作與 Go 版本相同的 RLE 算法
 * 適用於有大量重複數據的場景，如單色區域較多的 EPD 圖像
 */

const BaseCompressor = require('./baseCompressor');
const { RAWDATA_FORMATS } = require('../types');

/**
 * Run-Length Encoding 壓縮器
 */
class RunLengthCompressor extends BaseCompressor {
  constructor() {
    super(RAWDATA_FORMATS.RUNLENDATA);
    
    // RLE 編碼常數（與 Go 版本一致）
    this.MIN_RUN_LENGTH = 2;      // 最小重複長度
    this.MAX_RUN_LENGTH = 0x7F;   // 最大重複長度 (127)
    this.MAX_LITERAL_LENGTH = 0x7F; // 最大非重複長度 (127)
    this.LITERAL_FLAG = 0x80;     // 非重複序列標記 (bit7 = 1)
  }
  
  /**
   * 壓縮像素數據
   * 編碼格式（與 Go 版本相同）：
   * - 重複序列：[runLength, value] (runLength >= 2, bit7 = 0)
   * - 非重複序列：[0x80|length, data...] (bit7 = 1)
   */
  compress(pixelData) {
    const startTime = this.getTimestamp();
    
    try {
      this.validateInput(pixelData, 'compression');
      
      const result = [];
      const n = pixelData.length;
      let inx = 0;
      
      while (inx < n) {
        // 檢查重複序列
        let runLength = 1;
        while (inx + runLength < n && 
               pixelData[inx + runLength] === pixelData[inx] && 
               runLength < this.MAX_RUN_LENGTH) {
          runLength++;
        }
        
        if (runLength >= this.MIN_RUN_LENGTH) {
          // 編碼重複序列：[runLength, value] (bit7 = 0)
          result.push(runLength);
          result.push(pixelData[inx]);
          inx += runLength;
        } else {
          // 非重複序列
          const start = inx;
          while (inx < n && 
                 (inx + 1 >= n || pixelData[inx] !== pixelData[inx + 1]) && 
                 (inx - start) < this.MAX_LITERAL_LENGTH) {
            inx++;
          }
          const length = inx - start;
          result.push(this.LITERAL_FLAG | length); // bit7 = 1
          for (let i = start; i < inx; i++) {
            result.push(pixelData[i]);
          }
        }
      }
      
      const compressedData = new Uint8Array(result);
      const processingTime = this.getTimestamp() - startTime;
      
      const compressionResult = this.createResult(true, pixelData, compressedData, processingTime);
      this.logCompressionStats(compressionResult);
      
      return compressionResult;
      
    } catch (error) {
      const processingTime = this.getTimestamp() - startTime;
      return this.createResult(false, pixelData, null, processingTime, error.message);
    }
  }
  
  /**
   * 解壓縮數據（與 Go 版本對應）
   */
  decompress(compressedData) {
    this.validateInput(compressedData, 'decompression');
    
    const decompressed = [];
    let i = 0;
    
    while (i < compressedData.length) {
      const header = compressedData[i];
      i++;
      
      if ((header & this.LITERAL_FLAG) === 0) {
        // 重複序列：bit7 = 0
        const runLength = header;
        if (i >= compressedData.length) {
          throw new Error('Incomplete RLE data: missing value byte');
        }
        const value = compressedData[i];
        i++;
        
        for (let j = 0; j < runLength; j++) {
          decompressed.push(value);
        }
      } else {
        // 非重複序列：bit7 = 1
        const length = header & this.MAX_LITERAL_LENGTH;
        if (i + length > compressedData.length) {
          throw new Error('Incomplete RLE data: insufficient data bytes');
        }
        
        for (let j = 0; j < length; j++) {
          decompressed.push(compressedData[i + j]);
        }
        i += length;
      }
    }
    
    return new Uint8Array(decompressed);
  }
  
  /**
   * 快速估算壓縮比（與 Go 版本邏輯一致）
   */
  estimateCompressionRatio(pixelData) {
    if (!pixelData || pixelData.length === 0) {
      return 1.0;
    }
    
    let estimatedSize = 0;
    const n = pixelData.length;
    let inx = 0;
    
    while (inx < n) {
      // 檢查重複序列
      let runLength = 1;
      while (inx + runLength < n && 
             pixelData[inx + runLength] === pixelData[inx] && 
             runLength < this.MAX_RUN_LENGTH) {
        runLength++;
      }
      
      if (runLength >= this.MIN_RUN_LENGTH) {
        // 重複序列：2 bytes (runLength + value)
        estimatedSize += 2;
        inx += runLength;
      } else {
        // 非重複序列
        const start = inx;
        while (inx < n && 
               (inx + 1 >= n || pixelData[inx] !== pixelData[inx + 1]) && 
               (inx - start) < this.MAX_LITERAL_LENGTH) {
          inx++;
        }
        const length = inx - start;
        // 非重複序列：1 + length bytes (header + data)
        estimatedSize += 1 + length;
      }
    }
    
    return estimatedSize / pixelData.length;
  }
  
  /**
   * 檢查數據是否適合 RLE 壓縮
   * RLE 適合有大量重複數據的場景
   */
  isSuitableFor(pixelData) {
    if (!pixelData || pixelData.length < 4) {
      return false;
    }
    
    // 快速檢查：計算連續相同字節對的數量
    let consecutivePairs = 0;
    for (let i = 0; i < pixelData.length - 1; i++) {
      if (pixelData[i] === pixelData[i + 1]) {
        consecutivePairs++;
      }
    }
    
    const consecutiveRatio = consecutivePairs / (pixelData.length - 1);
    
    // 如果連續相同字節對比例超過 25% 且估算壓縮比小於 0.85，則適合
    return consecutiveRatio > 0.25 && this.estimateCompressionRatio(pixelData) < 0.85;
  }
  
  /**
   * 獲取壓縮器詳細信息
   * @returns {Object} 壓縮器信息
   */
  getInfo() {
    return {
      name: this.formatName,
      algorithm: 'Run-Length Encoding',
      minRunLength: this.MIN_RUN_LENGTH,
      maxRunLength: this.MAX_RUN_LENGTH,
      maxLiteralLength: this.MAX_LITERAL_LENGTH,
      literalFlag: `0x${this.LITERAL_FLAG.toString(16).toUpperCase()}`,
      description: 'Suitable for data with many repeated values, such as EPD images with large solid color areas',
      compatibility: 'Compatible with Go implementation'
    };
  }
  
  /**
   * 分析數據特性
   * @param {Uint8Array} pixelData - 像素數據
   * @returns {Object} 分析結果
   */
  analyzeData(pixelData) {
    if (!pixelData || pixelData.length === 0) {
      return {
        suitable: false,
        reason: 'Empty or invalid data'
      };
    }
    
    // 統計重複模式
    let consecutivePairs = 0;
    let uniqueValues = new Set();
    let maxRunLength = 1;
    let currentRunLength = 1;
    
    for (let i = 0; i < pixelData.length; i++) {
      uniqueValues.add(pixelData[i]);
      
      if (i > 0) {
        if (pixelData[i] === pixelData[i - 1]) {
          consecutivePairs++;
          currentRunLength++;
          maxRunLength = Math.max(maxRunLength, currentRunLength);
        } else {
          currentRunLength = 1;
        }
      }
    }
    
    const consecutiveRatio = consecutivePairs / (pixelData.length - 1);
    const estimatedRatio = this.estimateCompressionRatio(pixelData);
    const suitable = this.isSuitableFor(pixelData);
    
    return {
      suitable,
      consecutiveRatio: Math.round(consecutiveRatio * 100),
      estimatedCompressionRatio: Math.round(estimatedRatio * 100),
      uniqueValues: uniqueValues.size,
      maxRunLength,
      dataLength: pixelData.length,
      reason: suitable ? 
        `Good compression potential (${Math.round((1 - estimatedRatio) * 100)}% reduction)` :
        'Low compression potential or data too small'
    };
  }
}

module.exports = RunLengthCompressor;
