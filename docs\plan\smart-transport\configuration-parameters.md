# 智能網關選擇與任務隊列配置參數說明

## 概述

本文檔詳細說明智能網關選擇與任務隊列系統中所有可配置的參數，包括時間控制、循環次數、併發數量等關鍵配置。

## 核心配置參數

### 1. 隊列控制參數

```javascript
const QUEUE_CONFIG = {
  // 循環控制
  maxQueueCycles: 50,           // 最大隊列循環次數
  maxWaitCycles: 10,            // 最大等待循環次數 (v2.0)
  
  // 時間控制
  waitTimeout: 10000,           // 單次等待超時時間 (10秒) (v2.0)
  concurrencyCheckDelay: 500,   // 並發檢查延遲 (0.5秒)
  taskProcessDelay: 100,        // 任務處理間隔 (0.1秒)
  
  // 重試控制
  maxRetries: 3,                // 最大重試次數
  retryDelay: 1000,             // 重試延遲 (1秒)
  
  // 併發控制
  defaultConcurrency: 3,        // 默認併發數量
  minConcurrency: 2,            // 最小併發數量
  maxConcurrency: 8             // 最大併發數量
};
```

### 2. 網關狀態檢查參數

```javascript
const GATEWAY_CONFIG = {
  // 狀態檢查
  chunkTransmissionTimeout: 300000,  // chunk傳輸超時 (5分鐘)
  gatewayOnlineCheckInterval: 1000,  // 網關在線檢查間隔 (1秒)
  
  // 等待機制 (v2.0)
  waitForGatewayTimeout: 30000,      // 等待網關可用超時 (30秒)
  eventListenerTimeout: 10000,       // 事件監聽超時 (10秒)
  
  // 預防性標記
  preTaskMarkingEnabled: true,       // 啟用預防性標記
  preTaskMarkingTimeout: 60000       // 預防性標記超時 (1分鐘)
};
```

### 3. 性能優化參數

```javascript
const PERFORMANCE_CONFIG = {
  // 批量處理
  batchSize: 50,                     // 批量處理大小
  processingChunkSize: 10,           // 處理塊大小
  
  // 記憶體管理
  maxActiveTransmissions: 100,       // 最大活躍傳輸數
  transmissionCleanupInterval: 60000, // 傳輸清理間隔 (1分鐘)
  
  // 統計收集
  enableDetailedStats: true,         // 啟用詳細統計
  statsCollectionInterval: 5000      // 統計收集間隔 (5秒)
};
```

## 時間控制詳解

### 1. 總處理時間計算

```javascript
// 理論最大處理時間
const maxProcessingTime = 
  maxQueueCycles * (waitTimeout + taskProcessDelay + concurrencyCheckDelay);

// 實際計算：50 * (10000 + 100 + 500) = 530,000ms ≈ 8.8分鐘
```

### 2. 等待時間控制

```javascript
// 單次等待時間
const singleWaitTime = waitTimeout; // 10秒

// 最大總等待時間
const maxTotalWaitTime = maxWaitCycles * waitTimeout; // 10 * 10秒 = 100秒

// 等待觸發條件
const shouldWait = !foundProcessableTask && 
                   taskQueue.length > 0 && 
                   waitCycles < maxWaitCycles;
```

### 3. 循環間隔控制

```javascript
// 主循環間隔
while (taskQueue.length > 0 && queueCycles < maxQueueCycles) {
  // 並發檢查間隔
  if (currentlyProcessing >= concurrency) {
    await delay(concurrencyCheckDelay); // 500ms
    continue;
  }
  
  // 任務處理間隔
  await delay(taskProcessDelay); // 100ms
}
```

## 併發數量控制

### 1. 動態併發調整

```javascript
const calculateOptimalConcurrency = () => {
  const connectedGateways = websocketService.getConnectedGateways();
  const gatewayCount = connectedGateways.size;
  
  // 基於網關數量的動態調整
  const dynamicConcurrency = Math.max(
    QUEUE_CONFIG.minConcurrency,
    Math.min(
      gatewayCount * 2,  // 每個網關最多2個併發任務
      QUEUE_CONFIG.maxConcurrency
    )
  );
  
  return dynamicConcurrency;
};
```

### 2. 併發數量建議

| 場景 | 設備數量 | 網關數量 | 建議併發數 | 說明 |
|------|----------|----------|------------|------|
| 小批量 | <10 | 1-2 | 2-3 | 避免過度競爭 |
| 中批量 | 10-50 | 2-4 | 3-5 | 平衡效率與穩定性 |
| 大批量 | >50 | 4+ | 5-8 | 充分利用資源 |

### 3. 併發控制邏輯

```javascript
// 併發檢查
if (currentlyProcessing >= concurrency) {
  await new Promise(resolve => 
    setTimeout(resolve, concurrencyCheckDelay)
  );
  continue;
}

// 任務分配
currentlyProcessing++;
processTask(task).finally(() => {
  currentlyProcessing--;
});
```

## 等待機制參數 (v2.0)

### 1. 等待觸發條件

```javascript
const waitConditions = {
  noProcessableTask: !foundProcessableTask,
  hasRemainingTasks: taskQueue.length > 0,
  withinWaitLimit: waitCycles < maxWaitCycles,
  hasAvailableGateways: allGatewayIds.size > 0
};

const shouldTriggerWait = Object.values(waitConditions).every(Boolean);
```

### 2. 等待超時處理

```javascript
const waitWithTimeout = async (gatewayIds, timeout) => {
  try {
    await websocketService.waitForAnyGatewayAvailable(gatewayIds, timeout);
    return { success: true, reason: 'gateway_available' };
  } catch (error) {
    return { success: false, reason: 'timeout', error: error.message };
  }
};
```

### 3. 事件監聽配置

```javascript
const EVENT_CONFIG = {
  maxEventListeners: 50,           // 最大事件監聽器數量
  eventCleanupInterval: 30000,     // 事件清理間隔 (30秒)
  eventTimeoutWarning: 5000,       // 事件超時警告 (5秒)
  autoRemoveListeners: true        // 自動移除監聽器
};
```

## 錯誤處理與重試參數

### 1. 可重試錯誤類型

```javascript
const RETRYABLE_ERRORS = [
  'gateway_busy',
  'gateway_offline', 
  'connection_timeout',
  'transmission_failed',
  'chunk_transmission_active',
  'network_error'
];
```

### 2. 重試策略配置

```javascript
const RETRY_CONFIG = {
  maxRetries: 3,                   // 最大重試次數
  retryDelay: 1000,                // 基礎重試延遲 (1秒)
  exponentialBackoff: false,       // 是否使用指數退避
  retryMultiplier: 1.5,            // 重試延遲倍數
  maxRetryDelay: 10000             // 最大重試延遲 (10秒)
};
```

### 3. 超時保護配置

```javascript
const TIMEOUT_CONFIG = {
  // 主要超時
  queueProcessingTimeout: 1800000,  // 隊列處理總超時 (30分鐘)
  taskExecutionTimeout: 120000,     // 單個任務執行超時 (2分鐘)
  
  // 等待超時
  waitForGatewayTimeout: 10000,     // 等待網關超時 (10秒)
  eventListenerTimeout: 15000,      // 事件監聽超時 (15秒)
  
  // 網絡超時
  networkRequestTimeout: 30000,     // 網絡請求超時 (30秒)
  chunkTransmissionTimeout: 300000  // chunk傳輸超時 (5分鐘)
};
```

## 統計收集參數

### 1. 統計類型配置

```javascript
const STATS_CONFIG = {
  enableSmartSelectionStats: true,   // 智能選擇統計
  enablePerformanceStats: true,      // 性能統計
  enableWaitMechanismStats: true,    // 等待機制統計 (v2.0)
  enableTimingStats: true,           // 時間統計 (v2.0)
  enableDetailedLogs: true           // 詳細日誌
};
```

### 2. 統計收集間隔

```javascript
const STATS_INTERVALS = {
  realTimeStats: 1000,               // 實時統計更新 (1秒)
  performanceSnapshot: 5000,         // 性能快照 (5秒)
  detailedReport: 30000,             // 詳細報告 (30秒)
  summaryReport: 300000              // 總結報告 (5分鐘)
};
```

## 環境配置

### 1. 開發環境

```javascript
const DEV_CONFIG = {
  enableDebugLogs: true,
  detailedErrorReporting: true,
  performanceMonitoring: true,
  maxQueueCycles: 20,        // 較少的循環次數便於測試
  waitTimeout: 5000,         // 較短的等待時間
  concurrency: 2             // 較低的併發數
};
```

### 2. 生產環境

```javascript
const PROD_CONFIG = {
  enableDebugLogs: false,
  detailedErrorReporting: false,
  performanceMonitoring: true,
  maxQueueCycles: 50,        // 標準循環次數
  waitTimeout: 10000,        // 標準等待時間
  concurrency: 5             // 較高的併發數
};
```

### 3. 測試環境

```javascript
const TEST_CONFIG = {
  enableDebugLogs: true,
  detailedErrorReporting: true,
  performanceMonitoring: true,
  maxQueueCycles: 10,        // 快速測試
  waitTimeout: 2000,         // 快速等待
  concurrency: 1,            // 單線程測試
  mockGatewayDelay: 1000     // 模擬網關延遲
};
```

## 調優建議

### 1. 性能調優

- **併發數量**：根據網關數量和系統性能調整
- **等待時間**：根據網關響應時間調整
- **循環次數**：根據任務複雜度調整

### 2. 穩定性調優

- **超時時間**：設置合理的超時時間防止卡住
- **重試次數**：平衡成功率和性能
- **錯誤處理**：完善的錯誤分類和處理

### 3. 監控調優

- **統計收集**：啟用必要的統計信息
- **日誌級別**：根據環境調整日誌詳細程度
- **性能監控**：定期檢查關鍵性能指標
