const { MongoClient } = require('mongodb');

async function checkUsers() {
  try {
    console.log('🔍 檢查用戶數據...');
    
    const client = new MongoClient('mongodb://localhost:27017');
    await client.connect();
    console.log('✅ 連接到 MongoDB');
    
    const db = client.db('resourceManagement');
    const users = await db.collection('users').find({}).toArray();
    
    console.log(`📊 找到 ${users.length} 個用戶:`);
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. 用戶名: ${user.username}`);
      console.log(`     角色: ${user.role}`);
      console.log(`     密碼哈希: ${user.password ? user.password.substring(0, 20) + '...' : '無'}`);
      console.log(`     創建時間: ${user.createdAt}`);
      console.log('');
    });
    
    await client.close();
    console.log('✅ 檢查完成');
  } catch (error) {
    console.error('❌ 檢查失敗:', error.message);
  }
}

checkUsers();
