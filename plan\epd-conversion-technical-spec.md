# EPD 圖片轉換技術規格書

## 1. 模組架構設計

### 1.1 目錄結構
```
src/utils/epdConversion/
├── index.ts                 # 主要導出接口
├── types.ts                 # 類型定義
├── EpdConverter.ts          # 主轉換器類
├── converters/
│   ├── BaseConverter.ts     # 基礎轉換器抽象類
│   ├── BWConverter.ts       # BW/GRAY16 轉換器
│   ├── BWRConverter.ts      # BWR 轉換器
│   └── BWRYConverter.ts     # BWRY 轉換器
├── utils/
│   ├── rotationUtils.ts     # 旋轉處理工具
│   ├── paddingUtils.ts      # 寬度對齊工具
│   ├── colorPalettes.ts     # 預設調色板定義
│   └── imageDataUtils.ts    # ImageData 處理工具
└── __tests__/
    ├── converters/
    │   ├── BWConverter.test.ts
    │   ├── BWRConverter.test.ts
    │   └── BWRYConverter.test.ts
    └── utils/
        ├── rotationUtils.test.ts
        └── paddingUtils.test.ts
```

### 1.2 核心類型定義

```typescript
// types.ts
import { DisplayColorType } from '../../types';

// ImageInfo 結構 - 對應 Go 的 ImageInfo struct
export interface ImageInfo {
  imagecode: number; // uint32 - 圖片編碼 (與 WebSocket 發送的 imageCode 相同)
  x: number;         // uint16 - X 座標
  y: number;         // uint16 - Y 座標
  width: number;     // uint16 - 寬度
  height: number;    // uint16 - 高度
}

export interface EPDConversionOptions {
  colorType: DisplayColorType;     // 使用現有的 DisplayColorType
  width: number;
  height: number;
  imagecode: number;               // 圖片編碼 (與 WebSocket 發送的 imageCode 相同)
  x?: number;                      // 圖片在設備上的 X 座標，默認 0
  y?: number;                      // 圖片在設備上的 Y 座標，默認 0
  templateRotation?: number;       // 模板旋轉角度 (0, 90, 180, 270)，需要反向旋轉回 0 度
  paddingColor?: string;           // 填充顏色，默認白色
  qualityLevel?: number;           // 品質等級 1-10，默認 5
}

export interface EPDConversionResult {
  success: boolean;
  rawdata?: Uint8Array;            // 包含 ImageInfo + 圖片數據
  imageInfo?: ImageInfo;           // ImageInfo 結構
  pixelData?: Uint8Array;          // 純圖片像素數據
  error?: string;
  metadata: {
    originalSize: { width: number; height: number };
    finalSize: { width: number; height: number };
    bytesPerPixel: number;
    totalBytes: number;
    imageInfoBytes: number;        // ImageInfo 佔用的字節數 (固定12字節)
    pixelDataBytes: number;        // 像素數據佔用的字節數
    processingTime: number;
    colorType: DisplayColorType;
  };
}

export interface PixelData {
  r: number;
  g: number;
  b: number;
  a: number;
}
```

## 2. 轉換器實作規格

### 2.1 基礎轉換器抽象類

```typescript
// BaseConverter.ts
export abstract class BaseConverter {
  protected width: number;
  protected height: number;
  protected paddedWidth: number;
  protected buffer: Uint8Array;
  protected options: EPDConversionOptions;

  constructor(options: EPDConversionOptions) {
    this.options = options;
    this.width = options.width;
    this.height = options.height;
    this.paddedWidth = this.calculatePaddedWidth();
    this.buffer = new Uint8Array(this.calculateBufferSize());
  }

  abstract calculatePaddedWidth(): number;
  abstract calculateBufferSize(): number;
  abstract processPixel(x: number, y: number, pixel: PixelData): void;
  abstract getPixelData(): Uint8Array;

  // 創建 ImageInfo 結構
  protected createImageInfo(): ImageInfo {
    return {
      imagecode: this.options.imagecode,
      x: this.options.x || 0,
      y: this.options.y || 0,
      width: this.paddedWidth,
      height: this.height
    };
  }

  // 將 ImageInfo 序列化為字節數組 (Little Endian)
  protected serializeImageInfo(imageInfo: ImageInfo): Uint8Array {
    const buffer = new ArrayBuffer(12); // uint32 + 4 * uint16 = 12 bytes
    const view = new DataView(buffer);

    view.setUint32(0, imageInfo.imagecode, true);  // Little Endian
    view.setUint16(4, imageInfo.x, true);
    view.setUint16(6, imageInfo.y, true);
    view.setUint16(8, imageInfo.width, true);
    view.setUint16(10, imageInfo.height, true);

    return new Uint8Array(buffer);
  }

  // 組合 ImageInfo + 像素數據
  protected combineRawData(imageInfo: ImageInfo, pixelData: Uint8Array): Uint8Array {
    const imageInfoBytes = this.serializeImageInfo(imageInfo);
    const rawdata = new Uint8Array(imageInfoBytes.length + pixelData.length);

    rawdata.set(imageInfoBytes, 0);
    rawdata.set(pixelData, imageInfoBytes.length);

    return rawdata;
  }

  // 通用的轉換流程
  convert(imageData: ImageData): EPDConversionResult {
    const startTime = performance.now();

    try {
      // 應用反向旋轉，將模板旋轉回 0 度
      const normalizedImageData = this.applyReverseRotation(imageData);

      // 處理每個像素
      this.processImageData(normalizedImageData);

      // 獲取像素數據
      const pixelData = this.getPixelData();

      // 創建 ImageInfo
      const imageInfo = this.createImageInfo();

      // 組合最終的 rawdata
      const rawdata = this.combineRawData(imageInfo, pixelData);

      const endTime = performance.now();

      return {
        success: true,
        rawdata,
        imageInfo,
        pixelData,
        metadata: {
          originalSize: { width: this.width, height: this.height },
          finalSize: { width: this.paddedWidth, height: this.height },
          bytesPerPixel: this.getBytesPerPixel(),
          totalBytes: rawdata.length,
          imageInfoBytes: 12, // ImageInfo 固定 12 字節
          pixelDataBytes: pixelData.length,
          processingTime: endTime - startTime,
          colorType: this.options.colorType
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        metadata: {
          originalSize: { width: this.width, height: this.height },
          finalSize: { width: 0, height: 0 },
          bytesPerPixel: 0,
          totalBytes: 0,
          imageInfoBytes: 0,
          pixelDataBytes: 0,
          processingTime: 0,
          colorType: this.options.colorType
        }
      };
    }
  }

  protected abstract getBytesPerPixel(): number;

  // 應用反向旋轉，將模板旋轉回 0 度
  protected applyReverseRotation(imageData: ImageData): ImageData {
    const templateRotation = this.options.templateRotation || 0;

    if (templateRotation === 0) {
      return imageData; // 無需旋轉
    }

    // 計算反向旋轉角度
    const reverseRotation = (360 - templateRotation) % 360;

    console.log(`模板旋轉角度: ${templateRotation}°，應用反向旋轉: ${reverseRotation}°`);

    return rotateImageData(imageData, reverseRotation);
  }

  protected abstract processImageData(imageData: ImageData): void;
}
```

### 2.2 BW/GRAY16 轉換器

```typescript
// BWConverter.ts
export class BWConverter extends BaseConverter {
  private index: number = 0;

  calculatePaddedWidth(): number {
    // 寬度必須是2的倍數 (4bit per pixel)
    return Math.ceil(this.width / 2) * 2;
  }

  calculateBufferSize(): number {
    // 每2個像素佔用1個字節
    return Math.ceil((this.paddedWidth * this.height) / 2);
  }

  getBytesPerPixel(): number {
    return 0.5; // 4bit per pixel
  }

  processPixel(x: number, y: number, pixel: PixelData): void {
    const grayValue = this.toGray16(pixel);

    if ((this.index & 1) === 0) {
      // 偶數索引：存儲在高4位
      this.buffer[this.index >> 1] = (grayValue & 0xF0);
    } else {
      // 奇數索引：存儲在低4位
      this.buffer[this.index >> 1] |= (grayValue >> 4);
    }
    this.index++;
  }

  private toGray16(pixel: PixelData): number {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BW);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 將匹配的灰度值轉換為 4bit 格式 (0-15)
    const grayLevel = Math.round(matchedColor.r / 17); // 255/15 ≈ 17
    return Math.min(15, Math.max(0, grayLevel)) << 4;
  }

  getPixelData(): Uint8Array {
    return this.buffer;
  }

  // 實作圖像處理邏輯...

  protected processImageData(imageData: ImageData): void {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < this.paddedWidth; x++) {
        if (x < width) {
          // 處理實際像素
          const index = (y * width + x) * 4;
          const pixel: PixelData = {
            r: data[index],
            g: data[index + 1],
            b: data[index + 2],
            a: data[index + 3]
          };
          this.processPixel(x, y, pixel);
        } else {
          // 填充像素（白色）
          const paddingPixel: PixelData = { r: 255, g: 255, b: 255, a: 255 };
          this.processPixel(x, y, paddingPixel);
        }
      }
    }
  }
}
```

### 2.3 BWR 轉換器

```typescript
// BWRConverter.ts
export class BWRConverter extends BaseConverter {
  private buffer2: Uint8Array; // 紅色數據表
  private index: number = 0;

  constructor(options: EPDConversionOptions) {
    super(options);
    this.buffer2 = new Uint8Array(this.calculateBufferSize());
  }

  calculatePaddedWidth(): number {
    // 寬度必須是8的倍數 (1bit per pixel)
    return Math.ceil(this.width / 8) * 8;
  }

  calculateBufferSize(): number {
    // 每8個像素佔用1個字節，需要2個表格
    return Math.ceil((this.paddedWidth * this.height) / 8);
  }

  getBytesPerPixel(): number {
    return 0.25; // 1bit per pixel, 但有2個表格
  }

  processPixel(x: number, y: number, pixel: PixelData): void {
    const { isRed, isWhite } = this.analyzeColor(pixel);
    const bitMask = 0x80 >> (x & 7);

    if (isRed) {
      // 紅色：兩個buffer都設為0 (已經初始化為0)
    } else {
      this.buffer2[this.index] |= bitMask;
      if (isWhite) {
        this.buffer[this.index] |= bitMask;
      }
    }

    // 當處理完一個字節或到達行尾時，移動到下一個字節
    if ((bitMask === 1) || ((x + 1) === this.paddedWidth)) {
      this.index++;
    }
  }

  private analyzeColor(pixel: PixelData): { isRed: boolean; isWhite: boolean } {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BWR);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 根據匹配的顏色判斷類型
    const isRed = matchedColor.r === 255 && matchedColor.g === 0 && matchedColor.b === 0;
    const isWhite = matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 255;

    return { isRed, isWhite };
  }

  getPixelData(): Uint8Array {
    // 合併兩個表格
    const result = new Uint8Array(this.buffer.length * 2);
    result.set(this.buffer, 0);
    result.set(this.buffer2, this.buffer.length);
    return result;
  }
}
```

### 2.4 BWRY 轉換器

```typescript
// BWRYConverter.ts
export class BWRYConverter extends BaseConverter {
  private index: number = 0;

  calculatePaddedWidth(): number {
    // 寬度必須是4的倍數 (2bit per pixel)
    return Math.ceil(this.width / 4) * 4;
  }

  calculateBufferSize(): number {
    // 每4個像素佔用1個字節
    return Math.ceil((this.paddedWidth * this.height) / 4);
  }

  getBytesPerPixel(): number {
    return 0.25; // 2bit per pixel
  }

  processPixel(x: number, y: number, pixel: PixelData): void {
    const colorValue = this.analyzeColorBWRY(pixel);
    const pixelInByte = x & 3; // 每字節4個像素
    const shift = (3 - pixelInByte) * 2; // 2bit per pixel

    this.buffer[this.index] |= (colorValue << shift);

    // 當處理完一個字節或到達行尾時，移動到下一個字節
    if (pixelInByte === 3 || (x + 1) === this.paddedWidth) {
      this.index++;
    }
  }

  private analyzeColorBWRY(pixel: PixelData): number {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BWRY);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 根據匹配的顏色返回對應的數值：0=黑, 1=白, 2=黃, 3=紅 (修正紅黃對應關係)
    if (matchedColor.r === 0 && matchedColor.g === 0 && matchedColor.b === 0) {
      return 0; // 黑色
    } else if (matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 255) {
      return 1; // 白色
    } else if (matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 0) {
      return 2; // 黃色 (修正：原本是3，現在改為2)
    } else if (matchedColor.r === 255 && matchedColor.g === 0 && matchedColor.b === 0) {
      return 3; // 紅色 (修正：原本是2，現在改為3)
    }

    // 備用邏輯（不應該到達這裡）
    console.warn(`BWRY: 未預期的顏色匹配結果: RGB(${matchedColor.r}, ${matchedColor.g}, ${matchedColor.b})`);
    return 0; // 默認黑色
  }

  getPixelData(): Uint8Array {
    return this.buffer;
  }
}
```

## 3. 工具函數規格

### 3.1 旋轉處理工具

```typescript
// rotationUtils.ts
export function rotateImageData(
  imageData: ImageData,
  rotation: number
): ImageData {
  switch (rotation) {
    case 90:
      return rotateImageData90(imageData);
    case 180:
      return rotateImageData180(imageData);
    case 270:
      return rotateImageData270(imageData);
    default:
      return imageData;
  }
}

// 獲取模板旋轉角度的反向旋轉
export function getTemplateReverseRotation(templateRotation: number): number {
  if (templateRotation === 0) return 0;
  return (360 - templateRotation) % 360;
}

function rotateImageData90(imageData: ImageData): ImageData {
  const { width, height, data } = imageData;
  const newImageData = new ImageData(height, width);
  const newData = newImageData.data;

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const oldIndex = (y * width + x) * 4;
      const newX = height - 1 - y;
      const newY = x;
      const newIndex = (newY * height + newX) * 4;

      newData[newIndex] = data[oldIndex];         // R
      newData[newIndex + 1] = data[oldIndex + 1]; // G
      newData[newIndex + 2] = data[oldIndex + 2]; // B
      newData[newIndex + 3] = data[oldIndex + 3]; // A
    }
  }

  return newImageData;
}
```

### 3.2 預設調色板定義

```typescript
// colorPalettes.ts
import { DisplayColorType } from '../../types';

export interface ColorRGB {
  r: number;
  g: number;
  b: number;
}

// 獲取預設調色板（與預覽圖生成時使用的相同）
export function getColorPalette(colorType: DisplayColorType): ColorRGB[] {
  switch (colorType) {
    case DisplayColorType.BWR: // "Black & White & Red"
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 }      // 紅色
      ];

    case DisplayColorType.BWRY: // "Black & White & Red & Yellow"
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 },     // 紅色
        { r: 255, g: 255, b: 0 }    // 黃色
      ];

    case DisplayColorType.BW: // "Gray16"
    default:
      // 16級灰度調色板
      const grayPalette: ColorRGB[] = [];
      for (let i = 0; i < 16; i++) {
        const grayValue = Math.round((255 / 15) * i);
        grayPalette.push({ r: grayValue, g: grayValue, b: grayValue });
      }
      return grayPalette;
  }
}

// 精確顏色比對（與預覽圖生成時使用的相同邏輯）
export function findExactColorMatch(r: number, g: number, b: number, palette: ColorRGB[]): ColorRGB {
  // 直接比對 RGB 值，找到完全匹配的顏色
  for (const color of palette) {
    if (color.r === r && color.g === g && color.b === b) {
      return color;
    }
  }

  // 如果沒有完全匹配，說明預覽圖生成有問題，記錄警告
  console.warn(`未找到精確匹配的顏色: RGB(${r}, ${g}, ${b})，調色板:`, palette);

  // 返回最接近的顏色作為備用
  return findClosestColor(r, g, b, palette);
}

// 備用的最接近顏色查找（僅在精確匹配失敗時使用）
function findClosestColor(r: number, g: number, b: number, palette: ColorRGB[]): ColorRGB {
  let minDistance = Infinity;
  let closestColor = palette[0];

  for (const color of palette) {
    const distance = Math.sqrt(
      Math.pow(r - color.r, 2) +
      Math.pow(g - color.g, 2) +
      Math.pow(b - color.b, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      closestColor = color;
    }
  }

  return closestColor;
}
```

### 3.3 寬度對齊工具

```typescript
// paddingUtils.ts
import { DisplayColorType } from '../../types';

export function calculatePaddedWidth(
  width: number,
  colorType: DisplayColorType
): number {
  switch (colorType) {
    case DisplayColorType.BW:  // "Gray16"
      return Math.ceil(width / 2) * 2;  // 2的倍數
    case DisplayColorType.BWR: // "Black & White & Red"
      return Math.ceil(width / 8) * 8;  // 8的倍數
    case DisplayColorType.BWRY: // "Black & White & Red & Yellow"
      return Math.ceil(width / 4) * 4;  // 4的倍數
    default:
      throw new Error(`不支援的顏色類型: ${colorType}`);
  }
}

export function padImageData(
  imageData: ImageData,
  targetWidth: number,
  paddingColor: string = '#FFFFFF'
): ImageData {
  // 實作圖像寬度填充邏輯
}
```

## 4. 主轉換器類

```typescript
// EpdConverter.ts
export class EpdConverter {
  static convert(
    canvas: HTMLCanvasElement,
    options: EPDConversionOptions
  ): EPDConversionResult {
    try {
      // 獲取圖像數據
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('無法獲取 Canvas 上下文');
      }

      const imageData = ctx.getImageData(0, 0, options.width, options.height);

      // 選擇適當的轉換器
      const converter = this.createConverter(options);

      // 執行轉換
      return converter.convert(imageData);

    } catch (error) {
      return {
        success: false,
        error: error.message,
        metadata: {
          originalSize: { width: options.width, height: options.height },
          finalSize: { width: 0, height: 0 },
          bytesPerPixel: 0,
          totalBytes: 0,
          processingTime: 0,
          colorType: options.colorType
        }
      };
    }
  }

  private static createConverter(options: EPDConversionOptions): BaseConverter {
    switch (options.colorType) {
      case DisplayColorType.BW:  // "Gray16"
        return new BWConverter(options);
      case DisplayColorType.BWR: // "Black & White & Red"
        return new BWRConverter(options);
      case DisplayColorType.BWRY: // "Black & White & Red & Yellow"
        return new BWRYConverter(options);
      default:
        throw new Error(`不支援的顏色類型: ${options.colorType}`);
    }
  }
}
```

## 5. WebSocket 整合規格

### 5.1 sendPreviewToGateway.js 修改

```javascript
// 在 sendPreviewToGateway.js 中添加 EPD 轉換邏輯
const { EpdConverter } = require('../utils/epdConversion');
const { DisplayColorType } = require('../types');

// 映射設備 colorType 到 DisplayColorType
function mapDeviceColorType(deviceColorType) {
  if (!deviceColorType) return DisplayColorType.BW; // 默認值

  // 如果已經是正確的格式，直接返回
  if (Object.values(DisplayColorType).includes(deviceColorType)) {
    return deviceColorType;
  }

  // 處理簡短代碼
  switch (deviceColorType.toUpperCase()) {
    case 'BW':
    case 'GRAY16':
      return DisplayColorType.BW; // "Gray16"
    case 'BWR':
      return DisplayColorType.BWR; // "Black & White & Red"
    case 'BWRY':
      return DisplayColorType.BWRY; // "Black & White & Red & Yellow"
    default:
      console.warn(`未知的設備顏色類型: ${deviceColorType}，使用默認值`);
      return DisplayColorType.BW;
  }
}

// 獲取模板旋轉角度
function getTemplateRotation(template) {
  // 從模板的 orientation 或其他屬性獲取旋轉角度
  // 這裡需要根據實際的模板數據結構來實作
  if (template && template.orientation) {
    // 解析 orientation 字符串，例如 "90°", "180°", "270°"
    const match = template.orientation.match(/(\d+)°/);
    if (match) {
      return parseInt(match[1], 10);
    }
  }
  return 0; // 默認無旋轉
}

// 在發送消息前添加 rawdata 生成
async function generateRawData(imageDataStr, device, imageCode, template) {
  try {
    // 創建臨時 Canvas
    const canvas = createCanvasFromImageData(imageDataStr);

    // 獲取設備的 colorType 和尺寸
    const colorType = mapDeviceColorType(device.data?.colorType);
    const { width, height } = parseDeviceSize(device.data?.size);
    const templateRotation = getTemplateRotation(template);

    console.log(`設備 ${device.macAddress} - 模板旋轉角度: ${templateRotation}°`);

    // 轉換選項
    const options = {
      colorType,
      width,
      height,
      imagecode: parseInt(imageCode, 16), // 將 hex 字符串轉為數字
      x: 0,        // 圖片在設備上的 X 座標
      y: 0,        // 圖片在設備上的 Y 座標
      templateRotation // 模板旋轉角度，轉換時會反向旋轉
    };

    // 執行轉換
    const result = EpdConverter.convert(canvas, options);

    if (result.success) {
      return Array.from(result.rawdata); // 轉為數組便於 JSON 傳輸
    } else {
      console.error('EPD 轉換失敗:', result.error);
      return null;
    }
  } catch (error) {
    console.error('生成 rawdata 時發生錯誤:', error);
    return null;
  }
}

// 更新消息格式 (需要傳入 template 參數)
const message = {
  type: 'update_preview',
  deviceMac: device.macAddress,
  imageData: imageDataStr,
  rawdata: await generateRawData(imageDataStr, device, imageCode, template), // 新增：包含 ImageInfo + 像素數據
  imageCode: imageCode,
  timestamp: new Date().toISOString()
};
```

## 6. 測試規格

### 6.1 單元測試案例

```typescript
// BWConverter.test.ts
import { DisplayColorType } from '../../types';

describe('BWConverter', () => {
  test('應該正確轉換純黑色像素', () => {
    const options = {
      colorType: DisplayColorType.BW, // "Gray16"
      width: 2,
      height: 1
    };

    const converter = new BWConverter(options);
    const imageData = createTestImageData(2, 1, [0, 0, 0, 255, 0, 0, 0, 255]);

    const result = converter.convert(imageData);

    expect(result.success).toBe(true);
    // 檢查 rawdata 包含 ImageInfo (12 bytes) + 像素數據 (1 byte)
    expect(result.rawdata.length).toBe(13);
    // 檢查 ImageInfo 部分
    expect(result.imageInfo).toEqual({
      imagecode: 0x12345678, // 測試用的 imageCode
      x: 0,
      y: 0,
      width: 2,
      height: 1
    });
  });

  test('應該正確處理寬度對齊', () => {
    const options = {
      colorType: DisplayColorType.BW,
      width: 3, // 奇數寬度，應該對齊到 4
      height: 1
    };

    const converter = new BWConverter(options);
    expect(converter.calculatePaddedWidth()).toBe(4);
  });

  test('應該正確序列化 ImageInfo', () => {
    const options = {
      colorType: DisplayColorType.BW,
      width: 128,
      height: 296,
      imagecode: 0xABCDEF12,
      x: 10,
      y: 20
    };

    const converter = new BWConverter(options);
    const result = converter.convert(createTestImageData(128, 296));

    expect(result.success).toBe(true);
    expect(result.imageInfo).toEqual({
      imagecode: 0xABCDEF12,
      x: 10,
      y: 20,
      width: 128, // 已經是2的倍數，無需對齊
      height: 296
    });

    // 檢查序列化的字節數組
    const serialized = converter.serializeImageInfo(result.imageInfo);
    expect(serialized.length).toBe(12); // uint32 + 4 * uint16 = 12 bytes
  });
});
```

這個技術規格書提供了完整的實作指導，包括詳細的類別設計、介面定義、和實作邏輯。請確認這個規劃是否符合您的需求，我可以根據您的反饋進行調整。
