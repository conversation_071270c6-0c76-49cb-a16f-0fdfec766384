/**
 * EPD 旋轉處理測試
 *
 * 測試預覽圖轉換為EPD格式時的旋轉處理邏輯
 */

import { EpdConverter } from './EpdConverter';
import { DisplayColorType } from '../../types';

/**
 * 創建測試用的 Canvas
 */
function createTestCanvas(width: number, height: number, rotation: number = 0): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;

  const ctx = canvas.getContext('2d')!;

  // 清除背景為白色
  ctx.fillStyle = 'white';
  ctx.fillRect(0, 0, width, height);

  // 繪製一個簡單的測試圖案
  ctx.fillStyle = 'black';
  ctx.fillRect(10, 10, 20, 20); // 左上角黑色方塊

  ctx.fillStyle = 'red';
  ctx.fillRect(width - 30, 10, 20, 20); // 右上角紅色方塊

  // 繪製文字標識方向
  ctx.fillStyle = 'black';
  ctx.font = '12px Arial';
  ctx.fillText('TOP', width / 2 - 15, 25);

  return canvas;
}

/**
 * 測試不同尺寸匹配的EPD轉換
 */
export function testEPDRotationConversion() {
  console.log('=== EPD 尺寸匹配處理測試開始 ===');

  const testCases = [
    {
      canvasSize: { width: 128, height: 296 },
      deviceSize: { width: 128, height: 296 },
      description: 'Canvas與Device尺寸一致 (直向)'
    },
    {
      canvasSize: { width: 296, height: 128 },
      deviceSize: { width: 296, height: 128 },
      description: 'Canvas與Device尺寸一致 (橫向)'
    },
    {
      canvasSize: { width: 128, height: 296 },
      deviceSize: { width: 296, height: 128 },
      description: 'Canvas直向，Device橫向 (需要旋轉)'
    },
    {
      canvasSize: { width: 296, height: 128 },
      deviceSize: { width: 128, height: 296 },
      description: 'Canvas橫向，Device直向 (需要旋轉)'
    }
  ];

  testCases.forEach(testCase => {
    console.log(`\n--- 測試 ${testCase.description} ---`);

    // 創建測試 Canvas
    const canvas = createTestCanvas(testCase.canvasSize.width, testCase.canvasSize.height);

    // EPD 轉換選項 (使用設備尺寸)
    const options = {
      colorType: DisplayColorType.BWR,
      width: testCase.deviceSize.width,
      height: testCase.deviceSize.height,
      imagecode: 0x12345678,
      x: 0,
      y: 0,
      templateRotation: 0
    };

    try {
      // 執行轉換
      const result = EpdConverter.convert(canvas, options);

      if (result.success) {
        console.log('轉換成功:', {
          原始尺寸: `${result.metadata.originalSize.width}x${result.metadata.originalSize.height}`,
          最終尺寸: `${result.metadata.finalSize.width}x${result.metadata.finalSize.height}`,
          ImageInfo尺寸: `${result.imageInfo.width}x${result.imageInfo.height}`,
          總字節數: result.metadata.totalBytes,
          處理時間: `${result.metadata.processingTime.toFixed(2)}ms`
        });

        // 判斷是否需要旋轉
        const canvasIsLandscape = testCase.canvasSize.width > testCase.canvasSize.height;
        const deviceIsLandscape = testCase.deviceSize.width > testCase.deviceSize.height;
        const needsRotation = canvasIsLandscape !== deviceIsLandscape;

        // 計算期望的最終尺寸 (如果需要旋轉，使用旋轉後的尺寸)
        let expectedWidth, expectedHeight;
        if (needsRotation) {
          // 需要旋轉：Canvas尺寸會被旋轉90度
          expectedWidth = testCase.canvasSize.height;
          expectedHeight = testCase.canvasSize.width;
        } else {
          // 不需要旋轉：使用原始Canvas尺寸
          expectedWidth = testCase.canvasSize.width;
          expectedHeight = testCase.canvasSize.height;
        }

        const expectedPaddedWidth = Math.ceil(expectedWidth / 8) * 8; // BWR格式，8的倍數

        console.log(`期望處理: ${needsRotation ? '需要旋轉' : '無需旋轉'}, 最終尺寸: ${expectedPaddedWidth}x${expectedHeight}`);

        // 驗證 metadata 中的最終尺寸
        if (result.metadata.finalSize.width !== expectedPaddedWidth) {
          console.warn(`⚠️  metadata寬度對齊可能有問題: 期望 ${expectedPaddedWidth}, 實際 ${result.metadata.finalSize.width}`);
        } else {
          console.log('✅ metadata寬度對齊正確');
        }

        if (result.metadata.finalSize.height !== expectedHeight) {
          console.warn(`⚠️  metadata高度可能有問題: 期望 ${expectedHeight}, 實際 ${result.metadata.finalSize.height}`);
        } else {
          console.log('✅ metadata高度正確');
        }

        // 驗證 ImageInfo 中的尺寸
        if (result.imageInfo.width !== expectedPaddedWidth) {
          console.warn(`⚠️  ImageInfo寬度可能有問題: 期望 ${expectedPaddedWidth}, 實際 ${result.imageInfo.width}`);
        } else {
          console.log('✅ ImageInfo寬度正確');
        }

        if (result.imageInfo.height !== expectedHeight) {
          console.warn(`⚠️  ImageInfo高度可能有問題: 期望 ${expectedHeight}, 實際 ${result.imageInfo.height}`);
        } else {
          console.log('✅ ImageInfo高度正確');
        }

      } else {
        console.error('轉換失敗:', result.error);
      }

    } catch (error) {
      console.error('測試發生錯誤:', error);
    }
  });

  console.log('\n=== EPD 尺寸匹配處理測試結束 ===');
}

/**
 * 測試寬度對齊邏輯
 */
export function testWidthPadding() {
  console.log('\n=== 寬度對齊測試開始 ===');

  const testSizes = [
    { width: 127, height: 296, colorType: DisplayColorType.BWR, expectedPadded: 128 },
    { width: 129, height: 296, colorType: DisplayColorType.BWR, expectedPadded: 136 },
    { width: 295, height: 128, colorType: DisplayColorType.BW, expectedPadded: 296 },
    { width: 297, height: 128, colorType: DisplayColorType.BW, expectedPadded: 298 }
  ];

  testSizes.forEach((testSize, index) => {
    console.log(`\n--- 測試案例 ${index + 1}: ${testSize.width}x${testSize.height} (${testSize.colorType}) ---`);

    const canvas = createTestCanvas(testSize.width, testSize.height);

    const options = {
      colorType: testSize.colorType,
      width: canvas.width,
      height: canvas.height,
      imagecode: 0x12345678,
      x: 0,
      y: 0,
      templateRotation: 0
    };

    try {
      const result = EpdConverter.convert(canvas, options);

      if (result.success) {
        console.log(`實際對齊寬度: ${result.metadata.finalSize.width}, 期望: ${testSize.expectedPadded}`);

        if (result.metadata.finalSize.width === testSize.expectedPadded) {
          console.log('✅ 寬度對齊正確');
        } else {
          console.warn(`⚠️  寬度對齊不符期望`);
        }
      } else {
        console.error('轉換失敗:', result.error);
      }
    } catch (error) {
      console.error('測試發生錯誤:', error);
    }
  });

  console.log('\n=== 寬度對齊測試結束 ===');
}

// 如果在瀏覽器環境中，可以直接執行測試
if (typeof window !== 'undefined') {
  // 延遲執行，確保DOM已載入
  setTimeout(() => {
    testEPDRotationConversion();
    testWidthPadding();
  }, 1000);
}
