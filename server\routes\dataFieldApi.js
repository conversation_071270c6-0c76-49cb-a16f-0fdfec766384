const express = require('express');
const router = express.Router();
const { ObjectId } = require('mongodb');

// MongoDB 連接信息 (從 index.js 共享)
const collectionName = 'dataFields';

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取數據集合
const getCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();

  // 檢查 dataFields 集合是否存在，若不存在則創建
  const collections = await db.listCollections({ name: collectionName }).toArray();
  if (collections.length === 0) {
    console.log(`創建 ${collectionName} 集合`);
    await db.createCollection(collectionName);
  }

  const collection = db.collection(collectionName);
  return { collection, client };
};

// 獲取所有資料欄位
router.get('/dataFields', async (req, res) => {
  try {
    const { collection } = await getCollection();
    const dataFields = await collection.find().toArray();
    res.json(dataFields);
  } catch (error) {
    console.error('獲取資料欄位失敗:', error);
    res.status(500).json({ error: '獲取資料欄位失敗' });
  }
});

// 根據區塊類型獲取資料欄位
router.get('/dataFields/section/:section', async (req, res) => {
  try {
    const { section } = req.params;
    const { collection } = await getCollection();
    const filteredFields = await collection.find({ section }).toArray();
    res.json(filteredFields);
  } catch (error) {
    console.error(`獲取 ${req.params.section} 區塊資料欄位失敗:`, error);
    res.status(500).json({ error: `獲取 ${req.params.section} 區塊資料欄位失敗` });
  }
});

// 創建新資料欄位
router.post('/dataFields', async (req, res) => {
  try {
    const dataField = req.body;

    // 檢查請求中是否包含 ID，若無則返回錯誤
    if (!dataField.id) {
      return res.status(400).json({ error: '請提供資料欄位 ID' });
    }
      const { collection } = await getCollection();
    // 檢查 ID 和 Name 是否重複
    if (dataField.id) {
      console.log(`檢查 ID 是否重複: ${dataField.id}`);
      const duplicateId = await collection.findOne({ id: dataField.id });
      console.log(`查詢結果:`, duplicateId ? '找到重複' : '無重複');
      if (duplicateId) {
        console.log(`重複的 ID 記錄:`, duplicateId);
        return res.status(400).json({ error: 'ID 已存在，請使用其他 ID' });
      }
    }

    if (dataField.name) {
      console.log(`檢查名稱是否重複: ${dataField.name}`);
      const duplicateName = await collection.findOne({ name: dataField.name });
      console.log(`查詢結果:`, duplicateName ? '找到重複' : '無重複');
      if (duplicateName) {
        return res.status(400).json({ error: '名稱已存在，請使用其他名稱' });
      }
    }

    // 計算排序順序
    const sectionFields = await collection.find({ section: dataField.section }).toArray();
      const newDataField = {
      ...dataField,
      sortOrder: sectionFields.length
    };

    const result = await collection.insertOne(newDataField);

    // 確保回傳完整的資料欄位，包含 _id
    const insertedDataField = await collection.findOne({ id: dataField.id });

    // 如果這是第一個資料欄位，重新排序並返回最新的完整列表
    const allFields = await collection.find().toArray();
    if (allFields.length === 1) {
      console.log('這是資料庫中的第一筆資料，返回完整列表');
      res.status(201).json(insertedDataField);
    } else {
      res.status(201).json(insertedDataField);
    }
  } catch (error) {
    console.error('創建資料欄位失敗:', error);
    res.status(500).json({ error: '創建資料欄位失敗' });
  }
});

// 更新資料欄位
router.put('/dataFields/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const { collection } = await getCollection();

    // 檢查欄位是否存在
    const existingField = await collection.findOne({ id });
    if (!existingField) {
      return res.status(404).json({ error: '資料欄位不存在' });
    }

    // 如果要更新 name，需檢查是否重複
    if (updateData.name && updateData.name !== existingField.name) {
      const duplicateName = await collection.findOne({ name: updateData.name, id: { $ne: id } });
      if (duplicateName) {
        return res.status(400).json({ error: '名稱已存在，請使用其他名稱' });
      }
    }

    // 更新資料
    const result = await collection.updateOne(
      { id },
      { $set: updateData }
    );

    const updatedField = await collection.findOne({ id });
    res.json(updatedField);
  } catch (error) {
    console.error('更新資料欄位失敗:', error);
    res.status(500).json({ error: '更新資料欄位失敗' });
  }
});

// 刪除資料欄位
router.delete('/dataFields/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const { collection } = await getCollection();

    // 檢查欄位是否存在
    const fieldToDelete = await collection.findOne({ id });
    if (!fieldToDelete) {
      return res.status(404).json({ error: '資料欄位不存在' });
    }

    // 找出被刪除欄位的區塊類型
    const removedSection = fieldToDelete.section;

    // 刪除欄位
    await collection.deleteOne({ id });

    // 重新調整同一區塊內欄位的排序
    const sectionFields = await collection.find({ section: removedSection })
      .sort({ sortOrder: 1 })
      .toArray();

    // 批量更新操作
    const bulkOps = sectionFields.map((field, idx) => ({
      updateOne: {
        filter: { id: field.id },
        update: { $set: { sortOrder: idx } }
      }
    }));

    if (bulkOps.length > 0) {
      await collection.bulkWrite(bulkOps);
    }

    res.status(204).send();
  } catch (error) {
    console.error('刪除資料欄位失敗:', error);
    res.status(500).json({ error: '刪除資料欄位失敗' });
  }
});

// 批量更新資料欄位排序
router.put('/dataFields/batch/order', async (req, res) => {
  try {
    const updateOrders = req.body;

    const { collection } = await getCollection();

    // 創建批量更新操作
    const bulkOps = updateOrders.map(update => ({
      updateOne: {
        filter: { id: update.id },
        update: { $set: { sortOrder: update.sortOrder } }
      }
    }));

    const result = await collection.bulkWrite(bulkOps);
    const updatedFields = await collection.find().toArray();
    res.json(updatedFields);
  } catch (error) {
    console.error('更新資料欄位排序失敗:', error);
    res.status(500).json({ error: '更新資料欄位排序失敗' });
  }
});

// 檢查 ID 或名稱是否重複
router.get('/dataFields/check-duplicate', async (req, res) => {
  try {
    const { id, name, excludeId } = req.query;

    const { collection } = await getCollection();
      // 檢查重複 ID
    let isDuplicateId = false;
    if (id) {
      console.log(`檢查 ID 是否重複: ${id}, excludeId: ${excludeId || '無'}`);

      // 正確構建查詢條件
      let query = { id: id };

      // 使用正確的查詢條件
      const duplicateId = await collection.findOne(query);
      console.log(`查詢結果:`, duplicateId ? '找到重複' : '無重複');

      // 如果找到重複的 ID，且不是排除的 ID，則標記為重複
      if (duplicateId && (!excludeId || duplicateId.id !== excludeId)) {
        isDuplicateId = true;
        console.log(`找到重複的 ID 記錄:`, duplicateId);
      }
    }

    // 檢查重複名稱
    let isDuplicateName = false;
    if (name) {
      console.log(`檢查名稱是否重複: ${name}, excludeId: ${excludeId || '無'}`);
      const query = excludeId ? { name: name, id: { $ne: excludeId } } : { name: name };
      const duplicateName = await collection.findOne(query);
      console.log(`查詢結果:`, duplicateName ? '找到重複' : '無重複');
      if (duplicateName) {
        isDuplicateName = true;
      }
    }

    res.json({ isDuplicateId, isDuplicateName });
  } catch (error) {
    console.error('檢查重複失敗:', error);
    res.status(500).json({ error: '檢查重複失敗' });
  }
});

// 確保 id 欄位存在的端點
router.post('/dataFields/ensure-id-field', async (req, res) => {
  try {
    const { collection } = await getCollection();

    // 使用 findOneAndUpdate 原子操作
    // 如果 id 欄位不存在，則創建；如果已存在，則返回現有的
    const result = await collection.findOneAndUpdate(
      { id: 'id' },
      {
        $setOnInsert: {
          id: 'id',
          type: 'unique identifier',
          name: 'id',
          prefix: '',
          section: 'ordinary',
          sortOrder: -1
        }
      },
      {
        upsert: true,
        returnDocument: 'after'
      }
    );

    // 返回結果
    res.status(200).json(result.value || result);
  } catch (error) {
    console.error('確保 id 欄位存在失敗:', error);
    res.status(500).json({ error: '確保 id 欄位存在失敗' });
  }
});

// 與資料庫同步資料欄位
router.post('/dataFields/sync', async (req, res) => {
  try {
    // 在 MongoDB 實現中，資料已經在資料庫中，不需要額外同步
    // 這裡只需返回成功響應即可
    res.json({ success: true, lastSynced: new Date().toISOString() });
  } catch (error) {
    console.error('同步資料欄位失敗:', error);
    res.status(500).json({ error: '同步資料欄位失敗' });
  }
});

// 導出路由器和初始化函數
module.exports = { router, initDB };