import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../../store/authStore';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '../../components/ui/table';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from '../../components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../../components/ui/select';
import { Checkbox } from '../../components/ui/checkbox';
import { Label } from '../../components/ui/label';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '../../components/ui/alert-dialog';
import { Pencil, Trash2, Plus, Search, X, ChevronDown, ChevronRight } from 'lucide-react';
import { getAllRoles, getAvailablePermissions, createRole, updateRole, deleteRole } from '../../utils/api/roleApi';

// 角色接口
interface Role {
  _id: string;
  name: string;
  description: string;
  type: 'system' | 'store';
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

// 權限接口
interface Permission {
  id: string;
  name: string;
  group: string;
  roleType?: 'system' | 'store' | 'all';
  subGroup?: string;
}

export const RoleManagement: React.FC = () => {
  const { t } = useTranslation();
  const { token } = useAuthStore();

  // 狀態
  const [roles, setRoles] = useState<Role[]>([]);
  const [availablePermissions, setAvailablePermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  // 對話框狀態
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentRole, setCurrentRole] = useState<Role | null>(null);

  // 表單狀態
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'system',
    permissions: [] as string[]
  });

  // 獲取角色列表
  const fetchRoles = async () => {
    try {
      setLoading(true);
      const data = await getAllRoles(filterType !== 'all' ? filterType : undefined);
      setRoles(data);
    } catch (error) {
      console.error('獲取角色列表錯誤:', error);
    } finally {
      setLoading(false);
    }
  };

  // 獲取可用權限列表
  const fetchAvailablePermissions = async () => {
    try {
      const data = await getAvailablePermissions();
      setAvailablePermissions(data);

      // 初始化所有權限組為收折狀態
      const groups = data.reduce((acc, permission) => {
        acc[permission.group] = false; // 預設為收折狀態
        return acc;
      }, {} as Record<string, boolean>);

      setExpandedGroups(groups);

      // 輸出權限組信息，用於調試
      const uniqueGroups = [...new Set(data.map(p => p.group))];
      console.log('可用權限組:', uniqueGroups);

      // 按組分類權限
      const groupedPermissions = data.reduce((acc, permission) => {
        if (!acc[permission.group]) {
          acc[permission.group] = [];
        }
        acc[permission.group].push(permission);
        return acc;
      }, {} as Record<string, Permission[]>);

      console.log('按組分類的權限:', groupedPermissions);
    } catch (error) {
      console.error('獲取可用權限列表錯誤:', error);
    }
  };

  // 初始化
  useEffect(() => {
    fetchRoles();
    fetchAvailablePermissions();
  }, [token]);

  // 處理搜索
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // 處理過濾
  const handleFilterChange = (value: string) => {
    setFilterType(value);
  };

  // 過濾角色
  const filteredRoles = roles.filter(role => {
    const matchesSearch = role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         role.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterType === 'all' || role.type === filterType;

    return matchesSearch && matchesFilter;
  });

  // 輸出調試信息
  console.log('所有可用權限組:', [...new Set(availablePermissions.map(p => p.group))]);

  // 處理添加角色
  const handleAddRole = async () => {
    try {
      setLoading(true);

      await createRole(formData);

      // 重新獲取角色列表
      await fetchRoles();

      // 關閉對話框
      setIsAddDialogOpen(false);

      // 重置表單
      setFormData({
        name: '',
        description: '',
        type: 'system',
        permissions: []
      });
    } catch (error) {
      console.error('添加角色錯誤:', error);
    } finally {
      setLoading(false);
    }
  };

  // 處理編輯角色
  const handleEditRole = async () => {
    if (!currentRole) return;

    try {
      setLoading(true);

      await updateRole(currentRole._id, formData);

      // 重新獲取角色列表
      await fetchRoles();

      // 關閉對話框
      setIsEditDialogOpen(false);

      // 重置當前角色
      setCurrentRole(null);
    } catch (error) {
      console.error('編輯角色錯誤:', error);
    } finally {
      setLoading(false);
    }
  };

  // 處理刪除角色
  const handleDeleteRole = async () => {
    if (!currentRole) return;

    try {
      setLoading(true);

      await deleteRole(currentRole._id);

      // 重新獲取角色列表
      await fetchRoles();

      // 關閉對話框
      setIsDeleteDialogOpen(false);

      // 重置當前角色
      setCurrentRole(null);
    } catch (error) {
      console.error('刪除角色錯誤:', error);
    } finally {
      setLoading(false);
    }
  };

  // 打開編輯對話框
  const openEditDialog = (role: Role) => {
    setCurrentRole(role);
    setFormData({
      name: role.name,
      description: role.description,
      type: role.type,
      permissions: role.permissions
    });
    setIsEditDialogOpen(true);
  };

  // 打開刪除對話框
  const openDeleteDialog = (role: Role) => {
    setCurrentRole(role);
    setIsDeleteDialogOpen(true);
  };

  // 處理權限選擇
  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      // 添加權限
      setFormData(prev => ({
        ...prev,
        permissions: [...prev.permissions, permissionId]
      }));
    } else {
      // 移除權限
      setFormData(prev => ({
        ...prev,
        permissions: prev.permissions.filter(id => id !== permissionId)
      }));
    }
  };

  // 處理組權限全選/取消全選
  const handleGroupPermissionChange = (_group: string, permissions: Permission[], checked: boolean) => {
    // 只考慮當前角色類型的權限
    const relevantPermissions = permissions.filter(
      permission => permission.roleType === formData.type || permission.roleType === 'all'
    );

    // 如果沒有相關權限，不做任何操作
    if (relevantPermissions.length === 0) return;

    const permissionIds = relevantPermissions.map(p => p.id);

    if (checked) {
      // 添加所有子權限
      setFormData(prev => {
        const newPermissions = [...prev.permissions];
        permissionIds.forEach(id => {
          if (!newPermissions.includes(id)) {
            newPermissions.push(id);
          }
        });
        return {
          ...prev,
          permissions: newPermissions
        };
      });
    } else {
      // 移除所有子權限
      setFormData(prev => ({
        ...prev,
        permissions: prev.permissions.filter(id => !permissionIds.includes(id))
      }));
    }
  };

  // 切換組的展開/收起狀態
  const toggleGroupExpand = (group: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [group]: !prev[group]
    }));
  };

  // 檢查組是否全部選中
  const isGroupAllChecked = (permissions: Permission[]) => {
    // 只考慮當前角色類型的權限
    const relevantPermissions = permissions.filter(
      permission => permission.roleType === formData.type || permission.roleType === 'all'
    );

    // 如果沒有相關權限，返回 false
    if (relevantPermissions.length === 0) return false;

    return relevantPermissions.every(permission => formData.permissions.includes(permission.id));
  };

  // 檢查組是否部分選中
  const isGroupPartiallyChecked = (permissions: Permission[]) => {
    // 只考慮當前角色類型的權限
    const relevantPermissions = permissions.filter(
      permission => permission.roleType === formData.type || permission.roleType === 'all'
    );

    // 如果沒有相關權限，返回 false
    if (relevantPermissions.length === 0) return false;

    return relevantPermissions.some(permission => formData.permissions.includes(permission.id)) &&
           !isGroupAllChecked(permissions);
  };

  // 按權限組分組
  const permissionGroups = availablePermissions.reduce((groups, permission) => {
    if (!groups[permission.group]) {
      groups[permission.group] = [];
    }
    groups[permission.group].push(permission);
    return groups;
  }, {} as Record<string, Permission[]>);

  return (
    <div>
      {/* 工具欄 */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder={t('common.search')}
              value={searchTerm}
              onChange={handleSearch}
              className="pl-8 w-64"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute right-2 top-1/2 transform -translate-y-1/2"
              >
                <X className="h-4 w-4 text-gray-400" />
              </button>
            )}
          </div>

          <Select value={filterType} onValueChange={handleFilterChange}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder={t('permission.roleType')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('permission.all')}</SelectItem>
              <SelectItem value="system">{t('permission.system')}</SelectItem>
              <SelectItem value="store">{t('permission.store')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button onClick={() => {
          setFormData({
            name: '',
            description: '',
            type: 'system',
            permissions: []
          });
          setIsAddDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          {t('permission.addRole')}
        </Button>
      </div>

      {/* 角色表格 */}
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('permission.roleName')}</TableHead>
              <TableHead>{t('permission.roleDescription')}</TableHead>
              <TableHead>{t('permission.roleType')}</TableHead>
              <TableHead className="w-24">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                  </div>
                  <p className="mt-2 text-sm text-gray-500">{t('common.loading')}</p>
                </TableCell>
              </TableRow>
            ) : filteredRoles.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  <p className="text-gray-500">{t('common.noData')}</p>
                </TableCell>
              </TableRow>
            ) : (
              filteredRoles.map(role => (
                <TableRow key={role._id}>
                  <TableCell className="font-medium">{role.name}</TableCell>
                  <TableCell>{role.description}</TableCell>
                  <TableCell>
                    {role.type === 'system' ? t('permission.system') : t('permission.store')}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="icon" onClick={() => openEditDialog(role)}>
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => openDeleteDialog(role)}>
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 添加角色對話框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{t('permission.addRole')}</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                {t('permission.roleName')}
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                {t('permission.roleDescription')}
              </Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                {t('permission.roleType')}
              </Label>
              <Select
                value={formData.type}
                onValueChange={(value) => {
                  const newType = value as 'system' | 'store';

                  // 輸出調試信息
                  console.log(`角色類型變更為: ${newType}`);

                  // 過濾適用於新角色類型的權限
                  const filteredPermissions = formData.permissions.filter(permId => {
                    // 查找權限
                    const permission = availablePermissions.find(p => p.id === permId);
                    if (!permission) return false;

                    // 檢查權限是否適用於新角色類型
                    const isAllowed = permission.roleType === newType || permission.roleType === 'all';
                    console.log(`權限 ${permission.id} (${permission.name}) 屬於類型 "${permission.roleType}", 是否允許: ${isAllowed}`);

                    return isAllowed;
                  });

                  // 重新渲染組件
                  setFormData({
                    ...formData,
                    type: newType,
                    permissions: filteredPermissions
                  });

                  // 強制更新權限組顯示
                  setTimeout(() => {
                    console.log('角色類型變更完成，已更新權限組顯示');
                  }, 0);
                }}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder={t('permission.roleType')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="system">{t('permission.system')}</SelectItem>
                  <SelectItem value="store">{t('permission.store')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 gap-4">
              <Label className="text-right pt-2">
                {t('permission.permissions')}
              </Label>
              <div className="col-span-3 space-y-4">
                {/* 全選按鈕 */}
                <div className="flex justify-end mb-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // 獲取當前角色類型的所有權限 ID
                      const allPermissionIds = availablePermissions
                        .filter(p => p.roleType === formData.type || p.roleType === 'all')
                        .map(p => p.id);

                      // 檢查是否已經全選
                      const isAllSelected = allPermissionIds.every(id =>
                        formData.permissions.includes(id)
                      );

                      // 如果已經全選，則取消全選；否則全選
                      if (isAllSelected) {
                        setFormData({
                          ...formData,
                          permissions: []
                        });
                      } else {
                        setFormData({
                          ...formData,
                          permissions: allPermissionIds
                        });
                      }
                    }}
                  >
                    {availablePermissions
                      .filter(p => p.roleType === formData.type || p.roleType === 'all')
                      .every(p => formData.permissions.includes(p.id))
                      ? t('common.unselectAll')
                      : t('common.selectAll')
                    }
                  </Button>
                </div>

                {/* 權限組 */}
                {Object.entries(permissionGroups)
                  .filter(([group, permissions]) => {
                    // 檢查該組中是否有當前角色類型的權限
                    const hasPermissionsForRoleType = permissions.some(
                      permission => permission.roleType === formData.type || permission.roleType === 'all'
                    );

                    // 輸出調試信息
                    console.log(`過濾權限組: "${group}"`);
                    console.log(`當前角色類型: ${formData.type}`);
                    console.log(`權限組 "${group}" 是否包含當前角色類型的權限: ${hasPermissionsForRoleType}`);

                    return hasPermissionsForRoleType;
                  })
                  .map(([group, permissions]) => {
                    const isGroupExpanded = expandedGroups[group] === true; // 默認收折
                    const isAllChecked = isGroupAllChecked(permissions);
                    const isPartiallyChecked = isGroupPartiallyChecked(permissions);

                    return (
                      <div key={group} className="space-y-2 border rounded-md p-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`group-${group}`}
                            checked={isAllChecked || isPartiallyChecked}
                            data-state={isPartiallyChecked ? "indeterminate" : isAllChecked ? "checked" : "unchecked"}
                            onCheckedChange={(checked) =>
                              handleGroupPermissionChange(group, permissions, checked as boolean)
                            }
                          />
                          <Label
                            htmlFor={`group-${group}`}
                            className="text-sm font-medium flex-grow cursor-pointer"
                            onClick={() => toggleGroupExpand(group)}
                          >
                            {group.startsWith('sidebar.') ? t(group) : group}
                          </Label>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="p-0 h-6 w-6"
                            onClick={() => toggleGroupExpand(group)}
                          >
                            {isGroupExpanded ?
                              <ChevronDown className="h-4 w-4" /> :
                              <ChevronRight className="h-4 w-4" />
                            }
                          </Button>
                        </div>

                        {isGroupExpanded && (
                          <div className="grid grid-cols-2 gap-2 pl-6 mt-2 border-t pt-2">
                            {permissions
                              .filter(permission => permission.roleType === formData.type || permission.roleType === 'all')
                              .map(permission => (
                                <div key={permission.id} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`permission-${permission.id}`}
                                    checked={formData.permissions.includes(permission.id)}
                                    onCheckedChange={(checked) =>
                                      handlePermissionChange(permission.id, checked as boolean)
                                    }
                                  />
                                  <Label htmlFor={`permission-${permission.id}`} className="text-sm">
                                    {permission.name}
                                  </Label>
                                </div>
                              ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
              </div>
            </div>
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">{t('common.cancel')}</Button>
            </DialogClose>
            <Button onClick={handleAddRole} disabled={loading}>
              {loading ? t('common.saving') : t('common.save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 編輯角色對話框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{t('permission.editRole')}</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                {t('permission.roleName')}
              </Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-description" className="text-right">
                {t('permission.roleDescription')}
              </Label>
              <Input
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-type" className="text-right">
                {t('permission.roleType')}
              </Label>
              <Select
                value={formData.type}
                onValueChange={(value) => {
                  const newType = value as 'system' | 'store';

                  // 輸出調試信息
                  console.log(`編輯角色 - 角色類型變更為: ${newType}`);

                  // 過濾適用於新角色類型的權限
                  const filteredPermissions = formData.permissions.filter(permId => {
                    // 查找權限
                    const permission = availablePermissions.find(p => p.id === permId);
                    if (!permission) return false;

                    // 檢查權限是否適用於新角色類型
                    const isAllowed = permission.roleType === newType || permission.roleType === 'all';
                    console.log(`編輯角色 - 權限 ${permission.id} (${permission.name}) 屬於類型 "${permission.roleType}", 是否允許: ${isAllowed}`);

                    return isAllowed;
                  });

                  // 重新渲染組件
                  setFormData({
                    ...formData,
                    type: newType,
                    permissions: filteredPermissions
                  });

                  // 強制更新權限組顯示
                  setTimeout(() => {
                    console.log('編輯角色 - 角色類型變更完成，已更新權限組顯示');
                  }, 0);
                }}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder={t('permission.roleType')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="system">{t('permission.system')}</SelectItem>
                  <SelectItem value="store">{t('permission.store')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 gap-4">
              <Label className="text-right pt-2">
                {t('permission.permissions')}
              </Label>
              <div className="col-span-3 space-y-4">
                {/* 全選按鈕 */}
                <div className="flex justify-end mb-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // 獲取當前角色類型的所有權限 ID
                      const allPermissionIds = availablePermissions
                        .filter(p => p.roleType === formData.type || p.roleType === 'all')
                        .map(p => p.id);

                      // 檢查是否已經全選
                      const isAllSelected = allPermissionIds.every(id =>
                        formData.permissions.includes(id)
                      );

                      // 如果已經全選，則取消全選；否則全選
                      if (isAllSelected) {
                        setFormData({
                          ...formData,
                          permissions: []
                        });
                      } else {
                        setFormData({
                          ...formData,
                          permissions: allPermissionIds
                        });
                      }
                    }}
                  >
                    {availablePermissions
                      .filter(p => p.roleType === formData.type || p.roleType === 'all')
                      .every(p => formData.permissions.includes(p.id))
                      ? t('common.unselectAll')
                      : t('common.selectAll')
                    }
                  </Button>
                </div>

                {/* 權限組 */}
                {Object.entries(permissionGroups)
                  .filter(([group, permissions]) => {
                    // 檢查該組中是否有當前角色類型的權限
                    const hasPermissionsForRoleType = permissions.some(
                      permission => permission.roleType === formData.type || permission.roleType === 'all'
                    );

                    // 輸出調試信息
                    console.log(`編輯角色 - 過濾權限組: "${group}"`);
                    console.log(`編輯角色 - 當前角色類型: ${formData.type}`);
                    console.log(`編輯角色 - 權限組 "${group}" 是否包含當前角色類型的權限: ${hasPermissionsForRoleType}`);

                    return hasPermissionsForRoleType;
                  })
                  .map(([group, permissions]) => {
                    const isGroupExpanded = expandedGroups[group] === true; // 默認收折
                    const isAllChecked = isGroupAllChecked(permissions);
                    const isPartiallyChecked = isGroupPartiallyChecked(permissions);

                    return (
                      <div key={group} className="space-y-2 border rounded-md p-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`edit-group-${group}`}
                            checked={isAllChecked || isPartiallyChecked}
                            data-state={isPartiallyChecked ? "indeterminate" : isAllChecked ? "checked" : "unchecked"}
                            onCheckedChange={(checked) =>
                              handleGroupPermissionChange(group, permissions, checked as boolean)
                            }
                          />
                          <Label
                            htmlFor={`edit-group-${group}`}
                            className="text-sm font-medium flex-grow cursor-pointer"
                            onClick={() => toggleGroupExpand(group)}
                          >
                            {group.startsWith('sidebar.') ? t(group) : group}
                          </Label>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="p-0 h-6 w-6"
                            onClick={() => toggleGroupExpand(group)}
                          >
                            {isGroupExpanded ?
                              <ChevronDown className="h-4 w-4" /> :
                              <ChevronRight className="h-4 w-4" />
                            }
                          </Button>
                        </div>

                        {isGroupExpanded && (
                          <div className="grid grid-cols-2 gap-2 pl-6 mt-2 border-t pt-2">
                            {permissions
                              .filter(permission => permission.roleType === formData.type || permission.roleType === 'all')
                              .map(permission => (
                                <div key={permission.id} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`edit-permission-${permission.id}`}
                                    checked={formData.permissions.includes(permission.id)}
                                    onCheckedChange={(checked) =>
                                      handlePermissionChange(permission.id, checked as boolean)
                                    }
                                  />
                                  <Label htmlFor={`edit-permission-${permission.id}`} className="text-sm">
                                    {permission.name}
                                  </Label>
                                </div>
                              ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
              </div>
            </div>
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">{t('common.cancel')}</Button>
            </DialogClose>
            <Button onClick={handleEditRole} disabled={loading}>
              {loading ? t('common.saving') : t('common.save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 刪除角色確認對話框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('permission.confirmDelete')}</AlertDialogTitle>
            <AlertDialogDescription>
              {currentRole && t('permission.confirmDeleteRole', { name: currentRole.name })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRole} className="bg-red-500 hover:bg-red-600">
              {t('common.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
