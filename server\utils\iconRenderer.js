const fs = require('fs');
const path = require('path');

// Lucide icons 目錄路徑
const ICONS_DIR = path.join(__dirname, '../lucide-icons/icons');

/**
 * 前端圖標名稱到 Lucide 檔案名稱的映射表
 * 處理前端使用的名稱與 Lucide 實際檔案名稱不一致的情況
 *
 * 注意：前端使用 kebab-case 名稱（如 alert-circle），
 * 但 Lucide React 組件使用 PascalCase（如 AlertCircle），
 * 而 SVG 檔案名稱可能不同（如 circle-alert.svg）
 */
const ICON_NAME_MAPPING = {
  // 前端名稱 -> Lucide 檔案名稱
  'alert-circle': 'circle-alert',
  'check-circle': 'circle-check',
  'x-circle': 'circle-x',
  'home': 'house', // home 在 Lucide 中叫做 house
  // 其他圖標名稱保持一致，直接使用
  'star': 'star',
  'heart': 'heart',
  'square': 'square',
  'circle': 'circle',
  'triangle': 'triangle',
  'info': 'info',
  'arrow-up': 'arrow-up',
  'arrow-down': 'arrow-down',
  'arrow-left': 'arrow-left',
  'arrow-right': 'arrow-right',
  'shopping-cart': 'shopping-cart',
  'truck': 'truck',
  'package': 'package',
  'user': 'user',
  'mail': 'mail',
  'phone': 'phone',
  'calendar': 'calendar',
  'clock': 'clock',
  'settings': 'settings',
  'bookmark': 'bookmark',
  'bell': 'bell',
  'camera': 'camera'
};

/**
 * 前端 kebab-case 名稱到 Lucide React 組件 PascalCase 名稱的轉換
 * 這個函數模擬前端的轉換邏輯：iconType.split('-').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join('')
 * @param {string} iconType 前端圖標類型（kebab-case）
 * @returns {string} PascalCase 組件名稱
 */
function convertToPascalCase(iconType) {
  return iconType.split('-').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join('');
}

/**
 * PascalCase 組件名稱到 kebab-case 檔案名稱的映射
 * 用於將前端的組件名稱轉換為對應的 SVG 檔案名稱
 */
const COMPONENT_TO_FILE_MAPPING = {
  'AlertCircle': 'circle-alert',
  'CheckCircle': 'circle-check',
  'XCircle': 'circle-x',
  'Home': 'house',
  // 其他保持一致的圖標
  'Star': 'star',
  'Heart': 'heart',
  'Square': 'square',
  'Circle': 'circle',
  'Triangle': 'triangle',
  'Info': 'info',
  'ArrowUp': 'arrow-up',
  'ArrowDown': 'arrow-down',
  'ArrowLeft': 'arrow-left',
  'ArrowRight': 'arrow-right',
  'ShoppingCart': 'shopping-cart',
  'Truck': 'truck',
  'Package': 'package',
  'User': 'user',
  'Mail': 'mail',
  'Phone': 'phone',
  'Calendar': 'calendar',
  'Clock': 'clock',
  'Settings': 'settings',
  'Bookmark': 'bookmark',
  'Bell': 'bell',
  'Camera': 'camera'
};

/**
 * 將前端圖標名稱轉換為 Lucide 檔案名稱
 * 這個函數需要模擬前端的轉換邏輯，確保後端能找到正確的 SVG 檔案
 *
 * 轉換流程：
 * 1. 前端 kebab-case (alert-circle) → PascalCase (AlertCircle)
 * 2. PascalCase (AlertCircle) → SVG 檔案名稱 (circle-alert)
 *
 * @param {string} iconType 前端圖標類型（kebab-case）
 * @returns {string} Lucide SVG 檔案名稱
 */
function convertIconName(iconType) {
  // 第一步：將 kebab-case 轉換為 PascalCase（模擬前端邏輯）
  const pascalCaseName = convertToPascalCase(iconType);

  // 第二步：將 PascalCase 轉換為 SVG 檔案名稱
  const fileName = COMPONENT_TO_FILE_MAPPING[pascalCaseName];

  if (fileName) {
    console.log(`圖標名稱轉換: ${iconType} → ${pascalCaseName} → ${fileName}`);
    return fileName;
  }

  // 如果沒有特殊映射，嘗試直接使用原名稱
  console.log(`圖標名稱轉換: ${iconType} → 直接使用原名稱`);
  return iconType;
}

/**
 * 讀取並自定義 SVG 圖標
 * @param {string} iconType 前端圖標類型
 * @param {Object} options 選項
 * @param {number} options.size 圖標尺寸
 * @param {string} options.color 圖標顏色
 * @param {number} options.strokeWidth 線條寬度
 * @returns {string} 自定義後的 SVG 字符串
 */
function renderIconSvg(iconType, options = {}) {
  const { size = 24, color = '#000', strokeWidth = 2 } = options;
  
  try {
    // 轉換圖標名稱
    const fileName = convertIconName(iconType);
    const svgPath = path.join(ICONS_DIR, `${fileName}.svg`);
    
    // 檢查檔案是否存在
    if (!fs.existsSync(svgPath)) {
      console.warn(`圖標檔案不存在: ${svgPath}，使用預設圖標`);
      // 使用預設圖標（circle）
      const defaultPath = path.join(ICONS_DIR, 'circle.svg');
      if (fs.existsSync(defaultPath)) {
        const defaultSvg = fs.readFileSync(defaultPath, 'utf8');
        return customizeSvg(defaultSvg, { size, color, strokeWidth });
      }
      // 如果連預設圖標都沒有，返回簡單的 SVG
      return createFallbackSvg(size, color, strokeWidth);
    }
    
    // 讀取 SVG 內容
    const svgContent = fs.readFileSync(svgPath, 'utf8');
    
    // 自定義 SVG 屬性
    return customizeSvg(svgContent, { size, color, strokeWidth });
    
  } catch (error) {
    console.error('讀取圖標檔案時發生錯誤:', error);
    return createFallbackSvg(size, color, strokeWidth);
  }
}

/**
 * 自定義 SVG 屬性
 * @param {string} svgContent 原始 SVG 內容
 * @param {Object} options 自定義選項
 * @returns {string} 自定義後的 SVG 字符串
 */
function customizeSvg(svgContent, { size, color, strokeWidth }) {
  let customizedSvg = svgContent;

  // 替換尺寸屬性 - 只替換SVG根元素的width和height，不影響內部元素
  if (size !== undefined && size !== null) {
    // 使用更精確的正則表達式，只匹配SVG開始標籤中的width和height
    customizedSvg = customizedSvg
      .replace(/(<svg[^>]*\s)width="[^"]*"/g, `$1width="${size}"`)
      .replace(/(<svg[^>]*\s)height="[^"]*"/g, `$1height="${size}"`);
  }

  // 替換顏色屬性 - 只替換SVG根元素的stroke屬性，不影響內部元素
  if (color) {
    // 使用更精確的正則表達式，只匹配SVG開始標籤中的stroke屬性
    customizedSvg = customizedSvg
      .replace(/(<svg[^>]*\s)stroke="[^"]*"/g, `$1stroke="${color}"`);

    // 如果SVG根元素沒有stroke屬性，則添加一個
    if (!customizedSvg.match(/(<svg[^>]*\s)stroke="/)) {
      customizedSvg = customizedSvg.replace('<svg', `<svg stroke="${color}"`);
    }
  }

  // 替換線條寬度 - 只替換SVG根元素的stroke-width屬性，不影響內部元素
  if (strokeWidth !== undefined && strokeWidth !== null) {
    // 使用更精確的正則表達式，只匹配SVG開始標籤中的stroke-width屬性
    customizedSvg = customizedSvg
      .replace(/(<svg[^>]*\s)stroke-width="[^"]*"/g, `$1stroke-width="${strokeWidth}"`);

    // 如果SVG根元素沒有stroke-width屬性，則添加一個
    if (!customizedSvg.match(/(<svg[^>]*\s)stroke-width="/)) {
      customizedSvg = customizedSvg.replace('<svg', `<svg stroke-width="${strokeWidth}"`);
    }
  }

  // 確保 SVG 具有正確的基本屬性（如果沒有的話）
  if (!customizedSvg.includes('stroke-linecap')) {
    customizedSvg = customizedSvg.replace('<svg', '<svg stroke-linecap="round"');
  }
  if (!customizedSvg.includes('stroke-linejoin')) {
    customizedSvg = customizedSvg.replace('<svg', '<svg stroke-linejoin="round"');
  }

  return customizedSvg;
}

/**
 * 創建備用 SVG（當圖標檔案不存在時使用）
 * @param {number} size 尺寸
 * @param {string} color 顏色
 * @param {number} strokeWidth 線條寬度
 * @returns {string} 備用 SVG 字符串
 */
function createFallbackSvg(size, color, strokeWidth) {
  return `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="${strokeWidth}" stroke-linecap="round" stroke-linejoin="round">
    <circle cx="12" cy="12" r="10"/>
  </svg>`;
}

/**
 * 獲取所有支援的圖標類型
 * @returns {Array<string>} 支援的圖標類型列表
 */
function getSupportedIconTypes() {
  return Object.keys(ICON_NAME_MAPPING);
}

/**
 * 檢查圖標類型是否支援
 * @param {string} iconType 圖標類型
 * @returns {boolean} 是否支援
 */
function isIconTypeSupported(iconType) {
  return iconType in ICON_NAME_MAPPING;
}

module.exports = { 
  renderIconSvg, 
  getSupportedIconTypes, 
  isIconTypeSupported,
  convertIconName
};
