import * as imageEffects from './imageEffects';
import { convertImageByEffectType } from './colorConversion';
import { renderCanvasToImage } from '../components/editor/canvasUtils';
import { TemplateElement, DataField } from '../types';
import { componentBindingManager } from './dataBinding/componentBindingManager';
import { getAllDataFields } from './api/dataFieldApi';
import { getAllStoreData } from './api/storeDataApi';

/**
 * 獲取當前門店ID - 統一的方法，可在整個應用中使用
 * @param store 可選的 store 對象
 * @param device 可選的 device 對象
 * @returns 當前門店ID或null
 */
export const getCurrentStoreId = (store?: any, device?: any): string | null => {
  let currentStoreId = null;

  // 1. 從傳入的store屬性獲取 (最優先)
  if (store && store.id) {
    currentStoreId = store.id;
    console.log('從傳入的store屬性獲取門店ID:', currentStoreId);
  }
  // 2. 從App上下文中獲取
  else if ((window as any).selectedStore && (window as any).selectedStore.id) {
    currentStoreId = (window as any).selectedStore.id;
    console.log('從App上下文獲取門店ID:', currentStoreId);
  }
  // 3. 從URL參數中獲取
  else {
    const urlParams = new URLSearchParams(window.location.search);
    const storeIdFromUrl = urlParams.get('storeId');
    if (storeIdFromUrl) {
      currentStoreId = storeIdFromUrl;
      console.log('從URL參數獲取門店ID:', currentStoreId);
    }
  }

  // 如果仍然沒有獲取到門店ID，則嘗試從設備屬性獲取
  if (!currentStoreId && device && (device as any).storeId) {
    currentStoreId = (device as any).storeId;
    console.log('從設備屬性獲取門店ID:', currentStoreId);
  }

  return currentStoreId;
};

export type EffectType = 'original' | 'blackAndWhite' | 'grayscale' | 'inverted' | 'dithering';

/**
 * 用於預覽生成的數據索引信息
 */
export interface DataIndexInfo {
  dataIndex: number;
  displayName: string;
  fieldId: string;
}

/**
 * 用於預覽生成的綁定元素信息
 */
export interface BindingElement {
  element: any;
  dataIndex: number;
  fieldId: string;
  originalFieldId: string;
}

/**
 * 分析模板中的數據綁定關係
 */
export const analyzeTemplateBindings = (elements: any[]): {
  dataIndicesByIndex: DataIndexInfo[],
  fieldMappings: Map<number, string[]>
} => {
  // 收集所有帶有資料綁定的元素
  const elementsWithDataBinding: BindingElement[] = [];

  for (const element of elements) {
    if (element.dataBinding && element.dataBinding.dataIndex !== undefined && element.dataBinding.fieldId) {
      const dataIndex = element.dataBinding.dataIndex;
      const fieldId = element.dataBinding.fieldId;
      // 創建唯一標識符 - 組合 fieldId 和 dataIndex
      const uniqueFieldId = `${fieldId}_idx${dataIndex}`;

      elementsWithDataBinding.push({
        element,
        dataIndex,
        fieldId: uniqueFieldId,
        originalFieldId: fieldId
      });
    } else if (element.dataFieldId) {
      // 處理舊版數據綁定格式
      const match = element.dataFieldId.match(/(\d+)/);
      if (match) {
        const dataIndex = parseInt(match[1]) - 1; // 將 "data1" 映射到索引 0
        const fieldId = element.dataFieldId;
        // 創建唯一標識符
        const uniqueFieldId = `${fieldId}_idx${dataIndex}`;

        elementsWithDataBinding.push({
          element,
          dataIndex,
          fieldId: uniqueFieldId,
          originalFieldId: fieldId
        });
      }
    }
  }

  // 如果沒有找到任何綁定元素，返回空結果
  if (elementsWithDataBinding.length === 0) {
    return { dataIndicesByIndex: [], fieldMappings: new Map() };
  }

  // 按照 dataIndex 排序
  elementsWithDataBinding.sort((a, b) => a.dataIndex - b.dataIndex);

  // 按照 dataIndex 分組，保留每個 dataIndex 只出現一次
  const dataIndicesByIndex: DataIndexInfo[] = [];
  const seenDataIndices = new Set<number>();
  const fieldIdsByDataIndex = new Map<number, string[]>(); // 用於追踪每個 dataIndex 對應的所有 fieldId

  elementsWithDataBinding.forEach(item => {
    // 如果這個 dataIndex 還沒處理過
    if (!seenDataIndices.has(item.dataIndex)) {
      seenDataIndices.add(item.dataIndex);
      dataIndicesByIndex.push({
        dataIndex: item.dataIndex,
        displayName: `資料${item.dataIndex + 1}`, // dataIndex 是從 0 開始的，顯示時 +1
        fieldId: `dataIndex${item.dataIndex}` // 使用 dataIndex 作為唯一標識符
      });

      // 為這個 dataIndex 創建一個 fieldId 數組
      fieldIdsByDataIndex.set(item.dataIndex, [item.fieldId]);
    } else {
      // 如果這個 dataIndex 已經處理過，將當前 fieldId 添加到對應的數組中
      const existingFieldIds = fieldIdsByDataIndex.get(item.dataIndex);
      if (existingFieldIds) {
        existingFieldIds.push(item.fieldId);
      }
    }
  });

  return { dataIndicesByIndex, fieldMappings: fieldIdsByDataIndex };
};



/**
 * 處理資料欄位替換
 * @param elements 模板元素陣列
 * @param dataFields 資料欄位定義
 * @param sampleData 範例資料
 * @returns 處理後的元素陣列（複製而非修改原始陣列）
 */
export const processDataFieldBindings = (
  elements: TemplateElement[],
  dataFields: DataField[],
  sampleData: Record<string, any> | null = null
): TemplateElement[] => {
  // 複製陣列，避免修改原始資料
  return elements.map(element => {
    // 兼容舊版本：處理使用 dataFieldId 屬性的元素
    if ((element.type === 'text' || element.type === 'multiline-text') && element.dataFieldId && !element.dataBinding) {
      const dataField = dataFields.find(field => field.id === element.dataFieldId);

      // 如果找到對應的資料欄位
      if (dataField) {
        const fieldName = dataField.name;

        // 如果提供了範例資料，則使用實際資料
        if (sampleData && sampleData[dataField.id] !== undefined) {
          return {
            ...element,
            content: String(sampleData[dataField.id])
          };
        }
        // 否則使用佔位符或預設值
        else {
          return {
            ...element,
            content: dataField.defaultValue || `{${fieldName}}`
          };
        }
      }
    }

    // 使用新的綁定系統處理元素
    if (element.dataBinding && element.dataBinding.fieldId) {
      // 將 null 轉換為 undefined 以符合函數參數類型
      const safeData = sampleData === null ? undefined : sampleData;
      return componentBindingManager.processElementBinding(element, dataFields, safeData);
    }

    // 對於其他元素，保持不變
    return {...element};
  });
};

/**
 * 通用的圖像處理函數 - 與 PreviewModal 共用邏輯
 * 現在使用新的顏色轉換模組，提供更好的一致性和擴展性
 */
export const applyImageEffect = (
  canvas: HTMLCanvasElement,
  effectType: EffectType = 'blackAndWhite',
  threshold: number = 128
): HTMLCanvasElement => {
  // 使用新的顏色轉換模組
  const processedCanvas = convertImageByEffectType(canvas, effectType, threshold);
  return processedCanvas;
};

/**
 * 綁定元素處理的樣式信息接口
 */
export interface BindingStyleInfo {
  element: HTMLElement;
  textContainer: HTMLElement;
  backgroundColor: string;
  border: string;
  bindingIndicator?: HTMLElement;
  indicatorDisplay?: string;
  originalContent?: string;
  // 新增文字樣式屬性
  fontSize?: string;
  fontFamily?: string;
  color?: string;
}

/**
 * 處理綁定文字元素，使其顯示實際數據
 * @returns 原始樣式信息數組，用於後續還原
 */
export const processTextBindings = async (): Promise<BindingStyleInfo[]> => {
  const originalStyles: BindingStyleInfo[] = [];

  try {
    // 使用統一的方法獲取當前門店ID
    const currentStoreId = getCurrentStoreId();

    // 顯示調試信息
    console.log('=== 獲取門店ID ===');
    console.log('最終使用的門店ID:', currentStoreId);

    // 獲取資料欄位和門店數據
    const [dataFields, storeData] = await Promise.all([
      getAllDataFields(),
      currentStoreId ? getAllStoreData(currentStoreId) : getAllStoreData() // 如果有門店ID則傳入
    ]);

    // 調試：檢查 storeData 的結構
    console.log('獲取到的 storeData 結構:', {
      isArray: Array.isArray(storeData),
      length: Array.isArray(storeData) ? storeData.length : 0,
      firstItem: Array.isArray(storeData) && storeData.length > 0 ? storeData[0] : null,
      storeId: currentStoreId
    });

    // 找到所有文字元素，包括單行和多行文字元素
    const textElements = document.querySelectorAll('[data-element-type="text"], [data-element-type="multiline-text"]');
    console.log('找到文字元素數量:', textElements.length);

    // 找到所有綁定數據的文字元素
    const boundTextElements = document.querySelectorAll('[data-has-binding="true"]');
    console.log('找到綁定數據的文字元素數量:', boundTextElements.length);

    // 調試：檢查所有綁定元素的屬性
    boundTextElements.forEach((el, index) => {
      if (el instanceof HTMLElement) {
        console.log(`綁定元素 #${index} 屬性:`, {
          'data-field-id': el.getAttribute('data-field-id'),
          'data-store-id': el.getAttribute('data-store-id'),
          'data-show-prefix': el.getAttribute('data-show-prefix'),
          'data-index': el.getAttribute('data-index'),
          'data-item-uid': el.getAttribute('data-item-uid'),
          'data-original-content': el.getAttribute('data-original-content')
        });
      }
    });

    // 處理每個文字元素，不僅僅是綁定數據的元素
    textElements.forEach(element => {
      if (element instanceof HTMLElement) {
        // 找到文字容器
        const textContainer = element.querySelector('.text-element-content');
        if (textContainer instanceof HTMLElement) {
          // 獲取元素是否綁定了數據
          const hasBoundDataField = element.getAttribute('data-has-binding') === 'true';

          // 獲取綁定信息
          const fieldId = element.getAttribute('data-field-id');
          const selectedStoreId = element.getAttribute('data-store-id');
          const showPrefix = element.getAttribute('data-show-prefix') === 'true';

          // 獲取元素中的 dataIndex
          const dataIndexStr = element.getAttribute('data-index');
          const dataIndex = dataIndexStr ? parseInt(dataIndexStr, 10) : 0;

          // 獲取元素中可能存在的 storeItemUid
          const storeItemUid = element.getAttribute('data-item-uid');

          // 獲取元素的原始內容
          const originalContent = element.getAttribute('data-original-content') || 'TEXT';

          // 輸出調試信息
          console.log(`處理文字元素: id=${element.getAttribute('data-element-id')}, hasBoundDataField=${hasBoundDataField}, fieldId=${fieldId}, storeId=${selectedStoreId}, dataIndex=${dataIndex}, storeItemUid=${storeItemUid}, originalContent=${originalContent}`);

          // 存儲原始樣式，包括文字樣式
          const styleInfo: BindingStyleInfo = {
            element,
            textContainer,
            backgroundColor: textContainer.style.backgroundColor,
            border: textContainer.style.border,
            originalContent: undefined,
            bindingIndicator: undefined,
            indicatorDisplay: ''
          };

          // 保存文字樣式到 styleInfo 中
          styleInfo.fontSize = textContainer.style.fontSize;
          styleInfo.fontFamily = textContainer.style.fontFamily;
          styleInfo.color = textContainer.style.color;

          // 存儲原始文字內容（不包含「已綁定」標記）
          // 尋找所有的文字節點
          const textNodes: Node[] = [];
          for (let i = 0; i < textContainer.childNodes.length; i++) {
            const node = textContainer.childNodes[i];
            if (node.nodeType === Node.TEXT_NODE) {
              textNodes.push(node);
            }
          }

          // 將所有文字節點合併為一個字符串
          styleInfo.originalContent = textNodes.map(node => node.textContent).join('') || undefined;

          // 存儲「已綁定」標記
          const bindingIndicator = textContainer.querySelector('.binding-indicator');
          if (bindingIndicator && bindingIndicator instanceof HTMLElement) {
            styleInfo.bindingIndicator = bindingIndicator;
            styleInfo.indicatorDisplay = bindingIndicator.style.display;

            // 將「已綁定」標記從 DOM 中移除，以便我們可以完全控制元素的內容
            bindingIndicator.remove();
          }

          // 移除綁定標記樣式
          textContainer.style.backgroundColor = 'transparent';
          textContainer.style.border = 'none';

          // 先清空元素內容，確保沒有任何殘留的內容
          while (textContainer.firstChild) {
            textContainer.removeChild(textContainer.firstChild);
          }

          // 重新應用文字樣式（清空內容後樣式可能丟失）
          if (styleInfo.fontSize) textContainer.style.fontSize = styleInfo.fontSize;
          if (styleInfo.fontFamily) textContainer.style.fontFamily = styleInfo.fontFamily;
          if (styleInfo.color) textContainer.style.color = styleInfo.color;

          // 如果元素綁定了數據，則處理綁定數據
          if (hasBoundDataField && fieldId && selectedStoreId) {          // 獲取特定門店的數據
            let storeSpecificData: any[] = [];

            // 調試：輸出當前處理的門店ID和欄位ID
            console.log(`正在處理門店ID: ${selectedStoreId}, 欄位ID: ${fieldId}`);

            // 處理數據結構
            if (Array.isArray(storeData)) {
              // 確保元素綁定的門店ID與當前加載的門店ID匹配
              // 如果 selectedStoreId 不存在或與 currentStoreId 不匹配，使用 currentStoreId
              const effectiveStoreId = selectedStoreId || (window as any).selectedStore?.id;
              console.log(`使用有效的門店ID: ${effectiveStoreId}, 當前元素設置的門店ID: ${selectedStoreId}`);

              // 如果直接返回的是數組，可能是使用了 storeId 參數的情況
              if (storeData.length > 0) {
                // 檢查返回的數據結構
                if (storeData[0].uid !== undefined || storeData[0].id !== undefined) {
                  console.log('使用直接返回的數據數組');
                  storeSpecificData = storeData;
                } else {
                  // 尋找選擇的門店數據
                  console.log('尋找特定門店的數據');
                  // 首先嘗試精確匹配門店ID
                  const storeDataItem = storeData.find((s: any) => s.storeId === effectiveStoreId || s.id === effectiveStoreId);
                  console.log('找到的門店數據項:', storeDataItem);

                  if (storeDataItem && Array.isArray(storeDataItem.data)) {
                    storeSpecificData = storeDataItem.data;
                    console.log('使用門店數據項的 data 數組');
                  } else if (storeDataItem && storeDataItem.storeSpecificData && Array.isArray(storeDataItem.storeSpecificData)) {
                    // 嘗試使用 storeSpecificData 屬性
                    storeSpecificData = storeDataItem.storeSpecificData;
                    console.log('使用門店數據項的 storeSpecificData 數組');
                  } else if (storeDataItem) {
                    // 如果找到門店項但沒有符合預期的數據格式，則把整個項作為數據
                    storeSpecificData = [storeDataItem];
                    console.log('使用門店數據項自身作為數據');
                  }
                }
              } else {
                console.log('門店數據為空數組');
              }
            }            // 如果沒有找到數據，記錄日誌
            if (storeSpecificData.length === 0) {
              console.log('未找到門店數據，請確保門店ID正確且數據已加載');
              // 輸出更多調試信息
              console.log('門店數據載入情況:', {
                '元素綁定的門店ID': selectedStoreId,
                '實際使用的門店ID': (window as any).selectedStore?.id,
                '獲取到的數據類型': typeof storeData,
                '獲取到的數據是否為數組': Array.isArray(storeData),
                '獲取到的數據長度': Array.isArray(storeData) ? storeData.length : 0
              });

              // 如果有數據，顯示其結構
              if (Array.isArray(storeData) && storeData.length > 0) {
                console.log('獲取到的第一項數據結構:', Object.keys(storeData[0]));
                console.log('獲取到的第一項數據:', storeData[0]);
              }
            }

            console.log(`處理綁定元素: fieldId=${fieldId}, storeId=${selectedStoreId}, 數據項數量=${storeSpecificData.length}, dataIndex=${dataIndex}`);

            // 如果有特定的商品UID，則優先使用該商品
            if (storeItemUid && storeSpecificData.length > 0) {
              // 查找特定UID的商品
              const specificItem = storeSpecificData.find(item => item.uid === storeItemUid);
              console.log(`查找特定UID的商品: ${storeItemUid}, 找到:`, specificItem ? '是' : '否');

              if (specificItem && specificItem[fieldId] !== undefined && specificItem[fieldId] !== null) {
                // 如果找到特定商品且有欄位數據，更新顯示內容
                let displayValue = String(specificItem[fieldId]);

                // 根據前綴設置決定是否顯示前綴
                const field = dataFields.find(f => f.id === fieldId);
                if (field && showPrefix) {
                  // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
                  displayValue = field.prefix ? `${field.prefix}: ${displayValue}` : `: ${displayValue}`;
                }

                // 使用文字節點添加內容
                const textNode = document.createTextNode(displayValue);
                textContainer.appendChild(textNode);
                console.log('從特定商品更新文字元件預覽內容為:', displayValue);
              } else {
                // 如果找不到特定商品或沒有欄位數據，顯示無數據提示
                const field = dataFields.find(f => f.id === fieldId);
                const fieldName = field ? field.name : fieldId;
                const textNode = document.createTextNode(`(無 ${fieldName} 數據)`);
                textContainer.appendChild(textNode);
              }
            }
            // 如果沒有特定商品UID，則使用 dataIndex 索引
            else if (storeSpecificData.length > 0) {
              // 在這裡，我們需要找到正確的數據項
              // 首先，嘗試使用 dataIndex 作為索引
              let dataItem = null;

              // 檢查 dataIndex 是否在有效範圍內
              if (dataIndex >= 0 && dataIndex < storeSpecificData.length) {
                dataItem = storeSpecificData[dataIndex];
                console.log(`使用 dataIndex=${dataIndex} 獲取數據項:`, dataItem);
              } else {
                // 如果 dataIndex 超出範圍，嘗試查找匹配的數據項
                // 這裡我們不應該使用 fieldId 作為商品ID來查找，因為 fieldId 是欄位ID
                // 而是應該嘗試查找任何可用的數據項
                console.log(`dataIndex=${dataIndex} 超出範圍，嘗試查找任何可用的數據項`);

                // 如果找不到匹配的項目，使用第一個項目
                dataItem = storeSpecificData[0];
                console.log(`使用第一個數據項:`, dataItem);
              }

              // 檢查該數據項是否包含指定欄位
              if (dataItem) {
                // 首先嘗試直接使用 fieldId 作為鍵
                if (dataItem[fieldId] !== undefined && dataItem[fieldId] !== null) {
                  // 如果數據項包含指定欄位，更新顯示內容
                  let displayValue = String(dataItem[fieldId]);

                  // 根據前綴設置決定是否顯示前綴
                  const field = dataFields.find(f => f.id === fieldId);
                  if (field && showPrefix) {
                    // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
                    displayValue = field.prefix ? `${field.prefix}: ${displayValue}` : `: ${displayValue}`;
                  }

                  // 使用文字節點添加內容
                  const textNode = document.createTextNode(displayValue);
                  textContainer.appendChild(textNode);
                  console.log('從索引更新文字元件預覽內容為:', displayValue);
                } else {
                  // 如果數據項不包含指定欄位，嘗試查找包含該欄位ID的鍵
                  console.log(`數據項不包含欄位 ${fieldId}，嘗試查找相關欄位`);

                  // 獲取數據項的所有鍵
                  const keys = Object.keys(dataItem);
                  console.log(`數據項的所有鍵:`, keys);

                  // 嘗試查找包含欄位ID的鍵
                  const matchingKey = keys.find(key =>
                    key === fieldId ||
                    key.includes(fieldId) ||
                    (fieldId.includes(key) && key.length > 3) // 避免匹配太短的鍵
                  );

                  if (matchingKey) {
                    // 如果找到匹配的鍵，使用該鍵對應的值
                    let displayValue = String(dataItem[matchingKey]);

                    // 根據前綴設置決定是否顯示前綴
                    const field = dataFields.find(f => f.id === fieldId);
                    if (field && showPrefix) {
                      // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
                      displayValue = field.prefix ? `${field.prefix}: ${displayValue}` : `: ${displayValue}`;
                    }

                    // 使用文字節點添加內容
                    const textNode = document.createTextNode(displayValue);
                    textContainer.appendChild(textNode);
                    console.log(`從匹配的鍵 ${matchingKey} 更新文字元件預覽內容為:`, displayValue);
                  } else {
                    // 如果找不到匹配的鍵，顯示無數據提示
                    const field = dataFields.find(f => f.id === fieldId);
                    const fieldName = field ? field.name : fieldId;
                    const textNode = document.createTextNode(`(無 ${fieldName} 數據)`);
                    textContainer.appendChild(textNode);
                    console.log(`找不到匹配的鍵，顯示無數據提示`);
                  }
                }
              } else {
                // 如果找不到數據項，顯示無數據提示
                const field = dataFields.find(f => f.id === fieldId);
                const fieldName = field ? field.name : fieldId;
                const textNode = document.createTextNode(`(無 ${fieldName} 數據)`);
                textContainer.appendChild(textNode);
                console.log(`找不到數據項，顯示無數據提示`);
              }
            } else {            // 如果沒有找到數據或 dataIndex 超出範圍，嘗試一些備用邏輯
              console.log('嘗試備用方案獲取數據');

              // 嘗試從全局門店數據中獲取任意有用的數據來顯示
              if (Array.isArray(storeData) && storeData.length > 0) {
                // 如果 storeData 是平面數組格式
                const firstItem = storeData[0];

                // 檢查是否有與 fieldId 匹配的欄位
                if (firstItem && firstItem[fieldId] !== undefined) {
                  console.log('從第一個數據項獲取欄位值:', firstItem[fieldId]);
                  const textNode = document.createTextNode(String(firstItem[fieldId]));
                  textContainer.appendChild(textNode);
                } else {
                  // 如果仍然無法找到，顯示預設的 'TEXT'
                  const textNode = document.createTextNode('TEXT');
                  textContainer.appendChild(textNode);
                  console.log('所有備用方案都失敗，顯示預設的 TEXT');
                }
              } else {
                // 如果仍然無法找到，顯示預設的 'TEXT'
                const textNode = document.createTextNode('TEXT');
                textContainer.appendChild(textNode);
                console.log('沒有找到數據或 dataIndex 超出範圍，顯示預設的 TEXT');
              }
            }
          } else if (hasBoundDataField && fieldId) {
            // 如果有欄位 ID 但沒有門店數據，顯示預設的 'TEXT'
            const textNode = document.createTextNode('TEXT');
            textContainer.appendChild(textNode);
            console.log('有欄位 ID 但沒有門店數據，顯示預設的 TEXT');
          } else {
            // 如果沒有綁定數據或沒有欄位 ID，顯示元素的原始內容
            const textNode = document.createTextNode(originalContent);
            textContainer.appendChild(textNode);
            console.log('沒有綁定數據或沒有欄位 ID，顯示元素的原始內容:', originalContent);
          }

          // 將樣式信息添加到數組
          originalStyles.push(styleInfo);
        }
      }
    });
  } catch (error) {
    console.error('處理綁定元素時出錯:', error);
  }

  return originalStyles;
};

/**
 * 恢復綁定元素的原始樣式和內容
 * @param originalStyles 原始樣式信息數組
 */
export const restoreTextBindings = (originalStyles: BindingStyleInfo[]): void => {
  console.log('恢復綁定數據的文字元素原始樣式:', originalStyles.length);
  originalStyles.forEach(styleInfo => {
    // 完全重建元素內容，而不是簡單地設置 textContent
    // 首先清空元素
    while (styleInfo.textContainer.firstChild) {
      styleInfo.textContainer.removeChild(styleInfo.textContainer.firstChild);
    }

    // 添加文字內容
    if (styleInfo.originalContent) {
      const textNode = document.createTextNode(styleInfo.originalContent);
      styleInfo.textContainer.appendChild(textNode);
    }

    // 恢復綁定標記
    if (styleInfo.bindingIndicator) {
      // 將綁定標記添加回元素中
      styleInfo.textContainer.appendChild(styleInfo.bindingIndicator);
      styleInfo.bindingIndicator.style.display = styleInfo.indicatorDisplay || '';
    }

    // 最後恢復樣式
    styleInfo.textContainer.style.backgroundColor = styleInfo.backgroundColor;
    styleInfo.textContainer.style.border = styleInfo.border;

    // 恢復文字樣式
    if (styleInfo.fontSize) styleInfo.textContainer.style.fontSize = styleInfo.fontSize;
    if (styleInfo.fontFamily) styleInfo.textContainer.style.fontFamily = styleInfo.fontFamily;
    if (styleInfo.color) styleInfo.textContainer.style.color = styleInfo.color;
  });
};

/**
 * 從畫布元素生成預覽圖
 */
export const generatePreviewImage = async (
  canvasElement: HTMLElement,
  effectType: EffectType = 'blackAndWhite',
  threshold: number = 128
): Promise<string | null> => {
  try {
    // 獲取畫布尺寸
    const canvasWidth = parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10);
    const canvasHeight = parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10);

    // 備份選中狀態的元素
    const selectedElements = document.querySelectorAll('.selected');

    // 臨時隱藏選中狀態以獲得乾淨的預覽圖
    selectedElements.forEach(el => {
      el.classList.remove('selected');
    });

    // 處理綁定文字元素，使其顯示實際數據
    console.log('開始處理綁定文字元素...');
    const originalStyles = await processTextBindings();
    console.log('綁定文字元素處理完成，共處理了', originalStyles.length, '個元素');

    // 渲染畫布為圖像
    // 創建一個符合 HTMLDivElement 的對象
    const canvasRef = {
      current: canvasElement as unknown as HTMLDivElement
    };

    const renderedCanvas = await renderCanvasToImage(
      canvasRef,
      canvasWidth,
      canvasHeight
    );

    // 恢復選中狀態和綁定元素的原始樣式
    selectedElements.forEach(el => {
      el.classList.add('selected');
    });

    // 恢復綁定元素的原始樣式和內容
    restoreTextBindings(originalStyles);

    if (!renderedCanvas) {
      return null;
    }

    // 應用選定的效果
    const previewCanvas = applyImageEffect(renderedCanvas, effectType, threshold);

    // 將預覽圖轉換為 base64 數據
    return previewCanvas.toDataURL('image/png');
  } catch (error) {
    console.error('生成預覽圖時出錯:', error);
    return null;
  }
};