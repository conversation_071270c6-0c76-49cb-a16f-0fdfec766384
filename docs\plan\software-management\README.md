# 軟體管理系統 - 完整實現

## 📋 項目概述

軟體管理系統是EPD Manager的重要組成部分，提供完整的韌體管理功能，支援Gateway和EPD設備的軟體上傳、驗證、分類和部署管理。

## 🎯 核心功能

### ✅ 已實現功能

1. **軟體上傳與驗證**
   - 支援拖拽和點擊上傳
   - 自動CRC校驗和驗證
   - bin檔案格式解析
   - 設備功能相容性檢查

2. **分類管理**
   - Gateway和EPD設備分區
   - WiFi和BLE功能分類
   - 自定義分類和標籤

3. **狀態管理**
   - 啟用/禁用控制
   - 影響其他頁面選單顯示
   - 狀態歷史記錄

4. **版本控制**
   - 版本資訊自動解析
   - 版本歷史追蹤
   - 升級路徑管理

5. **檔案管理**
   - 原始檔案和純韌體分離存儲
   - GridFS大檔案支援
   - 安全下載功能

## 🏗️ 系統架構

### 文件結構

```
docs/plan/software-management/
├── system-design.md           # 系統設計文檔
├── implementation-plan.md     # 實現計劃
├── implementation-summary.md  # 實現總結
└── README.md                 # 本文檔

server/
├── models/Software.js         # 軟體數據模型
├── services/BinFileParser.js  # bin檔案解析服務
└── routes/softwareApi.js      # 軟體管理API

src/
├── utils/api/softwareApi.ts   # 前端API工具
└── components/system-config/
    ├── SoftwareManagementTab.tsx    # 主管理頁面
    ├── SoftwareUploadModal.tsx      # 上傳對話框
    └── SoftwareDetailModal.tsx      # 詳細資訊頁面

tools/bin-formatter/           # bin檔案格式化工具
├── bin_formatter.py           # 主程式
├── test_bin_formatter.py      # 測試腳本
└── output/                    # 輸出目錄
```

### 技術棧

**後端**
- Node.js + Express.js
- MongoDB + GridFS
- Multer (檔案上傳)
- 自定義bin檔案解析器

**前端**
- React 18 + TypeScript
- Tailwind CSS
- Lucide React (圖標)
- 自定義UI組件

## 🔧 bin檔案格式規範

### 檔案結構

```
+----------+----------+----------+----------+----------+
| Device   | Function | Version  | Original | Checksum |
| Type     | Type     | Info     | Bin Data | (CRC32)  |
| 2 bytes  | 2 bytes  | 4 bytes  | N bytes  | 4 bytes  |
| LE       | LE       | LE       | Raw      | LE       |
+----------+----------+----------+----------+----------+
```

### 代碼映射

| 項目 | 代碼 | 名稱 | 說明 |
|------|------|------|------|
| 設備類型 | 0 | Gateway | 閘道器設備 |
| 設備類型 | 1 | EPD | 電子紙顯示器 |
| 功能類型 | 0 | WiFi | 無線網路功能 |
| 功能類型 | 1 | BLE | 藍牙低功耗功能 |

### 支援矩陣

| 設備類型 | WiFi | BLE |
|----------|------|-----|
| Gateway  | ✓    | ✓   |
| EPD      | ✗    | ✓   |

## 🚀 快速開始

### 1. 環境準備

```bash
# 安裝依賴
npm install
cd server && npm install

# 啟動MongoDB
# 確保MongoDB服務正在運行
```

### 2. 啟動系統

```bash
# 啟動後端服務
cd server
node index.js

# 啟動前端開發服務器
npm run dev
```

### 3. 訪問軟體管理

1. 打開瀏覽器訪問 `http://localhost:5173`
2. 登入系統
3. 進入「系統設定」→「軟體管理」

## 📖 使用指南

### 上傳軟體

1. 點擊「上傳軟體」按鈕
2. 拖拽或選擇.bin檔案
3. 系統自動驗證檔案格式和CRC
4. 填寫軟體名稱和描述
5. 確認上傳

### 管理軟體

1. **查看列表**: 支援搜尋、過濾和分頁
2. **狀態控制**: 點擊切換開關啟用/禁用軟體
3. **下載檔案**: 支援下載原始檔案或純韌體
4. **查看詳情**: 點擊詳細按鈕查看完整資訊
5. **刪除軟體**: 點擊刪除按鈕（需確認）

### 在其他頁面使用

軟體管理系統提供API供其他頁面獲取啟用的軟體列表：

```typescript
import { getEnabledSoftware } from '../utils/api/softwareApi';

// 獲取Gateway WiFi軟體
const gatewaySoftware = await getEnabledSoftware({
  deviceType: 'gateway',
  functionType: 'wifi'
});
```

## 🔒 安全特性

### 檔案安全
- 檔案類型白名單驗證
- 檔案大小限制（100MB）
- CRC校驗和驗證
- 檔案內容格式驗證

### 權限控制
- 基於角色的存取控制
- API端點權限保護
- 操作權限細分
- 審計日誌記錄

### 數據安全
- 檔案分離存儲
- 元數據保護
- 傳輸加密
- 備份和恢復

## 📊 API文檔

### 主要端點

```
GET    /api/software              # 獲取軟體列表
POST   /api/software/upload       # 上傳軟體
GET    /api/software/:id          # 獲取軟體詳情
PUT    /api/software/:id/status   # 更新軟體狀態
DELETE /api/software/:id          # 刪除軟體
GET    /api/software/:id/download # 下載軟體檔案
GET    /api/software/enabled      # 獲取啟用軟體列表
POST   /api/software/validate     # 驗證bin檔案
```

### 權限要求

- `software:read` - 查看軟體
- `software:create` - 上傳軟體
- `software:update` - 更新軟體
- `software:delete` - 刪除軟體

## 🛠️ bin檔案格式化工具

### 工具位置
`tools/bin-formatter/`

### 使用方法

```bash
cd tools/bin-formatter

# 互動式使用
python bin_formatter.py

# 程式化使用
from bin_formatter import BinFormatter
formatter = BinFormatter()
output = formatter.format_bin_file("firmware.bin", "gateway", "wifi", "1.0.0.0")
```

### 功能特點
- 支援Gateway和EPD設備
- 支援WiFi和BLE功能
- 自動CRC32校驗和生成
- 版本格式驗證
- 設備功能相容性檢查

## 🧪 測試

### 運行測試

```bash
# bin格式化工具測試
cd tools/bin-formatter
python test_bin_formatter.py

# 使用範例
python example_usage.py
```

### 測試覆蓋
- ✅ bin檔案解析測試
- ✅ CRC驗證測試
- ✅ 設備功能相容性測試
- ✅ API端點測試
- ✅ 權限控制測試

## 📈 效能優化

### 數據庫優化
- 索引優化
- 分頁查詢
- 結果快取

### 檔案處理優化
- 串流上傳
- 分片下載
- 並行處理

### 前端優化
- 組件懶加載
- 虛擬滾動
- 狀態管理優化

## 🔮 未來規劃

### 計劃功能
- [ ] 軟體自動部署
- [ ] 版本回滾機制
- [ ] 批次操作支援
- [ ] 軟體依賴管理
- [ ] 部署統計分析

### 技術改進
- [ ] 檔案壓縮支援
- [ ] 增量更新機制
- [ ] 分散式存儲
- [ ] 實時同步功能

## 🐛 故障排除

### 常見問題

1. **上傳失敗**
   - 檢查檔案格式是否為.bin
   - 確認檔案大小不超過100MB
   - 驗證CRC校驗和是否正確

2. **權限錯誤**
   - 確認用戶具有相應權限
   - 檢查角色配置
   - 驗證登入狀態

3. **檔案下載問題**
   - 檢查GridFS配置
   - 確認檔案存在
   - 驗證網路連接

### 日誌查看

```bash
# 查看server日誌
cd server
node index.js

# 查看瀏覽器控制台
# F12 → Console
```

## 📞 支援

如有問題或建議，請參考：
- [系統設計文檔](./system-design.md)
- [實現計劃](./implementation-plan.md)
- [實現總結](./implementation-summary.md)
- [bin格式化工具文檔](../bin-formatter/README.md)

---

**項目狀態**: ✅ 完成並可用於生產環境  
**最後更新**: 2024-12-20  
**版本**: 1.0.0
