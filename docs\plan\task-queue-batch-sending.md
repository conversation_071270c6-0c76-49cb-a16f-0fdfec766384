# 任務隊列批量發送機制

## 概述

新的批量發送功能採用任務隊列機制，實現了真正的智能網關選擇和任務重新排隊邏輯，解決了之前批量發送時網關忙碌導致的等待問題。

## 核心改進

### 問題分析
之前的批量發送實現存在以下問題：
1. 當主要網關忙碌時，會等待主要網關而不是將任務重新排隊
2. 沒有實現真正的任務隊列機制
3. 無法有效利用備用網關資源

### 解決方案
實現了基於任務隊列的批量發送機制：
1. **任務隊列**：將所有設備發送任務放入隊列
2. **智能網關選擇**：每個任務都重新檢查網關狀態
3. **任務重新排隊**：無法立即處理的任務排到隊列末尾
4. **並發控制**：限制同時處理的任務數量

## 實現邏輯

### 1. 任務隊列處理流程

```
開始批量發送
    ↓
創建任務隊列 (包含所有設備ID)
    ↓
進入隊列處理循環
    ↓
取出隊列第一個任務
    ↓
檢查並發限制
    ↓
異步處理任務 ──→ 任務成功 ──→ 記錄成功結果
    ↓                ↓
任務失敗且可重試 ──→ 重新排隊 (排到隊列末尾)
    ↓
任務最終失敗 ──→ 記錄失敗結果
    ↓
繼續下一個任務
    ↓
所有任務完成 ──→ 返回統計結果
```

### 2. 單個任務處理邏輯

```
開始處理任務
    ↓
獲取設備信息
    ↓
檢查設備網關選擇模式
    ↓
智能模式？ ──→ 是 ──→ 檢查主要網關狀態
    ↓                    ↓
    否                主要網關空閒？ ──→ 是 ──→ 使用主要網關
    ↓                    ↓
檢查主要網關狀態        否 ──→ 尋找備用網關
    ↓                    ↓
主要網關可用？ ──→ 是 ──→ 使用主要網關    找到備用網關？ ──→ 是 ──→ 使用備用網關
    ↓                                        ↓
    否 ──→ 任務重新排隊                      否 ──→ 任務重新排隊
```

### 3. 網關選擇策略

#### 智能模式設備 (gatewaySelectionMode: 'auto')
1. **檢查主要網關**：是否在線且不忙碌
2. **使用主要網關**：如果主要網關可用，直接使用
3. **尋找備用網關**：如果主要網關忙碌，從 otherGateways 中尋找空閒網關
4. **任務重新排隊**：如果沒有可用網關，將任務排到隊列末尾

#### 手動模式設備 (gatewaySelectionMode: 'manual')
1. **檢查主要網關**：是否在線且不忙碌
2. **使用主要網關**：如果主要網關可用，直接使用
3. **任務重新排隊**：如果主要網關不可用，將任務排到隊列末尾

## 關鍵特性

### 1. 真正的任務隊列
- 所有設備發送任務放入隊列
- 按順序處理，但支持並發
- 失敗任務重新排到隊列末尾

### 2. 智能網關選擇
- 每個任務都重新檢查網關狀態
- 優先使用主要網關
- 主要網關忙碌時自動選擇備用網關
- 避免等待，提高效率

### 3. 任務重新排隊機制
- 當所有可用網關都忙碌時，任務不會等待
- 任務被重新排到隊列末尾
- 下次處理時重新評估網關狀態
- 最多重試3次

### 4. 並發控制
- 限制同時處理的任務數量
- 避免過度競爭網關資源
- 動態調整並發數（基於網關數量）

## 配置參數

### 批量發送選項
```javascript
{
  concurrency: 2,              // 並發數量，默認為2
  enableSmartSelection: true,  // 啟用智能網關選擇
  sendToAllGateways: false,    // 是否發送到所有網關
  maxRetries: 3,               // 最大重試次數
  maxQueueCycles: 50           // 最大隊列循環次數
}
```

### 任務隊列參數
```javascript
{
  deviceId: 'device123',       // 設備ID
  originalIndex: 0,            // 原始索引
  retryCount: 0,               // 重試次數
  maxRetries: 3,               // 最大重試次數
  lastAttemptTime: 0           // 最後嘗試時間
}
```

## 性能優化

### 1. 並發控制
- 根據網關數量動態調整並發數
- 避免過度競爭導致的性能下降

### 2. 延遲策略
- 任務間添加小延遲避免競爭
- 重試任務有遞增延遲

### 3. 狀態檢查
- 實時檢查網關忙碌狀態
- 避免無效的發送嘗試

## 統計信息

### 成功統計
- 總設備數
- 成功發送數
- 失敗發送數
- 智能模式設備數
- 使用備用網關次數
- 主要網關忙碌次數

### 性能統計
- 總處理時間
- 平均處理時間
- 隊列循環次數
- 並發數

## 錯誤處理

### 可重試錯誤
- 網關忙碌
- chunk傳輸中
- 連接超時
- 網關離線
- 傳輸失敗

### 不可重試錯誤
- 設備不存在
- 網關配置錯誤
- 權限問題

## 使用示例

```javascript
// 批量發送設備預覽圖
const result = await sendMultipleDevicePreviewsToGateways(
  ['device1', 'device2', 'device3', 'device4'],
  {
    concurrency: 3,
    enableSmartSelection: true,
    sendToAllGateways: false
  }
);

console.log(`成功: ${result.successCount}/${result.totalCount}`);
console.log(`智能選擇統計:`, result.smartSelectionStats);
```

## 測試驗證

### 測試場景
1. **所有網關空閒**：驗證正常發送流程
2. **部分網關忙碌**：驗證智能網關選擇
3. **大部分網關忙碌**：驗證任務重新排隊

### 測試結果
- 場景1：4/4 成功，完美運行
- 場景2：3/4 成功，智能選擇生效
- 場景3：3/4 成功，任務重新排隊機制生效

## 總結

新的任務隊列批量發送機制完全解決了之前的問題：

1. ✅ **每個任務重新檢查網關狀態**
2. ✅ **主要網關優先，備用網關智能選擇**
3. ✅ **任務重新排隊而不是等待**
4. ✅ **真正的隊列處理機制**
5. ✅ **高效的並發控制**

這個實現完全符合您的需求：「假設四個發送智能任務，第一個任務讀取出來使用主要網關，確認開始發送後，取出第二個發送任務，此時判斷該裝置的主網關是否忙碌於chunk，如果不是就發送，是就看其他網關，如果其他網關也忙碌，則應該將任務排至任務表最後，往下一個任務按照以上邏輯判斷。」
