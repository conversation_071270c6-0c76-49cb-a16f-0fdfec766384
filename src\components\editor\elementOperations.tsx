import React from 'react';
import { TemplateElement } from '../../types';
import { calculateElementDimensions, calculateLineDimensions, getCanvasCoordinates } from './canvasUtils';

/**
 * 處理元素拖曳和繪製相關的邏輯
 */

// 處理畫布滑鼠按下事件
export const handleMouseDown = (
  e: React.MouseEvent<HTMLDivElement>,
  canvasRef: React.RefObject<HTMLDivElement>,
  selectedTool: string | null,
  setIsDrawing: (isDrawing: boolean) => void,
  setStartPoint: (point: { x: number; y: number }) => void,
  zoom: number
) => {
  if (!selectedTool || !canvasRef.current) return;

  const rect = canvasRef.current.getBoundingClientRect();
  const { x, y } = getCanvasCoordinates(e, rect, zoom);

  console.log('MouseDown - 工具:', selectedTool, '起始點:', { x, y });

  setIsDrawing(true);
  setStartPoint({ x, y });
};

// 處理畫布滑鼠移動事件
export const handleMouseMove = (
  e: React.MouseEvent<HTMLDivElement>,
  canvasRef: React.RefObject<HTMLDivElement>,
  isDrawing: boolean,
  selectedTool: string | null,
  startPoint: { x: number; y: number },
  zoom: number,
  tempElement: TemplateElement | null,
  setTempElement: (element: TemplateElement | null) => void
) => {
  if (!isDrawing || !selectedTool || !canvasRef.current) return;

  const rect = canvasRef.current.getBoundingClientRect();
  const currentPoint = getCanvasCoordinates(e, rect, zoom);

  // 根據工具類型選擇不同的維度計算方法
  let dimensions;
  if (selectedTool === 'line') {
    // 使用線條專用的維度計算函數
    dimensions = calculateLineDimensions(startPoint, currentPoint);
  } else if (selectedTool === 'square') {
    // 對於正方形，確保長寬相等
    const standardDimensions = calculateElementDimensions(startPoint, currentPoint);
    const size = Math.round(Math.max(standardDimensions.width, standardDimensions.height));

    // 確定正方形的方向（基於鼠標移動方向）
    const isMovingRight = currentPoint.x > startPoint.x;
    const isMovingDown = currentPoint.y > startPoint.y;

    // 計算新的坐標，保持正方形的比例
    const x = Math.round(isMovingRight ? startPoint.x : startPoint.x - size);
    const y = Math.round(isMovingDown ? startPoint.y : startPoint.y - size);

    dimensions = {
      x,
      y,
      width: size,
      height: size
    };
  } else if (selectedTool === 'circle') {
    // 對於圓形，確保長寬相等以保持圓形
    const standardDimensions = calculateElementDimensions(startPoint, currentPoint);
    const size = Math.round(Math.max(standardDimensions.width, standardDimensions.height));

    // 確定圓形的方向（基於鼠標移動方向）
    const isMovingRight = currentPoint.x > startPoint.x;
    const isMovingDown = currentPoint.y > startPoint.y;

    // 計算新的坐標，保持圓形的比例
    const x = Math.round(isMovingRight ? startPoint.x : startPoint.x - size);
    const y = Math.round(isMovingDown ? startPoint.y : startPoint.y - size);

    dimensions = {
      x,
      y,
      width: size,
      height: size
    };
  } else {
    // 對其他元素使用標準維度計算
    dimensions = calculateElementDimensions(startPoint, currentPoint);
  }

  console.log('MouseMove - 尺寸計算:', dimensions);

  // 根據工具類型設置臨時元素
  if (tempElement) {
    setTempElement({
      ...tempElement,
      ...dimensions
    });
  } else {
    setTempElement({
      id: 'temp',
      type: selectedTool as TemplateElement['type'],
      ...dimensions
    });
    console.log('MouseMove - 建立臨時元素類型:', selectedTool);
  }
};

// 處理畫布滑鼠放開事件
export const handleMouseUp = (
  e: React.MouseEvent<HTMLDivElement>,
  canvasRef: React.RefObject<HTMLDivElement>,
  isDrawing: boolean,
  selectedTool: string | null,
  startPoint: { x: number; y: number },
  zoom: number,
  tempElement: TemplateElement | null,
  setIsDrawing: (isDrawing: boolean) => void,
  setTempElement: (element: TemplateElement | null) => void,
  addElement: (type: TemplateElement['type'], x: number, y: number, width: number, height: number, extraProperties?: Record<string, any>) => void
) => {
  console.log('MouseUp - 工具:', selectedTool, '繪製狀態:', isDrawing, '臨時元素:', tempElement);

  if (!isDrawing || !selectedTool || !canvasRef.current) {
    console.log('MouseUp - 提前返回，條件不符合:', { isDrawing, selectedTool, canvasExists: !!canvasRef.current });
    return;
  }

  setIsDrawing(false);

  if (tempElement) {
    const { x, y, width, height, endX, endY } = tempElement;
    console.log('MouseUp - 臨時元素尺寸:', { x, y, width, height, endX, endY });

    // 確保線段至少有最小長度
    if (selectedTool === 'line') {
      const minLength = 5; // 最小長度
      const length = Math.round(Math.sqrt(width * width + height * height));
      console.log('MouseUp - 線段長度:', length, '最小需要:', minLength);

      if (length >= minLength) {
        console.log('MouseUp - 添加線段元素');
        // 為線條元素傳遞額外的終點坐標屬性
        addElement(
          selectedTool as TemplateElement['type'],
          x,
          y,
          width,
          height,
          { endX, endY } // 添加終點坐標作為額外屬性
        );
      } else {
        console.log('MouseUp - 線段太短，不添加');
      }
    } else {
      // 其他元素類型需要同時確保寬度和高度
      console.log('MouseUp - 其他元素類型檢查:', { width, height, minRequired: 5 });
      if (width > 5 && height > 5) {
        console.log('MouseUp - 添加元素:', selectedTool);
        addElement(selectedTool as TemplateElement['type'], x, y, width, height);
      } else {
        console.log('MouseUp - 元素太小，不添加');
      }
    }

    setTempElement(null);
  } else {
    console.log('MouseUp - 沒有臨時元素');
  }
};