# 電子紙設備預覽圖自動更新系統設計

## 1. 背景介紹

隨著電子紙設備的增加和數據的頻繁更新，手動更新所有設備的預覽圖並發送到網關變得越來越不切實際。為了解決這個問題，我們需要一個自動化系統，能夠定期檢查設備綁定的數據是否有更新，並在需要時生成新的預覽圖並發送到相應的設備。

本文檔詳細規劃了一個定時預覽圖生成與發送系統，該系統將利用現有的 `PreviewComponent`、`previewImageManager` 和 `crypto` 模組，通過 WebSocket 定期發送最新預覽圖到連接的設備。

## 2. 系統架構

### 2.1 整體架構

```
┌─────────────────────┐       ┌───────────────────────┐       ┌─────────────────┐
│                     │       │                       │       │                 │
│  定時任務調度模塊   ├──────>│  預覽圖生成處理模塊   ├──────>│  網關通信模塊   │
│  (AutoUpdateTask)   │       │  (PreviewUpdater)     │       │  (GatewaySender)│
│                     │       │                       │       │                 │
└─────────────────────┘       └───────────────────────┘       └─────────────────┘
          │                              │                             │
          │                              │                             │
          ▼                              ▼                             ▼
┌─────────────────────┐       ┌───────────────────────┐       ┌─────────────────┐
│                     │       │                       │       │                 │
│     配置管理模塊    │       │    數據檢查模塊       │       │   狀態監控模塊  │
│   (ConfigManager)   │       │   (DataChecker)       │       │  (StatusMonitor)│
│                     │       │                       │       │                 │
└─────────────────────┘       └───────────────────────┘       └─────────────────┘
```

### 2.2 主要組件

1. **定時任務調度模塊 (AutoUpdateTask)**
   - 負責根據配置的時間間隔觸發預覽圖更新任務
   - 支持全局配置和設備級別的定時設置
   - 提供手動觸發接口

2. **預覽圖生成處理模塊 (PreviewUpdater)**
   - 使用 `PreviewComponent` 組件生成預覽圖
   - 調用 `previewImageManager` 保存和管理預覽圖
   - 使用 `crypto` 模組生成 CRC 校驗碼

3. **網關通信模塊 (GatewaySender)**
   - 建立和維護與網關的 WebSocket 連接
   - 構建消息格式並發送預覽圖
   - 處理發送錯誤和重試邏輯

4. **配置管理模塊 (ConfigManager)**
   - 管理定時更新的全局和設備級別配置
   - 處理配置更新和持久化

5. **數據檢查模塊 (DataChecker)**
   - 檢查設備綁定的數據是否有更新
   - 決定是否需要重新生成預覽圖

6. **狀態監控模塊 (StatusMonitor)**
   - 記錄任務執行狀態和結果
   - 提供任務執行日誌和統計數據
   - 監控系統健康狀態

## 3. 系統實現方案

### 3.1 微服務/API 方案

由於 `PreviewComponent`、`previewImageManager` 和 `crypto` 都是前端組件，而定時任務需要在後端運行，我們採用微服務/API 方案來實現前後端協作。

#### 架構設計

```
┌────────────────────┐       ┌──────────────────────┐       ┌─────────────────────┐
│                    │       │                      │       │                     │
│  後端定時任務模塊  │       │  預覽生成 API 服務   │       │    前端渲染組件     │
│  (Node.js 後端)   ├──────>│  (Express 服務)      ├──────>│  (React 組件)       │
│                    │       │                      │       │                     │
└────────────────────┘       └──────────────────────┘       └─────────────────────┘
        │                              │                              │
        │                              │                              │
        ▼                              ▼                              ▼
┌────────────────────┐       ┌──────────────────────┐       ┌─────────────────────┐
│                    │       │                      │       │                     │
│  網關通信模塊      │       │   數據存儲模塊       │       │  預覽圖生成組件     │
│  (WebSocket 客戶端)│       │  (數據庫服務)        │       │  (PreviewComponent) │
│                    │       │                      │       │                     │
└────────────────────┘       └──────────────────────┘       └─────────────────────┘
```

#### 工作流程

此方案中，後端處理定時調度，前端負責渲染預覽圖：

1. 後端定時任務模塊按照配置的時間間隔觸發更新任務
2. 後端檢查哪些設備需要更新預覽圖
3. 後端調用前端預覽生成 API 服務
4. API 服務使用前端渲染組件生成預覽圖
5. 預覽圖和校驗碼返回給後端
6. 後端將預覽圖發送到網關

### 3.2 自動更新流程

```mermaid
sequenceDiagram
    participant Scheduler as 定時調度器(後端)
    participant AutoUpdateTask as 自動更新任務(後端)
    participant DataChecker as 數據檢查模塊(後端)
    participant APIClient as API客戶端(後端)
    participant PreviewAPI as 預覽生成API(前端服務)
    participant PreviewComponent as 預覽組件(前端)
    participant ImageManager as previewImageManager(前端)
    participant CryptoUtil as crypto(前端)
    participant GatewaySender as 網關發送器(後端)
    participant WebSocket as WebSocket服務(後端)
    
    Scheduler->>AutoUpdateTask: 觸發定時任務
    AutoUpdateTask->>DataChecker: 獲取需要更新的設備列表
    DataChecker-->>AutoUpdateTask: 返回需要更新的設備
    
    loop 每個需要更新的設備
        AutoUpdateTask->>APIClient: 調用預覽生成API
        APIClient->>PreviewAPI: HTTP POST請求
        
        PreviewAPI->>PreviewComponent: 創建臨時渲染組件
        PreviewComponent->>PreviewComponent: 渲染預覽圖
        PreviewComponent->>ImageManager: 調用handlePreviewImageGenerated
        ImageManager->>ImageManager: 保存預覽圖數據
        PreviewComponent-->>PreviewAPI: 返回生成的預覽圖
        
        PreviewAPI->>CryptoUtil: 調用calculateCRC32生成校驗碼
        CryptoUtil-->>PreviewAPI: 返回圖像校驗碼
        
        PreviewAPI-->>APIClient: 返回預覽圖和校驗碼
        APIClient-->>AutoUpdateTask: 返回API結果
        
        AutoUpdateTask->>GatewaySender: 發送預覽圖到設備
        GatewaySender->>WebSocket: 使用WebSocket發送消息
        WebSocket-->>GatewaySender: 發送結果回調
        GatewaySender-->>AutoUpdateTask: 返回發送結果
    end
    
    AutoUpdateTask->>StatusMonitor: 更新任務狀態
```

### 3.3 關鍵函數調用說明

#### 3.3.1 定時任務調度 (後端實現)

```typescript
// 創建定時任務
function createAutoUpdateTask(options: AutoUpdateOptions): TaskId {
  const { interval, deviceIds, scheduleType } = options;
  
  // 創建定時任務
  const taskId = scheduler.scheduleTask({
    name: 'preview-auto-update',
    interval: interval,
    taskFn: () => executeAutoUpdate(deviceIds),
    scheduleType: scheduleType
  });
  
  return taskId;
}

// 執行自動更新任務
async function executeAutoUpdate(deviceIds: string[]): Promise<UpdateResult[]> {
  const results: UpdateResult[] = [];
  
  // 檢查前端預覽服務是否可用
  try {
    const serviceCheck = await fetch(`${config.PREVIEW_API_URL}/health`, {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${config.API_TOKEN}` }
    });
    
    if (!serviceCheck.ok) {
      throw new Error(`預覽服務不可用: ${serviceCheck.status}`);
    }
  } catch (error) {
    console.error('預覽服務檢查失敗:', error);
    return [{ success: false, error: `預覽服務不可用: ${error.message}` }];
  }
  
  // 獲取需要更新的設備
  const devicesToUpdate = await dataChecker.getDevicesNeedingUpdate(deviceIds);
  
  // 批次處理設備，避免同時處理太多造成性能問題
  const batches = createBatches(devicesToUpdate, CONFIG.BATCH_SIZE);
  
  for (const batch of batches) {
    const batchResults = await Promise.all(
      batch.map(device => updateDevicePreview(device))
    );
    results.push(...batchResults);
  }
  
  // 更新任務狀態
  await statusMonitor.updateTaskStatus({
    taskId: currentTaskId,
    status: 'completed',
    results: results
  });
  
  return results;
}
```

#### 3.3.2 預覽圖生成與處理

```typescript
// 更新設備預覽圖 (後端實現)
async function updateDevicePreview(device: Device): Promise<UpdateResult> {
  try {
    // 檢查設備是否具備生成預覽圖的條件
    if (!device.templateId || !device.dataBindings || !device.primaryGatewayId) {
      return {
        deviceId: device._id,
        success: false,
        error: 'Missing required data for preview generation'
      };
    }
    
    // 獲取模板和數據
    const template = await fetchTemplate(device.templateId);
    const storeData = await fetchStoreData(device.storeId);
    
    // 調用前端 API 服務生成預覽圖
    const response = await fetch(`${config.PREVIEW_API_URL}/api/generate-preview`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.API_TOKEN}`
      },
      body: JSON.stringify({
        device,
        template,
        storeData
      })
    });
    
    // 解析 API 響應
    const result = await response.json();
    
    if (!result.success || !result.imageData) {
      return {
        deviceId: device._id,
        success: false,
        error: result.error || 'Failed to generate preview image'
      };
    }
    
    // 使用 API 返回的預覽圖和校驗碼
    const { imageData, imageCode } = result;
    
    // 構建消息並發送
    const sendResult = await gatewaySender.sendPreview({
      deviceId: device._id,
      deviceMac: device.macAddress,
      imageData: imageData,
      imageCode: imageCode
    });
    
    return {
      deviceId: device._id,
      success: sendResult.success,
      error: sendResult.error
    };
  } catch (error) {
    console.error(`Failed to update preview for device ${device._id}:`, error);
    return {
      deviceId: device._id,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
```

#### 3.3.3 預覽圖渲染 API 服務

```typescript
// 前端預覽圖生成 API 服務 (使用 Express)
const express = require('express');
const cors = require('cors');
const { createSSREngine } = require('./ssrEngine'); // 服務端渲染引擎

const app = express();
app.use(express.json({ limit: '10mb' }));
app.use(cors());

// 認證中間件
function authenticate(req, res, next) {
  const token = req.headers.authorization?.split(' ')[1];
  if (!token || token !== process.env.API_TOKEN) {
    return res.status(401).json({ 
      success: false, 
      error: 'Unauthorized' 
    });
  }
  next();
}

// 預覽圖生成 API 端點
app.post('/api/generate-preview', authenticate, async (req, res) => {
  const { device, template, storeData } = req.body;
  
  try {
    // 驗證請求數據
    if (!device || !template || !storeData) {
      return res.status(400).json({
        success: false,
        error: 'Missing required data'
      });
    }
    
    // 創建 SSR 渲染引擎
    const ssrEngine = createSSREngine();
    
    // 使用 PreviewComponent 生成預覽圖
    const previewImage = await ssrEngine.renderComponent({
      component: 'PreviewComponent',
      props: {
        template: template,
        bindingData: device.dataBindings || {},
        storeData: storeData,
        effectType: "blackAndWhite",
        threshold: 128,
        deviceId: device._id,
        storeId: device.storeId
      }
    });
    
    // 檢查生成結果
    if (!previewImage) {
      throw new Error('Failed to generate preview image');
    }
    
    // 計算校驗碼
    const crypto = require('../../src/utils/crypto');
    const imageCode = crypto.calculateCRC32(previewImage);
    
    // 返回結果
    res.json({
      success: true,
      imageData: previewImage,
      imageCode: imageCode
    });
  } catch (error) {
    console.error('生成預覽圖失敗:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Unknown error'
    });
  }
});

// SSR 渲染引擎實現 (使用 jsdom 或類似工具模擬瀏覽器環境)
// server/services/ssrEngine.js
function createSSREngine() {
  const { JSDOM } = require('jsdom');
  const React = require('react');
  const ReactDOMServer = require('react-dom/server');
  const { renderToString } = ReactDOMServer;
  const PreviewComponent = require('../../src/components/PreviewComponent').default;
  
  return {
    async renderComponent({ component, props }) {
      // 創建虛擬 DOM 環境
      const dom = new JSDOM('<!DOCTYPE html><html><body><div id="root"></div></body></html>');
      global.window = dom.window;
      global.document = dom.window.document;
      global.navigator = dom.window.navigator;
      
      try {
        // 在服務端渲染 React 組件
        renderToString(React.createElement(PreviewComponent, props));
        
        // 使用模擬的 PreviewComponent 生成預覽圖
        // 這裡需要進一步適配，因為實際的 PreviewComponent 可能依賴瀏覽器 API
        return new Promise((resolve) => {
          // 模擬生成預覽圖的過程
          setTimeout(() => {
            // 使用前端導出的工具函數生成預覽圖
            const previewManager = require('../../src/utils/previewImageManager');
            const previewData = previewManager.generatePreviewSync(props);
            resolve(previewData);
          }, 100);
        });
      } finally {
        // 清理全局對象
        delete global.window;
        delete global.document;
        delete global.navigator;
      }
    }
  };
}

// 啟動服務器
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`預覽圖生成 API 服務運行在端口 ${PORT}`);
});
```
```

#### 3.2.4 網關發送

```typescript
// GatewaySender.sendPreview 的實現
async sendPreview(options: {
  deviceId: string,
  deviceMac: string,
  imageData: string,
  imageCode: string
}): Promise<SendResult> {
  const { deviceId, deviceMac, imageData, imageCode } = options;
  
  // 獲取設備資訊
  const device = await this.deviceRepository.findById(deviceId);
  if (!device || !device.primaryGatewayId) {
    return { success: false, error: 'Device not found or no primary gateway' };
  }
  
  // 準備發送資訊
  const message = {
    type: 'update_preview',
    deviceMac: deviceMac,
    imageData: imageData,
    imageCode: imageCode,
    timestamp: new Date().toISOString()
  };
  
  // 嘗試發送，支持重試機制
  let attempts = 0;
  while (attempts < this.config.MAX_RETRY_ATTEMPTS) {
    try {
      const result = await this.websocketService.sendCommandToGateway(
        device.primaryGatewayId.toString(),
        message
      );
      
      // 記錄發送事件
      await this.logService.logDeviceEvent(deviceId, 'preview_sent_auto', {
        gatewayId: device.primaryGatewayId.toString(),
        success: result.success,
        timestamp: new Date(),
        autoUpdateTask: true
      });
      
      return {
        success: result.success,
        error: result.error
      };
    } catch (error) {
      attempts++;
      console.error(`發送預覽圖失敗(嘗試 ${attempts}/${this.config.MAX_RETRY_ATTEMPTS}):`, error);
      
      // 如果還有重試機會，等待一段時間後重試
      if (attempts < this.config.MAX_RETRY_ATTEMPTS) {
        await new Promise(resolve => setTimeout(resolve, this.config.RETRY_DELAY_MS));
      }
    }
  }
  
  return {
    success: false,
    error: `發送失敗，已重試 ${attempts} 次`
  };
}
```

## 4. 配置項說明

### 4.1 全局配置項

| 配置項 | 說明 | 默認值 | 單位 |
|--------|------|--------|------|
| ENABLED | 是否啟用自動更新系統 | true | - |
| DEFAULT_UPDATE_INTERVAL | 默認更新間隔時間 | 3600 | 秒 |
| BATCH_SIZE | 批量處理設備數量 | 10 | 個 |
| MAX_RETRY_ATTEMPTS | 發送失敗後最大重試次數 | 3 | 次 |
| RETRY_DELAY_MS | 重試間隔時間 | 5000 | 毫秒 |
| WORKER_THREADS | 工作線程數量 | 2 | 個 |
| LOG_LEVEL | 日誌級別 | 'info' | - |
| TASK_TIMEOUT | 任務超時時間 | 30000 | 毫秒 |
| DATA_CHECK_OPTIMIZATION | 數據檢查優化方法 | 'hash' | - |

### 4.2 設備級別配置項

| 配置項 | 說明 | 默認值 | 單位 |
|--------|------|--------|------|
| enabled | 是否為此設備啟用自動更新 | true | - |
| updateInterval | 設備預覽圖更新間隔 | null (使用全局) | 秒 |
| priority | 更新優先級 | 'normal' | - |
| scheduleType | 排程類型 | 'interval' | - |
| cronExpression | cron表達式 (scheduleType='cron'時有效) | null | - |
| dataChangeCheck | 是否僅在數據變更時更新 | true | - |

## 5. 實現細節

### 5.1 任務調度系統

任務調度系統基於 Node.js 的 `node-schedule` 庫實現，支持兩種調度方式：

1. **間隔調度 (Interval)**：以固定的時間間隔執行任務
2. **Cron 調度**：根據 cron 表達式在特定時間執行任務

```typescript
// 建立調度器
const scheduler = {
  // 存儲活動任務
  activeTasks: new Map(),
  
  // 調度任務
  scheduleTask({ name, interval, taskFn, scheduleType, cronExpression }) {
    const taskId = generateTaskId();
    
    let scheduledTask;
    if (scheduleType === 'cron' && cronExpression) {
      // 使用 cron 表達式調度
      scheduledTask = schedule.scheduleJob(cronExpression, async () => {
        await this.executeTask(taskId, name, taskFn);
      });
    } else {
      // 使用間隔調度
      scheduledTask = setInterval(async () => {
        await this.executeTask(taskId, name, taskFn);
      }, interval * 1000);
    }
    
    this.activeTasks.set(taskId, {
      id: taskId,
      name,
      scheduledTask,
      lastRun: null,
      status: 'scheduled'
    });
    
    return taskId;
  },
  
  // 執行任務
  async executeTask(taskId, name, taskFn) {
    const task = this.activeTasks.get(taskId);
    if (!task) return;
    
    task.status = 'running';
    task.lastRun = new Date();
    
    try {
      const result = await taskFn();
      task.status = 'idle';
      task.lastResult = {
        success: true,
        timestamp: new Date(),
        data: result
      };
    } catch (error) {
      task.status = 'error';
      task.lastResult = {
        success: false,
        timestamp: new Date(),
        error: error.toString()
      };
      console.error(`Task ${name} (${taskId}) failed:`, error);
    }
  },
  
  // 取消任務
  cancelTask(taskId) {
    const task = this.activeTasks.get(taskId);
    if (!task) return false;
    
    if (task.scheduleType === 'cron') {
      task.scheduledTask.cancel();
    } else {
      clearInterval(task.scheduledTask);
    }
    
    this.activeTasks.delete(taskId);
    return true;
  }
};
```

### 5.2 數據變更檢測

為了避免不必要的預覽圖生成，系統需要檢測設備綁定的數據是否有變更。這通過以下方法實現：

```typescript
// 檢查數據變更
class DataChecker {
  // 存儲上次數據的哈希
  private lastDataHashes = new Map<string, string>();
  
  // 獲取需要更新的設備
  async getDevicesNeedingUpdate(deviceIds: string[]): Promise<Device[]> {
    const devices = await fetchDevices(deviceIds);
    const needsUpdate: Device[] = [];
    
    for (const device of devices) {
      // 跳過沒有必要數據的設備
      if (!device.templateId || !device.dataBindings || !device.primaryGatewayId) {
        continue;
      }
      
      // 檢查設備配置
      const deviceConfig = await configManager.getDeviceConfig(device._id);
      if (deviceConfig && deviceConfig.enabled === false) {
        continue;
      }
      
      // 如果設備配置不需要檢查數據變更，則始終更新
      if (deviceConfig && deviceConfig.dataChangeCheck === false) {
        needsUpdate.push(device);
        continue;
      }
      
      // 檢查數據是否有變更
      const currentHash = await this.calculateDataHash(device);
      const lastHash = this.lastDataHashes.get(device._id);
      
      if (lastHash !== currentHash) {
        needsUpdate.push(device);
        this.lastDataHashes.set(device._id, currentHash);
      }
    }
    
    return needsUpdate;
  }
  
  // 計算數據哈希值
  private async calculateDataHash(device: Device): Promise<string> {
    // 獲取綁定數據
    const storeData = await fetchStoreData(device.storeId);
    const filteredData = this.filterRelevantData(storeData, device.dataBindings);
    
    // 計算哈希
    return crypto.createHash('md5')
      .update(JSON.stringify(filteredData))
      .digest('hex');
  }
  
  // 過濾與設備相關的數據
  private filterRelevantData(storeData: any[], dataBindings: any): any {
    // 從所有門店數據中提取設備綁定使用的數據
    const relevantData = {};
    
    for (const binding of Object.values(dataBindings)) {
      const { dataField, dataIndex } = binding as any;
      if (dataField && dataIndex !== undefined && storeData[dataIndex]) {
        if (!relevantData[dataIndex]) {
          relevantData[dataIndex] = {};
        }
        relevantData[dataIndex][dataField] = storeData[dataIndex][dataField];
      }
    }
    
    return relevantData;
  }
}
```

### 5.3 優化策略

為了確保系統穩定運行，實現了以下優化策略：

1. **批量處理**：將大量設備分批處理，避免同時佔用過多系統資源
2. **任務隊列**：使用任務隊列限制並發數量
3. **優先級策略**：支持設備優先級設置
4. **失敗重試**：自動重試失敗的任務
5. **資源釋放**：及時清理臨時DOM元素

```typescript
// 批量處理實現
function createBatches<T>(items: T[], batchSize: number): T[][] {
  const batches: T[][] = [];
  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize));
  }
  return batches;
}

// 任務隊列實現
class TaskQueue {
  private queue: QueueItem[] = [];
  private processing = 0;
  private maxConcurrent: number;
  
  constructor(maxConcurrent = 5) {
    this.maxConcurrent = maxConcurrent;
  }
  
  add<T>(taskFn: () => Promise<T>, priority = 0): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push({
        task: taskFn,
        priority,
        resolve,
        reject
      });
      
      // 按優先級排序
      this.queue.sort((a, b) => b.priority - a.priority);
      
      // 嘗試處理隊列
      this.processQueue();
    });
  }
  
  private async processQueue() {
    if (this.processing >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }
    
    this.processing++;
    const item = this.queue.shift()!;
    
    try {
      const result = await item.task();
      item.resolve(result);
    } catch (error) {
      item.reject(error);
    } finally {
      this.processing--;
      this.processQueue();
    }
  }
}
```

## 6. 最佳實踐與建議

### 6.1 系統監控

1. 添加詳細的日誌記錄，包括任務執行時間、結果和錯誤信息
2. 設置監控儀表板，實時顯示系統運行狀態
3. 實現告警機制，在任務失敗率超過閾值時發送通知

### 6.2 性能調優

1. 根據服務器資源和設備數量調整批處理大小
2. 在非高峰時段調度大批量更新任務
3. 使用數據緩存減少數據庫查詢次數
4. 考慮使用工作線程池進行預覽圖渲染

### 6.3 容錯處理

1. 對於暫時無法連接的網關，實現後台重試機制
2. 將失敗的任務記錄到專門的失敗隊列，便於後續手動處理
3. 實現系統自動恢復機制，在系統崩潰後能夠自動恢復任務狀態

### 6.4 安全性考慮

1. 實現適當的訪問控制，限制定時任務API的訪問權限
2. 對發送的預覽圖數據進行加密
3. 實施速率限制，防止惡意調用API

## 7. API 設計

### 7.1 REST API

```
# 獲取自動更新任務列表
GET /api/auto-update/tasks

# 創建新的自動更新任務
POST /api/auto-update/tasks

# 獲取特定任務詳情
GET /api/auto-update/tasks/:taskId

# 更新任務配置
PUT /api/auto-update/tasks/:taskId

# 刪除任務
DELETE /api/auto-update/tasks/:taskId

# 立即執行任務
POST /api/auto-update/tasks/:taskId/execute

# 暫停任務
PATCH /api/auto-update/tasks/:taskId/pause

# 恢復任務
PATCH /api/auto-update/tasks/:taskId/resume
```

### 7.2 任務配置示例

```json
{
  "name": "Daily Morning Update",
  "description": "每天早上更新所有設備預覽圖",
  "scheduleType": "cron",
  "cronExpression": "0 0 8 * * *",
  "devices": {
    "type": "query",
    "query": { "storeId": "store123" }
  },
  "options": {
    "batchSize": 20,
    "dataChangeCheck": true,
    "retryAttempts": 5
  }
}
```

## 8. 微服務/API 方案實現步驟

1. **第一階段**：前端預覽服務 API 實現
   - 創建獨立的 Express 服務
   - 實現預覽圖生成 API 端點
   - 集成 React SSR 環境
   - 適配前端 PreviewComponent 和 crypto 模組

2. **第二階段**：後端定時系統實現
   - 實現核心調度系統
   - 創建 API 客戶端模塊
   - 實現數據變更檢測
   - 添加失敗重試機制

3. **第三階段**：配置管理與監控
   - 創建配置管理系統
   - 實現設備級別的更新策略
   - 添加詳細日誌記錄
   - 實現管理界面

4. **第四階段**：性能優化與系統整合
   - 優化預覽服務的性能
   - 添加分佈式部署支持
   - 集成到現有系統中
   - 全面測試和部署

## 9. 微服務/API 方案優勢

1. **職責分離**：前端專注於渲染預覽圖，後端負責調度和發送
2. **低耦合**：各組件間通過標準 API 通信，易於維護和更新
3. **可擴展性**：可以根據需求獨立擴展前端預覽服務或後端任務系統
4. **可靠性高**：一個組件的臨時故障不會影響整個系統
5. **開發效率**：可以利用現有的前端組件，無需重寫
6. **靈活部署**：可以在不同的服務器上部署前後端服務

## 10. 可能的挑戰與解決方案

1. **網絡延遲問題**
   - 解決方案：將預覽服務部署在與後端相同的網絡環境中
   - 優化 API 響應大小，考慮圖像壓縮

2. **服務可靠性**
   - 解決方案：實現服務健康檢查
   - 添加負載均衡和自動擴展
   - 設置失敗告警機制

3. **安全性考慮**
   - 解決方案：實現強認證和授權
   - 限制 API 訪問範圍
   - 加密敏感數據傳輸

4. **開發和測試挑戰**
   - 解決方案：創建完整的測試環境
   - 使用 Docker 容器化服務
   - 實現自動化測試流程
