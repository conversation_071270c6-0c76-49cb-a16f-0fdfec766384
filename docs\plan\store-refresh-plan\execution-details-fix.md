# 執行詳情查看功能修復

## 問題分析

用戶點擊"查看詳情"按鈕沒有反應，錯誤信息顯示：
```
GET http://*************:3001/api/stores/KH001/refresh-plans/executions/undefined 400 (Bad Request)
```

**根本原因：**
- 前端代碼使用 `execution.id` 獲取執行記錄ID
- 但MongoDB返回的數據使用 `_id` 字段
- 導致傳遞給API的ID為 `undefined`

## 修復內容

### 1. 修正ID字段映射
```typescript
// 修復前
const response = await refreshPlanApi.getExecutionDetail(store.id, execution.id);

// 修復後
const executionId = execution._id || execution.id;
const response = await refreshPlanApi.getExecutionDetail(store.id, executionId);
```

### 2. 更新數據類型定義
```typescript
recentExecutions: Array<{
  _id?: string;        // 添加 MongoDB _id 字段
  id?: string;         // 保留原有 id 字段
  // ... 其他字段
  result?: any;        // 添加 result 字段支持網關統計
}>
```

### 3. 修正表格key屬性
```typescript
// 修復前
<tr key={execution.id} className="hover:bg-gray-50">

// 修復後  
<tr key={execution._id || execution.id} className="hover:bg-gray-50">
```

### 4. 增強執行記錄數據映射
```typescript
recentExecutions: executionsResponse.success ? executionsResponse.data.executions.map((exec: any) => ({
  ...exec,
  // 確保有正確的字段映射
  totalDevices: exec.result?.totalDevices || 0,
  successDevices: exec.result?.successDevices || 0,
  failedDevices: exec.result?.failedDevices || 0,
  duration: exec.result?.processingTime ? Math.round(exec.result.processingTime / 1000) : 0,
  // 將 completed 狀態映射為 success
  status: exec.status === 'completed' ? 'success' : exec.status
})) : []
```

### 5. 添加調試和錯誤處理
- 添加 console.log 輸出執行記錄數據
- 添加 ID 字段檢查和錯誤提示
- 添加詳細的錯誤信息顯示

## 測試步驟

1. **檢查執行記錄數據結構**
   - 打開瀏覽器開發者工具
   - 查看 console.log 輸出的執行記錄數據
   - 確認 `_id` 字段存在

2. **測試查看詳情功能**
   - 點擊"查看詳情"按鈕
   - 確認沒有 undefined ID 錯誤
   - 確認執行詳情模態框正常打開

3. **驗證數據顯示**
   - 檢查執行概要信息
   - 檢查設備統計數據
   - 檢查網關統計信息（如果有）
   - 檢查設備詳細結果

## 預期結果

修復後應該能夠：
- ✅ 正常點擊"查看詳情"按鈕
- ✅ 成功獲取執行記錄詳情
- ✅ 顯示執行詳情模態框
- ✅ 展示完整的統計信息

## 如果仍有問題

如果修復後仍有問題，請檢查：

1. **後端執行記錄是否存在**
   - 確認數據庫中有執行記錄
   - 確認執行記錄包含必要字段

2. **API路由是否正確**
   - 檢查後端路由配置
   - 確認權限設置正確

3. **數據結構是否匹配**
   - 檢查執行記錄的實際數據結構
   - 確認前端類型定義匹配

## 調試信息

修復後會在瀏覽器控制台看到以下調試信息：
- "執行記錄響應:" - 顯示API返回的執行記錄列表
- "執行記錄數據:" - 顯示點擊的執行記錄數據
- "正在獲取執行詳情，ID:" - 顯示使用的執行記錄ID
- "執行詳情響應:" - 顯示執行詳情API響應

這些信息有助於進一步診斷問題。
