# EPD Manager 正確的 Docker 快速開始指南

## 🎯 **重要說明**

EPD Manager 是一個**統一專案**，使用 `npm run dev` 同時啟動前後端。
Docker 化應該保持這個原始架構，而不是分離成獨立容器。

## 📋 **需要創建的文件**

### 1. Dockerfile (根目錄)
```dockerfile
FROM node:18-alpine

# 安裝系統依賴 (Canvas 和 Puppeteer 需要)
RUN apk add --no-cache \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    giflib-dev \
    librsvg-dev \
    pixman-dev \
    pkgconfig \
    make \
    g++ \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates

# 設置 Puppeteer 環境變數
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

WORKDIR /app

# 複製根目錄的 package.json (前端)
COPY package*.json ./
RUN npm ci

# 複製後端的 package.json
COPY server/package*.json ./server/
RUN cd server && npm ci

# 複製所有源碼
COPY . .

# 創建必要目錄
RUN mkdir -p server/logs server/uploads

# 暴露端口 (前端 5173, 後端 3001)
EXPOSE 5173 3001

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3001/api/health || exit 1

# 使用原始的開發命令
CMD ["npm", "run", "dev"]
```

### 2. docker-compose.yml (更新現有文件)
```yaml
version: '3.8'

services:
  # EPD Manager 統一服務 (前端 + 後端)
  epd-manager:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: epd-manager
    restart: unless-stopped
    ports:
      # 注意：容器內端口固定，避免程式碼調用錯誤
      - "${FRONTEND_PORT:-5173}:5173"  # 外部:內部 (內部固定 5173)
      - "${SERVER_PORT:-3001}:3001"    # 外部:內部 (內部固定 3001)
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - MONGO_URI=mongodb://mongodb:27017
      - MONGO_DB=${MONGO_DB:-resourceManagement}
      - CORS_ORIGIN=${CORS_ORIGIN:-*}
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - server_uploads:/app/server/uploads
      - server_logs:/app/server/logs
    depends_on:
      mongodb:
        condition: service_started
    networks:
      - epd-network

  # MongoDB 資料庫
  mongodb:
    image: mongo:7-jammy
    container_name: epd-manager-mongodb
    restart: unless-stopped
    ports:
      - "${MONGO_PORT:-27017}:27017"
    environment:
      - MONGO_INITDB_DATABASE=${MONGO_DB:-resourceManagement}
    volumes:
      - mongo_data:/data/db
    command: mongod --wiredTigerCacheSizeGB 1.5 --bind_ip_all
    networks:
      - epd-network

volumes:
  mongo_data:
  server_uploads:
  server_logs:

networks:
  epd-network:
    driver: bridge
```

### 3. .env (環境變數)
```env
# === 必須設置 ===
JWT_SECRET=your-super-secret-jwt-key-change-this

# === 端口配置 ===
# 注意：這些是外部訪問端口，容器內部端口固定
FRONTEND_PORT=5173  # 外部訪問前端的端口
SERVER_PORT=3001    # 外部訪問後端的端口
MONGO_PORT=27017    # 外部訪問 MongoDB 的端口

# === 基本配置 ===
NODE_ENV=development
MONGO_DB=resourceManagement

# === 重要提醒 ===
# 程式碼中硬編碼了端口 3001 和 5173
# 如需修改，請同時更新程式碼中的端口配置
```

### 4. .dockerignore (根目錄)
```
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.env.*
.vscode
.idea
*.log
.DS_Store
Thumbs.db
server/logs/*
server/uploads/*
```

## 🚀 **部署步驟**

### 1. 準備環境
```bash
# 確保在專案根目錄
cd epd-manager-lite

# 創建環境變數文件
cp .env.example .env
# 編輯 .env 設置 JWT_SECRET
```

### 2. 構建和啟動
```bash
# 構建服務
docker-compose build

# 啟動服務 (後台運行)
docker-compose up -d

# 查看服務狀態
docker-compose ps

# 查看日誌
docker-compose logs -f epd-manager
```

### 3. 驗證部署
```bash
# 檢查前端 (應該看到 React 應用)
curl http://localhost:5173

# 檢查後端 API
curl http://localhost:3001/api/health

# 檢查 MongoDB
docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')"
```

### 4. 外部訪問測試
```bash
# 檢測本機 IP
SERVER_IP=$(hostname -I | awk '{print $1}')
echo "服務器 IP: $SERVER_IP"

# 測試外部訪問
curl http://${SERVER_IP}:5173
curl http://${SERVER_IP}:3001/api/health

# 在其他設備上測試
# 瀏覽器訪問: http://*************:5173
# API 測試: http://*************:3001/api/health
```

## 🔧 **常用管理命令**

### 服務管理
```bash
# 啟動服務
docker-compose up -d

# 停止服務
docker-compose down

# 重啟服務
docker-compose restart epd-manager

# 查看服務狀態
docker-compose ps

# 查看資源使用
docker stats epd-manager
```

### 日誌和除錯
```bash
# 查看所有日誌
docker-compose logs

# 查看 EPD Manager 日誌
docker-compose logs -f epd-manager

# 進入容器除錯
docker-compose exec epd-manager sh

# 查看容器內進程
docker-compose exec epd-manager ps aux
```

### 資料管理
```bash
# 備份 MongoDB
docker-compose exec mongodb mongodump --out /tmp/backup
docker cp epd-manager-mongodb:/tmp/backup ./backup

# 清理未使用資源
docker system prune

# 重建服務 (清除快取)
docker-compose build --no-cache
docker-compose up -d
```

## 🎯 **與原始開發流程的對比**

### 本地開發 (原始方式)
```bash
npm run dev  # 同時啟動前後端
```

### Docker 開發 (容器化)
```bash
docker-compose up -d  # 同時啟動前後端 (在容器中)
```

### 開發體驗
- ✅ 保持原始的 `npm run dev` 命令
- ✅ 前後端在同一個容器中
- ✅ 同時暴露 5173 和 3001 端口
- ✅ 支援外部設備訪問
- ✅ 熱重載功能正常 (如果配置了 volume 映射)

## 🔄 **開發模式配置 (可選)**

如果需要在 Docker 中進行開發 (代碼修改後自動重載)：

### docker-compose.override.yml
```yaml
version: '3.8'

services:
  epd-manager:
    volumes:
      # 映射源碼目錄 (開發模式)
      - .:/app
      - /app/node_modules
      - /app/server/node_modules
    environment:
      - NODE_ENV=development
```

### 使用開發模式
```bash
# 自動載入 docker-compose.override.yml
docker-compose up -d

# 代碼修改後會自動重載
```

## 📋 **故障排除**

### 常見問題

#### 1. 容器無法啟動
```bash
# 查看詳細錯誤
docker-compose logs epd-manager

# 檢查端口衝突
netstat -tulpn | grep :5173
netstat -tulpn | grep :3001
```

#### 2. 外部訪問失敗
```bash
# 檢查防火牆設置
sudo ufw status

# 檢查 CORS 配置
docker-compose exec epd-manager env | grep CORS
```

#### 3. MongoDB 連接失敗
```bash
# 檢查 MongoDB 狀態
docker-compose exec mongodb mongosh --eval "db.runCommand({ping: 1})"

# 檢查網路連接
docker-compose exec epd-manager ping mongodb
```

## 🎯 **成功標準**

- [ ] 容器正常啟動
- [ ] 前端可訪問 (http://localhost:5173)
- [ ] 後端 API 正常 (http://localhost:3001/api/health)
- [ ] MongoDB 連接正常
- [ ] 外部設備可以訪問 (http://*************:5173)
- [ ] 移動應用可以連接 API
- [ ] Gateway 設備可以建立 WebSocket 連接

這個方案完全保持了原始架構的簡潔性，同時提供了 Docker 部署的便利性。
