import { BaseConverter } from './BaseConverter';
import { PixelData } from '../types';
import { DisplayColorType } from '../../../types';
import { getColorPalette, findExactColorMatch } from '../utils/colorPalettes';

/**
 * BWRY 轉換器
 * 2bit per pixel, 寬度需為4的倍數
 */
export class BWRYConverter extends BaseConverter {
  private index: number = 0;

  calculatePaddedWidth(): number {
    // 寬度必須是4的倍數 (2bit per pixel)
    return Math.ceil(this.width / 4) * 4;
  }

  calculateBufferSize(): number {
    // 每4個像素佔用1個字節
    return Math.ceil((this.paddedWidth * this.height) / 4);
  }

  protected getBytesPerPixel(): number {
    return 0.25; // 2bit per pixel
  }

  processPixel(x: number, _y: number, pixel: PixelData): void {
    const colorValue = this.analyzeColorBWRY(pixel);
    const pixelInByte = x & 3; // 每字節4個像素
    const shift = (3 - pixelInByte) * 2; // 2bit per pixel

    this.buffer[this.index] |= (colorValue << shift);

    // 當處理完一個字節或到達行尾時，移動到下一個字節
    if (pixelInByte === 3 || (x + 1) === this.paddedWidth) {
      this.index++;
    }
  }

  private analyzeColorBWRY(pixel: PixelData): number {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BWRY);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 根據匹配的顏色返回對應的數值：0=黑, 1=白, 2=黃, 3=紅 (修正紅黃對應關係)
    if (matchedColor.r === 0 && matchedColor.g === 0 && matchedColor.b === 0) {
      return 0; // 黑色
    } else if (matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 255) {
      return 1; // 白色
    } else if (matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 0) {
      return 2; // 黃色 (修正：原本是3，現在改為2)
    } else if (matchedColor.r === 255 && matchedColor.g === 0 && matchedColor.b === 0) {
      return 3; // 紅色 (修正：原本是2，現在改為3)
    }

    // 備用邏輯（不應該到達這裡）
    console.warn(`BWRY: 未預期的顏色匹配結果: RGB(${matchedColor.r}, ${matchedColor.g}, ${matchedColor.b})`);
    return 0; // 默認黑色
  }

  getPixelData(): Uint8Array {
    return this.buffer;
  }
}
