// tests/helpers/mockDb.js
/**
 * 模擬資料庫連接輔助模組
 */

// 創建模擬的資料庫集合
const createMockCollection = (initialData = []) => {
  const data = [...initialData];
  
  return {
    find: jest.fn().mockImplementation(query => ({
      toArray: jest.fn().mockResolvedValue(
        query ? data.filter(item => {
          return Object.entries(query).every(([key, value]) => item[key] === value);
        }) : data
      )
    })),
    findOne: jest.fn().mockImplementation(query => {
      const item = data.find(item => {
        return Object.entries(query).every(([key, value]) => item[key] === value);
      });
      return Promise.resolve(item || null);
    }),
    insertOne: jest.fn().mockImplementation(doc => {
      const newId = Math.random().toString(36).substring(2, 15);
      const newDoc = { _id: newId, ...doc };
      data.push(newDoc);
      return Promise.resolve({ insertedId: newId });
    }),
    updateOne: jest.fn().mockImplementation((query, update) => {
      const index = data.findIndex(item => {
        return Object.entries(query).every(([key, value]) => item[key] === value);
      });
      
      if (index !== -1) {
        if (update.$set) {
          data[index] = { ...data[index], ...update.$set };
        }
        return Promise.resolve({ modifiedCount: 1 });
      }
      
      return Promise.resolve({ modifiedCount: 0 });
    }),
    deleteOne: jest.fn().mockImplementation(query => {
      const index = data.findIndex(item => {
        return Object.entries(query).every(([key, value]) => item[key] === value);
      });
      
      if (index !== -1) {
        data.splice(index, 1);
        return Promise.resolve({ deletedCount: 1 });
      }
      
      return Promise.resolve({ deletedCount: 0 });
    }),
    // 額外添加一個方法用於測試期間檢查集合數據
    _getData: () => data
  };
};

// 創建模擬的資料庫連接
const createMockDbConnection = (collections = {}) => {
  const mockClient = { close: jest.fn() };
  const mockDb = { 
    collection: jest.fn().mockImplementation(name => {
      if (!collections[name]) {
        collections[name] = createMockCollection();
      }
      return collections[name];
    })
  };
  
  return jest.fn().mockResolvedValue({
    client: mockClient,
    db: mockDb
  });
};

module.exports = {
  createMockCollection,
  createMockDbConnection
};
