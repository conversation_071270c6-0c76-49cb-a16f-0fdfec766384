#!/bin/bash

# EPD Manager 部署工具 (Linux/macOS)
echo "========================================"
echo "EPD Manager 部署工具 (Linux/macOS)"
echo "========================================"
echo

# 檢查Docker是否安裝
if ! command -v docker &> /dev/null; then
    echo "[錯誤] Docker 未安裝，請先安裝 Docker"
    echo "安裝指南: https://docs.docker.com/engine/install/"
    exit 1
fi

# 檢查Docker是否運行
if ! docker info &> /dev/null; then
    echo "[錯誤] Docker 未運行，請先啟動 Docker 服務"
    echo "Ubuntu/Debian: sudo systemctl start docker"
    echo "CentOS/RHEL: sudo systemctl start docker"
    exit 1
fi

# 檢查docker-compose是否安裝
if ! command -v docker-compose &> /dev/null; then
    echo "[錯誤] docker-compose 未安裝，請先安裝 docker-compose"
    echo "安裝指南: https://docs.docker.com/compose/install/"
    exit 1
fi

# 檢查必要檔案
if [ ! -f "epd-manager-all.tar" ]; then
    echo "[錯誤] 找不到 epd-manager-all.tar 鏡像檔案"
    exit 1
fi

if [ ! -f "docker-compose.yml" ]; then
    echo "[錯誤] 找不到 docker-compose.yml 檔案"
    exit 1
fi

# 檢查.env檔案
if [ ! -f ".env" ]; then
    echo "[警告] 找不到 .env 檔案"
    if [ -f ".env.example" ]; then
        echo "[信息] 正在從 .env.example 建立 .env 檔案..."
        cp ".env.example" ".env"
        echo "[重要] 請編輯 .env 檔案並設置正確的 JWT_SECRET"
        echo "[重要] 完成後請重新執行此腳本"
        echo "編輯命令: nano .env 或 vim .env"
        exit 1
    else
        echo "[錯誤] 找不到 .env.example 檔案"
        exit 1
    fi
fi

echo "[信息] 正在載入 Docker 鏡像..."
echo "載入所有鏡像 (EPD Manager + MongoDB)..."
docker load -i epd-manager-all.tar
if [ $? -ne 0 ]; then
    echo "[錯誤] 鏡像載入失敗"
    exit 1
fi

echo
echo "[信息] 正在停止現有容器..."
docker-compose down

echo "[信息] 正在啟動 EPD Manager..."
docker-compose up -d

if [ $? -ne 0 ]; then
    echo "[錯誤] 啟動失敗"
    exit 1
fi

echo
echo "[成功] EPD Manager 已成功部署！"
echo
echo "服務狀態:"
docker-compose ps

# 獲取系統IP地址
# 嘗試使用不同的命令來獲取IP，以兼容不同的Linux發行版
IP_ADDRESS=$(ip -4 addr show scope global | grep -oP '(?<=inet\s)\d+(\.\d+){3}' | head -n1)

# 如果上面的命令失敗，嘗試其他方法
if [ -z "$IP_ADDRESS" ]; then
    IP_ADDRESS=$(hostname -I | awk '{print $1}')
fi

# 如果仍然無法獲取IP，使用localhost
if [ -z "$IP_ADDRESS" ]; then
    IP_ADDRESS="localhost"
    echo "[警告] 無法獲取本機IP地址，使用預設值 'localhost'"
fi

echo
echo "訪問地址:"
echo "  前端: http://$IP_ADDRESS:5173"
echo "  後端: http://$IP_ADDRESS:3001"
echo
echo "[重要] 首次訪問設置:"
echo "  1. 開啟瀏覽器訪問 http://$IP_ADDRESS:5173"
echo "  2. 系統會自動顯示初始化頁面"
echo "  3. 設置管理員帳號和密碼"
echo "  4. 完成初始化後即可正常使用"
echo
echo "[提示] 請設置強密碼以確保安全"
echo

# 顯示日誌
echo "如需查看日誌，請執行:"
echo "  docker-compose logs -f"
echo