import React from 'react';

/**
 * Button component used for tools in the editor sidebar
 */
export const ToolButton: React.FC<{
  icon: React.ReactNode;
  label: string;
  onClick?: () => void;
  isActive?: boolean;
}> = ({ icon, label, onClick, isActive = false }) => (
  <button 
    className={`flex flex-col items-center justify-center p-2 hover:bg-gray-700 rounded transition-colors ${
      isActive ? 'bg-gray-700' : ''
    }`} 
    onClick={onClick}
  >
    <div className="w-6 h-6 flex items-center justify-center">
      {icon}
    </div>
    <span className="text-xs mt-1 text-center">{label}</span>
  </button>
);