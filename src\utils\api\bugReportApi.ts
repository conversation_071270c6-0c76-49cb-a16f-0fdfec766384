import { BugReport, CreateBugReportRequest, UpdateBugReportRequest, BugReportListResponse, BugReportFilters } from '../../types/bugReport';
import { buildEndpointUrl } from './apiConfig';
import { useAuthStore } from '../../store/authStore';

/**
 * 獲取所有bug回報
 */
export async function getAllBugReports(filters?: BugReportFilters): Promise<BugReportListResponse> {
  try {
    const { token } = useAuthStore.getState();
    
    // 構建查詢參數
    const params = new URLSearchParams();
    if (filters?.status) params.append('status', filters.status);
    if (filters?.priority) params.append('priority', filters.priority);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());
    
    const url = `${buildEndpointUrl('bug-reports')}${params.toString() ? `?${params.toString()}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include',
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('獲取bug回報失敗:', error);
    throw error;
  }
}

/**
 * 創建bug回報
 */
export async function createBugReport(data: CreateBugReportRequest): Promise<BugReport> {
  try {
    const { token } = useAuthStore.getState();
    
    // 使用 FormData 來支持文件上傳
    const formData = new FormData();
    formData.append('title', data.title);
    formData.append('content', data.content);
    if (data.priority) formData.append('priority', data.priority);
    if (data.currentPage) formData.append('currentPage', data.currentPage);
    if (data.image) formData.append('image', data.image);
    
    const response = await fetch(buildEndpointUrl('bug-reports'), {
      method: 'POST',
      headers: {
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: formData,
      credentials: 'include',
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const errorData = await response.json();
      throw new Error(errorData.error || `API error: ${response.status}`);
    }
    
    const result = await response.json();
    return result.bugReport;
  } catch (error) {
    console.error('創建bug回報失敗:', error);
    throw error;
  }
}

/**
 * 更新bug回報
 */
export async function updateBugReport(id: string, data: UpdateBugReportRequest): Promise<void> {
  try {
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('bug-reports', id), {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(data),
      credentials: 'include',
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const errorData = await response.json();
      throw new Error(errorData.error || `API error: ${response.status}`);
    }
  } catch (error) {
    console.error('更新bug回報失敗:', error);
    throw error;
  }
}

/**
 * 刪除bug回報
 */
export async function deleteBugReport(id: string): Promise<void> {
  try {
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('bug-reports', id), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include',
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const errorData = await response.json();
      throw new Error(errorData.error || `API error: ${response.status}`);
    }
  } catch (error) {
    console.error('刪除bug回報失敗:', error);
    throw error;
  }
}

/**
 * 獲取單個bug回報詳情
 */
export async function getBugReport(id: string): Promise<BugReport> {
  try {
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('bug-reports', id), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include',
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('獲取bug回報詳情失敗:', error);
    throw error;
  }
}

/**
 * 導出bug回報為CSV
 */
export async function exportBugReports(): Promise<Blob> {
  try {
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('bug-reports', 'export/csv'), {
      method: 'GET',
      headers: {
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include',
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    
    return await response.blob();
  } catch (error) {
    console.error('導出bug回報失敗:', error);
    throw error;
  }
}

/**
 * 檢查是否啟用開發模式
 */
export function isDevModeEnabled(): boolean {
  // 在開發環境（npm run dev）時預設啟用
  if (import.meta.env.DEV) {
    return true; // 開發環境預設啟用
  }

  // 生產環境預設關閉
  return false;
}

/**
 * 從服務器獲取開發模式狀態（用於生產環境）
 */
export async function getDevModeStatus(): Promise<boolean> {
  try {
    // 先檢查後端 TEST_MODE 設置
    const response = await fetch(buildEndpointUrl('test-mode', 'status'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      console.warn('無法獲取測試模式狀態，預設為關閉');
      return false;
    }

    const data = await response.json();

    // 如果環境變數有被設定，使用設定的值
    if (data.isSet) {
      const enabled = data.enabled || false;
      console.log('測試模式檢查: 環境變數已設定，狀態:', enabled);
      return enabled;
    }

    // 如果環境變數沒有被設定，在開發環境下預設啟用
    if (import.meta.env.DEV) {
      console.log('測試模式檢查: 環境變數未設定，開發環境下預設啟用');
      return true;
    }

    // 生產環境且環境變數未設定，預設關閉
    console.log('測試模式檢查: 環境變數未設定，生產環境下預設關閉');
    return false;
  } catch (error) {
    console.warn('獲取測試模式狀態失敗:', error);
    return false;
  }
}

/**
 * 從服務器獲取懸浮球啟用狀態
 */
export async function getFloatingButtonStatus(): Promise<boolean> {
  try {
    // 先檢查後端 FLOATING_BUTTON 設置
    const response = await fetch(buildEndpointUrl('floating-button', 'status'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      console.warn('無法獲取懸浮球狀態，預設為關閉');
      return false;
    }

    const data = await response.json();

    // 如果環境變數有被設定，使用設定的值
    if (data.isSet) {
      const enabled = data.enabled || false;
      console.log('懸浮球檢查: 環境變數已設定，狀態:', enabled);
      return enabled;
    }

    // 如果環境變數沒有被設定，在開發環境下預設啟用
    if (import.meta.env.DEV) {
      console.log('懸浮球檢查: 環境變數未設定，開發環境下預設啟用');
      return true;
    }

    // 生產環境且環境變數未設定，預設關閉
    console.log('懸浮球檢查: 環境變數未設定，生產環境下預設關閉');
    return false;
  } catch (error) {
    console.warn('獲取懸浮球狀態失敗:', error);
    return false;
  }
}
