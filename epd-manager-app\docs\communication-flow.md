# EPD Manager App 通信流程設計

## 1. 整體通信架構

EPD Manager App 的通信架構包含三個主要部分：

1. **移動應用 (App)**: React Native 開發的移動應用，負責掃描本地網關並將其註冊到服務器
2. **後端服務 (Server)**: 現有的 EPD Manager 後端服務，負責管理網關和設備數據
3. **網關設備 (Gateway)**: 實際的硬件設備，接收 App 發送的 WebSocket 連接信息後與服務器建立連接

通信流程主要分為三個階段：
- **UDP 掃描**: App 通過 UDP 廣播掃描本地網絡中的網關設備
- **HTTP/HTTPS API 通信**: App 與服務器之間的 API 通信，用於用戶認證、網關註冊等
- **HTTP/TCP 配置**: App 將服務器返回的 WebSocket 連接信息發送給網關設備

## 2. 通信流程圖

### 2.1 整體通信架構

```mermaid
graph TD
    subgraph "移動應用"
        AppUI[用戶界面]
        AppState[應用狀態]
        APIClient[API 客戶端]
        UDPScanner[UDP 掃描器]
        GatewayConfig[網關配置器]
    end

    subgraph "後端服務"
        APIServer[API 服務]
        WSServer[WebSocket 服務]
        DB[數據庫]
    end

    subgraph "網關設備"
        Gateway[網關]
        EPD[電子紙顯示器]
    end

    AppUI <--> AppState
    AppState <--> APIClient
    AppState <--> UDPScanner
    AppState <--> GatewayConfig

    UDPScanner <--> |UDP 廣播| Gateway
    APIClient <--> |HTTP/HTTPS| APIServer
    GatewayConfig <--> |HTTP/TCP| Gateway

    APIServer <--> DB
    WSServer <--> DB

    Gateway <--> |WebSocket| WSServer
    Gateway <--> EPD
```

### 2.2 用戶認證流程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant Server as 後端服務
    participant DB as 數據庫

    User->>App: 輸入用戶名和密碼
    App->>Server: POST /api/auth/login
    Server->>DB: 驗證用戶憑證
    DB-->>Server: 返回用戶信息
    Server-->>App: 返回 JWT Token
    App->>App: 存儲 Token
    App-->>User: 顯示登入成功
```

### 2.3 網關掃描與註冊流程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant Gateway as 網關設備
    participant Server as 後端服務
    participant DB as 數據庫

    User->>App: 選擇門店
    App->>Server: GET /api/stores
    Server-->>App: 返回門店列表

    User->>App: 點擊掃描網關按鈕
    App->>Gateway: 發送 UDP 廣播掃描消息
    Gateway-->>App: 回應掃描消息（含 MAC 地址等信息）
    App-->>User: 顯示發現的網關列表

    User->>App: 選擇要註冊的網關
    App->>App: 填寫網關名稱等信息
    App->>Server: POST /api/gateways（含網關信息和門店 ID）
    Server->>DB: 創建網關記錄
    Server->>Server: 生成 WebSocket 連接信息
    DB-->>Server: 返回創建結果
    Server-->>App: 返回網關信息（含 WebSocket 連接信息）
    App-->>User: 顯示網關註冊成功

    User->>App: 確認將連接信息發送給網關
    App->>Gateway: 發送 WebSocket 連接信息
    Gateway-->>App: 確認接收
    App-->>User: 顯示配置成功
```

### 2.4 網關配置流程

```mermaid
sequenceDiagram
    participant App as 移動應用
    participant Gateway as 網關設備
    participant Server as 後端服務

    Note over App,Gateway: App 已獲取 WebSocket 連接信息
    App->>Gateway: 發送 WebSocket 連接信息（HTTP/TCP）
    Gateway-->>App: 確認接收

    Gateway->>Server: 建立 WebSocket 連接（含 Token）
    Server->>Server: 驗證 Token
    Server-->>Gateway: 連接確認

    loop 心跳檢測
        Gateway->>Server: 發送 ping 消息
        Server-->>Gateway: 返回 pong 消息
    end

    loop 設備狀態報告
        Gateway->>Server: 發送設備狀態消息
        Server->>Server: 更新設備狀態
        Server-->>Gateway: 確認接收
    end

    Note over Gateway,Server: 網關信息報告
    Gateway->>Server: 發送網關信息消息
    Server->>Server: 更新網關信息
    Server-->>Gateway: 確認接收
```

### 2.5 設備管理流程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant Server as 後端服務
    participant Gateway as 網關設備

    User->>App: 添加設備
    App->>App: 更新本地設備列表
    App->>Server: 通過 WebSocket 發送更新的設備列表
    Server->>DB: 更新設備記錄
    Server-->>App: 確認設備添加

    User->>App: 移除設備
    App->>App: 更新本地設備列表
    App->>Server: 通過 WebSocket 發送更新的設備列表
    Server->>DB: 更新設備記錄
    Server-->>App: 確認設備移除
```

### 2.6 圖像請求流程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant Server as 後端服務
    participant Gateway as 網關設備
    participant EPD as 電子紙顯示器

    User->>App: 請求設備預覽圖像
    App->>Server: 通過 WebSocket 發送圖像請求
    Server->>Gateway: 轉發圖像請求
    Gateway->>EPD: 獲取當前顯示內容
    EPD-->>Gateway: 返回圖像數據
    Gateway-->>Server: 發送圖像數據
    Server-->>App: 轉發圖像數據
    App->>App: 處理並顯示圖像
    App-->>User: 顯示預覽圖像
```

## 3. 消息格式

### 3.1 WebSocket 消息格式

所有 WebSocket 消息使用 JSON 格式，基本結構如下：

```json
{
  "type": "消息類型",
  "timestamp": 1683270664000,
  "data": { /* 消息內容 */ }
}
```

### 3.2 主要消息類型

#### 3.2.1 心跳消息

```json
// 發送
{
  "type": "ping",
  "timestamp": 1683270664000
}

// 接收
{
  "type": "pong",
  "timestamp": 1683270664500
}
```

#### 3.2.2 設備狀態消息

```json
{
  "type": "deviceStatus",
  "timestamp": 1683270664000,
  "devices": [
    {
      "macAddress": "AA:BB:CC:DD:EE:FF",
      "status": "online",
      "dataId": "TEST001",
      "data": {
        "size": "2.9\"",
        "battery": 85,
        "rssi": -65
      }
    }
  ]
}
```

#### 3.2.3 網關信息消息

```json
{
  "type": "gatewayInfo",
  "timestamp": 1683270664000,
  "info": {
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "name": "Test Gateway",
    "model": "Gateway Model XYZ",
    "wifiFirmwareVersion": "1.0.0",
    "btFirmwareVersion": "2.0.0",
    "ipAddress": "*************"
  }
}
```

#### 3.2.4 圖像請求消息

```json
// 請求
{
  "type": "requestPreviewImage",
  "timestamp": 1683270664000,
  "macAddress": "AA:BB:CC:DD:EE:FF"
}

// 回應
{
  "type": "preview",
  "timestamp": 1683270665000,
  "deviceMac": "AA:BB:CC:DD:EE:FF",
  "imageData": "base64編碼的圖像數據..."
}
```

## 4. 錯誤處理

### 4.1 網絡錯誤處理

- **API 請求錯誤**: 使用標準 HTTP 狀態碼，應用內顯示友好錯誤消息
- **WebSocket 連接錯誤**: 自動重連機制，指數退避算法
- **離線模式**: 檢測網絡狀態，在離線時緩存操作，恢復連接後同步

### 4.2 業務邏輯錯誤處理

- **認證錯誤**: 提示用戶重新登入
- **權限錯誤**: 顯示權限不足提示
- **數據錯誤**: 提供重試和手動刷新選項

## 5. 安全考慮

- **API 認證**: 使用 JWT Token 進行認證
- **WebSocket 認證**: 使用專門的 WebSocket Token
- **數據加密**: 敏感數據使用 HTTPS 傳輸
- **本地存儲安全**: 敏感信息使用加密存儲

## 6. 性能優化

- **批量處理**: 設備狀態批量上報
- **消息壓縮**: 大型消息（如圖像數據）進行壓縮
- **連接保活**: 心跳機制確保連接活躍
- **資源釋放**: 不需要的連接及時關閉
