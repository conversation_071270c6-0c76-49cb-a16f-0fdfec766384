// filepath: c:\Users\<USER>\Desktop\code\git\epd-manager\src\utils\api\apiConfig.ts
/**
 * API 配置文件
 * 集中管理後端 API 連接配置
 */

/**
 * 自動檢測服務器主機地址
 * 如果當前頁面不是從 localhost 訪問，則使用當前頁面的主機名
 */
function getServerHost(): string {
  // 檢查是否有環境變數或 URL 參數指定的服務器地址
  const urlParams = new URLSearchParams(window.location.search);
  const serverHost = urlParams.get('server');

  if (serverHost) {
    console.log(`使用 URL 參數指定的服務器地址: ${serverHost}`);
    return serverHost;
  }

  // 如果當前頁面不是從 localhost 訪問，使用當前主機名
  if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
    console.log(`使用當前頁面主機名作為服務器地址: ${window.location.hostname}`);
    return window.location.hostname;
  }

  // 默認使用 localhost
  return 'localhost';
}

// 後端服務器配置
export const API_CONFIG = {
  // 後端服務器 IP（動態檢測）
  host: getServerHost(),

  // 後端服務器埠口
  port: 3001,

  // 是否使用 HTTPS
  useHttps: false,
};

/**
 * 根據配置生成基礎 URL
 * @returns 完整的基礎 URL
 */
export function getBaseUrl(): string {
  const protocol = API_CONFIG.useHttps ? 'https' : 'http';
  return `${protocol}://${API_CONFIG.host}:${API_CONFIG.port}`;
}

/**
 * 構建完整的 API 路徑
 * @param path API 路徑（不含基礎 URL 部分）
 * @returns 完整的 API URL
 */
export function buildApiUrl(path: string): string {
  // 確保路徑以 / 開頭
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  return `${getBaseUrl()}${normalizedPath}`;
}

/**
 * 建構 API 請求路徑
 * @param endpoint API 端點，如 'dataFields'，'storeData' 等
 * @param subPath 子路徑，如 'sync', '123' 等
 * @returns 完整的 API URL
 */
export function buildEndpointUrl(endpoint: string, subPath?: string): string {
  let path = `/api/${endpoint}`;
  if (subPath) {
    // 確保子路徑不以 / 開頭，以避免重複斜杠
    path += `/${subPath.replace(/^\/+/, '')}`;
  }
  return buildApiUrl(path);
}
