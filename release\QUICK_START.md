# EPD Manager 快速開始指南

## 🚀 一鍵部署

### Windows 用戶
```cmd
# 1. 雙擊執行
deploy.bat

# 2. 檢查狀態
check-status.bat

# 3. 停止服務（可選）
stop.bat
```

### Linux/macOS 用戶
```bash
# 1. 執行部署
./deploy.sh

# 2. 檢查狀態
./check-status.sh

# 3. 停止服務（可選）
./stop.sh
```

## 📋 部署前檢查清單

- [ ] 已安裝 Docker 和 Docker Compose
- [ ] 確保端口 5173、3001、27017 未被佔用
- [ ] 系統有至少 4GB 可用記憶體
- [ ] 硬碟有至少 10GB 可用空間

## ⚙️ 首次設置

1. **編輯環境變數**（重要）
   ```bash
   cp .env.example .env
   # 編輯 .env 檔案，修改 JWT_SECRET
   ```

2. **執行部署腳本**
   - Windows: 雙擊 `deploy.bat`
   - Linux/macOS: `./deploy.sh`

3. **訪問系統並初始化**
   - 開啟瀏覽器訪問: http://localhost:5173
   - 按照頁面提示設置管理員帳號和密碼
   - 完成初始化後即可正常使用

## 🔧 常用操作

| 操作 | Windows | Linux/macOS |
|------|---------|-------------|
| 部署 | `deploy.bat` | `./deploy.sh` |
| 檢查狀態 | `check-status.bat` | `./check-status.sh` |
| 停止服務 | `stop.bat` | `./stop.sh` |
| 查看日誌 | `docker-compose logs -f` | `docker-compose logs -f` |
| 重啟服務 | `docker-compose restart` | `docker-compose restart` |

## 🛠️ 故障排除

### 常見問題

1. **端口被佔用**
   - 修改 `.env` 中的端口設定
   - 或停止佔用端口的服務

2. **Docker 未啟動**
   - Windows: 啟動 Docker Desktop
   - Linux: `sudo systemctl start docker`

3. **記憶體不足**
   - 關閉其他應用程式
   - 或調整 MongoDB 記憶體設定

4. **無法訪問服務**
   - 檢查防火牆設定
   - 確認容器狀態：`docker-compose ps`

### 檢查命令

```bash
# 檢查 Docker 狀態
docker --version
docker-compose --version

# 檢查容器狀態
docker-compose ps

# 檢查日誌
docker-compose logs epd-manager
docker-compose logs mongodb

# 檢查資源使用
docker stats
```

## 📞 需要幫助？

1. 查看 `README.md` 獲取詳細說明
2. 執行 `check-status` 腳本診斷問題
3. 查看容器日誌：`docker-compose logs -f`
4. 檢查系統資源是否充足

## 🔒 安全提醒

- ✅ 修改 JWT_SECRET
- ✅ 設置強管理員密碼
- ✅ 設定防火牆規則
- ✅ 定期備份資料
- ✅ 監控系統資源
