import React from 'react';
import { Calendar, Activity, Trash2, Power, PowerOff, Play } from 'lucide-react';
import { RefreshPlan, PlanAction } from '../types/refreshPlan';

interface RefreshPlanCompactCardProps {
  plan: RefreshPlan;
  onAction: (action: PlanAction, planId: string) => void;
  isRunning?: boolean;
}

export const RefreshPlanCompactCard: React.FC<RefreshPlanCompactCardProps> = ({ 
  plan, 
  onAction, 
  isRunning = false 
}) => {
  const getStatusDisplay = (status: RefreshPlan['status']) => {
    const statusConfig = {
      running: { color: 'bg-green-100 text-green-800 border-green-200', text: '運行中', icon: '🟢' },
      active: { color: 'bg-blue-100 text-blue-800 border-blue-200', text: '已啟用', icon: '🔵' },
      inactive: { color: 'bg-gray-100 text-gray-800 border-gray-200', text: '已停用', icon: '⚪' },
      error: { color: 'bg-red-100 text-red-800 border-red-200', text: '錯誤', icon: '🔴' },
    };
    const config = statusConfig[status];
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium border ${config.color}`}>
        <span className="mr-1">{config.icon}</span>
        {config.text}
        {isRunning && status === 'running' && (
          <Activity className="w-3 h-3 ml-1 animate-pulse" />
        )}
      </span>
    );
  };



  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 p-3">
      {/* 頭部：名稱和狀態 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center min-w-0 flex-1">
          <Calendar className="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" />
          <h3 className="text-sm font-semibold text-gray-900 truncate">{plan.name}</h3>
        </div>
        <div className="ml-3 flex-shrink-0">
          {getStatusDisplay(plan.status)}
        </div>
      </div>

      {/* 操作按鈕 */}
      <div className="flex items-center justify-end space-x-1">
        {/* 立即執行按鈕 */}
        <button
          onClick={() => onAction('execute', plan._id)}
          disabled={plan.status === 'running'}
          className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded transition-colors ${
            plan.status === 'running'
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-green-500 text-white hover:bg-green-600'
          }`}
          title={plan.status === 'running' ? '計畫正在執行中' : '立即執行'}
        >
          <Play className="w-3 h-3 mr-1" />
          執行
        </button>

        {/* 啟用/停用按鈕 */}
        <button
          onClick={() => onAction('toggle', plan._id)}
          disabled={plan.status === 'running'}
          className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded transition-colors ${
            plan.status === 'running'
              ? 'text-gray-400 cursor-not-allowed'
              : plan.status === 'active'
              ? 'text-orange-600 hover:text-orange-700 hover:bg-orange-50'
              : 'text-green-600 hover:text-green-700 hover:bg-green-50'
          }`}
          title={
            plan.status === 'running'
              ? '計畫正在執行中'
              : plan.status === 'active'
              ? '停用計畫'
              : '啟用計畫'
          }
        >
          {plan.status === 'active' ? (
            <>
              <PowerOff className="w-3 h-3 mr-1" />
              停用
            </>
          ) : (
            <>
              <Power className="w-3 h-3 mr-1" />
              啟用
            </>
          )}
        </button>

        {/* 刪除按鈕 */}
        <button
          onClick={() => onAction('delete', plan._id)}
          disabled={plan.status === 'running'}
          className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded transition-colors ${
            plan.status === 'running'
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-red-600 hover:text-red-700 hover:bg-red-50'
          }`}
          title={plan.status === 'running' ? '計畫正在執行中，無法刪除' : '刪除計畫'}
        >
          <Trash2 className="w-3 h-3 mr-1" />
          刪除
        </button>
      </div>
    </div>
  );
};

export default RefreshPlanCompactCard;
