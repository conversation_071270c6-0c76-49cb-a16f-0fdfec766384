import { BindingInfo, DataField, DataFieldType, TemplateElement } from "../../types";
import { bindingCore } from "./bindingCore";

/**
 * 文字元件綁定處理器
 * 專門處理文字和多行文字元件的綁定邏輯
 */
export class TextBinding {
  /**
   * 獲取文字元件可綁定的資料欄位
   * @param fields 所有資料欄位
   * @returns 可綁定的資料欄位
   */
  public static getBindableFields(fields: DataField[]): DataField[] {
    // 文字元件僅能綁定 TEXT 和 NUMBER 類型的資料欄位
    return fields.filter(field =>
      field.type === DataFieldType.TEXT ||
      field.type === DataFieldType.NUMBER
    );
  }

  /**
   * 處理文字元件的綁定
   * @param element 文字元件
   * @param fields 所有資料欄位
   * @param sampleData 範例數據（用於預覽）
   * @returns 處理後的文字元件
   */
  public static processBinding(
    element: TemplateElement,
    fields: DataField[],
    sampleData?: Record<string, any>
  ): TemplateElement {
    if (!element.dataBinding || !element.dataBinding.fieldId) {
      return element;
    }

    const { dataBinding } = element;
    const field = fields.find(f => f.id === dataBinding.fieldId);

    if (!field) {
      console.warn(`找不到ID為 ${dataBinding.fieldId} 的資料欄位`);
      return element;
    }

    // 處理數據顯示
    let displayValue = '';
    const showPrefix = dataBinding.displayOptions?.showPrefix ?? false;

    // 如果有範例數據，使用範例數據
    if (sampleData && sampleData[field.id] !== undefined) {
      displayValue = String(sampleData[field.id]);
      if (showPrefix) {
        // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
        displayValue = field.prefix ? `${field.prefix}: ${displayValue}` : `: ${displayValue}`;
      }
    } else {
      // 沒有範例數據時，顯示佔位符
      displayValue = field.name;
      if (showPrefix) {
        // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
        displayValue = field.prefix ? `${field.prefix}: ${displayValue}` : `: ${displayValue}`;
      }
      displayValue = `{{${displayValue}}}`;
    }

    return {
      ...element,
      content: displayValue
    };
  }

  /**
   * 為文字元件設置綁定並進行預處理
   * @param element 文字元件
   * @param dataIndex 數據索引
   * @param fieldId 欄位ID
   * @param showPrefix 是否顯示前綴
   * @param selectedStoreId 預覽門店數據的選擇門店ID
   * @returns 處理後的文字元件
   */
  public static setBinding(
    element: TemplateElement,
    dataIndex: number,
    fieldId: string | null,
    showPrefix: boolean = false,
    selectedStoreId: string | null = null
  ): TemplateElement {
    // 保留現有的 selectedStoreId，如果沒有新值則使用現有值
    const currentStoreId = element.dataBinding?.selectedStoreId;
    const storeId = selectedStoreId !== null ? selectedStoreId : currentStoreId;

    // 建立顯示選項對象
    const displayOptions = { showPrefix };

    // 使用綁定核心建立綁定信息
    const updatedElement = bindingCore.bindElementToField(element, dataIndex, fieldId, displayOptions);

    // 如果有綁定信息且有門店ID，才添加門店ID
    if (updatedElement.dataBinding && (storeId || selectedStoreId === null)) {
      return {
        ...updatedElement,
        dataBinding: {
          ...updatedElement.dataBinding,
          selectedStoreId: storeId
        }
      };
    }

    return updatedElement;
  }

  /**
   * 移除文字元件的資料綁定
   * @param element 文字元件
   * @returns 移除綁定後的元件
   */
  public static removeBinding(element: TemplateElement): TemplateElement {
    return bindingCore.unbindElement(element);
  }

  /**
   * 設置是否顯示前綴
   * @param element 文字元件
   * @param showPrefix 是否顯示前綴
   * @returns 更新後的元件
   */
  public static setShowPrefix(element: TemplateElement, showPrefix: boolean): TemplateElement {
    return bindingCore.updateBindingOptions(element, { showPrefix });
  }
}
