// tests/templateValidatorApi.test.js
const request = require('supertest');
const express = require('express');
const createMockTemplateValidatorApi = require('./helpers/mockTemplateValidator');

describe('模板驗證 API 測試', () => {
  let app;
  let mockValidator;
  let apiRouter;
  beforeEach(() => {
    // 創建 Express 應用
    app = express();

    // 模擬驗證器
    mockValidator = {
      validateTemplate: jest.fn().mockImplementation(template => {
        const errors = [];
        if (!template.id) errors.push('模板缺少 ID');
        if (!template.name) errors.push('模板缺少名稱');
        return { valid: errors.length === 0, errors };
      }),
      calculateChecksum: jest.fn().mockReturnValue('test-checksum-123'),
      validateChecksum: jest.fn().mockImplementation((template, checksum) => checksum === 'test-checksum-123')
    };

    // 創建模擬的模板驗證 API 路由
    const mockApi = createMockTemplateValidatorApi();

    // 設置模擬驗證器
    apiRouter = mockApi.setValidator(mockValidator);

    // 使用模擬路由
    app.use(express.json());
    app.use('/api', apiRouter);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/validate-template', () => {
    test('應該驗證有效的模板', async () => {
      // 模擬驗證成功
      mockValidator.validateTemplate.mockReturnValue({
        valid: true,
        errors: []
      });

      const template = {
        id: '123',
        name: '測試模板',
        content: { elements: [] }
      };

      const response = await request(app)
        .post('/api/validate-template')
        .send(template);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('valid', true);
      expect(response.body.errors).toHaveLength(0);
      expect(mockValidator.validateTemplate).toHaveBeenCalledWith(template);
    });

    test('應該返回無效模板的錯誤', async () => {
      // 模擬驗證失敗
      mockValidator.validateTemplate.mockReturnValue({
        valid: false,
        errors: ['模板缺少必要欄位']
      });

      const invalidTemplate = {
        // 缺少必要欄位
      };

      const response = await request(app)
        .post('/api/validate-template')
        .send(invalidTemplate);

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('valid', false);
      expect(response.body.errors).toContain('模板缺少必要欄位');
    });
  });

  describe('POST /api/calculate-checksum', () => {
    test('應該計算模板的檢查碼', async () => {
      // 模擬檢查碼計算
      mockValidator.calculateChecksum.mockReturnValue('test-checksum-123');

      const template = {
        id: '123',
        name: '測試模板',
        content: { elements: [] }
      };

      const response = await request(app)
        .post('/api/calculate-checksum')
        .send(template);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('checksum', 'test-checksum-123');
      expect(mockValidator.calculateChecksum).toHaveBeenCalledWith(template);
    });

    test('應該處理計算檢查碼時的錯誤', async () => {
      // 模擬錯誤
      mockValidator.calculateChecksum.mockImplementation(() => {
        throw new Error('檢查碼計算錯誤');
      });

      const template = {
        id: '123',
        name: '測試模板'
      };

      const response = await request(app)
        .post('/api/calculate-checksum')
        .send(template);

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('POST /api/validate-checksum', () => {
    test('應該驗證正確的檢查碼', async () => {
      // 模擬檢查碼驗證成功
      mockValidator.validateChecksum.mockReturnValue(true);

      const data = {
        template: {
          id: '123',
          name: '測試模板',
          content: { elements: [] }
        },
        checksum: 'test-checksum-123'
      };

      const response = await request(app)
        .post('/api/validate-checksum')
        .send(data);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('valid', true);
      expect(mockValidator.validateChecksum).toHaveBeenCalledWith(data.template, data.checksum);
    });

    test('應該驗證錯誤的檢查碼', async () => {
      // 模擬檢查碼驗證失敗
      mockValidator.validateChecksum.mockReturnValue(false);

      const data = {
        template: {
          id: '123',
          name: '測試模板',
          content: { elements: [] }
        },
        checksum: 'wrong-checksum'
      };

      const response = await request(app)
        .post('/api/validate-checksum')
        .send(data);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('valid', false);
    });

    test('應該處理請求缺少必要參數的情況', async () => {
      const incompleteData = {
        template: {
          id: '123',
          name: '測試模板'
        }
        // 缺少 checksum
      };

      const response = await request(app)
        .post('/api/validate-checksum')
        .send(incompleteData);

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });
});
