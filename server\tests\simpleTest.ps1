# Simple PowerShell test script
Write-Host "Testing API endpoints" -ForegroundColor Green

# Login to get token
Write-Host "`nLogging in" -ForegroundColor Cyan
try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/auth/login" -Method Post -ContentType "application/json" -Body '{"username":"root","password":"123456789"}' -SessionVariable session -ErrorAction Stop
    Write-Host "Login successful" -ForegroundColor Green
    $token = $loginResponse.token
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
} catch {
    Write-Host "Login failed: $_" -ForegroundColor Red
    exit
}

# Test creating a store
Write-Host "`nTesting create store API" -ForegroundColor Cyan
try {
    $body = @{
        id = "TEST002"
        name = "Test Store"
        address = "Test Address"
        status = "active"
    } | ConvertTo-Json
    $createStoreResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/stores" -Method Post -Headers $headers -Body $body -ErrorAction Stop
    Write-Host "Store created successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to create store: $_" -ForegroundColor Red
}

# Test getting a store
Write-Host "`nTesting get store API" -ForegroundColor Cyan
try {
    $getStoreResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/stores/TEST002" -Method Get -Headers $headers -ErrorAction Stop
    Write-Host "Store retrieved successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to get store: $_" -ForegroundColor Red
}

# Test adding data to store's storeSpecificData array
Write-Host "`nTesting add data to store's storeSpecificData array" -ForegroundColor Cyan
try {
    $dataBody = @{
        storeId = "TEST002"  # This is needed for API to identify which store to update
        id = "API001"
        name = "Test Product"
        price = "300"
        quantity = "5"
    } | ConvertTo-Json
    $createDataResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/storeData" -Method Post -Headers $headers -Body $dataBody -ErrorAction Stop
    Write-Host "Data added to store's storeSpecificData array successfully" -ForegroundColor Green
    $sn = $createDataResponse.sn
    Write-Host "Retrieved SN: $sn" -ForegroundColor Green

    # Store the storeId for later use in queries
    $global:currentStoreId = "TEST002"
} catch {
    Write-Host "Failed to add data to store's storeSpecificData array: $_" -ForegroundColor Red
}

# Test getting store specific data
Write-Host "`nTesting get store specific data API" -ForegroundColor Cyan
try {
    $getDataResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/storeData?storeId=TEST002" -Method Get -Headers $headers -ErrorAction Stop
    Write-Host "Store specific data retrieved successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to get store specific data: $_" -ForegroundColor Red
}

# If we got an SN, test update and delete
if ($sn) {
    # Test updating store specific data
    Write-Host "`nTesting update store specific data API" -ForegroundColor Cyan
    try {
        $updateBody = @{
            price = "400"
        } | ConvertTo-Json
        $updateDataResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/storeData/$sn`?storeId=TEST002" -Method Put -Headers $headers -Body $updateBody -ErrorAction Stop
        Write-Host "Store specific data updated successfully" -ForegroundColor Green
    } catch {
        Write-Host "Failed to update store specific data: $_" -ForegroundColor Red
    }

    # Test deleting store specific data
    Write-Host "`nTesting delete store specific data API" -ForegroundColor Cyan
    try {
        $deleteDataResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/storeData/$sn`?storeId=TEST002" -Method Delete -Headers $headers -ErrorAction Stop
        Write-Host "Store specific data deleted successfully" -ForegroundColor Green
    } catch {
        Write-Host "Failed to delete store specific data: $_" -ForegroundColor Red
    }
}

# Test deleting store
Write-Host "`nTesting delete store API" -ForegroundColor Cyan
try {
    $deleteStoreResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/stores/TEST002" -Method Delete -Headers $headers -ErrorAction Stop
    Write-Host "Store deleted successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to delete store: $_" -ForegroundColor Red
}

Write-Host "`nTesting complete" -ForegroundColor Green
