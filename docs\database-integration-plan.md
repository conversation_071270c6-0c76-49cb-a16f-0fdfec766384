# EPD-Manager 資料庫整合規劃文檔

## 目錄

- [1. 專案概述](#1-專案概述)
- [2. 現有架構](#2-現有架構)
- [3. 整合策略](#3-整合策略)
- [4. 技術實現](#4-技術實現)
- [5. 整合步驟](#5-整合步驟)
- [6. 效能優化](#6-效能優化)
- [7. 維護與監控](#7-維護與監控)
- [8. 部署指南](#8-部署指南)

## 1. 專案概述

EPD-Manager 是一個電子紙顯示器模板管理系統，目前使用 MongoDB 作為主要資料庫。本規劃旨在整合 MySQL 和 Redis，建立一個更完整的資料庫架構。

### 1.1 目標

- 優化資料存儲結構
- 提升系統效能
- 增強資料安全性
- 提供更好的擴展性
- 整合多服務架構

### 1.2 預期效益

- 更高效的資料查詢
- 更好的資料一致性
- 更強的系統可靠性
- 更靈活的擴展能力
- 更完善的服務管理

## 2. 現有架構

### 2.1 技術棧

```yaml
Frontend:
  - React 18.3.1
  - TypeScript 5.5.3
  - Vite 5.4.2
  - Zustand 4.5.2
  - TailwindCSS 3.4.1

Backend:
  - Node.js
  - MongoDB
  - Express.js
```

### 2.2 現有資料結構

```typescript
// 現有主要資料模型
interface Template {
  id: string;
  name: string;
  type: 'label' | 'system';
  screenSize: string;
  elements: TemplateElement[];
  createdAt: Date;
  updatedAt: Date;
  version: number;
}

interface User {
  id: string;
  username: string;
  password: string;
  role: string;
  lastLogin: Date;
  status: 'active' | 'inactive';
}

interface TemplateElement {
  id: string;
  type: string;
  properties: {
    x: number;
    y: number;
    width: number;
    height: number;
    content?: string;
    style?: object;
  };
}
```

## 3. 整合策略

### 3.1 資料庫職責分配

#### MongoDB
- 模板資料
- 資源文件
- 顯示配置
- 歷史版本記錄

#### MySQL
- 用戶管理
- 權限控制
- 審計日誌
- 配額管理
- 系統配置

#### Redis
- 會話管理
- 快取層
- 即時資料
- 任務佇列
- 限流控制

### 3.2 資料庫架構圖

```mermaid
graph TD
    A[EPD-Manager] --> B[資料儲存層]
    B --> C[MongoDB]
    B --> D[MySQL]
    B --> E[Redis]
    
    C --> F[模板數據]
    C --> G[資源文件]
    
    D --> H[用戶管理]
    D --> I[權限系統]
    
    E --> J[快取層]
    E --> K[會話管理]
```

## 4. 技術實現

### 4.1 Docker 配置

```yaml
version: '3.8'
services:
  # 前端應用
  epd-manager-frontend:
    build: 
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=http://epd-manager-api:3001
    depends_on:
      - epd-manager-api

  # 主要後端 API 服務
  epd-manager-api:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - MONGO_URI=mongodb://mongodb:27017/epd
      - MYSQL_HOST=mysql
      - MYSQL_USER=root
      - MYSQL_PASSWORD=password
      - MYSQL_DATABASE=epd
      - REDIS_URL=redis://redis:6379
      - NODE_ENV=production
    depends_on:
      - mongodb
      - mysql
      - redis
    volumes:
      - ./server:/app
      - /app/node_modules

  # 管理者後端服務
  epd-manager-admin-api:
    build:
      context: ./admin-server
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - MONGO_URI=mongodb://mongodb:27017/epd
      - MYSQL_HOST=mysql
      - MYSQL_USER=root
      - MYSQL_PASSWORD=password
      - MYSQL_DATABASE=epd
      - REDIS_URL=redis://redis:6379
      - NODE_ENV=production
    depends_on:
      - mongodb
      - mysql
      - redis
    volumes:
      - ./admin-server:/app
      - /app/node_modules

  # MongoDB 服務
  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    command: mongod --wiredTigerCacheSizeGB 1.5
    environment:
      - MONGO_INITDB_DATABASE=epd

  # MySQL 服務
  mysql:
    image: mysql:8.2
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    environment:
      - MYSQL_DATABASE=epd
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci

  # Redis 服務
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

volumes:
  mongo_data:
  mysql_data:
  redis_data:

networks:
  default:
    driver: bridge
```

### 4.2 資料模型

```typescript
// MySQL 模型
interface MySQLUser {
  id: number;
  username: string;
  password: string;
  role: string;
  email: string;
  status: 'active' | 'inactive';
  lastLoginAt: Date;
  created_at: Date;
  updated_at: Date;
}

interface Permission {
  id: number;
  name: string;
  description: string;
  created_at: Date;
}

interface AuditLog {
  id: number;
  userId: number;
  action: string;
  resourceType: string;
  resourceId: string;
  details: string;
  createdAt: Date;
}

// MongoDB 模型
interface Template {
  _id: ObjectId;
  name: string;
  type: string;
  screenSize: string;
  elements: Element[];
  version: number;
  createdBy: string;
  updatedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Element {
  _id: ObjectId;
  type: string;
  properties: {
    x: number;
    y: number;
    width: number;
    height: number;
    content?: string;
    style?: object;
  };
}

// Redis 結構
interface CacheStructure {
  'user:{id}': string;  // 用戶資料快取
  'template:{id}': string;  // 模板快取
  'session:{id}': string;  // 會話資訊
  'ratelimit:{ip}': string;  // 限流計數器
}
```

### 4.3 連接管理

```typescript
class DatabaseManager {
  private static instance: DatabaseManager;
  private mongoClient: MongoClient;
  private mysqlPool: Pool;
  private redisClient: Redis;

  private constructor() {}

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  async connect() {
    await Promise.all([
      this.connectMongo(),
      this.connectMySQL(),
      this.connectRedis()
    ]);
  }

  private async connectMongo() {
    this.mongoClient = await MongoClient.connect(process.env.MONGO_URI, {
      maxPoolSize: 50,
      minPoolSize: 10,
      retryWrites: true,
      w: 'majority'
    });
  }

  private async connectMySQL() {
    this.mysqlPool = await createPool({
      host: process.env.MYSQL_HOST,
      user: process.env.MYSQL_USER,
      password: process.env.MYSQL_PASSWORD,
      database: process.env.MYSQL_DATABASE,
      connectionLimit: 20,
      queueLimit: 0,
      waitForConnections: true
    });
  }

  private async connectRedis() {
    this.redisClient = await Redis.createClient({
      url: process.env.REDIS_URL,
      maxRetriesPerRequest: 3,
      enableReadyCheck: true,
      reconnectOnError: (err) => {
        const targetError = 'READONLY';
        if (err.message.includes(targetError)) {
          return true;
        }
        return false;
      }
    });
  }

  // 健康檢查方法
  async healthCheck() {
    try {
      await Promise.all([
        this.mongoClient.db().command({ ping: 1 }),
        this.mysqlPool.query('SELECT 1'),
        this.redisClient.ping()
      ]);
      return true;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }
}
```

## 5. 整合步驟

### 5.1 第一階段：環境準備

1. 更新 Docker 配置
2. 建立新的資料庫連接
3. 設置環境變數
4. 準備資料庫遷移腳本

### 5.2 第二階段：資料遷移

```typescript
class DataMigration {
  async migrateUsers() {
    const users = await mongodb.users.find().toArray();
    
    for (const user of users) {
      await mysql.transaction(async (trx) => {
        await trx('users').insert({
          id: user._id.toString(),
          username: user.username,
          password: user.password,
          role: user.role,
          created_at: user.createdAt,
          updated_at: user.updatedAt
        });
      });
    }
  }

  async validateMigration() {
    const mongoCount = await mongodb.users.countDocuments();
    const mysqlCount = await mysql.users.count();
    return mongoCount === mysqlCount;
  }

  async rollback() {
    // 回滾邏輯
  }
}
```

### 5.3 第三階段：服務改造

```typescript
class UserService {
  private db: DatabaseManager;
  private cache: Redis;

  async getUser(id: number) {
    // 快取查詢
    const cached = await this.cache.get(`user:${id}`);
    if (cached) return JSON.parse(cached);

    // 資料庫查詢
    const user = await this.db.mysql.users.findOne(id);
    
    // 更新快取
    if (user) {
      await this.cache.set(
        `user:${id}`, 
        JSON.stringify(user), 
        'EX', 
        3600
      );
    }
    
    return user;
  }

  async updateUser(id: number, data: Partial<User>) {
    // 開啟事務
    await this.db.mysql.transaction(async (trx) => {
      // 更新用戶資料
      await trx('users')
        .where({ id })
        .update(data);
      
      // 記錄審計日誌
      await trx('audit_logs').insert({
        userId: id,
        action: 'UPDATE_USER',
        details: JSON.stringify(data)
      });
    });

    // 清除快取
    await this.cache.del(`user:${id}`);
  }
}
```

## 6. 效能優化

### 6.1 資料庫優化

```typescript
// MongoDB 索引配置
db.templates.createIndex({ name: 1 });
db.templates.createIndex({ "elements.type": 1 });
db.templates.createIndex({ createdAt: 1 });

// MySQL 索引配置
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

// Redis 快取策略
const cacheConfig = {
  template: {
    ttl: 3600,    // 1小時
    maxSize: 1000
  },
  user: {
    ttl: 1800,    // 30分鐘
    maxSize: 500
  }
};
```

### 6.2 連接池配置

```typescript
const poolConfig = {
  mysql: {
    max: 20,
    min: 5,
    idle: 10000
  },
  mongo: {
    maxPoolSize: 50,
    minPoolSize: 10
  },
  redis: {
    maxRetriesPerRequest: 3
  }
};
```

## 7. 維護與監控

### 7.1 監控指標

- 資料庫連接數
- 查詢響應時間
- 快取命中率
- 系統資源使用率
- API 響應時間
- 錯誤率統計

### 7.2 備份策略

```bash
# MongoDB 備份
mongodump --out /backup/mongo/$(date +%Y%m%d)

# MySQL 備份
mysqldump -u root -p epd > /backup/mysql/epd_$(date +%Y%m%d).sql

# Redis 備份
redis-cli SAVE
cp /data/dump.rdb /backup/redis/dump_$(date +%Y%m%d).rdb
```

### 7.3 維護計劃

- 每日：監控日誌檢查
- 每週：效能指標分析
- 每月：資料庫優化
- 每季：架構評估

## 8. 部署指南

### 8.1 前置準備

1. 安裝 Docker 和 Docker Compose
2. 準備環境變數文件
3. 確認網路配置
4. 準備資料備份

### 8.2 部署步驟

1. 克隆代碼庫：
```bash
git clone <repository-url>
cd epd-manager
```

2. 配置環境變數：
```bash
cp .env.example .env
# 編輯 .env 文件設置實際的環境變數
```

3. 啟動服務：
```bash
# 首次啟動
docker-compose up -d

# 查看日誌
docker-compose logs -f

# 停止服務
docker-compose down
```

4. 驗證部署：
```bash
# 檢查服務狀態
docker-compose ps

# 檢查資料庫連接
curl http://localhost:3001/health
```

### 8.3 故障排除

1. 資料庫連接問題：
   - 檢查網絡配置
   - 驗證認證信息
   - 檢查防火牆設置

2. 效能問題：
   - 檢查資源使用情況
   - 分析慢查詢日誌
   - 優化查詢語句

3. 容器問題：
   - 檢查容器日誌
   - 驗證配置文件
   - 重啟問題容器

### 8.4 回滾流程

```bash
# 1. 停止服務
docker-compose down

# 2. 恢復資料備份
mongorestore /backup/mongo/<backup-date>
mysql -u root -p epd < /backup/mysql/epd_<backup-date>.sql

# 3. 回滾代碼
git checkout <previous-version>

# 4. 重啟服務
docker-compose up -d
```

## 9. 安全考慮

1. 資料加密
2. 訪問控制
3. 審計日誌
4. 備份策略
5. 漏洞掃描

## 10. 效能基準

- API 響應時間 < 100ms
- 資料庫查詢時間 < 50ms
- 快取命中率 > 80%
- 系統可用性 > 99.9%

## 11. 注意事項

1. 確保資料一致性
2. 做好備份措施
3. 監控系統效能
4. 定期維護優化
5. 安全性考慮

## 12. 時程規劃

1. 準備階段：1週
2. 開發階段：2週
3. 測試階段：1週
4. 部署階段：1週
5. 驗證階段：1週

總計：6週
