/**
 * WebSocket客戶端，用於接收批量傳送進度更新
 */

export interface BatchProgressEvent {
  type: 'batch_progress';
  batchId: string;
  totalDevices: number;
  completedDevices: number;
  failedDevices: number;
  currentDevice?: {
    id: string;
    macAddress: string;
    name?: string;
  };
  status: 'preparing' | 'running' | 'paused' | 'completed' | 'cancelled' | 'error';
  startTime?: number;
  estimatedTimeRemaining?: number;
  queueCycles?: number;
  waitCycles?: number;
  smartSelectionStats?: {
    totalAutoModeDevices: number;
    usedBackupGateway: number;
    primaryGatewayBusy: number;
  };
  error?: string;
}

export interface BatchCompleteEvent {
  type: 'batch_complete';
  batchId: string;
  result: {
    totalCount: number;
    successCount: number;
    failedCount: number;
    smartSelectionStats?: any;
    performanceStats?: any;
  };
}

// 設備狀態更新事件（用於網關回報的狀態變化）
export interface DeviceStatusEvent {
  type: 'device_status_update';
  storeId: string;
  devices: Array<{
    _id: string;
    macAddress: string;
    status: 'online' | 'offline';
    lastSeen: string;
    imageUpdateStatus?: '已更新' | '未更新';
    data?: {
      battery?: number;
      rssi?: number;
      imageCode?: string;
    };
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'single' | 'batch';
}

// 設備CRUD操作事件（用於API操作的新增、修改、刪除）
export interface DeviceCRUDEvent {
  type: 'device_crud_update';
  storeId: string;
  devices: Array<{
    _id: string;
    macAddress: string;
    status: 'online' | 'offline';
    lastSeen: string;
    imageUpdateStatus?: '已更新' | '未更新';
    data?: any;
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'create' | 'update' | 'delete';
}

// 網關狀態更新事件（用於網關連接狀態變化）
export interface GatewayStatusEvent {
  type: 'gateway_status_update';
  storeId: string;
  gateways: Array<{
    _id: string;
    name: string;
    macAddress: string;
    status: 'online' | 'offline';
    lastSeen: string;
    ipAddress?: string;
    model?: string;
    wifiFirmwareVersion?: string;
    btFirmwareVersion?: string;
    connectionInfo?: {
      connectedAt: string;
      lastActivity: string;
      isWebSocketConnected: boolean;
    };
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'connection' | 'info' | 'status';
}

// 網關CRUD操作事件（用於API操作的新增、修改、刪除）
export interface GatewayCRUDEvent {
  type: 'gateway_crud_update';
  storeId: string;
  gateways: Array<{
    _id: string;
    name: string;
    macAddress: string;
    status: 'online' | 'offline';
    lastSeen: string;
    ipAddress?: string;
    model?: string;
    wifiFirmwareVersion?: string;
    btFirmwareVersion?: string;
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'create' | 'update' | 'delete';
}

// 網關統計信息更新事件（用於忙碌/閒置狀態變化）
export interface GatewayStatsUpdateEvent {
  type: 'gateway_stats_update';
  storeId: string;
  stats: {
    totalConnected: number;
    busyGateways: number;
    idleGateways: number;
    busyGatewayDetails: Array<{
      id: string;
      name: string;
      storeId: string;
      ipAddress: string;
      activeTransmissions: {
        active: boolean;
        count: number;
        transmissions: Array<{
          chunkId: string;
          deviceMac: string;
          startTime: number;
          duration: number;
          status: string;
        }>;
      };
    }>;
    idleGatewayDetails: Array<{
      id: string;
      name: string;
      storeId: string;
      ipAddress: string;
    }>;
    summary: {
      totalConnected: number;
      busyCount: number;
      idleCount: number;
      busyPercentage: number;
      idlePercentage: number;
    };
    connectedGateways: Array<{
      id: string;
      name: string;
      storeId: string;
      ipAddress: string;
      connectionTime: string;
      lastActivity: string;
      inactiveForMs: number;
    }>;
    lastUpdated: string;
  };
  timestamp: string;
  triggerGatewayId: string;
}

// 門店資料更新事件
export interface StoreDataUpdateEvent {
  type: 'store_data_update';
  storeId: string;
  storeData: Array<{
    uid: string;
    data: Record<string, any>;
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'create' | 'update' | 'delete';
}

// 系統資料更新事件
export interface SystemDataUpdateEvent {
  type: 'system_data_update';
  systemData: Array<{
    uid: string;
    data: Record<string, any>;
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'create' | 'update' | 'delete';
}

// 模板更新事件
export interface TemplateUpdateEvent {
  type: 'template_update';
  storeId?: string;
  templates: Array<{
    id: string;
    name: string;
    isSystemTemplate: boolean;
    storeId?: string;
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'create' | 'update' | 'delete';
}

// 刷圖計畫更新事件
export interface RefreshPlanUpdateEvent {
  type: 'refresh_plan_update';
  storeId: string;
  planId: string;
  planData: {
    _id: string;
    name: string;
    status: 'active' | 'inactive' | 'running' | 'error';
    enabled: boolean;
    lastRun?: string;
    nextRun?: string;
    statistics?: {
      totalRuns: number;
      successRuns: number;
      failedRuns: number;
      lastRunResult?: any;
    };
    updatedFields: string[];
  };
  timestamp: string;
  updateType: 'create' | 'update' | 'delete' | 'status_change';
}

// 狀態訂閱確認事件
export interface StatusSubscriptionAck {
  type: 'device_status_subscription_ack' | 'gateway_status_subscription_ack' | 'store_data_subscription_ack' | 'system_data_subscription_ack' | 'template_subscription_ack' | 'refresh_plan_subscription_ack';
  storeId?: string;
  subscribed: boolean;
  timestamp: string;
}

export type WebSocketEvent =
  | BatchProgressEvent
  | BatchCompleteEvent
  | DeviceStatusEvent
  | DeviceCRUDEvent
  | GatewayStatusEvent
  | GatewayCRUDEvent
  | GatewayStatsUpdateEvent
  | StoreDataUpdateEvent
  | SystemDataUpdateEvent
  | TemplateUpdateEvent
  | RefreshPlanUpdateEvent
  | StatusSubscriptionAck;

export type WebSocketEventHandler = (event: WebSocketEvent) => void;
export type DeviceStatusEventHandler = (event: DeviceStatusEvent) => void;
export type DeviceCRUDEventHandler = (event: DeviceCRUDEvent) => void;
export type GatewayStatusEventHandler = (event: GatewayStatusEvent) => void;
export type GatewayCRUDEventHandler = (event: GatewayCRUDEvent) => void;
export type GatewayStatsUpdateEventHandler = (event: GatewayStatsUpdateEvent) => void;
export type StoreDataUpdateEventHandler = (event: StoreDataUpdateEvent) => void;
export type SystemDataUpdateEventHandler = (event: SystemDataUpdateEvent) => void;
export type TemplateUpdateEventHandler = (event: TemplateUpdateEvent) => void;
export type RefreshPlanUpdateEventHandler = (event: RefreshPlanUpdateEvent) => void;

class WebSocketClient {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventHandlers: Set<WebSocketEventHandler> = new Set();
  private deviceStatusHandlers: Set<DeviceStatusEventHandler> = new Set();
  private deviceCRUDHandlers: Set<DeviceCRUDEventHandler> = new Set();
  private gatewayStatusHandlers: Set<GatewayStatusEventHandler> = new Set();
  private gatewayCRUDHandlers: Set<GatewayCRUDEventHandler> = new Set();
  private gatewayStatsUpdateHandlers: Set<GatewayStatsUpdateEventHandler> = new Set();
  private storeDataUpdateHandlers: Set<StoreDataUpdateEventHandler> = new Set();
  private systemDataUpdateHandlers: Set<SystemDataUpdateEventHandler> = new Set();
  private templateUpdateHandlers: Set<TemplateUpdateEventHandler> = new Set();
  private refreshPlanUpdateHandlers: Set<RefreshPlanUpdateEventHandler> = new Set();
  private isConnecting = false;
  private shouldReconnect = true;
  private pendingSubscriptions = new Set<string>(); // 待處理的批量進度訂閱
  private subscribedStores = new Set<string>(); // 已訂閱的設備狀態門店
  private subscribedGatewayStores = new Set<string>(); // 已訂閱的網關狀態門店
  private subscribedStoreDataStores = new Set<string>(); // 已訂閱的門店資料門店
  private subscribedSystemData = false; // 是否已訂閱系統資料
  private subscribedTemplateStores = new Set<string>(); // 已訂閱的模板門店
  private subscribedRefreshPlanStores = new Set<string>(); // 已訂閱的刷圖計畫門店

  constructor() {
    this.connect();
  }

  private async connect() {
    if (this.isConnecting || this.ws?.readyState === WebSocket.OPEN) {
      return;
    }

    this.isConnecting = true;

    try {
      // 獲取WebSocket專用token
      let wsToken = null;

      try {
        // 使用與其他API相同的URL構建邏輯
        const { buildEndpointUrl } = await import('./api/apiConfig');

        // 嘗試從API獲取WebSocket token
        const response = await fetch(buildEndpointUrl('auth', 'websocket-token'), {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          wsToken = data.wsToken;
          console.log('成功獲取WebSocket token');
        } else {
          console.warn(`無法獲取WebSocket token (${response.status})，嘗試使用開發模式`);
        }
      } catch (error) {
        console.warn('獲取WebSocket token失敗:', error);
      }

      // 動態獲取WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.hostname;
      // WebSocket服務器始終運行在3001端口（根據server/index.js配置）
      const port = '3001';

      let wsUrl;
      if (wsToken) {
        wsUrl = `${protocol}//${host}:${port}?token=${encodeURIComponent(wsToken)}`;
      } else {
        // 開發環境允許無token連接
        wsUrl = `${protocol}//${host}:${port}`;
        console.warn('WebSocket連接: 未找到WebSocket token，使用開發模式連接');
      }

      console.log('正在連接WebSocket:', `${protocol}//${host}:${port}`);
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('WebSocket連接已建立');
        this.isConnecting = false;
        this.reconnectAttempts = 0;

        // 發送客戶端識別信息
        this.send({
          type: 'client_identify',
          clientType: 'frontend',
          timestamp: new Date().toISOString()
        });

        // 處理待處理的訂閱
        this.processPendingSubscriptions();
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleMessage(data);
        } catch (error) {
          console.error('解析WebSocket消息失敗:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log('WebSocket連接已關閉:', event.code, event.reason);
        this.isConnecting = false;
        this.ws = null;

        if (this.shouldReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket錯誤:', error);
        this.isConnecting = false;
      };

    } catch (error) {
      console.error('創建WebSocket連接失敗:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  private scheduleReconnect() {
    if (!this.shouldReconnect) return;

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`將在 ${delay}ms 後嘗試重新連接 (第 ${this.reconnectAttempts} 次)`);

    setTimeout(() => {
      if (this.shouldReconnect) {
        this.connect();
      }
    }, delay);
  }

  private processPendingSubscriptions() {
    // 恢復批量進度訂閱
    if (this.pendingSubscriptions.size > 0) {
      console.log(`處理 ${this.pendingSubscriptions.size} 個待處理的批量進度訂閱`);
      Array.from(this.pendingSubscriptions).forEach(batchId => {
        console.log(`重新發送批量進度訂閱: ${batchId}`);
        this.send({
          type: 'subscribe_batch_progress',
          batchId,
          timestamp: new Date().toISOString()
        });
      });
    }

    // 恢復設備狀態訂閱
    if (this.subscribedStores.size > 0) {
      console.log(`恢復 ${this.subscribedStores.size} 個設備狀態訂閱`);
      Array.from(this.subscribedStores).forEach(storeId => {
        this.subscribeDeviceStatus(storeId);
      });
    }

    // 恢復網關狀態訂閱
    if (this.subscribedGatewayStores.size > 0) {
      console.log(`恢復 ${this.subscribedGatewayStores.size} 個網關狀態訂閱`);
      Array.from(this.subscribedGatewayStores).forEach(storeId => {
        this.subscribeGatewayStatus(storeId);
      });
    }

    // 恢復門店資料訂閱
    if (this.subscribedStoreDataStores.size > 0) {
      console.log(`恢復 ${this.subscribedStoreDataStores.size} 個門店資料訂閱`);
      Array.from(this.subscribedStoreDataStores).forEach(storeId => {
        this.subscribeStoreDataUpdate(storeId);
      });
    }

    // 恢復系統資料訂閱
    if (this.subscribedSystemData) {
      console.log('恢復系統資料訂閱');
      this.subscribeSystemDataUpdate();
    }

    // 恢復模板訂閱
    if (this.subscribedTemplateStores.size > 0) {
      console.log(`恢復 ${this.subscribedTemplateStores.size} 個模板訂閱`);
      Array.from(this.subscribedTemplateStores).forEach(storeId => {
        this.subscribeTemplateUpdate(storeId);
      });
    }

    // 恢復刷圖計畫訂閱
    if (this.subscribedRefreshPlanStores.size > 0) {
      console.log(`恢復 ${this.subscribedRefreshPlanStores.size} 個刷圖計畫訂閱`);
      Array.from(this.subscribedRefreshPlanStores).forEach(storeId => {
        this.subscribeRefreshPlanUpdate(storeId);
      });
    }
  }

  private handleMessage(data: any) {
    // 處理不同類型的消息
    switch (data.type) {
      case 'batch_progress':
      case 'batch_complete':
        // 處理批量傳送進度事件
        this.notifyHandlers(data as WebSocketEvent);
        break;
      case 'device_status_update':
        // 處理設備狀態更新事件
        this.notifyDeviceStatusHandlers(data as DeviceStatusEvent);
        break;
      case 'device_crud_update':
        // 處理設備CRUD操作事件
        this.notifyDeviceCRUDHandlers(data as DeviceCRUDEvent);
        break;
      case 'gateway_status_update':
        // 處理網關狀態更新事件
        this.notifyGatewayStatusHandlers(data as GatewayStatusEvent);
        break;
      case 'gateway_crud_update':
        // 處理網關CRUD操作事件
        this.notifyGatewayCRUDHandlers(data as GatewayCRUDEvent);
        break;
      case 'gateway_stats_update':
        // 處理網關統計信息更新事件
        this.notifyGatewayStatsUpdateHandlers(data as GatewayStatsUpdateEvent);
        break;
      case 'store_data_update':
        // 處理門店資料更新事件
        this.notifyStoreDataUpdateHandlers(data as StoreDataUpdateEvent);
        break;
      case 'system_data_update':
        // 處理系統資料更新事件
        this.notifySystemDataUpdateHandlers(data as SystemDataUpdateEvent);
        break;
      case 'template_update':
        // 處理模板更新事件
        this.notifyTemplateUpdateHandlers(data as TemplateUpdateEvent);
        break;
      case 'refresh_plan_update':
        // 處理刷圖計畫更新事件
        this.notifyRefreshPlanUpdateHandlers(data as RefreshPlanUpdateEvent);
        break;
      case 'welcome':
        console.log('收到服務器歡迎消息:', data.message);
        break;
      case 'pong':
        // 心跳回應
        break;
      case 'subscription_ack':
        // 訂閱確認消息
        console.log(`訂閱確認: batchId=${data.batchId}, subscribed=${data.subscribed}`);
        break;
      case 'device_status_subscription_ack':
        // 設備狀態訂閱確認
        console.log(`設備狀態訂閱確認: storeId=${data.storeId}, subscribed=${data.subscribed}`);
        break;
      case 'gateway_status_subscription_ack':
        // 網關狀態訂閱確認
        console.log(`網關狀態訂閱確認: storeId=${data.storeId}, subscribed=${data.subscribed}`);
        break;
      case 'refresh_plan_subscription_ack':
        // 刷圖計畫訂閱確認
        console.log(`刷圖計畫訂閱確認: storeId=${data.storeId}, subscribed=${data.subscribed}`);
        break;
      case 'batch_cancel_ack':
        // 取消批量傳送確認
        console.log(`取消批量傳送確認: batchId=${data.batchId}, cancelled=${data.cancelled}`);
        break;
      default:
        console.log('收到未處理的WebSocket消息:', data);
    }
  }

  private notifyHandlers(event: WebSocketEvent) {
    this.eventHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('WebSocket事件處理器錯誤:', error);
      }
    });
  }

  // 通知設備狀態事件處理器
  private notifyDeviceStatusHandlers(event: DeviceStatusEvent) {
    this.deviceStatusHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('設備狀態事件處理器錯誤:', error);
      }
    });
  }

  // 通知設備CRUD事件處理器
  private notifyDeviceCRUDHandlers(event: DeviceCRUDEvent) {
    this.deviceCRUDHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('設備CRUD事件處理器錯誤:', error);
      }
    });
  }

  // 通知網關狀態事件處理器
  private notifyGatewayStatusHandlers(event: GatewayStatusEvent) {
    this.gatewayStatusHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('網關狀態事件處理器錯誤:', error);
      }
    });
  }

  // 通知網關CRUD事件處理器
  private notifyGatewayCRUDHandlers(event: GatewayCRUDEvent) {
    this.gatewayCRUDHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('網關CRUD事件處理器錯誤:', error);
      }
    });
  }

  // 通知網關統計信息更新事件處理器
  private notifyGatewayStatsUpdateHandlers(event: GatewayStatsUpdateEvent) {
    this.gatewayStatsUpdateHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('網關統計信息更新事件處理器錯誤:', error);
      }
    });
  }

  // 通知門店資料更新事件處理器
  private notifyStoreDataUpdateHandlers(event: StoreDataUpdateEvent) {
    this.storeDataUpdateHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('門店資料更新事件處理器錯誤:', error);
      }
    });
  }

  // 通知系統資料更新事件處理器
  private notifySystemDataUpdateHandlers(event: SystemDataUpdateEvent) {
    this.systemDataUpdateHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('系統資料更新事件處理器錯誤:', error);
      }
    });
  }

  // 通知模板更新事件處理器
  private notifyTemplateUpdateHandlers(event: TemplateUpdateEvent) {
    this.templateUpdateHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('模板更新事件處理器錯誤:', error);
      }
    });
  }

  // 通知刷圖計畫更新事件處理器
  private notifyRefreshPlanUpdateHandlers(event: RefreshPlanUpdateEvent) {
    this.refreshPlanUpdateHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('刷圖計畫更新事件處理器錯誤:', error);
      }
    });
  }

  private send(data: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    } else {
      console.warn('WebSocket未連接，無法發送消息:', data);
    }
  }

  // 添加事件監聽器
  public addEventListener(handler: WebSocketEventHandler) {
    this.eventHandlers.add(handler);
  }

  // 移除事件監聽器
  public removeEventListener(handler: WebSocketEventHandler) {
    this.eventHandlers.delete(handler);
  }

  // 發送心跳
  public ping() {
    this.send({ type: 'ping', timestamp: new Date().toISOString() });
  }

  // 訂閱批量傳送進度
  public subscribeBatchProgress(batchId: string) {
    // 添加到待處理訂閱列表
    this.pendingSubscriptions.add(batchId);

    // 如果已連接，立即發送訂閱消息
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.send({
        type: 'subscribe_batch_progress',
        batchId,
        timestamp: new Date().toISOString()
      });
    } else {
      console.log(`WebSocket未連接，將 ${batchId} 添加到待處理訂閱列表`);
    }
  }

  // 取消訂閱批量傳送進度
  public unsubscribeBatchProgress(batchId: string) {
    // 從待處理訂閱列表中移除
    this.pendingSubscriptions.delete(batchId);

    // 如果已連接，發送取消訂閱消息
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.send({
        type: 'unsubscribe_batch_progress',
        batchId,
        timestamp: new Date().toISOString()
      });
    } else {
      console.log(`WebSocket未連接，已從待處理訂閱列表中移除 ${batchId}`);
    }
  }

  // 取消批量傳送
  public cancelBatchSend(batchId: string) {
    this.send({
      type: 'cancel_batch_send',
      batchId,
      timestamp: new Date().toISOString()
    });
  }

  // ==================== 設備狀態訂閱方法 ====================

  // 訂閱設備狀態
  public subscribeDeviceStatus(storeId?: string, options?: any) {
    if (storeId) {
      this.subscribedStores.add(storeId);
    }

    console.log(`訂閱設備狀態: storeId=${storeId}`);

    this.send({
      type: 'subscribe_device_status',
      storeId,
      options: options || {
        includeImageStatus: true,
        includeBatteryInfo: true
      },
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱設備狀態
  public unsubscribeDeviceStatus(storeId?: string) {
    if (storeId) {
      this.subscribedStores.delete(storeId);
    }

    console.log(`取消訂閱設備狀態: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_device_status',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 添加設備狀態事件監聽器
  public addDeviceStatusListener(handler: DeviceStatusEventHandler) {
    this.deviceStatusHandlers.add(handler);
  }

  // 移除設備狀態事件監聽器
  public removeDeviceStatusListener(handler: DeviceStatusEventHandler) {
    this.deviceStatusHandlers.delete(handler);
  }

  // ==================== 網關狀態訂閱方法 ====================

  // 訂閱網關狀態
  public subscribeGatewayStatus(storeId?: string, options?: any) {
    if (storeId) {
      this.subscribedGatewayStores.add(storeId);
    }

    console.log(`訂閱網關狀態: storeId=${storeId}`);

    this.send({
      type: 'subscribe_gateway_status',
      storeId,
      options: options || {
        includeConnectionInfo: true,
        includeFirmwareInfo: true
      },
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱網關狀態
  public unsubscribeGatewayStatus(storeId?: string) {
    if (storeId) {
      this.subscribedGatewayStores.delete(storeId);
    }

    console.log(`取消訂閱網關狀態: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_gateway_status',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 添加網關狀態事件監聽器
  public addGatewayStatusListener(handler: GatewayStatusEventHandler) {
    this.gatewayStatusHandlers.add(handler);
  }

  // 移除網關狀態事件監聽器
  public removeGatewayStatusListener(handler: GatewayStatusEventHandler) {
    this.gatewayStatusHandlers.delete(handler);
  }

  // ==================== 設備CRUD訂閱方法 ====================

  // 訂閱設備CRUD操作
  public subscribeDeviceCRUD(storeId: string, options?: any) {
    console.log(`訂閱設備CRUD操作: storeId=${storeId}`);

    this.send({
      type: 'subscribe_device_status', // 使用相同的訂閱類型，後端會推送不同的事件
      storeId,
      options: options || {},
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱設備CRUD操作
  public unsubscribeDeviceCRUD(storeId: string) {
    console.log(`取消訂閱設備CRUD操作: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_device_status',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 添加設備CRUD事件監聽器
  public addDeviceCRUDListener(handler: DeviceCRUDEventHandler) {
    this.deviceCRUDHandlers.add(handler);
  }

  // 移除設備CRUD事件監聽器
  public removeDeviceCRUDListener(handler: DeviceCRUDEventHandler) {
    this.deviceCRUDHandlers.delete(handler);
  }

  // ==================== 網關CRUD訂閱方法 ====================

  // 訂閱網關CRUD操作
  public subscribeGatewayCRUD(storeId: string, options?: any) {
    console.log(`訂閱網關CRUD操作: storeId=${storeId}`);

    this.send({
      type: 'subscribe_gateway_status', // 使用相同的訂閱類型，後端會推送不同的事件
      storeId,
      options: options || {},
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱網關CRUD操作
  public unsubscribeGatewayCRUD(storeId: string) {
    console.log(`取消訂閱網關CRUD操作: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_gateway_status',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 添加網關CRUD事件監聽器
  public addGatewayCRUDListener(handler: GatewayCRUDEventHandler) {
    this.gatewayCRUDHandlers.add(handler);
  }

  // 移除網關CRUD事件監聽器
  public removeGatewayCRUDListener(handler: GatewayCRUDEventHandler) {
    this.gatewayCRUDHandlers.delete(handler);
  }

  // ==================== 網關統計信息更新訂閱方法 ====================

  // 添加網關統計信息更新事件監聽器
  public addGatewayStatsUpdateListener(handler: GatewayStatsUpdateEventHandler) {
    this.gatewayStatsUpdateHandlers.add(handler);
  }

  // 移除網關統計信息更新事件監聽器
  public removeGatewayStatsUpdateListener(handler: GatewayStatsUpdateEventHandler) {
    this.gatewayStatsUpdateHandlers.delete(handler);
  }

  // ==================== 門店資料訂閱方法 ====================

  // 訂閱門店資料更新
  public subscribeStoreDataUpdate(storeId: string, options?: any) {
    this.subscribedStoreDataStores.add(storeId);

    console.log(`訂閱門店資料更新: storeId=${storeId}`);

    this.send({
      type: 'subscribe_store_data_update',
      storeId,
      options: options || {},
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱門店資料更新
  public unsubscribeStoreDataUpdate(storeId: string) {
    this.subscribedStoreDataStores.delete(storeId);

    console.log(`取消訂閱門店資料更新: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_store_data_update',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 添加門店資料更新事件監聽器
  public addStoreDataUpdateListener(handler: StoreDataUpdateEventHandler) {
    this.storeDataUpdateHandlers.add(handler);
  }

  // 移除門店資料更新事件監聽器
  public removeStoreDataUpdateListener(handler: StoreDataUpdateEventHandler) {
    this.storeDataUpdateHandlers.delete(handler);
  }

  // ==================== 系統資料訂閱方法 ====================

  // 訂閱系統資料更新
  public subscribeSystemDataUpdate(options?: any) {
    this.subscribedSystemData = true;

    console.log('訂閱系統資料更新');

    this.send({
      type: 'subscribe_system_data_update',
      options: options || {},
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱系統資料更新
  public unsubscribeSystemDataUpdate() {
    this.subscribedSystemData = false;

    console.log('取消訂閱系統資料更新');

    this.send({
      type: 'unsubscribe_system_data_update',
      timestamp: new Date().toISOString()
    });
  }

  // 添加系統資料更新事件監聽器
  public addSystemDataUpdateListener(handler: SystemDataUpdateEventHandler) {
    this.systemDataUpdateHandlers.add(handler);
  }

  // 移除系統資料更新事件監聽器
  public removeSystemDataUpdateListener(handler: SystemDataUpdateEventHandler) {
    this.systemDataUpdateHandlers.delete(handler);
  }

  // ==================== 模板訂閱方法 ====================

  // 訂閱模板更新
  public subscribeTemplateUpdate(storeId?: string, options?: any) {
    if (storeId) {
      this.subscribedTemplateStores.add(storeId);
    }

    console.log(`訂閱模板更新: storeId=${storeId || 'all'}`);

    this.send({
      type: 'subscribe_template_update',
      storeId,
      options: options || {},
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱模板更新
  public unsubscribeTemplateUpdate(storeId?: string) {
    if (storeId) {
      this.subscribedTemplateStores.delete(storeId);
    }

    console.log(`取消訂閱模板更新: storeId=${storeId || 'all'}`);

    this.send({
      type: 'unsubscribe_template_update',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 添加模板更新事件監聽器
  public addTemplateUpdateListener(handler: TemplateUpdateEventHandler) {
    this.templateUpdateHandlers.add(handler);
  }

  // 移除模板更新事件監聽器
  public removeTemplateUpdateListener(handler: TemplateUpdateEventHandler) {
    this.templateUpdateHandlers.delete(handler);
  }

  // ==================== 刷圖計畫訂閱方法 ====================

  // 訂閱刷圖計畫更新
  public subscribeRefreshPlanUpdate(storeId: string, options?: any) {
    this.subscribedRefreshPlanStores.add(storeId);

    console.log(`訂閱刷圖計畫更新: storeId=${storeId}`);

    this.send({
      type: 'subscribe_refresh_plan_update',
      storeId,
      options: options || {},
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱刷圖計畫更新
  public unsubscribeRefreshPlanUpdate(storeId: string) {
    this.subscribedRefreshPlanStores.delete(storeId);

    console.log(`取消訂閱刷圖計畫更新: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_refresh_plan_update',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 添加刷圖計畫更新事件監聽器
  public addRefreshPlanUpdateListener(handler: RefreshPlanUpdateEventHandler) {
    this.refreshPlanUpdateHandlers.add(handler);
  }

  // 移除刷圖計畫更新事件監聽器
  public removeRefreshPlanUpdateListener(handler: RefreshPlanUpdateEventHandler) {
    this.refreshPlanUpdateHandlers.delete(handler);
  }

  // 獲取連接狀態
  public isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  // 手動重新連接
  public reconnect() {
    this.disconnect();
    this.shouldReconnect = true;
    this.reconnectAttempts = 0;
    this.connect();
  }

  // 斷開連接
  public disconnect() {
    this.shouldReconnect = false;
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  // 清理資源
  public destroy() {
    this.disconnect();
    this.eventHandlers.clear();
    this.deviceStatusHandlers.clear();
    this.deviceCRUDHandlers.clear();
    this.gatewayStatusHandlers.clear();
    this.gatewayCRUDHandlers.clear();
    this.gatewayStatsUpdateHandlers.clear();
    this.storeDataUpdateHandlers.clear();
    this.systemDataUpdateHandlers.clear();
    this.templateUpdateHandlers.clear();
    this.refreshPlanUpdateHandlers.clear();
    this.pendingSubscriptions.clear();
    this.subscribedStores.clear();
    this.subscribedGatewayStores.clear();
    this.subscribedStoreDataStores.clear();
    this.subscribedSystemData = false;
    this.subscribedTemplateStores.clear();
    this.subscribedRefreshPlanStores.clear();
  }
}

// 創建全局WebSocket客戶端實例
let wsClient: WebSocketClient | null = null;

export const getWebSocketClient = (): WebSocketClient => {
  if (!wsClient) {
    wsClient = new WebSocketClient();
  }
  return wsClient;
};

// 便捷方法
export const subscribeToBatchProgress = (
  batchId: string, 
  handler: WebSocketEventHandler
): (() => void) => {
  const client = getWebSocketClient();
  client.addEventListener(handler);
  client.subscribeBatchProgress(batchId);

  // 返回清理函數
  return () => {
    client.removeEventListener(handler);
    client.unsubscribeBatchProgress(batchId);
  };
};

export const cancelBatchSend = (batchId: string) => {
  const client = getWebSocketClient();
  client.cancelBatchSend(batchId);
};

// 設備狀態訂閱便捷方法
export const subscribeToDeviceStatus = (
  storeId: string,
  handler: DeviceStatusEventHandler,
  options?: any
): (() => void) => {
  const client = getWebSocketClient();
  client.addDeviceStatusListener(handler);
  client.subscribeDeviceStatus(storeId, options);

  // 返回清理函數
  return () => {
    client.removeDeviceStatusListener(handler);
    client.unsubscribeDeviceStatus(storeId);
  };
};

// 設備CRUD操作訂閱便捷方法
export const subscribeToDeviceCRUD = (
  storeId: string,
  handler: DeviceCRUDEventHandler,
  options?: any
): (() => void) => {
  const client = getWebSocketClient();
  client.addDeviceCRUDListener(handler);
  client.subscribeDeviceCRUD(storeId, options);

  // 返回清理函數
  return () => {
    client.removeDeviceCRUDListener(handler);
    client.unsubscribeDeviceCRUD(storeId);
  };
};

// 網關狀態訂閱便捷方法
export const subscribeToGatewayStatus = (
  storeId: string,
  handler: GatewayStatusEventHandler,
  options?: any
): (() => void) => {
  const client = getWebSocketClient();
  client.addGatewayStatusListener(handler);
  client.subscribeGatewayStatus(storeId, options);

  // 返回清理函數
  return () => {
    client.removeGatewayStatusListener(handler);
    client.unsubscribeGatewayStatus(storeId);
  };
};

// 網關CRUD操作訂閱便捷方法
export const subscribeToGatewayCRUD = (
  storeId: string,
  handler: GatewayCRUDEventHandler,
  options?: any
): (() => void) => {
  const client = getWebSocketClient();
  client.addGatewayCRUDListener(handler);
  client.subscribeGatewayCRUD(storeId, options);

  // 返回清理函數
  return () => {
    client.removeGatewayCRUDListener(handler);
    client.unsubscribeGatewayCRUD(storeId);
  };
};

// 門店資料更新訂閱便捷方法
export const subscribeToStoreDataUpdate = (
  storeId: string,
  handler: StoreDataUpdateEventHandler,
  options?: any
): (() => void) => {
  const client = getWebSocketClient();
  client.addStoreDataUpdateListener(handler);
  client.subscribeStoreDataUpdate(storeId, options);

  // 返回清理函數
  return () => {
    client.removeStoreDataUpdateListener(handler);
    client.unsubscribeStoreDataUpdate(storeId);
  };
};

// 系統資料更新訂閱便捷方法
export const subscribeToSystemDataUpdate = (
  handler: SystemDataUpdateEventHandler,
  options?: any
): (() => void) => {
  const client = getWebSocketClient();
  client.addSystemDataUpdateListener(handler);
  client.subscribeSystemDataUpdate(options);

  // 返回清理函數
  return () => {
    client.removeSystemDataUpdateListener(handler);
    client.unsubscribeSystemDataUpdate();
  };
};

// 模板更新訂閱便捷方法
export const subscribeToTemplateUpdate = (
  storeId: string | undefined,
  handler: TemplateUpdateEventHandler,
  options?: any
): (() => void) => {
  const client = getWebSocketClient();
  client.addTemplateUpdateListener(handler);
  client.subscribeTemplateUpdate(storeId, options);

  // 返回清理函數
  return () => {
    client.removeTemplateUpdateListener(handler);
    client.unsubscribeTemplateUpdate(storeId);
  };
};

// 網關統計信息更新訂閱便捷方法
export const subscribeToGatewayStatsUpdate = (
  storeId: string,
  handler: GatewayStatsUpdateEventHandler
): (() => void) => {
  const client = getWebSocketClient();
  client.addGatewayStatsUpdateListener(handler);

  // 網關統計信息更新使用網關狀態訂閱，因為它們共享同一個訂閱通道
  client.subscribeGatewayStatus(storeId, {
    includeConnectionInfo: true,
    includeFirmwareInfo: true
  });

  // 返回清理函數
  return () => {
    client.removeGatewayStatsUpdateListener(handler);
    // 注意：這裡不取消訂閱網關狀態，因為可能有其他監聽器在使用
  };
};

// 刷圖計畫更新訂閱便捷方法
export const subscribeToRefreshPlanUpdate = (
  storeId: string,
  handler: RefreshPlanUpdateEventHandler,
  options?: any
): (() => void) => {
  const client = getWebSocketClient();
  client.addRefreshPlanUpdateListener(handler);
  client.subscribeRefreshPlanUpdate(storeId, options);

  // 返回清理函數
  return () => {
    client.removeRefreshPlanUpdateListener(handler);
    client.unsubscribeRefreshPlanUpdate(storeId);
  };
};

// 在應用卸載時清理WebSocket連接
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    if (wsClient) {
      wsClient.destroy();
    }
  });
}
