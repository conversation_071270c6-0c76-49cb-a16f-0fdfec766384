# Apple風格懸浮球功能特性

## 🎨 視覺設計

### 主按鈕設計
- **漸變背景**: 紫色到粉色的漸變，營造現代感
- **多層光暈**: 
  - 外圈光暈：脈衝動畫效果
  - 中圈光暈：旋轉動畫效果
  - 內圈：磨砂玻璃質感
- **展開指示器**: 右上角黃色小圓點，提示可展開
- **懸浮效果**: 輕微的上下浮動動畫

### 子按鈕設計
- **磨砂玻璃**: 真實的backdrop-filter效果
- **漸變背景**: 每個按鈕都有獨特的漸變色
- **陰影層次**: 多層陰影營造深度感
- **懸停效果**: 輕微上移和縮放

## 🎭 動畫效果

### 展開動畫
```css
/* Apple風格彈性進入 */
cubic-bezier(0.175, 0.885, 0.32, 1.275)

/* 展開路徑 */
弧形角度: -126° 到 -18° (144度範圍)
半徑: 90px
延遲: 每個按鈕間隔80ms
```

### 收起動畫
```css
/* 快速退出 */
cubic-bezier(0.55, 0.055, 0.675, 0.19)

/* 收起順序 */
反向延遲: 最後展開的先收起
延遲: 每個按鈕間隔50ms
```

### 主按鈕動畫
- **旋轉**: 展開時45度旋轉
- **縮放**: 展開時110%縮放
- **圖標切換**: Plus → X 的平滑過渡
- **脈衝效果**: 2秒循環的光暈脈衝

## 🎯 交互體驗

### 觸發方式
1. **點擊主按鈕**: 展開/收起
2. **點擊背景遮罩**: 收起
3. **點擊外部區域**: 收起

### 視覺反饋
- **波紋效果**: 點擊時的波紋擴散
- **懸停提示**: 標籤文字顯示
- **狀態指示**: 展開指示器的顯示/隱藏

### 響應式設計
- **移動端適配**: 按鈕尺寸自動調整
- **深色模式**: 自動適配系統主題
- **高對比度**: 保證可訪問性

## 🔧 技術實現

### CSS動畫關鍵幀
```css
@keyframes apple-bounce-in {
  0% { opacity: 0; transform: scale(0) rotate(-90deg); }
  60% { opacity: 0.9; transform: scale(1.15) rotate(10deg); }
  80% { opacity: 1; transform: scale(0.95) rotate(-5deg); }
  100% { opacity: 1; transform: scale(1) rotate(0deg); }
}
```

### 位置計算算法
```typescript
const getCircularPosition = (index: number, total: number, radius: number = 90) => {
  const startAngle = -Math.PI * 0.7; // -126度
  const endAngle = -Math.PI * 0.1;   // -18度
  const totalAngle = endAngle - startAngle;
  const angleStep = totalAngle / Math.max(total - 1, 1);
  const angle = startAngle + (index * angleStep);
  
  return {
    x: Math.cos(angle) * radius,
    y: Math.sin(angle) * radius
  };
};
```

### 磨砂玻璃效果
```css
.fab-apple-glass {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}
```

## 🎪 特殊效果

### 背景遮罩
- **半透明黑色**: 10%透明度
- **模糊效果**: backdrop-blur-sm
- **點擊關閉**: 點擊遮罩自動收起

### 光暈動畫
- **外圈脈衝**: 2秒循環，透明度和縮放變化
- **中圈旋轉**: 4秒循環，360度旋轉
- **展開指示器**: 3秒循環，旋轉和縮放

### 懸停效果
- **主按鈕**: 上移2px + 5%縮放
- **子按鈕**: 10%縮放 + 陰影增強
- **標籤提示**: 淡入顯示，帶箭頭指示器

## 🌟 Apple風格特點

### 設計原則
1. **簡潔**: 最少的視覺元素
2. **直觀**: 清晰的交互指示
3. **流暢**: 自然的動畫過渡
4. **精緻**: 細膩的視覺效果

### 色彩搭配
- **主色調**: 紫色漸變 (#8B5CF6 → #EC4899)
- **功能色**: 紅色(Bug) + 藍色(AI)
- **輔助色**: 白色光暈 + 黃色指示器

### 動畫時機
- **進入**: 800ms 彈性動畫
- **退出**: 500ms 快速動畫
- **懸停**: 300ms 平滑過渡
- **點擊**: 200ms 即時反饋

## 📱 兼容性

### 瀏覽器支持
- **Chrome**: 完全支持
- **Safari**: 完全支持
- **Firefox**: 部分支持 (backdrop-filter需要開啟)
- **Edge**: 完全支持

### 設備適配
- **桌面**: 完整體驗
- **平板**: 觸摸優化
- **手機**: 尺寸調整

### 性能優化
- **GPU加速**: transform3d
- **避免重排**: 使用transform而非position
- **節流處理**: 動畫期間禁用重複觸發
