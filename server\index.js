// server/index.js
const express = require('express');
const http = require('http');
const { MongoClient, GridFSBucket } = require('mongodb');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const { createTemplateValidatorMiddleware } = require('./utils/templateValidation'); // 導入模板驗證中間件創建函數

// 導入所有 API 路由
const { router: dataFieldApiRouter, initDB: initDataFieldApi } = require('./routes/dataFieldApi'); // 導入資料欄位 API 路由及初始化函數
const { router: storeDataApiRouter, initDB: initStoreDataApi } = require('./routes/storeDataApi'); // 導入門店資料 API 路由及初始化函數
const { router: systemSpecificDataApiRouter, initDB: initSystemSpecificDataApi } = require('./routes/systemSpecificDataApi'); // 導入系統專屬數據 API 路由及初始化函數
const { router: storeApiRouter, initDB: initStoreApi } = require('./routes/storeApi'); // 導入門店管理 API 路由及初始化函數
const { router: sysConfigApiRouter, initDB: initSysConfigApi } = require('./routes/sysConfigApi'); // 導入系統配置 API 路由及初始化函數
const { router: fileApiRouter, initDB: initFileApi } = require('./routes/fileApi'); // 導入文件 API 路由及初始化函數
const { router: bugReportApiRouter, initDB: initBugReportApi } = require('./routes/bugReportApi'); // 導入bug回報 API 路由及初始化函數
const { router: templateApiRouter, initDB: initTemplateApi } = require('./routes/templateApi'); // 導入模板 API 路由及初始化函數
const { router: deviceApiRouter, initDB: initDeviceApi } = require('./routes/deviceApi'); // 導入設備管理 API 路由及初始化函數
const { router: gatewayApiRouter, initDB: initGatewayApi } = require('./routes/gatewayApi'); // 導入網關管理 API 路由及初始化函數
const { router: refreshPlanApiRouter, initDB: initRefreshPlanApi } = require('./routes/refreshPlanApi'); // 導入刷圖計畫 API 路由及初始化函數
const { router: softwareApiRouter, initDB: initSoftwareApi } = require('./routes/softwareApi'); // 導入軟體管理 API 路由及初始化函數
const templateValidatorApiRouter = require('./routes/templateValidatorApi'); // 導入檢查碼相關 API 路由
const codePreviewApiRouter = require('./routes/codePreview'); // 導入 QR Code 和條碼預覽 API 路由

// 導入用戶認證和權限管理相關路由
const { router: authApiRouter, initDB: initAuthApi } = require('./routes/authApi'); // 導入認證 API 路由
const { router: userApiRouter, initDB: initUserApi } = require('./routes/userApi'); // 導入用戶管理 API 路由
const { router: roleApiRouter, initDB: initRoleApi } = require('./routes/roleApi'); // 導入角色管理 API 路由
const { router: permissionApiRouter, initDB: initPermissionApi } = require('./routes/permissionApi'); // 導入權限分配 API 路由

// 導入AI助手相關路由
const aiAssistantApiRouter = require('./routes/aiAssistantApi'); // 導入AI助手 API 路由

// 導入 WebSocket 服務
const {
  initDB: initWebSocketServiceDB,
  initWebSocketServer,
  setupHeartbeatCheck
} = require('./services/websocketService');

// 導入設備狀態服務
const {
  initDB: initDeviceStatusServiceDB,
  startDeviceStatusChecker
} = require('./services/deviceStatusService');

// 導入發送預覽圖到網關服務
const sendPreviewToGateway = require('./services/sendPreviewToGateway');

// 導入系統配置工具
const { initDB: initSysConfigUtils } = require('./utils/sysConfigUtils');

// 導入網關 API 中的 JWT 密鑰設置函數
const { setJwtSecret } = require('./routes/gatewayApi');

const app = express();
const server = http.createServer(app);  // 創建 HTTP 服務器
const port = process.env.PORT || 3001;

// 設置開發環境
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'development';
  console.log('設置環境為開發模式: NODE_ENV=development');
}

// JWT 密鑰配置
const jwtSecret = process.env.JWT_SECRET || 'your_jwt_secret_here'; // 應從環境變數獲取

// 獲取本機 IP 地址
function getLocalIpAddress() {
  const os = require('os');
  const interfaces = os.networkInterfaces();

  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      // 跳過內部地址和非 IPv4 地址
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost'; // 如果找不到外部 IP，回退到 localhost
}

const localIp = getLocalIpAddress();
console.log(`檢測到本機 IP 地址: ${localIp}`);

// 自定義 CORS 中間件
app.use((req, res, next) => {
  // 允許來自前端的請求（支持多個端口和多個主機）
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    `http://${localIp}:5173`,
    `http://${localIp}:5174`,
    `http://${localIp}:5175`
  ];

  const origin = req.headers.origin;

  // 檢查是否為允許的來源
  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else if (origin) {
    // 對於開發環境，動態允許同網段的 IP 地址
    const originUrl = new URL(origin);
    const originHost = originUrl.hostname;

    // 檢查是否為本機 IP 或同網段 IP（192.168.x.x, 10.x.x.x, 172.16-31.x.x）
    const isLocalNetwork =
      originHost === 'localhost' ||
      originHost === '127.0.0.1' ||
      originHost === localIp ||
      /^192\.168\.\d+\.\d+$/.test(originHost) ||
      /^10\.\d+\.\d+\.\d+$/.test(originHost) ||
      /^172\.(1[6-9]|2\d|3[01])\.\d+\.\d+$/.test(originHost);

    if (isLocalNetwork && (originUrl.port === '5173' || originUrl.port === '5174' || originUrl.port === '5175')) {
      res.header('Access-Control-Allow-Origin', origin);
      console.log(`允許來自同網段的請求: ${origin}`);
    } else {
      console.log(`拒絕來自未知來源的請求: ${origin}`);
      // 不設置 Access-Control-Allow-Origin，讓瀏覽器阻止請求
    }
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');

  // 處理 OPTIONS 請求
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }

  // 記錄請求信息
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url} - Origin: ${origin}`);

  next();
});

// 增加請求體大小限制以支持大型模板（包含圖片）
app.use(express.json({
  limit: '50mb',
  // 添加錯誤處理
  verify: (req, res, buf) => {
    if (buf.length > 50 * 1024 * 1024) { // 50MB
      const error = new Error('請求體太大');
      error.status = 413;
      error.type = 'entity.too.large';
      throw error;
    }
  }
}));
app.use(express.urlencoded({
  limit: '50mb',
  extended: true,
  verify: (req, res, buf) => {
    if (buf.length > 50 * 1024 * 1024) { // 50MB
      const error = new Error('請求體太大');
      error.status = 413;
      error.type = 'entity.too.large';
      throw error;
    }
  }
}));
app.use(cookieParser()); // 解析 cookie
app.use(createTemplateValidatorMiddleware()); // 使用模板驗證中間件

// MongoDB 連接
// 使用環境變數或默認本地 MongoDB
const uri = process.env.MONGO_URI || 'mongodb://127.0.0.1:27017';
const dbName = process.env.MONGO_DB || 'resourceManagement';
let client = null;
let gridFSBucket = null;

// 連接數據庫並返回客戶端和數據庫物件
async function connectDB() {
  try {
    if (!client) {
      console.log('嘗試連接 MongoDB...');
      client = new MongoClient(uri, {
        serverSelectionTimeoutMS: 5000, // 5秒超時
        connectTimeoutMS: 5000,
      });
      await client.connect();
      console.log('MongoDB 連接成功');
      const db = client.db(dbName);

      // 檢查數據庫是否存在
      const admin = client.db().admin();
      const dbs = await admin.listDatabases();
      const dbExists = dbs.databases.some(d => d.name === dbName);
      console.log(`數據庫 ${dbName} ${dbExists ? '已存在' : '不存在'}`);

      // 檢查必要的集合是否存在
      const collections = await db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);
      console.log('現有集合:', collectionNames.join(', '));

      // 確保必要的集合存在
      const requiredCollections = ['users', 'roles', 'permissions'];
      for (const collName of requiredCollections) {
        if (!collectionNames.includes(collName)) {
          console.log(`創建集合: ${collName}`);
          await db.createCollection(collName);
        }
      }

      // 檢查是否有管理員用戶
      const usersCount = await db.collection('users').countDocuments();
      console.log(`用戶數量: ${usersCount}`);

      gridFSBucket = new GridFSBucket(db);
    }
    return { client, db: client.db(dbName), gridFSBucket };
  } catch (error) {
    console.error('MongoDB 連接錯誤:', error);
    throw error;
  }
}

// 添加一個不需要認證的測試路由
app.get('/dbtest', async (req, res) => {
  try {
    console.log('收到數據庫測試請求');

    // 獲取數據庫連接
    const { db } = await connectDB();

    // 檢查數據庫連接
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);

    // 檢查用戶集合
    const usersCount = await db.collection('users').countDocuments();

    console.log('數據庫測試成功:', { collections: collectionNames, usersCount });

    res.json({
      status: 'ok',
      message: '數據庫連接正常',
      collections: collectionNames,
      usersCount
    });
  } catch (error) {
    console.error('數據庫測試錯誤:', error);
    res.status(500).json({ error: '數據庫測試失敗: ' + error.message });
  }
});

// 中間件：將數據庫連接添加到請求對象
app.use(async (req, res, next) => {
  try {
    const { db } = await connectDB();
    req.db = db;
    next();
  } catch (error) {
    console.error('數據庫連接錯誤:', error);
    res.status(500).json({ error: '數據庫連接錯誤' });
  }
});

// 初始化所有 API 的數據庫連接
// 將 connectDB 函數共享給 API 路由
initDataFieldApi(connectDB);
initStoreDataApi(connectDB);
initSystemSpecificDataApi(connectDB);
initStoreApi(connectDB);
initSysConfigApi(connectDB);
initFileApi(connectDB);
initBugReportApi(connectDB);
initTemplateApi(connectDB);
initDeviceApi(connectDB);
initGatewayApi(connectDB);
initRefreshPlanApi(connectDB);
initSoftwareApi(connectDB);

// 初始化用戶認證和權限管理相關路由的數據庫連接
initAuthApi(connectDB);
initUserApi(connectDB);
initRoleApi(connectDB);
initPermissionApi(connectDB);

// 初始化 WebSocket 服務的數據庫連接
initWebSocketServiceDB(connectDB);

// 初始化設備狀態服務的數據庫連接
initDeviceStatusServiceDB(connectDB);

// 初始化發送預覽圖到網關服務的數據庫連接
sendPreviewToGateway.initDB(connectDB);

// 初始化系統配置工具的數據庫連接
initSysConfigUtils(connectDB);

// 初始化AI相關服務的數據庫連接
const aiService = require('./services/aiService');
const taskExecutor = require('./services/taskExecutor');
aiService.initDB(connectDB);
taskExecutor.initDB(connectDB);

// 初始化刷圖計畫相關服務
const taskScheduler = require('./services/taskScheduler');
const executionEngine = require('./services/executionEngine');

// 設置執行引擎的數據庫連接
executionEngine.setDbConnection(connectDB);

// 設置任務調度器的執行引擎
taskScheduler.setExecutionEngine(executionEngine);

// 設置網關 API 的 JWT 密鑰
setJwtSecret(jwtSecret);

// 添加一個測試路由
app.get('/test', async (req, res) => {
  try {
    const { db } = await connectDB();

    // 檢查數據庫連接
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);

    // 檢查用戶集合
    const usersCount = await db.collection('users').countDocuments();

    res.json({
      status: 'ok',
      message: '數據庫連接正常',
      collections: collectionNames,
      usersCount
    });
  } catch (error) {
    console.error('測試路由錯誤:', error);
    res.status(500).json({ error: '測試失敗: ' + error.message });
  }
});

// 使用 API 路由
app.use('/api', dataFieldApiRouter);
app.use('/api', storeDataApiRouter);
app.use('/api', systemSpecificDataApiRouter);
app.use('/api', storeApiRouter);
app.use('/api', sysConfigApiRouter);
app.use('/api', fileApiRouter);
app.use('/api', bugReportApiRouter);
app.use('/api', templateApiRouter);
app.use('/api', deviceApiRouter);
app.use('/api', gatewayApiRouter);
app.use('/api', refreshPlanApiRouter);
app.use('/api', softwareApiRouter);
app.use('/api', templateValidatorApiRouter);
app.use('/api/code-preview', codePreviewApiRouter);

// 使用用戶認證和權限管理相關路由
app.use('/api', authApiRouter);
app.use('/api', userApiRouter);
app.use('/api', roleApiRouter);
app.use('/api', permissionApiRouter);

// 使用AI助手相關路由
app.use('/api/ai-assistant', aiAssistantApiRouter);

// 啟動服務器函數
async function startServer() {
  try {
    // 確保數據庫連接成功
    await connectDB();
    console.log('數據庫連接驗證成功');

    // 啟動服務器，監聽所有網路介面
    server.listen(port, '0.0.0.0', async (error) => {
      if (error) {
        console.error('服務器監聽失敗:', error);
        process.exit(1);
      }

      console.log(`後端服務運行在:`);
      console.log(`  - 本機訪問: http://localhost:${port}`);
      console.log(`  - 網路訪問: http://${localIp}:${port}`);
      console.log(`測試連接:`);
      console.log(`  - http://localhost:${port}/dbtest`);
      console.log(`  - http://${localIp}:${port}/dbtest`);

      try {
        // 初始化 WebSocket 服務（現在是 async 函數）
        await initWebSocketServer(server, jwtSecret);

        // 設置 WebSocket 心跳檢測
        setupHeartbeatCheck();

        // 啟動設備狀態自動檢查服務
        startDeviceStatusChecker();

        // 啟動任務調度器
        try {
          await taskScheduler.start();
          console.log('刷圖計畫任務調度器已啟動');
        } catch (error) {
          console.error('啟動任務調度器失敗:', error);
        }

        console.log(`WebSocket 服務已啟動，連接格式:`);
        console.log(`  - ws://localhost:${port}/ws/store/{storeId}/gateway/{gatewayId}?token={jwt_token}`);
        console.log(`  - ws://${localIp}:${port}/ws/store/{storeId}/gateway/{gatewayId}?token={jwt_token}`);
        console.log(`測試 WebSocket: node tests/test-ws-client.js [gatewayId] [storeId]`);
      } catch (wsError) {
        console.error('WebSocket 服務初始化失敗:', wsError);
        // 不退出，讓 HTTP 服務繼續運行
      }
    });

    // 添加服務器錯誤處理
    server.on('error', (error) => {
      console.error('服務器錯誤:', error);
    });

  } catch (error) {
    console.error('服務器啟動失敗:', error);
    process.exit(1);
  }
}

// 啟動服務器
startServer();
