import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Play, 
  Edit, 
  BarChart3, 
  MoreHorizontal, 
  Calendar,
  Target,
  Clock,
  TrendingUp,
  Trash2,
  Power,
  PowerOff
} from 'lucide-react';
import { RefreshPlan, PlanAction } from '../types/refreshPlan';
import { formatExecuteTime, calculateSuccessRate } from '../services/refreshPlanApi';

interface RefreshPlanCardProps {
  plan: RefreshPlan;
  onAction: (action: PlanAction, planId: string) => void;
}

// 狀態指示器組件
const StatusIndicator: React.FC<{ status: RefreshPlan['status'] }> = ({ status }) => {
  const { t } = useTranslation();
  
  const statusConfig = {
    running: { color: 'bg-green-500', text: '運行中', icon: '🟢' },
    active: { color: 'bg-blue-500', text: '已啟用', icon: '🔵' },
    inactive: { color: 'bg-gray-400', text: '已停用', icon: '⚪' },
    error: { color: 'bg-red-500', text: '錯誤', icon: '🔴' },
  };

  const config = statusConfig[status];

  return (
    <div className="flex items-center">
      <span className="mr-2">{config.icon}</span>
      <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${config.color}`}>
        {config.text}
      </span>
    </div>
  );
};

// 目標對象顯示組件
const TargetDisplay: React.FC<{ targetSelection: RefreshPlan['targetSelection'] }> = ({ targetSelection }) => {
  const { t } = useTranslation();
  
  if (targetSelection.type === 'mac_addresses') {
    const count = targetSelection.macAddresses?.length || 0;
    return (
      <div className="flex items-center text-sm text-gray-600">
        <Target className="w-4 h-4 mr-1" />
        <span>指定MAC ({count}台設備)</span>
      </div>
    );
  } else {
    const count = targetSelection.storeDataIds?.length || 0;
    return (
      <div className="flex items-center text-sm text-gray-600">
        <Target className="w-4 h-4 mr-1" />
        <span>門店數據 ({count}項數據)</span>
      </div>
    );
  }
};

// 下次執行時間顯示組件
const NextRunDisplay: React.FC<{ nextRun?: string; trigger: RefreshPlan['trigger']; enabled: boolean }> = ({ nextRun, trigger, enabled }) => {
  const { t } = useTranslation();

  // 如果計畫未啟用，顯示未安排執行
  if (!enabled) {
    return (
      <div className="flex items-center text-sm text-gray-500">
        <Clock className="w-4 h-4 mr-1" />
        <span>未安排執行</span>
      </div>
    );
  }

  // 如果沒有 nextRun 時間
  if (!nextRun) {
    // 對於單次執行，檢查執行時間是否已過
    if (trigger.type === 'once' && trigger.executeDate && trigger.executeTime) {
      const executeDateTime = new Date(`${trigger.executeDate} ${trigger.executeTime}`);
      const now = new Date();

      if (executeDateTime <= now) {
        return (
          <div className="flex items-center text-sm text-gray-500">
            <Clock className="w-4 h-4 mr-1" />
            <span>單次執行已完成</span>
          </div>
        );
      }
    }

    return (
      <div className="flex items-center text-sm text-gray-500">
        <Clock className="w-4 h-4 mr-1" />
        <span>未安排執行</span>
      </div>
    );
  }

  const nextRunDate = new Date(nextRun);
  const now = new Date();
  const isToday = nextRunDate.toDateString() === now.toDateString();
  const isTomorrow = nextRunDate.toDateString() === new Date(now.getTime() + 24 * 60 * 60 * 1000).toDateString();

  let displayText = '';
  if (isToday) {
    displayText = `今天 ${nextRunDate.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' })}`;
  } else if (isTomorrow) {
    displayText = `明天 ${nextRunDate.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' })}`;
  } else {
    displayText = nextRunDate.toLocaleString('zh-TW', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  return (
    <div className="flex items-center text-sm text-gray-600">
      <Clock className="w-4 h-4 mr-1" />
      <span>下次執行: {displayText}</span>
    </div>
  );
};

// 統計信息顯示組件
const StatisticsDisplay: React.FC<{ statistics: RefreshPlan['statistics'] }> = ({ statistics }) => {
  const successRate = calculateSuccessRate(statistics.successRuns, statistics.totalRuns);
  
  return (
    <div className="flex items-center text-sm text-gray-600">
      <TrendingUp className="w-4 h-4 mr-1" />
      <span>成功率: {successRate} ({statistics.successRuns}/{statistics.totalRuns})</span>
    </div>
  );
};

// 操作按鈕組件
const ActionButtons: React.FC<{ 
  plan: RefreshPlan; 
  onAction: (action: PlanAction, planId: string) => void;
}> = ({ plan, onAction }) => {
  const { t } = useTranslation();
  const [showMoreMenu, setShowMoreMenu] = useState(false);

  const handleExecute = () => {
    if (plan.status === 'running') {
      return; // 正在運行中，不允許執行
    }
    onAction('execute', plan._id);
  };

  const handleToggle = () => {
    onAction('toggle', plan._id);
  };

  return (
    <div className="flex items-center space-x-2">
      {/* 立即執行按鈕 */}
      <button
        onClick={handleExecute}
        disabled={plan.status === 'running'}
        className={`flex items-center px-3 py-1.5 rounded text-sm font-medium transition-colors ${
          plan.status === 'running'
            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
            : 'bg-green-500 text-white hover:bg-green-600'
        }`}
        title={plan.status === 'running' ? '計畫正在執行中' : '立即執行'}
      >
        <Play className="w-4 h-4 mr-1" />
        立即執行
      </button>

      {/* 編輯按鈕 */}
      <button
        onClick={() => onAction('edit', plan._id)}
        className="flex items-center px-3 py-1.5 bg-blue-500 text-white rounded text-sm font-medium hover:bg-blue-600 transition-colors"
        title="編輯計畫"
      >
        <Edit className="w-4 h-4 mr-1" />
        編輯
      </button>

      {/* 統計按鈕 */}
      <button
        onClick={() => onAction('statistics', plan._id)}
        className="flex items-center px-3 py-1.5 bg-purple-500 text-white rounded text-sm font-medium hover:bg-purple-600 transition-colors"
        title="查看統計"
      >
        <BarChart3 className="w-4 h-4 mr-1" />
        統計
      </button>

      {/* 更多操作菜單 */}
      <div className="relative">
        <button
          onClick={() => setShowMoreMenu(!showMoreMenu)}
          className="flex items-center px-3 py-1.5 bg-gray-500 text-white rounded text-sm font-medium hover:bg-gray-600 transition-colors"
          title="更多操作"
        >
          <MoreHorizontal className="w-4 h-4" />
        </button>

        {showMoreMenu && (
          <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
            <button
              onClick={() => {
                handleToggle();
                setShowMoreMenu(false);
              }}
              className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              {plan.enabled ? (
                <>
                  <PowerOff className="w-4 h-4 mr-2" />
                  停用計畫
                </>
              ) : (
                <>
                  <Power className="w-4 h-4 mr-2" />
                  啟用計畫
                </>
              )}
            </button>
            <button
              onClick={() => {
                onAction('delete', plan._id);
                setShowMoreMenu(false);
              }}
              className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              刪除計畫
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// 主要的刷圖計畫卡片組件
export const RefreshPlanCard: React.FC<RefreshPlanCardProps> = ({ plan, onAction }) => {
  const { t } = useTranslation();

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
      {/* 卡片頭部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <div className="flex items-center">
          <Calendar className="w-5 h-5 text-blue-500 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">{plan.name}</h3>
        </div>
        <StatusIndicator status={plan.status} />
      </div>

      {/* 卡片內容 */}
      <div className="p-4">
        {/* 計畫描述 - 始終保留此區域以保持卡片一致性 */}
        <div className="mb-3 min-h-[1.25rem]">
          {plan.description ? (
            <p className="text-sm text-gray-600">{plan.description}</p>
          ) : (
            <p className="text-sm text-gray-400 italic">無描述</p>
          )}
        </div>

        {/* 計畫信息 */}
        <div className="space-y-2 mb-4">
          <TargetDisplay targetSelection={plan.targetSelection} />
          <NextRunDisplay nextRun={plan.nextRun} trigger={plan.trigger} enabled={plan.enabled} />
          <StatisticsDisplay statistics={plan.statistics} />
        </div>

        {/* 操作按鈕 */}
        <ActionButtons plan={plan} onAction={onAction} />
      </div>
    </div>
  );
};

export default RefreshPlanCard;
