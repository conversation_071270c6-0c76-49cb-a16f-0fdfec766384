const express = require('express');
const router = express.Router();
const aiService = require('../services/aiService');
const taskExecutor = require('../services/taskExecutor');
const { authenticate, checkPermission } = require('../middleware/auth');

// AI對話處理
router.post('/chat', authenticate, async (req, res) => {
  try {
    const { message, context } = req.body;
    const user = req.user;

    console.log('收到AI對話請求:', { message, userId: user._id });

    if (!message || typeof message !== 'string') {
      return res.status(400).json({
        intent: 'error',
        response: '請提供有效的消息內容',
        error: '無效的請求參數'
      });
    }

    // 處理用戶輸入
    const aiResponse = await aiService.processUserRequest(message, {
      ...context,
      userId: user._id.toString(),
      username: user.username
    });

    console.log('AI回應:', aiResponse);

    // 如果AI識別出明確的任務且信息完整，執行任務
    if (aiResponse.intent !== 'unknown' &&
        aiResponse.intent !== 'ask_info' &&
        aiResponse.missingFields.length === 0 &&
        aiResponse.confidence > 0.7) {

      // 額外驗證：檢查門店模板是否有門店信息
      const validationResult = validateTaskData(aiResponse);
      if (!validationResult.isValid) {
        console.log('任務數據驗證失敗:', validationResult.missingFields);
        aiResponse.missingFields = validationResult.missingFields;
        aiResponse.response = validationResult.message;
      } else {
        console.log('執行任務:', aiResponse.intent);

        // 檢查用戶權限
        const hasPermission = await checkTaskPermission(aiResponse.intent, user);
        if (!hasPermission) {
          aiResponse.response = '抱歉，您沒有執行此操作的權限。請聯繫管理員。';
          aiResponse.executionResult = {
            success: false,
            error: '權限不足',
            message: '沒有執行權限'
          };
        } else {
          try {
            const executionResult = await taskExecutor.executeTask(aiResponse, user);
            aiResponse.executionResult = executionResult;

            // 如果執行成功，更新回應消息
            if (executionResult.success) {
              aiResponse.response = executionResult.message;
            } else {
              aiResponse.response = `執行失敗: ${executionResult.error}`;
            }
          } catch (error) {
            console.error('任務執行錯誤:', error);
            aiResponse.executionResult = {
              success: false,
              error: error.message,
              message: '任務執行失敗'
            };
            aiResponse.response = `執行失敗: ${error.message}`;
          }
        }
      }
    }

    res.json(aiResponse);
  } catch (error) {
    console.error('AI對話處理失敗:', error);
    res.status(500).json({
      intent: 'error',
      response: '抱歉，處理您的請求時發生錯誤，請稍後重試。',
      error: error.message
    });
  }
});

// 測試AI連接
router.post('/test', authenticate, checkPermission(['system:view']), async (req, res) => {
  try {
    console.log('收到AI連接測試請求');

    const testResult = await aiService.testConnection();
    console.log('AI測試結果:', testResult);

    if (testResult.success) {
      res.json({
        success: true,
        message: testResult.message || 'AI服務連接正常',
        response: testResult.response
      });
    } else {
      res.status(500).json({
        success: false,
        message: testResult.message || 'AI服務連接失敗',
        error: testResult.response
      });
    }
  } catch (error) {
    console.error('AI連接測試異常:', error);
    res.status(500).json({
      success: false,
      message: `AI服務連接失敗: ${error.message}`,
      error: error.message
    });
  }
});

// 獲取AI系統狀態
router.get('/status', authenticate, checkPermission(['system:view']), async (req, res) => {
  try {
    const aiConfig = await aiService.getAIConfig();

    const status = {
      configured: !!(aiConfig.enabled && aiConfig.geminiApiKey),
      enabled: aiConfig.enabled,
      model: aiConfig.model,
      initialized: aiService.isInitialized,
      timestamp: Date.now()
    };

    res.json(status);
  } catch (error) {
    console.error('獲取AI狀態失敗:', error);
    res.status(500).json({
      error: '獲取AI狀態失敗',
      message: error.message
    });
  }
});

// 獲取AI自我介紹
router.get('/introduction', async (req, res) => {
  try {
    const introduction = `您好！我是EPD Agent。我可以幫助您：

1. 🏪 創建門店 - 協助建立新門店
2. 📄 創建模板 - 協助建立EPD模板
3. 👤 創建帳號 - 協助建立新用戶帳號
4. 📅 建立刷圖計畫 - 協助設定定時觸發的設備刷新計畫
5. ⚡ 建立條件刷圖 - 協助建立自動執行設備刷新任務條件
6. 📊 建立數據欄位/資料 - 協助設計和建立門店專屬的數據結構
7. 🎨 協助模板設計 - 提供模板佈局建議和設計指導並自動化建立

請告訴我您需要什麼幫助？`;

    res.json({ introduction });
  } catch (error) {
    console.error('獲取AI介紹失敗:', error);
    res.status(500).json({
      error: '獲取AI介紹失敗',
      message: error.message
    });
  }
});

// 獲取快速範例
router.get('/quick-example', async (req, res) => {
  try {
    const example = aiService.generateStoreExample();
    res.json({ example });
  } catch (error) {
    console.error('獲取快速範例失敗:', error);
    res.status(500).json({
      error: '獲取快速範例失敗',
      message: error.message
    });
  }
});

// 重新初始化AI服務
router.post('/reinitialize', authenticate, checkPermission(['system:update']), async (req, res) => {
  try {
    console.log('重新初始化AI服務');
    
    // 重置初始化狀態
    aiService.isInitialized = false;
    aiService.genAI = null;
    aiService.model = null;
    aiService.config = null;
    
    // 重新初始化
    await aiService.initialize();
    
    res.json({
      success: true,
      message: 'AI服務重新初始化成功'
    });
  } catch (error) {
    console.error('重新初始化AI服務失敗:', error);
    res.status(500).json({
      success: false,
      message: 'AI服務重新初始化失敗',
      error: error.message
    });
  }
});

// 驗證任務數據完整性
function validateTaskData(aiResponse) {
  const { intent, extractedData } = aiResponse;

  if (intent === 'create_template') {
    const missingFields = [];

    // 檢查基本必填字段
    if (!extractedData.templateName) {
      missingFields.push('templateName');
    }
    if (!extractedData.screen_size) {
      missingFields.push('screen_size');
    }
    if (!extractedData.template_type) {
      missingFields.push('template_type');
    }

    // 如果是門店模板，檢查門店信息
    if (extractedData.template_type === 'store') {
      if (!extractedData.store_name && !extractedData.store_id) {
        missingFields.push('store_name');
      }
    }

    if (missingFields.length > 0) {
      let message = '還需要以下信息：';
      if (missingFields.includes('templateName')) {
        message += ' 模板名稱';
      }
      if (missingFields.includes('screen_size')) {
        message += ' 螢幕尺寸';
      }
      if (missingFields.includes('template_type')) {
        message += ' 模板類型（系統模板或門店模板）';
      }
      if (missingFields.includes('store_name')) {
        message += ' 門店名稱（因為這是門店模板）';
      }

      return {
        isValid: false,
        missingFields,
        message: message + '。'
      };
    }
  }

  // 其他任務類型的驗證可以在這裡添加

  return {
    isValid: true,
    missingFields: [],
    message: ''
  };
}

// 檢查任務權限
async function checkTaskPermission(intent, user) {
  try {
    // 獲取用戶權限 - 暫時返回true，稍後實現完整的權限檢查
    return true;
  } catch (error) {
    console.error('檢查權限失敗:', error);
    return false;
  }
}

// 獲取AI對話歷史（可選功能）
router.get('/history', authenticate, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const user = req.user;
    
    // 這裡可以實現對話歷史記錄功能
    // 目前返回空數組
    res.json({
      conversations: [],
      total: 0,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('獲取對話歷史失敗:', error);
    res.status(500).json({
      error: '獲取對話歷史失敗',
      message: error.message
    });
  }
});

// 清除對話歷史（可選功能）
router.delete('/history', authenticate, async (req, res) => {
  try {
    const user = req.user;
    
    // 這裡可以實現清除對話歷史功能
    // 目前直接返回成功
    res.json({
      success: true,
      message: '對話歷史已清除'
    });
  } catch (error) {
    console.error('清除對話歷史失敗:', error);
    res.status(500).json({
      error: '清除對話歷史失敗',
      message: error.message
    });
  }
});

module.exports = router;
