// 為測試門店添加門店數據和模板數據
import fetch from 'node-fetch';
// 使用時間戳作為 ID

// 配置
const API_BASE_URL = 'http://localhost:3001/api';
let TOKEN = '';

// 通用請求函數
async function makeRequest(endpoint, method = 'GET', data = null) {
  const url = `${API_BASE_URL}/${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': `token=${TOKEN}`
    },
    credentials: 'include'
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);

    // 檢查響應狀態
    if (!response.ok) {
      let errorMessage = `API error: ${response.status}`;

      try {
        const errorData = await response.json();
        errorMessage += ` - ${errorData.error || 'Unknown error'}`;
      } catch (jsonError) {
        errorMessage += ' - Could not parse error response';
      }

      throw new Error(errorMessage);
    }

    // 嘗試解析 JSON 響應
    try {
      const responseData = await response.json();
      return responseData;
    } catch (jsonError) {
      console.warn(`Warning: Could not parse JSON response from ${endpoint}`);
      return {}; // 返回空對象而不是拋出錯誤
    }
  } catch (error) {
    console.error(`Error making request to ${endpoint}:`, error.message);
    throw error;
  }
}

// 登入函數
async function login(username, password) {
  try {
    console.log(`Logging in as ${username}...`);
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });

    const cookies = response.headers.get('set-cookie');
    if (cookies) {
      // 從 cookie 中提取 token
      const tokenMatch = cookies.match(/token=([^;]+)/);
      if (tokenMatch && tokenMatch[1]) {
        TOKEN = tokenMatch[1];
        console.log('Login successful, token obtained');
        return true;
      }
    }

    const data = await response.json();
    if (data.token) {
      TOKEN = data.token;
      console.log('Login successful, token obtained from response body');
      return true;
    }

    throw new Error('Failed to extract token from response');
  } catch (error) {
    console.error('Login failed:', error.message);
    return false;
  }
}

// 獲取所有門店
async function getAllStores() {
  try {
    console.log('Getting all stores...');
    const response = await makeRequest('stores');

    // 檢查響應格式 - 處理不同的可能響應結構
    let stores = [];

    if (Array.isArray(response)) {
      // 如果響應直接是數組
      stores = response;
    } else if (response && typeof response === 'object') {
      // 如果響應是對象，嘗試獲取 stores 屬性
      if (Array.isArray(response.stores)) {
        stores = response.stores;
      } else if (response.data && Array.isArray(response.data)) {
        stores = response.data;
      }
    }

    console.log(`Found ${stores.length} stores`);
    return stores;
  } catch (error) {
    console.error('Failed to get stores:', error.message);
    return [];
  }
}

// 創建門店數據
async function createStoreData(storeId, data) {
  try {
    console.log(`Creating store data for store ${storeId}...`);
    // 確保 storeId 作為查詢參數傳遞
    const result = await makeRequest(`storeData?storeId=${storeId}`, 'POST', data);
    console.log(`Store data created successfully for store ${storeId}`);
    return result;
  } catch (error) {
    console.error(`Failed to create store data for store ${storeId}:`, error.message);
    return null;
  }
}

// 創建模板
async function createTemplate(template) {
  try {
    console.log(`Creating template: ${template.name}...`);
    const result = await makeRequest('templates', 'POST', template);
    console.log(`Template created successfully: ${template.name}`);
    return result;
  } catch (error) {
    console.error(`Failed to create template ${template.name}:`, error.message);
    return null;
  }
}

// 生成門店數據
async function generateStoreData() {
  try {
    // 登入
    const loginSuccess = await login('root', '123456789');
    if (!loginSuccess) {
      throw new Error('Login failed, cannot proceed');
    }

    // 獲取所有門店
    const stores = await getAllStores();
    if (stores.length === 0) {
      throw new Error('No stores found, please run generate-test-data.js first');
    }

    // 找到三個測試門店
    const store1 = stores.find(s => s.id === 'TP001'); // 台北總店
    const store2 = stores.find(s => s.id === 'TC001'); // 台中分店
    const store3 = stores.find(s => s.id === 'KH001'); // 高雄分店

    if (!store1 || !store2 || !store3) {
      throw new Error('Test stores not found, please run generate-test-data.js first');
    }

    console.log('Found test stores:', {
      'TP001': store1.name,
      'TC001': store2.name,
      'KH001': store3.name
    });

    // 為台北總店添加1筆門店數據
    console.log('\n=== Creating Store Data for 台北總店 ===');
    await createStoreData(store1.id, {
      id: '台北總店商品A',
      name: '台北總店商品A',
      description: '台北總店的第一筆商品數據',
      price: '1000',
      quantity: '50',
      date: '2023-05-01'
    });

    // 為台中分店添加2筆門店數據
    console.log('\n=== Creating Store Data for 台中分店 ===');
    await createStoreData(store2.id, {
      id: '台中分店商品A',
      name: '台中分店商品A',
      description: '台中分店的第一筆商品數據',
      price: '800',
      quantity: '30',
      date: '2023-05-02'
    });

    await createStoreData(store2.id, {
      id: '台中分店商品B',
      name: '台中分店商品B',
      description: '台中分店的第二筆商品數據',
      price: '1200',
      quantity: '20',
      date: '2023-05-03'
    });

    // 為高雄分店添加3筆門店數據
    console.log('\n=== Creating Store Data for 高雄分店 ===');
    await createStoreData(store3.id, {
      id: '高雄分店商品A',
      name: '高雄分店商品A',
      description: '高雄分店的第一筆商品數據',
      price: '750',
      quantity: '40',
      date: '2023-05-04'
    });

    await createStoreData(store3.id, {
      id: '高雄分店商品B',
      name: '高雄分店商品B',
      description: '高雄分店的第二筆商品數據',
      price: '950',
      quantity: '25',
      date: '2023-05-05'
    });

    await createStoreData(store3.id, {
      id: '高雄分店商品C',
      name: '高雄分店商品C',
      description: '高雄分店的第三筆商品數據',
      price: '1100',
      quantity: '15',
      date: '2023-05-06'
    });

    // 創建系統模板
    console.log('\n=== Creating System Templates ===');
    // 使用更穩定的 ID 生成方式，避免時間戳衝突
    const systemTemplateId = `system-template-${Math.floor(Math.random() * 10000)}-${Date.now()}`;
    await createTemplate({
      id: systemTemplateId,
      name: '系統模板A',
      type: 'Single data template',
      screenSize: '296x128',
      color: 'Black & White',
      orientation: 'landscape',
      elements: [
        {
          id: 'text-1',
          type: 'text',
          properties: {
            x: 10,
            y: 10,
            width: 276,
            height: 30,
            content: '系統模板A - 標題',
            style: {
              fontSize: 16,
              fontWeight: 'bold',
              textAlign: 'center'
            }
          }
        },
        {
          id: 'text-2',
          type: 'text',
          properties: {
            x: 10,
            y: 50,
            width: 276,
            height: 68,
            content: '這是一個系統模板，所有門店都可以使用',
            style: {
              fontSize: 12,
              textAlign: 'center'
            }
          }
        }
      ],
      isSystemTemplate: true
    });

    // 為台北總店創建1個模板
    console.log('\n=== Creating Templates for 台北總店 ===');
    const tp1TemplateId = `tp-template-${Math.floor(Math.random() * 10000)}-${Date.now()}`;
    await createTemplate({
      id: tp1TemplateId,
      name: '台北總店模板A',
      type: 'Single data template',
      screenSize: '296x128',
      color: 'Black & White',
      orientation: 'landscape',
      elements: [
        {
          id: 'text-1',
          type: 'text',
          properties: {
            x: 10,
            y: 10,
            width: 276,
            height: 30,
            content: '台北總店專用模板',
            style: {
              fontSize: 16,
              fontWeight: 'bold',
              textAlign: 'center'
            }
          }
        },
        {
          id: 'text-2',
          type: 'text',
          properties: {
            x: 10,
            y: 50,
            width: 276,
            height: 68,
            content: '這是台北總店的專用模板',
            style: {
              fontSize: 12,
              textAlign: 'center'
            }
          }
        }
      ],
      storeId: store1.id,
      isSystemTemplate: false
    });

    // 為台中分店創建2個模板
    console.log('\n=== Creating Templates for 台中分店 ===');
    const tc1TemplateId = `tc-template-1-${Math.floor(Math.random() * 10000)}-${Date.now()}`;
    await createTemplate({
      id: tc1TemplateId,
      name: '台中分店模板A',
      type: 'Single data template',
      screenSize: '296x128',
      color: 'Black & White',
      orientation: 'landscape',
      elements: [
        {
          id: 'text-1',
          type: 'text',
          properties: {
            x: 10,
            y: 10,
            width: 276,
            height: 30,
            content: '台中分店專用模板A',
            style: {
              fontSize: 16,
              fontWeight: 'bold',
              textAlign: 'center'
            }
          }
        }
      ],
      storeId: store2.id,
      isSystemTemplate: false
    });

    const tc2TemplateId = `tc-template-2-${Math.floor(Math.random() * 10000)}-${Date.now()}`;
    await createTemplate({
      id: tc2TemplateId,
      name: '台中分店模板B',
      type: 'Single data template',
      screenSize: '400x300',
      color: 'Black & White & Red',
      orientation: 'portrait',
      elements: [
        {
          id: 'text-1',
          type: 'text',
          properties: {
            x: 10,
            y: 10,
            width: 380,
            height: 30,
            content: '台中分店專用模板B',
            style: {
              fontSize: 16,
              fontWeight: 'bold',
              textAlign: 'center'
            }
          }
        }
      ],
      storeId: store2.id,
      isSystemTemplate: false
    });

    // 為高雄分店創建3個模板
    console.log('\n=== Creating Templates for 高雄分店 ===');
    const kh1TemplateId = `kh-template-1-${Math.floor(Math.random() * 10000)}-${Date.now()}`;
    await createTemplate({
      id: kh1TemplateId,
      name: '高雄分店模板A',
      type: 'Single data template',
      screenSize: '296x128',
      color: 'Black & White',
      orientation: 'landscape',
      elements: [
        {
          id: 'text-1',
          type: 'text',
          properties: {
            x: 10,
            y: 10,
            width: 276,
            height: 30,
            content: '高雄分店專用模板A',
            style: {
              fontSize: 16,
              fontWeight: 'bold',
              textAlign: 'center'
            }
          }
        }
      ],
      storeId: store3.id,
      isSystemTemplate: false
    });

    const kh2TemplateId = `kh-template-2-${Math.floor(Math.random() * 10000)}-${Date.now()}`;
    await createTemplate({
      id: kh2TemplateId,
      name: '高雄分店模板B',
      type: 'Single data template',
      screenSize: '400x300',
      color: 'Black & White & Red',
      orientation: 'portrait',
      elements: [
        {
          id: 'text-1',
          type: 'text',
          properties: {
            x: 10,
            y: 10,
            width: 380,
            height: 30,
            content: '高雄分店專用模板B',
            style: {
              fontSize: 16,
              fontWeight: 'bold',
              textAlign: 'center'
            }
          }
        }
      ],
      storeId: store3.id,
      isSystemTemplate: false
    });

    const kh3TemplateId = `kh-template-3-${Math.floor(Math.random() * 10000)}-${Date.now()}`;
    await createTemplate({
      id: kh3TemplateId,
      name: '高雄分店模板C',
      type: 'Multiple data template',
      screenSize: '800x480',
      color: 'Black & White & Red',
      orientation: 'landscape',
      elements: [
        {
          id: 'text-1',
          type: 'text',
          properties: {
            x: 10,
            y: 10,
            width: 628,
            height: 40,
            content: '高雄分店專用模板C',
            style: {
              fontSize: 20,
              fontWeight: 'bold',
              textAlign: 'center'
            }
          }
        }
      ],
      storeId: store3.id,
      isSystemTemplate: false
    });

    console.log('\nStore data and template generation completed!');
  } catch (error) {
    console.error('Error generating store data and templates:', error);
  }
}

// 執行
console.log('Starting script...');
generateStoreData().catch(error => {
  console.error('Unhandled error in generateStoreData:', error);
});

// 需要導出一些內容，因為這是一個 ES 模塊
export {};
