import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../../store/authStore';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select';
import { Input } from '../ui/input';
import { Checkbox } from '../ui/checkbox';
import { DialogFooter } from '../ui/dialog';
import { ScrollArea } from '../ui/scroll-area';
import { getAllUsers } from '../../utils/api/userApi';
import { getAllRoles } from '../../utils/api/roleApi';
import { batchCreatePermissions } from '../../utils/api/permissionApi';

// 用戶接口
interface User {
  _id: string;
  username: string;
  name: string;
  status: string;
}

// 角色接口
interface Role {
  _id: string;
  name: string;
  type: 'system' | 'store';
}

interface PermissionBatchFormProps {
  onClose: () => void;
  onSuccess: () => void;
}

export const PermissionBatchForm: React.FC<PermissionBatchFormProps> = ({
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation();
  const { token } = useAuthStore();

  // 狀態
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [formData, setFormData] = useState({
    roleId: '',
    scope: 'system',
    scopeType: 'system' as 'system' | 'store'
  });
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // 獲取用戶列表
  const fetchUsers = async () => {
    try {
      const data = await getAllUsers();
      setUsers(data.users);
    } catch (error) {
      console.error('獲取用戶列表錯誤:', error);
    }
  };

  // 獲取角色列表
  const fetchRoles = async () => {
    try {
      const data = await getAllRoles();
      setRoles(data);
    } catch (error) {
      console.error('獲取角色列表錯誤:', error);
    }
  };

  // 初始化
  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, [token]);

  // 處理用戶選擇
  const handleUserSelect = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUserIds(prev => [...prev, userId]);
    } else {
      setSelectedUserIds(prev => prev.filter(id => id !== userId));
    }
  };

  // 處理全選
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUserIds(filteredUsers.map(user => user._id));
    } else {
      setSelectedUserIds([]);
    }
  };

  // 過濾用戶
  const filteredUsers = users.filter(user => {
    const searchLower = searchTerm.toLowerCase();
    return (
      user.username.toLowerCase().includes(searchLower) ||
      (user.name && user.name.toLowerCase().includes(searchLower))
    );
  });

  // 處理表單提交
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      // 驗證表單
      if (!formData.roleId || !formData.scope || !formData.scopeType || selectedUserIds.length === 0) {
        setError(t('errors.required'));
        return;
      }

      // 批量創建權限分配
      await batchCreatePermissions({
        userIds: selectedUserIds,
        roleId: formData.roleId,
        scope: formData.scope,
        scopeType: formData.scopeType
      });

      // 成功回調
      onSuccess();
    } catch (error: any) {
      console.error('批量添加權限分配錯誤:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4 py-4">
      {/* 錯誤提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-3 text-sm">
          {error}
        </div>
      )}

      <div className="grid grid-cols-2 gap-6">
        {/* 左側：用戶選擇 */}
        <div className="space-y-4">
          <h3 className="font-medium">{t('permission.user')}</h3>

          {/* 搜索框 */}
          <Input
            placeholder={t('common.search')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />

          {/* 用戶列表 */}
          <div className="border rounded-md">
            <div className="p-2 border-b flex items-center">
              <Checkbox
                checked={filteredUsers.length > 0 && selectedUserIds.length === filteredUsers.length}
                onCheckedChange={handleSelectAll}
                className="mr-2"
              />
              <span className="text-sm font-medium">{t('common.selectAll')}</span>
            </div>

            <ScrollArea className="h-60">
              <div className="p-2 space-y-2">
                {filteredUsers.length === 0 ? (
                  <p className="text-center text-gray-500 py-4">{t('common.noData')}</p>
                ) : (
                  filteredUsers.map(user => (
                    <div key={user._id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`user-${user._id}`}
                        checked={selectedUserIds.includes(user._id)}
                        onCheckedChange={(checked) => handleUserSelect(user._id, !!checked)}
                      />
                      <Label htmlFor={`user-${user._id}`} className="flex-1 cursor-pointer">
                        <span className="font-medium">{user.username}</span>
                        {user.name && <span className="ml-2 text-gray-500">({user.name})</span>}
                      </Label>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>

            <div className="p-2 border-t text-sm text-gray-500">
              {t('common.selected')}: {selectedUserIds.length}
            </div>
          </div>
        </div>

        {/* 右側：權限設置 */}
        <div className="space-y-4">
          <h3 className="font-medium">{t('permission.permissions')}</h3>

          {/* 角色選擇 */}
          <div className="space-y-2">
            <Label htmlFor="roleId">
              {t('permission.role')}
            </Label>
            <Select
              value={formData.roleId}
              onValueChange={(value) => setFormData({ ...formData, roleId: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('permission.role')} />
              </SelectTrigger>
              <SelectContent>
                {roles.map(role => (
                  <SelectItem key={role._id} value={role._id}>
                    {role.name} ({role.type === 'system' ? t('permission.system') : t('permission.store')})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 範圍類型選擇 */}
          <div className="space-y-2">
            <Label htmlFor="scopeType">
              {t('permission.scopeType')}
            </Label>
            <Select
              value={formData.scopeType}
              onValueChange={(value) => setFormData({
                ...formData,
                scopeType: value as 'system' | 'store',
                scope: value === 'system' ? 'system' : formData.scope
              })}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('permission.scopeType')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="system">{t('permission.system')}</SelectItem>
                <SelectItem value="store">{t('permission.store')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 範圍輸入 */}
          <div className="space-y-2">
            <Label htmlFor="scope">
              {t('permission.scope')}
            </Label>
            {formData.scopeType === 'system' ? (
              <Select
                value={formData.scope}
                onValueChange={(value) => setFormData({ ...formData, scope: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('permission.scope')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="system">{t('permission.system')}</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <Input
                id="scope"
                value={formData.scope}
                onChange={(e) => setFormData({ ...formData, scope: e.target.value })}
                placeholder={t('permission.scope')}
              />
            )}
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={onClose}>
          {t('common.cancel')}
        </Button>
        <Button onClick={handleSubmit} disabled={loading || selectedUserIds.length === 0}>
          {loading ? t('common.saving') : t('common.save')}
        </Button>
      </DialogFooter>
    </div>
  );
};
