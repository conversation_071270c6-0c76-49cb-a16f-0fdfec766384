# === 必須設置 ===
# 請將此文件複製為 .env 並修改以下值
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# === 端口配置 ===
# 注意：這些是外部訪問端口，容器內部端口固定為 5173 和 3001
FRONTEND_PORT=5173  # 外部訪問前端的端口
SERVER_PORT=3001    # 外部訪問後端的端口  
MONGO_PORT=27017    # 外部訪問 MongoDB 的端口

# === 基本配置 ===
NODE_ENV=development
MONGO_DB=resourceManagement

# === 測試模式配置 ===
# 設置為 dev 啟用測試功能（bug回報等）
TEST_MODE=dev

# === 懸浮球配置 ===
# 設置為 enable 啟用懸浮球功能
FLOATING_BUTTON=enable

# === 重要提醒 ===
# 程式碼中硬編碼了端口 3001 和 5173
# 如需修改，請同時更新程式碼中的端口配置
