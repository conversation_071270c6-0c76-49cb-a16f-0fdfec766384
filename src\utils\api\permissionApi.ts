import { buildEndpointUrl } from './apiConfig';

/**
 * 獲取所有權限分配
 * @param params 查詢參數
 * @returns 權限分配列表和分頁信息
 */
export async function getAllPermissions(params?: {
  userId?: string;
  roleId?: string;
  scopeType?: string;
  scope?: string;
  page?: number;
  limit?: number;
}): Promise<{ permissions: any[]; pagination: any }> {
  try {
    // 構建查詢參數
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    // 構建URL
    const url = queryParams.toString() 
      ? `${buildEndpointUrl('permissions')}?${queryParams}`
      : buildEndpointUrl('permissions');
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });
    
    if (!response.ok) {
      throw new Error(`獲取權限分配列表失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('獲取權限分配列表錯誤:', error);
    throw error;
  }
}

/**
 * 獲取單個權限分配
 * @param id 權限分配ID
 * @returns 權限分配詳情
 */
export async function getPermission(id: string): Promise<any> {
  try {
    const response = await fetch(buildEndpointUrl('permissions', id), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });
    
    if (!response.ok) {
      throw new Error(`獲取權限分配詳情失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`獲取權限分配詳情錯誤 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 創建權限分配
 * @param permissionData 權限分配數據
 * @returns 創建的權限分配
 */
export async function createPermission(permissionData: any): Promise<any> {
  try {
    const response = await fetch(buildEndpointUrl('permissions'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(permissionData),
      credentials: 'include'
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `創建權限分配失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('創建權限分配錯誤:', error);
    throw error;
  }
}

/**
 * 更新權限分配
 * @param id 權限分配ID
 * @param permissionData 更新的權限分配數據
 * @returns 更新後的權限分配
 */
export async function updatePermission(id: string, permissionData: any): Promise<any> {
  try {
    const response = await fetch(buildEndpointUrl('permissions', id), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(permissionData),
      credentials: 'include'
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `更新權限分配失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`更新權限分配錯誤 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 刪除權限分配
 * @param id 權限分配ID
 */
export async function deletePermission(id: string): Promise<void> {
  try {
    const response = await fetch(buildEndpointUrl('permissions', id), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `刪除權限分配失敗: ${response.statusText}`);
    }
  } catch (error) {
    console.error(`刪除權限分配錯誤 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 批量刪除權限分配
 * @param ids 權限分配ID列表
 */
export async function batchDeletePermissions(ids: string[]): Promise<void> {
  try {
    const response = await fetch(buildEndpointUrl('permissions'), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ ids }),
      credentials: 'include'
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `批量刪除權限分配失敗: ${response.statusText}`);
    }
  } catch (error) {
    console.error('批量刪除權限分配錯誤:', error);
    throw error;
  }
}

/**
 * 批量創建權限分配
 * @param batchData 批量創建數據
 * @returns 創建結果
 */
export async function batchCreatePermissions(batchData: {
  userIds: string[];
  roleId: string;
  scope: string;
  scopeType: string;
}): Promise<any> {
  try {
    const response = await fetch(buildEndpointUrl('permissions', 'batch'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(batchData),
      credentials: 'include'
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `批量創建權限分配失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('批量創建權限分配錯誤:', error);
    throw error;
  }
}
