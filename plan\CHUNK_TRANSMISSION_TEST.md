# WebSocket 分片傳輸測試指南

## 概述

本文檔說明如何測試新實作的WebSocket分片傳輸功能。該功能實現了嵌入式Index分片傳輸，可以將大型圖片數據分割成小塊進行傳輸，適用於記憶體受限的Gateway設備。

## 功能特點

### 🎯 核心功能
- **嵌入式Index設計**: Header只發送一次，每個分片前4 bytes包含chunkIndex
- **流量優化**: 相比傳統方案節省38%流量（2% vs 40%開銷）
- **Gateway能力上報**: Gateway在gatewayInfo中上報chunkingSupport能力
- **精確判斷機制**: Server根據Gateway上報的maxChunkSize精確判斷是否啟用分片傳輸
- **ACK等待機制**: 每個分片必須等待Gateway確認後才發送下一個
- **可靠傳輸**: 失敗分片自動重傳，重傳時仍包含完整index信息
- **完美重複檢測**: 基於嵌入的chunkIndex進行O(1)重複檢測

### 🔧 技術特色
1. **動態分片大小**: 根據Gateway能力自動調整(200 bytes - 512KB)
2. **智能門檻**: 安全記憶體限制 = maxMemory × 0.8
3. **ACK超時**: 5秒(平衡效率與可靠性)
4. **最大重試**: 3次(指數退避：1s, 2s, 4s)
5. **總超時**: 5分鐘(大圖片完整傳輸)

## 測試步驟

### 1. 準備環境

確保服務器正在運行：
```bash
cd server
npm start
```

### 2. 運行Gateway模擬器

使用互動式測試客戶端：
```bash
cd server/tests
node test-ws-client-interactive.js
```

按照提示：
1. 登入（用戶名: root, 密碼: 123456789）
2. 選擇門店
3. 選擇或創建網關
4. 等待連接建立

### 3. 觸發分片傳輸

在另一個終端中，可以通過以下方式觸發分片傳輸：

#### 方法1: 使用API
```bash
curl -X POST http://localhost:3001/api/devices/YOUR_DEVICE_ID/send-preview \
  -H "Content-Type: application/json" \
  -H "Cookie: token=YOUR_TOKEN"
```

#### 方法2: 使用測試腳本
```bash
node test-chunk-transmission.js
```

#### 方法3: 通過前端界面
1. 打開前端應用
2. 進入設備管理頁面
3. 選擇一個設備
4. 點擊"發送預覽圖"按鈕

### 4. 觀察測試結果

在Gateway模擬器的控制台中，你應該看到類似以下的輸出：

```
🚀 開始接收分片傳輸: {
  type: 'image_chunk_start',
  chunkId: 'chunk_1703123456789_abc123def',
  deviceMac: '00:11:22:33:44:55',
  imageCode: '12345678',
  totalChunks: 26,
  totalSize: 5120,
  chunkSize: 200,
  indexSize: 4,
  dataType: 'rawdata',
  mode: 'embedded_index'
}

📦 開始接收分片: chunk_1703123456789_abc123def, 總分片數: 26, 設備: 00:11:22:33:44:55
📤 發送開始ACK: ready

📥 收到分片 0: 200 bytes 數據 + 4 bytes index
✅ 分片 0 已儲存，進度: 1/26
📤 發送分片ACK: chunk 0, status: received

📥 收到分片 1: 200 bytes 數據 + 4 bytes index
✅ 分片 1 已儲存，進度: 2/26
📤 發送分片ACK: chunk 1, status: received

...

📥 收到分片 25: 120 bytes 數據 + 4 bytes index
✅ 分片 25 已儲存，進度: 26/26
📤 發送分片ACK: chunk 25, status: received

🎯 所有分片接收完成，開始重組圖片
🎯 圖片重組完成: 5120 bytes

🏁 分片傳輸完成: {
  type: 'image_chunk_complete',
  chunkId: 'chunk_1703123456789_abc123def',
  deviceMac: '00:11:22:33:44:55',
  imageCode: '12345678',
  totalChecksum: 'a1b2c3d4'
}

📊 分片傳輸統計: 26 接收, 0 重複
📤 發送完成ACK: success
```

## 測試場景

### 場景1: 正常分片傳輸
- **條件**: 圖片大小 > Gateway maxChunkSize
- **預期**: 啟用分片傳輸，所有分片正確接收和重組

### 場景2: 直接傳輸
- **條件**: 圖片大小 ≤ Gateway maxChunkSize
- **預期**: 使用直接傳輸，不進行分片

### 場景3: 重複分片處理
- **條件**: 模擬ACK丟失導致重傳
- **預期**: Gateway正確檢測重複分片並回應duplicate狀態

### 場景4: 錯誤恢復
- **條件**: 模擬分片傳輸錯誤
- **預期**: 自動重傳失敗的分片，最多重試3次

### 場景5: Gateway能力檢測
- **條件**: 不同的Gateway能力配置
- **預期**: Server根據Gateway能力正確選擇傳輸方式

## 驗證要點

### ✅ 功能驗證
- [ ] Gateway能力正確上報
- [ ] Server正確判斷是否啟用分片
- [ ] 分片數據包含正確的嵌入式index
- [ ] 所有分片按順序正確接收
- [ ] 重複分片被正確檢測和處理
- [ ] 圖片數據完整重組
- [ ] ACK機制正常工作

### ✅ 性能驗證
- [ ] 分片傳輸比直接傳輸節省流量
- [ ] ACK回應時間 < 1秒
- [ ] 重傳機制有效性 > 98%
- [ ] 大圖片傳輸時間 < 60秒

### ✅ 可靠性驗證
- [ ] 網路不穩定環境下傳輸成功率 > 95%
- [ ] 無記憶體洩漏
- [ ] 錯誤處理機制正常

## 故障排除

### 常見問題

1. **分片傳輸未啟用**
   - 檢查Gateway是否正確上報chunkingSupport
   - 確認圖片大小是否超過maxChunkSize

2. **ACK超時**
   - 檢查網路連接
   - 確認Gateway正確處理分片數據

3. **重組失敗**
   - 檢查是否有分片丟失
   - 確認分片順序是否正確

4. **重複檢測失效**
   - 檢查chunkIndex是否正確嵌入
   - 確認重複檢測邏輯

### 調試技巧

1. **啟用詳細日誌**
   ```javascript
   console.log('分片詳細信息:', {
     chunkIndex,
     dataSize: actualData.length,
     totalProgress: `${receivedCount}/${expectedTotalChunks}`
   });
   ```

2. **檢查數據完整性**
   ```javascript
   const checksum = calculateChecksum(completeData);
   console.log('重組數據校驗碼:', checksum);
   ```

3. **監控記憶體使用**
   ```javascript
   console.log('記憶體使用:', process.memoryUsage());
   ```

## 結論

分片傳輸功能已成功實作並可以進行測試。該功能提供了高效、可靠的大型圖片數據傳輸方案，特別適用於記憶體受限的Gateway設備。通過嵌入式Index設計和完善的錯誤處理機制，確保了傳輸的可靠性和效率。
