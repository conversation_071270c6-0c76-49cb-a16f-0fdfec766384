# 軟體編輯即時更新修復

## 問題描述

用戶反映編輯軟體名稱後，按下保存按鈕，詳細畫面的資訊沒有即時更新，需要跳出詳細頁面再進去才會看到更新後的內容。

## 問題分析

原本的實現中，`SoftwareDetailModal` 組件在保存成功後只調用了 `onUpdate()` 來刷新父組件的軟體列表，但沒有更新當前顯示的軟體物件。這導致：

1. 後端數據已經更新成功
2. 軟體列表也會刷新
3. 但詳細頁面仍然顯示舊的軟體物件資料

## 解決方案

### 1. 修改接口定義

**檔案：** `src/components/system-config/SoftwareDetailModal.tsx`

```typescript
interface SoftwareDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  software: Software;
  onUpdate: (updatedSoftware?: Software) => void; // 允許傳遞更新後的軟體物件
}
```

### 2. 修改保存邏輯

**檔案：** `src/components/system-config/SoftwareDetailModal.tsx`

在 `saveEdit()` 函數中：

```typescript
// 更新本地軟體物件以即時反映變更
const updatedSoftware = {
  ...software,
  name: editName.trim(),
  description: editDescription.trim(),
  lastModified: new Date().toISOString()
};

// 更新原始名稱
setOriginalName(editName.trim());

setIsEditing(false);

// 調用父組件的更新回調，並傳遞更新後的軟體物件
onUpdate(updatedSoftware);
```

### 3. 修改父組件處理邏輯

**檔案：** `src/components/system-config/SoftwareManagementTab.tsx`

新增 `handleSoftwareUpdate` 函數：

```typescript
// 處理軟體更新
const handleSoftwareUpdate = (updatedSoftware?: Software) => {
  if (updatedSoftware) {
    // 如果提供了更新後的軟體物件，更新選中的軟體和列表中的對應項目
    setSelectedSoftware(updatedSoftware);
    setSoftware(prevSoftware => 
      prevSoftware.map(item => 
        item._id === updatedSoftware._id ? updatedSoftware : item
      )
    );
  } else {
    // 如果沒有提供更新後的軟體物件，重新載入整個列表
    loadSoftware();
  }
};
```

並將詳細模態窗口的 `onUpdate` 屬性改為：

```typescript
onUpdate={handleSoftwareUpdate}
```

## 修復效果

修復後的行為：

1. **即時更新**：編輯並保存後，詳細頁面立即顯示更新後的內容
2. **列表同步**：軟體列表中的對應項目也會同步更新
3. **狀態一致**：避免了前端顯示與後端數據不一致的問題
4. **用戶體驗**：用戶不需要重新打開詳細頁面就能看到更新結果

## 技術優勢

1. **性能優化**：避免不必要的 API 調用來重新載入整個列表
2. **狀態管理**：保持前端狀態與後端數據的一致性
3. **用戶體驗**：提供即時反饋，提升操作流暢度
4. **向後兼容**：如果沒有傳遞更新後的軟體物件，仍會執行原有的列表重載邏輯

## 測試步驟

1. **基本編輯測試**：
   - 打開任一軟體的詳細頁面
   - 點擊編輯按鈕
   - 修改軟體名稱和描述
   - 點擊保存
   - 確認詳細頁面立即顯示更新後的內容

2. **列表同步測試**：
   - 在詳細頁面編輯並保存
   - 關閉詳細頁面
   - 確認軟體列表中的對應項目也已更新

3. **錯誤處理測試**：
   - 嘗試保存無效的名稱（空白或重複）
   - 確認錯誤處理正常，不會影響現有數據

## 相關檔案

- `src/components/system-config/SoftwareDetailModal.tsx`
- `src/components/system-config/SoftwareManagementTab.tsx`

## 後續改進

1. **樂觀更新**：可以考慮在 API 調用前就更新 UI，提供更快的響應
2. **錯誤回滾**：如果 API 調用失敗，自動回滾到原始狀態
3. **更新動畫**：添加平滑的更新動畫效果
4. **批量更新**：支援批量編輯多個軟體時的即時更新
