import React from 'react';
import { useTranslation } from 'react-i18next';

interface EmptyPageProps {
  title: string;
}

export const EmptyPage: React.FC<EmptyPageProps> = ({ title }) => {
  const { t } = useTranslation();
  return (
    <div className="flex flex-col items-center justify-center h-full min-h-[calc(100vh-4rem)]">
      <div className="p-8 bg-white rounded-lg shadow-md text-center">
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">{title}</h2>
        <p className="text-gray-600">
          {t('common.pageUnderDevelopment')}
        </p>
      </div>
    </div>
  );
};