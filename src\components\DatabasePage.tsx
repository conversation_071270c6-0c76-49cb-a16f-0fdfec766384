import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Search, Import, Upload, Lightbulb, Plus, Trash2, Grid, Link, Trash, AlertCircle, RefreshCw, Edit, CheckSquare } from 'lucide-react';
import { StoreData, DataField, DataFieldSectionType } from '../types';
import { getAllStoreData, deleteStoreData, syncDataFieldsToStoreData } from '../utils/api/storeDataApi';
import { getAllDataFields } from '../utils/api/dataFieldApi';
import { saveFieldsViewConfig, getFieldsViewConfig } from '../utils/api/sysConfigApi';
import { getAllStores } from '../utils/api/storeApi';
import { AddStoreDataModal } from './AddStoreDataModal';
import { EditStoreDataModal } from './EditStoreDataModal';
import { useTranslation } from 'react-i18next';
import { Store } from '../types/store';
import { showSuccessNotification, showErrorNotification } from '../utils/bubbleNotification';
import { subscribeToStoreDataUpdate, StoreDataUpdateEvent } from '../utils/websocketClient';

interface DatabasePageProps {
  store: Store | null;
}

export function DatabasePage({ store }: DatabasePageProps) {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [storeData, setStoreData] = useState<StoreData[]>([]);
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [syncingFields, setSyncingFields] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showFieldManager, setShowFieldManager] = useState(false);
  const [visibleFields, setVisibleFields] = useState<Record<string, boolean>>({});

  // 新增：用於追蹤選中的項目
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // 欄位管理視窗的 ref
  const fieldManagerRef = useRef<HTMLDivElement>(null);
  const fieldManagerButtonRef = useRef<HTMLButtonElement>(null);

  // 編輯門店資料的狀態
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedStoreData, setSelectedStoreData] = useState<StoreData | null>(null);

  // 即時更新相關狀態
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);

  // 新增：過濾後的數據（搜索結果）
  const filteredStoreData = useMemo(() => {
    if (!searchTerm) return storeData;

    return storeData.filter(item => {
      // 在所有欄位中搜索
      return Object.entries(item).some(([key, value]) => {
        // 忽略 sn 欄位，只在其他欄位中搜索
        if (key === 'sn') return false;

        // 將值轉換為字符串後進行不區分大小寫的搜索
        return String(value).toLowerCase().includes(searchTerm.toLowerCase());
      });
    });
  }, [storeData, searchTerm]);

  // 獲取當前頁的項目
  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredStoreData.slice(startIndex, endIndex);
  };

  // 計算總頁數
  const totalPages = Math.ceil(filteredStoreData.length / itemsPerPage);

  // 點擊外部關閉欄位管理視窗，並自動保存設定
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        showFieldManager &&
        fieldManagerRef.current &&
        !fieldManagerRef.current.contains(event.target as Node) &&
        fieldManagerButtonRef.current &&
        !fieldManagerButtonRef.current.contains(event.target as Node)
      ) {
        // 關閉設定視窗前自動保存設定
        saveFieldsViewSettings(visibleFields); // 傳入當前最新的 visibleFields
        setShowFieldManager(false);
      }
    }

    // 添加事件監聽器
    document.addEventListener('mousedown', handleClickOutside);

    // 清理函數
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showFieldManager, visibleFields]); // 同時監聽 visibleFields 變更
  // 監控選取狀態變化，自動更新全選狀態（只檢查當前頁）
  useEffect(() => {
    const currentPageItems = getCurrentPageItems();
    if (currentPageItems.length === 0) {
      setSelectAll(false);
      return;
    }

    // 檢查當前頁的所有門店資料是否都被選中
    const currentPageIds = currentPageItems.map(item => item.uid).filter(uid => uid !== undefined) as string[];
    const allCurrentPageSelected = currentPageIds.length > 0 &&
      currentPageIds.every(uid => selectedItems.includes(uid));

    setSelectAll(allCurrentPageSelected);
  }, [selectedItems, currentPage, filteredStoreData]);

  // 搜索條件變更時的處理
  useEffect(() => {
    // 重置分頁
    setCurrentPage(1);

    // 保持有效的選取狀態（只保留在篩選結果中的項目）
    if (selectedItems.length > 0) {
      const filteredUids = filteredStoreData.map(item => item.uid).filter(uid => uid !== undefined) as string[];
      const validSelectedItems = selectedItems.filter(uid => filteredUids.includes(uid));

      if (validSelectedItems.length !== selectedItems.length) {
        console.log(`[門店資料篩選選取保持] 保持有效選取項目: ${selectedItems.length} -> ${validSelectedItems.length}`);
        setSelectedItems(validSelectedItems);
      }
    }
  }, [searchTerm, filteredStoreData]);
    // 當門店變化時重新獲取數據
  useEffect(() => {
    if (store?.id) {
      fetchData();
    }
  }, [store?.id]);

  // 門店資料即時更新Hook
  useEffect(() => {
    if (!store?.id || !isRealTimeEnabled) return;

    console.log(`啟用門店資料即時更新: storeId=${store.id}`);

    // 處理門店資料更新事件（保持選取狀態）
    const handleStoreDataUpdate = (event: StoreDataUpdateEvent) => {
      if (event.storeId !== store.id) return;

      console.log(`收到門店資料更新: ${event.storeData.length} 筆資料`);

      // 根據更新類型處理
      if (event.updateType === 'delete') {
        // 刪除操作：從本地狀態中移除
        const deletedUids = event.storeData.map(item => item.uid);
        setStoreData(prevData => prevData.filter(item => !deletedUids.includes(item.uid)));

        // 清理選取狀態中被刪除的項目
        setSelectedItems(prevSelected => prevSelected.filter(uid => !deletedUids.includes(uid)));
      } else {
        // 創建或更新操作：重新獲取數據並保持選取狀態
        fetchDataWithSelectionPreservation();
      }
    };

    // 訂閱門店資料更新
    const unsubscribe = subscribeToStoreDataUpdate(
      store.id,
      handleStoreDataUpdate
    );

    return unsubscribe;
  }, [store?.id, isRealTimeEnabled]);  // 保存欄位顯示設定
  const saveFieldsViewSettings = async (currentVisibleFields: Record<string, boolean> = visibleFields) => {
    try {
      // 將 Record<string, boolean> 格式轉換為欄位 ID 數組格式
      const visibleFieldsArray = Object.entries(currentVisibleFields)
        .filter(([_, isVisible]) => isVisible)
        .map(([fieldId]) => fieldId);

      console.log('正在保存欄位設定:', visibleFieldsArray);

      await saveFieldsViewConfig({
        visibleFields: visibleFieldsArray,
        columnOrder: [], // 目前未實現排序功能
        columnWidths: {} // 目前未實現寬度調整功能
      });

      console.log('欄位設定保存成功');
      showNotification('欄位顯示設定已保存', 'success');
    } catch (error) {
      console.error('保存欄位設定失敗:', error);
      showNotification('保存欄位設定失敗', 'error');
    }
  };

  // 檢查是否所有欄位都被選中
  const areAllFieldsSelected = () => {
    const selectableFields = dataFields.filter(field => field.id !== 'sn' && field.id !== 'id');
    return selectableFields.length > 0 &&
      selectableFields.every(field => visibleFields[field.id]);
  };

  // 計算是否有可顯示的浮動欄位（非 sn 和 id）
  const hasVisibleFloatingFields = Object.entries(visibleFields)
    .some(([fieldId, isVisible]) => isVisible && fieldId !== 'sn' && fieldId !== 'id');
    // 獲取門店資料和資料欄位定義
  const fetchData = async () => {
    if (!store?.id) {
      setError('未選擇門店');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // 同時獲取門店資料、資料欄位定義和已保存的欄位顯示設定
      const [storeItems, fieldItems, savedConfig] = await Promise.all([
        getAllStoreData(store.id), // 使用從props傳入的門店ID
        getAllDataFields(),
        getFieldsViewConfig().catch(err => {
          console.error('載入欄位視圖設定失敗:', err);
          return null; // 如果載入失敗，返回 null
        })
      ]);

      setStoreData(storeItems);

      // 只保留一般資料欄位
      const ordinaryFields = fieldItems.filter(field => field.section === DataFieldSectionType.ORDINARY);
      setDataFields(ordinaryFields);
        // 檢查是否有已保存的欄位顯示設定
      if (savedConfig && savedConfig.visibleFields !== undefined) {
        console.log('已載入保存的欄位設定:', savedConfig.visibleFields);

        // 將數組格式的 visibleFields 轉換為 Record<string, boolean> 格式
        // 即使 visibleFields 是空陣列也視為有效設定（表示全部取消選取）
        const savedFieldsVisibility = ordinaryFields.reduce((acc, field) => {
          // 檢查欄位 ID 是否在已保存的可見欄位列表中，
          // 也確保不包含 sn 和 id
          if (field.id !== 'sn' && field.id !== 'id') {
            acc[field.id] = savedConfig.visibleFields.includes(field.id);
          }
          return acc;
        }, {} as Record<string, boolean>);

        setVisibleFields(savedFieldsVisibility);
      } else {
        // 沒有已保存的設定，使用預設值（所有欄位都顯示，但排除 sn 和 id）
        console.log('未找到已保存設定，使用預設值');
        const initialVisibility = ordinaryFields.reduce((acc, field) => {
          if (field.id !== 'sn' && field.id !== 'id') {
            acc[field.id] = true;
          }
          return acc;
        }, {} as Record<string, boolean>);

        setVisibleFields(initialVisibility);
      }
    } catch (err: any) {
      console.error('獲取資料失敗:', err);
      setError(err.message || '獲取資料失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 帶選取狀態保持的數據獲取函數
  const fetchDataWithSelectionPreservation = async () => {
    if (!store?.id) {
      setError('未選擇門店');
      return;
    }

    try {
      setError(null);

      // 只獲取門店資料，不重新載入欄位定義和設定
      const storeItems = await getAllStoreData(store.id);

      // 檢查選取項目是否還存在，保持有效的選取狀態
      if (selectedItems.length > 0) {
        const existingUids = storeItems.map(item => item.uid).filter(Boolean) as string[];
        const validSelectedItems = selectedItems.filter(uid => existingUids.includes(uid));

        if (validSelectedItems.length !== selectedItems.length) {
          console.log(`[門店資料選取保持] 清理無效選取項目: ${selectedItems.length} -> ${validSelectedItems.length}`);
          setSelectedItems(validSelectedItems);

          // 更新全選狀態
          const allSelected = storeItems.length > 0 &&
            storeItems.every(item => item.uid && validSelectedItems.includes(item.uid));
          setSelectAll(allSelected);
        }
      }

      setStoreData(storeItems);

      // 保持分頁狀態 - 檢查當前頁面是否仍然有效
      const newTotalPages = Math.ceil(storeItems.length / itemsPerPage);
      if (currentPage > newTotalPages && newTotalPages > 0) {
        // 如果當前頁面超出了新的總頁數，調整到最後一頁
        console.log(`[門店資料分頁保持] 當前頁面 ${currentPage} 超出新總頁數 ${newTotalPages}，調整到最後一頁`);
        setCurrentPage(newTotalPages);
      }
      // 如果當前頁面仍然有效，則保持不變
    } catch (err: any) {
      console.error('自動刷新門店資料失敗:', err);
      // 自動刷新失敗時不顯示錯誤，避免干擾用戶
    }
  };

  // 處理欄位顯示切換
  const toggleFieldVisibility = (fieldId: string) => {
    setVisibleFields(prev => ({
      ...prev,
      [fieldId]: !prev[fieldId]
    }));
  };
    // 全選或取消全選所有欄位
  const toggleAllFields = () => {
    const allSelected = areAllFieldsSelected();
    const newVisibility = dataFields.reduce((acc, field) => {
      if (field.id !== 'sn' && field.id !== 'id') {
        acc[field.id] = !allSelected;
      }
      return acc;
    }, {} as Record<string, boolean>);
    setVisibleFields(newVisibility);
  };

  // 同步資料欄位到門店資料
  const syncFields = async () => {
    try {
      setSyncingFields(true);
      const result = await syncDataFieldsToStoreData();
      if (result) {
        showNotification('資料欄位已成功同步到門店資料', 'success');
        // 重新獲取資料
        fetchData();
      }
    } catch (err: any) {
      console.error('同步資料欄位失敗:', err);
      showNotification(err.message || '同步資料欄位失敗，請重試', 'error');
    } finally {
      setSyncingFields(false);
    }
  };

  // 處理單筆資料刪除
  const handleDelete = async (uid: string) => {
    if (!store?.id) {
      showNotification('未選擇門店', 'error');
      return;
    }

    if (window.confirm(t('common.confirm') + '?')) {
      try {
        // 使用當前選擇的門店 ID
        await deleteStoreData(uid, store.id);
        showNotification('刪除門店資料成功', 'success');
        // 更新本地數據
        setStoreData(prevData => prevData.filter(item => item.uid !== uid));
      } catch (err: any) {
        showNotification(err.message || '刪除門店資料失敗', 'error');
      }
    }
  };

  // 新增：處理批量刪除
  const handleBatchDelete = async () => {
    const count = selectedItems.length;
    if (count === 0) {
      showNotification('請先選擇要刪除的門店資料', 'error');
      return;
    }

    if (!store?.id) {
      showNotification('未選擇門店', 'error');
      return;
    }

    if (window.confirm(`${t('common.confirm')}${count}?`)) {
      try {
        let deletedCount = 0;
        let errorCount = 0;

        // 依序刪除所選門店資料
        for (const uid of selectedItems) {
          try {
            if (!uid) {
              console.error(`無效的資料ID: ${uid}`);
              errorCount++;
              continue;
            }

            // 使用當前選擇的門店 ID
            await deleteStoreData(uid, store.id);
            deletedCount++;
          } catch (error) {
            console.error(`刪除門店資料失敗 (ID: ${uid}):`, error);
            errorCount++;
          }
        }

        // 更新本地數據
        setStoreData(prevData => prevData.filter(item => !selectedItems.includes(item.uid)));

        // 清空選擇
        setSelectedItems([]);
        setSelectAll(false);

        // 顯示結果
        if (errorCount === 0) {
          showNotification(`已成功刪除 ${deletedCount} 筆門店資料`, 'success');
        } else {
          showNotification(`成功刪除 ${deletedCount} 筆，失敗 ${errorCount} 筆門店資料`, 'error');
        }
      } catch (err: any) {
        showNotification(err.message || '批量刪除門店資料失敗', 'error');
      }
    }
  };

  // 處理資料編輯
  const handleEdit = (item: StoreData) => {
    setSelectedStoreData(item);
    setShowEditModal(true);
  };

  // 顯示通知訊息 - 使用泡泡通知
  const showNotification = (message: string, type: 'success' | 'error') => {
    if (type === 'success') {
      showSuccessNotification(message);
    } else {
      showErrorNotification(message);
    }
  };
  return (
    <div className="p-8 lg:px-4 py-2">
      <div className="max-w-7xl mx-auto">


        {/* 錯誤消息 */}
        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded flex items-center">
            <AlertCircle className="w-5 h-5 mr-2" />
            <span>{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-700 hover:text-red-900"
            >
              &times;
            </button>
          </div>
        )}

        {/* Search and Actions Bar */}
        <div className="mb-6 flex items-center space-x-4">
          <div className="relative flex-1">
            <input
              type="text"
              placeholder={t('database.searchPlaceholder')}
              className="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
          </div>

          {/* 顯示當前門店名稱 */}
          <div className="px-4 py-2 bg-blue-100 text-blue-800 rounded-md border border-blue-300">
            <span className="font-medium">{store?.name || '未選擇門店'}</span>
            <span className="ml-2 text-blue-600">({store?.id || ''})</span>
          </div>

          {/* 即時更新狀態指示器 */}
          <div className={`flex items-center gap-2 px-3 py-2 rounded-md border ${
            isRealTimeEnabled ? 'bg-green-50 border-green-300' : 'bg-gray-100 border-gray-300'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              isRealTimeEnabled ? 'bg-green-500' : 'bg-gray-400'
            }`} />
            <span className={`text-sm ${
              isRealTimeEnabled ? 'text-green-700' : 'text-gray-500'
            }`}>
              {isRealTimeEnabled ? '即時更新已開啟' : '即時更新已關閉'}
            </span>
            <button
              onClick={() => setIsRealTimeEnabled(!isRealTimeEnabled)}
              className="text-sm text-blue-600 hover:text-blue-800 underline ml-2"
            >
              {isRealTimeEnabled ? '關閉' : '開啟'}
            </button>
          </div>

          <button className="flex items-center gap-2 px-4 py-2 bg-emerald-500 text-white rounded-md hover:bg-emerald-600">
            <Import className="w-5 h-5" />
            {t('database.import')}
          </button>

          <button className="flex items-center gap-2 px-4 py-2 bg-cyan-500 text-white rounded-md hover:bg-cyan-600">
            <Upload className="w-5 h-5" />
            {t('database.export')}
          </button>

          <button
            className={`flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md ${
              syncingFields ? 'opacity-75 cursor-not-allowed' : 'hover:bg-blue-600'
            }`}
            onClick={syncFields}
            disabled={syncingFields}
          >
            <RefreshCw className={`w-5 h-5 ${syncingFields ? 'animate-spin' : ''}`} />
            {t('database.syncFields')}
          </button>
            <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-violet-500 text-white rounded-md hover:bg-violet-600"
          >
            <Plus className="w-5 h-5" />
            {t('common.add')}
          </button>

          {/* 全選所有按鈕 */}
          <button
            onClick={() => {
              const allFilteredDataIds = filteredStoreData.map(item => item.uid).filter(uid => uid !== undefined) as string[];
              const allSelected = allFilteredDataIds.length > 0 &&
                allFilteredDataIds.every(uid => selectedItems.includes(uid));

              if (allSelected) {
                // 取消全選所有
                setSelectedItems([]);
              } else {
                // 全選所有
                setSelectedItems(allFilteredDataIds);
              }
            }}
            className="flex items-center gap-2 px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
            title={
              filteredStoreData.length > 0 &&
              filteredStoreData.every(item => item.uid && selectedItems.includes(item.uid))
                ? '取消全選所有門店資料'
                : '全選所有門店資料 (跨頁)'
            }
          >
            <CheckSquare className="w-5 h-5" />
            {filteredStoreData.length > 0 &&
             filteredStoreData.every(item => item.uid && selectedItems.includes(item.uid))
              ? '取消全選所有'
              : '全選所有'
            }
          </button>

          <button
            className={`flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-md ${
              selectedItems.length === 0 ? 'opacity-75 cursor-not-allowed' : 'hover:bg-red-600'
            }`}
            onClick={handleBatchDelete}
            disabled={selectedItems.length === 0}
            title={selectedItems.length === 0 ? '請先選擇要刪除的門店資料' : `批量刪除 ${selectedItems.length} 筆門店資料`}
          >
            <Trash2 className="w-5 h-5" />
            {selectedItems.length > 0 ? `${t('common.delete')} (${selectedItems.length})` : t('common.delete')}
          </button>
            <div className="relative" ref={fieldManagerRef}>
            <button
              className="flex items-center gap-2 px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
              onClick={() => setShowFieldManager(!showFieldManager)}
              ref={fieldManagerButtonRef}
            >
              <Grid className="w-5 h-5" />
              {t('database.fieldManagement')}
            </button>            {showFieldManager && (
              <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-50 py-2 px-3 border border-gray-200">
                <div className="pb-2 border-b border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-sm text-gray-700">{t('database.displayFieldSettings')}</h3>
                    <div>
                      <label className="text-xs flex items-center text-gray-600 cursor-pointer">
                        <input
                          type="checkbox"
                          className="mr-1 rounded"
                          checked={areAllFieldsSelected()}
                          onChange={toggleAllFields}
                        />
                        {t('common.selectAll')}
                      </label>
                    </div>
                  </div>
                </div>
                <div className="pt-2 max-h-60 overflow-y-auto">
                  {dataFields
                    .filter(field => field.id !== 'sn' && field.id !== 'id')
                    .map(field => (
                      <label key={field.id} className="block py-1 px-2 text-sm text-gray-700 hover:bg-gray-100 rounded cursor-pointer">
                        <input
                          type="checkbox"
                          className="mr-2 rounded"
                          checked={visibleFields[field.id] || false}
                          onChange={() => toggleFieldVisibility(field.id)}
                        />
                        {field.name}
                      </label>
                    ))
                  }
                </div>                {/* 移除保存按鈕，改為設定視窗關閉時自動保存 */}
              </div>
            )}
          </div>
        </div>        {/* Table */}
        <div className="bg-card rounded-lg shadow relative">
          {/* 使用三個固定位置的表格實現左右固定，中間滾動的效果 */}
          <div className="relative overflow-x-auto" style={{ maxWidth: "100%" }}>
            <div className="flex relative">              {/* 左側固定欄位（勾選框和 SN） */}              <div className="sticky left-0 z-20 bg-card shadow-sm" style={hasVisibleFloatingFields ? { width: "230px" } : { width: "130px" }}>
                <table className="w-full border-separate border-spacing-0">                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 w-12 border-b border-gray-200 text-white">
                        <div className="flex flex-col items-center">
                          <input
                            type="checkbox"
                            className="rounded"
                            checked={selectAll}
                            onChange={(e) => {
                              const checked = e.target.checked;
                              setSelectAll(checked);

                              if (checked) {
                                // 選中當前頁的所有門店資料
                                const currentPageItems = getCurrentPageItems().map(item => item.uid).filter(uid => uid !== undefined) as string[];
                                // 合併當前頁的項目到已選取的項目中
                                setSelectedItems(prev => {
                                  const newItems = [...prev];
                                  currentPageItems.forEach(uid => {
                                    if (!newItems.includes(uid)) {
                                      newItems.push(uid);
                                    }
                                  });
                                  return newItems;
                                });
                              } else {
                                // 取消選中當前頁的所有門店資料
                                const currentPageItems = getCurrentPageItems().map(item => item.uid).filter(uid => uid !== undefined) as string[];
                                setSelectedItems(prev => prev.filter(uid => !currentPageItems.includes(uid)));
                              }
                            }}
                            title={selectAll ? "取消當前頁全選" : "全選當前頁"}
                          />
                        </div>
                      </th>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 text-white">
                        {t('database.serialNumber')}
                      </th>
                      {hasVisibleFloatingFields && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 text-white">
                          {t('database.id')}
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={hasVisibleFloatingFields ? 3 : 2} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8">
                            <RefreshCw className="w-5 h-5 animate-spin mx-auto mb-2" />
                          </div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td colSpan={hasVisibleFloatingFields ? 3 : 2} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8">{t('database.noData')}</div>
                        </td>
                      </tr>                    ) : (                      getCurrentPageItems().map((item, index) => (
                        <tr key={`left-${item.sn}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50 dark:bg-amber-900/20' : 'bg-blue-50 dark:bg-blue-900/20'}`}>
                          <td className={`px-4 py-3 border-b border-gray-200 align-middle`}>
                            <input
                              type="checkbox"
                              className="rounded"
                              checked={item.uid !== undefined && selectedItems.includes(item.uid)}
                              onChange={(e) => {
                                if (!item.uid) return;

                                if (e.target.checked) {
                                  setSelectedItems(prev => [...prev, item.uid]);
                                } else {
                                  setSelectedItems(prev => prev.filter(uid => uid !== item.uid));
                                }
                              }}
                            />
                          </td>                          <td className={`px-4 py-3 border-b border-gray-200 align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                            {/* 顯示基於分頁的流水號 */}
                            {(currentPage - 1) * itemsPerPage + index + 1}
                          </td>
                          {hasVisibleFloatingFields && (
                            <td className={`px-4 py-3 border-b border-gray-200 align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {item.id}
                            </td>
                          )}
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 中間可滾動區域（動態欄位） */}
              <div className="flex-1 overflow-x-auto">
                <table className="w-full border-separate border-spacing-0">                  <thead>                    <tr>
                      {!hasVisibleFloatingFields ? (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('database.id')}
                        </th>
                      ) : (
                        dataFields
                          .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
                          .filter(field => field.id !== 'sn' && field.id !== 'id' && visibleFields[field.id])
                          .map(field => (
                            <th key={field.id} className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                              {field.name}
                            </th>
                          ))
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={!hasVisibleFloatingFields ? 1 : Object.values(visibleFields).filter(Boolean).length} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td colSpan={!hasVisibleFloatingFields ? 1 : Object.values(visibleFields).filter(Boolean).length} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8">
                            {t('database.noStoreData')}
                          </div>
                        </td>
                      </tr>                    ) : (                      getCurrentPageItems().map((item, index) => (
                        <tr key={`mid-${item.sn}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50 dark:bg-amber-900/20' : 'bg-blue-50 dark:bg-blue-900/20'}`}>
                          {!hasVisibleFloatingFields ? (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {item.id}
                            </td>
                          ) : (
                            dataFields
                              .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
                              .filter(field => field.id !== 'sn' && field.id !== 'id' && visibleFields[field.id])
                              .map(field => (
                                <td key={field.id} className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                                  {item[field.id] !== undefined ? String(item[field.id]) : ''}
                                </td>
                              ))
                          )}
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
                {/* 右側固定區域（操作欄） */}
              <div className="sticky right-0 z-20 bg-white shadow-sm" style={{ width: "100px" }}>                <table className="w-full border-separate border-spacing-0">                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap text-white">
                        {t('database.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>                    ) : (                      getCurrentPageItems().map((item, index) => (
                        <tr key={`right-${item.sn}`} className={`h-[53px] align-middle ${index % 2 === 0 ? 'bg-amber-50 dark:bg-amber-900/20' : 'bg-blue-50 dark:bg-blue-900/20'}`}>
                          <td className="px-4 py-3 border-b border-gray-200 whitespace-nowrap">
                            <div className="flex items-center gap-2">
                              <button
                                className="text-muted-foreground hover:text-blue-600"
                                onClick={() => handleEdit(item)}
                              >
                                <Edit className="w-5 h-5" />
                              </button>
                              <button
                                className="text-muted-foreground hover:text-red-600"
                                onClick={() => handleDelete(item.uid)}
                              >
                                <Trash className="w-5 h-5" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          {/* 分頁控制 */}
          {filteredStoreData.length > 0 && (
            <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200">
              <div className="flex flex-col gap-1">
                <p className="text-sm text-gray-700">
                  {t('common.showing')}
                  <span className="font-medium mx-1">
                    {filteredStoreData.length === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1}
                  </span>
                  {t('common.to')}
                  <span className="font-medium mx-1">
                    {Math.min(currentPage * itemsPerPage, filteredStoreData.length)}
                  </span>
                  {t('common.of')}
                  <span className="font-medium mx-1">{filteredStoreData.length}</span>
                  {t('common.entries')}
                </p>
                {/* 選取狀態信息 */}
                {selectedItems.length > 0 && (
                  <p className="text-sm text-blue-600 font-medium">
                    已選取 {selectedItems.length} 筆門店資料
                    {selectedItems.length > itemsPerPage && ' (跨頁選取)'}
                  </p>
                )}
              </div>

              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {/* 上一頁按鈕 */}
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">{t('common.previousPage')}</span>
                    &laquo;
                  </button>

                  {/* 頁碼按鈕 */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(pageNum => (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium
                        ${currentPage === pageNum
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                    >
                      {pageNum}
                    </button>
                  ))}

                  {/* 下一頁按鈕 */}
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">{t('common.nextPage')}</span>
                    &raquo;
                  </button>
                </nav>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 新增門店資料模態窗口 */}
      <AddStoreDataModal
        isOpen={showAddModal}
        dataFields={dataFields}
        storeId={store?.id || ''}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          // 重新加載門店資料
          fetchData();
          // 顯示成功訊息
          showNotification('門店資料新增成功', 'success');
        }}
      />      {/* 編輯門店資料模態窗口 */}
      <EditStoreDataModal
        isOpen={showEditModal}
        dataFields={dataFields}
        storeData={selectedStoreData}
        storeId={store?.id || ''}
        onClose={() => {
          setShowEditModal(false);
          setSelectedStoreData(null);
        }}
        onSuccess={() => {
          // 重新加載門店資料
          fetchData();
          // 顯示成功訊息
          showNotification('門店資料修改成功', 'success');
        }}
      />
    </div>
  );
}
