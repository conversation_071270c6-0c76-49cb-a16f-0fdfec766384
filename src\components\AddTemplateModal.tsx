import React, { useState, useEffect } from 'react';
import { useTemplateStore } from '../store';
import { X } from 'lucide-react';
import { screenConfigs, parseScreenSizeString, parseOrientation } from '../utils/screenUtils';
import { DisplayColorType, TemplateType } from '../types';
import { buildEndpointUrl } from '../utils/api/apiConfig';
import ColorTypeGradient from './ui/ColorTypeGradient';

interface AddTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  store?: { id: string; name: string } | null; // 當前選中的門店
  systemTemplatesOnly?: boolean; // 是否只創建系統模板
}

export const AddTemplateModal: React.FC<AddTemplateModalProps> = ({
  isOpen,
  onClose,
  store,
  systemTemplatesOnly = false
}) => {
  const [formData, setFormData] = useState({
    name: '',
    screenSize: '',
    color: '' as DisplayColorType,
    orientation: 'No rotation',
  });
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [availableColors, setAvailableColors] = useState<DisplayColorType[]>([]);

  // 初始化表單數據
  useEffect(() => {
    if (isOpen && screenConfigs.length > 0) {
      // 預設選擇第一個屏幕尺寸
      const defaultScreen = screenConfigs[0];
      setFormData({
        name: '',
        screenSize: defaultScreen.displayName,
        color: defaultScreen.supportedColors[0],
        orientation: 'No rotation',
      });

      // 設置可用的顏色
      setAvailableColors(defaultScreen.supportedColors);
      // 重置錯誤狀態
      setError(null);
    }
  }, [isOpen]);

  // 處理屏幕尺寸變更
  const handleScreenSizeChange = (screenSize: string) => {
    const selectedScreen = screenConfigs.find(screen => screen.displayName === screenSize);

    if (selectedScreen) {
      setAvailableColors(selectedScreen.supportedColors);

      // 更新選擇的顏色，確保它在可用選項內
      setFormData(prev => ({
        ...prev,
        screenSize,
        color: selectedScreen.supportedColors.includes(prev.color as DisplayColorType)
          ? prev.color as DisplayColorType
          : selectedScreen.supportedColors[0]
      }));
    }
  };

  const { addTemplate } = useTemplateStore();

  if (!isOpen) return null;
  // 將模板保存到服務器
  const saveTemplateToServer = async (template: any) => {
    try {
      setIsSaving(true);
      const response = await fetch(buildEndpointUrl('templates'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(template)
      });

      const result = await response.json();

      // 檢查是否有錯誤
      if (!response.ok) {
        console.error('模板保存失敗:', result);

        // 處理特定類型的錯誤
        if (result.templates && result.templates.length > 0) {
          const templateError = result.templates[0];
          if (templateError.code === 'DUPLICATE_TEMPLATE_NAME') {
            setError(`模板名稱 "${templateError.name}" 已存在，請使用其他名稱`);
            return false;
          }
        }

        // 一般錯誤
        setError(result.error || result.message || `服務器錯誤: ${response.status}`);
        return false;
      }

      console.log('模板保存成功:', result);
      return true;
    } catch (error) {
      console.error('保存模板到服務器失敗:', error);
      setError('保存模板到服務器失敗，請稍後再試');
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 解析螢幕尺寸和方向
    const parsedScreenSize = parseScreenSizeString(formData.screenSize);
    const parsedOrientation = parseOrientation(formData.orientation);

    console.log('創建模板:', {
      screenSize: parsedScreenSize,
      orientation: parsedOrientation
    });

    // 獲取螢幕寬高
    const screenDimensions = parsedScreenSize.split('x').map(Number);
    const screenWidth = screenDimensions[0] || 0;
    const screenHeight = screenDimensions[1] || 0;

    // 如果模板為縱向，需要交換寬高
    let adjustedScreenSize = parsedScreenSize;
    if (parsedOrientation === 'portrait') {
      adjustedScreenSize = `${screenHeight}x${screenWidth}`;
      console.log('縱向模板，轉換螢幕尺寸為:', adjustedScreenSize);
    }

    // 創建新模板對象
    const newTemplate = {
      id: Date.now().toString(),
      name: formData.name,
      type: TemplateType.SINGLE_DATA, // 使用默認的 Single data template 類型
      screenSize: adjustedScreenSize,
      color: formData.color,
      orientation: parsedOrientation,
      elements: [],
      // 如果是系統模板頁面，則設置為系統模板
      isSystemTemplate: systemTemplatesOnly ? true : false,
      // 如果是系統模板頁面，則不設置 storeId
      storeId: systemTemplatesOnly ? undefined : store?.id,
    };

    // 保存到資料庫
    try {
      setIsSaving(true);
      const saveResult = await saveTemplateToServer(newTemplate);
      if (saveResult) {
        console.log('模板已成功保存到數據庫');

        // 不再立即添加到本地狀態，讓WebSocket事件來處理
        // addTemplate(newTemplate);

        // 顯示彈出提示訊息
        showSuccessMessage('模板創建成功！');

        // 只有在保存成功時才關閉模態窗口
        onClose();
      }
    } finally {
      setIsSaving(false);
    }
  };

  // 顯示成功訊息的函數
  const showSuccessMessage = (message: string) => {
    // 建立自定義彈出訊息元素
    const messageDiv = document.createElement('div');
    messageDiv.style.position = 'fixed';
    messageDiv.style.top = '20px';
    messageDiv.style.left = '50%';
    messageDiv.style.transform = 'translateX(-50%)';
    messageDiv.style.backgroundColor = '#4CAF50';
    messageDiv.style.color = 'white';
    messageDiv.style.padding = '15px 25px';
    messageDiv.style.borderRadius = '4px';
    messageDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
    messageDiv.style.zIndex = '9999';
    messageDiv.textContent = message;

    // 添加到頁面
    document.body.appendChild(messageDiv);

    // 2.5秒後自動移除
    setTimeout(() => {
      messageDiv.style.opacity = '0';
      messageDiv.style.transition = 'opacity 0.5s';
      setTimeout(() => {
        document.body.removeChild(messageDiv);
      }, 500);
    }, 2500);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30">
      <div className="bg-white rounded-lg p-6 w-[500px] z-30">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Add template</h2>
          <button onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Template name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
                disabled={isSaving}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Screen size <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.screenSize}
                onChange={(e) => handleScreenSizeChange(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                disabled={isSaving}
              >
                {screenConfigs.map(screen => (
                  <option key={screen.id} value={screen.displayName}>
                    {screen.displayName}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Selectable color <span className="text-red-500">*</span>
              </label>
              <div className="mt-1 space-y-2">
                {availableColors.map(color => (
                  <div
                    key={color}
                    onClick={() => !isSaving && setFormData({ ...formData, color })}
                    className={`p-3 border rounded-md cursor-pointer transition-all ${
                      formData.color === color
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{color}</span>
                      <div className="w-20 h-4 rounded-sm overflow-hidden">
                        <ColorTypeGradient colorType={color} size="sm" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>



            <div>
              <label className="block text-sm font-medium text-gray-700">
                Screen orientation <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.orientation}
                onChange={(e) =>
                  setFormData({ ...formData, orientation: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                disabled={isSaving}
              >
                <option>No rotation</option>
                <option>90° rotation</option>
                <option>180° rotation</option>
                <option>270° rotation</option>
              </select>
            </div>

            {/* 顯示當前門店信息或系統模板信息 */}
            {systemTemplatesOnly ? (
              <div className="text-sm text-gray-600 bg-blue-100 p-2 rounded">
                <span>此模板將創建為系統模板，所有門店可用</span>
              </div>
            ) : store ? (
              <div className="text-sm text-gray-600 bg-gray-100 p-2 rounded">
                <span>此模板將關聯到門店：</span>
                <span className="font-medium ml-1">{store.name}</span>
                <span className="text-gray-500 ml-1">({store.id})</span>
              </div>
            ) : null}

            {error && (
              <div className="text-red-500 text-sm">{error}</div>
            )}
          </div>

          <div className="mt-6 flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={isSaving}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center"
              disabled={isSaving}
            >
              {isSaving ? '處理中...' : 'Confirm'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};