import { BaseConverter } from './BaseConverter';
import { PixelData } from '../types';
import { DisplayColorType } from '../../../types';
import { getColorPalette, findExactColorMatch } from '../utils/colorPalettes';

/**
 * BW/GRAY16 轉換器
 * 4bit per pixel, 寬度需為2的倍數
 */
export class BWConverter extends BaseConverter {
  private index: number = 0;

  calculatePaddedWidth(): number {
    // 寬度必須是2的倍數 (4bit per pixel)
    return Math.ceil(this.width / 2) * 2;
  }

  calculateBufferSize(): number {
    // 每2個像素佔用1個字節
    return Math.ceil((this.paddedWidth * this.height) / 2);
  }

  protected getBytesPerPixel(): number {
    return 0.5; // 4bit per pixel
  }

  processPixel(_x: number, _y: number, pixel: PixelData): void {
    const grayValue = this.toGray16(pixel);

    if ((this.index & 1) === 0) {
      // 偶數索引：存儲在高4位
      this.buffer[this.index >> 1] = (grayValue & 0xF0);
    } else {
      // 奇數索引：存儲在低4位
      this.buffer[this.index >> 1] |= (grayValue >> 4);
    }
    this.index++;
  }

  private toGray16(pixel: PixelData): number {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BW);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 將匹配的灰度值轉換為 4bit 格式 (0-15)
    const grayLevel = Math.round(matchedColor.r / 17); // 255/15 ≈ 17
    return Math.min(15, Math.max(0, grayLevel)) << 4;
  }

  getPixelData(): Uint8Array {
    return this.buffer;
  }
}
