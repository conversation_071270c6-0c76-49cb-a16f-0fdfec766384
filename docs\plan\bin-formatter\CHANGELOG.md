# Bin檔案格式化工具 - 變更記錄

## 版本 2.0.0 (2024-12-23)

### 🚀 重大變更

#### 檔案格式更新
- **新增裝置編號欄位**: 在設備類型後新增2字節的裝置編號欄位
- **新增硬體版本支援**: 在韌體版本後新增最小和最大硬體版本欄位（各4字節）
- **格式變更**: 從12字節標頭擴展到22字節標頭

#### 新檔案格式
```
[2 bytes] 設備類型 (little endian)
[2 bytes] 裝置編號 (little endian) - 新增
[2 bytes] 功能類型 (little endian)  
[4 bytes] 韌體版本信息 (little endian)
[4 bytes] 最小硬體版本 (little endian) - 新增
[4 bytes] 最大硬體版本 (little endian) - 新增
[N bytes] 原始bin檔案內容
[4 bytes] CRC32校驗和 (little endian)
```

### 📝 裝置型號更新
- **Gateway型號**: M001 → GW-001
- **EPD型號**: E001 → EPD-001

### 🔧 API變更

#### Python工具
```python
# 舊版本
formatter.format_bin_file(bin_path, device_type, function_type, version, device_model)

# 新版本
formatter.format_bin_file(
    bin_path, 
    device_type, 
    function_type, 
    version, 
    device_model=0,
    min_hw_version="0.0.0.0",
    max_hw_version="***************"
)
```

#### 新增方法
- `get_device_model_name(device_type, device_model)`: 獲取裝置型號名稱
- `get_device_models(device_type)`: 獲取指定設備的所有型號
- `get_all_device_models()`: 獲取所有設備型號

### 🔍 後端解析器更新

#### JavaScript/Node.js
```javascript
// 解析結果新增欄位
{
  deviceType: "gateway",
  deviceModel: 0,
  deviceModelName: "GW-001",
  functionType: "wifi",
  version: "*******",
  minHwVersion: "*******",  // 新增
  maxHwVersion: "*******",  // 新增
  // ... 其他欄位
}
```

### 📋 互動式介面更新
新增硬體版本輸入步驟：
1. bin檔案路徑
2. 設備類型 (gateway/epd)
3. 裝置編號 (0=GW-001/EPD-001等)
4. 功能類型 (wifi/ble)
5. 韌體版本信息 (x.x.x.x格式)
6. 最小硬體版本 (x.x.x.x格式，可選) - 新增
7. 最大硬體版本 (x.x.x.x格式，可選) - 新增

### ✅ 測試更新
- 更新所有測試案例以支援新格式
- 新增硬體版本驗證測試
- 更新範例程式碼

### 📚 文檔更新
- 更新README.md
- 更新設計規範文檔
- 更新API文檔
- 新增變更記錄

### 🔄 向後相容性
**注意**: 此版本不向後相容，舊格式的bin檔案無法被新版本解析器正確處理。

### 🚨 升級注意事項
1. 所有使用舊格式的bin檔案需要重新生成
2. 後端解析器需要同步更新
3. 前端顯示邏輯需要適配新的欄位
4. 資料庫schema可能需要更新以儲存新欄位

### 🎯 未來規劃
- 支援更多裝置型號
- 版本相容性檢查功能
- 自動升級路徑建議
- 批次處理工具

---

## 版本 1.0.0 (2024-12-20)

### 🎉 初始版本
- 基本bin檔案格式化功能
- 支援Gateway和EPD設備
- 支援WiFi和BLE功能
- CRC32校驗和驗證
- 互動式命令行介面
- 完整的測試套件
