import fetch from 'node-fetch';

// 配置
const API_BASE_URL = 'http://localhost:3001/api';
let TOKEN = '';

// 通用請求函數
async function makeRequest(endpoint, method = 'GET', data = null) {
  const url = `${API_BASE_URL}/${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': `token=${TOKEN}`
    },
    credentials: 'include'
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);

    // 檢查響應狀態
    if (!response.ok) {
      let errorMessage = `API error: ${response.status}`;

      try {
        const errorData = await response.json();
        errorMessage += ` - ${errorData.error || 'Unknown error'}`;
      } catch (jsonError) {
        errorMessage += ' - Could not parse error response';
      }

      throw new Error(errorMessage);
    }

    // 嘗試解析 JSON 響應
    try {
      const responseData = await response.json();
      return responseData;
    } catch (jsonError) {
      console.warn(`Warning: Could not parse JSON response from ${endpoint}`);
      return {}; // 返回空對象而不是拋出錯誤
    }
  } catch (error) {
    console.error(`Error making request to ${endpoint}:`, error.message);
    throw error;
  }
}

// 創建角色
async function createRole(name, description, type, permissions) {
  console.log(`Creating role: ${name}`);
  try {
    // 檢查角色是否已存在
    try {
      const roles = await makeRequest('roles');
      const existingRole = roles.find(r => r.name === name);
      if (existingRole) {
        console.log(`Role already exists: ${existingRole.name} (${existingRole._id})`);
        return existingRole;
      }
    } catch (err) {
      console.log('Error checking existing roles, will try to create new one');
    }

    const role = await makeRequest('roles', 'POST', {
      name,
      description,
      type,
      permissions
    });
    console.log(`Role created: ${role.name} (${role._id})`);
    return role;
  } catch (error) {
    console.error(`Failed to create role ${name}:`, error.message);
    return null;
  }
}

// 創建門店
async function createStore(name, id, address) {
  console.log(`Creating store: ${name}`);
  try {
    // 檢查門店是否已存在
    try {
      const stores = await makeRequest('stores');
      const existingStore = stores.find(s => s.id === id || s.name === name);
      if (existingStore) {
        console.log(`Store already exists: ${existingStore.name} (${existingStore._id})`);
        return existingStore;
      }
    } catch (err) {
      console.log('Error checking existing stores, will try to create new one');
    }

    // 創建新門店
    const store = await makeRequest('stores', 'POST', {
      name,
      id,
      address,
      status: 'active'
    });
    console.log(`Store created: ${store.name} (${store._id})`);
    return store;
  } catch (error) {
    console.error(`Failed to create store ${name}:`, error.message);
    return null;
  }
}

// 創建用戶
async function createUser(username, password, name, email, phone) {
  console.log(`Creating user: ${username}`);
  try {
    // 檢查用戶是否已存在
    try {
      const response = await makeRequest('users');
      const existingUser = response.users.find(u => u.username === username);
      if (existingUser) {
        console.log(`User already exists: ${existingUser.username} (${existingUser._id})`);
        return existingUser;
      }
    } catch (err) {
      console.log('Error checking existing users, will try to create new one');
    }

    const user = await makeRequest('users', 'POST', {
      username,
      password,
      name,
      email,
      phone,
      status: 'active'
    });
    console.log(`User created: ${user.username} (${user._id})`);
    return user;
  } catch (error) {
    console.error(`Failed to create user ${username}:`, error.message);
    return null;
  }
}

// 分配權限
async function assignPermission(userId, roleId, scope, scopeType) {
  console.log(`Assigning permission: ${roleId} to user ${userId} for ${scopeType} ${scope}`);
  try {
    // 檢查用戶是否已有該範圍的權限
    try {
      const permissions = await makeRequest(`permissions?userId=${userId}`);
      const existingPermission = Array.isArray(permissions) ?
        permissions.find(p => p.scope === scope) :
        (permissions.permissions ? permissions.permissions.find(p => p.scope === scope) : null);

      if (existingPermission) {
        console.log(`User already has permission for scope ${scope}, skipping...`);
        return existingPermission;
      }
    } catch (err) {
      console.log('Error checking existing permissions, will try to create new one');
    }

    const permission = await makeRequest('permissions', 'POST', {
      userId,
      roleId,
      scope,
      scopeType
    });
    console.log(`Permission assigned: ${permission._id}`);
    return permission;
  } catch (error) {
    console.error(`Failed to assign permission:`, error.message);
    return null;
  }
}

// 創建資料欄位
async function createDataField(id, name, type, section, prefix = '') {
  console.log(`Creating data field: ${id} (${name})`);
  try {
    // 檢查資料欄位是否已存在
    try {
      const dataFields = await makeRequest('dataFields');
      const existingField = dataFields.find(f => f.id === id || f.name === name);
      if (existingField) {
        console.log(`Data field already exists: ${existingField.id} (${existingField.name})`);
        return existingField;
      }
    } catch (err) {
      console.log('Error checking existing data fields, will try to create new one');
    }

    const dataField = await makeRequest('dataFields', 'POST', {
      id,
      name,
      type,
      section,
      prefix
    });
    console.log(`Data field created: ${dataField.id} (${dataField.name})`);
    return dataField;
  } catch (error) {
    console.error(`Failed to create data field ${id}:`, error.message);
    return null;
  }
}

// 創建門店數據
async function createStoreData(storeId, data = {}) {
  console.log(`Creating store data for store: ${storeId}`);
  try {
    // 檢查門店數據是否已存在
    try {
      const storeData = await makeRequest(`storeData?storeId=${storeId}`);
      if (storeData && storeData.length > 0) {
        console.log(`Store data already exists for store ${storeId}`);
        return storeData[0];
      }
    } catch (err) {
      console.log('Error checking existing store data, will try to create new one');
    }

    // 創建新門店數據 - 使用查詢參數傳遞 storeId，而不是請求體
    const result = await makeRequest(`storeData?storeId=${storeId}`, 'POST', data);
    console.log(`Store data created for store ${storeId}`);
    return result;
  } catch (error) {
    console.error(`Failed to create store data for store ${storeId}:`, error.message);
    return null;
  }
}

// 登入獲取 TOKEN
async function login(username, password) {
  console.log(`Logging in as ${username}...`);
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`Login failed: ${data.error || 'Unknown error'}`);
    }

    // 從 response headers 中獲取 token
    const setCookieHeader = response.headers.get('set-cookie');
    if (setCookieHeader) {
      const tokenMatch = setCookieHeader.match(/token=([^;]+)/);
      if (tokenMatch && tokenMatch[1]) {
        TOKEN = tokenMatch[1];
        console.log('Login successful, token obtained');
        return true;
      }
    }

    // 如果 headers 中沒有 token，嘗試從 response body 獲取
    if (data.token) {
      TOKEN = data.token;
      console.log('Login successful, token obtained from response body');
      return true;
    }

    throw new Error('Failed to obtain token from response');
  } catch (error) {
    console.error('Login error:', error.message);
    return false;
  }
}

// 主函數
async function generateTestData() {
  try {
    console.log('Starting test data generation...');

    // 先登入獲取 TOKEN
    const loginSuccess = await login('root', '123456789');
    if (!loginSuccess) {
      throw new Error('Failed to login, cannot proceed with data generation');
    }

    // 1. 創建資料欄位
    console.log('\n=== Creating Data Fields ===');

    // 首先確保 id 欄位存在（使用原子操作）
    console.log('Ensuring id field exists...');
    await makeRequest('dataFields/ensure-id-field', 'POST');

    // 一般資料欄位（不需要創建 id 欄位，因為已經確保存在了）
    await createDataField('name', '名稱', 'pure text', 'ordinary', '名稱');
    await createDataField('description', '描述', 'pure text', 'ordinary', '描述');
    await createDataField('price', '價格', 'number', 'ordinary', '價格');
    await createDataField('quantity', '數量', 'number', 'ordinary', '數量');
    await createDataField('date', '日期', 'pure text', 'ordinary', '日期');

    // 圖標資料欄位
    await createDataField('status_icon', '狀態圖標', 'ICON', 'icon');
    await createDataField('category_icon', '分類圖標', 'ICON', 'icon');

    // 圖片資料欄位
    await createDataField('product_image', '產品圖片', 'IMAGE', 'image');
    await createDataField('store_image', '門店圖片', 'IMAGE', 'image');

    // 視頻資料欄位
    await createDataField('product_video', '產品視頻', 'VIDEO', 'video');
    await createDataField('store_video', '門店視頻', 'VIDEO', 'video');

    // 2. 創建角色
    console.log('\n=== Creating Roles ===');
    const systemAdminRole = await createRole('系統管理員', '具有所有系統權限', 'system', ['all']);
    const operatorRole = await createRole('操作員', '基本操作權限', 'system', ['user:view', 'role:view', 'store:view']);
    const reviewerRole = await createRole('審核員', '審核相關權限', 'system', ['user:view', 'role:view', 'store:view', 'store:update']);
    const managerRole = await createRole('店長', '門店管理者', 'store', ['store:view', 'store:update']);
    const staffRole = await createRole('店員', '門店基本操作', 'store', ['store:view']);

    // 3. 創建門店
    console.log('\n=== Creating Stores ===');
    const store1 = await createStore('台北總店', 'TP001', '台北市信義區101號');
    const store2 = await createStore('台中分店', 'TC001', '台中市西區中港路123號');
    const store3 = await createStore('高雄分店', 'KH001', '高雄市前鎮區中山路456號');

    // 4. 創建用戶
    console.log('\n=== Creating Users ===');
    const operator1 = await createUser('operator1', '123456', '操作員1', '<EMAIL>', '0912345678');
    const operator2 = await createUser('operator2', '123456', '操作員2', '<EMAIL>', '0923456789');
    const manager1 = await createUser('manager1', '123456', '店長1', '<EMAIL>', '0934567890');
    const manager2 = await createUser('manager2', '123456', '店長2', '<EMAIL>', '0945678901');
    const staff1 = await createUser('staff1', '123456', '店員1', '<EMAIL>', '0956789012');

    // 5. 分配權限
    console.log('\n=== Assigning Permissions ===');
    if (operator1 && operatorRole) {
      await assignPermission(operator1._id, operatorRole._id, 'system', 'system');
    }

    if (operator2 && reviewerRole) {
      await assignPermission(operator2._id, reviewerRole._id, 'system', 'system');
    }

    if (manager1 && managerRole && store1) {
      const scope = store1.id;
      console.log(`Assigning manager1 to store1 with scope: ${scope}`);
      await assignPermission(manager1._id, managerRole._id, scope, 'store');
    } else {
      console.warn('Cannot assign manager1 to store1: missing data');
      if (manager1 && managerRole) {
        console.log('manager1 and managerRole exist, but store1 is missing or has no id');
        console.log('manager1:', manager1?._id);
        console.log('managerRole:', managerRole?._id);
        console.log('store1:', store1?.id);
      }
    }

    if (manager2 && managerRole && store2) {
      const scope = store2.id;
      console.log(`Assigning manager2 to store2 with scope: ${scope}`);
      await assignPermission(manager2._id, managerRole._id, scope, 'store');
    } else {
      console.warn('Cannot assign manager2 to store2: missing data');
    }

    if (staff1 && staffRole && store3) {
      const scope = store3.id;
      console.log(`Assigning staff1 to store3 with scope: ${scope}`);
      await assignPermission(staff1._id, staffRole._id, scope, 'store');
    } else {
      console.warn('Cannot assign staff1 to store3: missing data');
    }

    // 6. 創建門店數據（注意：我們不會自動創建門店數據，因為這應該由用戶手動添加）
    console.log('\n=== Note about Store Data ===');
    console.log('Store data should be manually added by users through the UI.');
    console.log('The system has been configured to not automatically create store data.');

    console.log('\nTest data generation completed!');
  } catch (error) {
    console.error('Error generating test data:', error);
  }
}

// 執行
console.log('Starting script...');
generateTestData().catch(error => {
  console.error('Unhandled error in generateTestData:', error);
});

export {};
