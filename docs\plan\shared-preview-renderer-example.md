# 共享預覽渲染器實現示例

本文檔提供了將現有預覽圖渲染邏輯抽取為共享模塊的具體代碼示例。

## 目錄

1. [項目結構](#項目結構)
2. [核心模塊實現](#核心模塊實現)
3. [環境適配器](#環境適配器)
4. [使用示例](#使用示例)
5. [打包配置](#打包配置)

## 項目結構

```
shared-preview-renderer/
  ├── src/
  │   ├── adapters/
  │   │   ├── browser-adapter.ts
  │   │   ├── puppeteer-adapter.ts
  │   │   └── index.ts
  │   ├── components/
  │   │   └── preview-renderer.ts
  │   ├── utils/
  │   │   ├── canvas-utils.ts
  │   │   ├── preview-utils.ts
  │   │   ├── image-effects.ts
  │   │   └── index.ts
  │   └── index.ts
  ├── dist/
  ├── package.json
  ├── rollup.config.js
  └── tsconfig.json
```

## 核心模塊實現

### 1. 環境適配器接口 (src/adapters/index.ts)

```typescript
/**
 * 環境適配器接口
 * 用於在不同環境中提供一致的DOM操作能力
 */
export interface EnvironmentAdapter {
  // DOM創建方法
  createCanvas(): HTMLCanvasElement;
  createImage(): HTMLImageElement;
  createElement(tagName: string): HTMLElement;
  createDiv(): HTMLDivElement;
  
  // DOM查詢方法
  querySelector(selector: string): Element | null;
  querySelectorAll(selector: string): NodeListOf<Element>;
  
  // DOM操作方法
  appendChild(parent: HTMLElement, child: HTMLElement): void;
  setStyle(element: HTMLElement, styles: Record<string, string>): void;
  setText(element: HTMLElement, text: string): void;
  
  // 圖像處理方法
  loadImage(src: string): Promise<HTMLImageElement>;
  
  // 其他工具方法
  delay(ms: number): Promise<void>;
}

export type AdapterType = 'browser' | 'puppeteer';

export function createAdapter(type: AdapterType): EnvironmentAdapter {
  if (type === 'browser') {
    const { BrowserAdapter } = require('./browser-adapter');
    return new BrowserAdapter();
  } else if (type === 'puppeteer') {
    const { PuppeteerAdapter } = require('./puppeteer-adapter');
    return new PuppeteerAdapter();
  }
  
  throw new Error(`不支持的適配器類型: ${type}`);
}
```

### 2. 瀏覽器適配器 (src/adapters/browser-adapter.ts)

```typescript
import { EnvironmentAdapter } from './index';

/**
 * 瀏覽器環境適配器
 */
export class BrowserAdapter implements EnvironmentAdapter {
  createCanvas(): HTMLCanvasElement {
    return document.createElement('canvas');
  }
  
  createImage(): HTMLImageElement {
    return new Image();
  }
  
  createElement(tagName: string): HTMLElement {
    return document.createElement(tagName);
  }
  
  createDiv(): HTMLDivElement {
    return document.createElement('div');
  }
  
  querySelector(selector: string): Element | null {
    return document.querySelector(selector);
  }
  
  querySelectorAll(selector: string): NodeListOf<Element> {
    return document.querySelectorAll(selector);
  }
  
  appendChild(parent: HTMLElement, child: HTMLElement): void {
    parent.appendChild(child);
  }
  
  setStyle(element: HTMLElement, styles: Record<string, string>): void {
    Object.assign(element.style, styles);
  }
  
  setText(element: HTMLElement, text: string): void {
    element.textContent = text;
  }
  
  async loadImage(src: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = (e) => reject(e);
      img.src = src;
    });
  }
  
  async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 3. Puppeteer適配器 (src/adapters/puppeteer-adapter.ts)

```typescript
import { EnvironmentAdapter } from './index';

/**
 * Puppeteer環境適配器
 * 注意：此適配器需要在Puppeteer的page.evaluate內部使用
 */
export class PuppeteerAdapter implements EnvironmentAdapter {
  createCanvas(): HTMLCanvasElement {
    return document.createElement('canvas');
  }
  
  createImage(): HTMLImageElement {
    return new Image();
  }
  
  createElement(tagName: string): HTMLElement {
    return document.createElement(tagName);
  }
  
  createDiv(): HTMLDivElement {
    return document.createElement('div');
  }
  
  querySelector(selector: string): Element | null {
    return document.querySelector(selector);
  }
  
  querySelectorAll(selector: string): NodeListOf<Element> {
    return document.querySelectorAll(selector);
  }
  
  appendChild(parent: HTMLElement, child: HTMLElement): void {
    parent.appendChild(child);
  }
  
  setStyle(element: HTMLElement, styles: Record<string, string>): void {
    Object.assign(element.style, styles);
  }
  
  setText(element: HTMLElement, text: string): void {
    element.textContent = text;
  }
  
  async loadImage(src: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = (e) => reject(e);
      img.src = src;
    });
  }
  
  async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 4. 畫布工具 (src/utils/canvas-utils.ts)

```typescript
import { EnvironmentAdapter } from '../adapters';

/**
 * 將畫布渲染為圖片
 * @param adapter 環境適配器
 * @param container 畫布容器
 * @param width 畫布寬度
 * @param height 畫布高度
 * @returns 渲染後的canvas元素
 */
export async function renderCanvasToImage(
  adapter: EnvironmentAdapter,
  container: HTMLElement,
  width: number,
  height: number
): Promise<HTMLCanvasElement | null> {
  try {
    // 創建臨時容器
    const tempContainer = adapter.createDiv();
    adapter.setStyle(tempContainer, {
      position: 'absolute',
      left: '-9999px',
      top: '0',
      width: `${width}px`,
      height: `${height}px`,
      overflow: 'hidden',
      visibility: 'hidden'
    });
    
    // 複製容器內容
    tempContainer.innerHTML = container.innerHTML;
    
    // 添加到文檔
    adapter.appendChild(document.body, tempContainer);
    
    // 等待內容渲染
    await adapter.delay(10);
    
    // 使用html2canvas渲染
    // 注意：這裡假設html2canvas在全局可用
    // 在實際實現中，需要確保html2canvas在不同環境中都可用
    const html2canvas = (window as any).html2canvas;
    const renderedCanvas = await html2canvas(tempContainer, {
      backgroundColor: '#FFFFFF',
      scale: 1,
      useCORS: true,
      logging: false,
      width: width,
      height: height,
      allowTaint: true
    });
    
    // 清理臨時容器
    document.body.removeChild(tempContainer);
    
    return renderedCanvas;
  } catch (error) {
    console.error('渲染畫布時發生錯誤:', error);
    return null;
  }
}
```

### 5. 圖像效果處理 (src/utils/image-effects.ts)

```typescript
import { EnvironmentAdapter } from '../adapters';

export type EffectType = 'original' | 'blackAndWhite' | 'grayscale';

/**
 * 應用圖像效果
 * @param adapter 環境適配器
 * @param sourceCanvas 源畫布
 * @param effectType 效果類型
 * @param threshold 閾值 (用於黑白效果)
 * @returns 處理後的畫布
 */
export function applyImageEffect(
  adapter: EnvironmentAdapter,
  sourceCanvas: HTMLCanvasElement,
  effectType: EffectType = 'blackAndWhite',
  threshold: number = 128
): HTMLCanvasElement {
  // 創建新畫布
  const canvas = adapter.createCanvas();
  canvas.width = sourceCanvas.width;
  canvas.height = sourceCanvas.height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return canvas;
  
  // 繪製原始圖像
  ctx.drawImage(sourceCanvas, 0, 0);
  
  // 如果是原始效果，直接返回
  if (effectType === 'original') {
    return canvas;
  }
  
  // 獲取圖像數據
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;
  
  // 應用效果
  if (effectType === 'blackAndWhite') {
    // 黑白效果 (二值化)
    for (let i = 0; i < data.length; i += 4) {
      const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
      const color = avg > threshold ? 255 : 0;
      data[i] = data[i + 1] = data[i + 2] = color;
    }
  } else if (effectType === 'grayscale') {
    // 灰度效果
    for (let i = 0; i < data.length; i += 4) {
      const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
      data[i] = data[i + 1] = data[i + 2] = avg;
    }
  }
  
  // 更新畫布
  ctx.putImageData(imageData, 0, 0);
  
  return canvas;
}
```

### 6. 預覽渲染器 (src/components/preview-renderer.ts)

```typescript
import { EnvironmentAdapter } from '../adapters';
import { renderCanvasToImage } from '../utils/canvas-utils';
import { applyImageEffect, EffectType } from '../utils/image-effects';

export interface Template {
  id: string;
  name: string;
  elements: any[];
  width: number;
  height: number;
  color?: string;
}

export interface PreviewOptions {
  effectType?: EffectType;
  threshold?: number;
}

/**
 * 預覽渲染器
 * 負責將模板和綁定數據渲染為預覽圖
 */
export class PreviewRenderer {
  private adapter: EnvironmentAdapter;
  
  constructor(adapter: EnvironmentAdapter) {
    this.adapter = adapter;
  }
  
  /**
   * 生成預覽圖
   * @param template 模板數據
   * @param bindingData 綁定數據
   * @param storeData 門店數據
   * @param options 預覽選項
   * @returns 預覽圖數據URL
   */
  async generatePreview(
    template: Template,
    bindingData: Record<string, string>,
    storeData: any[],
    options: PreviewOptions = {}
  ): Promise<string | null> {
    try {
      // 創建渲染容器
      const container = this.adapter.createDiv();
      this.adapter.setStyle(container, {
        width: `${template.width}px`,
        height: `${template.height}px`,
        position: 'relative',
        background: template.color === 'bwr' ? 'white' : '#f0f0f0',
        overflow: 'hidden'
      });
      
      // 渲染模板元素
      await this.renderElements(container, template.elements, bindingData, storeData);
      
      // 渲染為圖像
      const renderedCanvas = await renderCanvasToImage(
        this.adapter,
        container,
        template.width,
        template.height
      );
      
      if (!renderedCanvas) {
        return null;
      }
      
      // 應用效果
      const effectType = options.effectType || 'blackAndWhite';
      const threshold = options.threshold || 128;
      const previewCanvas = applyImageEffect(this.adapter, renderedCanvas, effectType, threshold);
      
      // 轉換為數據URL
      return previewCanvas.toDataURL('image/png');
    } catch (error) {
      console.error('生成預覽圖時發生錯誤:', error);
      return null;
    }
  }
  
  /**
   * 渲染模板元素
   * @param container 容器元素
   * @param elements 模板元素
   * @param bindingData 綁定數據
   * @param storeData 門店數據
   */
  private async renderElements(
    container: HTMLElement,
    elements: any[],
    bindingData: Record<string, string>,
    storeData: any[]
  ): Promise<void> {
    // 這裡實現元素渲染邏輯
    // 根據元素類型創建對應的DOM元素
    // 應用樣式和屬性
    // 處理數據綁定
    
    // 注意：實際實現會更複雜，這裡只是示例
    for (const element of elements) {
      const elementDiv = this.adapter.createDiv();
      
      // 設置基本樣式
      this.adapter.setStyle(elementDiv, {
        position: 'absolute',
        left: `${element.x}px`,
        top: `${element.y}px`,
        width: `${element.width}px`,
        height: `${element.height}px`
      });
      
      // 根據元素類型渲染
      if (element.type === 'text') {
        // 處理文本元素
        let text = element.text || '';
        
        // 處理數據綁定
        if (element.dataField && bindingData[element.dataField]) {
          text = bindingData[element.dataField];
        }
        
        this.adapter.setText(elementDiv, text);
        this.adapter.setStyle(elementDiv, {
          fontFamily: element.fontFamily || 'Arial',
          fontSize: `${element.fontSize || 12}px`,
          fontWeight: element.bold ? 'bold' : 'normal',
          color: element.color || '#000000',
          textAlign: element.textAlign || 'left'
        });
      } else if (element.type === 'image') {
        // 處理圖像元素
        // 實際實現中需要處理圖像加載
      } else if (element.type === 'line') {
        // 處理線條元素
        this.adapter.setStyle(elementDiv, {
          borderTop: `${element.thickness || 1}px solid ${element.color || '#000000'}`
        });
      }
      
      // 添加到容器
      this.adapter.appendChild(container, elementDiv);
    }
  }
}
```

## 使用示例

### 1. 在瀏覽器中使用

```typescript
import { createAdapter } from 'shared-preview-renderer/adapters';
import { PreviewRenderer } from 'shared-preview-renderer/components/preview-renderer';

// 創建瀏覽器適配器
const adapter = createAdapter('browser');

// 創建預覽渲染器
const renderer = new PreviewRenderer(adapter);

// 生成預覽圖
async function generatePreview() {
  const template = {
    id: 'template-123',
    name: 'Test Template',
    width: 250,
    height: 122,
    elements: [
      // 模板元素
    ]
  };
  
  const bindingData = {
    field1: 'Value 1',
    field2: 'Value 2'
  };
  
  const storeData = [
    // 門店數據
  ];
  
  const previewData = await renderer.generatePreview(
    template,
    bindingData,
    storeData,
    { effectType: 'blackAndWhite', threshold: 128 }
  );
  
  // 使用預覽圖數據
  if (previewData) {
    const previewImg = document.getElementById('preview-img') as HTMLImageElement;
    previewImg.src = previewData;
  }
}
```

### 2. 在Puppeteer中使用

```typescript
const puppeteer = require('puppeteer');
const { PreviewRenderer } = require('shared-preview-renderer');

async function generatePreviewWithPuppeteer(template, bindingData, storeData) {
  // 啟動瀏覽器
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // 注入html2canvas
  await page.addScriptTag({ url: 'https://html2canvas.hertzen.com/dist/html2canvas.min.js' });
  
  // 注入共享渲染器
  await page.addScriptTag({ path: './dist/shared-preview-renderer.js' });
  
  // 注入數據
  await page.evaluate((templateData, bindingData, storeData) => {
    window.templateData = templateData;
    window.bindingData = bindingData;
    window.storeData = storeData;
  }, template, bindingData, storeData);
  
  // 執行渲染
  const previewData = await page.evaluate(async () => {
    // 創建Puppeteer適配器
    const adapter = window.SharedPreviewRenderer.createAdapter('puppeteer');
    
    // 創建預覽渲染器
    const renderer = new window.SharedPreviewRenderer.PreviewRenderer(adapter);
    
    // 生成預覽圖
    return await renderer.generatePreview(
      window.templateData,
      window.bindingData,
      window.storeData,
      { effectType: 'blackAndWhite', threshold: 128 }
    );
  });
  
  await browser.close();
  
  return previewData;
}
```

## 打包配置

### package.json

```json
{
  "name": "shared-preview-renderer",
  "version": "1.0.0",
  "description": "共享預覽渲染器",
  "main": "dist/index.js",
  "module": "dist/index.esm.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "rollup -c",
    "test": "jest"
  },
  "dependencies": {
    "html2canvas": "^1.4.1"
  },
  "devDependencies": {
    "rollup": "^2.79.1",
    "rollup-plugin-typescript2": "^0.31.2",
    "typescript": "^4.9.5",
    "jest": "^29.5.0",
    "ts-jest": "^29.1.0"
  }
}
```

### rollup.config.js

```javascript
import typescript from 'rollup-plugin-typescript2';

export default {
  input: 'src/index.ts',
  output: [
    {
      file: 'dist/index.js',
      format: 'cjs',
      sourcemap: true
    },
    {
      file: 'dist/index.esm.js',
      format: 'esm',
      sourcemap: true
    },
    {
      file: 'dist/shared-preview-renderer.js',
      format: 'umd',
      name: 'SharedPreviewRenderer',
      sourcemap: true
    }
  ],
  plugins: [
    typescript({
      tsconfig: './tsconfig.json'
    })
  ],
  external: ['html2canvas']
};
```
