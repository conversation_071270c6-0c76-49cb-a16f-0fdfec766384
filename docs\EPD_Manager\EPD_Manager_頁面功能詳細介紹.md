# EPD Manager - 頁面功能詳細介紹

## 目錄

- [1. 用戶認證系統](#1-用戶認證系統)
- [2. 門店管理系統](#2-門店管理系統)
- [3. 設備管理系統](#3-設備管理系統)
- [4. 網關管理系統](#4-網關管理系統)
- [5. 模板管理系統](#5-模板管理系統)
- [6. 數據管理系統](#6-數據管理系統)
- [7. 系統配置管理](#7-系統配置管理)
- [8. 權限管理系統](#8-權限管理系統)
- [9. 刷圖計劃管理](#9-刷圖計劃管理)
- [10. 系統日誌管理](#10-系統日誌管理)
- [11. 統計分析系統](#11-統計分析系統)
- [12. 移動應用程序](#12-移動應用程序)

---

## 1. 用戶認證系統

### 1.1 登入頁面

**功能描述：**
提供安全的用戶身份驗證，支持多種登入方式和安全設置。

**主要功能：**
- 用戶名/密碼登入
- 記住登入狀態
- 自動登入功能
- 密碼強度驗證
- 登入失敗保護機制

**特色功能：**
- 支援多語言界面
- 響應式設計適配各種設備
- 安全的Token認證機制
- 自動會話管理

<!-- 截圖預留位置 -->
![登入頁面截圖](screenshots/login-page.png)

### 1.2 密碼修改頁面

**功能描述：**
允許用戶安全地修改登入密碼，確保帳戶安全。

**主要功能：**
- 舊密碼驗證
- 新密碼強度檢查
- 密碼確認驗證
- 即時密碼強度提示

<!-- 截圖預留位置 -->
![密碼修改頁面截圖](screenshots/change-password-page.png)

---

## 2. 門店管理系統

### 2.1 門店概覽頁面

**功能描述：**
提供門店的整體運營狀況概覽，包括設備統計、網關狀態等關鍵指標。

**主要功能：**
- 門店基本信息顯示
- 設備數量統計
- 網關狀態統計
- 在線設備監控
- 異常狀態告警

**特色功能：**
- 實時數據更新
- 可視化圖表展示
- 快速操作入口
- 狀態趨勢分析

<!-- 截圖預留位置 -->
![門店概覽頁面截圖](screenshots/store-overview-page.png)

### 2.2 門店管理頁面

**功能描述：**
集中管理所有門店信息，支持門店的創建、編輯、刪除等操作。

**主要功能：**
- 門店列表顯示
- 新增門店功能
- 門店信息編輯
- 門店刪除管理
- 門店搜索篩選

**特色功能：**
- 卡片式門店展示
- 批量操作支持
- 門店狀態管理
- 管理員分配功能

<!-- 截圖預留位置 -->
![門店管理頁面截圖](screenshots/store-management-page.png)

### 2.3 門店設置頁面

**功能描述：**
配置門店的各項設置，包括用戶權限、操作參數等。

**主要功能：**
- 門店基本設置
- 用戶權限配置
- 操作參數設定
- 功能模塊開關

<!-- 截圖預留位置 -->
![門店設置頁面截圖](screenshots/store-settings-page.png)

---

## 3. 設備管理系統

### 3.1 設備列表頁面

**功能描述：**
EPD設備的核心管理界面，提供設備的全生命週期管理功能。

**主要功能：**
- 設備列表顯示與分頁
- 設備狀態實時監控
- 設備搜索與篩選
- 批量設備操作
- 設備詳細信息查看

**特色功能：**
- **智能網關選擇標識**：設備MAC地址帶有動態波浪邊框，標識智能分配模式
- **實時狀態更新**：WebSocket實時推送設備狀態變化
- **批量刷圖功能**：支持大批量設備的智能排程刷圖
- **設備綁定管理**：一鍵綁定數據和模板
- **狀態統計面板**：實時顯示在線/離線設備統計

**操作功能：**
- 新增設備
- 編輯設備信息
- 刪除設備
- 發送預覽圖到設備
- 批量發送操作
- 設備數據綁定

<!-- 截圖預留位置 -->
![設備列表頁面截圖](screenshots/devices-page.png)

### 3.2 設備詳情頁面

**功能描述：**
顯示單個設備的詳細信息和操作歷史。

**主要功能：**
- 設備基本信息
- 設備狀態歷史
- 操作記錄查看
- 設備配置管理

<!-- 截圖預留位置 -->
![設備詳情頁面截圖](screenshots/device-detail-page.png)

### 3.3 設備綁定數據模態窗口

**功能描述：**
為設備綁定數據和模板，並實時生成預覽圖。

**主要功能：**
- 模板選擇
- 數據欄位綁定
- 實時預覽生成
- 綁定確認提交

**特色功能：**
- 智能數據映射
- 即時預覽更新
- 多種預覽效果
- 綁定狀態驗證

<!-- 截圖預留位置 -->
![設備綁定數據模態窗口截圖](screenshots/bind-device-data-modal.png)

---

## 4. 網關管理系統

### 4.1 網關管理頁面

**功能描述：**
管理EPD網關設備，監控網關狀態和性能。

**主要功能：**
- 網關列表顯示
- 網關狀態監控
- 網關配置管理
- 固件升級功能
- 網關操作記錄

**特色功能：**
- **實時狀態監控**：WebSocket實時更新網關在線狀態
- **智能負載顯示**：顯示網關當前負載和處理能力
- **固件管理**：支持WiFi和藍牙固件的遠程升級
- **網關統計**：實時統計在線/離線網關數量
- **批量操作**：支持批量網關管理操作

**操作功能：**
- 新增網關
- 編輯網關信息
- 刪除網關
- 重啟網關
- 固件升級
- 網關配置

<!-- 截圖預留位置 -->
![網關管理頁面截圖](screenshots/gateways-page.png)

---

## 5. 模板管理系統

### 5.1 模板列表頁面

**功能描述：**
管理EPD顯示模板，支持系統模板和門店專屬模板。

**主要功能：**
- 模板列表顯示
- 模板分類篩選
- 模板搜索功能
- 模板預覽顯示
- 批量模板操作

**特色功能：**
- **可視化模板預覽**：實時渲染模板元素
- **模板類型分類**：系統模板vs門店模板
- **多選批量操作**：支持批量刪除和管理
- **模板複製功能**：快速複製現有模板
- **響應式網格布局**：適配不同屏幕尺寸

<!-- 截圖預留位置 -->
![模板列表頁面截圖](screenshots/template-list-page.png)

### 5.2 模板編輯器

**功能描述：**
強大的可視化模板編輯器，支持拖拽式設計。

**主要功能：**
- 拖拽式元素添加
- 元素屬性編輯
- 多選元素操作
- 對齊和分佈工具
- 圖層管理

**特色功能：**
- **多種元素類型**：文字、圖片、QR碼、條碼、圖標等
- **智能對齊**：自動對齊輔助線
- **多選功能**：圈選和Shift+點擊多選
- **實時預覽**：即時查看設計效果
- **元素旋轉**：支持任意角度旋轉
- **縮放功能**：畫布縮放查看細節

<!-- 截圖預留位置 -->
![模板編輯器截圖](screenshots/template-editor.png)

### 5.3 系統模板頁面

**功能描述：**
管理系統級別的通用模板，可供所有門店使用。

**主要功能：**
- 系統模板列表
- 模板創建和編輯
- 模板權限管理
- 模板版本控制

<!-- 截圖預留位置 -->
![系統模板頁面截圖](screenshots/system-templates-page.png)

---

## 6. 數據管理系統

### 6.1 門店數據管理頁面

**功能描述：**
管理門店專屬的數據內容，支持動態欄位定義。

**主要功能：**
- 數據列表顯示
- 數據新增編輯
- 數據搜索篩選
- 批量數據操作
- 數據導入導出

**特色功能：**
- **動態欄位支持**：根據系統配置顯示不同欄位
- **流水號自動生成**：S/N欄位自動編號
- **數據驗證機制**：確保數據完整性和準確性
- **實時數據同步**：WebSocket實時更新數據變更
- **Excel導入導出**：支持批量數據處理

<!-- 截圖預留位置 -->
![門店數據管理頁面截圖](screenshots/database-page.png)

### 6.2 系統數據管理頁面

**功能描述：**
管理系統級別的共用數據，可作為門店數據的模板。

**主要功能：**
- 系統數據維護
- 數據模板管理
- 數據同步功能
- 數據權限控制

<!-- 截圖預留位置 -->
![系統數據管理頁面截圖](screenshots/system-data-page.png)

---

## 7. 系統配置管理

### 7.1 系統配置頁面

**功能描述：**
系統的核心配置中心，包含多個配置分頁。

**主要分頁：**
- 數據欄位配置
- 用戶信息管理
- 資源管理
- 網關設置
- 參數設定
- 金鑰工具
- 語言設置

<!-- 截圖預留位置 -->
![系統配置頁面截圖](screenshots/system-config-page.png)

### 7.2 數據欄位配置分頁

**功能描述：**
定義系統中使用的動態數據欄位。

**主要功能：**
- 欄位定義管理
- 欄位類型設置
- 欄位驗證規則
- 欄位顯示配置

<!-- 截圖預留位置 -->
![數據欄位配置截圖](screenshots/data-field-config.png)

### 7.3 網關設置分頁

**功能描述：**
配置網關相關的系統參數。

**主要功能：**
- 網關連接參數
- 超時設置
- 重試機制配置
- 並發數控制

<!-- 截圖預留位置 -->
![網關設置截圖](screenshots/gateway-settings.png)

---

## 8. 權限管理系統

### 8.1 權限管理頁面

**功能描述：**
管理系統用戶的權限和角色分配。

**主要功能：**
- 用戶權限配置
- 角色管理
- 權限組設定
- 訪問控制管理

<!-- 截圖預留位置 -->
![權限管理頁面截圖](screenshots/permission-management-page.png)

---

## 9. 刷圖計劃管理

### 9.1 刷圖計劃列表頁面

**功能描述：**
管理自動化的設備刷圖計劃，支持定時和條件觸發的批量刷圖任務。

**主要功能：**
- 計劃列表顯示
- 計劃創建和編輯
- 計劃執行狀態監控
- 計劃執行歷史查看
- 計劃啟用/停用控制

**特色功能：**
- **多種觸發方式**：支持單次、每日、每週等定時觸發
- **智能設備選擇**：支持全部設備、指定設備、條件篩選等多種選擇方式
- **執行狀態追蹤**：實時顯示計劃執行進度和結果
- **失敗重試機制**：自動重試失敗的刷圖任務
- **統計分析**：提供詳細的執行統計和成功率分析

<!-- 截圖預留位置 -->
![刷圖計劃列表頁面截圖](screenshots/refresh-plan-list-page.png)

### 9.2 刷圖計劃編輯頁面

**功能描述：**
創建和編輯刷圖計劃的詳細配置。

**主要功能：**
- 計劃基本信息設定
- 觸發條件配置
- 目標設備選擇
- 執行參數設定
- 計劃預覽和驗證

**特色功能：**
- **可視化時間設定**：直觀的時間選擇器
- **設備篩選器**：靈活的設備選擇條件
- **參數智能推薦**：根據設備數量推薦最佳參數
- **計劃衝突檢測**：避免計劃時間衝突

<!-- 截圖預留位置 -->
![刷圖計劃編輯頁面截圖](screenshots/refresh-plan-edit-page.png)

---

## 10. 系統日誌管理

### 10.1 系統日誌頁面

**功能描述：**
查看和管理系統操作日誌，便於問題追蹤和審計。

**主要功能：**
- 日誌列表顯示
- 日誌搜索篩選
- 日誌詳情查看
- 日誌導出功能
- 日誌級別篩選
- 時間範圍查詢

**特色功能：**
- **實時日誌更新**：新日誌即時顯示
- **多維度篩選**：按用戶、操作類型、時間等篩選
- **日誌詳情展開**：點擊查看完整日誌信息
- **批量導出**：支持Excel和CSV格式導出

<!-- 截圖預留位置 -->
![系統日誌頁面截圖](screenshots/system-logs-page.png)

---

## 11. 統計分析系統

### 11.1 統計分析頁面

**功能描述：**
提供系統運營數據的統計分析和可視化展示。

**主要功能：**
- 設備使用統計
- 網關性能分析
- 刷圖成功率統計
- 用戶操作分析
- 系統健康度監控

**特色功能：**
- **多維度圖表**：柱狀圖、折線圖、餅圖等多種展示方式
- **時間範圍選擇**：靈活的時間維度分析
- **數據鑽取**：點擊圖表查看詳細數據
- **報表導出**：生成PDF和Excel報表
- **實時數據更新**：數據自動刷新

<!-- 截圖預留位置 -->
![統計分析頁面截圖](screenshots/analytics-page.png)

---

## 12. 移動應用程序

### 12.1 App登入頁面

**功能描述：**
移動端的用戶認證界面，提供簡潔直觀的登入體驗。

**主要功能：**
- 用戶名/密碼登入
- 服務器地址配置
- 記住登入狀態
- 多語言支持
- 自動登入功能

**特色功能：**
- **響應式設計**：適配各種移動設備屏幕
- **離線緩存**：記住服務器配置和用戶偏好
- **安全驗證**：支持生物識別登入（指紋/面部識別）
- **錯誤提示**：友好的錯誤信息顯示

<!-- 截圖預留位置 -->
![App登入頁面截圖](screenshots/app-login-page.png)

### 12.2 App門店選擇頁面

**功能描述：**
選擇要管理的門店，支持多門店切換。

**主要功能：**
- 門店列表顯示
- 門店搜索功能
- 門店詳情查看
- 快速門店切換
- 最近使用門店

**特色功能：**
- **卡片式布局**：美觀的門店信息展示
- **智能搜索**：支持門店名稱和ID搜索
- **狀態指示**：顯示門店在線狀態和設備數量
- **收藏功能**：標記常用門店

<!-- 截圖預留位置 -->
![App門店選擇頁面截圖](screenshots/app-store-selection-page.png)

### 12.3 App網關掃描頁面

**功能描述：**
EPD Manager App的核心功能，自動發現和註冊本地網關。

**主要功能：**
- **UDP自動掃描**：自動發現局域網內的網關設備
- **網關列表顯示**：顯示發現的網關及其詳細信息
- **一鍵註冊**：快速將網關註冊到雲端系統
- **網關配置**：配置網關的WebSocket連接信息
- **狀態監控**：實時顯示網關連接狀態

**特色功能：**
- **智能發現**：基於UDP廣播的自動網關發現
- **批量操作**：支持批量選擇和註冊網關
- **配置驗證**：自動驗證網關配置的正確性
- **進度追蹤**：顯示註冊和配置進度

<!-- 截圖預留位置 -->
![App網關掃描頁面截圖](screenshots/app-gateway-scan-page.png)

### 12.4 App網關管理頁面

**功能描述：**
管理已註冊的網關設備，監控運行狀態。

**主要功能：**
- 網關列表查看
- 網關狀態監控
- 網關重啟操作
- 網關配置修改
- 網關刪除管理

**特色功能：**
- **實時狀態**：WebSocket實時更新網關狀態
- **快速操作**：滑動手勢快速操作
- **狀態圖標**：直觀的狀態指示器
- **詳情查看**：點擊查看網關詳細信息

<!-- 截圖預留位置 -->
![App網關管理頁面截圖](screenshots/app-gateway-management-page.png)

### 12.5 App設備管理頁面

**功能描述：**
移動端的設備管理功能，支持設備的基本操作。

**主要功能：**
- 設備列表查看
- 設備添加功能
- 設備狀態監控
- 設備基本操作
- 設備搜索篩選

**特色功能：**
- **下拉刷新**：手勢刷新設備列表
- **無限滾動**：大量設備的流暢瀏覽
- **快速添加**：掃描或手動添加設備
- **狀態同步**：與Web端實時同步

<!-- 截圖預留位置 -->
![App設備管理頁面截圖](screenshots/app-device-management-page.png)

### 12.6 App WebSocket控制台頁面

**功能描述：**
開發和調試用的WebSocket通信監控界面。

**主要功能：**
- WebSocket連接狀態顯示
- 實時消息日誌
- 手動命令發送
- 連接參數配置
- 調試信息查看

**特色功能：**
- **實時日誌**：滾動顯示WebSocket消息
- **消息過濾**：按類型篩選消息
- **命令歷史**：保存常用命令
- **連接管理**：手動控制連接狀態

<!-- 截圖預留位置 -->
![App WebSocket控制台頁面截圖](screenshots/app-websocket-console-page.png)

---

## 13. 特殊功能頁面

### 13.1 顏色測試工具頁面

**功能描述：**
專門用於測試EPD設備顏色顯示效果的工具頁面。

**主要功能：**
- 顏色效果預覽
- 不同EPD型號適配
- 顏色轉換測試
- 顯示效果對比

**特色功能：**
- **實時預覽**：即時查看顏色轉換效果
- **多種模式**：黑白、紅黑白、黃黑白等模式
- **閾值調整**：可調整顏色轉換閾值
- **批量測試**：批量測試多種顏色組合

<!-- 截圖預留位置 -->
![顏色測試工具頁面截圖](screenshots/color-test-page.png)

### 13.2 浮動操作按鈕

**功能描述：**
系統右下角的智能浮動按鈕，提供快速訪問常用功能。

**主要功能：**
- AI助手快速訪問
- Bug回報功能（測試模式）
- 快速操作入口
- 系統狀態指示

**特色功能：**
- **智能顯示**：根據用戶權限和模式動態顯示
- **動畫效果**：流暢的展開和收起動畫
- **快捷操作**：一鍵訪問常用功能
- **狀態感知**：根據系統狀態調整顯示

<!-- 截圖預留位置 -->
![浮動操作按鈕截圖](screenshots/floating-action-button.png)

### 13.3 AI助手模態窗口

**功能描述：**
展示AI助手功能的介紹和未來規劃。

**主要功能：**
- AI功能介紹
- 開發進度展示
- 功能預告
- 用戶期待收集

**特色功能：**
- **未來願景**：展示AI助手的強大功能
- **進度透明**：實時更新開發進度
- **用戶參與**：收集用戶需求和建議
- **期待管理**：合理設置用戶期望

<!-- 截圖預留位置 -->
![AI助手模態窗口截圖](screenshots/ai-assistant-modal.png)

---

## 14. 頁面間的數據流和交互

### 14.1 實時數據同步

**WebSocket實時更新：**
- 設備狀態變化即時推送到設備管理頁面
- 網關狀態變化即時推送到網關管理頁面
- 數據變更即時推送到相關頁面
- 刷圖進度即時更新到進度監控頁面

### 14.2 頁面間導航

**智能導航系統：**
- 基於用戶權限的動態菜單
- 麵包屑導航支持
- 快速返回和前進功能
- 頁面狀態保持

### 14.3 數據一致性

**統一數據管理：**
- 全局狀態管理確保數據一致性
- 自動數據同步和更新
- 衝突檢測和解決機制
- 離線數據緩存和同步

---

## 總結

EPD Manager 提供了完整的電子紙顯示器管理解決方案，從Web端的全功能管理平台到移動端的便攜操作工具，涵蓋了EPD系統管理的各個方面。每個頁面都經過精心設計，注重用戶體驗和操作效率，同時提供了強大的實時監控和智能化管理功能。

### 系統特色總覽

**🎯 統一的用戶體驗**
- Web端和移動端保持一致的設計語言
- 響應式設計適配各種設備和屏幕尺寸
- 直觀的操作流程和交互設計

**⚡ 實時狀態更新**
- 基於WebSocket的即時狀態同步
- 毫秒級的數據更新響應
- 自動斷線重連和狀態恢復

**🤖 智能化管理**
- AI輔助的網關選擇和任務調度
- 智能排程刷圖系統
- 自動故障檢測和處理

**📊 可視化操作**
- 直觀的圖形界面和數據展示
- 豐富的圖表和統計分析
- 實時預覽和效果展示

**🔧 靈活配置**
- 高度可配置的系統參數
- 細粒度的權限管理
- 動態欄位定義和管理

**📱 跨平台支持**
- Web、iOS、Android全平台覆蓋
- 統一的API和數據同步
- 離線功能和數據緩存

### 頁面功能統計

**Web端頁面：** 20+ 個主要功能頁面
**移動端頁面：** 6+ 個核心功能頁面
**模態窗口：** 15+ 個功能模態窗口
**配置分頁：** 7+ 個系統配置分頁

### 截圖文件夾結構建議

為了方便管理截圖，建議按以下結構組織截圖文件：

```
screenshots/
├── web/
│   ├── auth/
│   │   ├── login-page.png
│   │   └── change-password-page.png
│   ├── store/
│   │   ├── store-overview-page.png
│   │   ├── store-management-page.png
│   │   └── store-settings-page.png
│   ├── device/
│   │   ├── devices-page.png
│   │   ├── device-detail-page.png
│   │   └── bind-device-data-modal.png
│   ├── gateway/
│   │   └── gateways-page.png
│   ├── template/
│   │   ├── template-list-page.png
│   │   ├── template-editor.png
│   │   └── system-templates-page.png
│   ├── data/
│   │   ├── database-page.png
│   │   └── system-data-page.png
│   ├── system/
│   │   ├── system-config-page.png
│   │   ├── data-field-config.png
│   │   ├── gateway-settings.png
│   │   ├── permission-management-page.png
│   │   ├── system-logs-page.png
│   │   └── analytics-page.png
│   ├── plan/
│   │   ├── refresh-plan-list-page.png
│   │   └── refresh-plan-edit-page.png
│   └── special/
│       ├── color-test-page.png
│       ├── floating-action-button.png
│       └── ai-assistant-modal.png
└── mobile/
    ├── app-login-page.png
    ├── app-store-selection-page.png
    ├── app-gateway-scan-page.png
    ├── app-gateway-management-page.png
    ├── app-device-management-page.png
    └── app-websocket-console-page.png
```

這個詳細的頁面功能介紹文件為每個主要功能頁面都預留了截圖位置，方便您後續補充實際的頁面截圖，完善產品文檔。
