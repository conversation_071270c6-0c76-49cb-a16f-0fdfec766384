# EPD-Manager 專案計劃

## 專案概述

EPD-Manager 是一個電子紙顯示器 (Electronic Paper Display) 模板管理系統，用於創建、編輯和管理顯示在電子紙設備上的模板。該專案使用 React、TypeScript、Vite 以及 TailwindCSS 構建，採用 Zustand 作為狀態管理解決方案。

## 技術棧

- **框架**: React 18.3.1
- **語言**: TypeScript 5.5.3
- **構建工具**: Vite 5.4.2
- **狀態管理**: Zustand 4.5.2
- **CSS 框架**: TailwindCSS 3.4.1
- **圖標庫**: Lucide React 0.344.0
- **拖放功能**: @dnd-kit (core, sortable, utilities)
- **ID生成**: uuid 11.1.0
- **代碼檢查**: ESLint 9.9.1

## 目錄結構

```
epd-manager/
├── db/                  # 數據庫相關
│   └── Resource/        # 資源文件
├── docs/                # 項目文檔
│   ├── db.md           # 數據庫文檔
│   └── multi-selection.md # 多選功能文檔
├── figma-plugin/        # Figma 插件相關
│   └── assets/         # 插件資源
├── plugin/              # 插件相關代碼
├── scripts/             # 腳本文件
├── server/              # 服務器端代碼
│   ├── index.js        # 服務器入口
│   └── package.json    # 服務器依賴
├── src/                 # 源代碼
│   ├── App.tsx             # 主應用組件
│   ├── index.css           # 全局樣式
│   ├── main.tsx            # 應用入口點
│   ├── store.ts            # Zustand 狀態管理
│   ├── types.ts            # TypeScript 類型定義
│   ├── vite-env.d.ts       # Vite 環境類型
│   ├── db/                 # 客戶端數據庫相關
│   │   ├── database.ts     # 數據庫操作函數
│   │   └── mongodb.ts      # MongoDB 連接配置
│   └── components/         # 組件目錄
│       ├── AddTemplateModal.tsx  # 添加模板的模態窗口
│       ├── DatabasePage.tsx      # 數據庫管理頁面
│       ├── EmptyPage.tsx         # 空頁面顯示
│       ├── Sidebar.tsx           # 側邊欄導航
│       ├── StoreOverviewPage.tsx # 店鋪概覽頁面
│       ├── TemplateEditor.tsx    # 模板編輯器主組件
│       ├── TemplateList.tsx      # 模板列表顯示
│       ├── editor/              # 模板編輯器相關組件
│       │   ├── Canvas.tsx            # 畫布組件
│       │   ├── canvasUtils.tsx       # 畫布相關工具函數
│       │   ├── CommonComponents.tsx   # 共用UI組件
│       │   ├── elementOperations.tsx  # 元素操作相關邏輯
│       │   ├── ElementPropertiesPanel.tsx # 元素屬性面板
│       │   ├── InfoPanel.tsx          # 模板信息面板
│       │   ├── ToolsPanel.tsx         # 工具面板組件
│       │   ├── useEditorState.tsx     # 編輯器狀態管理Hook
│       │   ├── elements/              # 元素渲染相關組件
│       │   │   ├── CircleElement.tsx    # 圓形元素渲染
│       │   │   ├── ElementRenderer.tsx  # 元素渲染器
│       │   │   ├── IconComponent.tsx    # 圖標組件
│       │   │   ├── ImageElement.tsx     # 圖片元素渲染
│       │   │   ├── LineElement.tsx      # 線段元素渲染
│       │   │   ├── MultilineTextElement.tsx # 多行文本元素
│       │   │   ├── RectangleElement.tsx # 矩形元素渲染
│       │   │   ├── ShapeElement.tsx     # 形狀元素基類
│       │   │   └── TextElement.tsx      # 文本元素渲染
│       │   └── properties/            # 屬性編輯相關組件
│       │       ├── CommonProperties.tsx  # 通用屬性編輯
│       │       ├── DefaultProperties.tsx # 預設屬性設置
│       │       ├── FormComponents.tsx    # 屬性表單組件
│       │       ├── IconProperties.tsx    # 圖標屬性編輯
│       │       ├── ImageProperties.tsx   # 圖片屬性編輯
│       │       ├── LineProperties.tsx    # 線段屬性編輯
│       │       └── TextProperties.tsx    # 文本屬性編輯
│       └── system-config/          # 系統配置相關組件
│           ├── DataFieldTab.tsx      # 數據字段配置
│           ├── GatewaySettingsTab.tsx # 網關設置
│           ├── KeyToolTab.tsx        # 金鑰工具
│           ├── ParamSettingTab.tsx   # 參數設置
│           ├── ResourceManagementTab.tsx # 資源管理
│           ├── SystemConfigPage.tsx  # 系統配置頁面
│           └── UserInfoTab.tsx       # 用戶信息配置
├── index.html            # HTML入口
├── eslint.config.js      # ESLint配置
├── package.json          # 依賴配置
├── postcss.config.js     # PostCSS配置
├── tailwind.config.js    # Tailwind配置
├── tsconfig.json         # TypeScript配置
├── vite.config.ts        # Vite配置
└── README.md             # 項目說明
```

## 關鍵數據模型

### Template (模板)

```typescript
interface Template {
  id: string;
  name: string;
  type: 'label' | 'system';
  screenSize: string;
  color: string;
  orientation: string;
  elements: TemplateElement[];
}
```

### TemplateElement (模板元素)

```typescript
interface TemplateElement {
  id: string;
  type: 'text' | 'multiline-text' | 'image' | 'qr-code' | 'barcode' | 'icon' | 'rectangle' | 'square' | 'line' | 'circle' | 'ellipse';
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;      // 旋轉角度
  content?: string;       // 文本內容
  fontSize?: number;      // 文本大小
  fontFamily?: string;    // 字體
  lineWidth?: number;     // 線條寬度
  lineColor?: string;     // 線條/文本顏色
  fillColor?: string;     // 填充顏色
  iconType?: string;      // 圖標類型
  imageUrl?: string;      // 圖片URL
}
```

## 主要功能

### 1. 模板管理

- **模板列表**: 顯示所有可用模板，支持分頁和搜索
- **添加模板**: 通過模態窗口創建新模板
- **編輯模板**: 在模板編輯器中編輯模板內容和屬性
- **刪除模板**: 刪除單個或多個模板
- **多選操作**: 支持批量選擇和操作模板

### 2. 模板編輯器

- **動態元素**: 添加文本、多行文本、圖像、QR碼、條形碼和圖標
- **靜態元素**: 添加矩形、正方形、線條、圓形、橢圓和圖像
- **元素屬性編輯**: 根據元素類型提供特定的屬性編輯面板
- **多選功能**: 支持圈選、Shift+點擊多選及多選元素操作
- **對齊與分佈**: 多選元素時支持多種對齊和分佈操作
- **元素旋轉**: 支持元素的旋轉操作
- **圖層管理**: 管理元素的層級順序
- **縮放功能**: 支持畫布縮放
- **模板預覽**: 實時預覽模板效果

### 3. 多選功能

- **圈選操作**: 使用滑鼠拖曳在畫布上創建選擇框，選取多個元素
- **Shift+點擊多選**: 在現有選擇基礎上添加或移除元素，實現自定義組合選擇
- **多選移動**: 同時移動多個選中元素，保持它們之間的相對位置關係
- **對齊操作**: 支持左對齊、居中對齊、右對齊、頂部對齊、垂直居中和底部對齊
- **分佈操作**: 支持水平和垂直方向的元素分佈，使元素間距均勻
- **多選狀態防護**: 使用時間窗口機制（300ms）防止多選移動與單選操作衝突
- **多選視覺反饋**: 選中的元素顯示特定樣式，包括半透明背景和虛線邊框
- **多選框顯示**: 顯示包含所有已選元素的拖動覆蓋層，提供良好的交互體驗

### 4. 系統配置

- **數據字段配置**: 自定義資料欄位結構
- **網關設置**: 管理與電子紙設備的通訊網關
- **參數設置**: 配置系統運行參數
- **資源管理**: 管理系統資源文件
- **用戶信息**: 管理用戶權限與資訊

### 5. 系統導航

- **側邊欄**: 提供模板管理、設備管理、部署管理、數據分析、用戶管理和系統設置等功能入口
- **可折疊**: 支持折疊側邊欄以擴大工作區

## 狀態管理 (Zustand)

### Template Store

```typescript
interface TemplateStore {
  templates: Template[];
  selectedTemplate: Template | null;
  selectedTemplateIds: string[];
  addTemplate: (template: Template) => void;
  updateTemplate: (template: Template) => void;
  deleteTemplate: (id: string) => void;
  setSelectedTemplate: (template: Template | null) => void;
  toggleTemplateSelection: (id: string) => void;
  selectAllTemplates: (selected: boolean) => void;
  deleteSelectedTemplates: () => void;
}
```

### 編輯器狀態

編輯器狀態使用自定義 Hook `useEditorState` 管理，包含以下核心功能：

- **元素管理**: 添加、更新、刪除模板元素
- **選擇管理**:
  - 單選元素管理 (`selectedElementId`)
  - 多選元素管理 (`selectedElementIds`, `isMultiSelectMode`)
  - 圈選狀態管理 (`selectionBox`)
  - 多選防護機制 (`isMultiMoving`, `preventSelectTimeoutRef`)
- **元素操作**:
  - 移動元素 (`moveSelectedElements`)
  - 對齊元素 (`alignSelectedElements`)
  - 分佈元素 (`distributeSelectedElements`)
  - 旋轉元素
- **其他功能**:
  - 縮放控制 (`zoom`)
  - 工具選擇 (`selectedTool`)
  - 繪製狀態 (`isDrawing`)

## 實現階段

### 第一階段: 基礎框架搭建 [已完成]

1. 配置 React + TypeScript + Vite 環境
2. 集成 TailwindCSS
3. 設置基本目錄結構
4. 建立核心數據模型

### 第二階段: 實現核心功能 [已完成]

1. 實現模板管理 (列表、添加、刪除)
2. 實現模板編輯器基本功能
   - 畫布渲染 
   - 元素渲染 
   - 元素屬性編輯 
   - 元素操作 (移動、調整大小、旋轉)
3. 實現側邊欄導航

### 第三階段: 高級功能實現 [已完成]

1. 多選功能實現
   - 圈選操作完整流程
   - Shift+點擊多選機制
   - 多選元素移動帶防護機制
   - 對齊與分佈操作
   - 多選視覺反饋
2. 完善模板編輯器
   - 新增多行文本、圖標等元素類型
   - 完善元素旋轉功能
   - 添加填充顏色支持
   - 增強元素互動
3. 圖層管理實現

### 第四階段: 系統配置與數據管理 [進行中]

1. 系統配置模塊實現
   - 數據字段配置
   - 網關設置
   - 參數設置
   - 資源管理
   - 用戶信息管理
2. 資料庫連接與管理
   - MongoDB 整合
   - 資源管理
3. 優化用戶界面和體驗
   - 優化選擇機制
   - 提升視覺反饋
4. 完善模板預覽功能

### 第五階段: 功能模塊擴展 [計劃中]

1. 添加設備管理功能模塊
2. 添加部署管理功能模塊
3. 實現數據分析功能
4. 擴展用戶管理功能
5. 完善系統設置功能
6. 移動端適配支持
7. 多語言支持

## Figma 插件集成 [計劃中]

計劃開發 Figma 插件，支持：
1. 從 Figma 設計導出到 EPD-Manager
2. 模板元素屬性映射
3. 批量導出功能
4. 元素樣式保留
5. 圖層結構保留

## 部署指南

### 開發環境

1. 克隆項目
   ```
   git clone <repository-url>
   cd epd-manager
   ```

2. 安裝依賴
   ```
   npm install
   ```

3. 啟動開發服務器
   ```
   npm run dev
   ```

### 生產環境

1. 構建項目
   ```
   npm run build
   ```

2. 預覽生產版本
   ```
   npm run preview
   ```

## 未來擴展

1. 支持更多種類的電子紙屏幕
2. 實現模板版本控制
3. 添加協作功能
4. 整合設備管理和部署流程
5. 添加數據統計和分析功能
6. 模板庫功能，支持導入和導出模板
7. 優化元素屬性編輯面板，支持更多自定義設置
8. 實現複雜形狀和路徑編輯功能
9. 添加模板變量支持，實現動態內容
10. 整合條碼、QR碼實時生成預覽功能