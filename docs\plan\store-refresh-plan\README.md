# 門店刷圖計畫系統企劃

## 概述

門店刷圖計畫系統是一個針對各門店獨立運作的自動化刷圖調度系統，旨在提供穩定、可擴展的設備預覽圖更新服務，並保留與後續條件觸發功能的延伸性。

**實現狀態：** ✅ **已完成並可正常運行**

## 設計目標

### 1. 各門店獨立
- 每個門店擁有獨立的刷圖計畫配置
- 門店間計畫互不干擾
- 支援門店級別的權限控制

### 2. 穩定運作
- 可靠的任務調度機制
- 完善的錯誤處理和重試機制
- 系統資源合理分配

### 3. 延伸性設計
- 模組化架構支援功能擴展
- 預留條件觸發接口
- 支援多種觸發方式

## 系統架構

```mermaid
graph TD
    A[門店設置頁面] --> B[刷圖計畫管理]
    B --> C[計畫配置模組]
    B --> D[任務調度模組]
    B --> E[執行引擎模組]
    
    C --> C1[基礎設定]
    C --> C2[設備選擇]
    C --> C3[觸發條件]
    C --> C4[執行策略]
    
    D --> D1[定時調度器]
    D --> D2[條件監控器]
    D --> D3[任務隊列]
    
    E --> E1[預覽圖生成]
    E --> E2[網關通信]
    E --> E3[狀態追蹤]
    E --> E4[結果回報]
```

## 功能模組設計

### 1. 計畫配置模組

#### 1.1 基礎設定
- **計畫名稱**：用戶自定義的計畫識別名稱
- **計畫描述**：詳細說明計畫用途和目標
- **啟用狀態**：計畫的啟用/停用控制
- **優先級**：計畫執行的優先順序（高/中/低）

#### 1.2 刷圖對象選擇
- **指定 MAC 地址**：
  - 直接選擇特定設備的 MAC 地址
  - 支援多選，所有選中的設備都會執行刷圖
  - 不考慮設備的數據綁定狀態
- **指定門店數據**：
  - 選擇特定的門店數據項目
  - 只有同時滿足以下條件的設備才會執行刷圖：
    - 設備已綁定模板
    - 設備的數據綁定包含選中的門店數據項目
  - 支援多選門店數據項目
- **選擇限制**：
  - 每個計畫只能選擇一種對象類型（MAC 或門店數據）
  - 在選定的類型內可以多選具體項目

#### 1.3 觸發條件
- **執行類型**：
  - 單次執行：指定具體日期時間執行一次
  - 循環執行：可設定每天或每週重複執行
- **執行時間點**：
  - 具體時間設定（時:分）
  - 支援多個時間點設定
- **條件觸發**（預留）：
  - 數據變更觸發
  - 設備狀態觸發
  - 外部事件觸發

#### 1.4 執行策略
- **傳送邏輯**：
  - 完全沿用設備管理中的批量傳送邏輯
  - 支援智能網關選擇機制
  - 自動/手動網關模式處理
- **系統設定整合**：
  - 並發控制：使用系統設定中的網關並發數
  - 重試機制：使用系統設定中的隊列重試次數
  - 超時設定：使用系統設定中的超時時間
  - 所有傳送參數都從系統設定中讀取，確保一致性

### 2. 任務調度模組

#### 2.1 定時調度器
```typescript
interface ScheduleConfig {
  type: 'once' | 'recurring' | 'cron';
  datetime?: string;           // 單次執行時間
  interval?: number;           // 週期間隔（秒）
  cronExpression?: string;     // Cron 表達式
  timezone?: string;           // 時區設定
}
```

#### 2.2 條件監控器（預留）
```typescript
interface ConditionConfig {
  type: 'data_change' | 'device_status' | 'external_event';
  conditions: {
    field?: string;            // 監控欄位
    operator?: string;         // 比較運算子
    value?: any;              // 比較值
    threshold?: number;        // 閾值
  }[];
}
```

#### 2.3 任務隊列
- 基於現有的批量發送隊列系統
- 支援任務優先級排序
- 提供任務狀態追蹤

### 3. 執行引擎模組

#### 3.1 預覽圖生成
- 整合現有的 `sendPreviewToGateway` 服務
- 支援批量預覽圖生成
- 提供生成進度回報

#### 3.2 網關通信
- 利用現有的 WebSocket 通信機制
- 支援智能網關選擇
- 提供傳輸狀態監控

#### 3.3 狀態追蹤
- 計畫執行狀態記錄
- 設備處理結果統計
- 錯誤日誌收集

#### 3.4 結果回報
- 執行結果通知
- 統計報表生成
- 異常警報機制

## 數據模型設計

### 1. 刷圖計畫模型
```typescript
interface RefreshPlan {
  _id: string;
  storeId: string;             // 所屬門店ID
  name: string;                // 計畫名稱
  description?: string;        // 計畫描述
  enabled: boolean;            // 啟用狀態
  priority: 'high' | 'medium' | 'low';  // 優先級
  
  // 刷圖對象選擇配置
  targetSelection: {
    type: 'mac_addresses' | 'store_data';  // 對象類型：MAC地址 或 門店數據
    macAddresses?: string[];               // 指定的MAC地址列表
    storeDataIds?: string[];              // 指定的門店數據ID列表
  };
  
  // 觸發配置
  trigger: {
    type: 'once' | 'daily' | 'weekly';  // 執行類型
    executeTime: string;                 // 執行時間 (HH:mm 格式)
    executeDate?: string;                // 單次執行的日期 (YYYY-MM-DD)
    weekDays?: number[];                 // 週期執行的星期 (0-6, 0=週日)
    conditions?: ConditionConfig[];      // 預留條件觸發
  };
  
  // 執行策略（強制使用系統設定）
  execution: {
    useSystemSettings: boolean;        // 是否使用系統設定（強制為 true，不可關閉）
    // 智能網關選擇取決於裝置本身的設定，不再在計畫層級配置
    // 以下參數從系統設定中讀取，不在計畫中存儲
    // concurrency: 從系統設定的網關並發數讀取
    // retryAttempts: 從系統設定的隊列重試次數讀取
    // timeout: 從系統設定的超時時間讀取
    // enableSmartSelection: 固定啟用，具體行為取決於裝置設定
  };
  
  // 狀態信息
  status: 'active' | 'inactive' | 'running' | 'error';
  lastRun?: Date;             // 最後執行時間
  nextRun?: Date;             // 下次執行時間
  
  // 統計信息
  statistics: {
    totalRuns: number;         // 總執行次數
    successRuns: number;       // 成功次數
    failedRuns: number;        // 失敗次數
    lastRunResult?: ExecutionResult;
  };
  
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;          // 創建者ID
}
```

### 2. 執行記錄模型
```typescript
interface ExecutionRecord {
  _id: string;
  planId: string;             // 計畫ID
  storeId: string;            // 門店ID
  startTime: Date;            // 開始時間
  endTime?: Date;             // 結束時間
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  
  // 執行結果
  result: {
    totalDevices: number;      // 總設備數
    successDevices: number;    // 成功設備數
    failedDevices: number;     // 失敗設備數
    skippedDevices: number;    // 跳過設備數
    processingTime: number;    // 處理時間（毫秒）
  };
  
  // 詳細結果
  deviceResults: {
    deviceId: string;
    status: 'success' | 'failed' | 'skipped';
    error?: string;
    processingTime?: number;
  }[];
  
  // 錯誤信息
  errors?: {
    type: string;
    message: string;
    timestamp: Date;
  }[];
}
```

## 用戶界面設計

### 1. 計畫列表頁面
- 顯示當前門店的所有刷圖計畫
- 支援計畫的啟用/停用切換
- 提供計畫執行狀態和統計信息
- 支援計畫的新增、編輯、刪除操作

### 2. 計畫配置頁面
- 分步驟的計畫配置向導
- 直觀的設備選擇界面
- 靈活的觸發條件設定
- 詳細的執行策略配置

### 3. 執行監控頁面
- 實時顯示計畫執行狀態
- 提供執行進度和結果統計
- 支援執行歷史查詢
- 提供錯誤日誌查看

### 4. 統計報表頁面
- 計畫執行成功率統計
- 設備更新頻率分析
- 系統性能指標監控
- 可導出的報表功能

## 技術實現要點

### 1. 門店隔離
- 在所有 API 中加入門店 ID 驗證
- 數據庫查詢時強制加入門店過濾條件
- 權限檢查確保用戶只能操作所屬門店的計畫

### 2. 任務調度
- 使用 `node-schedule` 庫實現定時任務
- 建立任務註冊表管理所有活動計畫
- 實現任務的動態添加、修改、刪除

### 3. 執行引擎
- 整合現有的 `sendMultipleDevicePreviewsToGateways` 功能
- 實現執行狀態的實時追蹤和回報
- 提供執行結果的持久化存儲

### 4. 錯誤處理
- 實現多層次的錯誤捕獲和處理
- 提供詳細的錯誤日誌記錄
- 支援自動重試和手動重試機制

### 5. 性能優化
- 實現任務執行的資源控制
- 提供系統負載監控和調節
- 支援任務執行的優先級管理

## 開發階段規劃

### 第一階段：基礎框架
1. 建立數據模型和 API 接口
2. 實現基本的計畫 CRUD 功能
3. 建立簡單的定時調度機制

### 第二階段：核心功能
1. 實現完整的任務調度系統
2. 整合預覽圖生成和發送功能
3. 建立執行狀態追蹤機制

### 第三階段：用戶界面
1. 開發計畫管理界面
2. 實現執行監控頁面
3. 建立統計報表功能

### 第四階段：優化擴展
1. 性能優化和穩定性提升
2. 預留條件觸發接口實現
3. 系統整合測試和部署

## 風險評估與應對

### 1. 技術風險
- **風險**：任務調度衝突
- **應對**：實現任務鎖機制，避免重複執行

### 2. 性能風險
- **風險**：大量計畫同時執行影響系統性能
- **應對**：實現資源控制和負載均衡

### 3. 數據風險
- **風險**：執行記錄數據量過大
- **應對**：實現數據歸檔和清理機制

### 4. 用戶體驗風險
- **風險**：複雜的配置界面影響易用性
- **應對**：提供預設模板和配置向導

## 總結

門店刷圖計畫系統將為各門店提供獨立、穩定、可擴展的自動化刷圖服務。通過模組化的設計和完善的錯誤處理機制，確保系統的可靠性和可維護性。同時，預留的條件觸發接口為未來的功能擴展提供了良好的基礎。

## 相關文檔

本企劃包含以下詳細文檔：

### 📋 [技術實現詳細說明](./technical-implementation.md) ✅
- API 設計規範
- 數據庫設計方案
- 核心服務實現
- 系統整合要點

### 🎨 [用戶界面設計](./ui-design.md) ✅
- 整體設計原則
- 頁面結構設計
- 交互細節說明
- 響應式設計方案

### 🗺️ [實施路線圖](./implementation-roadmap.md) ✅
- 開發階段規劃
- 技術風險評估
- 資源需求分析
- 質量保證策略

### ⚙️ [參數規格詳細說明](./parameter-specifications.md) ✅
- 執行類型參數設計
- 執行時間點配置
- 刷圖對象選擇邏輯
- 傳送邏輯與系統設定整合

### 📊 [實現狀況報告](./implementation-status.md) 🆕
- 總體實現狀況
- 修正的關鍵問題
- 文檔同步更新
- 測試建議和後續優化

## 快速開始

### 1. 環境準備
確保您的開發環境包含以下組件：
- Node.js 16+
- MongoDB 4.4+
- React 18+
- 現有的 EPD 管理系統

### 2. 開發順序
建議按照以下順序進行開發：
1. **數據庫設計** → 建立基礎數據結構
2. **後端 API** → 實現核心業務邏輯
3. **任務調度** → 建立調度和執行機制
4. **前端界面** → 開發用戶交互界面
5. **系統整合** → 與現有系統整合測試

### 3. 關鍵整合點
- 整合現有的 `sendPreviewToGateway` 服務
- 利用現有的設備管理和門店管理 API
- 保持與現有權限系統的一致性
- 使用現有的 WebSocket 通信機制

## 聯絡信息

如有任何問題或建議，請聯絡開發團隊。
