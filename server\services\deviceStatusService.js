// server/services/deviceStatusService.js
const { MongoClient, ObjectId } = require('mongodb');

// 用於存儲數據庫連接函數
let getDbConnection = null;

// 數據庫集合名稱
const DEVICE_COLLECTION = 'devices';

// 設備離線閾值（毫秒）
const OFFLINE_THRESHOLD = 30 * 1000; // 30 秒

// 初始化數據庫連接
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 定時檢查任務間隔（毫秒）
const CHECK_INTERVAL = 10 * 1000; // 10 秒檢查一次
let checkInterval = null;

/**
 * 定時檢查設備狀態並將超時的設備設為離線
 */
const checkAndUpdateDeviceStatus = async () => {
  try {
    if (!getDbConnection) {
      console.warn('設備狀態服務：數據庫連接未初始化');
      return;
    }

    const { db } = await getDbConnection();
    const collection = db.collection(DEVICE_COLLECTION);

    const now = new Date();
    const threshold = new Date(now.getTime() - OFFLINE_THRESHOLD);

    // 查找所有在線但已超過閾值的設備
    const result = await collection.updateMany(
      {
        status: 'online',
        lastSeen: { $lt: threshold } 
      },
      {
        $set: {
          status: 'offline',
          updatedAt: now
        }
      }
    );

    if (result.modifiedCount > 0) {
      console.log(`設備狀態服務：${result.modifiedCount} 個設備已自動標記為離線 (超過 ${OFFLINE_THRESHOLD / 1000} 秒未更新)`);
    }
  } catch (error) {
    console.error('設備狀態服務錯誤：', error);
  }
};

/**
 * 啟動定時檢查設備狀態的任務
 */
const startDeviceStatusChecker = () => {
  if (checkInterval) {
    clearInterval(checkInterval);
  }
  
  console.log(`設備狀態服務：啟動自動檢查任務，每 ${CHECK_INTERVAL / 1000} 秒檢查一次`);
  console.log(`設備狀態服務：設備 ${OFFLINE_THRESHOLD / 1000} 秒未更新將被標記為離線`);
  
  checkInterval = setInterval(checkAndUpdateDeviceStatus, CHECK_INTERVAL);
  
  // 立即進行一次檢查
  checkAndUpdateDeviceStatus();
  
  return checkInterval;
};

/**
 * 停止定時檢查任務
 */
const stopDeviceStatusChecker = () => {
  if (checkInterval) {
    clearInterval(checkInterval);
    checkInterval = null;
    console.log('設備狀態服務：停止自動檢查任務');
  }
};

module.exports = {
  initDB,
  startDeviceStatusChecker,
  stopDeviceStatusChecker,
  checkAndUpdateDeviceStatus
};
