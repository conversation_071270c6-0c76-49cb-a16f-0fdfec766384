// 測試生成 JWT token
const jwt = require('jsonwebtoken');
const { buildWebSocketUrl } = require('../utils/networkUtils');

// 測試生成 Gateway 的 JWT token
const generateGatewayToken = () => {
  const jwtSecret = 'your_jwt_secret_here'; // 與服務器中相同的密鑰
  
  const payload = {
    gatewayId: 'test-gateway',
    storeId: 'test-store',
    type: 'gateway'
  };
  
  const token = jwt.sign(payload, jwtSecret, { expiresIn: '30d' });
  return token;
};

// 生成並顯示 token
const token = generateGatewayToken();
console.log('Gateway JWT Token:', token);

// 測試驗證 token
try {
  const decoded = jwt.verify(token, 'your_jwt_secret_here');
  console.log('Token 驗證成功，解碼後的數據:', decoded);
} catch (error) {
  console.error('Token 驗證失敗:', error);
}

// 構建 WebSocket URL，使用動態 IP 檢測
const wsPath = '/ws/store/test-store/gateway/test-gateway';
const wsBaseUrl = buildWebSocketUrl(wsPath, 3001);
const wsUrl = `${wsBaseUrl}?token=${token}`;
console.log('WebSocket 連接 URL:', wsUrl);
