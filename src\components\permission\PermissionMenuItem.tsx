import React from 'react';
import { usePermission } from '../../hooks/usePermission';

interface PermissionMenuItemProps {
  /**
   * 需要的權限標識符
   */
  permission?: string;
  
  /**
   * 需要的權限標識符列表（任意一個）
   */
  anyPermissions?: string[];
  
  /**
   * 需要的權限標識符列表（全部）
   */
  allPermissions?: string[];
  
  /**
   * 子元素
   */
  children: React.ReactNode;
}

/**
 * 權限菜單項組件
 * 根據用戶權限控制菜單項的顯示
 */
export const PermissionMenuItem: React.FC<PermissionMenuItemProps> = ({
  permission,
  anyPermissions,
  allPermissions,
  children
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermission();
  
  // 如果正在加載權限，不渲染菜單項
  if (loading) {
    return null;
  }
  
  // 檢查權限
  let hasAccess = true;
  
  if (permission) {
    hasAccess = hasPermission(permission);
  }
  
  if (anyPermissions && anyPermissions.length > 0) {
    hasAccess = hasAccess && hasAnyPermission(anyPermissions);
  }
  
  if (allPermissions && allPermissions.length > 0) {
    hasAccess = hasAccess && hasAllPermissions(allPermissions);
  }
  
  // 根據權限渲染菜單項
  if (hasAccess) {
    return <>{children}</>;
  }
  
  // 沒有權限，不渲染菜單項
  return null;
};

export default PermissionMenuItem;
