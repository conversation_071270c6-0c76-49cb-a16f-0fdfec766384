# EPD 權限管理系統架構

## 1. 整體架構

```mermaid
graph TD
    A[權限管理] --> B[角色管理]
    A --> C[人員管理]
    A --> D[權限分配]

    subgraph 主要功能模塊
        direction LR
        B --- C --- D
    end
```

## 2. 角色管理 (Role Management)

```mermaid
graph TD
    B[角色管理] --> B1[列表顯示]
    B --> B2[新增角色]
    B --> B3[編輯角色]
    B --> B4[刪除角色]
    B --> B5[分頁]
```

## 3. 人員管理 (User Management)

```mermaid
graph TD
    C[人員管理] --> C1[列表顯示]
    C --> C2[搜索]
    C --> C3[篩選狀態]
    C --> C4[批量添加]
    C --> C5[新增人員]
    C --> C6[編輯人員]
    C --> C7[刪除人員]
    C --> C8[批量刪除]
    C --> C9[重置密碼]
    C --> C10[分頁]
```

## 4. 權限分配 (Permission Assignment)

```mermaid
graph TD
    D[權限分配] --> D1[列表顯示]
    D --> D2[按角色類型篩選]
    D --> D3[按角色篩選]
    D --> D4[按門店篩選]
    D --> D5[搜索]
    D --> D6[批量設置]
    D --> D7[新增分配]
    D --> D8[編輯分配]
    D --> D9[刪除分配]
    D --> D10[批量刪除]
    D --> D11[分頁]
```

## 功能清單

*   [ ] **權限管理**
    *   [ ] **角色管理**
        *   [ ] 列表顯示 (角色名稱, 角色描述, 角色類型)
        *   [ ] 新增角色
        *   [ ] 編輯角色
        *   [ ] 刪除角色
        *   [ ] 分頁功能
    *   [ ] **人員管理**
        *   [ ] 列表顯示 (用戶名, 姓名, 手機, 郵箱, 狀態)
        *   [ ] 搜索功能
        *   [ ] 篩選 (按狀態)
        *   [ ] 批量添加用戶
        *   [ ] 新增用戶
        *   [ ] 編輯用戶
        *   [ ] 刪除用戶
        *   [ ] 批量刪除用戶
        *   [ ] 重置密碼 (?)
        *   [ ] 分頁功能
    *   [ ] **權限分配**
        *   [ ] 列表顯示 (用戶名, 姓名, 權限範圍, 角色)
        *   [ ] 篩選 (按角色類型)
        *   [ ] 篩選 (按角色)
        *   [ ] 篩選 (按門店)
        *   [ ] 搜索功能
        *   [ ] 批量設置權限
        *   [ ] 新增權限分配
        *   [ ] 編輯權限分配
        *   [ ] 刪除權限分配
        *   [ ] 批量刪除權限分配
        *   [ ] 分頁功能
