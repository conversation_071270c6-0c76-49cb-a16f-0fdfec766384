import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../../store/authStore';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '../../components/ui/table';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from '../../components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../../components/ui/select';
import { Label } from '../../components/ui/label';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '../../components/ui/alert-dialog';
import { Pencil, Trash2, Plus, Search, X, Key } from 'lucide-react';
import { Badge } from '../../components/ui/badge';
import { Pagination } from '../../components/ui/pagination';
import { getAllUsers, updateUser, deleteUser, resetUserPassword } from '../../utils/api/userApi';

// 用戶接口
interface User {
  _id: string;
  username: string;
  name: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 分頁接口
interface Pagination {
  page: number;
  limit: number;
  total: number;
}

export const UserManagement: React.FC = () => {
  const { t } = useTranslation();
  const { token, user: currentUser } = useAuthStore();

  // 狀態
  const [users, setUsers] = useState<User[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    total: 0
  });
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  // 對話框狀態
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] = useState(false);
  const [isPasswordResetSuccessDialogOpen, setIsPasswordResetSuccessDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [resetPasswordResult, setResetPasswordResult] = useState<string>('');

  // 表單狀態
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    name: '',
    email: '',
    phone: '',
    status: 'active'
  });

  // 獲取用戶列表
  const fetchUsers = async () => {
    try {
      setLoading(true);

      const params: {
        page: number;
        limit: number;
        status?: string;
        search?: string;
      } = {
        page: pagination.page,
        limit: pagination.limit
      };

      if (filterStatus !== 'all') {
        params.status = filterStatus;
      }

      if (searchTerm) {
        params.search = searchTerm;
      }

      const data = await getAllUsers(params);
      setUsers(data.users);
      setPagination(data.pagination);
    } catch (error) {
      console.error('獲取用戶列表錯誤:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchUsers();
  }, [token, pagination.page, pagination.limit, filterStatus]);

  // 處理搜索
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchUsers();
  };

  // 處理過濾
  const handleFilterChange = (value: string) => {
    setFilterStatus(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // 處理分頁
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  // 處理添加用戶
  const handleAddUser = async () => {
    try {
      setLoading(true);
      setError(null);

      // 驗證必填字段
      if (!formData.username || !formData.password) {
        setError(t('errors.required'));
        return;
      }

      console.log('正在創建用戶:', formData);

      // 直接使用 fetch 發送請求，以便獲取更詳細的錯誤信息
      const response = await fetch('http://localhost:3001/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData),
        credentials: 'include'
      });

      console.log('創建用戶響應狀態:', response.status);

      // 解析響應
      const responseData = await response.json().catch(e => {
        console.error('解析響應失敗:', e);
        return { error: '解析響應失敗' };
      });

      console.log('創建用戶響應數據:', responseData);

      if (!response.ok) {
        throw new Error(responseData.error || `創建用戶失敗: ${response.statusText}`);
      }

      console.log('創建用戶成功:', responseData);

      // 重新獲取用戶列表
      await fetchUsers();

      // 關閉對話框
      setIsAddDialogOpen(false);

      // 重置表單
      setFormData({
        username: '',
        password: '',
        name: '',
        email: '',
        phone: '',
        status: 'active'
      });
    } catch (error: any) {
      console.error('添加用戶錯誤:', error);
      setError(error.message || '創建用戶失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 處理編輯用戶
  const handleEditUser = async () => {
    if (!selectedUser) return;

    try {
      setLoading(true);
      setError(null);

      // 移除密碼字段
      const { password, ...updateData } = formData;

      // 驗證必填字段
      if (!updateData.username) {
        setError(t('errors.required'));
        return;
      }

      console.log('正在更新用戶:', updateData);
      await updateUser(selectedUser._id, updateData);
      console.log('更新用戶成功');

      // 重新獲取用戶列表
      await fetchUsers();

      // 關閉對話框
      setIsEditDialogOpen(false);

      // 重置當前用戶
      setSelectedUser(null);
    } catch (error: any) {
      console.error('編輯用戶錯誤:', error);
      setError(error.message || '更新用戶失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 處理刪除用戶
  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      setLoading(true);
      setError(null);

      console.log('正在刪除用戶:', selectedUser._id);
      await deleteUser(selectedUser._id);
      console.log('刪除用戶成功');

      // 重新獲取用戶列表
      await fetchUsers();

      // 關閉對話框
      setIsDeleteDialogOpen(false);

      // 重置當前用戶
      setSelectedUser(null);
    } catch (error: any) {
      console.error('刪除用戶錯誤:', error);
      setError(error.message || '刪除用戶失敗，請重試');

      // 即使出錯也關閉對話框
      setIsDeleteDialogOpen(false);
    } finally {
      setLoading(false);
    }
  };

  // 處理重設密碼
  const handleResetPassword = async () => {
    if (!selectedUser) return;

    try {
      setLoading(true);
      setError(null);

      console.log('正在重設密碼:', selectedUser._id);
      const data = await resetUserPassword(selectedUser._id);
      console.log('重設密碼成功');

      // 關閉重設密碼對話框
      setIsResetPasswordDialogOpen(false);

      // 設置重設密碼結果
      setResetPasswordResult(data.defaultPassword);

      // 打開密碼重設成功對話框
      setIsPasswordResetSuccessDialogOpen(true);
    } catch (error: any) {
      console.error('重設密碼錯誤:', error);
      setError(error.message || '重設密碼失敗，請重試');

      // 關閉重設密碼對話框
      setIsResetPasswordDialogOpen(false);
    } finally {
      setLoading(false);
    }
  };

  // 打開編輯對話框
  const openEditDialog = (user: User) => {
    setSelectedUser(user);
    setFormData({
      username: user.username,
      password: '',
      name: user.name,
      email: user.email,
      phone: user.phone,
      status: user.status
    });
    setIsEditDialogOpen(true);
  };

  // 打開刪除對話框
  const openDeleteDialog = (user: User) => {
    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  // 打開重設密碼對話框
  const openResetPasswordDialog = (user: User) => {
    setSelectedUser(user);
    setIsResetPasswordDialogOpen(true);
  };

  return (
    <div>
      {/* 工具欄 */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder={t('common.search')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              className="pl-8 w-64"
            />
            {searchTerm && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  handleSearch();
                }}
                className="absolute right-2 top-1/2 transform -translate-y-1/2"
              >
                <X className="h-4 w-4 text-gray-400" />
              </button>
            )}
          </div>

          <Button variant="outline" size="sm" onClick={handleSearch}>
            {t('common.search')}
          </Button>

          <Select value={filterStatus} onValueChange={handleFilterChange}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder={t('permission.status')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('permission.all')}</SelectItem>
              <SelectItem value="active">{t('permission.active')}</SelectItem>
              <SelectItem value="inactive">{t('permission.inactive')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button onClick={() => {
          setFormData({
            username: '',
            password: '',
            name: '',
            email: '',
            phone: '',
            status: 'active'
          });
          setIsAddDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          {t('permission.addUser')}
        </Button>
      </div>

      {/* 用戶表格 */}
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('permission.username')}</TableHead>
              <TableHead>{t('permission.name')}</TableHead>
              <TableHead>{t('permission.email')}</TableHead>
              <TableHead>{t('permission.phone')}</TableHead>
              <TableHead>{t('permission.status')}</TableHead>
              <TableHead className="w-32">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                  </div>
                  <p className="mt-2 text-sm text-gray-500">{t('common.loading')}</p>
                </TableCell>
              </TableRow>
            ) : users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <p className="text-gray-500">{t('common.noData')}</p>
                </TableCell>
              </TableRow>
            ) : (
              users.map(user => (
                <TableRow key={user._id}>
                  <TableCell className="font-medium">{user.username}</TableCell>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.phone}</TableCell>
                  <TableCell>
                    <Badge variant={user.status === 'active' ? 'success' : 'destructive'}>
                      {user.status === 'active' ? t('permission.active') : t('permission.inactive')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="icon" onClick={() => openEditDialog(user)}>
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => openResetPasswordDialog(user)}>
                        <Key className="h-4 w-4 text-yellow-500" />
                      </Button>
                      {/* 不允許刪除當前登入的用戶 */}
                      {currentUser?._id !== user._id && (
                        <Button variant="ghost" size="icon" onClick={() => openDeleteDialog(user)}>
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 分頁 */}
      {!loading && users.length > 0 && (
        <div className="flex justify-between items-center mt-4">
          <div className="text-sm text-gray-500">
            {t('common.showing')} {(pagination.page - 1) * pagination.limit + 1} {t('common.to')}{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} {t('common.of')}{' '}
            {pagination.total} {t('common.entries')}
          </div>

          <Pagination
            currentPage={pagination.page}
            totalPages={Math.ceil(pagination.total / pagination.limit)}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      {/* 添加用戶對話框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('permission.addUser')}</DialogTitle>
          </DialogHeader>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded mb-4">
              {error}
            </div>
          )}

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="username" className="text-right">
                {t('permission.username')}
              </Label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="password" className="text-right">
                {t('auth.password')}
              </Label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                {t('permission.name')}
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                {t('permission.email')}
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="phone" className="text-right">
                {t('permission.phone')}
              </Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                {t('permission.status')}
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData({ ...formData, status: value as 'active' | 'inactive' })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder={t('permission.status')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">{t('permission.active')}</SelectItem>
                  <SelectItem value="inactive">{t('permission.inactive')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">{t('common.cancel')}</Button>
            </DialogClose>
            <Button onClick={handleAddUser} disabled={loading}>
              {loading ? t('common.saving') : t('common.save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 編輯用戶對話框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('permission.editUser')}</DialogTitle>
          </DialogHeader>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded mb-4">
              {error}
            </div>
          )}

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-username" className="text-right">
                {t('permission.username')}
              </Label>
              <Input
                id="edit-username"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                {t('permission.name')}
              </Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-email" className="text-right">
                {t('permission.email')}
              </Label>
              <Input
                id="edit-email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-phone" className="text-right">
                {t('permission.phone')}
              </Label>
              <Input
                id="edit-phone"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-status" className="text-right">
                {t('permission.status')}
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData({ ...formData, status: value as 'active' | 'inactive' })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder={t('permission.status')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">{t('permission.active')}</SelectItem>
                  <SelectItem value="inactive">{t('permission.inactive')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">{t('common.cancel')}</Button>
            </DialogClose>
            <Button onClick={handleEditUser} disabled={loading}>
              {loading ? t('common.saving') : t('common.save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 刪除用戶確認對話框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('permission.confirmDelete')}</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedUser && t('permission.confirmDeleteUser', { name: selectedUser.username })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteUser} className="bg-red-500 hover:bg-red-600">
              {t('common.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 重設密碼確認對話框 */}
      <AlertDialog open={isResetPasswordDialogOpen} onOpenChange={setIsResetPasswordDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('permission.resetPassword')}</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedUser && t('permission.confirmResetPassword', { name: selectedUser.username })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleResetPassword}>
              {t('permission.resetPassword')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 密碼重設成功對話框 */}
      <Dialog open={isPasswordResetSuccessDialogOpen} onOpenChange={setIsPasswordResetSuccessDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('permission.resetPassword')}</DialogTitle>
          </DialogHeader>

          <div className="py-4">
            <p className="mb-2">{t('permission.passwordResetSuccess', { password: resetPasswordResult })}</p>
            <div className="flex items-center justify-between p-2 bg-gray-100 rounded">
              <code className="text-sm">{resetPasswordResult}</code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(resetPasswordResult);
                }}
              >
                {t('common.copy')}
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={() => setIsPasswordResetSuccessDialogOpen(false)}>
              {t('common.confirm')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
