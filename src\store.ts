import { create } from 'zustand';
import { Template } from './types';

interface TemplateStore {
  templates: Template[];
  selectedTemplate: Template | null;
  selectedTemplateIds: string[]; // 新增多選功能的狀態
  addTemplate: (template: Template) => void;
  updateTemplate: (template: Template) => void;
  deleteTemplate: (id: string) => void;
  setSelectedTemplate: (template: Template | null) => void;
  toggleTemplateSelection: (id: string) => void; // 切換單個模板的選中狀態
  selectAllTemplates: (selected: boolean, specificIds?: string[]) => void; // 全選/取消全選，支持指定特定ID
  deselectAllTemplates: () => void; // 取消所有選擇
  deleteSelectedTemplates: () => void; // 刪除所有選中的模板
}

export const useTemplateStore = create<TemplateStore>((set) => ({  templates: [],
  selectedTemplate: null,
  selectedTemplateIds: [], // 初始化為空數組
  addTemplate: (template) => 
    set((state) => ({ templates: [template, ...state.templates] })),
  updateTemplate: (template) =>
    set((state) => ({
      templates: state.templates.map((t) => 
        t.id === template.id ? template : t
      ),
    })),
  deleteTemplate: (id) =>
    set((state) => ({
      templates: state.templates.filter((t) => t.id !== id),
    })),
  setSelectedTemplate: (template) =>
    set({ selectedTemplate: template }),
  toggleTemplateSelection: (id) =>
    set((state) => {
      if (state.selectedTemplateIds.includes(id)) {
        return { 
          selectedTemplateIds: state.selectedTemplateIds.filter(templateId => templateId !== id) 
        };
      } else {
        return { 
          selectedTemplateIds: [...state.selectedTemplateIds, id] 
        };
      }
    }),
  selectAllTemplates: (selected, specificIds) =>
    set((state) => ({
      selectedTemplateIds: selected 
        ? (specificIds || state.templates.map(t => t.id)) // 如果提供了特定 ID，則使用它們
        : [] // 否則清空選擇
    })),
  deselectAllTemplates: () => 
    set({ selectedTemplateIds: [] }),
  deleteSelectedTemplates: () =>
    set((state) => ({
      templates: state.templates.filter(t => !state.selectedTemplateIds.includes(t.id)),
      selectedTemplateIds: []
    })),
}));