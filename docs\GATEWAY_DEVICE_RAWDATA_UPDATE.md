# Gateway-Device 文檔 rawdata 字段更新說明

## 更新概述

根據 EPD 格式轉換功能的實現，我們在 Gateway-Device 相關文檔中添加了缺少的 `rawdata` 字段，確保文檔與實際實現保持一致。

## 更新的文檔

### 1. Gateway-Device-Quick-Reference.md

**文件路徑**: `docs/plan/Gateway-Device-Quick-Reference.md`

**更新內容**:
- 在 `update_preview` 消息格式中添加 `rawdata` 字段
- 更新處理邏輯說明，包含 rawdata 的用途

**修改前**:
```json
{
  "type": "update_preview",
  "deviceMac": "11:22:33:44:55:66",
  "imageData": "data:image/png;base64,iVBORw0KGgo...",
  "imageCode": "87654321",
  "timestamp": "2021-12-31T16:00:00.000Z"
}
```

**修改後**:
```json
{
  "type": "update_preview",
  "deviceMac": "11:22:33:44:55:66",
  "imageData": "data:image/png;base64,iVBORw0KGgo...",
  "imageCode": "87654321",
  "rawdata": [255, 255, 0, 128, 64, ...],  // EPD 原始數據陣列 (Uint8Array)
  "timestamp": "2021-12-31T16:00:00.000Z"
}
```

### 2. Gateway-Device-Implementation-Guide.md

**文件路徑**: `docs/plan/Gateway-Device-Implementation-Guide.md`

**更新內容**:
1. **update_preview 消息格式**: 添加 rawdata 字段和詳細說明
2. **圖像處理器類**: 更新 `handle_image_update` 方法支援 rawdata 處理
3. **實作範例**: 更新 Gateway 實作範例中的圖像更新處理邏輯

**主要修改**:

#### 消息格式更新
```json
{
  "type": "update_preview",
  "deviceMac": "11:22:33:44:55:66",
  "imageData": "data:image/png;base64,iVBORw0KGgo...",
  "imageCode": "87654321",
  "rawdata": [255, 255, 0, 128, 64, ...],  // EPD 原始數據陣列 (Uint8Array)
  "timestamp": "2021-12-31T16:00:00.000Z"
}
```

#### 處理邏輯更新
- 添加 rawdata 字段說明
- 包含 ImageInfo 結構 (12 bytes) + 像素數據
- 可直接發送到 EPD 設備進行顯示，無需額外轉換

#### 圖像處理器更新
```python
async def handle_image_update(self, device_mac, image_data, image_code, rawdata=None):
    """處理圖像更新消息"""
    if rawdata:
        # 直接使用 EPD 格式數據
        await self.send_epd_data_to_device(device_mac, rawdata)
    else:
        # 需要轉換 image_data 為 EPD 格式
        epd_data = await self.convert_image_to_epd(device_mac, image_data)
        await self.send_epd_data_to_device(device_mac, epd_data)
```

## rawdata 字段詳細說明

### 數據格式
- **類型**: 數組 (Array) 或 Uint8Array
- **內容**: EPD 設備可直接使用的二進制數據
- **結構**: ImageInfo (12 bytes) + 像素數據

### ImageInfo 結構 (12 bytes)
```
- imagecode: uint32 (4 bytes) - 圖像校驗碼
- X: uint16 (2 bytes) - X 座標
- Y: uint16 (2 bytes) - Y 座標  
- Width: uint16 (2 bytes) - 寬度
- Height: uint16 (2 bytes) - 高度
```

### 像素數據格式
根據設備的 colorType 而定:
- **BW/GRAY16**: 4bit per pixel, 寬度需為2的倍數
- **BWR**: 2 tables, 1bit per pixel, 寬度需為8的倍數
- **BWRY**: 2bit per pixel, 寬度需為4的倍數

### 使用優勢
1. **性能優化**: Gateway 無需進行圖像轉換，直接發送到設備
2. **一致性**: 確保所有設備收到相同格式的數據
3. **簡化實作**: Gateway 實作更簡單，只需轉發數據
4. **向後兼容**: 保留 imageData 字段作為備用方案

## 實作建議

### Gateway 端處理
```python
async def handle_image_update(self, data):
    device_mac = data.get("deviceMac")
    image_code = data.get("imageCode")
    rawdata = data.get("rawdata")
    image_data = data.get("imageData")
    
    if rawdata:
        # 優先使用 EPD 原始數據
        await self.send_epd_data_to_device(device_mac, rawdata)
    else:
        # 備用方案：使用圖像數據
        await self.send_image_to_device(device_mac, image_data)
```

### 數據轉換
```python
# 將陣列轉換為 bytes
epd_bytes = bytes(rawdata) if isinstance(rawdata, list) else rawdata

# 發送到 EPD 設備
await bluetooth_manager.send_to_device(device_mac, epd_bytes)
```

## 向後兼容性

- 保留原有的 `imageData` 字段
- `rawdata` 字段為可選，舊版本 Gateway 可以忽略
- 新版本 Gateway 應優先使用 `rawdata`，如果不存在則使用 `imageData`

## 測試建議

1. **消息格式測試**: 確認 WebSocket 消息包含正確的 rawdata 字段
2. **數據完整性測試**: 驗證 rawdata 數據的完整性和正確性
3. **設備兼容性測試**: 測試不同 colorType 設備的顯示效果
4. **向後兼容性測試**: 確認舊版本 Gateway 仍能正常工作

## 相關文件

- [EPD 轉換技術規格](../plan/epd-conversion-technical-spec.md)
- [BWRY 顏色映射修正說明](./BWRY_COLOR_MAPPING_FIX.md)
- [WebSocket 服務實作規範](../plan/Deployment and Configuration/server-implementation-spec.md)

---

**更新日期**: 2024年12月  
**版本**: 1.0.0
