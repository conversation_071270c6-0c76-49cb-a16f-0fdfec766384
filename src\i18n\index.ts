import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 導入語言文件
import enTranslation from './locales/en.json';
import zhTWTranslation from './locales/zh-TW.json';
import jaTranslation from './locales/ja.json';

// i18next 初始化之前先清除之前的設定
const currentLang = localStorage.getItem('i18nextLng');
if (currentLang) {
  console.log('現有語言設定:', currentLang);
  // 不再清除語言設定，而是保留它
}

// 配置 i18next
i18n
  // 將 i18next 傳遞給 react-i18next (必須在 LanguageDetector 之前)
  .use(initReactI18next)
  // 檢測用戶瀏覽器語言
  .use(LanguageDetector)
  // 初始化 i18next
  .init({
    compatibilityJSON: 'v3',
    resources: {
      en: enTranslation,
      'zh-TW': zhTWTranslation,
      ja: jaTranslation
    },
    lng: currentLang || 'zh-TW', // 如果有已存儲的語言設定則使用，否則預設為中文
    fallbackLng: 'en', // 如果找不到用戶語言的翻譯，則使用英文
    interpolation: {
      escapeValue: false, // 不要轉義 HTML 實體
    },
    debug: true, // 開啟調試模式以查看更多日誌
    react: {
      useSuspense: false, // 在非 Suspense 環境中也能使用 i18n
      wait: true // 等待翻譯載入完成
    },
    detection: {
      order: ['localStorage', 'navigator'], // 優先從 localStorage 讀取設定
      lookupLocalStorage: 'i18nextLng', // localStorage 的鍵名
      caches: ['localStorage']
    }
  });

// 測試日文翻譯檔案是否正確
console.log('日文翻譯檔案範例:', jaTranslation.translation.common.add);
console.log('中文翻譯檔案範例:', zhTWTranslation.translation.common.add);
console.log('英文翻譯檔案範例:', enTranslation.translation.common.add);

// 在控制台輸出當前語言
console.log('i18n 初始化完成 - 當前語言:', i18n.language);
console.log('所有可用語言:', Object.keys({
  en: enTranslation,
  'zh-TW': zhTWTranslation,
  ja: jaTranslation
}));

export default i18n;
