/**
 * 批量發送智能網關選擇功能測試腳本
 * 
 * 測試場景：
 * 1. 批量發送時智能網關選擇的觸發
 * 2. 並發處理和統計信息收集
 * 3. 混合模式設備的處理（auto和manual）
 */

const sendPreviewToGateway = require('../services/sendPreviewToGateway');

// 模擬測試數據
const mockDevices = [
  {
    _id: 'device1',
    macAddress: 'AA:BB:CC:DD:EE:01',
    primaryGatewayId: 'gateway1',
    otherGateways: ['gateway2', 'gateway3'],
    gatewaySelectionMode: 'auto',
    imageUpdateStatus: '未更新'
  },
  {
    _id: 'device2',
    macAddress: 'AA:BB:CC:DD:EE:02',
    primaryGatewayId: 'gateway1',
    otherGateways: ['gateway2'],
    gatewaySelectionMode: 'auto',
    imageUpdateStatus: '未更新'
  },
  {
    _id: 'device3',
    macAddress: 'AA:BB:CC:DD:EE:03',
    primaryGatewayId: 'gateway2',
    otherGateways: ['gateway1', 'gateway3'],
    gatewaySelectionMode: 'manual',
    imageUpdateStatus: '未更新'
  },
  {
    _id: 'device4',
    macAddress: 'AA:BB:CC:DD:EE:04',
    primaryGatewayId: 'gateway3',
    otherGateways: ['gateway1', 'gateway2'],
    gatewaySelectionMode: 'auto',
    imageUpdateStatus: '未更新'
  }
];

// 模擬 sendDevicePreviewToGateway 函數
const mockSendDevicePreviewToGateway = async (deviceId, options = {}) => {
  const device = mockDevices.find(d => d._id === deviceId);
  if (!device) {
    throw new Error(`設備 ${deviceId} 不存在`);
  }

  // 模擬處理時間
  const processingTime = Math.random() * 1000 + 500; // 500-1500ms
  await new Promise(resolve => setTimeout(resolve, processingTime));

  // 模擬智能選擇邏輯
  const isAutoMode = device.gatewaySelectionMode === 'auto';
  const primaryGatewayBusy = Math.random() < 0.3; // 30% 機率主要網關忙碌
  const usedBackupGateway = isAutoMode && primaryGatewayBusy && device.otherGateways.length > 0;
  const selectedGateway = usedBackupGateway ? device.otherGateways[0] : device.primaryGatewayId;

  // 模擬成功率（95%）
  const success = Math.random() < 0.95;

  if (!success) {
    return {
      success: false,
      deviceId,
      error: '模擬傳輸失敗',
      timestamp: new Date().toISOString()
    };
  }

  return {
    success: true,
    deviceId,
    deviceMac: device.macAddress,
    primaryGateway: {
      gatewayId: device.primaryGatewayId,
      gatewayName: `網關-${device.primaryGatewayId}`,
      success: true,
      message: '發送成功'
    },
    actualUsedGateway: {
      gatewayId: selectedGateway,
      gatewayName: `網關-${selectedGateway}`,
      isUsingBackupGateway: usedBackupGateway,
      backupGatewayReason: usedBackupGateway ? '主要網關正在進行chunk傳輸' : '',
      backupGatewayFailed: false
    },
    smartGatewaySelection: {
      enabled: isAutoMode,
      primaryGatewayBusy: primaryGatewayBusy,
      usedBackupGateway: usedBackupGateway
    },
    otherGateways: [],
    timestamp: new Date().toISOString()
  };
};

// 測試批量發送功能
async function testBatchSmartGatewaySelection() {
  console.log('🧪 開始批量發送智能網關選擇功能測試\n');

  // 測試場景1: 小批量發送（並發數 = 2）
  console.log('📋 測試場景1: 小批量發送（並發數 = 2）');
  
  const deviceIds1 = ['device1', 'device2'];
  console.log(`設備列表: ${deviceIds1.join(', ')}`);
  
  // 模擬批量發送邏輯
  const startTime1 = Date.now();
  const results1 = [];
  const smartStats1 = { totalAutoModeDevices: 0, usedBackupGateway: 0, primaryGatewayBusy: 0 };
  
  // 分批處理（並發數 = 2）
  const concurrency1 = 2;
  const batches1 = [];
  for (let i = 0; i < deviceIds1.length; i += concurrency1) {
    batches1.push(deviceIds1.slice(i, i + concurrency1));
  }
  
  for (const batch of batches1) {
    const batchPromises = batch.map(async (deviceId) => {
      const result = await mockSendDevicePreviewToGateway(deviceId);
      
      // 收集統計信息
      if (result.smartGatewaySelection) {
        if (result.smartGatewaySelection.enabled) smartStats1.totalAutoModeDevices++;
        if (result.smartGatewaySelection.primaryGatewayBusy) smartStats1.primaryGatewayBusy++;
        if (result.smartGatewaySelection.usedBackupGateway) smartStats1.usedBackupGateway++;
      }
      
      return result;
    });
    
    const batchResults = await Promise.all(batchPromises);
    results1.push(...batchResults);
  }
  
  const duration1 = Date.now() - startTime1;
  
  console.log(`✅ 場景1完成 (${duration1}ms):`);
  console.log(`   成功: ${results1.filter(r => r.success).length}/${results1.length}`);
  console.log(`   智能模式設備: ${smartStats1.totalAutoModeDevices}`);
  console.log(`   使用備用網關: ${smartStats1.usedBackupGateway}`);
  console.log(`   主要網關忙碌: ${smartStats1.primaryGatewayBusy}`);

  // 測試場景2: 大批量發送（並發數 = 3）
  console.log('\n📋 測試場景2: 大批量發送（並發數 = 3）');
  
  const deviceIds2 = ['device1', 'device2', 'device3', 'device4'];
  console.log(`設備列表: ${deviceIds2.join(', ')}`);
  
  const startTime2 = Date.now();
  const results2 = [];
  const smartStats2 = { totalAutoModeDevices: 0, usedBackupGateway: 0, primaryGatewayBusy: 0 };
  
  // 分批處理（並發數 = 3）
  const concurrency2 = 3;
  const batches2 = [];
  for (let i = 0; i < deviceIds2.length; i += concurrency2) {
    batches2.push(deviceIds2.slice(i, i + concurrency2));
  }
  
  console.log(`分為 ${batches2.length} 個批次: ${batches2.map(b => `[${b.join(',')}]`).join(', ')}`);
  
  for (let batchIndex = 0; batchIndex < batches2.length; batchIndex++) {
    const batch = batches2[batchIndex];
    console.log(`處理批次 ${batchIndex + 1}: ${batch.join(', ')}`);
    
    const batchPromises = batch.map(async (deviceId) => {
      const result = await mockSendDevicePreviewToGateway(deviceId);
      
      // 收集統計信息
      if (result.smartGatewaySelection) {
        if (result.smartGatewaySelection.enabled) smartStats2.totalAutoModeDevices++;
        if (result.smartGatewaySelection.primaryGatewayBusy) smartStats2.primaryGatewayBusy++;
        if (result.smartGatewaySelection.usedBackupGateway) smartStats2.usedBackupGateway++;
      }
      
      console.log(`  ${deviceId}: ${result.success ? '成功' : '失敗'}${result.actualUsedGateway?.isUsingBackupGateway ? ' [備用網關]' : ''}`);
      return result;
    });
    
    const batchResults = await Promise.all(batchPromises);
    results2.push(...batchResults);
    
    // 批次間延遲
    if (batchIndex < batches2.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  const duration2 = Date.now() - startTime2;
  
  console.log(`✅ 場景2完成 (${duration2}ms):`);
  console.log(`   成功: ${results2.filter(r => r.success).length}/${results2.length}`);
  console.log(`   智能模式設備: ${smartStats2.totalAutoModeDevices}`);
  console.log(`   使用備用網關: ${smartStats2.usedBackupGateway}`);
  console.log(`   主要網關忙碌: ${smartStats2.primaryGatewayBusy}`);

  // 測試場景3: 混合模式設備分析
  console.log('\n📋 測試場景3: 混合模式設備分析');
  
  const autoModeDevices = mockDevices.filter(d => d.gatewaySelectionMode === 'auto');
  const manualModeDevices = mockDevices.filter(d => d.gatewaySelectionMode === 'manual');
  
  console.log(`自動模式設備: ${autoModeDevices.map(d => d._id).join(', ')}`);
  console.log(`手動模式設備: ${manualModeDevices.map(d => d._id).join(', ')}`);
  
  // 分析結果
  const autoResults = results2.filter(r => {
    const device = mockDevices.find(d => d._id === r.deviceId);
    return device && device.gatewaySelectionMode === 'auto';
  });
  
  const manualResults = results2.filter(r => {
    const device = mockDevices.find(d => d._id === r.deviceId);
    return device && device.gatewaySelectionMode === 'manual';
  });
  
  console.log(`自動模式結果: ${autoResults.length} 個設備`);
  autoResults.forEach(r => {
    console.log(`  ${r.deviceId}: 智能選擇=${r.smartGatewaySelection?.enabled}, 使用備用=${r.actualUsedGateway?.isUsingBackupGateway}`);
  });
  
  console.log(`手動模式結果: ${manualResults.length} 個設備`);
  manualResults.forEach(r => {
    console.log(`  ${r.deviceId}: 智能選擇=${r.smartGatewaySelection?.enabled}, 使用備用=${r.actualUsedGateway?.isUsingBackupGateway}`);
  });

  console.log('\n✅ 批量發送智能網關選擇功能測試完成');
}

// 主測試函數
async function runBatchTests() {
  try {
    console.log('🚀 開始批量發送智能網關選擇測試套件\n');
    
    await testBatchSmartGatewaySelection();
    
    console.log('\n🎉 所有批量測試完成！');
    
    console.log('\n📖 批量發送智能選擇特性:');
    console.log('1. 支援並發處理，提高批量發送效率');
    console.log('2. 自動收集智能選擇統計信息');
    console.log('3. 只有 gatewaySelectionMode="auto" 的設備才會觸發智能選擇');
    console.log('4. 批次間有適當延遲，避免過度負載網關');
    console.log('5. 詳細的處理時間和性能統計');
    
  } catch (error) {
    console.error('❌ 批量測試過程中發生錯誤:', error);
  }
}

// 如果直接運行此腳本
if (require.main === module) {
  runBatchTests();
}

module.exports = {
  testBatchSmartGatewaySelection,
  runBatchTests
};
