import React from 'react';
import { FormField, TextInput, NumberInput, SelectInput, RestrictedColorInput, fontOptions } from './FormComponents';
import { DisplayColorType } from '../../../types';

interface DefaultPropertiesProps {
  elementType: string;
  properties: {
    text: string;
    fontSize: number;
    fontFamily: string;
    lineWidth: number;
    lineColor: string;
  };
  setProperties: (properties: {
    text: string;
    fontSize: number;
    fontFamily: string;
    lineWidth: number;
    lineColor: string;
  }) => void;
  colorType?: string | DisplayColorType; // 新增：模板的顏色類型
}

export const DefaultProperties: React.FC<DefaultPropertiesProps> = ({
  elementType,
  properties,
  setProperties,
  colorType
}) => {
  const updateProperty = <K extends keyof typeof properties>(
    key: K,
    value: typeof properties[K]
  ) => {
    setProperties({
      ...properties,
      [key]: value
    });
  };

  const isTextElement = elementType === 'text' || elementType === 'multiline-text';
  const isLineElement = elementType === 'line';

  return (
    <div className="mt-6 pt-4 border-t border-gray-700">
      <h4 className="text-xs font-medium mb-2">預設屬性設置</h4>
      <div className="space-y-3">
        {isTextElement && (
          <>
            <FormField label="預設文字">
              <TextInput
                value={properties.text}
                onChange={(value) => updateProperty('text', value)}
              />
            </FormField>
            <FormField label="預設字體大小">
              <NumberInput
                value={properties.fontSize}
                onChange={(value) => updateProperty('fontSize', value)}
              />
            </FormField>
            <FormField label="預設字體">
              <SelectInput
                value={properties.fontFamily}
                onChange={(value) => updateProperty('fontFamily', value)}
                options={fontOptions}
              />
            </FormField>
          </>
        )}

        {isLineElement && (
          <>
            <FormField label="預設線條寬度">
              <NumberInput
                value={properties.lineWidth}
                onChange={(value) => updateProperty('lineWidth', value)}
                min={1}
                max={10}
              />
            </FormField>
            <FormField label="預設線條顏色">
              <RestrictedColorInput
                value={properties.lineColor}
                onChange={(value) => updateProperty('lineColor', value)}
                colorType={colorType}
              />
            </FormField>
          </>
        )}
      </div>
    </div>
  );
};