{"name": "epd-manager-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest", "lint": "eslint ."}, "dependencies": {"@react-native-async-storage/async-storage": "^1.17.11", "@react-navigation/bottom-tabs": "^6.5.7", "@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.12", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.4.0", "expo": "~48.0.15", "expo-status-bar": "~1.4.4", "react": "18.2.0", "react-native": "0.71.8", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.9.0", "react-native-reanimated": "~2.14.4", "react-native-safe-area-context": "4.5.0", "react-native-screens": "~3.20.0", "react-native-vector-icons": "^9.2.0", "react-redux": "^8.0.5", "redux": "^4.2.1", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/jest-native": "^5.4.2", "@testing-library/react-native": "^12.1.2", "@types/react": "~18.0.14", "@types/react-native": "^0.71.6", "babel-jest": "^29.5.0", "eslint": "^8.40.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-native": "^4.0.0", "jest": "^29.5.0", "jest-expo": "^48.0.2", "react-test-renderer": "^18.2.0", "typescript": "^4.9.4"}, "private": true, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"], "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"]}}