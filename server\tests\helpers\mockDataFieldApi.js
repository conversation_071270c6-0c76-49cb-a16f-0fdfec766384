// tests/helpers/mockDataFieldApi.js
/**
 * 資料欄位 API 的模擬路由
 */
const express = require('express');

// 創建模擬路由
function createMockDataFieldApi(mockCollection) {
  const router = express.Router();
  
  // 初始化資料庫連接函數
  router.initDB = jest.fn();

  // 獲取所有資料欄位
  router.get('/dataFields', async (req, res) => {
    try {
      const dataFields = await mockCollection.find().toArray();
      res.json(dataFields);
    } catch (error) {
      res.status(500).json({ error: '獲取資料欄位失敗' });
    }
  });
  
  // 根據區塊類型獲取資料欄位
  router.get('/dataFields/section/:section', async (req, res) => {
    try {
      const { section } = req.params;
      const filteredFields = await mockCollection.find({ section }).toArray();
      res.json(filteredFields);
    } catch (error) {
      res.status(500).json({ error: `獲取 ${req.params.section} 區塊資料欄位失敗` });
    }
  });
  
  // 創建新資料欄位
  router.post('/dataFields', async (req, res) => {
    try {
      const result = await mockCollection.insertOne(req.body);
      res.status(201).json({ id: result.insertedId });
    } catch (error) {
      res.status(500).json({ error: '創建資料欄位失敗' });
    }
  });
  
  // 更新資料欄位
  router.put('/dataFields/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const result = await mockCollection.updateOne({ _id: id }, { $set: req.body });
      
      if (result.modifiedCount === 0) {
        return res.status(404).json({ error: '找不到資料欄位' });
      }
      
      res.json({ message: '資料欄位更新成功' });
    } catch (error) {
      res.status(500).json({ error: '更新資料欄位失敗' });
    }
  });
  
  // 刪除資料欄位
  router.delete('/dataFields/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const result = await mockCollection.deleteOne({ _id: id });
      
      if (result.deletedCount === 0) {
        return res.status(404).json({ error: '找不到資料欄位或刪除失敗' });
      }
      
      res.json({ message: '資料欄位刪除成功' });
    } catch (error) {
      res.status(500).json({ error: '刪除資料欄位失敗' });
    }
  });
  
  return router;
}

module.exports = createMockDataFieldApi;
