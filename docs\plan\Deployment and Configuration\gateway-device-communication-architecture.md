# 網關與設備通信架構規劃

## 1. 系統架構概述

根據需求，我們需要建立一個完整的通信架構，包含三個主要部分：

1. **Server (遠端控制頁面)**：
   - 提供網關管理功能
   - 實現WebSocket服務，接收並處理來自網關的數據
   - 提供API接口供App註冊網關

2. **App (配網工具)**：
   - 登入遠端Server
   - 從Server獲取已註冊的網關資料
   - 通過ARP獲取本地網關資料
   - 提供網關註冊功能
   - 通過安全API將網關註冊到Server
   - 發送WebSocket連線訊息給網關

3. **Gateway (網關設備)**：
   - 支持掃描模式和工作模式
   - 接收App的WebSocket訊息後切換到工作模式
   - 在工作模式下固定回傳數據給系統

## 2. 技術選擇

### 2.1 Server端

- **WebSocket服務**：使用`ws`或`socket.io`庫實現WebSocket服務
- **API服務**：繼續使用現有的Express框架
- **數據存儲**：繼續使用MongoDB存儲網關和設備信息
- **認證機制**：JWT認證 + WebSocket認證

### 2.2 App端

- **框架**：React Native (跨平台移動應用)
- **網絡通信**：
  - HTTP/HTTPS：用於與Server的API通信
  - WebSocket：用於與網關通信
  - ARP掃描：使用原生模塊實現本地網絡掃描
- **狀態管理**：Redux或MobX
- **UI組件**：React Native Paper或NativeBase

### 2.3 Gateway端

- **通信協議**：WebSocket
- **數據格式**：JSON
- **安全機制**：TLS加密 + 認證Token
- **固件更新**：OTA (Over-The-Air) 更新機制

### 2.4 正確的流程現在為：

1. **網關註冊流程**：
   - 網關通過API註冊
   - 系統生成JWT令牌和WebSocket連接信息
   - 網關使用這些信息連接到WebSocket服務

2. **設備創建與初始化流程**：
   - 方法一：通過API創建設備（標記為未初始化狀態）
   - 方法二：設備被網關發現並通過WebSocket自動創建（標記為已初始化狀態）
   - 當網關通過WebSocket報告未初始化的設備時，該設備會被標記為已初始化

3. **設備與用戶關聯流程**：
   - 用戶可以通過API手動綁定設備
   - 通過API創建的設備自動關聯到創建它的用戶

## 3. 詳細設計

### 3.1 Server端實現

#### 3.1.1 WebSocket服務

```javascript
// server/websocket/index.js
const WebSocket = require('ws');
const http = require('http');
const url = require('url');
const { verifyToken } = require('../utils/auth');

// 創建WebSocket服務器
function setupWebSocketServer(server) {
  // 使用路徑匹配的WebSocket服務器
  const wss = new WebSocket.Server({
    noServer: true // 不自動附加到HTTP服務器
  });

  // 存儲連接的網關
  const connectedGateways = new Map();

  // 處理HTTP服務器的升級請求
  server.on('upgrade', (request, socket, head) => {
    const pathname = url.parse(request.url).pathname;

    // 檢查路徑是否匹配網關WebSocket路徑模式 (包含門店ID)
    const gatewayPathMatch = pathname.match(/^\/ws\/store\/([a-zA-Z0-9]+)\/gateway\/([a-zA-Z0-9]+)$/);

    if (gatewayPathMatch) {
      // 從路徑中提取門店ID和網關ID
      const pathStoreId = gatewayPathMatch[1];
      const pathGatewayId = gatewayPathMatch[2];

      // 解析URL中的token參數
      const parsedUrl = new URL(request.url, `http://${request.headers.host}`);
      const token = parsedUrl.searchParams.get('token');

      // 驗證token
      try {
        const decoded = verifyToken(token);
        const tokenGatewayId = decoded.gatewayId;

        // 從token中獲取門店ID
        const tokenStoreId = decoded.storeId;

        // 確保token中的網關ID與路徑中的網關ID匹配
        if (pathGatewayId !== tokenGatewayId) {
          console.error(`網關ID不匹配: 路徑=${pathGatewayId}, Token=${tokenGatewayId}`);
          socket.destroy();
          return;
        }

        // 確保token中的門店ID與路徑中的門店ID匹配
        if (pathStoreId !== tokenStoreId) {
          console.error(`門店ID不匹配: 路徑=${pathStoreId}, Token=${tokenStoreId}`);
          socket.destroy();
          return;
        }

        // 升級連接為WebSocket
        wss.handleUpgrade(request, socket, head, (ws) => {
          wss.emit('connection', ws, request, decoded);
        });
      } catch (error) {
        console.error('WebSocket認證失敗:', error);
        socket.destroy();
      }
    } else {
      // 不是網關WebSocket路徑，拒絕連接
      socket.destroy();
    }
  });

  // 處理WebSocket連接
  wss.on('connection', (ws, request, decoded) => {
    const gatewayId = decoded.gatewayId;

    // 存儲網關連接
    connectedGateways.set(gatewayId, ws);

    console.log(`網關 ${gatewayId} 已連接`);

    // 更新網關狀態為在線
    updateGatewayStatus(gatewayId, 'online');

    // 處理網關發送的消息
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        handleGatewayMessage(gatewayId, data);
      } catch (error) {
        console.error('處理網關消息錯誤:', error);
      }
    });

    // 處理連接關閉
    ws.on('close', () => {
      console.log(`網關 ${gatewayId} 已斷開連接`);
      connectedGateways.delete(gatewayId);
      updateGatewayStatus(gatewayId, 'offline');
    });

    // 處理錯誤
    ws.on('error', (error) => {
      console.error(`網關 ${gatewayId} 連接錯誤:`, error);
    });

    // 發送歡迎消息
    ws.send(JSON.stringify({
      type: 'welcome',
      message: `Welcome Gateway ${gatewayId}`,
      timestamp: new Date().toISOString()
    }));
  });

  // 返回WebSocket服務器實例和連接的網關Map
  return { wss, connectedGateways };
}

// 更新網關狀態
async function updateGatewayStatus(gatewayId, status) {
  try {
    const { client, db } = await require('../db')();
    const collection = db.collection('gateways');

    await collection.updateOne(
      { _id: new require('mongodb').ObjectId(gatewayId) },
      {
        $set: {
          status: status,
          lastSeen: status === 'online' ? new Date() : undefined,
          updatedAt: new Date()
        }
      }
    );
  } catch (error) {
    console.error(`更新網關 ${gatewayId} 狀態失敗:`, error);
  }
}

// 處理來自網關的消息
function handleGatewayMessage(gatewayId, data) {
  // 根據消息類型處理不同的邏輯
  switch (data.type) {
    case 'deviceStatus':
      updateDeviceStatus(gatewayId, data.devices);
      break;
    case 'gatewayInfo':
      updateGatewayInfo(gatewayId, data.info);
      break;
    case 'ping':
      // 處理心跳消息
      respondToPing(gatewayId, data);
      break;
    default:
      console.log(`收到來自網關 ${gatewayId} 的未知類型消息:`, data);
  }
}

// 處理心跳消息
function respondToPing(gatewayId, data) {
  const gateway = connectedGateways.get(gatewayId);
  if (gateway && gateway.readyState === WebSocket.OPEN) {
    gateway.send(JSON.stringify({
      type: 'pong',
      timestamp: new Date().toISOString(),
      echo: data.timestamp
    }));
  }
}

// 向特定網關發送消息
function sendMessageToGateway(gatewayId, message) {
  const gateway = connectedGateways.get(gatewayId);
  if (gateway && gateway.readyState === WebSocket.OPEN) {
    gateway.send(JSON.stringify(message));
    return true;
  }
  return false;
}

// 向所有連接的網關廣播消息
function broadcastMessage(message, filter = null) {
  let count = 0;
  connectedGateways.forEach((gateway, gatewayId) => {
    if (gateway.readyState === WebSocket.OPEN) {
      if (!filter || filter(gatewayId)) {
        gateway.send(JSON.stringify(message));
        count++;
      }
    }
  });
  return count;
}

module.exports = {
  setupWebSocketServer,
  sendMessageToGateway,
  broadcastMessage
};
```

#### 3.1.2 使用現有的網關註冊API

系統已經有現成的網關註冊API，我們將直接使用這些API而不是重新開發。以下是從 `scripts\generate-gateway-device-data.js` 中的相關代碼，展示了如何創建網關和設備：

```javascript
// 創建網關 (從 generate-gateway-device-data.js)
async function createGateway(gatewayData) {
  console.log(`Creating gateway: ${gatewayData.name}`);
  try {
    // 檢查網關是否已存在
    try {
      const gateways = await makeRequest(`gateways?storeId=${gatewayData.storeId}`);
      const existingGateway = gateways.find(g => g.macAddress === gatewayData.macAddress);
      if (existingGateway) {
        console.log(`Gateway with MAC ${gatewayData.macAddress} already exists, skipping...`);
        return existingGateway;
      }
    } catch (err) {
      console.log('Error checking existing gateways, will try to create new one');
    }

    // 創建新網關
    const gateway = await makeRequest('gateways', 'POST', gatewayData);
    console.log(`Gateway created: ${gateway.name} (${gateway._id})`);
    return gateway;
  } catch (error) {
    console.error(`Failed to create gateway ${gatewayData.name}:`, error.message);
    return null;
  }
}

// 創建設備 (從 generate-gateway-device-data.js)
async function createDevice(deviceData) {
  console.log(`Creating device with MAC: ${deviceData.macAddress} for store: ${deviceData.storeId}`);
  try {
    // 驗證必要欄位
    if (!deviceData.storeId) {
      throw new Error('門店ID不能為空');
    }

    // 檢查設備是否已存在
    try {
      const devices = await makeRequest(`devices?storeId=${deviceData.storeId}`);
      const existingDevice = devices.find(d => d.macAddress === deviceData.macAddress);
      if (existingDevice) {
        console.log(`Device with MAC ${deviceData.macAddress} already exists in store ${deviceData.storeId}, skipping...`);
        return existingDevice;
      }
    } catch (err) {
      console.log('Error checking existing devices, will try to create new one');
    }

    // 創建新設備
    const device = await makeRequest('devices', 'POST', deviceData);
    console.log(`Device created with MAC: ${device.macAddress} (${device._id}) for store: ${device.storeId}`);
    return device;
  } catch (error) {
    console.error(`Failed to create device with MAC ${deviceData.macAddress}:`, error.message);
    return null;
  }
}
```

App端將直接調用這些現有的API來註冊網關和設備。唯一需要添加的是生成網關認證Token的功能，這將在WebSocket服務中實現。

### 3.2 App端實現

#### 3.2.1 網關掃描與註冊

```javascript
// App端掃描與註冊網關的核心邏輯
import { NetworkInfo } from 'react-native-network-info';
import { arpScan } from 'react-native-arp-scanner';
import { api } from '../services/api';

// 獲取本地網絡信息
export async function getLocalNetworkInfo() {
  try {
    const ipAddress = await NetworkInfo.getIPAddress();
    const subnet = await NetworkInfo.getSubnet();
    return { ipAddress, subnet };
  } catch (error) {
    console.error('獲取網絡信息失敗:', error);
    throw error;
  }
}

// 掃描本地網絡中的設備
export async function scanLocalDevices() {
  try {
    const { ipAddress, subnet } = await getLocalNetworkInfo();
    const devices = await arpScan();
    return devices;
  } catch (error) {
    console.error('掃描本地設備失敗:', error);
    throw error;
  }
}

// 獲取已註冊的網關
export async function getRegisteredGateways() {
  try {
    const response = await api.get('/gateways');
    return response.data;
  } catch (error) {
    console.error('獲取已註冊網關失敗:', error);
    throw error;
  }
}

// 註冊新網關
export async function registerGateway(gatewayInfo) {
  try {
    const response = await api.post('/gateways/register', gatewayInfo);
    return response.data;
  } catch (error) {
    console.error('註冊網關失敗:', error);
    throw error;
  }
}

// 發送WebSocket連接命令給網關
export async function sendConnectCommand(gatewayIp, serverUrl, token) {
  try {
    // 使用HTTP請求通知網關連接到WebSocket服務器
    const response = await fetch(`http://${gatewayIp}/api/connect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        serverUrl,
        token,
      }),
    });

    if (!response.ok) {
      throw new Error(`網關返回錯誤: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('發送連接命令失敗:', error);
    throw error;
  }
}
```

### 3.3 Gateway端實現

網關端的實現通常是嵌入式系統，以下是概念性的偽代碼：

```javascript
// Gateway端的WebSocket客戶端實現 (偽代碼)
const WebSocket = require('ws');
let wsClient = null;
let serverUrl = '';
let token = '';
let workMode = 'scan'; // 'scan' or 'work'

// 初始化網關
function initGateway() {
  // 啟動HTTP服務器接收配置
  startHttpServer();

  // 默認進入掃描模式
  enterScanMode();
}

// 啟動HTTP服務器
function startHttpServer() {
  const http = require('http');
  const server = http.createServer((req, res) => {
    if (req.url === '/api/connect' && req.method === 'POST') {
      let body = '';

      req.on('data', chunk => {
        body += chunk.toString();
      });

      req.on('end', () => {
        try {
          const data = JSON.parse(body);
          serverUrl = data.serverUrl;
          token = data.token;

          // 切換到工作模式
          enterWorkMode();

          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: true }));
        } catch (error) {
          res.writeHead(400, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: error.message }));
        }
      });
    } else {
      res.writeHead(404);
      res.end();
    }
  });

  server.listen(8080, () => {
    console.log('HTTP服務器運行在端口8080');
  });
}

// 進入掃描模式
function enterScanMode() {
  workMode = 'scan';
  console.log('進入掃描模式');

  // 關閉現有WebSocket連接
  if (wsClient) {
    wsClient.close();
    wsClient = null;
  }
}

// 進入工作模式
function enterWorkMode() {
  workMode = 'work';
  console.log('進入工作模式');

  // 連接到WebSocket服務器
  connectToServer();
}

// 連接到WebSocket服務器
function connectToServer() {
  if (!serverUrl || !token) {
    console.error('缺少服務器URL或認證Token');
    return;
  }

  const wsUrl = `${serverUrl}?token=${token}`;
  wsClient = new WebSocket(wsUrl);

  wsClient.on('open', () => {
    console.log('已連接到WebSocket服務器');

    // 發送網關信息
    sendGatewayInfo();

    // 開始定期發送設備狀態
    startDeviceStatusReporting();
  });

  wsClient.on('message', (data) => {
    try {
      const message = JSON.parse(data);
      handleServerMessage(message);
    } catch (error) {
      console.error('處理服務器消息錯誤:', error);
    }
  });

  wsClient.on('close', () => {
    console.log('WebSocket連接已關閉');

    // 嘗試重新連接
    setTimeout(connectToServer, 5000);
  });

  wsClient.on('error', (error) => {
    console.error('WebSocket錯誤:', error);
  });
}

// 發送網關信息
function sendGatewayInfo() {
  if (!wsClient) return;

  const gatewayInfo = {
    type: 'gatewayInfo',
    info: {
      macAddress: getMacAddress(),
      ipAddress: getIpAddress(),
      model: getModel(),
      wifiFirmwareVersion: getWifiFirmwareVersion(),
      btFirmwareVersion: getBtFirmwareVersion()
    }
  };

  wsClient.send(JSON.stringify(gatewayInfo));
}

// 開始定期發送設備狀態
function startDeviceStatusReporting() {
  setInterval(() => {
    if (workMode === 'work' && wsClient) {
      const devices = scanConnectedDevices();

      const message = {
        type: 'deviceStatus',
        devices: devices
      };

      wsClient.send(JSON.stringify(message));
    }
  }, 10000); // 每10秒發送一次
}

// 處理來自服務器的消息
function handleServerMessage(message) {
  switch (message.type) {
    case 'restart':
      restartGateway();
      break;
    case 'updateFirmware':
      updateFirmware(message.firmwareType, message.version, message.url);
      break;
    default:
      console.log('收到未知類型的消息:', message);
  }
}

// 啟動網關
initGateway();
```

#### 3.3.1 網關工作模式與掃描模式的Python模擬實現

為了更好地理解網關的工作模式和掃描模式，以下提供了使用Python的詳細模擬實現：

```python
# gateway_simulator.py
import asyncio
import json
import logging
import socket
import time
import uuid
from enum import Enum
from http.server import HTTPServer, BaseHTTPRequestHandler
import websockets
import threading
import random

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("GatewaySimulator")

# 網關模式枚舉
class GatewayMode(Enum):
    SCAN = "scan"  # 掃描模式
    WORK = "work"  # 工作模式

# 網關狀態
class GatewayState:
    def __init__(self):
        self.mode = GatewayMode.SCAN  # 默認為掃描模式
        self.ws_url = None            # WebSocket URL
        self.ws_token = None          # WebSocket 認證令牌
        self.ws_connection = None     # WebSocket 連接
        self.mac_address = f"AA:BB:CC:DD:EE:{random.randint(10, 99)}"  # 模擬MAC地址
        self.ip_address = f"192.168.1.{random.randint(100, 200)}"      # 模擬IP地址
        self.model = "Gateway-Simulator-1.0"
        self.firmware_version = "1.0.0"
        self.connected_devices = []   # 連接的設備列表
        self.device_report_task = None  # 設備狀態報告任務

# 模擬設備類
class Device:
    def __init__(self, device_id=None):
        self.id = device_id or str(uuid.uuid4())
        self.mac_address = f"DD:EE:FF:00:11:{random.randint(10, 99)}"
        self.type = random.choice(["EPD", "LCD", "LED"])
        self.status = random.choice(["online", "offline", "error"])
        self.battery = random.randint(0, 100)
        self.signal_strength = random.randint(-90, -30)
        self.last_seen = time.time()

    def to_dict(self):
        return {
            "id": self.id,
            "macAddress": self.mac_address,
            "type": self.type,
            "status": self.status,
            "battery": self.battery,
            "signalStrength": self.signal_strength,
            "lastSeen": self.last_seen
        }

    def update_status(self):
        """更新設備狀態，模擬真實設備的狀態變化"""
        self.status = random.choice(["online", "online", "online", "offline", "error"])
        self.battery = max(0, min(100, self.battery + random.randint(-5, 3)))
        self.signal_strength = max(-90, min(-30, self.signal_strength + random.randint(-5, 5)))
        self.last_seen = time.time()

# 網關HTTP處理器
class GatewayHTTPHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, gateway_state=None, **kwargs):
        self.gateway_state = gateway_state
        super().__init__(*args, **kwargs)

    def do_GET(self):
        """處理GET請求"""
        if self.path == "/api/status":
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()

            status = {
                "mode": self.gateway_state.mode.value,
                "macAddress": self.gateway_state.mac_address,
                "ipAddress": self.gateway_state.ip_address,
                "model": self.gateway_state.model,
                "firmwareVersion": self.gateway_state.firmware_version,
                "connectedDevicesCount": len(self.gateway_state.connected_devices)
            }

            self.wfile.write(json.dumps(status).encode())
        else:
            self.send_response(404)
            self.end_headers()

    def do_POST(self):
        """處理POST請求"""
        if self.path == "/api/connect":
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length).decode('utf-8')

            try:
                data = json.loads(post_data)
                logger.info(f"收到連接請求: {data}")

                # 提取WebSocket連接信息
                self.gateway_state.ws_url = data.get('wsUrl')
                self.gateway_state.ws_token = data.get('wsToken')

                if not self.gateway_state.ws_url or not self.gateway_state.ws_token:
                    raise ValueError("缺少WebSocket URL或Token")

                # 切換到工作模式
                self.switch_to_work_mode()

                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"success": True}).encode())
            except Exception as e:
                logger.error(f"處理連接請求錯誤: {str(e)}")
                self.send_response(400)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": str(e)}).encode())
        else:
            self.send_response(404)
            self.end_headers()

    def switch_to_work_mode(self):
        """切換到工作模式並啟動WebSocket連接"""
        if self.gateway_state.mode == GatewayMode.SCAN:
            logger.info("切換到工作模式")
            self.gateway_state.mode = GatewayMode.WORK

            # 在新線程中啟動WebSocket連接
            threading.Thread(target=self.start_websocket_connection, daemon=True).start()

    def start_websocket_connection(self):
        """啟動WebSocket連接的異步處理"""
        asyncio.run(self.connect_websocket())

    async def connect_websocket(self):
        """連接到WebSocket服務器"""
        ws_url = f"{self.gateway_state.ws_url}?token={self.gateway_state.ws_token}"
        logger.info(f"嘗試連接到WebSocket服務器: {ws_url}")

        try:
            async with websockets.connect(ws_url) as websocket:
                self.gateway_state.ws_connection = websocket
                logger.info("WebSocket連接成功")

                # 發送網關信息
                await self.send_gateway_info(websocket)

                # 啟動設備狀態報告任務
                self.gateway_state.device_report_task = asyncio.create_task(
                    self.report_device_status(websocket)
                )

                # 處理接收到的消息
                await self.handle_messages(websocket)
        except Exception as e:
            logger.error(f"WebSocket連接錯誤: {str(e)}")
            # 如果連接失敗，嘗試重新連接
            await asyncio.sleep(5)
            asyncio.create_task(self.connect_websocket())

    async def send_gateway_info(self, websocket):
        """發送網關信息到服務器"""
        gateway_info = {
            "type": "gatewayInfo",
            "info": {
                "macAddress": self.gateway_state.mac_address,
                "ipAddress": self.gateway_state.ip_address,
                "model": self.gateway_state.model,
                "firmwareVersion": self.gateway_state.firmware_version
            }
        }

        await websocket.send(json.dumps(gateway_info))
        logger.info("已發送網關信息")

    async def report_device_status(self, websocket):
        """定期報告設備狀態"""
        while True:
            if self.gateway_state.mode == GatewayMode.WORK:
                # 模擬掃描連接的設備
                self.scan_connected_devices()

                # 更新現有設備的狀態
                for device in self.gateway_state.connected_devices:
                    device.update_status()

                # 發送設備狀態
                devices_data = [device.to_dict() for device in self.gateway_state.connected_devices]
                message = {
                    "type": "deviceStatus",
                    "devices": devices_data
                }

                try:
                    await websocket.send(json.dumps(message))
                    logger.info(f"已發送設備狀態 ({len(devices_data)} 個設備)")
                except Exception as e:
                    logger.error(f"發送設備狀態錯誤: {str(e)}")

            # 每10秒報告一次
            await asyncio.sleep(10)

    def scan_connected_devices(self):
        """模擬掃描連接的設備"""
        # 隨機添加或移除設備，模擬真實環境
        if random.random() < 0.2 and len(self.gateway_state.connected_devices) < 10:  # 20%機率添加設備
            new_device = Device()
            self.gateway_state.connected_devices.append(new_device)
            logger.info(f"發現新設備: {new_device.mac_address}")

        if random.random() < 0.1 and self.gateway_state.connected_devices:  # 10%機率移除設備
            removed_device = random.choice(self.gateway_state.connected_devices)
            self.gateway_state.connected_devices.remove(removed_device)
            logger.info(f"設備離線: {removed_device.mac_address}")

    async def handle_messages(self, websocket):
        """處理從服務器接收的消息"""
        async for message in websocket:
            try:
                data = json.loads(message)
                logger.info(f"收到消息: {data}")

                message_type = data.get('type')
                if message_type == 'restart':
                    logger.info("收到重啟命令")
                    # 模擬重啟
                    await asyncio.sleep(2)
                    logger.info("網關重啟中...")
                    # 實際應用中這裡會觸發真實的重啟
                elif message_type == 'updateFirmware':
                    firmware_type = data.get('firmwareType')
                    version = data.get('version')
                    url = data.get('url')
                    logger.info(f"收到固件更新命令: 類型={firmware_type}, 版本={version}, URL={url}")
                    # 模擬固件更新過程
                    await self.simulate_firmware_update(websocket, firmware_type, version, url)
                elif message_type == 'pong':
                    # 處理心跳回應
                    logger.debug("收到心跳回應")
                else:
                    logger.warning(f"收到未知類型的消息: {message_type}")
            except Exception as e:
                logger.error(f"處理消息錯誤: {str(e)}")

    async def simulate_firmware_update(self, websocket, firmware_type, version, url):
        """模擬固件更新過程"""
        stages = ["downloading", "verifying", "installing", "rebooting", "complete"]

        for stage in stages:
            update_status = {
                "type": "firmwareUpdateStatus",
                "status": stage,
                "progress": random.randint(0, 100) if stage != "complete" else 100,
                "firmwareType": firmware_type,
                "version": version
            }

            await websocket.send(json.dumps(update_status))
            logger.info(f"固件更新狀態: {stage}")
            await asyncio.sleep(2)  # 模擬每個階段的延遲

        # 更新完成後更新固件版本
        self.gateway_state.firmware_version = version
        logger.info(f"固件已更新到版本 {version}")

# 網關模擬器主類
class GatewaySimulator:
    def __init__(self, http_port=8080):
        self.http_port = http_port
        self.gateway_state = GatewayState()
        self.http_server = None

        # 初始化一些模擬設備
        for _ in range(3):
            self.gateway_state.connected_devices.append(Device())

    def start(self):
        """啟動網關模擬器"""
        logger.info(f"啟動網關模擬器 (MAC: {self.gateway_state.mac_address}, IP: {self.gateway_state.ip_address})")

        # 創建HTTP服務器
        handler = lambda *args, **kwargs: GatewayHTTPHandler(*args, gateway_state=self.gateway_state, **kwargs)
        self.http_server = HTTPServer(('0.0.0.0', self.http_port), handler)

        logger.info(f"HTTP服務器運行在端口 {self.http_port}")
        logger.info(f"網關處於{self.gateway_state.mode.value}模式")

        # 在單獨的線程中運行HTTP服務器
        threading.Thread(target=self.http_server.serve_forever, daemon=True).start()

        try:
            # 保持主線程運行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("正在關閉網關模擬器...")
            self.stop()

    def stop(self):
        """停止網關模擬器"""
        if self.http_server:
            self.http_server.shutdown()
            logger.info("HTTP服務器已關閉")

# 主程序入口
if __name__ == "__main__":
    simulator = GatewaySimulator()
    simulator.start()
```

#### 3.3.2 網關工作模式與掃描模式的行為規劃

##### 掃描模式 (Scan Mode)

掃描模式是網關的初始狀態，主要特點和行為如下：

1. **初始化行為**：
   - 網關啟動時默認進入掃描模式
   - 啟動HTTP服務器，監聽配置請求
   - 不主動連接到Server端WebSocket服務

2. **設備掃描**：
   - 定期掃描本地網絡中的設備
   - 維護一個已發現設備的列表
   - 記錄設備的MAC地址、信號強度等基本信息

3. **配置接收**：
   - 通過HTTP API接收來自App的配置信息
   - 接收WebSocket連接信息（URL和認證Token）
   - 驗證配置信息的有效性

4. **模式轉換**：
   - 當收到有效的WebSocket連接信息後，切換到工作模式
   - 關閉任何現有的WebSocket連接（如果有）
   - 準備建立新的WebSocket連接

5. **狀態查詢**：
   - 提供HTTP API供App查詢網關當前狀態
   - 返回網關模式、MAC地址、IP地址等信息

##### 工作模式 (Work Mode)

工作模式是網關與Server建立連接後的運行狀態，主要特點和行為如下：

1. **連接建立**：
   - 使用接收到的URL和Token連接到Server的WebSocket服務
   - 實施重連機制，確保連接穩定性
   - 發送初始化信息（網關信息）到Server

2. **設備監控**：
   - 持續監控已連接設備的狀態
   - 檢測新設備的加入和現有設備的離線
   - 更新設備的電池電量、信號強度等動態信息

3. **數據上報**：
   - 定期（如每10秒）向Server發送設備狀態報告
   - 發送設備的詳細信息，包括ID、MAC地址、類型、狀態等
   - 實施數據壓縮和批量發送，優化網絡使用

4. **命令處理**：
   - 接收並處理來自Server的命令
   - 支持重啟、固件更新等基本操作
   - 執行設備控制命令，如顯示更新、設置參數等

5. **心跳機制**：
   - 定期發送心跳消息確保連接活躍
   - 處理心跳回應，監控連接延遲
   - 在連接中斷時自動嘗試重新連接

6. **異常處理**：
   - 處理網絡異常、命令執行失敗等情況
   - 實施日誌記錄，便於問題診斷
   - 在嚴重錯誤時可回退到掃描模式

7. **安全機制**：
   - 驗證所有接收到的命令的有效性
   - 加密敏感數據傳輸
   - 防止未授權的命令執行

通過這種模式設計，網關能夠在初始配置階段（掃描模式）和正常運行階段（工作模式）表現出不同的行為，適應不同的應用場景需求。

## 4. 安全考慮

1. **認證與授權**：
   - 使用JWT進行API認證
   - 為每個網關生成唯一的認證Token
   - 實施基於角色的訪問控制

2. **數據加密**：
   - 使用HTTPS/WSS進行通信加密
   - 敏感數據存儲加密

3. **防護措施**：
   - 實施速率限制防止暴力攻擊
   - 輸入驗證防止注入攻擊
   - 定期更新依賴庫修復安全漏洞

## 5. 擴展性考慮

1. **水平擴展**：
   - WebSocket服務可以部署多個實例
   - 使用Redis等共享存儲保存會話狀態

2. **功能擴展**：
   - 模塊化設計便於添加新功能
   - 版本控制API便於向後兼容

## 6. 實施計劃

1. **第一階段**：Server端WebSocket服務實現
   - 實現基本的WebSocket服務
   - 網關註冊API已經存在，不需要重新開發
   - 整合WebSocket服務到現有系統
   - 擴展網關API，提供WebSocket連接信息

   > 注意：網關註冊將直接使用現有的API，如 `scripts\generate-gateway-device-data.js` 中的 `createDevice` 和 `createGateway` 函數。App端將直接調用這些API進行網關註冊。

   **WebSocket連接信息提供方案**：

   在網關註冊或更新API的響應中添加唯一的WebSocket連接信息，包括：

   ```javascript
   // 擴展現有的網關API響應，添加WebSocket連接信息
   router.post('/gateways', authenticate, checkPermission(['gateway:create']), async (req, res) => {
     try {
       // 現有的網關創建邏輯...

       // 獲取網關ID和門店ID
       const gatewayId = result.insertedId.toString();
       const storeId = gatewayData.storeId || existingResponseData.storeId;

       // 生成網關專用的認證Token，包含門店ID
       const gatewayToken = jwt.sign(
         {
           gatewayId: gatewayId,
           storeId: storeId,
           type: 'gateway'
         },
         process.env.JWT_SECRET || 'your-secret-key',
         { expiresIn: '30d' } // 網關Token有效期較長
       );

       // 為每個網關生成唯一的WebSocket路徑，包含門店ID
       const wsPath = `/ws/store/${storeId}/gateway/${gatewayId}`;

       // 在響應中添加WebSocket連接信息
       res.status(201).json({
         ...existingResponseData,
         websocket: {
           url: `ws://${req.headers.host || 'localhost:3001'}${wsPath}`,
           path: wsPath,
           token: gatewayToken,
           protocol: 'json'
         }
       });
     } catch (error) {
       // 錯誤處理...
     }
   });
   ```

   同樣地，在獲取單個網關的API中也添加唯一的WebSocket連接信息：

   ```javascript
   router.get('/gateways/:id', authenticate, checkPermission(['gateway:view']), async (req, res) => {
     try {
       // 現有的獲取網關邏輯...

       // 獲取網關ID和門店ID
       const gatewayId = gateway._id.toString();
       const storeId = gateway.storeId;

       // 生成網關專用的認證Token，包含門店ID
       const gatewayToken = jwt.sign(
         {
           gatewayId: gatewayId,
           storeId: storeId,
           type: 'gateway'
         },
         process.env.JWT_SECRET || 'your-secret-key',
         { expiresIn: '30d' }
       );

       // 為每個網關生成唯一的WebSocket路徑，包含門店ID
       const wsPath = `/ws/store/${storeId}/gateway/${gatewayId}`;

       // 在響應中添加WebSocket連接信息
       res.json({
         ...gateway,
         websocket: {
           url: `ws://${req.headers.host || 'localhost:3001'}${wsPath}`,
           path: wsPath,
           token: gatewayToken,
           protocol: 'json'
         }
       });
     } catch (error) {
       // 錯誤處理...
     }
   });
   ```

   這樣，每個網關都會獲得一個唯一的WebSocket連接URL，包含其自身的ID，使Server能夠更容易地識別和管理不同的網關連接。App在註冊網關或獲取網關信息時，就能同時獲取到這些唯一的WebSocket連接信息，然後將這些信息提供給網關設備。

2. **第二階段**：App端實現
   - 開發網關掃描功能
   - 實現網關註冊流程，調用Server端API (詳見下方API調用參考)
   - 獲取WebSocket連接信息並提供給網關

   **API調用參考**：
   ```javascript
   // App端調用Server API的示例代碼

   // 1. 登入獲取認證Token
   async function login(username, password) {
     try {
       const response = await fetch('http://server-address:3001/api/auth/login', {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json'
         },
         body: JSON.stringify({ username, password })
       });

       if (!response.ok) {
         throw new Error(`Login failed: ${response.status}`);
       }

       const data = await response.json();
       return data.token; // 保存token用於後續請求
     } catch (error) {
       console.error('Login error:', error);
       throw error;
     }
   }

   // 2. 獲取已註冊的網關列表
   async function getRegisteredGateways(token, storeId) {
     try {
       const url = storeId
         ? `http://server-address:3001/api/gateways?storeId=${storeId}`
         : 'http://server-address:3001/api/gateways';

       const response = await fetch(url, {
         method: 'GET',
         headers: {
           'Content-Type': 'application/json',
           'Authorization': `Bearer ${token}`
         }
       });

       if (!response.ok) {
         throw new Error(`Failed to get gateways: ${response.status}`);
       }

       return await response.json();
     } catch (error) {
       console.error('Error getting gateways:', error);
       throw error;
     }
   }

   // 3. 註冊新網關
   async function registerGateway(token, gatewayData) {
     try {
       const response = await fetch('http://server-address:3001/api/gateways', {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         body: JSON.stringify(gatewayData)
       });

       if (!response.ok) {
         throw new Error(`Failed to register gateway: ${response.status}`);
       }

       return await response.json();
     } catch (error) {
       console.error('Error registering gateway:', error);
       throw error;
     }
   }

   // 4. 獲取網關的WebSocket連接信息
   async function getGatewayWithWsInfo(token, gatewayId) {
     try {
       const response = await fetch(`http://server-address:3001/api/gateways/${gatewayId}`, {
         method: 'GET',
         headers: {
           'Content-Type': 'application/json',
           'Authorization': `Bearer ${token}`
         }
       });

       if (!response.ok) {
         throw new Error(`Failed to get gateway: ${response.status}`);
       }

       const gateway = await response.json();

       // 檢查響應中是否包含WebSocket連接信息
       if (!gateway.websocket) {
         throw new Error('Gateway response does not contain WebSocket information');
       }

       return gateway;
     } catch (error) {
       console.error('Error getting gateway with WebSocket info:', error);
       throw error;
     }
   }

   // 5. 發送連接命令給網關
   async function sendConnectCommand(gatewayIp, wsInfo) {
     try {
       const response = await fetch(`http://${gatewayIp}/api/connect`, {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json'
         },
         body: JSON.stringify({
           wsUrl: wsInfo.url,
           wsToken: wsInfo.token,
           wsProtocol: wsInfo.protocol
         })
       });

       if (!response.ok) {
         throw new Error(`Failed to send connect command: ${response.status}`);
       }

       return await response.json();
     } catch (error) {
       console.error('Error sending connect command:', error);
       throw error;
     }
   }

   // 6. 完整的網關註冊和配置流程示例
   async function registerAndConfigureGateway(username, password, gatewayData, gatewayIp) {
     try {
       // 1. 登入獲取認證Token
       const authToken = await login(username, password);

       // 2. 註冊新網關
       const newGateway = await registerGateway(authToken, gatewayData);

       // 3. 獲取包含WebSocket連接信息的網關詳情
       const gatewayWithWsInfo = await getGatewayWithWsInfo(authToken, newGateway._id);

       // 4. 發送WebSocket連接命令給網關
       const result = await sendConnectCommand(gatewayIp, gatewayWithWsInfo.websocket);

       console.log('Gateway registered and configured successfully:', result);
       return result;
     } catch (error) {
       console.error('Error in gateway registration and configuration process:', error);
       throw error;
     }
   }
   ```

3. **第三階段**：Gateway端實現
   - 開發網關固件
   - 實現工作模式和掃描模式
     - 掃描模式：初始狀態，監聽配置請求，掃描本地設備
     - 工作模式：與Server建立WebSocket連接，定期上報設備狀態，處理Server命令
   - 添加與Server的WebSocket通信
   - 實現Python模擬器用於測試和開發

4. **第四階段**：測試與優化
   - 進行端到端測試
   - 性能優化
   - 安全審計

## 7. 結論

本規劃提供了一個完整的網關與設備通信架構，包括Server、App和Gateway三個部分的實現方案。通過WebSocket實現實時通信，確保系統的高效運行和良好的用戶體驗。
