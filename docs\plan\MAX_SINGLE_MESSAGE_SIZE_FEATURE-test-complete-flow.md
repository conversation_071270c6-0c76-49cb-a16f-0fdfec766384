# 完整流程測試指南

## 測試流程說明

### 1. 分片傳輸失敗
- 客戶端接收一半分片後停止
- Server 的 `waitForChunkAck` 在 5 秒後超時
- `sendEmbeddedChunkWithRetry` 重試 3 次後失敗
- `sendChunkedRawdataWithEmbeddedIndex` 拋出錯誤

### 2. 回退機制觸發
- `sendImageToGateway` 拋出錯誤
- `sendPreviewToGateway.js` 捕獲錯誤
- 觸發回退到 `update_preview` 機制

### 3. JSON 大小檢查
- 回退時調用 `sendCommandToGateway`
- `sendCommandToGateway` 調用 `checkJsonMessageSize`
- 如果 JSON 過大，會被拒絕並記錄事件

## 預期時間線

```
T+0s:   客戶端設定 test-chunk-fail
T+1s:   請求圖片預覽 (request-image)
T+2s:   Server 開始分片傳輸
T+3s:   客戶端接收前幾個分片後停止
T+8s:   Server 第一個分片 ACK 超時 (5秒)
T+9s:   Server 重試第一次
T+14s:  Server 第二次 ACK 超時
T+15s:  Server 重試第二次
T+20s:  Server 第三次 ACK 超時
T+21s:  Server 重試第三次
T+26s:  Server 第四次 ACK 超時
T+27s:  Server 放棄重試，拋出錯誤
T+28s:  觸發回退機制
T+29s:  檢查 update_preview JSON 大小
T+30s:  如果過大，記錄事件並拒絕
```

## 測試步驟

### 1. 準備環境
```bash
# 確保 server 正在運行
cd server && npm start

# 啟動測試客戶端
cd server/tests && node ws-client-from-copied-info.js
```

### 2. 設定測試參數
- 選擇小分片大小 (如 20 bytes)
- 確保 maxSingleMessageSize 設為 2048 bytes

### 3. 啟用失敗模式
```bash
test-chunk-fail
```

### 4. 請求圖片預覽
```bash
request-image AA:BB:CC:DD:EE:01
```

### 5. 觀察日誌輸出

#### 客戶端預期日誌
```
🧪 開始模擬分片傳輸失敗 - 將接收前 3 個分片後停止
📦 開始接收分片: chunk_xxx, 總分片數: 5, 設備: AA:BB:CC:DD:EE:01
📥 收到分片 0: 16 bytes 數據 + 4 bytes index
✅ 分片 0 已儲存，進度: 1/5
📥 收到分片 1: 16 bytes 數據 + 4 bytes index
✅ 分片 1 已儲存，進度: 2/5
📥 收到分片 2: 16 bytes 數據 + 4 bytes index
✅ 分片 2 已儲存，進度: 3/5
🛑 模擬傳輸失敗 - 已接收 2 個分片（0-1），停止接收分片 2 及後續分片
⏰ 等待 server 超時檢測傳輸失敗...
```

#### Server 預期日誌
```
準備發送圖片到設備 AA:BB:CC:DD:EE:01: 1024 bytes rawdata + imageData
目標設備: AA:BB:CC:DD:EE:01, 通過網關: AA:BB:CC:DD:EE:FF
判斷分片需求: 資料=1024 bytes, Gateway maxChunkSize=20 bytes
✅ 啟用分片傳輸
使用分片傳輸，分片大小: 20 bytes
分片配置: 分片大小=20 bytes, 總分片數=52
發送分片 0/52: 24 bytes (20 + 4 index)
發送分片 1/52: 24 bytes (20 + 4 index)
發送分片 2/52: 24 bytes (20 + 4 index)
Chunk 2 attempt 1 failed: Error: Chunk 2 ACK timeout
Chunk 2 attempt 2 failed: Error: Chunk 2 ACK timeout
Chunk 2 attempt 3 failed: Error: Chunk 2 ACK timeout
❌ 圖片傳輸失敗: Error: Chunk 2 failed after 3 attempts
分片傳輸失敗，回退到傳統方式: Error: Chunk 2 failed after 3 attempts
檢查 JSON 訊息大小: 3456 bytes, Gateway 限制: 2048 bytes
❌ JSON 訊息大小 3456 bytes 超過 Gateway AA:BB:CC:DD:EE:FF 的限制 2048 bytes，拒絕發送
📝 記錄因 JSON 訊息過大而被拒絕的任務
已記錄網關 xxx 的訊息被拒絕事件
```

## 驗證要點

### ✅ 成功標準
1. **分片部分成功**：客戶端正確接收部分分片
2. **超時檢測**：Server 檢測到 ACK 超時
3. **重試機制**：Server 進行 3 次重試
4. **回退觸發**：分片失敗後觸發回退機制
5. **JSON 檢查**：update_preview JSON 大小檢查正常
6. **事件記錄**：被拒絕的任務正確記錄

### ❌ 可能的問題
1. **沒有超時**：檢查客戶端是否正確停止處理
2. **沒有回退**：檢查 sendPreviewToGateway.js 的錯誤處理
3. **沒有檢查**：檢查 sendCommandToGateway 的大小檢查邏輯

## 調試技巧

### 1. 檢查分片停止
確認客戶端日誌中有：
```
🛑 模擬傳輸失敗 - 已接收 X 個分片，停止接收分片 Y 及後續分片
```

### 2. 檢查超時
確認 server 日誌中有：
```
Chunk X attempt Y failed: Error: Chunk X ACK timeout
```

### 3. 檢查回退
確認 server 日誌中有：
```
分片傳輸失敗，回退到傳統方式: Error: ...
```

### 4. 檢查 JSON 大小
確認 server 日誌中有：
```
檢查 JSON 訊息大小: XXXX bytes, Gateway 限制: 2048 bytes
```

## 故障排除

### 問題 1: 客戶端沒有停止接收
**解決**: 檢查 globalSimulateFailure 標記是否正確設定

### 問題 2: Server 沒有超時
**解決**: 檢查 waitForChunkAck 的超時設定 (5秒)

### 問題 3: 沒有觸發回退
**解決**: 檢查 sendPreviewToGateway.js 的 catch 邏輯

### 問題 4: JSON 沒有被檢查
**解決**: 檢查 sendCommandToGateway 中的 checkJsonMessageSize 調用

## 測試變化

### 測試 1: 不同分片大小
- 20 bytes: 產生很多分片
- 200 bytes: 產生中等數量分片
- 1024 bytes: 產生少量分片

### 測試 2: 不同 maxSingleMessageSize
- 1024 bytes: 嚴格限制
- 4096 bytes: 寬鬆限制
- 未設定: 無限制

### 測試 3: 不同設備數據
- 小圖片: JSON 可能不會超過限制
- 大圖片: JSON 更容易超過限制

這個完整的測試流程可以驗證 maxSingleMessageSize 功能的所有方面！
