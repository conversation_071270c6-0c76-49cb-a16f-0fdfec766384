# 後端圖片URL處理修復

## 問題描述

Template 使用新的方式儲存 imageUrl（只儲存文件ID），但後端圖片渲染沒有跟著使用新方式取得圖片。

### 具體問題

1. **前端已經修改**：使用 `processImageUrl()` 函數將文件ID轉換為完整URL
2. **後端未修改**：在 `server/services/previewService.js` 的 `renderCanvasToImage` 函數中，直接使用原始的 `imageUrl`，沒有進行URL處理
3. **結果**：當 imageUrl 是文件ID格式時，後端無法正確載入圖片，導致預覽圖生成失敗

### 錯誤表現

- 模板中的圖片元素在後端預覽生成時顯示為"圖片加載失敗"
- 控制台出現圖片載入錯誤訊息
- 預覽圖中圖片元素顯示為紅色錯誤佔位符

## 解決方案

### 1. 創建後端圖片URL處理函數

在 `server/services/previewService.js` 中添加了 `processImageUrl` 函數：

```javascript
/**
 * 處理圖片 URL，確保能正確載入圖片
 * 支援多種格式：完整URL、相對路徑、文件ID
 * @param {string} imageUrl 原始圖片 URL 或文件ID
 * @returns {string} 處理後的完整圖片 URL
 */
function processImageUrl(imageUrl) {
  if (!imageUrl) return imageUrl;

  // 如果看起來像文件 ID（24位十六進制字符），直接構建 API URL
  if (/^[a-f0-9]{24}$/i.test(imageUrl)) {
    return `http://localhost:3001/api/files/${imageUrl}`;
  }

  // 如果是相對路徑，構建完整的 API URL
  if (imageUrl.startsWith('/api/files/')) {
    return `http://localhost:3001${imageUrl}`;
  }

  // 如果是完整的 HTTP/HTTPS URL，檢查是否需要轉換
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    if (imageUrl.includes('localhost:3001') || imageUrl.includes('127.0.0.1:3001')) {
      return imageUrl; // 已經是正確的格式
    }
    return imageUrl;
  }

  // 其他情況直接返回原 URL
  return imageUrl;
}
```

### 2. 修改圖片渲染邏輯

在 `renderCanvasToImage` 函數的圖片元素處理部分，使用新的URL處理函數：

```javascript
// 修改前
const image = await loadImage(imageUrl);

// 修改後
const processedImageUrl = processImageUrl(imageUrl);
console.log(`嘗試加載圖片: ${imageUrl} -> ${processedImageUrl}`);
const image = await loadImage(processedImageUrl);
```

## 修改的文件

- `server/services/previewService.js`
  - 添加 `processImageUrl` 函數（第8-40行）
  - 修改圖片渲染邏輯（第1237-1240行）

## 測試驗證

創建了測試腳本驗證前後端圖片URL處理的一致性，測試結果顯示：

- ✓ 文件ID格式處理一致
- ✓ 相對路徑格式處理一致  
- ✓ 完整URL格式處理一致
- ✓ 邊界情況處理一致

## 預期效果

修復後，後端預覽生成將能夠：

1. **正確處理文件ID格式的圖片URL**：將 `64a7b8c9d1e2f3a4b5c6d7e8` 轉換為 `http://localhost:3001/api/files/64a7b8c9d1e2f3a4b5c6d7e8`
2. **正確處理相對路徑格式的圖片URL**：將 `/api/files/64a7b8c9d1e2f3a4b5c6d7e8` 轉換為完整URL
3. **保持與前端一致的URL處理邏輯**：確保前後端生成的圖片URL完全一致
4. **成功載入圖片並渲染到預覽圖中**：圖片元素將正常顯示，不再出現錯誤佔位符

## 相關文檔

- [圖片URL動態IP修復](./image-url-dynamic-ip-fix.md) - 前端圖片URL處理機制
- [預覽圖生成流程](../plan/preview-image-generation-flow.md) - 完整的預覽圖生成流程說明

## 注意事項

1. **向後兼容性**：修復保持了對舊格式URL的支援
2. **錯誤處理**：保留了原有的錯誤處理邏輯，當圖片載入失敗時仍會顯示錯誤佔位符
3. **性能影響**：URL處理函數執行速度很快，對預覽生成性能影響微乎其微
4. **調試信息**：添加了詳細的日誌輸出，便於問題排查

## 後續改進

1. **動態IP支援**：未來可以考慮讓後端也支援動態IP檢測，而不是硬編碼 localhost:3001
2. **統一配置**：可以將API基礎URL提取為配置項，便於不同環境的部署
3. **錯誤重試**：可以添加圖片載入失敗時的重試機制
