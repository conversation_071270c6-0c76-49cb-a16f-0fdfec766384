/**
 * 測試設備功能API腳本
 * 測試設備的綁定用戶、解除綁定和設置主要網關功能
 */

const { MongoClient, ObjectId } = require('mongodb');

// 資料庫連接配置
// 根據你的實際配置修改以下資訊
const DB_URI = 'mongodb://localhost:27017';
const DB_NAME = 'epd_manager';

async function testDeviceAPI() {
  let client;
  try {
    // 連接到資料庫
    client = new MongoClient(DB_URI);
    await client.connect();
    
    console.log('成功連接到資料庫');
    
    const db = client.db(DB_NAME);
    const deviceCollection = db.collection('devices');
    const userCollection = db.collection('users');
    const gatewayCollection = db.collection('gateways');
    
    // 清空測試數據
    await cleanup(db);
    
    // 1. 創建測試數據
    console.log('創建測試數據...');
    
    // 創建測試用戶
    const userResult = await userCollection.insertOne({
      username: 'testuser',
      password: 'test_password_hash',
      name: '測試用戶',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    });
    const userId = userResult.insertedId;
    console.log(`創建測試用戶: ${userId}`);
    
    // 創建測試網關
    const gateway1Result = await gatewayCollection.insertOne({
      name: '測試網關1',
      macAddress: '11:22:33:44:55:66',
      status: 'online',
      storeId: 'store1',
      devices: [],
      createdAt: new Date(),
      updatedAt: new Date()
    });
    const gateway1Id = gateway1Result.insertedId;
    
    const gateway2Result = await gatewayCollection.insertOne({
      name: '測試網關2',
      macAddress: '22:33:44:55:66:77',
      status: 'online',
      storeId: 'store1',
      devices: [],
      createdAt: new Date(),
      updatedAt: new Date()
    });
    const gateway2Id = gateway2Result.insertedId;
    
    console.log(`創建測試網關1: ${gateway1Id}`);
    console.log(`創建測試網關2: ${gateway2Id}`);
    
    // 創建測試設備
    const deviceResult = await deviceCollection.insertOne({
      macAddress: 'AA:BB:CC:DD:EE:FF',
      status: 'online',
      dataId: '',
      storeId: 'store1',
      initialized: false,
      otherGateways: [],
      lastSeen: new Date(),
      note: '',
      data: {
        size: '2.9"',
        rssi: -70,
        battery: 100
      },
      createdAt: new Date(),
      updatedAt: new Date()
    });
    const deviceId = deviceResult.insertedId;
    console.log(`創建測試設備: ${deviceId}`);
    
    // 2. 測試綁定設備到用戶
    console.log('\n測試綁定設備到用戶...');
    await deviceCollection.updateOne(
      { _id: deviceId },
      { 
        $set: { 
          userId: userId,
          updatedAt: new Date()
        } 
      }
    );
    
    // 驗證綁定結果
    let device = await deviceCollection.findOne({ _id: deviceId });
    console.log(`綁定後設備狀態: ${device.userId.equals(userId) ? '成功' : '失敗'}`);
    
    // 3. 測試設置主要網關
    console.log('\n測試設置主要網關...');
    
    // 先將測試設備添加到網關的發現設備列表中
    await gatewayCollection.updateOne(
      { _id: gateway1Id },
      { $addToSet: { devices: deviceId } }
    );
    
    // 設置主要網關
    const primaryGatewayId = gateway1Id;
    await deviceCollection.updateOne(
      { _id: deviceId },
      { 
        $set: { 
          primaryGatewayId: primaryGatewayId,
          initialized: true,
          updatedAt: new Date()
        } 
      }
    );
    
    // 驗證設置結果
    device = await deviceCollection.findOne({ _id: deviceId });
    console.log(`設置主要網關後設備狀態: ${device.primaryGatewayId.equals(primaryGatewayId) ? '成功' : '失敗'}`);
    console.log(`設備initialized狀態: ${device.initialized ? '初始化' : '未初始化'}`);
    
    // 4. 測試添加另一個網關為其他網關
    console.log('\n測試添加其他網關...');
    
    // 將測試設備添加到第二個網關的發現設備列表中
    await gatewayCollection.updateOne(
      { _id: gateway2Id },
      { $addToSet: { devices: deviceId } }
    );
    
    // 添加其他網關
    await deviceCollection.updateOne(
      { _id: deviceId },
      { 
        $addToSet: { 
          otherGateways: gateway2Id 
        },
        $set: {
          updatedAt: new Date()
        }
      }
    );
    
    // 驗證添加結果
    device = await deviceCollection.findOne({ _id: deviceId });
    console.log(`添加其他網關後設備狀態: ${device.otherGateways.some(id => id.equals(gateway2Id)) ? '成功' : '失敗'}`);
    
    // 5. 測試解除設備綁定
    console.log('\n測試解除設備綁定...');
    await deviceCollection.updateOne(
      { _id: deviceId },
      { 
        $unset: { userId: "" },
        $set: { updatedAt: new Date() }
      }
    );
    
    // 驗證解除綁定結果
    device = await deviceCollection.findOne({ _id: deviceId });
    console.log(`解除綁定後設備狀態: ${device.userId === undefined ? '成功' : '失敗'}`);
    
    console.log('\n測試完成，所有功能正常運作!');
  } catch (error) {
    console.error('測試設備API時發生錯誤:', error);
  } finally {
    // 清理測試數據
    if (client) {
      const db = client.db(DB_NAME);
      await cleanup(db);
      await client.close();
      console.log('資料庫連接已關閉');
    }
  }
}

// 清理測試數據
async function cleanup(db) {
  try {
    await db.collection('devices').deleteMany({ macAddress: 'AA:BB:CC:DD:EE:FF' });
    await db.collection('users').deleteMany({ username: 'testuser' });
    await db.collection('gateways').deleteMany({ 
      macAddress: {
        $in: ['11:22:33:44:55:66', '22:33:44:55:66:77']
      }
    });
    console.log('測試數據已清理');
  } catch (error) {
    console.error('清理測試數據時發生錯誤:', error);
  }
}

// 執行測試
testDeviceAPI().catch(console.error);
