const { ObjectId } = require('mongodb');

/**
 * 用戶模型
 * 用於存儲用戶信息
 */
class User {
  /**
   * 創建用戶集合
   * @param {Object} db - MongoDB 數據庫實例
   * @returns {Object} - MongoDB 集合
   */
  static getCollection(db) {
    return db.collection('users');
  }

  /**
   * 創建新用戶
   * @param {Object} db - MongoDB 數據庫實例
   * @param {Object} userData - 用戶數據
   * @returns {Object} - 創建的用戶
   */
  static async createUser(db, userData) {
    const collection = this.getCollection(db);

    console.log('檢查用戶名是否已存在:', userData.username);
    // 檢查用戶名是否已存在
    const existingUser = await collection.findOne({ username: userData.username });
    if (existingUser) {
      console.log('用戶名已存在');
      throw new Error('用戶名已存在');
    }

    // 創建用戶
    const newUser = {
      username: userData.username,
      password: userData.password, // 注意：密碼應該在控制器層進行加密
      name: userData.name || '',
      email: userData.email || '',
      phone: userData.phone || '',
      status: userData.status || 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('插入新用戶到數據庫', newUser);
    try {
      // 檢查集合是否存在
      const collections = await db.listCollections({ name: 'users' }).toArray();
      if (collections.length === 0) {
        console.log('users 集合不存在，創建集合');
        await db.createCollection('users');
      }

      const result = await collection.insertOne(newUser);
      console.log('用戶插入成功，ID:', result.insertedId);
      return { ...newUser, _id: result.insertedId };
    } catch (error) {
      console.error('插入用戶到數據庫失敗:', error);
      // 檢查數據庫連接狀態
      try {
        const admin = db.admin();
        const serverStatus = await admin.serverStatus();
        console.log('數據庫連接狀態:', serverStatus.ok === 1 ? '正常' : '異常');
      } catch (statusError) {
        console.error('獲取數據庫狀態失敗:', statusError);
      }
      throw new Error(`插入用戶失敗: ${error.message}`);
    }
  }

  /**
   * 根據用戶名查找用戶
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} username - 用戶名
   * @returns {Object|null} - 找到的用戶或 null
   */
  static async findByUsername(db, username) {
    const collection = this.getCollection(db);
    return await collection.findOne({ username });
  }

  /**
   * 根據 ID 查找用戶
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 用戶 ID
   * @returns {Object|null} - 找到的用戶或 null
   */
  static async findById(db, id) {
    const collection = this.getCollection(db);
    return await collection.findOne({ _id: new ObjectId(id) });
  }

  /**
   * 獲取所有用戶
   * @param {Object} db - MongoDB 數據庫實例
   * @param {Object} filter - 過濾條件
   * @param {Object} options - 查詢選項
   * @returns {Array} - 用戶列表
   */
  static async findAll(db, filter = {}, options = {}) {
    const collection = this.getCollection(db);
    return await collection.find(filter, options).toArray();
  }

  /**
   * 更新用戶
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 用戶 ID
   * @param {Object} userData - 更新的用戶數據
   * @returns {Object} - 更新結果
   */
  static async updateUser(db, id, userData) {
    const collection = this.getCollection(db);

    // 如果要更新用戶名，檢查是否已存在
    if (userData.username) {
      const existingUser = await collection.findOne({
        username: userData.username,
        _id: { $ne: new ObjectId(id) }
      });

      if (existingUser) {
        throw new Error('用戶名已存在');
      }
    }

    const updateData = {
      ...userData,
      updatedAt: new Date()
    };

    // 移除不應該更新的字段
    delete updateData._id;

    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    return result;
  }

  /**
   * 刪除用戶
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 用戶 ID
   * @returns {Object} - 刪除結果
   */
  static async deleteUser(db, id) {
    const collection = this.getCollection(db);
    return await collection.deleteOne({ _id: new ObjectId(id) });
  }

  /**
   * 重設用戶密碼
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 用戶 ID
   * @param {string} newPassword - 新密碼
   * @returns {Object} - 更新結果
   */
  static async resetPassword(db, id, newPassword) {
    const collection = this.getCollection(db);
    return await collection.updateOne(
      { _id: new ObjectId(id) },
      {
        $set: {
          password: newPassword,
          updatedAt: new Date()
        }
      }
    );
  }

  /**
   * 搜索用戶
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} query - 搜索關鍵詞
   * @returns {Array} - 搜索結果
   */
  static async searchUsers(db, query) {
    const collection = this.getCollection(db);
    return await collection.find({
      $or: [
        { username: { $regex: query, $options: 'i' } },
        { name: { $regex: query, $options: 'i' } },
        { email: { $regex: query, $options: 'i' } },
        { phone: { $regex: query, $options: 'i' } }
      ]
    }).toArray();
  }
}

module.exports = User;
