import { DataField, DataFieldType, TemplateElement, BindingInfo } from '../../types';
import { bindingCore } from './bindingCore';

/**
 * 條碼資料綁定處理器
 * 負責處理條碼元件的資料綁定邏輯
 */
export class BarcodeBinding {
  /**
   * 獲取條碼元件可綁定的資料欄位
   * 根據條碼類型過濾適用的欄位
   * @param fields 所有資料欄位
   * @param barcodeType 條碼類型
   * @returns 可綁定的資料欄位
   */
  public static getBindableFields(fields: DataField[], barcodeType?: string): DataField[] {
    // 根據條碼類型只顯示對應的特定類型
    const supportedTypes: DataFieldType[] = [];

    switch (barcodeType) {
      case 'code128':
        supportedTypes.push(DataFieldType.BARCODE_CODE128);
        break;
      case 'ean13':
        supportedTypes.push(DataFieldType.BARCODE_EAN13);
        break;
      case 'upc-a':
        supportedTypes.push(DataFieldType.BARCODE_UPC_A);
        break;
      case 'code39':
        supportedTypes.push(DataFieldType.BARCODE_CODE39);
        break;
      case 'code93':
        supportedTypes.push(DataFieldType.BARCODE_CODE93);
        break;
      default:
        // 預設為 code128 類型
        supportedTypes.push(DataFieldType.BARCODE_CODE128);
        break;
    }

    return fields.filter(field =>
      supportedTypes.includes(field.type as DataFieldType)
    );
  }

  /**
   * 處理條碼元件的綁定和內容驗證
   * @param element 元素配置
   * @param fields 資料欄位定義
   * @param sampleData 範例數據
   * @returns 處理後的元素
   */
  public static processBinding(
    element: TemplateElement,
    fields: DataField[],
    sampleData?: Record<string, any>
  ): TemplateElement {
    // 如果沒有資料綁定，直接返回原元素
    if (!element.dataBinding || !element.dataBinding.fieldId) {
      return element;
    }

    // 獲取綁定的資料欄位
    const field = fields.find(f => f.id === element.dataBinding!.fieldId);
    if (!field) {
      console.warn(`條碼綁定: 找不到欄位 ${element.dataBinding.fieldId}`);
      return element;
    }

    // 檢查欄位類型是否相容
    const bindableFields = this.getBindableFields(fields, element.barcodeType);
    if (!bindableFields.some(f => f.id === field.id)) {
      console.warn(`條碼綁定: 欄位類型 ${field.type} 與條碼類型 ${element.barcodeType} 不相容`);
      return element;
    }

    // 如果有範例數據，驗證內容
    if (sampleData && sampleData[field.id]) {
      const content = String(sampleData[field.id]);
      const validation = this.validateContent(content, element.barcodeType || 'code128');
      
      if (!validation.isValid) {
        console.warn(`條碼綁定: 內容驗證失敗 - ${validation.error}`);
      }
    }

    return element;
  }

  /**
   * 驗證條碼內容格式
   * @param content 要驗證的內容
   * @param barcodeType 條碼類型
   * @returns 驗證結果
   */
  public static validateContent(content: string, barcodeType: string): {
    isValid: boolean;
    error?: string;
    sanitizedContent?: string;
  } {
    if (!content || typeof content !== 'string') {
      return {
        isValid: false,
        error: '內容不能為空'
      };
    }

    // 條碼驗證規則
    const BARCODE_LIMITS: Record<string, { length?: number; maxLength?: number; charset: string }> = {
      code128: { maxLength: 80, charset: 'ascii' },
      ean13: { length: 13, charset: 'numeric' },
      'upc-a': { length: 12, charset: 'numeric' },
      code39: { maxLength: 43, charset: 'code39' },
      code93: { maxLength: 47, charset: 'ascii' }
    };

    const limits = BARCODE_LIMITS[barcodeType] || BARCODE_LIMITS.code128;

    // 長度檢查
    if (limits.length) {
      // 固定長度檢查
      if (content.length !== limits.length) {
        return {
          isValid: false,
          error: `${barcodeType.toUpperCase()} 需要 ${limits.length} 位數字`
        };
      }
    } else if (limits.maxLength) {
      // 最大長度檢查
      if (content.length > limits.maxLength) {
        return {
          isValid: false,
          error: `內容長度超過限制 (${limits.maxLength} 字符)`
        };
      }
    }

    // 字符集檢查
    switch (limits.charset) {
      case 'numeric':
        if (!/^\d+$/.test(content)) {
          return {
            isValid: false,
            error: '僅支援數字字符',
            sanitizedContent: content.replace(/\D/g, '')
          };
        }
        break;
      case 'ascii':
        if (!/^[\x00-\x7F]*$/.test(content)) {
          return {
            isValid: false,
            error: '僅支援 ASCII 字符',
            sanitizedContent: content.replace(/[^\x00-\x7F]/g, '?')
          };
        }
        break;
      case 'code39':
        // Code39 支援的字符集：A-Z, 0-9, 空格, -, ., $, /, +, %, *
        if (!/^[A-Z0-9 \-.$\/+%*]*$/.test(content.toUpperCase())) {
          return {
            isValid: false,
            error: 'Code39 僅支援 A-Z, 0-9, 空格, -, ., $, /, +, %, * 字符',
            sanitizedContent: content.toUpperCase().replace(/[^A-Z0-9 \-.$\/+%*]/g, '')
          };
        }
        break;
    }

    return {
      isValid: true,
      sanitizedContent: content
    };
  }

  /**
   * 根據條碼類型獲取範例值
   * @param barcodeType 條碼類型
   * @returns 範例值
   */
  public static getSampleValueForBarcodeType(barcodeType?: string): string {
    switch (barcodeType) {
      case 'ean13':
        return '1234567890123';
      case 'upc-a':
        return '123456789012';
      case 'code39':
        return 'SAMPLE123';
      case 'code93':
        return 'SAMPLE123';
      case 'code128':
      default:
        return 'Sample123';
    }
  }

  /**
   * 獲取條碼類型的顯示名稱
   * @param barcodeType 條碼類型
   * @returns 顯示名稱
   */
  public static getBarcodeTypeDisplayName(barcodeType?: string): string {
    switch (barcodeType) {
      case 'code128': return 'Code 128';
      case 'ean13': return 'EAN-13';
      case 'upc-a': return 'UPC-A';
      case 'code39': return 'Code 39';
      case 'code93': return 'Code 93';
      default: return 'Code 128';
    }
  }

  /**
   * 獲取支援的條碼類型列表
   * @returns 條碼類型選項
   */
  public static getSupportedBarcodeTypes(): Array<{ value: string; label: string; length?: number; maxLength?: number }> {
    return [
      { value: 'code128', label: 'Code 128', maxLength: 80 },
      { value: 'ean13', label: 'EAN-13', length: 13 },
      { value: 'upc-a', label: 'UPC-A', length: 12 },
      { value: 'code39', label: 'Code 39', maxLength: 43 },
      { value: 'code93', label: 'Code 93', maxLength: 47 }
    ];
  }

  /**
   * 檢查欄位類型是否與條碼類型相容
   * @param fieldType 欄位類型
   * @param barcodeType 條碼類型
   * @returns 是否相容
   */
  public static isFieldTypeCompatible(fieldType: string, barcodeType?: string): boolean {
    const baseTypes = [
      DataFieldType.TEXT,
      DataFieldType.NUMBER,
      DataFieldType.UNIQUE_IDENTIFIER
    ];

    // 檢查基本類型
    if (baseTypes.includes(fieldType as DataFieldType)) {
      return true;
    }

    // 檢查特定類型
    switch (barcodeType) {
      case 'code128':
        return fieldType === DataFieldType.BARCODE_CODE128;
      case 'ean13':
        return fieldType === DataFieldType.BARCODE_EAN13;
      case 'upc-a':
        return fieldType === DataFieldType.BARCODE_UPC_A;
      case 'code39':
        return fieldType === DataFieldType.BARCODE_CODE39;
      case 'code93':
        return fieldType === DataFieldType.BARCODE_CODE93;
      default:
        return false;
    }
  }

  /**
   * 根據欄位類型推薦條碼類型
   * @param fieldType 欄位類型
   * @returns 推薦的條碼類型
   */
  public static getRecommendedBarcodeType(fieldType: string): string {
    switch (fieldType) {
      case DataFieldType.BARCODE_CODE128:
        return 'code128';
      case DataFieldType.BARCODE_EAN13:
        return 'ean13';
      case DataFieldType.BARCODE_UPC_A:
        return 'upc-a';
      case DataFieldType.BARCODE_CODE39:
        return 'code39';
      case DataFieldType.BARCODE_CODE93:
        return 'code93';
      case DataFieldType.NUMBER:
        return 'ean13'; // 數字類型預設推薦 EAN13
      case DataFieldType.TEXT:
      case DataFieldType.UNIQUE_IDENTIFIER:
      default:
        return 'code128'; // 文字類型預設推薦 Code128
    }
  }

  /**
   * 為條碼元件設置綁定並進行預處理
   * @param element 條碼元件
   * @param dataIndex 數據索引
   * @param fieldId 欄位ID
   * @param selectedStoreId 預覽門店數據的選擇門店ID
   * @returns 處理後的條碼元件
   */
  public static setBinding(
    element: TemplateElement,
    dataIndex: number,
    fieldId: string | null,
    selectedStoreId: string | null = null
  ): TemplateElement {
    // 保留現有的 selectedStoreId，如果沒有新值則使用現有值
    const currentStoreId = element.dataBinding?.selectedStoreId;
    const storeId = selectedStoreId !== null ? selectedStoreId : currentStoreId;

    // 使用綁定核心建立綁定信息
    const updatedElement = bindingCore.bindElementToField(element, dataIndex, fieldId);

    // 如果有綁定信息且有門店ID，才添加門店ID
    if (updatedElement.dataBinding && (storeId || selectedStoreId === null)) {
      return {
        ...updatedElement,
        dataBinding: {
          ...updatedElement.dataBinding,
          selectedStoreId: storeId
        }
      };
    }

    return updatedElement;
  }

  /**
   * 移除條碼元件的資料綁定
   * @param element 條碼元件
   * @returns 移除綁定後的元件
   */
  public static removeBinding(element: TemplateElement): TemplateElement {
    return bindingCore.unbindElement(element);
  }
}
