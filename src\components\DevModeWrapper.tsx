import React, { useState, useEffect } from 'react';
import { getDevModeStatus } from '../utils/api/bugReportApi';

interface DevModeWrapperProps {
  /**
   * 子元素
   */
  children: React.ReactNode;
  
  /**
   * 是否在載入時顯示佔位符
   */
  showPlaceholderOnLoad?: boolean;
}

/**
 * 測試模式包裝器組件
 * 根據 TEST_MODE 環境變數控制子元素的顯示
 * 只有當 TEST_MODE=dev 時才顯示子元素
 */
export const DevModeWrapper: React.FC<DevModeWrapperProps> = ({
  children,
  showPlaceholderOnLoad = false
}) => {
  const [isDevMode, setIsDevMode] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const checkDevMode = async () => {
      try {
        const enabled = await getDevModeStatus();
        setIsDevMode(enabled);
      } catch (error) {
        console.error('檢查測試模式失敗:', error);
        setIsDevMode(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkDevMode();
  }, []);

  // 如果正在載入且不顯示佔位符，返回 null
  if (isLoading && !showPlaceholderOnLoad) {
    return null;
  }

  // 如果正在載入且顯示佔位符，返回空的佔位符
  if (isLoading && showPlaceholderOnLoad) {
    return <div className="opacity-50">{children}</div>;
  }

  // 根據測試模式狀態渲染子元素
  if (isDevMode) {
    return <>{children}</>;
  }

  // 非測試模式，不渲染子元素
  return null;
};

export default DevModeWrapper;
