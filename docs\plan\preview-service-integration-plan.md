# 預覽圖生成服務整合計劃

本文檔詳細說明了如何將預覽圖生成服務整合到現有系統中的步驟和時間表。

## 目錄

1. [整合概述](#整合概述)
2. [階段劃分](#階段劃分)
3. [代碼重構計劃](#代碼重構計劃)
4. [API設計](#api設計)
5. [部署流程](#部署流程)
6. [測試策略](#測試策略)
7. [回滾計劃](#回滾計劃)
8. [時間表](#時間表)

## 整合概述

整合預覽圖生成服務需要進行以下主要工作：

1. 將現有渲染邏輯抽取為共享模塊
2. 開發獨立的預覽服務
3. 修改主服務器代碼以調用預覽服務
4. 更新前端代碼以兼容新的架構
5. 部署和測試新服務

## 階段劃分

### 階段一：準備工作

1. 建立共享模塊項目結構
2. 設置構建和打包流程
3. 準備開發和測試環境

### 階段二：共享模塊開發

1. 抽取現有渲染邏輯
2. 重構為平台無關的代碼
3. 添加單元測試
4. 發布共享模塊包

### 階段三：預覽服務開發

1. 搭建Express服務框架
2. 集成Puppeteer
3. 實現預覽圖生成API
4. 添加錯誤處理和日誌記錄

### 階段四：主服務器整合

1. 添加預覽服務客戶端
2. 修改現有代碼以調用預覽服務
3. 實現向後兼容性

### 階段五：前端整合

1. 更新前端代碼以使用共享模塊
2. 添加預覽服務API調用選項
3. 優化用戶界面

### 階段六：部署和測試

1. 部署預覽服務
2. 進行集成測試
3. 進行性能和負載測試
4. 監控和調優

## 代碼重構計劃

### 1. 共享模塊結構

```
shared-preview-renderer/
  ├── src/
  │   ├── components/
  │   │   └── PreviewRenderer.ts
  │   ├── utils/
  │   │   ├── canvasUtils.ts
  │   │   ├── previewUtils.ts
  │   │   └── imageEffects.ts
  │   └── index.ts
  ├── dist/
  ├── package.json
  ├── rollup.config.js
  └── tsconfig.json
```

### 2. 從現有代碼抽取的關鍵函數

需要抽取的關鍵函數包括：

- `renderCanvasToImage` (從 canvasUtils.tsx)
- `generatePreviewImage` (從 previewUtils.ts)
- `applyImageEffect` (從 previewUtils.ts)
- `processTextBindings` 和 `restoreTextBindings` (從 previewUtils.ts)
- `PreviewComponent` 的核心渲染邏輯 (從 PreviewComponent.tsx)

### 3. 適配層設計

為了確保共享模塊在不同環境中都能正常工作，需要設計適配層：

```typescript
// 環境適配層接口
interface EnvironmentAdapter {
  createCanvas(): HTMLCanvasElement;
  createImage(): HTMLImageElement;
  querySelector(selector: string): Element | null;
  querySelectorAll(selector: string): Element[];
  createElement(tagName: string): Element;
  // 其他DOM操作方法
}

// 瀏覽器環境適配器
class BrowserAdapter implements EnvironmentAdapter {
  createCanvas() { return document.createElement('canvas'); }
  createImage() { return new Image(); }
  querySelector(selector: string) { return document.querySelector(selector); }
  querySelectorAll(selector: string) { return document.querySelectorAll(selector); }
  createElement(tagName: string) { return document.createElement(tagName); }
  // 實現其他方法
}

// Puppeteer環境適配器
class PuppeteerAdapter implements EnvironmentAdapter {
  // 在Puppeteer環境中實現相應方法
}
```

## API設計

### 預覽服務API

#### 1. 生成預覽圖

```
POST /api/preview/generate
```

**請求體**:

```json
{
  "template": {
    "id": "template-123",
    "name": "Template Name",
    "elements": [
      // 模板元素數組
    ],
    "width": 250,
    "height": 122
  },
  "bindingData": {
    "field1": "value1",
    "field2": "value2"
  },
  "storeData": [
    // 門店數據數組
  ],
  "effectType": "blackAndWhite",
  "threshold": 128
}
```

**響應**:

```json
{
  "success": true,
  "previewData": "data:image/png;base64,..."
}
```

#### 2. 批量生成預覽圖

```
POST /api/preview/batch-generate
```

**請求體**:

```json
{
  "items": [
    {
      "id": "device-1",
      "template": { /* 模板數據 */ },
      "bindingData": { /* 綁定數據 */ },
      "storeData": [ /* 門店數據 */ ]
    },
    // 更多項目
  ],
  "effectType": "blackAndWhite",
  "threshold": 128
}
```

**響應**:

```json
{
  "success": true,
  "results": [
    {
      "id": "device-1",
      "success": true,
      "previewData": "data:image/png;base64,..."
    },
    // 更多結果
  ]
}
```

## 部署流程

### 1. Docker Compose 配置

```yaml
version: '3'

services:
  preview-service:
    build:
      context: ./preview-service
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - MAX_CONCURRENT_RENDERINGS=5
    restart: always
    volumes:
      - ./logs:/app/logs
    networks:
      - app-network

  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PREVIEW_SERVICE_URL=http://preview-service:3002
    depends_on:
      - preview-service
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
```

### 2. 部署步驟

1. 構建共享模塊並發布
2. 構建預覽服務Docker鏡像
3. 更新主服務器代碼
4. 使用Docker Compose部署服務
5. 配置負載均衡和監控

## 測試策略

1. **單元測試**：測試共享模塊的各個組件
2. **集成測試**：測試預覽服務API
3. **比較測試**：比較前端和服務端生成的預覽圖
4. **性能測試**：測試預覽服務的響應時間
5. **負載測試**：測試預覽服務在高並發下的表現

## 回滾計劃

如果整合過程中出現問題，可以通過以下步驟回滾：

1. 切換回使用原有的前端預覽圖生成方式
2. 停止預覽服務
3. 恢復主服務器代碼
4. 恢復前端代碼

## 時間表

| 階段 | 任務 | 時間估計 | 負責人 |
|------|------|----------|--------|
| 階段一 | 準備工作 | 2天 | 開發團隊 |
| 階段二 | 共享模塊開發 | 5天 | 前端開發 |
| 階段三 | 預覽服務開發 | 4天 | 後端開發 |
| 階段四 | 主服務器整合 | 3天 | 後端開發 |
| 階段五 | 前端整合 | 3天 | 前端開發 |
| 階段六 | 部署和測試 | 3天 | 運維和QA |
| **總計** | | **20天** | |
