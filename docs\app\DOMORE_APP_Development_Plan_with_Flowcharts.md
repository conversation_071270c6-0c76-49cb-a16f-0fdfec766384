# DOMORE智慧雲平台APP開發計劃

## 1. 專案概述

本文檔提供了DOMORE智慧雲平台APP的開發計劃和設計細節，旨在幫助開發團隊快速構建一個功能完整、用戶體驗良好的移動應用程序，用於管理ESL電子標籤系統。

### 1.1 背景介紹

DOMORE智慧雲平台是ESL電子標籤的控制管理平台，通過雲平台能實現對系統內所有閘道、電子標籤的控制，並能與應用企業的系統對接，實現資訊在電子標籤上的即時顯示。移動APP是該平台的重要組成部分，使用戶能夠在移動設備上進行標籤管理、資料修改等操作。

### 1.2 目標用戶

- 零售店鋪管理人員
- 倉儲管理人員
- 辦公場所管理人員
- 醫療機構管理人員
- 其他需要使用電子標籤的場景管理人員

### 1.3 應用名稱

應用名稱為"CloudTag"，在各大應用商店中以此名稱發布。

## 2. 技術架構

### 2.1 開發框架選擇

#### 移動端開發框架
- **React Native**: 選擇React Native作為主要開發框架，實現跨平台(iOS和Android)開發
- **Expo**: 使用Expo工具加速開發流程，簡化配置和部署

#### Chrome Extension開發框架
- **React**: 使用React v18+作為擴展開發框架，保持與移動端相同的技術棧
- **Material-UI**: 使用Material-UI組件庫實現統一的界面風格
- **Chrome Extension Manifest V3**: 採用最新的Chrome擴展開發標準

#### 後端服務
- **Node.js + Express**: 作為中間層API服務，連接前端和DOMORE雲平台
- **RESTful API**: 設計標準化的API接口，確保與雲平台的穩定通信

#### 數據存儲
- **AsyncStorage**: 用於React Native本地數據緩存
- **Chrome Storage**: 用於Chrome Extension本地數據存儲
- **Redux**: 用於應用狀態管理
- **Redux Persist**: 實現持久化存儲

#### 共享技術棧
- **TypeScript**: 用於所有前端代碼，提供類型安全和更好的開發體驗
- **Redux & Redux Toolkit**: 統一的狀態管理解決方案
- **Axios**: 統一的HTTP請求處理
- **Jest**: 單元測試框架
- **ESLint & Prettier**: 代碼質量和風格管理

### 2.2 系統架構圖

```mermaid
graph TB
    subgraph "前端應用層"
        RN["移動應用(React Native)"]
        CE["Chrome Extension"]
    end

    subgraph "共享邏輯層"
        BL["業務邏輯層"]
        DM["數據管理層(Redux)"]
        SA["存儲適配層"]
    end

    subgraph "平台特定存儲"
        RNS["React Native Storage<br/>(AsyncStorage)"]
        CS["Chrome Storage<br/>(chrome.storage)"]
    end

    subgraph "後端服務"
        API["中間層API服務<br/>(Node.js + Express)"]
        CLOUD["DOMORE雲平台<br/>(現有系統)"]
    end

    RN --> BL
    CE --> BL
    BL --> DM
    DM --> SA
    SA --> RNS
    SA --> CS
    RN -.-> API
    CE -.-> API
    API --> CLOUD

    style RN fill:#f9f,stroke:#333,stroke-width:2px,color:#000
    style CE fill:#f9f,stroke:#333,stroke-width:2px,color:#000
    style BL fill:#bbf,stroke:#333,stroke-width:2px,color:#000
    style DM fill:#bbf,stroke:#333,stroke-width:2px,color:#000
    style SA fill:#bbf,stroke:#333,stroke-width:2px,color:#000
    style RNS fill:#dfd,stroke:#333,stroke-width:2px,color:#000
    style CS fill:#dfd,stroke:#333,stroke-width:2px,color:#000
    style API fill:#fdb,stroke:#333,stroke-width:2px,color:#000
    style CLOUD fill:#fdb,stroke:#333,stroke-width:2px,color:#000
```

### 2.3 技術依賴

#### 共享依賴
- Redux & Redux Toolkit
- Axios (HTTP請求)
- i18next (國際化)

#### React Native 特定依賴
- React Native v0.70+
- Expo SDK 48+
- React Native Paper (UI組件庫)
- React Native Vector Icons
- React Native Camera (掃描功能)
- React Native NFC Manager (NFC功能)
- React Native BLE PLX (藍牙功能)
- React Native NetInfo (網絡狀態檢測)
- AsyncStorage (本地存儲)

#### Chrome Extension 特定依賴
- React v18+
- Material-UI (UI組件庫)
- Chrome Extension Manifest V3
- Chrome Storage API
- Chrome Runtime API
- Chrome Tabs API
- Chrome Notifications API

### 2.4 架構特點

1. **分層設計**
   - 前端應用層：平台特定UI實現
   - 共享邏輯層：跨平台業務邏輯
   - 存儲適配層：統一存儲介面
   - 後端服務層：統一API服務

2. **數據流向**
   - 單向數據流
   - 統一狀態管理
   - 平台無關的業務邏輯
   - 平台特定的存儲實現

3. **離線功能**
   - 本地數據緩存
   - 離線操作隊列
   - 自動同步機制
   - 衝突解決策略

4. **擴展性**
   - 鬗耦合設計
   - 平台特性封裝
   - 可插拔的功能模組
   - 易於添加新平台支持

## 3. 功能規劃

### 3.1 核心功能列表

根據DOMORE智慧雲平台APP操作簡介，應用需要實現以下核心功能：

1. **用戶認證**
   - 登入/登出功能
   - 伺服器地址配置
   - 記住登入狀態

2. **多語言支持**
   - 中文/英文切換
   - 語言設置保存

3. **標籤管理**
   - 標籤綁定
   - 標籤解綁
   - 標籤移動
   - 標籤詳情查詢
   - 標籤點燈控制

4. **資料管理**
   - 資料修改
   - 資料查詢

5. **刷圖功能**
   - 在線刷圖
   - 離線刷圖
   - NFC刷圖

6. **設備管理**
   - LCD設備配網
   - 設備狀態查詢

### 3.2 功能優先級

| 功能 | 優先級 | 開發週期(人天) |
|------|--------|--------------|
| 用戶認證 | P0 | 3 |
| 標籤綁定 | P0 | 5 |
| 標籤解綁 | P0 | 3 |
| 資料修改 | P0 | 4 |
| 在線刷圖 | P0 | 5 |
| 標籤移動 | P1 | 4 |
| 標籤詳情 | P1 | 3 |
| 離線刷圖 | P1 | 6 |
| NFC功能 | P1 | 7 |
| 標籤點燈 | P2 | 4 |
| LCD配網 | P2 | 8 |
| 多語言支持 | P2 | 3 |

## 4. UI/UX設計

### 4.1 設計風格指南

- **配色方案**:
  - 主色: #1976D2 (藍色)
  - 輔助色: #FF5722 (橙色)
  - 背景色: #F5F5F5 (淺灰)
  - 文字色: #212121 (深灰)
  - 警告色: #F44336 (紅色)
  - 成功色: #4CAF50 (綠色)

- **字體**:
  - 主要字體: Roboto (Android), San Francisco (iOS)
  - 標題大小: 20sp
  - 正文大小: 16sp
  - 小字體: 14sp

- **間距**:
  - 基礎間距單位: 8dp
  - 內邊距: 16dp
  - 元素間距: 8dp/16dp

### 4.2 頁面導航結構

```
App
├── 登入頁
│   └── 伺服器設置
├── 主頁 (底部導航)
│   ├── 門市選擇
│   ├── 功能頁
│   │   ├── 標籤綁定
│   │   ├── 標籤解綁
│   │   ├── 標籤移動
│   │   ├── 資料修改
│   │   ├── 標籤詳情
│   │   ├── 離線刷圖
│   │   ├── NFC連接
│   │   ├── 標籤點燈
│   │   └── LCD配網
│   └── 個人中心
│       ├── 設置
│       │   └── 語言設置
│       └── 登出
```

## 5. 用戶操作流程圖

### 5.1 登入流程

```mermaid
flowchart TD
    A[開始] --> B[打開APP]
    B --> C[輸入用戶名和密碼]
    C --> D[輸入伺服器地址]
    D --> E{驗證信息}
    E -->|成功| F[進入主頁]
    E -->|失敗| G[顯示錯誤信息]
    G --> C
    F --> H[結束]
```

![登入畫面](images/cb651e0957021998323e765ec0e84c979761b564b0372662a5a3adb09ebbad7a.jpg)

### 5.2 標籤綁定流程

```mermaid
flowchart TD
    A[開始] --> B[選擇門市]
    B --> C[進入標籤綁定頁面]
    C --> D{選擇標籤方式}
    D -->|掃描| E[掃描標籤MAC]
    D -->|手動輸入| F[輸入標籤MAC]
    E --> G[選擇資料]
    F --> G
    G --> H[選擇模板]
    H --> I[確認綁定]
    I --> J{綁定結果}
    J -->|成功| K[顯示成功信息]
    J -->|失敗| L[顯示錯誤信息]
    L --> C
    K --> M[結束]
```

![標籤綁定畫面](images/a410dbeffbd2eba38c0b7e3a8df0b05927e7a749565d4b946c90991124db4e32.jpg)

### 5.3 標籤解綁流程

```mermaid
flowchart TD
    A[開始] --> B[選擇門市]
    B --> C[進入標籤解綁頁面]
    C --> D{選擇標籤方式}
    D -->|掃描| E[掃描標籤MAC]
    D -->|手動輸入| F[輸入標籤MAC]
    E --> G[確認解綁]
    F --> G
    G --> H{解綁結果}
    H -->|成功| I[顯示成功信息]
    H -->|失敗| J[顯示錯誤信息]
    J --> C
    I --> K[結束]
```

![標籤解綁畫面](images/e7dd35976751a611fd61faebf1e7c60f3fd9a91e74b4594f80da05182319ff64.jpg)

### 5.4 標籤移動流程

```mermaid
flowchart TD
    A[開始] --> B[選擇門市]
    B --> C[進入標籤移動頁面]
    C --> D{選擇標籤方式}
    D -->|掃描| E[掃描標籤MAC]
    D -->|手動輸入| F[輸入標籤MAC]
    E --> G[選擇資料]
    F --> G
    G --> H[選擇模板]
    H --> I[選擇閘道]
    I --> J[確認移動]
    J --> K{移動結果}
    K -->|成功| L[顯示成功信息]
    K -->|失敗| M[顯示錯誤信息]
    M --> C
    L --> N[結束]
```

![標籤移動畫面](images/ad086bfcd71f2e539bae77c6a0850dcb9329f6f4618d0cbce38d240a18472e35.jpg)

### 5.5 資料修改流程

```mermaid
flowchart TD
    A[開始] --> B[選擇門市]
    B --> C[進入資料修改頁面]
    C --> D{選擇資料方式}
    D -->|掃描| E[掃描資料ID]
    D -->|搜索| F[搜索資料]
    E --> G[顯示資料詳情]
    F --> G
    G --> H[修改資料欄位]
    H --> I[確認修改]
    I --> J{修改結果}
    J -->|成功| K[顯示成功信息]
    J -->|失敗| L[顯示錯誤信息]
    L --> G
    K --> M[結束]
```

![資料修改畫面](images/17cff5bc44de03efe4f1f6f694ba614429fbe6ab8e9429282f9f7a675174caef.jpg)

### 5.6 離線刷圖流程

```mermaid
flowchart TD
    A[開始] --> B[選擇門市]
    B --> C[進入離線刷圖頁面]
    C --> D{選擇標籤方式}
    D -->|掃描| E[掃描標籤MAC]
    D -->|手動輸入| F[輸入標籤MAC]
    E --> G[選擇資料]
    F --> G
    G --> H[選擇模板]
    H --> I[確認刷圖]
    I --> J{藍牙連接}
    J -->|成功| K[發送刷圖指令]
    J -->|失敗| L[顯示連接錯誤]
    L --> I
    K --> M{刷圖結果}
    M -->|成功| N[顯示成功信息]
    M -->|失敗| O[顯示錯誤信息]
    O --> I
    N --> P[結束]
```

![離線刷圖畫面](images/082f94d63a68f0982cad782dcbc7c65a1647dd1c9d7292c6ef791deb5b7ad4d5.jpg)

### 5.7 NFC刷圖流程

```mermaid
flowchart TD
    A[開始] --> B[選擇門市]
    B --> C[進入NFC連接頁面]
    C --> D[選擇NFC刷圖]
    D --> E[將手機靠近標籤]
    E --> F{NFC連接}
    F -->|成功| G[顯示標籤信息]
    F -->|失敗| H[顯示連接錯誤]
    H --> E
    G --> I[選擇資料]
    I --> J[選擇模板]
    J --> K[確認刷圖]
    K --> L{刷圖結果}
    L -->|成功| M[顯示成功信息]
    L -->|失敗| N[顯示錯誤信息]
    N --> K
    M --> O[結束]
```

![NFC刷圖入口](images/af259ae56e71d9f641e838958b1ab51e7ea6e1ff99548ad60145b79e6faecf35.jpg)
![NFC刷圖綁定頁面](images/e4f1faefbbc2adfe2192afe8d593b506ca7a8d005023c739b5d831cd516bac3a.jpg)

### 5.8 NFC切頁流程

```mermaid
flowchart TD
    A[開始] --> B[選擇門市]
    B --> C[進入NFC連接頁面]
    C --> D[選擇NFC切頁]
    D --> E[將手機靠近標籤]
    E --> F{NFC連接}
    F -->|成功| G[顯示預存圖預覽]
    F -->|失敗| H[顯示連接錯誤]
    H --> E
    G --> I[選擇頁數]
    I --> J[確認切頁]
    J --> K{切頁結果}
    K -->|成功| L[顯示成功信息]
    K -->|失敗| M[顯示錯誤信息]
    M --> J
    L --> N[結束]
```

![NFC切頁入口](images/f5be466d322f9a9aa90d7d48f7443263c06d79e012c884e6612404920e920841.jpg)

### 5.9 標籤點燈流程

```mermaid
flowchart TD
    A[開始] --> B[選擇門市]
    B --> C[進入標籤點燈頁面]
    C --> D[選擇資料]
    D --> E[顯示綁定標籤列表]
    E --> F[選擇標籤]
    F --> G[點擊燈泡圖標]
    G --> H[選擇燈光顏色]
    H --> I[選擇亮燈時長]
    I --> J[確認點燈]
    J --> K{點燈結果}
    K -->|成功| L[顯示成功信息]
    K -->|失敗| M[顯示錯誤信息]
    M --> J
    L --> N[結束]
```

![標籤點燈資料列表](images/ed60712765e404fd4a7768896a0a2734c10d144ae17f8fdb8aa49240677224e5.jpg)
![標籤點燈設定](images/373348f68e62a05031a5c3464bd63a101ab735bb0d07e924c58fa9788633e40f.jpg)

### 5.10 LCD配網流程

```mermaid
flowchart TD
    A[開始] --> B[選擇門市]
    B --> C[進入LCD配網頁面]
    C --> D[顯示LCD設備列表]
    D --> E[選擇需配網設備]
    E --> F[顯示設備詳情]
    F --> G[點擊WiFi配置]
    G --> H[選擇WiFi網絡]
    H --> I[輸入WiFi密碼]
    I --> J[確認配置]
    J --> K{配置結果}
    K -->|成功| L[顯示成功狀態]
    K -->|失敗| M[顯示錯誤信息]
    M --> G
    L --> N[結束]
```

![LCD配網設備列表](images/63bd8b7b318392823754e4dccb0fc42a64ee5f072217bd189f9ce16659ddae5a.jpg)
![LCD配網設備詳情](images/44b138d9769502d2f34b671d6ae18d5780d53e0f0fd0ba7db0ce899cf288289e.jpg)
![LCD配網WiFi配置](images/264860efa934a75317af3bee6782538310437f72eda4b1d9f06693fabdd19b62.jpg)
![LCD配網配置狀態](images/121d73290daa366bbd9039477ec09344650e309dab962424d51cfd21bec422e3.jpg)
![LCD配網配置成功](images/172ddd000dee06703324a3794da18603b7f5f788561ac98cc940c838fc65744d.jpg)

## 6. 關鍵頁面原型

### 6.1 登入頁面
- 用戶名輸入框
- 密碼輸入框
- 伺服器地址輸入框
- 登入按鈕
- 記住密碼選項

### 6.2 門市選擇頁面
- 門市列表
- 搜索框
- 門市詳情顯示

### 6.3 功能頁面
- 網格布局的功能卡片
- 每個功能卡片包含圖標和文字描述

### 6.4 標籤綁定頁面
- 標籤MAC輸入/掃描
- 資料選擇列表
- 模板選擇列表
- 綁定確認按鈕

### 6.5 資料修改頁面
- 資料搜索
- 資料列表
- 資料編輯表單
- 保存按鈕

## 7. API接口設計

### 7.1 認證接口

```
POST /api/auth/login
請求:
{
  "username": "string",
  "password": "string",
  "server": "string"
}
響應:
{
  "token": "string",
  "user": {
    "id": "string",
    "username": "string",
    "role": "string"
  }
}
```

### 7.2 門市接口

```
GET /api/stores
請求頭:
Authorization: Bearer {token}
響應:
{
  "stores": [
    {
      "id": "string",
      "name": "string",
      "address": "string",
      "image": "string"
    }
  ]
}
```

### 7.3 標籤接口

```
GET /api/tags
請求頭:
Authorization: Bearer {token}
請求參數:
storeId: "string"
mac: "string" (可選)
響應:
{
  "tags": [
    {
      "mac": "string",
      "status": "string",
      "model": "string",
      "firmware": "string",
      "bindingData": {
        "dataId": "string",
        "templateId": "string"
      }
    }
  ]
}

POST /api/tags/bind
請求頭:
Authorization: Bearer {token}
請求:
{
  "storeId": "string",
  "mac": "string",
  "dataId": "string",
  "templateId": "string"
}
響應:
{
  "success": true,
  "message": "string"
}

POST /api/tags/unbind
請求頭:
Authorization: Bearer {token}
請求:
{
  "storeId": "string",
  "mac": "string"
}
響應:
{
  "success": true,
  "message": "string"
}
```

### 7.4 資料接口

```
GET /api/data
請求頭:
Authorization: Bearer {token}
請求參數:
storeId: "string"
keyword: "string" (可選)
響應:
{
  "data": [
    {
      "id": "string",
      "fields": {
        "field1": "value1",
        "field2": "value2"
      }
    }
  ]
}

PUT /api/data/{id}
請求頭:
Authorization: Bearer {token}
請求:
{
  "storeId": "string",
  "fields": {
    "field1": "value1",
    "field2": "value2"
  }
}
響應:
{
  "success": true,
  "message": "string"
}
```

### 7.5 刷圖接口

```
POST /api/tags/refresh
請求頭:
Authorization: Bearer {token}
請求:
{
  "storeId": "string",
  "mac": "string",
  "gateway": "string" (可選，標籤移動時使用)
}
響應:
{
  "success": true,
  "message": "string"
}
```

### 7.6 LCD配網接口

```
GET /api/lcd-devices
請求頭:
Authorization: Bearer {token}
請求參數:
storeId: "string"
響應:
{
  "devices": [
    {
      "mac": "string",
      "status": "string",
      "name": "string",
      "bluetoothName": "string"
    }
  ]
}

POST /api/lcd-devices/configure
請求頭:
Authorization: Bearer {token}
請求:
{
  "storeId": "string",
  "mac": "string",
  "wifi": {
    "ssid": "string",
    "password": "string"
  }
}
響應:
{
  "success": true,
  "message": "string"
}
```

## 8. 數據模型設計

### 8.1 本地存儲模型

#### 用戶信息
```json
{
  "user": {
    "id": "string",
    "username": "string",
    "token": "string",
    "server": "string"
  }
}
```

#### 應用設置
```json
{
  "settings": {
    "language": "zh_TW | en",
    "rememberPassword": true | false,
    "lastStoreId": "string"
  }
}
```

#### 離線數據緩存
```json
{
  "offlineCache": {
    "tags": [...],
    "data": [...],
    "templates": [...]
  }
}
```

### 8.2 Redux狀態管理

```javascript
// Store結構
{
  auth: {
    isAuthenticated: boolean,
    user: Object,
    loading: boolean,
    error: string
  },
  store: {
    currentStore: Object,
    storeList: Array,
    loading: boolean
  },
  tags: {
    tagList: Array,
    currentTag: Object,
    loading: boolean,
    error: string
  },
  data: {
    dataList: Array,
    currentData: Object,
    loading: boolean
  },
  settings: {
    language: string,
    theme: string
  },
  offline: {
    isOffline: boolean,
    pendingOperations: Array
  }
}
```

## 9. 開發計劃

### 9.1 開發階段

| 階段 | 時間(週) | 主要任務 |
|------|---------|---------|
| 準備階段 | 1 | 環境搭建、技術選型確認、API設計確認 |
| 第一階段 | 2 | 實現P0級功能：用戶認證、標籤綁定/解綁、資料修改、在線刷圖 |
| 第二階段 | 2 | 實現P1級功能：標籤移動、標籤詳情、離線刷圖、NFC功能 |
| 第三階段 | 2 | 實現P2級功能：標籤點燈、LCD配網、多語言支持 |
| 測試階段 | 1 | 功能測試、性能測試、用戶體驗測試 |
| 優化階段 | 1 | 根據測試反饋進行優化、修復問題 |
| 發布階段 | 1 | 應用打包、上傳應用商店、準備發布材料 |

### 9.2 里程碑

1. **M1**: 完成開發環境搭建和基礎框架 (第1週結束)
2. **M2**: 完成P0級功能開發和單元測試 (第3週結束)
3. **M3**: 完成P1級功能開發和集成測試 (第5週結束)
4. **M4**: 完成P2級功能開發和系統測試 (第7週結束)
5. **M5**: 完成所有功能測試和優化 (第9週結束)
6. **M6**: 應用發布到應用商店 (第10週結束)

### 9.3 團隊配置

- 1名項目經理
- 2名前端開發工程師 (React Native)
- 1名後端開發工程師 (Node.js)
- 1名UI/UX設計師
- 1名測試工程師

## 10. 測試計劃

### 10.1 測試類型

- **單元測試**: 使用Jest測試框架
- **集成測試**: 使用React Native Testing Library
- **E2E測試**: 使用Detox
- **用戶體驗測試**: 真實設備測試和用戶反饋

### 10.2 測試場景

1. **認證測試**
   - 正確/錯誤的用戶名密碼
   - 不同伺服器地址
   - 網絡中斷情況

2. **標籤操作測試**
   - 標籤綁定/解綁
   - 標籤移動
   - 標籤刷圖

3. **資料操作測試**
   - 資料查詢
   - 資料修改

4. **離線功能測試**
   - 網絡中斷時的離線操作
   - 網絡恢復後的數據同步

5. **性能測試**
   - 大量數據加載
   - 電池消耗
   - 內存使用

## 11. 部署與發布計劃

### 11.1 應用打包

- 使用Expo EAS Build服務進行應用打包
- 為iOS和Android平台分別生成生產版本

### 11.2 應用商店發布

- **iOS**: App Store
- **Android**: Google Play, 華為應用市場

### 11.3 版本更新策略

- 採用漸進式更新策略
- 每2-4週發布一個小版本更新
- 每季度發布一個功能更新版本

## 12. 風險評估與應對策略

### 12.1 潛在風險

1. **API兼容性風險**
   - 風險: DOMORE雲平台API變更可能導致應用功能失效
   - 應對: 設計中間層API服務，隔離前端和後端變化

2. **設備兼容性風險**
   - 風險: 不同型號的標籤和閘道可能有不同的通信協議
   - 應對: 建立設備適配層，統一通信接口

3. **網絡穩定性風險**
   - 風險: 移動環境下網絡不穩定影響用戶體驗
   - 應對: 實現離線模式和數據緩存機制

4. **用戶體驗風險**
   - 風險: 複雜功能可能導致用戶操作困難
   - 應對: 簡化UI設計，提供操作引導和幫助文檔

### 12.2 應對措施

- 建立完善的錯誤處理和日誌記錄機制
- 實現應用崩潰自動報告系統
- 提供用戶反饋渠道
- 定期進行用戶體驗調研和優化

## 13. 維護與支持計劃

### 13.1 應用維護

- 每月進行一次安全更新
- 每季度進行一次功能更新
- 根據用戶反饋進行持續優化

### 13.2 用戶支持

- 應用內幫助文檔
- 在線客服支持
- 問題反饋機制

## 14. 結論

DOMORE智慧雲平台APP將為用戶提供便捷的移動端管理體驗，使其能夠隨時隨地管理電子標籤系統。通過本開發計劃的實施，我們將在10週內完成一個功能完整、用戶體驗良好的移動應用，滿足用戶的各種管理需求。

---

## 附錄

### A. 技術選型詳細比較

| 技術 | 優勢 | 劣勢 | 選擇理由 |
|------|------|------|---------|
| React Native | 跨平台、性能好、社區活躍 | 原生功能集成複雜 | 最佳的跨平台開發體驗 |
| Flutter | 性能優秀、UI一致性好 | 學習曲線陡峭、插件生態較弱 | - |
| Native (iOS/Android) | 性能最佳、平台特性支持最好 | 需要維護兩套代碼、開發成本高 | - |

### B. 參考資料

- DOMORE智慧雲平台操作文件 V5.15.0
- React Native官方文檔: https://reactnative.dev/docs/getting-started
- Expo文檔: https://docs.expo.dev/
- Redux文檔: https://redux.js.org/
