# GridFS 檔案存儲架構文檔

## 📋 概述

EPD Manager 系統採用 MongoDB GridFS 作為統一的檔案存儲解決方案，支援各種類型的檔案管理，包括圖片、韌體、附件等。本文檔詳細記錄了系統中 GridFS 的使用情況和架構設計。

## 🏗️ 整體架構

### GridFS vs 普通 MongoDB 存儲

```javascript
// ❌ 普通 MongoDB 文檔存儲 (不適合大檔案)
{
  _id: ObjectId,
  name: "檔案名稱",
  data: BinData(...), // 限制 16MB
  metadata: {...}
}

// ✅ GridFS 存儲方式 (無大小限制)
// fs.files 集合 (檔案元數據)
{
  _id: ObjectId("檔案ID"),
  filename: "example.jpg",
  length: 2048576,
  chunkSize: 261120,
  uploadDate: ISODate(),
  metadata: { type: 'image', mimetype: 'image/jpeg' }
}

// fs.chunks 集合 (檔案內容分塊)
{
  _id: ObjectId,
  files_id: ObjectId("檔案ID"),
  n: 0,  // 分塊序號
  data: BinData(...) // 檔案內容片段
}
```

## 🗂️ 系統中的 GridFS 使用情況

### 1. **圖庫管理** (`fileApi.js`)

**用途**: 模板編輯器的圖片資源管理

```javascript
// 檔案上傳
router.post('/files/upload', upload.single('file'), async (req, res) => {
  const { gridFSBucket } = await connectDBFunction();
  const { originalname, mimetype, buffer } = req.file;
  
  const uploadStream = gridFSBucket.openUploadStream(originalname, {
    metadata: { 
      mimetype, 
      originalFilename: originalname 
    }
  });
  
  uploadStream.write(buffer);
  uploadStream.end();
});

// 多檔案上傳
router.post('/files/upload-multiple', upload.any(), async (req, res) => {
  const { gridFSBucket } = await connectDBFunction();
  // 批次處理多個檔案...
});
```

**特點**:
- 支援單檔案和多檔案上傳
- 自動處理檔案名編碼問題
- 儲存基本元數據 (mimetype, originalFilename)

### 2. **Bug 報告附件** (`bugReportApi.js`)

**用途**: Bug 報告的圖片附件存儲

```javascript
// Bug 報告圖片上傳
const { gridFSBucket } = await getDbConnection();

if (req.file) {
  const { originalname, mimetype, buffer } = req.file;
  
  const uploadStream = gridFSBucket.openUploadStream(originalname, {
    metadata: { 
      mimetype, 
      originalFilename: originalname,
      type: 'bug-report-image'  // 特殊標記
    }
  });
  
  uploadStream.write(buffer);
  uploadStream.end();
}
```

**特點**:
- 10MB 檔案大小限制
- 僅允許圖片檔案 (image/*)
- 特殊標記 `type: 'bug-report-image'`

### 3. **軟體管理** (`softwareApi.js`)

**用途**: 韌體檔案的雙重存儲 (原始檔案 + 純韌體)

```javascript
// 儲存原始檔案
const originalFileId = await saveToGridFS(gridFSBucket, file.buffer, file.originalname, {
  type: 'original_firmware',
  deviceType: binInfo.deviceType,
  functionType: binInfo.functionType,
  version: binInfo.version,
  uploadedBy: req.user._id.toString(),
  uploadDate: new Date()
});

// 儲存純韌體內容
const pureBinId = await saveToGridFS(gridFSBucket, binInfo.binData, pureBinFilename, {
  type: 'pure_firmware',
  deviceType: binInfo.deviceType,
  functionType: binInfo.functionType,
  version: binInfo.version,
  uploadedBy: req.user._id.toString(),
  uploadDate: new Date()
});
```

**特點**:
- 100MB 檔案大小限制
- 雙重存儲策略
- 豐富的業務元數據
- CRC 校驗和驗證

## 📊 完整的數據庫架構

```
MongoDB Database: resourceManagement
├── fs.files (GridFS 檔案元數據集合)
│   ├── 一般圖片檔案
│   │   └── metadata: { mimetype, originalFilename }
│   ├── Bug報告圖片
│   │   └── metadata: { type: 'bug-report-image', mimetype, originalFilename }
│   ├── 軟體原始檔案
│   │   └── metadata: { type: 'original_firmware', deviceType, functionType, version }
│   └── 軟體純韌體
│       └── metadata: { type: 'pure_firmware', deviceType, functionType, version }
│
├── fs.chunks (GridFS 檔案內容分塊集合)
│   ├── 圖片檔案分塊
│   ├── Bug報告圖片分塊
│   └── 軟體檔案分塊
│
├── software (軟體元數據集合)
│   └── { _id, name, version, binFileId, extractedBinId, ... }
│
├── bugReports (Bug報告集合)
│   └── { _id, title, description, imageId, ... }
│
└── 其他業務集合...
```

## 🔗 檔案類型與元數據映射

| 檔案類型 | API端點 | metadata.type | 特殊欄位 | 大小限制 |
|----------|---------|---------------|----------|----------|
| **一般圖片** | `/api/files/upload` | 無 | `mimetype`, `originalFilename` | 預設 |
| **Bug報告圖片** | `/api/bug-reports` | `bug-report-image` | `mimetype`, `originalFilename` | 10MB |
| **軟體原始檔案** | `/api/software/upload` | `original_firmware` | `deviceType`, `functionType`, `version`, `uploadedBy` | 100MB |
| **軟體純韌體** | `/api/software/upload` | `pure_firmware` | `deviceType`, `functionType`, `version`, `uploadedBy` | 100MB |

## 🚀 GridFS 初始化與配置

### 統一初始化 (`server/index.js`)

```javascript
const { GridFSBucket } = require('mongodb');

let client = null;
let gridFSBucket = null;

async function connectDB() {
  try {
    if (!client) {
      client = new MongoClient(uri, {
        serverSelectionTimeoutMS: 5000,
        connectTimeoutMS: 5000,
      });
      
      await client.connect();
      const db = client.db(dbName);
      
      // 初始化 GridFS
      gridFSBucket = new GridFSBucket(db);
    }
    
    return { client, db: client.db(dbName), gridFSBucket };
  } catch (error) {
    console.error('MongoDB 連接錯誤:', error);
    throw error;
  }
}
```

### 通用 GridFS 操作函數

```javascript
// 儲存檔案到 GridFS
async function saveToGridFS(gridFSBucket, buffer, filename, metadata = {}) {
  return new Promise((resolve, reject) => {
    const uploadStream = gridFSBucket.openUploadStream(filename, { metadata });

    uploadStream.write(buffer);
    uploadStream.end();

    uploadStream.on('finish', () => {
      resolve(uploadStream.id);
    });

    uploadStream.on('error', (error) => {
      reject(error);
    });
  });
}

// 從 GridFS 下載檔案
function downloadFromGridFS(gridFSBucket, fileId) {
  return gridFSBucket.openDownloadStream(fileId);
}

// 刪除 GridFS 檔案
async function deleteFromGridFS(gridFSBucket, fileId) {
  try {
    await gridFSBucket.delete(fileId);
    return true;
  } catch (error) {
    console.error('刪除 GridFS 檔案失敗:', error);
    return false;
  }
}
```

## 🎯 前端檔案訪問

### 圖片顯示

```typescript
// 資源管理頁面
<img
  src={buildEndpointUrl('files', file._id)}  // GridFS 檔案 ID
  alt={file.filename}
  className="w-full h-full object-cover"
/>

// 圖片選擇器
<img
  src={buildEndpointUrl('files', file._id)}
  alt={file.filename}
  className="w-full h-full object-cover"
  loading="lazy"
/>
```

### 檔案下載

```typescript
// 軟體下載
export async function downloadSoftware(id: string, type: 'original' | 'pure' = 'original') {
  const response = await fetch(`${API_BASE}/software/${id}/download?type=${type}`);
  
  // 處理檔案下載...
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.click();
}
```

## 🔒 安全性考量

### 檔案類型驗證

```javascript
// Bug 報告 - 僅允許圖片
fileFilter: (req, file, cb) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('只允許上傳圖片文件'), false);
  }
}

// 軟體管理 - 僅允許 .bin 檔案
fileFilter: (req, file, cb) => {
  if (file.originalname.toLowerCase().endsWith('.bin')) {
    cb(null, true);
  } else {
    cb(new Error('只允許上傳.bin檔案'), false);
  }
}
```

### 檔案大小限制

```javascript
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB (軟體管理)
    // fileSize: 10 * 1024 * 1024,  // 10MB (Bug 報告)
  }
});
```

### 權限控制

```javascript
// API 端點權限保護
router.post('/software/upload',
  authenticate,                    // 身份驗證
  checkPermission('software:create'), // 權限檢查
  upload.single('file'),
  async (req, res) => {
    // 檔案處理邏輯...
  }
);
```

## 📈 效能優化

### 索引優化

```javascript
// GridFS 集合自動索引
// fs.files 集合
db.fs.files.createIndex({ filename: 1 });
db.fs.files.createIndex({ uploadDate: -1 });
db.fs.files.createIndex({ "metadata.type": 1 });

// fs.chunks 集合
db.fs.chunks.createIndex({ files_id: 1, n: 1 });
```

### 串流處理

```javascript
// 大檔案串流上傳
const uploadStream = gridFSBucket.openUploadStream(filename);
uploadStream.write(buffer);
uploadStream.end();

// 大檔案串流下載
const downloadStream = gridFSBucket.openDownloadStream(fileId);
downloadStream.pipe(res);
```

## 🛠️ 維護與監控

### 檔案清理

```javascript
// 清理孤立檔案 (沒有被業務集合引用的檔案)
async function cleanupOrphanedFiles() {
  // 1. 獲取所有 GridFS 檔案 ID
  const allFileIds = await db.collection('fs.files').distinct('_id');
  
  // 2. 獲取業務集合中引用的檔案 ID
  const referencedIds = [
    ...await db.collection('software').distinct('binFileId'),
    ...await db.collection('software').distinct('extractedBinId'),
    ...await db.collection('bugReports').distinct('imageId')
  ];
  
  // 3. 找出孤立檔案並刪除
  const orphanedIds = allFileIds.filter(id => !referencedIds.includes(id));
  
  for (const fileId of orphanedIds) {
    await gridFSBucket.delete(fileId);
  }
}
```

### 存儲統計

```javascript
// 獲取 GridFS 存儲統計
async function getGridFSStats() {
  const stats = await db.collection('fs.files').aggregate([
    {
      $group: {
        _id: '$metadata.type',
        count: { $sum: 1 },
        totalSize: { $sum: '$length' }
      }
    }
  ]).toArray();
  
  return stats;
}
```

## 🔮 未來擴展

### 計劃改進

1. **檔案壓縮**: 自動壓縮圖片檔案
2. **CDN 整合**: 靜態檔案 CDN 分發
3. **分散式存儲**: 多節點檔案存儲
4. **檔案版本控制**: 檔案歷史版本管理
5. **自動備份**: 定期檔案備份機制

### 擴展架構

```javascript
// 未來可能的檔案類型
const FILE_TYPES = {
  IMAGE: 'image',
  BUG_REPORT_IMAGE: 'bug-report-image',
  ORIGINAL_FIRMWARE: 'original_firmware',
  PURE_FIRMWARE: 'pure_firmware',
  // 未來擴展
  TEMPLATE: 'template',
  DOCUMENT: 'document',
  VIDEO: 'video',
  AUDIO: 'audio'
};
```

## 📞 故障排除

### 常見問題

1. **檔案上傳失敗**
   - 檢查檔案大小限制
   - 驗證檔案類型
   - 確認 GridFS 連接狀態

2. **檔案下載問題**
   - 檢查檔案 ID 是否存在
   - 驗證權限設置
   - 確認網路連接

3. **存儲空間問題**
   - 監控 GridFS 使用量
   - 清理孤立檔案
   - 實施檔案歸檔策略

### 調試命令

```javascript
// 查看 GridFS 檔案列表
db.fs.files.find().limit(10);

// 查看特定類型檔案
db.fs.files.find({ "metadata.type": "original_firmware" });

// 檢查檔案完整性
db.fs.chunks.aggregate([
  { $group: { _id: "$files_id", chunkCount: { $sum: 1 } } }
]);
```

---

**文檔版本**: 1.0.0  
**最後更新**: 2024-12-20  
**維護者**: EPD Manager 開發團隊
