// WebSocket 測試客戶端
const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const { buildWebSocketUrl } = require('../utils/networkUtils');

// 從命令行獲取參數
const gatewayId = process.argv[2] || 'test-gateway';
const storeId = process.argv[3] || 'test-store';

// 生成 JWT token
const generateToken = (gatewayId, storeId) => {
  const jwtSecret = 'your_jwt_secret_here'; // 與服務器相同的密鑰

  const payload = {
    gatewayId: gatewayId,
    storeId: storeId,
    type: 'gateway'
  };

  return jwt.sign(payload, jwtSecret, { expiresIn: '30d' });
};

// 生成 token
const token = generateToken(gatewayId, storeId);
console.log(`已生成 token: ${token}`);

// 構建 WebSocket URL，使用動態 IP 檢測
const wsPath = `/ws/store/${storeId}/gateway/${gatewayId}`;
const wsBaseUrl = buildWebSocketUrl(wsPath, 3001);
const url = `${wsBaseUrl}?token=${token}`;

console.log(`嘗試連接到 WebSocket 服務器: ${url}`);

const ws = new WebSocket(url);

ws.on('open', () => {
  console.log('連接已建立');

  // 發送 ping 消息
  const pingMessage = {
    type: 'ping',
    timestamp: Date.now()
  };

  console.log('發送 ping 消息:', pingMessage);
  ws.send(JSON.stringify(pingMessage));

  // 每 5 秒發送一次設備狀態消息
  setInterval(() => {
    const deviceStatusMessage = {
      type: 'deviceStatus',
      devices: [
        {
          macAddress: '00:11:22:33:44:55',
          status: 'online',
          // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
          data: {
            size: '10.3',
            battery: Math.floor(Math.random() * 100),
            rssi: -1 * Math.floor(Math.random() * 100)
          }
        }
      ]
    };

    console.log('發送設備狀態消息:', deviceStatusMessage);
    ws.send(JSON.stringify(deviceStatusMessage));
  }, 5000);

  // 30 秒後發送網關信息消息
  setTimeout(() => {
    const gatewayInfoMessage = {
      type: 'gatewayInfo',
      info: {
        macAddress: 'AA:BB:CC:DD:EE:FF',
        model: 'Gateway Model 001',
        wifiFirmwareVersion: '1.0.0',
        btFirmwareVersion: '2.0.0',
        ipAddress: '*************'
      }
    };

    console.log('發送網關信息消息:', gatewayInfoMessage);
    ws.send(JSON.stringify(gatewayInfoMessage));
  }, 30000);
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data);
    console.log('收到消息:', message);
  } catch (error) {
    console.error('解析收到的消息時出錯:', error);
  }
});

ws.on('error', (error) => {
  console.error('WebSocket 錯誤:', error);
});

ws.on('close', (code, reason) => {
  console.log(`連接已關閉，代碼: ${code}，原因: ${reason}`);
});

// 處理程序結束時，關閉 WebSocket 連接
process.on('SIGINT', () => {
  console.log('關閉 WebSocket 連接...');
  ws.close();
  process.exit();
});
