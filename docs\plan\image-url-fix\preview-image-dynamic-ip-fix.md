# 預覽圖生成動態IP修復

## 問題描述

在"綁定資料"生成預覽圖時，如果模板中使用了圖片元素，而圖片URL包含固定IP，當服務器IP改變時會導致：

1. **圖片載入失敗**：固定IP的圖片URL在IP變更後無法訪問
2. **預覽圖生成失敗**：圖片載入失敗導致整個預覽圖生成過程失敗
3. **用戶體驗問題**：無法正確預覽包含圖片的模板效果

## 解決方案

### 1. 圖片URL動態處理

在 `PreviewComponent.tsx` 中的圖片渲染邏輯已經使用了 `processImageUrl()` 函數：

```javascript
// 第769行：使用 processImageUrl 處理圖片 URL
const processedUrl = processImageUrl(element.imageUrl);
imgElement.src = processedUrl;
```

這個函數會：
- 檢測文件ID格式並動態組裝完整URL
- 處理相對路徑格式
- 轉換固定IP的URL為動態IP

### 2. 圖片載入等待機制

添加了圖片載入等待邏輯，確保所有圖片載入完成後再進行畫布渲染：

```javascript
// 等待所有圖片載入完成
const imageElements = elementsContainer.querySelectorAll('img');
const imageLoadPromises = Array.from(imageElements).map(img => {
  return new Promise<void>((resolve) => {
    if (img.complete) {
      resolve();
    } else {
      const onLoad = () => {
        img.removeEventListener('load', onLoad);
        img.removeEventListener('error', onError);
        resolve();
      };
      const onError = () => {
        img.removeEventListener('load', onLoad);
        img.removeEventListener('error', onError);
        console.warn('圖片載入失敗，但繼續生成預覽:', img.src);
        resolve(); // 即使載入失敗也繼續
      };
      img.addEventListener('load', onLoad);
      img.addEventListener('error', onError);
    }
  });
});

// 等待所有圖片載入完成（最多等待5秒）
await Promise.race([
  Promise.all(imageLoadPromises),
  new Promise(resolve => setTimeout(resolve, 5000)) // 5秒超時
]);
```

### 3. 錯誤處理和日誌

增強了圖片載入的錯誤處理和日誌記錄：

```javascript
// 添加載入成功和錯誤處理
imgElement.onload = () => {
  console.log('圖片載入成功:', element.imageUrl, '處理後的URL:', processedUrl);
};

imgElement.onerror = () => {
  console.error('圖片載入失敗:', element.imageUrl, '處理後的URL:', processedUrl);
  console.error('請檢查圖片URL是否正確，以及服務器是否可訪問');
};
```

## 修改的文件

### 前端文件

1. **PreviewComponent.tsx**
   - 使用 `processImageUrl()` 處理圖片URL（已存在）
   - 添加圖片載入等待機制
   - 增強錯誤處理和日誌記錄

2. **imageUrlUtils.ts**
   - 提供統一的圖片URL處理邏輯（已存在）
   - 支援文件ID、相對路徑、完整URL的動態轉換

## 工作流程

### 預覽圖生成流程

1. **模板渲染**：根據模板配置創建DOM元素
2. **圖片URL處理**：使用 `processImageUrl()` 動態組裝圖片URL
3. **圖片載入等待**：等待所有圖片載入完成（最多5秒）
4. **文字綁定處理**：處理數據綁定的文字元素
5. **畫布渲染**：將DOM渲染為Canvas
6. **效果應用**：應用顏色效果（黑白、灰階等）
7. **預覽圖生成**：轉換為base64格式的預覽圖

### 圖片URL處理流程

1. **檢測格式**：判斷是文件ID、相對路徑還是完整URL
2. **動態組裝**：使用 `buildEndpointUrl()` 動態組裝完整URL
3. **載入圖片**：設置圖片src並等待載入完成
4. **錯誤處理**：記錄載入失敗的圖片，但不中斷整個流程

## 測試場景

### 1. 包含圖片的模板測試
- ✅ 創建包含圖片元素的模板
- ✅ 在"綁定資料"中選擇該模板
- ✅ 檢查預覽圖是否正確生成
- ✅ 檢查圖片是否正確顯示

### 2. IP變更測試
- ✅ 模擬服務器IP變更
- ✅ 檢查預覽圖生成是否仍然正常
- ✅ 檢查圖片是否能正確載入

### 3. 網路問題測試
- ✅ 模擬網路延遲或圖片載入失敗
- ✅ 檢查是否有適當的超時機制
- ✅ 檢查錯誤處理是否正常

### 4. 混合內容測試
- ✅ 測試包含文字、圖片、QR碼等多種元素的模板
- ✅ 檢查所有元素是否正確渲染到預覽圖中

## 預期效果

### 修復前
- 圖片URL包含固定IP：`http://*************:3001/api/files/xxx`
- IP變更後圖片載入失敗
- 預覽圖生成失敗或圖片顯示空白

### 修復後
- 圖片儲存為文件ID：`64a7b8c9d1e2f3a4b5c6d7e8`
- 動態組裝URL：`http://[當前IP]:3001/api/files/xxx`
- 圖片正確載入，預覽圖正常生成

## 注意事項

1. **載入超時**：設置了5秒的圖片載入超時，避免無限等待
2. **錯誤容忍**：即使部分圖片載入失敗，也會繼續生成預覽圖
3. **向後相容**：支援現有的完整URL格式
4. **性能考慮**：使用Promise.race避免長時間等待

## 相關文件

- `src/components/PreviewComponent.tsx` - 預覽圖生成主邏輯
- `src/utils/imageUrlUtils.ts` - 圖片URL處理工具
- `src/utils/api/apiConfig.ts` - API配置（動態IP檢測）
- `src/components/BindDeviceDataModal.tsx` - 綁定資料界面
