// 設備事件類型接口
export interface DeviceEvent {
  _id: string;
  deviceId: string;
  eventType: 'discovered' | 'user_binding' | 'gateway_changed' | 'data_update' | 'status_change' | string;
  eventData: {
    [key: string]: any;
  };
  timestamp: Date;
  deviceMac: string;
  storeId: string;
}

// 設備事件響應接口
export interface DeviceEventsResponse {
  success: boolean;
  total: number;
  events: DeviceEvent[];
}
