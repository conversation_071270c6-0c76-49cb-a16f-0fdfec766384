import { useState, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { TemplateElement, Template } from '../../types';
import { useTemplateStore } from '../../store';

/**
 * 計算元素旋轉後的實際邊界點
 * @param element 元素對象
 * @returns 元素的實際邊界 {minX, maxX, minY, maxY}
 */
const calculateElementBounds = (element: TemplateElement) => {
  // 處理直線元素的特殊情況
  if (element.type === 'line') {
    // 計算直線的兩個端點
    const x1 = element.x;
    const y1 = element.y;
    const x2 = element.x + element.width;
    const y2 = element.y + element.height;

    // 返回正確的邊界（不論線段方向如何）
    return {
      minX: Math.min(x1, x2),
      maxX: Math.max(x1, x2),
      minY: Math.min(y1, y2),
      maxY: Math.max(y1, y2)
    };
  }

  // 如果元素沒有旋轉，直接返回基本邊界
  if (!element.rotation || element.rotation === 0) {
    // 對於普通元素，計算邊界
    // 處理可能的負值寬度/高度
    const minX = element.width < 0 ? element.x + element.width : element.x;
    const maxX = element.width < 0 ? element.x : element.x + element.width;
    const minY = element.height < 0 ? element.y + element.height : element.y;
    const maxY = element.height < 0 ? element.y : element.y + element.height;

    return {
      minX,
      maxX,
      minY,
      maxY
    };
  }

  // 元素的中心點
  const centerX = Math.round(element.x + element.width / 2);
  const centerY = Math.round(element.y + element.height / 2);

  // 元素的四個角點（相對於中心點）
  const halfWidth = Math.round(Math.abs(element.width) / 2);
  const halfHeight = Math.round(Math.abs(element.height) / 2);

  // 角點的相對座標 (相對於元素中心)
  const corners = [
    { x: -halfWidth, y: -halfHeight }, // 左上
    { x: halfWidth, y: -halfHeight },  // 右上
    { x: halfWidth, y: halfHeight },   // 右下
    { x: -halfWidth, y: halfHeight }   // 左下
  ];

  // 將角點旋轉並轉換為絕對座標
  const rotationRad = (element.rotation * Math.PI) / 180;
  const rotatedCorners = corners.map(corner => {
    // 旋轉公式: x' = x*cos(θ) - y*sin(θ), y' = x*sin(θ) + y*cos(θ)
    const rotatedX = corner.x * Math.cos(rotationRad) - corner.y * Math.sin(rotationRad);
    const rotatedY = corner.x * Math.sin(rotationRad) + corner.y * Math.cos(rotationRad);

    // 轉換為絕對座標
    return {
      x: Math.round(centerX + rotatedX),
      y: Math.round(centerY + rotatedY)
    };
  });

  // 計算旋轉後的最小和最大座標
  const xCoords = rotatedCorners.map(c => c.x);
  const yCoords = rotatedCorners.map(c => c.y);

  return {
    minX: Math.min(...xCoords),
    maxX: Math.max(...xCoords),
    minY: Math.min(...yCoords),
    maxY: Math.max(...yCoords)
  };
};

/**
 * Custom hook to handle editor state and operations
 */
export const useEditorState = (selectedTemplate: Template | null) => {
  const { updateTemplate } = useTemplateStore();
  const [zoom, setZoom] = useState(100); // 修改預設值為 100%
  const [templateName, setTemplateName] = useState(selectedTemplate?.name || '');
  const [expandedSections, setExpandedSections] = useState({
    dynamic: false,
    static: false,
    layer: true
  });
  const [selectedElementId, setSelectedElementId] = useState<string | null>(null);
  // 多選功能 - 新增多選元素的狀態
  const [selectedElementIds, setSelectedElementIds] = useState<string[]>([]);
  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);
  const [selectionBox, setSelectionBox] = useState<{startX: number; startY: number; endX: number; endY: number} | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPoint, setStartPoint] = useState({ x: 0, y: 0 });
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [localElements, setLocalElements] = useState<TemplateElement[]>([]);
  // 添加複製/貼上相關狀態
  const [copiedElements, setCopiedElements] = useState<TemplateElement[]>([]);
  const [elementProperties, setElementProperties] = useState({
    text: 'Text',
    fontSize: 14,
    fontFamily: 'Arial',
    lineWidth: 2,
    lineColor: '#000000'
  });

  // 新增多選移動狀態追蹤
  const [isMultiMoving, setIsMultiMoving] = useState(false);
  // 使用 ref 防止觸發誤選的時間窗口 (300ms)
  const preventSelectTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // 新增狀態用於顯示元素 ID
  const [showElementId, setShowElementId] = useState<string | null>(null);

  // 添加一個函數來切換 ID 的顯示
  const toggleElementIdDisplay = (id: string) => {
    if (showElementId === id) {
      setShowElementId(null); // 如果已經是顯示中的 ID，則關閉顯示
    } else {
      setShowElementId(id); // 否則顯示新點擊的元素 ID
    }
  };

  // Initialize local elements when selected template changes
  useEffect(() => {
    if (selectedTemplate) {
      setTemplateName(selectedTemplate.name);
      setLocalElements(selectedTemplate.elements || []);
      console.log("Template loaded in useEditorState:", {
        id: selectedTemplate.id,
        name: selectedTemplate.name,
        elementsCount: selectedTemplate.elements?.length || 0
      });

      // 檢查模板 ID 是否有效
      if (!selectedTemplate.id) {
        console.warn("警告: 載入的模板沒有 ID!");
      }
    }
  }, [selectedTemplate]);

  const toggleSection = (section: 'dynamic' | 'static' | 'layer') => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // 多選功能 - 根據圈選框選擇元素
  const selectElementsByBox = (box: {startX: number; startY: number; endX: number; endY: number}) => {
    console.log('執行圈選選擇元素:', box);

    const minX = Math.min(box.startX, box.endX);
    const maxX = Math.max(box.startX, box.endX);
    const minY = Math.min(box.startY, box.endY);
    const maxY = Math.max(box.startY, box.endY);

    // 添加更靈活的重疊檢測
    const selectedIds = localElements.filter(element => {
      // 檢查每個元素是否與選擇框重疊，使用新的計算方法考慮旋轉
      const { minX: elementMinX, maxX: elementMaxX, minY: elementMinY, maxY: elementMaxY } = calculateElementBounds(element);

      // 使用重疊檢測算法：如果兩個矩形不重疊的條件取反，就是重疊的條件
      const isOverlapping = !(
        elementMaxX < minX || // 元素在選擇框左側
        elementMinX > maxX || // 元素在選擇框右側
        elementMaxY < minY || // 元素在選擇框上方
        elementMinY > maxY    // 元素在選擇框下方
      );

      console.log(`元素 ${element.id} 是否被選中:`, isOverlapping,
                 '元素位置:', {x: elementMinX, y: elementMinY, maxX: elementMaxX, maxY: elementMaxY},
                 '選擇框:', {minX, minY, maxX, maxY});

      return isOverlapping;
    }).map(element => element.id);

    console.log('被選中的元素:', selectedIds);

    if (selectedIds.length === 0) {
      console.log('沒有元素被選中，不做任何操作');
      return;
    }

    // 在更新狀態前將值保存到變量中，避免閉包陷阱
    const newSelectedIds = [...selectedIds];

    // 保存一個本地副本，在計時器中使用
    const stableIds = [...newSelectedIds];

    // 根據選中元素的數量設置選中的元素 ID
    if (newSelectedIds.length === 1) {
      setSelectedElementId(newSelectedIds[0]);
    } else {
      // 當有多個元素被選中時，清除單選狀態
      setSelectedElementId(null);
    }

    // 使用函數式更新確保使用最新狀態
    setSelectedElementIds(newSelectedIds);

    // 強制更新狀態，確保 UI 反應
    setIsMultiSelectMode(newSelectedIds.length > 1);

    // 添加延遲檢查確保選擇狀態被正確設置
    setTimeout(() => {
      console.log('選擇狀態檢查 - 目標選中ID:', stableIds);
      // 如果選擇狀態與預期不符，嘗試再次設置
      if (stableIds.length > 0) {
        setSelectedElementIds(prev => {
          // 如果已經設置好了，不需要再次更新
          if (prev.length === stableIds.length &&
              stableIds.every(id => prev.includes(id))) {
            console.log('選擇狀態已經正確設置');
            return prev;
          }
          console.log('重新設置選擇狀態:', stableIds);
          return stableIds;
        });
      }
    }, 50);
  };

  // 多選功能 - 添加/移除單個元素到多選集合
  const toggleElementSelection = (id: string) => {
    // 如果目前處於多選移動保護時間窗口，不處理單擊選擇
    if (preventSelectTimeoutRef.current) {
      console.log('處於多選移動保護狀態，忽略選擇操作');
      return;
    }

    setSelectedElementIds(prev => {
      if (prev.includes(id)) {
        const newIds = prev.filter(eId => eId !== id);
        if (newIds.length === 1) {
          setSelectedElementId(newIds[0]);
        }
        return newIds;
      } else {
        return [...prev, id];
      }
    });
  };

  // 多選功能 - 清除所有選擇
  const clearSelection = () => {
    // 如果目前處於多選移動保護時間窗口，不處理清除選擇
    if (preventSelectTimeoutRef.current) {
      console.log('處於多選移動保護狀態，忽略清除選擇操作');
      return;
    }

    setSelectedElementId(null);
    setSelectedElementIds([]);
  };

  // 複製選中的元素 (支持多選)
  const handleCopy = () => {
    console.log('useEditorState: 執行複製操作');
    console.log('useEditorState: 選中元素ID:', selectedElementId);
    console.log('useEditorState: 多選元素IDs:', selectedElementIds);
    console.log('useEditorState: 本地元素數量:', localElements.length);

    // 獲取所有選中的元素
    let elementsToCopy: TemplateElement[] = [];

    // 如果有多選，優先處理多選的元素
    if (selectedElementIds.length > 0) {
      elementsToCopy = localElements.filter(element => selectedElementIds.includes(element.id));
      console.log('useEditorState: 從多選中獲取元素，找到', elementsToCopy.length, '個元素');
    }
    // 否則處理單選的元素
    else if (selectedElementId) {
      const element = localElements.find(el => el.id === selectedElementId);
      if (element) {
        elementsToCopy = [element];
        console.log('useEditorState: 從單選中獲取元素，找到 1 個元素');
      } else {
        console.log('useEditorState: 找不到選中的元素 ID:', selectedElementId);
      }
    }

    if (elementsToCopy.length > 0) {
      // 深度複製元素，避免引用問題
      const deepCopiedElements = JSON.parse(JSON.stringify(elementsToCopy));
      setCopiedElements(deepCopiedElements);
      console.log(`useEditorState: 已複製 ${deepCopiedElements.length} 個元素，元素類型:`, deepCopiedElements.map((el: TemplateElement) => el.type).join(', '));

      // 顯示複製元素的詳細信息
      deepCopiedElements.forEach((el: TemplateElement, index: number) => {
        console.log(`useEditorState: 複製元素 #${index + 1}:`, {
          id: el.id,
          type: el.type,
          x: el.x,
          y: el.y,
          width: el.width,
          height: el.height
        });
      });
    } else {
      console.log('useEditorState: 沒有選中元素，無法複製');
    }
  };

  // 貼上複製的元素
  const handlePaste = () => {
    console.log('useEditorState: 執行貼上操作');
    console.log('useEditorState: 已複製元素數量:', copiedElements.length);

    if (copiedElements.length === 0) {
      console.log('useEditorState: 沒有已複製的元素，無法執行貼上操作');
      return;
    }

    // 新選中的元素ID列表
    const newSelectedIds: string[] = [];

    // 創建新元素，生成新ID並添加小偏移量
    const newElements = copiedElements.map(element => {
      const newId = uuidv4();
      const newElement = {
        ...element,
        id: newId,
        x: element.x + 10, // 添加偏移，讓用戶能看到新貼上的元素
        y: element.y + 10,
        // 如果當前模板有門店ID，則使用當前模板的門店ID覆蓋原有的templateStoreId
        templateStoreId: selectedTemplate?.storeId || element.templateStoreId
      };
      newSelectedIds.push(newId);
      return newElement;
    });

    console.log(`useEditorState: 創建了 ${newElements.length} 個新元素，準備添加到畫布上`);

    // 顯示新元素的詳細信息
    newElements.forEach((el: TemplateElement, index: number) => {
      console.log(`useEditorState: 新元素 #${index + 1}:`, {
        id: el.id,
        type: el.type,
        x: el.x,
        y: el.y,
        width: el.width,
        height: el.height
      });
    });

    // 將新元素添加到畫布上
    const updatedElements = [...localElements, ...newElements];
    setLocalElements(updatedElements);
    console.log(`useEditorState: 更新後的元素總數: ${updatedElements.length}`);

    // 選中新貼上的元素
    setSelectedElementIds(newSelectedIds);
    if (newSelectedIds.length === 1) {
      setSelectedElementId(newSelectedIds[0]);
      console.log(`useEditorState: 選中單個新元素 ID: ${newSelectedIds[0]}`);
    } else {
      setSelectedElementId(null);
      console.log(`useEditorState: 選中多個新元素，清除單選狀態`);
    }

    // 確保更新全局模板狀態
    if (selectedTemplate) {
      try {
        const updatedTemplate = {
          ...selectedTemplate,
          elements: updatedElements
        };
        console.log('useEditorState: 準備更新全局模板狀態，模板 ID:', updatedTemplate.id);
        updateTemplate(updatedTemplate);
        console.log('useEditorState: 已更新全局模板狀態');
      } catch (error) {
        console.error('useEditorState: 更新全局模板狀態失敗:', error);
      }
    } else {
      console.error('useEditorState: 沒有選中的模板，無法更新全局狀態');
    }

    console.log(`useEditorState: 已貼上 ${newElements.length} 個元素`);
  };

  // 多選功能 - 對齊選中元素
  const alignSelectedElements = (alignType: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom') => {
    if (selectedElementIds.length <= 1) return;

    const selectedElements = localElements.filter(el => selectedElementIds.includes(el.id));

    // 計算邊界值 - 使用更精確的邊界計算函數
    let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
    let sumCenterX = 0, sumCenterY = 0;

    selectedElements.forEach(el => {
      // 獲取元素的真實邊界（考慮旋轉）
      const bounds = calculateElementBounds(el);

      minX = Math.min(minX, bounds.minX);
      maxX = Math.max(maxX, bounds.maxX);
      minY = Math.min(minY, bounds.minY);
      maxY = Math.max(maxY, bounds.maxY);

      // 計算元素中心點用於居中對齊
      const centerX = Math.round((bounds.minX + bounds.maxX) / 2);
      const centerY = Math.round((bounds.minY + bounds.maxY) / 2);
      sumCenterX += centerX;
      sumCenterY += centerY;
    });

    const centerX = Math.round(sumCenterX / selectedElements.length);
    const centerY = Math.round(sumCenterY / selectedElements.length);

    const updatedElements = localElements.map(element => {
      if (!selectedElementIds.includes(element.id)) return element;

      const updates: Partial<TemplateElement> = {};
      const elementWidth = element.width;
      const elementHeight = element.height;

      switch (alignType) {
        case 'left':
          updates.x = minX;
          break;
        case 'center':
          updates.x = Math.round(centerX - elementWidth / 2);
          break;
        case 'right':
          updates.x = maxX - elementWidth;
          break;
        case 'top':
          updates.y = minY;
          break;
        case 'middle':
          updates.y = Math.round(centerY - elementHeight / 2);
          break;
        case 'bottom':
          updates.y = maxY - elementHeight;
          break;
      }

      return { ...element, ...updates };
    });

    setLocalElements(updatedElements);

    // 更新全局模板狀態
    if (selectedTemplate) {
      updateTemplate({
        ...selectedTemplate,
        elements: updatedElements
      });
    }
  };

  // 多選功能 - 分配選中元素
  const distributeSelectedElements = (distributeType: 'horizontal' | 'vertical') => {
    if (selectedElementIds.length <= 2) return;

    const selectedElements = localElements.filter(el => selectedElementIds.includes(el.id))
      .sort((a, b) => {
        if (distributeType === 'horizontal') {
          return a.x - b.x;
        } else {
          return a.y - b.y;
        }
      });

    // 計算總空間和間距
    let totalSpace, totalGap;
    if (distributeType === 'horizontal') {
      const firstElement = selectedElements[0];
      const lastElement = selectedElements[selectedElements.length - 1];
      totalSpace = Math.round((lastElement.x + lastElement.width) - firstElement.x);
      totalGap = Math.round(totalSpace - selectedElements.reduce((sum, el) => sum + el.width, 0));
    } else {
      const firstElement = selectedElements[0];
      const lastElement = selectedElements[selectedElements.length - 1];
      totalSpace = Math.round((lastElement.y + lastElement.height) - firstElement.y);
      totalGap = Math.round(totalSpace - selectedElements.reduce((sum, el) => sum + el.height, 0));
    }

    const gapSize = Math.round(totalGap / (selectedElements.length - 1));

    const updatedElements = localElements.map(element => {
      const index = selectedElements.findIndex(sel => sel.id === element.id);
      if (index === -1 || index === 0 || index === selectedElements.length - 1) {
        return element;
      }

      const updates: Partial<TemplateElement> = {};

      if (distributeType === 'horizontal') {
        let expectedPosition = selectedElements[0].x;
        for (let i = 0; i < index; i++) {
          expectedPosition = Math.round(expectedPosition + selectedElements[i].width + gapSize);
        }
        updates.x = expectedPosition;
      } else {
        let expectedPosition = selectedElements[0].y;
        for (let i = 0; i < index; i++) {
          expectedPosition = Math.round(expectedPosition + selectedElements[i].height + gapSize);
        }
        updates.y = expectedPosition;
      }

      return { ...element, ...updates };
    });

    setLocalElements(updatedElements);

    // 更新全局模板狀態
    if (selectedTemplate) {
      updateTemplate({
        ...selectedTemplate,
        elements: updatedElements
      });
    }
  };

  // Function to add a new element to the template
  const addElement = (
    elementType: TemplateElement['type'],
    x: number,
    y: number,
    width: number,
    height: number,
    extraProperties?: Record<string, any> | string
  ) => {
    console.log('addElement 被調用:', { elementType, x, y, width, height, extraProperties });

    if (!selectedTemplate) {
      console.error('addElement - 沒有選中的模板!');
      return;
    }

    // 處理舊版的 API，其中 extraProperties 可能是字串（content）
    const content = typeof extraProperties === 'string' ? extraProperties : undefined;

    const newElement: TemplateElement = {
      id: uuidv4(),
      type: elementType,
      x,
      y,
      width,
      height,
      ...(content && { content }),
      // 如果 extraProperties 是物件，添加其所有屬性到新元素
      ...(typeof extraProperties === 'object' && extraProperties),
      // 添加模板的門店ID到元素中，以便在TextProperties中使用
      templateStoreId: selectedTemplate.storeId
    };

    // 根據元素類型添加特定屬性
    if (elementType === 'text' || elementType === 'multiline-text') {
      newElement.fontSize = elementProperties.fontSize;
      newElement.fontFamily = elementProperties.fontFamily;
      newElement.content = newElement.content || elementProperties.text;
      // 為文字元素添加線條寬度和顏色屬性，與圖標元素一致
      newElement.lineWidth = elementProperties.lineWidth || 1;
      newElement.lineColor = elementProperties.lineColor || '#000000';
      console.log('addElement - 設置文字特有屬性:', {
        content: newElement.content,
        fontSize: newElement.fontSize,
        fontFamily: newElement.fontFamily,
        lineWidth: newElement.lineWidth,
        lineColor: newElement.lineColor
      });

      // 自動計算文字寬度
      const calculateTextWidth = (text: string, fontSize: number, fontFamily: string): number => {
        // 創建臨時 canvas 元素來測量文字寬度
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        if (context) {
          context.font = `${fontSize}px ${fontFamily}`;
          const metrics = context.measureText(text);
          // 添加額外的寬度以確保文字能完全顯示（增加一些邊距）
          return Math.round(metrics.width + 20); // 添加左右各 10px 的內邊距
        }
        return 100; // 默認寬度
      };

      // 使用計算的寬度更新元素寬度
      if (newElement.content) {
        newElement.width = Math.max(
          calculateTextWidth(newElement.content, newElement.fontSize, newElement.fontFamily),
          newElement.width
        );
      }
    } else if (elementType === 'line') {
      // 為線段添加特定屬性
      newElement.lineWidth = elementProperties.lineWidth;
      newElement.lineColor = elementProperties.lineColor;
      console.log('addElement - 設置線段特有屬性:', {
        lineWidth: newElement.lineWidth,
        lineColor: newElement.lineColor
      });
    } else if (elementType === 'icon') {
      // 為圖標元素添加特定屬性
      newElement.iconType = 'star'; // 設置預設圖標類型為 star
      newElement.lineWidth = elementProperties.lineWidth || 2;
      newElement.lineColor = elementProperties.lineColor || '#000000';
      console.log('addElement - 設置圖標特有屬性:', {
        iconType: newElement.iconType,
        lineWidth: newElement.lineWidth,
        lineColor: newElement.lineColor
      });
    } else if (elementType === 'qr-code') {
      // 為 QR Code 元素添加特定屬性
      newElement.qrCodeType = 'qrcode';
      newElement.errorCorrectionLevel = 'M';
      newElement.quietZone = 4;
      newElement.moduleSize = 3;
      newElement.codeContent = 'QR Code 內容';
      newElement.lineColor = '#000000';
      newElement.fillColor = '#FFFFFF';
      newElement.showBorder = true; // 默認顯示邊框
      newElement.lineWidth = 2; // 默認邊框寬度
      newElement.borderColor = '#000000'; // 默認邊框顏色
      console.log('addElement - 設置 QR Code 特有屬性:', {
        qrCodeType: newElement.qrCodeType,
        errorCorrectionLevel: newElement.errorCorrectionLevel,
        codeContent: newElement.codeContent,
        showBorder: newElement.showBorder
      });
    } else if (elementType === 'barcode') {
      // 為條碼元素添加特定屬性
      newElement.barcodeType = 'code128';
      newElement.quietZone = 10;
      newElement.codeContent = 'Sample123';
      newElement.lineColor = '#000000';
      newElement.fillColor = '#FFFFFF';
      newElement.showBorder = true; // 默認顯示邊框
      newElement.lineWidth = 2; // 默認邊框寬度
      newElement.borderColor = '#000000'; // 默認邊框顏色
      console.log('addElement - 設置條碼特有屬性:', {
        barcodeType: newElement.barcodeType,
        codeContent: newElement.codeContent,
        showBorder: newElement.showBorder
      });
    }

    console.log('addElement - 創建新元素:', newElement);

    // Update local elements state to ensure immediate UI update
    const updatedElements = [...localElements, newElement];
    console.log('addElement - 更新前元素數量:', localElements.length, '更新後:', updatedElements.length);
    setLocalElements(updatedElements);

    // Create a new template object to trigger state update
    const updatedTemplate: Template = {
      ...selectedTemplate,
      elements: updatedElements
    };

    // Update global template state
    console.log('addElement - 更新全局模板');
    updateTemplate(updatedTemplate);

    setSelectedElementId(newElement.id);
    setSelectedElementIds([newElement.id]);
    console.log('addElement - 元素添加完成, ID:', newElement.id);

    // QR Code 和 Barcode 元件現在會自動在渲染器中生成，不需要預覽圖片
  };

  // 多選移動 - 更新多個元素位置
  const moveSelectedElements = (dx: number, dy: number) => {
    if (selectedElementIds.length === 0) return;

    // 標記開始多選移動
    setIsMultiMoving(true);

    // 獲取畫布的實際寬度和高度
    const canvasElement = document.querySelector('[data-canvas-width][data-canvas-height]');
    let canvasWidth = 250; // 預設值
    let canvasHeight = 122; // 預設值

    if (canvasElement) {
      // 從畫布元素的 data 屬性獲取實際尺寸
      canvasWidth = parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10);
      canvasHeight = parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10);
    }

    // 計算所有選中元素的邊界 - 使用更精確的邊界計算函數
    const selectedElements = localElements.filter(el => selectedElementIds.includes(el.id));

    // 初始化邊界值
    let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;

    // 計算每個元素的邊界，並找出整體邊界
    selectedElements.forEach(el => {
      const bounds = calculateElementBounds(el);
      minX = Math.min(minX, bounds.minX);
      maxX = Math.max(maxX, bounds.maxX);
      minY = Math.min(minY, bounds.minY);
      maxY = Math.max(maxY, bounds.maxY);
    });

    // 計算移動後的邊界
    const newMinX = Math.round(minX + dx);
    const newMaxX = Math.round(maxX + dx);
    const newMinY = Math.round(minY + dy);
    const newMaxY = Math.round(maxY + dy);

    // 調整移動量，確保不會超出畫布
    let adjustedDx = dx;
    let adjustedDy = dy;

    // 檢查左邊界
    if (newMinX < 0) {
      adjustedDx = -minX; // 調整為剛好貼合左邊界
    }

    // 檢查右邊界
    if (newMaxX > canvasWidth) {
      adjustedDx = canvasWidth - maxX; // 調整為剛好貼合右邊界
    }

    // 檢查上邊界
    if (newMinY < 0) {
      adjustedDy = -minY; // 調整為剛好貼合上邊界
    }

    // 檢查下邊界
    if (newMaxY > canvasHeight) {
      adjustedDy = canvasHeight - maxY; // 調整為剛好貼合下邊界
    }

    // 使用調整後的移動量更新元素
    const updatedElements = localElements.map(element => {
      if (!selectedElementIds.includes(element.id)) return element;

      return {
        ...element,
        x: Math.round(element.x + adjustedDx),
        y: Math.round(element.y + adjustedDy)
      };
    });

    setLocalElements(updatedElements);

    // 更新全局模板狀態
    if (selectedTemplate) {
      updateTemplate({
        ...selectedTemplate,
        elements: updatedElements
      });
    }

    // 清除先前的計時器（如果存在）
    if (preventSelectTimeoutRef.current) {
      clearTimeout(preventSelectTimeoutRef.current);
    }

    // 設置計時器，在多選移動結束後300毫秒內防止觸發單選
    preventSelectTimeoutRef.current = setTimeout(() => {
      setIsMultiMoving(false);
      preventSelectTimeoutRef.current = null;
      console.log('多選移動保護時間結束');
    }, 300);
  };
  return {
    zoom,
    setZoom,
    templateName,
    setTemplateName,
    expandedSections,
    toggleSection,
    selectedElementId,
    setSelectedElementId,
    selectedElementIds,
    setSelectedElementIds,
    isMultiSelectMode,
    setIsMultiSelectMode,
    selectionBox,
    setSelectionBox,
    selectElementsByBox,
    toggleElementSelection,
    clearSelection,
    alignSelectedElements,
    distributeSelectedElements,
    moveSelectedElements,
    isDrawing,
    setIsDrawing,
    startPoint,
    setStartPoint,
    selectedTool,
    setSelectedTool,
    localElements,
    setLocalElements,
    elementProperties,
    setElementProperties,
    addElement,
    isMultiMoving, // 導出多選移動狀態
    handleCopy,     // 導出複製函數
    handlePaste,     // 導出貼上函數
    showElementId,  // 導出顯示元素 ID 的狀態
    setShowElementId, // 導出設置顯示元素 ID 的函數
    toggleElementIdDisplay // 導出切換顯示元素 ID 的函數
  };
};