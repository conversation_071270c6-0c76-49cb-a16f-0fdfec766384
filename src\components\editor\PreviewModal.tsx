import React, { useState, useEffect, useRef } from 'react';
import { X, Settings } from 'lucide-react';
import { applyImageEffect, EffectType, processDataFieldBindings, processTextBindings, restoreTextBindings } from '../../utils/previewUtils';
import { convertImageByColorType } from '../../utils/colorConversion';
import { getAllDataFields } from '../../utils/api/dataFieldApi';
import { DataField, TemplateElement, Template } from '../../types';
import ColorTypeGradient from '../ui/ColorTypeGradient';

interface PreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  canvasData: HTMLCanvasElement | null;
  width: number;
  height: number;
  elements?: TemplateElement[]; // 添加元素陣列，以便處理資料欄位綁定
  template?: Template; // 添加 template 信息
}

export const PreviewModal: React.FC<PreviewModalProps> = ({
  isOpen,
  onClose,
  canvasData,
  width,
  height,
  elements = [],
  template
}) => {
  const [previewCanvas, setPreviewCanvas] = useState<HTMLCanvasElement | null>(null);
  const [isTestMode, setIsTestMode] = useState(false); // 新增：是否為測試模式
  const [selectedEffect, setSelectedEffect] = useState<EffectType>('blackAndWhite'); // 預設黑白效果
  const [threshold, setThreshold] = useState(128);
  const previewContainerRef = useRef<HTMLDivElement>(null);

  // 新增資料欄位狀態
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [isLoadingDataFields, setIsLoadingDataFields] = useState(false);
  const [sampleData, setSampleData] = useState<Record<string, any>>({
    // 預設的範例資料，實際應用會從 API 獲取
    "商品名稱": "範例產品A",
    "價格": 199,
    "庫存": 42,
    "條碼": "0123456789"
  });
    // 當模態框打開時加載資料欄位定義
  useEffect(() => {
    if (!isOpen) return;

    const fetchDataFields = async () => {
      setIsLoadingDataFields(true);
      try {
        const fields = await getAllDataFields();
        setDataFields(fields);
      } catch (error) {
        console.error('載入資料欄位失敗:', error);
      } finally {
        setIsLoadingDataFields(false);
      }
    };

    fetchDataFields();
  }, [isOpen]);
    // 當 canvasData 或相關狀態改變時，處理預覽
  useEffect(() => {
    if (!canvasData || !isOpen) return;

    // 處理資料欄位綁定的預覽
    if (elements && elements.length > 0 && dataFields.length > 0) {
      try {
        // 處理資料欄位綁定，替換為實際數據
        const processedElements = processDataFieldBindings(elements, dataFields, sampleData);

        // 記錄綁定數據的元素數量
        const boundElements = processedElements.filter(el =>
          (el.type === 'text' || el.type === 'multiline-text') &&
          (el.dataBinding?.fieldId || el.dataFieldId)
        );

        console.log('處理後的元素總數:', processedElements.length);
        console.log('綁定數據的元素數量:', boundElements.length);

        // 這裡需要將處理後的元素重新渲染到畫布
        // 但目前預覽使用的是整體的畫布數據而不是單獨的元素
        // 所以我們可能需要在後續優化這部分
      } catch (error) {
        console.error('處理資料欄位綁定時出錯:', error);
      }
    }

    let processedCanvas: HTMLCanvasElement;

    if (isTestMode) {
      // 測試模式：使用選擇的效果
      processedCanvas = applyImageEffect(canvasData, selectedEffect, threshold);
    } else {
      // 正常模式：使用模板的 colorType
      if (template?.color) {
        console.log(`使用模板顏色類型進行轉換: ${template.color}`);
        const convertedCanvas = convertImageByColorType(canvasData, template.color, { threshold });
        processedCanvas = convertedCanvas || canvasData;
      } else {
        // 如果沒有 colorType，使用默認的黑白效果
        processedCanvas = applyImageEffect(canvasData, 'blackAndWhite', threshold);
      }
    }

    setPreviewCanvas(processedCanvas);
  }, [canvasData, selectedEffect, threshold, isOpen, dataFields, elements, sampleData, isTestMode, template]);

  // 若沒有開啟或沒有畫布數據，不顯示
  if (!isOpen || !canvasData) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-gray-800 rounded-lg shadow-xl w-4/5 max-w-4xl h-4/5 flex flex-col">
        {/* 標題欄 */}
        <div className="flex justify-between items-center p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-white">預覽模板</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X size={20} />
          </button>
        </div>

        {/* 內容區域 */}
        <div className="flex flex-1 overflow-hidden">
          {/* 左側：顯示預覽 */}
          <div className="w-3/4 p-4 flex flex-col">            <div className="bg-gray-900 rounded-md flex-1 flex items-center justify-center overflow-auto" ref={previewContainerRef}>
              {previewCanvas && (
                <div className="w-full h-full flex items-center justify-center" style={{ padding: '20px' }}>
                    <div className="flex items-center justify-center" style={{
                    width: '80%',
                    height: '80%',
                    backgroundColor: 'var(--tw-bg-opacity-gray-900)',
                    border: '0px solid #e1e1e1',
                    position: 'relative'
                    }}>
                    <img
                      src={previewCanvas.toDataURL()}
                      alt="預覽"
                      style={{
                      maxWidth: '95%',
                      maxHeight: '95%',
                      objectFit: 'contain'
                      }}
                    />
                    <div className="absolute bottom-0 right-2 text-xs text-gray-500 bg-gray-200 px-1 py-0.5 rounded">
                      {width}×{height}px
                    </div>
                    </div>
                </div>
              )}
            </div>
          </div>

          {/* 右側：控制面板 */}
          <div className="w-1/4 border-l border-gray-700 p-4 overflow-y-auto">
            <div className="space-y-4">
              {/* 模式切換 */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">預覽模式</h3>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="mode"
                      checked={!isTestMode}
                      onChange={() => setIsTestMode(false)}
                      className="mr-2"
                    />
                    <div className="flex items-center gap-2">
                      <span className="text-gray-200">模板預覽</span>
                      {template?.color && (
                        <div className="w-16 h-3 rounded-sm overflow-hidden">
                          <ColorTypeGradient colorType={template.color} size="sm" />
                        </div>
                      )}
                    </div>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="mode"
                      checked={isTestMode}
                      onChange={() => setIsTestMode(true)}
                      className="mr-2"
                    />
                    <span className="text-gray-200 flex items-center">
                      <Settings size={16} className="mr-1" />
                      測試工具
                    </span>
                  </label>
                </div>
              </div>

              {/* 測試工具選項 - 只在測試模式下顯示 */}
              {isTestMode && (
                <>
                  <div className="border-t border-gray-600 pt-4">
                    <h4 className="text-md font-medium text-white mb-3">效果設定</h4>

                    {/* 效果選擇 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        效果選擇
                      </label>
                      <div className="space-y-2">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="effect"
                            value="original"
                            checked={selectedEffect === 'original'}
                            onChange={() => setSelectedEffect('original')}
                            className="mr-2"
                          />
                          <span className="text-gray-200">原始圖像</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="effect"
                            value="blackAndWhite"
                            checked={selectedEffect === 'blackAndWhite'}
                            onChange={() => setSelectedEffect('blackAndWhite')}
                            className="mr-2"
                          />
                          <span className="text-gray-200">黑白二值化</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="effect"
                            value="grayscale"
                            checked={selectedEffect === 'grayscale'}
                            onChange={() => setSelectedEffect('grayscale')}
                            className="mr-2"
                          />
                          <span className="text-gray-200">灰階</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="effect"
                            value="inverted"
                            checked={selectedEffect === 'inverted'}
                            onChange={() => setSelectedEffect('inverted')}
                            className="mr-2"
                          />
                          <span className="text-gray-200">反轉顏色</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="effect"
                            value="dithering"
                            checked={selectedEffect === 'dithering'}
                            onChange={() => setSelectedEffect('dithering')}
                            className="mr-2"
                          />
                          <span className="text-gray-200">抖動效果</span>
                        </label>
                      </div>
                    </div>

                    {/* 閾值調整 - 只在黑白模式下顯示 */}
                    {selectedEffect === 'blackAndWhite' && (
                      <div className="mt-4">
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          黑白閾值: {threshold}
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="255"
                          value={threshold}
                          onChange={(e) => setThreshold(parseInt(e.target.value))}
                          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="flex justify-between mt-1 text-xs text-gray-400">
                          <span>0</span>
                          <span>128</span>
                          <span>255</span>
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}

              {/* 下載按鈕 */}
              <div className="pt-4 border-t border-gray-600">
                <button
                  onClick={() => {
                    if (previewCanvas) {
                      const link = document.createElement('a');
                      link.download = 'preview.png';
                      link.href = previewCanvas.toDataURL('image/png');
                      link.click();
                    }
                  }}
                  className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
                >
                  下載預覽圖
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};