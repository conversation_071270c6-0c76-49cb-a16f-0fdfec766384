// 為系統專屬數據添加測試數據
import fetch from 'node-fetch';

// 配置
const API_BASE_URL = 'http://localhost:3001/api';
let TOKEN = '';

// 通用請求函數
async function makeRequest(endpoint, method = 'GET', data = null) {
  const url = `${API_BASE_URL}/${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': `token=${TOKEN}`
    },
    credentials: 'include'
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    
    // 檢查 token 是否在 cookie 中
    const setCookieHeader = response.headers.get('set-cookie');
    if (setCookieHeader && setCookieHeader.includes('token=')) {
      const tokenMatch = setCookieHeader.match(/token=([^;]+)/);
      if (tokenMatch && tokenMatch[1]) {
        TOKEN = tokenMatch[1];
        console.log('Token updated from cookie');
      }
    }

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    // 檢查是否有響應內容
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    } else if (response.status === 204) {
      return { success: true, message: 'No content' };
    } else {
      return { success: true, message: await response.text() };
    }
  } catch (error) {
    console.error(`API request error (${method} ${endpoint}):`, error.message);
    throw error;
  }
}

// 登入函數
async function login(username, password) {
  try {
    console.log(`Logging in as ${username}...`);
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password }),
      credentials: 'include'
    });

    // 檢查響應狀態
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Login failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    // 從 cookie 中獲取 token
    const setCookieHeader = response.headers.get('set-cookie');
    if (setCookieHeader && setCookieHeader.includes('token=')) {
      const tokenMatch = setCookieHeader.match(/token=([^;]+)/);
      if (tokenMatch && tokenMatch[1]) {
        TOKEN = tokenMatch[1];
        console.log('Token obtained from cookie');
        return true;
      }
    }

    // 如果 headers 中沒有 token，嘗試從 response body 獲取
    const data = await response.json();
    if (data.token) {
      TOKEN = data.token;
      console.log('Login successful, token obtained from response body');
      return true;
    }

    throw new Error('Failed to obtain token from response');
  } catch (error) {
    console.error('Login error:', error.message);
    return false;
  }
}

// 創建系統專屬數據
async function createSystemSpecificData(data) {
  try {
    console.log(`Creating system specific data: ${data.id}...`);
    const result = await makeRequest('systemSpecificData', 'POST', data);
    console.log(`System specific data created successfully: ${data.id}`);
    return result;
  } catch (error) {
    console.error(`Failed to create system specific data ${data.id}:`, error.message);
    return null;
  }
}

// 檢查系統專屬數據是否已存在
async function checkSystemSpecificDataExists(id) {
  try {
    const systemSpecificData = await makeRequest('systemSpecificData');
    return systemSpecificData.some(item => item.id === id);
  } catch (error) {
    console.error('Error checking system specific data:', error.message);
    return false;
  }
}

// 生成系統專屬數據
async function generateSystemSpecificData() {
  try {
    // 登入
    const loginSuccess = await login('root', '123456789');
    if (!loginSuccess) {
      throw new Error('Login failed, cannot proceed');
    }

    // 檢查系統專屬數據是否已存在
    const systemSpecificData = await makeRequest('systemSpecificData');
    if (systemSpecificData.length > 0) {
      console.log(`Found ${systemSpecificData.length} existing system specific data items.`);
      console.log('Skipping creation of duplicate items...');
    }

    // 準備10筆系統專屬數據
    const systemSpecificDataItems = [
      {
        id: 'SYS001',
        name: '系統商品A',
        description: '系統專屬的第一筆商品數據',
        price: '1500',
        quantity: '100',
        date: '2023-06-01'
      },
      {
        id: 'SYS002',
        name: '系統商品B',
        description: '系統專屬的第二筆商品數據',
        price: '2000',
        quantity: '80',
        date: '2023-06-02'
      },
      {
        id: 'SYS003',
        name: '系統商品C',
        description: '系統專屬的第三筆商品數據',
        price: '1800',
        quantity: '120',
        date: '2023-06-03'
      },
      {
        id: 'SYS004',
        name: '系統商品D',
        description: '系統專屬的第四筆商品數據',
        price: '2200',
        quantity: '60',
        date: '2023-06-04'
      },
      {
        id: 'SYS005',
        name: '系統商品E',
        description: '系統專屬的第五筆商品數據',
        price: '1600',
        quantity: '90',
        date: '2023-06-05'
      },
      {
        id: 'SYS006',
        name: '系統商品F',
        description: '系統專屬的第六筆商品數據',
        price: '2500',
        quantity: '70',
        date: '2023-06-06'
      },
      {
        id: 'SYS007',
        name: '系統商品G',
        description: '系統專屬的第七筆商品數據',
        price: '1900',
        quantity: '110',
        date: '2023-06-07'
      },
      {
        id: 'SYS008',
        name: '系統商品H',
        description: '系統專屬的第八筆商品數據',
        price: '2100',
        quantity: '85',
        date: '2023-06-08'
      },
      {
        id: 'SYS009',
        name: '系統商品I',
        description: '系統專屬的第九筆商品數據',
        price: '1700',
        quantity: '95',
        date: '2023-06-09'
      },
      {
        id: 'SYS010',
        name: '系統商品J',
        description: '系統專屬的第十筆商品數據',
        price: '2300',
        quantity: '75',
        date: '2023-06-10'
      }
    ];

    // 創建系統專屬數據
    console.log('\n=== Creating System Specific Data ===');
    let createdCount = 0;
    let skippedCount = 0;

    for (const item of systemSpecificDataItems) {
      // 檢查是否已存在
      const exists = systemSpecificData.some(existingItem => existingItem.id === item.id);
      if (exists) {
        console.log(`System specific data with ID ${item.id} already exists, skipping...`);
        skippedCount++;
        continue;
      }

      // 創建系統專屬數據
      await createSystemSpecificData(item);
      createdCount++;
    }

    console.log(`\nSystem specific data generation completed!`);
    console.log(`Created: ${createdCount} items`);
    console.log(`Skipped: ${skippedCount} items (already exist)`);
  } catch (error) {
    console.error('Error generating system specific data:', error);
  }
}

// 執行
console.log('Starting script...');
generateSystemSpecificData().catch(error => {
  console.error('Unhandled error in generateSystemSpecificData:', error);
});

export {};
