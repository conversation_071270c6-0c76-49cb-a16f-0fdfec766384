# 批量發送進度卡住問題修復報告

## 問題描述

用戶反映批量發送進度視窗現在變成畫面卡住，但批量數量顯示是對的，進度事件一直重複接收。

## 問題分析

從日誌分析發現以下問題：

### 1. 重複的進度事件
```
收到批量傳送進度事件: {type: 'batch_progress', batchId: 'batch_1749719065827_t45oolm78', totalDevices: 1, completedDevices: 0, failedDevices: 0, …}
websocketClient.ts:193 收到未處理的WebSocket消息: {type: 'batch_progress', batchId: 'batch_1749719065827_t45oolm78', totalDevices: 1, completedDevices: 0, failedDevices: 0, …}
```

### 2. 重複的訂閱/取消訂閱
```
BatchSendProgress.tsx:155 🔗 BatchSendProgress: 取消訂閱批量傳送進度 batch_1749719065827_t45oolm78
BatchSendProgress.tsx:99 🔗 BatchSendProgress: 訂閱批量傳送進度 batch_1749719065827_t45oolm78
```

### 3. 根本原因
1. **WebSocket 消息重複處理**：`batch_progress` 事件被處理兩次
2. **重複的事件監聽器**：DevicesPage 和 BatchSendProgress 都在監聽同一個事件
3. **useEffect 依賴問題**：導致重複訂閱/取消訂閱
4. **後端等待循環**：在沒有網關或網關忙碌時進入等待循環，重複發送進度更新

## 修復內容

### 1. WebSocket 客戶端修復

**文件**: `src/utils/websocketClient.ts`

**問題**: `batch_progress` 事件被處理兩次
- 第一次：在 `handleMessage` 開頭正確處理
- 第二次：在 switch 語句中被歸類為"未處理消息"

**修復**: 統一消息處理邏輯
```typescript
private handleMessage(data: any) {
  // 處理不同類型的消息
  switch (data.type) {
    case 'batch_progress':
    case 'batch_complete':
      // 處理批量傳送進度事件
      this.notifyHandlers(data as WebSocketEvent);
      break;
    // ... 其他消息類型
  }
}
```

### 2. 移除重複的事件監聽器

**文件**: `src/components/DevicesPage.tsx`

**問題**: DevicesPage 和 BatchSendProgress 都在監聽同一個 batchId 的進度事件

**修復**: 移除 DevicesPage 中不必要的進度事件監聽
```typescript
// 移除了這部分代碼
// const unsubscribe = subscribeToBatchProgress(batchId, (event: WebSocketEvent) => {
//   console.log('收到批量傳送進度事件:', event);
// });
```

### 3. 修復 useEffect 依賴

**文件**: `src/components/BatchSendProgress.tsx`

**問題**: useEffect 依賴包含 `onClose`，導致每次父組件重新渲染時都重新訂閱

**修復**: 移除不必要的依賴
```typescript
useEffect(() => {
  // ... 訂閱邏輯
}, [isVisible, batchId]); // 移除 onClose 依賴
```

### 4. 後端進度更新優化

**文件**: `server/services/sendPreviewToGateway.js`

**問題**: 在等待循環中每次都發送進度更新，導致前端收到大量重複事件

**修復**: 限制進度更新頻率
```javascript
// 只在特定循環次數時廣播等待狀態的進度更新，避免過度頻繁
if (batchId && (queueCycles === 1 || queueCycles % 5 === 0)) {
  websocketService.broadcastBatchProgress(batchId, {
    // ... 進度數據
    currentDevice: {
      id: 'waiting',
      name: `等待網關可用... (循環 ${queueCycles})`
    }
  });
}
```

### 5. 增強調試信息

添加了詳細的網關狀態調試信息，幫助診斷問題：
```javascript
// 詳細列出連接的網關
if (connectedGateways.size > 0) {
  console.log(`📋 已連接的網關列表:`);
  for (const [gatewayId] of connectedGateways) {
    const isBusy = websocketService.isGatewayBusyWithChunk(gatewayId);
    console.log(`  - 網關 ${gatewayId}: ${isBusy ? '忙碌' : '空閒'}`);
  }
}
```

## 修復驗證

### 修復前的問題
1. ❌ 進度事件重複處理，導致前端收到大量重複消息
2. ❌ 多個組件監聽同一個事件，造成資源浪費
3. ❌ useEffect 重複執行，導致訂閱/取消訂閱循環
4. ❌ 後端等待循環中過度頻繁的進度更新

### 修復後的改進
1. ✅ 統一的 WebSocket 消息處理邏輯
2. ✅ 移除重複的事件監聽器
3. ✅ 修復 useEffect 依賴問題
4. ✅ 限制後端進度更新頻率
5. ✅ 增強調試信息，便於問題診斷

## 臨時解決方案

如果問題仍然存在，可以嘗試以下臨時解決方案：

### 1. 刷新頁面
最簡單的方法是刷新瀏覽器頁面，清除所有 WebSocket 連接和事件監聽器。

### 2. 手動取消批量發送
如果進度視窗卡住，可以：
1. 關閉進度視窗
2. 等待幾秒鐘
3. 重新嘗試批量發送

### 3. 檢查網關狀態
使用調試工具檢查網關連接狀態：
```bash
node server/debug-websocket-status.js
```

## 後續改進建議

### 1. 進度事件去重
在前端添加進度事件去重邏輯，避免處理重複的進度更新。

### 2. 超時機制
為批量發送添加超時機制，避免無限等待。

### 3. 用戶反饋
在等待過程中提供更好的用戶反饋，顯示當前狀態和預計等待時間。

### 4. 錯誤恢復
添加錯誤恢復機制，當檢測到異常狀態時自動重置。

## 相關文件

### 修改的文件
- `src/utils/websocketClient.ts` - WebSocket 消息處理修復
- `src/components/DevicesPage.tsx` - 移除重複事件監聽器
- `src/components/BatchSendProgress.tsx` - 修復 useEffect 依賴
- `server/services/sendPreviewToGateway.js` - 進度更新優化

### 調試工具
- `server/debug-websocket-status.js` - WebSocket 狀態調試工具

## 驗證步驟

1. 確保沒有網關連接或網關忙碌
2. 選擇設備並點擊批量發送
3. 觀察進度視窗是否正常顯示狀態
4. 檢查瀏覽器控制台是否還有重複的進度事件
5. 確認進度視窗能正常關閉或自動關閉

## 注意事項

- 這些修復主要解決了前端的重複處理問題
- 如果後端仍然有網關等待循環的問題，需要進一步調試網關連接狀態
- 建議在生產環境中監控 WebSocket 連接和進度事件的頻率
