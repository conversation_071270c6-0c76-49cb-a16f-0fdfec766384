/**
 * 設備配置 API
 * 負責管理設備相關的配置
 */

import { saveConfig, getConfig } from './sysConfigApi';

/**
 * 設備欄位視圖配置接口
 */
export interface DeviceFieldsViewConfig {
  visibleFields: string[];
  columnOrder: string[];
  columnWidths: Record<string, number>;
}

/**
 * 保存設備欄位顯示設定
 * @param config 欄位顯示設定
 */
export async function saveDeviceFieldsViewConfig(config: DeviceFieldsViewConfig): Promise<void> {
  return saveConfig('deviceFieldsView', config);
}

/**
 * 獲取設備欄位顯示設定
 * @returns 欄位顯示設定
 */
export async function getDeviceFieldsViewConfig(): Promise<DeviceFieldsViewConfig | null> {
  return getConfig<DeviceFieldsViewConfig>('deviceFieldsView');
}

/**
 * 設備欄位顯示設定默認值
 */
export const DEFAULT_DEVICE_FIELDS_VIEW_CONFIG: DeviceFieldsViewConfig = {
  visibleFields: [
    'macAddress',
    'size',
    'colorType',
    'rssi',
    'battery',
    'status',
    'imageUpdateStatus',
    'dataId',
    'templateId',
    'lastSeen',
    'code',
    'note',
  ],
  columnOrder: [],
  columnWidths: {}
};
