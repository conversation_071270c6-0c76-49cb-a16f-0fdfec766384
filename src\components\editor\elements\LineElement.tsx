import React, { useState, useEffect, useRef } from 'react';
import { TemplateElement } from '../../../types';
import { ControlHandle } from './ShapeElement';
import { constrainElementToCanvas } from '../canvasUtils';

interface LineElementProps {
  element: TemplateElement;
  isSelected: boolean;
  onSelect: (id: string, e?: React.MouseEvent) => void; // 修改: 添加事件參數
  onUpdate: (id: string, updates: Partial<TemplateElement>) => void;
  zoom?: number; // 預設縮放比例為 100%
  setSelectedTool?: (tool: string | null) => void; // 新增參數
  // 多選功能 - 新增屬性
  selectedElementIds?: string[];
  moveSelectedElements?: (dx: number, dy: number) => void;
  isMultiMoving?: boolean; // 新增：多選移動狀態標誌
  onDragStart?: () => void;
  onDragEnd?: () => void;
}

export const LineElement: React.FC<LineElementProps> = ({
  element,
  isSelected,
  onSelect,
  onUpdate,
  zoom = 100, // 預設縮放比例為 100%
  setSelectedTool, // 新增參數
  // 多選功能 - 新增屬性
  selectedElementIds = [],
  moveSelectedElements,
  isMultiMoving = false, // 預設為 false
  onDragStart,
  onDragEnd
}) => {
  const elementRef = useRef<HTMLDivElement>(null);

  const [isDragging, setIsDragging] = useState(false);
  const [startDragPosition, setStartDragPosition] = useState({ x: 0, y: 0 });
  const [isResizing, setIsResizing] = useState(false);
  const [activeHandle, setActiveHandle] = useState<ControlHandle | null>(null);
  const [isRotating, setIsRotating] = useState(false);

  // 線條寬度和顏色
  const lineWidth = element.lineWidth || 2;
  const lineColor = element.lineColor || '#000000';

  // 是否為多選狀態
  const isMultiSelected = selectedElementIds.length > 1 && selectedElementIds.includes(element.id);
  // 元件是否被鎖定
  const isLocked = element.locked === true;

  // 計算虛擬選取框的實際位置和尺寸
  let boxLeft = element.x;
  let boxTop = element.y;
  let boxWidth = Math.abs(element.width);
  let boxHeight = Math.abs(element.height);

  // 根據線段的方向調整選取框位置
  if (element.width < 0) {
    boxLeft = element.x + element.width;
  }
  if (element.height < 0) {
    boxTop = element.y + element.height;
  }

  // 處理滑鼠按下事件 - 開始拖曳
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!elementRef.current || isResizing || isRotating || isLocked) return;

    e.stopPropagation();
    setIsDragging(true);
    setStartDragPosition({ x: e.clientX, y: e.clientY });

    // 通知開始拖曳
    if (onDragStart) {
      onDragStart();
    }

    // 確保元素被選中
    if (!isSelected) {
      onSelect(element.id);
    }
  };

  // 處理控制點滑鼠按下事件 - 開始調整大小或選擇端點
  const handleControlPointMouseDown = (handle: ControlHandle, e: React.MouseEvent) => {
    e.stopPropagation();

    // 通知開始拖曳
    if (onDragStart) {
      onDragStart();
    }

    setIsResizing(true);
    setActiveHandle(handle);
  };

  // 處理滑鼠移動事件 - 拖曳、調整大小或旋轉
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        // 元素拖曳移動
        const deltaX = e.clientX - startDragPosition.x;
        const deltaY = e.clientY - startDragPosition.y;

        // 根據縮放比例調整移動量
        const scaledDeltaX = deltaX / (zoom / 100);
        const scaledDeltaY = deltaY / (zoom / 100);

        // 多選狀態下移動所有選中元素
        if (isMultiSelected && moveSelectedElements) {
          moveSelectedElements(scaledDeltaX, scaledDeltaY);
        } else {
          // 單選狀態下只移動當前元素
          // 計算新位置
          const newX = Math.round(element.x + scaledDeltaX);
          const newY = Math.round(element.y + scaledDeltaY);

          // 獲取畫布的寬高
          const canvasElement = elementRef.current?.closest('[data-canvas-width]');
          const canvasWidth = canvasElement ?
            parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
          const canvasHeight = canvasElement ?
            parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

          // 使用限制函數確保元素在畫布內
          const constrainedUpdates = constrainElementToCanvas(
            { x: newX, y: newY, width: Math.round(element.width), height: Math.round(element.height) },
            canvasWidth,
            canvasHeight
          );

          // 更新元素位置
          onUpdate(element.id, constrainedUpdates);
        }

        setStartDragPosition({ x: e.clientX, y: e.clientY });
      } else if (isResizing && activeHandle) {
        // 獲取畫布的寬高
        const canvasElement = elementRef.current?.closest('[data-canvas-width]');
        const canvasWidth = canvasElement ?
          parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
        const canvasHeight = canvasElement ?
          parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

        // 對於線段元素，根據控制點調整線條的端點
        if (activeHandle === ControlHandle.Right) {
          // 調整線段的終點 - 考慮縮放比例調整移動量
          const scaledMovementX = e.movementX / (zoom / 100);
          const scaledMovementY = e.movementY / (zoom / 100);

          // 調整線段的終點 - 直接更新寬度和高度
          let newWidth = Math.round(element.width + scaledMovementX);
          let newHeight = Math.round(element.height + scaledMovementY);

          // 檢查並限制終點不超出畫布邊界
          if (element.x + newWidth > canvasWidth) {
            newWidth = canvasWidth - element.x;
          }
          if (element.x + newWidth < 0) {
            newWidth = -element.x;
          }
          if (element.y + newHeight > canvasHeight) {
            newHeight = canvasHeight - element.y;
          }
          if (element.y + newHeight < 0) {
            newHeight = -element.y;
          }

          // 更新線段尺寸
          onUpdate(element.id, {
            width: newWidth,
            height: newHeight
          });
        } else if (activeHandle === ControlHandle.Left) {
          // 如果拖動起點，需要調整整個線段
          const scaledMovementX = e.movementX / (zoom / 100);
          const scaledMovementY = e.movementY / (zoom / 100);

          // 計算線段新的起點和終點
          let newX = Math.round(element.x + scaledMovementX);
          let newY = Math.round(element.y + scaledMovementY);
          let newWidth = Math.round(element.width - scaledMovementX);
          let newHeight = Math.round(element.height - scaledMovementY);

          // 檢查並限制起點不超出畫布邊界
          if (newX < 0) {
            newWidth += newX; // 調整寬度以補償位置調整
            newX = 0;
          }
          if (newY < 0) {
            newHeight += newY; // 調整高度以補償位置調整
            newY = 0;
          }

          // 檢查終點是否超出畫布
          if (newX + newWidth > canvasWidth) {
            newWidth = canvasWidth - newX;
          }
          if (newY + newHeight > canvasHeight) {
            newHeight = canvasHeight - newY;
          }

          // 更新元素位置和尺寸
          onUpdate(element.id, {
            x: newX,
            y: newY,
            width: newWidth,
            height: newHeight
          });
        }
      }
    };

    const handleMouseUp = (e: MouseEvent) => {
      // 阻止事件冒泡，避免觸發畫布的點擊事件導致選取狀態被清除
      e.stopPropagation();

      // 如果剛完成拖曳操作，調用拖曳完成回調
      if ((isDragging || isResizing || isRotating) && onDragEnd) {
        onDragEnd();
      }

      setIsDragging(false);
      setIsResizing(false);
      setIsRotating(false);
      setActiveHandle(null);
    };

    if (isDragging || isResizing || isRotating) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [
    isDragging,
    isResizing,
    isRotating,
    startDragPosition,
    activeHandle,
    element,
    onUpdate,
    zoom,
    isMultiSelected,
    moveSelectedElements
  ]);

  return (
    <div
      ref={elementRef}
      style={{
        position: 'absolute',
        left: boxLeft,
        top: boxTop,
        width: boxWidth + 1, // 確保有足夠的空間顯示線條
        height: boxHeight + 1,
        cursor: isLocked ? 'not-allowed' : (isSelected ? 'move' : 'pointer'),
        // 改進選中效果
        border: isSelected ? (isLocked ? '1px dashed #f97316' : '1px dashed #3b82f6') : 'none',
        transformOrigin: 'top left',
        backgroundColor: 'transparent',
        // 提高選中元素的 z-index
        zIndex: isSelected ? 10 : 1
      }}
      onClick={(e) => {
        e.stopPropagation();

        // 如果正在進行多選移動，則不處理點擊選擇事件
        if (isMultiMoving) {
          console.log('處於多選移動狀態，忽略線條選擇操作');
          return;
        }

        // 處理Shift多選 - 修改: 使用事件參數而不是window.event
        if (e.shiftKey && selectedElementIds.length > 0) {
          // 多選模式下的點擊處理由父組件處理
          onSelect(element.id, e);
        } else {
          // 普通點擊，選中單個元素
          onSelect(element.id, e);
          // 選中元素時取消工具選擇
          if (setSelectedTool) {
            setSelectedTool(null);
          }
        }
      }}
      onMouseDown={handleMouseDown}
      data-element-id={element.id}
    >
      {/* 使用 SVG 繪製任意方向的線條 */}
      <svg
        width="100%"
        height="100%"
        style={{
          display: 'block',
          overflow: 'visible'
        }}
      >
        <line
          x1={element.width < 0 ? boxWidth : 0}
          y1={element.height < 0 ? boxHeight : 0}
          x2={element.width < 0 ? 0 : boxWidth}
          y2={element.height < 0 ? 0 : boxHeight}
          stroke={lineColor}
          strokeWidth={lineWidth}
          strokeLinecap="round"
        />
      </svg>

      {/* 只在單選狀態下顯示控制點，多選狀態下不顯示，鎖定時也不顯示 */}
      {isSelected && !isMultiSelected && !isLocked && (
        <>
          {/* 控制點定位在線段兩端 */}
          <div
            style={{
              position: 'absolute',
              left: element.width < 0 ? boxWidth - 4 : -4,
              top: element.height < 0 ? boxHeight - 4 : -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'pointer'
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Left, e)}
          />
          <div
            style={{
              position: 'absolute',
              left: element.width < 0 ? -4 : boxWidth - 4,
              top: element.height < 0 ? -4 : boxHeight - 4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'pointer'
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Right, e)}
          />
        </>
      )}

      {/* 鎖定狀態指示器 */}
      {isLocked && (
        <div
          className="lock-indicator"
          style={{
            position: 'absolute',
            top: 2,
            right: 2,
            fontSize: '8px',
            color: 'white',
            backgroundColor: 'rgba(249, 115, 22, 0.8)',
            padding: '2px 4px',
            borderRadius: '2px',
            pointerEvents: 'none',
            userSelect: 'none',
            zIndex: 15
          }}
        >
          🔒
        </div>
      )}
    </div>
  );
};