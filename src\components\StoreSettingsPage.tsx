import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Search, Plus } from 'lucide-react';
import { Store } from '../types/store';
import { RefreshPlanManagement } from './RefreshPlanManagement';

// 標籤分組頁面組件
const TagGroupTab: React.FC = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');

  return (
    <div className="p-4">

      {/* 搜索和新增按鈕 */}
      <div className="flex justify-between mb-6">
        <div className="relative w-56">
          <input
            type="text"
            placeholder={t('common.search')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
          <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
        </div>
        <button
          className="flex items-center px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          <Plus size={18} className="mr-1" />
          {t('common.add')}
        </button>
      </div>

      {/* 表格頭部 */}
      <div className="bg-gray-100 rounded-t-md">
        <div className="grid grid-cols-5 py-3 px-4 font-medium text-gray-700">
          <div>{t('storeSettings.tagGroup.serialNumber')}</div>
          <div>{t('storeSettings.tagGroup.groupName')}</div>
          <div>{t('storeSettings.tagGroup.groupDescription')}</div>
          <div>{t('storeSettings.tagGroup.tagCount')}</div>
          <div>{t('common.actions')}</div>
        </div>
      </div>

      {/* 無數據提示 */}
      <div className="bg-white py-8 text-center text-gray-500 rounded-b-md">
        {t('database.noData')}
      </div>

      {/* 分頁 */}
      <div className="flex items-center justify-between mt-4">
        <div className="flex items-center space-x-2">
          <button className="px-3 py-1 bg-blue-500 text-white rounded-md">1</button>
          <select className="px-2 py-1 border border-gray-300 rounded-md">
            <option>10條/頁</option>
            <option>20條/頁</option>
            <option>50條/頁</option>
          </select>
          <span className="text-gray-600">共0頁 到第</span>
          <input type="text" className="w-16 px-2 py-1 border border-gray-300 rounded-md" />
          <span className="text-gray-600">頁</span>
          <button className="px-3 py-1 bg-white border border-gray-300 rounded-md">確定</button>
        </div>
      </div>
    </div>
  );
};

// 參數設置頁面組件
const ParameterSettingsTab: React.FC = () => {
  const { t } = useTranslation();
  const [tagListenInterval, setTagListenInterval] = useState<string>("1000");
  const [broadcastInterval, setBroadcastInterval] = useState<string>("");
  const [isSaving, setIsSaving] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);

  // 保存參數設置
  const handleSave = async () => {
    try {
      setIsSaving(true);
      setSuccess(null);

      // 這裡應該添加實際的API調用來保存設置
      // 模擬API調用延遲
      await new Promise(resolve => setTimeout(resolve, 500));

      setSuccess('參數設定已保存');
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('保存參數設定失敗:', err);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="p-4">
 
      {/* 成功提示 */}
      {success && (
        <div className="mb-4 p-3 bg-green-100 border-l-4 border-green-500 text-green-700">
          {success}
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('storeSettings.parameters.tagListenInterval')}
            <span className="ml-1 text-gray-500 text-xs">(?)</span>
          </label>
          <div className="flex items-center">
            <select
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value={tagListenInterval}
              onChange={(e) => setTagListenInterval(e.target.value)}
              disabled={isSaving}
            >
              <option value="1000">1000</option>
              <option value="2000">2000</option>
              <option value="3000">3000</option>
            </select>
            <span className="ml-2">ms</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('storeSettings.parameters.broadcastInterval')}
            <span className="ml-1 text-gray-500 text-xs">(?)</span>
          </label>
          <div className="flex items-center">
            <select
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value={broadcastInterval}
              onChange={(e) => setBroadcastInterval(e.target.value)}
              disabled={isSaving}
            >
              <option value="">{t('gateways.selectVersion')}</option>
              <option value="1000">1000</option>
              <option value="2000">2000</option>
            </select>
            <span className="ml-2">ms</span>
          </div>
        </div>

        <div className="pt-4">
          <button
            onClick={handleSave}
            disabled={isSaving}
            className={`px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isSaving ? t('common.saving') : t('common.save')}
          </button>
        </div>
      </div>
    </div>
  );
};

// 刷圖計劃頁面組件
const RefreshPlanTab: React.FC<{ store?: Store }> = ({ store }) => {
  if (!store) {
    return (
      <div className="p-4">
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4">
          請先選擇一個門店以管理刷圖計畫
        </div>
      </div>
    );
  }

  return <RefreshPlanManagement store={store} />;
};

// 模板策略頁面組件
const TemplateStrategyTab: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="p-4">
     
      {/* 新增按鈕 */}
      <div className="flex justify-end mb-6">
        <button
          className="flex items-center px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          <Plus size={18} className="mr-1" />
          {t('common.add')}
        </button>
      </div>

      {/* 表格頭部 */}
      <div className="bg-gray-100 rounded-t-md">
        <div className="grid grid-cols-6 py-3 px-4 font-medium text-gray-700">
          <div>{t('storeSettings.templateStrategy.strategyCode')}</div>
          <div>{t('storeSettings.templateStrategy.matchCondition')}</div>
          <div>{t('storeSettings.templateStrategy.tagSize')}</div>
          <div>{t('storeSettings.templateStrategy.tagColor')}</div>
          <div>{t('storeSettings.templateStrategy.usedTemplate')}</div>
          <div>{t('common.actions')}</div>
        </div>
      </div>

      {/* 無數據提示 */}
      <div className="bg-white py-8 text-center text-gray-500 rounded-b-md">
        {t('database.noData')}
      </div>
    </div>
  );
};

// 素材管理頁面組件
const MaterialManagementTab: React.FC = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');

  return (
    <div className="p-4">
   
      {/* 搜索和上傳按鈕 */}
      <div className="flex justify-between mb-6">
        <div className="flex items-center">
          <select className="px-3 py-2 border border-gray-300 rounded-md mr-4 w-40 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option>{t('gateways.selectVersion')}</option>
          </select>
          <select className="px-3 py-2 border border-gray-300 rounded-md mr-4 w-40 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option>{t('permission.all')}</option>
          </select>
          <div className="relative">
            <input
              type="text"
              placeholder={t('common.search')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-4 pr-10 py-2 rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
            <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
          </div>
        </div>
        <div className="flex space-x-2">
          <button className="flex items-center px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
            <Plus size={18} className="mr-1" />
            {t('storeSettings.materials.uploadImage')}
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
            <Plus size={18} className="mr-1" />
            {t('storeSettings.materials.uploadVideo')}
          </button>
          <button className="flex items-center px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
            {t('storeSettings.materials.more')}
          </button>
        </div>
      </div>

      {/* 無數據提示 */}
      <div className="bg-white py-8 text-center text-gray-500 rounded-md">
        {t('database.noData')}
      </div>

      {/* 分頁 */}
      <div className="flex items-center justify-between mt-4">
        <div className="flex items-center space-x-2">
          <button className="px-3 py-1 bg-blue-500 text-white rounded-md">1</button>
          <select className="px-2 py-1 border border-gray-300 rounded-md">
            <option>6條/頁</option>
            <option>12條/頁</option>
            <option>24條/頁</option>
          </select>
          <span className="text-gray-600">共0頁 到第</span>
          <input type="text" className="w-16 px-2 py-1 border border-gray-300 rounded-md" />
          <span className="text-gray-600">頁</span>
          <button className="px-3 py-1 bg-white border border-gray-300 rounded-md">確定</button>
        </div>
      </div>
    </div>
  );
};

// 主頁面組件
interface StoreSettingsPageProps {
  store?: Store;
}

export const StoreSettingsPage: React.FC<StoreSettingsPageProps> = ({ store }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('tagGroup');

  const tabs = [
    { id: 'tagGroup', label: t('storeSettings.tabs.tagGroup') },
    { id: 'parameterSettings', label: t('storeSettings.tabs.parameterSettings') },
    { id: 'refreshPlan', label: t('storeSettings.tabs.refreshPlan') },
    { id: 'templateStrategy', label: t('storeSettings.tabs.templateStrategy') },
    { id: 'materialManagement', label: t('storeSettings.tabs.materialManagement') },
  ];

  return (
    <div className="p-8 lg:px-4 py-2">

      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* 標籤頁切換 */}
          <div className="border-b">
            <nav className="flex">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-6 py-3 text-sm font-medium transition-colors
                    ${activeTab === tab.id
                      ? 'bg-orange-500 text-white'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                    }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* 標籤頁內容 */}
          <div className="min-h-[600px]">
            {activeTab === 'tagGroup' && <TagGroupTab />}
            {activeTab === 'parameterSettings' && <ParameterSettingsTab />}
            {activeTab === 'refreshPlan' && <RefreshPlanTab store={store} />}
            {activeTab === 'templateStrategy' && <TemplateStrategyTab />}
            {activeTab === 'materialManagement' && <MaterialManagementTab />}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreSettingsPage;
