/**
 * 預覽圖管理器
 * 用於统一处理预览图生成和保存的功能
 */
import { updateDevice } from './api/deviceApi';
import { Device } from '../types/device';
import { calculateCRC32 } from './crypto';
import React from 'react';
import { createRoot } from 'react-dom/client';
import PreviewComponent from '../components/PreviewComponent';


/**
 * 保存預覽圖到設備記錄
 * @param deviceId 設備ID
 * @param previewImage 預覽圖數據 (base64 格式)
 * @param storeId 可選的門店ID
 * @param updateImageStatus 是否更新圖片狀態為"未更新"，默認為false
 * @param updateImageCode 是否更新imageCode，默認為true
 * @returns Promise<boolean> 保存是否成功
 */
export const savePreviewImageToDevice = async (
  deviceId: string,
  previewImage: string,
  storeId?: string,
  updateImageStatus: boolean = false,
  updateImageCode: boolean = true
): Promise<boolean> => {
  if (!deviceId || !previewImage) {
    console.error('保存預覽圖失敗: 缺少設備ID或預覽圖數據');
    return false;
  }

  try {
    console.log(`正在保存預覽圖到設備: ${deviceId}`);

    // 準備更新數據
    const updateData: any = {
      previewImage,
      updatedAt: new Date()
    };

    // 計算並更新imageCode
    if (updateImageCode) {
      const imageCode = calculateCRC32(previewImage);
      updateData['data.imageCode'] = imageCode;
      console.log(`計算並更新imageCode: ${imageCode}`);
    }

    // 只有在需要更新圖片狀態時才設置imageUpdateStatus
    // 在綁定數據時，如果沒有勾選"立即發送"，則需要設置為"未更新"
    if (updateImageStatus) {
      updateData.imageUpdateStatus = '未更新';
      console.log('設置imageUpdateStatus為"未更新"');
    } else {
      console.log('不更新imageUpdateStatus，保持原狀');
    }

    // 更新設備記錄
    await updateDevice(deviceId, updateData, storeId);

    console.log('預覽圖成功保存到設備記錄');
    return true;
  } catch (error) {
    console.error('保存預覽圖到設備記錄失敗:', error);
    return false;
  }
};

/**
 * 處理預覽圖生成回調並保存到設備記錄
 * 這是一個統一的處理函數，可以在任何地方生成預覽圖時使用
 * @param deviceId 設備ID
 * @param previewData 預覽圖數據 (base64)
 * @param storeId 可選的門店ID
 * @param updateImageStatus 是否更新圖片狀態為"未更新"，默認為false
 * @param updateImageCode 是否更新imageCode，默認為true
 * @param originalCallback 原始回調函數
 * @returns void
 */
export const handlePreviewImageGenerated = async (
  deviceId: string,
  previewData: string | null,
  storeId?: string,
  updateImageStatus: boolean = false,
  updateImageCode: boolean = true,
  originalCallback?: (previewData: string | null) => void
): Promise<void> => {
  // 如果有有效的預覽圖數據，保存到設備記錄
  if (previewData && deviceId) {
    try {
      await savePreviewImageToDevice(deviceId, previewData, storeId, updateImageStatus, updateImageCode);
    } catch (error) {
      console.error('處理預覽圖生成並保存到設備記錄失敗:', error);
    }
  }

  // 如果提供了原始回調，繼續調用它
  if (originalCallback && typeof originalCallback === 'function') {
    originalCallback(previewData);
  }
};

/**
 * 在發送預覽圖到網關前重新生成最新預覽圖
 * @param device 設備數據
 * @param storeData 門店數據
 * @param template 模板數據
 * @param forceRefreshDevice 是否強制從服務器刷新設備數據
 * @returns Promise<string|null> 生成的預覽圖數據，如失敗則返回null
 */
export const regeneratePreviewBeforeSend = async (
  device: Device,
  storeData: any[],
  template: any,
  forceRefreshDevice: boolean = false
): Promise<string | null> => {
  if (!device || !template) {
    console.error('無法重新生成預覽圖：缺少設備或模板數據');
    return null;
  }

  try {
    // 如果啟用了強制刷新，先從服務器獲取最新的設備信息
    let updatedDevice = device;
    if (forceRefreshDevice && device._id) {
      try {
        const { buildEndpointUrl } = await import('./api/apiConfig');

        console.log(`正在從服務器獲取設備 ${device._id} 的最新資料...`);
        const deviceResponse = await fetch(buildEndpointUrl(`devices/${device._id}`), {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
        });

        if (deviceResponse.ok) {
          updatedDevice = await deviceResponse.json();
          console.log('已獲取最新設備信息:', updatedDevice._id);
        } else {
          console.warn('獲取最新設備信息失敗，將使用當前設備信息');
        }
      } catch (error) {
        console.error('獲取最新設備信息時出錯:', error);
        // 出錯時使用傳入的設備數據繼續執行
      }
    }

    // 創建一個臨時的PreviewComponent實例
    const tempContainer = document.createElement('div');
    document.body.appendChild(tempContainer);

    // 處理數據綁定格式，確保是對象類型
    const bindingData = typeof updatedDevice.dataBindings === 'string'
      ? JSON.parse(updatedDevice.dataBindings)
      : updatedDevice.dataBindings || {};

    // 調試輸出 bindingData
    console.log('重新生成預覽圖時的 bindingData 類型:', typeof bindingData);
    console.log('重新生成預覽圖時的 bindingData 值:', JSON.stringify(bindingData, null, 2));
    console.log('device.dataBindings 原始值:', updatedDevice.dataBindings);

    // 返回生成的預覽圖的Promise
    return new Promise((resolve) => {
      // 使用已經導入的 React 和 PreviewComponent
      // 再次檢查 bindingData，確保正確傳遞給 PreviewComponent
      console.log('即將傳遞給 PreviewComponent 的 bindingData:', bindingData);
        const root = createRoot(tempContainer);
      // 根據設備的 colorType 或模板的 color 決定效果類型
      const deviceColorType = updatedDevice.data?.colorType;
      const templateColor = template.color;

      // 優先使用設備的 colorType，其次使用模板的 color，最後使用默認值
      const targetColorType = deviceColorType || templateColor;

      console.log(`重新生成預覽圖 - 設備顏色類型: ${deviceColorType}, 模板顏色類型: ${templateColor}, 使用: ${targetColorType || 'blackAndWhite'}`);

      root.render(
        React.createElement(PreviewComponent, {
          template: template,
          bindingData: bindingData,
          storeData: storeData,
          effectType: targetColorType || "blackAndWhite", // 使用計算出的顏色類型
          threshold: 128,
          // 不再啟用自動保存，由調用者決定是否保存預覽圖
          deviceId: undefined,
          storeId: undefined,
          onPreviewGenerated: (previewData: string | null) => {
            // 添加調試信息
            console.log('預覽圖生成完成，數據長度:', previewData ? previewData.length : 0);

            // 清理臨時DOM元素
            setTimeout(() => {
              try {
                root.unmount();
                document.body.removeChild(tempContainer);
              } catch (e) {
                console.error('清理臨時DOM元素失敗:', e);
              }
            }, 100);

            // 將預覽圖數據傳遞給調用者
            resolve(previewData);
          }
        })
      );
    });
  } catch (error) {
    console.error('重新生成預覽圖失敗:', error);
    return null;
  }
};
