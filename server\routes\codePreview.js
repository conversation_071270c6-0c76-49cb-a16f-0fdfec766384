const express = require('express');
const router = express.Router();
const { generateQRCodePreview } = require('../utils/qrCodeRenderer');
const { generateBarcodePreview } = require('../utils/barcodeRenderer');

/**
 * QR Code 預覽生成 API
 * POST /api/code-preview/qr
 */
router.post('/qr', async (req, res) => {
  try {
    const {
      content,
      qrCodeType = 'qrcode',
      errorCorrectionLevel = 'M',
      quietZone = 4,
      moduleSize = 3,
      foregroundColor = '#000000',
      backgroundColor = '#FFFFFF',
      width = 200,
      height = 200
    } = req.body;

    // 參數驗證
    if (!content || typeof content !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'content 參數是必需的且必須是字符串'
      });
    }

    if (content.length === 0) {
      return res.status(400).json({
        success: false,
        error: '內容不能為空'
      });
    }

    // 驗證 QR Code 類型
    const validQRTypes = ['qrcode', 'datamatrix', 'pdf417'];
    if (!validQRTypes.includes(qrCodeType)) {
      return res.status(400).json({
        success: false,
        error: `無效的 QR Code 類型: ${qrCodeType}`
      });
    }

    // 驗證錯誤修正等級
    const validErrorLevels = ['L', 'M', 'Q', 'H'];
    if (!validErrorLevels.includes(errorCorrectionLevel)) {
      return res.status(400).json({
        success: false,
        error: `無效的錯誤修正等級: ${errorCorrectionLevel}`
      });
    }

    // 驗證數值參數
    if (width < 50 || width > 1000) {
      return res.status(400).json({
        success: false,
        error: '寬度必須在 50-1000 像素之間'
      });
    }

    if (height < 50 || height > 1000) {
      return res.status(400).json({
        success: false,
        error: '高度必須在 50-1000 像素之間'
      });
    }

    // 生成 QR Code
    const options = {
      content,
      qrCodeType,
      errorCorrectionLevel,
      quietZone,
      moduleSize,
      foregroundColor,
      backgroundColor,
      width,
      height
    };

    const imageBuffer = await generateQRCodePreview(options);
    
    // 轉換為 base64
    const base64Data = imageBuffer.toString('base64');
    const dataUrl = `data:image/png;base64,${base64Data}`;

    // 設置快取標頭
    res.set({
      'Cache-Control': 'public, max-age=300', // 5分鐘快取
      'Content-Type': 'application/json'
    });

    res.json({
      success: true,
      data: dataUrl
    });

  } catch (error) {
    console.error('QR Code 預覽生成錯誤:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'QR Code 生成失敗'
    });
  }
});

/**
 * 條碼預覽生成 API
 * POST /api/code-preview/barcode
 */
router.post('/barcode', async (req, res) => {
  try {
    const {
      content,
      barcodeType = 'code128',
      quietZone = 10,
      foregroundColor = '#000000',
      backgroundColor = '#FFFFFF',
      width = 200,
      height = 100,
      showText = false // 預設不顯示文字
    } = req.body;

    // 參數驗證
    if (!content || typeof content !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'content 參數是必需的且必須是字符串'
      });
    }

    if (content.length === 0) {
      return res.status(400).json({
        success: false,
        error: '內容不能為空'
      });
    }

    // 驗證條碼類型
    const validBarcodeTypes = ['code128', 'ean13', 'upc-a', 'code39', 'code93'];
    if (!validBarcodeTypes.includes(barcodeType)) {
      return res.status(400).json({
        success: false,
        error: `無效的條碼類型: ${barcodeType}`
      });
    }

    // 驗證數值參數
    if (width < 100 || width > 1000) {
      return res.status(400).json({
        success: false,
        error: '寬度必須在 100-1000 像素之間'
      });
    }

    if (height < 50 || height > 500) {
      return res.status(400).json({
        success: false,
        error: '高度必須在 50-500 像素之間'
      });
    }

    // 生成條碼
    const options = {
      content,
      barcodeType,
      quietZone,
      foregroundColor,
      backgroundColor,
      width,
      height,
      showText
    };

    const imageBuffer = await generateBarcodePreview(options);
    
    // 轉換為 base64
    const base64Data = imageBuffer.toString('base64');
    const dataUrl = `data:image/png;base64,${base64Data}`;

    // 設置快取標頭
    res.set({
      'Cache-Control': 'public, max-age=300', // 5分鐘快取
      'Content-Type': 'application/json'
    });

    res.json({
      success: true,
      data: dataUrl
    });

  } catch (error) {
    console.error('條碼預覽生成錯誤:', error);
    res.status(500).json({
      success: false,
      error: error.message || '條碼生成失敗'
    });
  }
});

/**
 * 健康檢查 API
 * GET /api/code-preview/health
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Code Preview API 運行正常',
    timestamp: new Date().toISOString()
  });
});

/**
 * 支援的類型列表 API
 * GET /api/code-preview/types
 */
router.get('/types', (req, res) => {
  res.json({
    success: true,
    data: {
      qrCodeTypes: [
        { value: 'qrcode', label: 'QR Code', maxLength: 4296 },
        { value: 'datamatrix', label: 'Data Matrix', maxLength: 3116 },
        { value: 'pdf417', label: 'PDF417', maxLength: 2710 }
      ],
      barcodeTypes: [
        { value: 'code128', label: 'Code 128', maxLength: 80 },
        { value: 'ean13', label: 'EAN-13', length: 13 },
        { value: 'upc-a', label: 'UPC-A', length: 12 },
        { value: 'code39', label: 'Code 39', maxLength: 43 },
        { value: 'code93', label: 'Code 93', maxLength: 47 }
      ],
      errorCorrectionLevels: [
        { value: 'L', label: 'L (低)' },
        { value: 'M', label: 'M (中)' },
        { value: 'Q', label: 'Q (四分位)' },
        { value: 'H', label: 'H (高)' }
      ]
    }
  });
});

module.exports = router;
