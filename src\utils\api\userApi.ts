import { buildEndpointUrl } from './apiConfig';

/**
 * 獲取用戶的權限分配
 * @param userId 用戶ID
 * @returns 用戶的權限分配列表
 */
export async function getUserPermissions(userId: string): Promise<any[]> {
  try {
    const response = await fetch(`${buildEndpointUrl('permissions')}?userId=${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`獲取用戶權限失敗: ${response.statusText}`);
    }

    const data = await response.json();
    return data.permissions || [];
  } catch (error) {
    console.error(`獲取用戶權限錯誤 (ID: ${userId}):`, error);
    throw error;
  }
}

/**
 * 獲取當前用戶的權限
 * @returns 當前用戶的權限信息
 */
export async function getCurrentUserPermissions(): Promise<{
  roles: any[];
  permissions: string[];
}> {
  try {
    const response = await fetch(buildEndpointUrl('users', 'me/permissions'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`獲取當前用戶權限失敗: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('獲取當前用戶權限錯誤:', error);
    throw error;
  }
}

/**
 * 獲取所有用戶
 * @param params 查詢參數
 * @returns 用戶列表和分頁信息
 */
export async function getAllUsers(params?: {
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
}): Promise<{ users: any[]; pagination: any }> {
  try {
    // 構建查詢參數
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    // 構建URL
    const url = queryParams.toString()
      ? `${buildEndpointUrl('users')}?${queryParams}`
      : buildEndpointUrl('users');

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`獲取用戶列表失敗: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('獲取用戶列表錯誤:', error);
    throw error;
  }
}

/**
 * 獲取單個用戶
 * @param id 用戶ID
 * @returns 用戶詳情
 */
export async function getUser(id: string): Promise<any> {
  try {
    const response = await fetch(buildEndpointUrl('users', id), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`獲取用戶詳情失敗: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`獲取用戶詳情錯誤 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 創建用戶
 * @param userData 用戶數據
 * @returns 創建的用戶
 */
export async function createUser(userData: any): Promise<any> {
  try {
    console.log('發送創建用戶請求:', userData);

    const response = await fetch(buildEndpointUrl('users'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData),
      credentials: 'include'
    });

    console.log('創建用戶響應狀態:', response.status);

    // 即使響應不是 2xx，也嘗試解析 JSON
    const responseData = await response.json().catch(() => ({ error: `解析響應失敗: ${response.statusText}` }));
    console.log('創建用戶響應數據:', responseData);

    if (!response.ok) {
      // 如果是權限錯誤
      if (response.status === 403) {
        throw new Error('沒有創建用戶的權限，請聯繫管理員');
      }

      throw new Error(responseData.error || `創建用戶失敗: ${response.statusText}`);
    }

    return responseData;
  } catch (error) {
    console.error('創建用戶錯誤:', error);
    throw error;
  }
}

/**
 * 更新用戶
 * @param id 用戶ID
 * @param userData 更新的用戶數據
 * @returns 更新後的用戶
 */
export async function updateUser(id: string, userData: any): Promise<any> {
  try {
    const response = await fetch(buildEndpointUrl('users', id), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData),
      credentials: 'include'
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `更新用戶失敗: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`更新用戶錯誤 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 刪除用戶
 * @param id 用戶ID
 */
export async function deleteUser(id: string): Promise<void> {
  try {
    const response = await fetch(buildEndpointUrl('users', id), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `刪除用戶失敗: ${response.statusText}`);
    }
  } catch (error) {
    console.error(`刪除用戶錯誤 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 重設用戶密碼
 * @param id 用戶ID
 * @returns 重設結果，包含默認密碼
 */
export async function resetUserPassword(id: string): Promise<{ defaultPassword: string }> {
  try {
    const response = await fetch(buildEndpointUrl('users', `${id}/reset-password`), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `重設密碼失敗: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`重設密碼錯誤 (ID: ${id}):`, error);
    throw error;
  }
}
