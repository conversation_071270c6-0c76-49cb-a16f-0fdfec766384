import { getAllDataFields } from './api/dataFieldApi';
import { getAllStoreData } from './api/storeDataApi';
import { DataField } from '../types';

/**
 * 綁定元素處理的樣式信息接口
 */
export interface BindingStyleInfo {
  element: HTMLElement;
  textContainer: HTMLElement;
  backgroundColor: string;
  border: string;
  bindingIndicator?: HTMLElement;
  indicatorDisplay?: string;
  originalContent?: string;
}

/**
 * 處理綁定元素，使其顯示實際數據
 * @returns 原始樣式信息數組，用於後續還原
 */
export const processBindingElements = async (): Promise<BindingStyleInfo[]> => {
  const originalStyles: BindingStyleInfo[] = [];

  try {
    // 獲取資料欄位和門店數據
    const [dataFields, storeData] = await Promise.all([
      getAllDataFields(),
      getAllStoreData()
    ]);

    // 找到所有綁定數據的文字元素
    const boundTextElements = document.querySelectorAll('[data-has-binding="true"]');
    console.log('找到綁定數據的文字元素數量:', boundTextElements.length);

    // 處理每個綁定元素
    boundTextElements.forEach(element => {
      if (element instanceof HTMLElement) {
        // 找到文字容器
        const textContainer = element.querySelector('.text-element-content');
        if (textContainer instanceof HTMLElement) {
          // 獲取綁定信息
          const fieldId = element.getAttribute('data-field-id');
          const selectedStoreId = element.getAttribute('data-store-id');
          const showPrefix = element.getAttribute('data-show-prefix') === 'true';

          // 存儲原始樣式
          const styleInfo: BindingStyleInfo = {
            element,
            textContainer,
            backgroundColor: textContainer.style.backgroundColor,
            border: textContainer.style.border,
            originalContent: undefined,
            bindingIndicator: undefined,
            indicatorDisplay: ''
          };

          // 存儲原始文字內容（不包含「已綁定」標記）
          // 尋找所有的文字節點
          const textNodes: Node[] = [];
          for (let i = 0; i < textContainer.childNodes.length; i++) {
            const node = textContainer.childNodes[i];
            if (node.nodeType === Node.TEXT_NODE) {
              textNodes.push(node);
            }
          }

          // 將所有文字節點合併為一個字符串
          styleInfo.originalContent = textNodes.map(node => node.textContent).join('') || undefined;

          // 存儲「已綁定」標記
          const bindingIndicator = textContainer.querySelector('.binding-indicator');
          if (bindingIndicator && bindingIndicator instanceof HTMLElement) {
            styleInfo.bindingIndicator = bindingIndicator;
            styleInfo.indicatorDisplay = bindingIndicator.style.display;

            // 將「已綁定」標記從 DOM 中移除，以便我們可以完全控制元素的內容
            bindingIndicator.remove();
          }

          // 移除綁定標記樣式
          textContainer.style.backgroundColor = 'transparent';
          textContainer.style.border = 'none';

          // 先清空元素內容，確保沒有任何殘留的內容
          while (textContainer.firstChild) {
            textContainer.removeChild(textContainer.firstChild);
          }

          // 如果有綁定欄位和選擇的門店，更新顯示內容
          if (fieldId && selectedStoreId && storeData.length > 0) {
            // 尋找選擇的門店
            const store = storeData.find((s: any) => s.id === selectedStoreId);

            if (store && store[fieldId] !== undefined && store[fieldId] !== null) {
              // 如果找到門店且有欄位數據，更新顯示內容
              let displayValue = String(store[fieldId]);

              // 根據前綴設置決定是否顯示前綴
              const field = dataFields.find((f: DataField) => f.id === fieldId);
              if (field && showPrefix && field.prefix) {
                displayValue = `${field.prefix}: ${displayValue}`;
              }

              // 使用文字節點添加內容，而不是設置 textContent
              const textNode = document.createTextNode(displayValue);
              textContainer.appendChild(textNode);
            }
          } else if (fieldId) {
            // 如果有欄位 ID 但沒有門店數據，顯示預設的 'TEXT'
            const textNode = document.createTextNode('TEXT');
            textContainer.appendChild(textNode);
          }

          // 將樣式信息添加到數組
          originalStyles.push(styleInfo);
        }
      }
    });
  } catch (error) {
    console.error('處理綁定元素時出錯:', error);
  }

  return originalStyles;
};

/**
 * 恢復綁定元素的原始樣式和內容
 * @param originalStyles 原始樣式信息數組
 */
export const restoreBindingElements = (originalStyles: BindingStyleInfo[]): void => {
  console.log('恢復綁定數據的文字元素原始樣式:', originalStyles.length);
  originalStyles.forEach(styleInfo => {
    // 完全重建元素內容，而不是簡單地設置 textContent
    // 首先清空元素
    while (styleInfo.textContainer.firstChild) {
      styleInfo.textContainer.removeChild(styleInfo.textContainer.firstChild);
    }

    // 添加文字內容
    if (styleInfo.originalContent) {
      const textNode = document.createTextNode(styleInfo.originalContent);
      styleInfo.textContainer.appendChild(textNode);
    }

    // 恢復綁定標記
    if (styleInfo.bindingIndicator) {
      // 將綁定標記添加回元素中
      styleInfo.textContainer.appendChild(styleInfo.bindingIndicator);
      styleInfo.bindingIndicator.style.display = styleInfo.indicatorDisplay || '';
    }

    // 最後恢復樣式
    styleInfo.textContainer.style.backgroundColor = styleInfo.backgroundColor;
    styleInfo.textContainer.style.border = styleInfo.border;
  });
};
