console.log('=== 測試 Path2D 支援 ===');

try {
  const { createCanvas, Path2D } = require('canvas');
  console.log('✓ 成功導入 canvas 和 Path2D');
  
  const canvas = createCanvas(100, 100);
  const ctx = canvas.getContext('2d');
  console.log('✓ 成功創建畫布和上下文');
  
  // 測試創建 Path2D
  const path = new Path2D('M10,10 L20,20 L30,10 Z');
  console.log('✓ 成功創建 Path2D 對象');
  
  // 測試使用 Path2D
  ctx.strokeStyle = '#000';
  ctx.lineWidth = 2;
  ctx.stroke(path);
  console.log('✓ 成功使用 Path2D 繪製路徑');
  
  console.log('=== Path2D 測試完成，所有功能正常 ===');
  
} catch (error) {
  console.error('✗ Path2D 測試失敗:', error.message);
  console.error('錯誤詳情:', error);
}
