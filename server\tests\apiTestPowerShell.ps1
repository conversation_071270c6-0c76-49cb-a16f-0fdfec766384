# PowerShell Test Script - API Endpoint Testing
# Note: These commands require the server to be running

Write-Host "Testing API Endpoints" -ForegroundColor Green

# Login to get token
Write-Host "`nLogging in" -ForegroundColor Cyan
try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/auth/login" -Method Post -ContentType "application/json" -Body '{"username":"root","password":"123456789"}' -SessionVariable session -ErrorAction Stop
    Write-Host "Login successful: $($loginResponse | ConvertTo-Json)" -ForegroundColor Green
    $token = $loginResponse.token
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
} catch {
    Write-Host "Login failed: $_" -ForegroundColor Red
    exit
}

# Test creating a store
Write-Host "`nTesting create store API" -ForegroundColor Cyan
try {
    $body = @{
        id = "TEST002"
        name = "API Test Store"
        address = "API Test Address"
        status = "active"
    } | ConvertTo-Json
    $createStoreResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/stores" -Method Post -Headers $headers -Body $body -ErrorAction Stop
    Write-Host "Store created successfully: $($createStoreResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green
} catch {
    Write-Host "Failed to create store: $_" -ForegroundColor Red
}

# Test getting a store
Write-Host "`nTesting get store API" -ForegroundColor Cyan
try {
    $getStoreResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/stores/TEST002" -Method Get -Headers $headers -ErrorAction Stop
    Write-Host "Store retrieved successfully: $($getStoreResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green
} catch {
    Write-Host "Failed to get store: $_" -ForegroundColor Red
}

# Test adding first data item to store's storeSpecificData array
Write-Host "`nTesting add first data item to store's storeSpecificData array" -ForegroundColor Cyan
try {
    $dataBody = @{
        id = "API001"
        name = "API Test Product 1"
        price = "300"
        quantity = "5"
    } | ConvertTo-Json
    $createDataResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/storeData?storeId=TEST002" -Method Post -Headers $headers -Body $dataBody -ErrorAction Stop
    Write-Host "First data item added to store's storeSpecificData array successfully: $($createDataResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green

    # Check if the API response includes storeId (it shouldn't according to our new structure)
    # Convert the response to JSON and check if it contains "storeId"
    $responseJson = $createDataResponse | ConvertTo-Json
    if ($responseJson -match '"storeId"') {
        Write-Host "WARNING: API response still includes storeId field" -ForegroundColor Yellow
        Write-Host "This is inconsistent with the new data structure specification" -ForegroundColor Yellow
    } else {
        Write-Host "API response correctly does not include storeId field (CORRECT)" -ForegroundColor Green
    }

    $sn1 = $createDataResponse.sn
    Write-Host "Retrieved SN for first item: $sn1" -ForegroundColor Green

    # Store the storeId for later use in queries
    $global:currentStoreId = "TEST002"
} catch {
    Write-Host "Failed to add first data item to store's storeSpecificData array: $_" -ForegroundColor Red
}

# Test adding second data item to store's storeSpecificData array
Write-Host "`nTesting add second data item to store's storeSpecificData array" -ForegroundColor Cyan
try {
    $dataBody = @{
        id = "API002"
        name = "API Test Product 2"
        price = "500"
        quantity = "10"
        description = "This is the second test product"
    } | ConvertTo-Json
    $createDataResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/storeData?storeId=TEST002" -Method Post -Headers $headers -Body $dataBody -ErrorAction Stop
    Write-Host "Second data item added to store's storeSpecificData array successfully: $($createDataResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green

    # Check if the API response includes storeId (it shouldn't according to our new structure)
    # Convert the response to JSON and check if it contains "storeId"
    $responseJson = $createDataResponse | ConvertTo-Json
    if ($responseJson -match '"storeId"') {
        Write-Host "WARNING: API response still includes storeId field" -ForegroundColor Yellow
        Write-Host "This is inconsistent with the new data structure specification" -ForegroundColor Yellow
    } else {
        Write-Host "API response correctly does not include storeId field (CORRECT)" -ForegroundColor Green
    }

    $sn2 = $createDataResponse.sn
    Write-Host "Retrieved SN for second item: $sn2" -ForegroundColor Green

    # Use the first item's SN for later operations
    $sn = $sn1
} catch {
    Write-Host "Failed to add second data item to store's storeSpecificData array: $_" -ForegroundColor Red
}

# Test getting store specific data
Write-Host "`nTesting get store specific data API" -ForegroundColor Cyan
try {
    $getDataResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/storeData?storeId=TEST002" -Method Get -Headers $headers -ErrorAction Stop
    Write-Host "Store specific data retrieved successfully: $($getDataResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green
} catch {
    Write-Host "Failed to get store specific data: $_" -ForegroundColor Red
}

# If we got an SN, test update and delete
if ($sn) {
    # Test updating store specific data
    Write-Host "`nTesting update store specific data API" -ForegroundColor Cyan
    try {
        $updateBody = @{
            price = "400"
        } | ConvertTo-Json
        $updateDataResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/storeData/$sn`?storeId=TEST002" -Method Put -Headers $headers -Body $updateBody -ErrorAction Stop
        Write-Host "Store specific data updated successfully: $($updateDataResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green

        # Check if the API response includes storeId (it shouldn't according to our new structure)
        # Convert the response to JSON and check if it contains "storeId"
        $responseJson = $updateDataResponse | ConvertTo-Json
        if ($responseJson -match '"storeId"') {
            Write-Host "WARNING: API response still includes storeId field" -ForegroundColor Yellow
            Write-Host "This is inconsistent with the new data structure specification" -ForegroundColor Yellow
        } else {
            Write-Host "API response correctly does not include storeId field (CORRECT)" -ForegroundColor Green
        }
    } catch {
        Write-Host "Failed to update store specific data: $_" -ForegroundColor Red
    }

    # Check the store data structure after update
    Write-Host "`nVerifying store data structure after update" -ForegroundColor Cyan
    try {
        $getStoreResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/stores/TEST002" -Method Get -Headers $headers -ErrorAction Stop
        Write-Host "Store data structure:" -ForegroundColor Green
        Write-Host "- Store ID: $($getStoreResponse.id)" -ForegroundColor Green
        Write-Host "- Store Name: $($getStoreResponse.name)" -ForegroundColor Green
        Write-Host "- Store Address: $($getStoreResponse.address)" -ForegroundColor Green
        Write-Host "- Store Status: $($getStoreResponse.status)" -ForegroundColor Green
        Write-Host "- Store Specific Data Count: $($getStoreResponse.storeSpecificData.Length)" -ForegroundColor Green

        if ($getStoreResponse.storeSpecificData.Length -gt 0) {
            $firstItem = $getStoreResponse.storeSpecificData[0]
            Write-Host "`nFirst storeSpecificData item:" -ForegroundColor Green
            Write-Host "- SN: $($firstItem.sn)" -ForegroundColor Green
            Write-Host "- ID: $($firstItem.id)" -ForegroundColor Green
            Write-Host "- Name: $($firstItem.name)" -ForegroundColor Green
            Write-Host "- Price: $($firstItem.price)" -ForegroundColor Green
            Write-Host "- Quantity: $($firstItem.quantity)" -ForegroundColor Green

            # Verify that storeId is not present in the storeSpecificData item
            if ($null -eq $firstItem.storeId) {
                Write-Host "- storeId: Not present (correct)" -ForegroundColor Green
            } else {
                Write-Host "- storeId: $($firstItem.storeId) (INCORRECT - should not be present)" -ForegroundColor Red
            }
        }

        # Verify that the structure matches the specification
        $hasCorrectStructure = $true

        # Check if storeSpecificData exists
        if ($null -eq $getStoreResponse.storeSpecificData) {
            $hasCorrectStructure = $false
            Write-Host "- Missing storeSpecificData field" -ForegroundColor Red
        }

        # Check if gatewayManagement exists
        if ($null -eq $getStoreResponse.gatewayManagement) {
            $hasCorrectStructure = $false
            Write-Host "- Missing gatewayManagement field" -ForegroundColor Red
        }

        # Check if deviceManagement exists
        if ($null -eq $getStoreResponse.deviceManagement) {
            $hasCorrectStructure = $false
            Write-Host "- Missing deviceManagement field" -ForegroundColor Red
        }

        # Check if storeSettings exists
        if ($null -eq $getStoreResponse.storeSettings) {
            $hasCorrectStructure = $false
            Write-Host "- Missing storeSettings field" -ForegroundColor Red
        }

        if ($hasCorrectStructure) {
            Write-Host "`nStore structure matches specification (CORRECT)" -ForegroundColor Green
        } else {
            Write-Host "`nStore structure does NOT match specification (INCORRECT)" -ForegroundColor Red
        }
    } catch {
        Write-Host "Failed to get store for verification: $_" -ForegroundColor Red
    }

    # Get the store again to verify the complete structure before deletion
    Write-Host "`nVerifying complete store structure before deletion" -ForegroundColor Cyan
    try {
        $getStoreResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/stores/TEST002" -Method Get -Headers $headers -ErrorAction Stop
        Write-Host "Complete store structure:" -ForegroundColor Green
        Write-Host ($getStoreResponse | ConvertTo-Json -Depth 5) -ForegroundColor Green
    } catch {
        Write-Host "Failed to get store for verification: $_" -ForegroundColor Red
    }

    # Test deleting store specific data
    Write-Host "`nTesting delete store specific data API" -ForegroundColor Cyan
    try {
        $deleteDataResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/storeData/$sn`?storeId=TEST002" -Method Delete -Headers $headers -ErrorAction Stop
        Write-Host "Store specific data deleted successfully" -ForegroundColor Green
    } catch {
        Write-Host "Failed to delete store specific data: $_" -ForegroundColor Red
    }
}

# Test deleting store
Write-Host "`nTesting delete store API" -ForegroundColor Cyan
try {
    $deleteStoreResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/stores/TEST002" -Method Delete -Headers $headers -ErrorAction Stop
    Write-Host "Store deleted successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to delete store: $_" -ForegroundColor Red
}

Write-Host "`nTesting complete" -ForegroundColor Green
