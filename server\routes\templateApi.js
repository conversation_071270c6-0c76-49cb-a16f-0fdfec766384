// server/routes/templateApi.js
const express = require('express');
const router = express.Router();

// 導入模板驗證功能
const { validateTemplate } = require('../utils/templateValidation');
const { broadcastTemplateUpdate } = require('../services/websocketService');

// 數據庫連接函數引用
let connectDBFunction;

// 初始化數據庫連接
function initDB(connectDB) {
  connectDBFunction = connectDB;
  return connectDBFunction;
}

// 中間件：應用模板驗證
router.use('/templates', (req, res, next) => {
  // 僅對 POST 和 PUT 請求進行驗證
  if ((req.method === 'POST' || req.method === 'PUT') && req.body) {
    const templates = Array.isArray(req.body) ? req.body : [req.body];
    for (const template of templates) {
      const validationResult = validateTemplate(template);
      if (!validationResult.valid) {
        return res.status(400).json({
          error: '模板驗證失敗',
          details: validationResult.errors
        });
      }
    }
  }
  next();
});

// 保存模板
router.post('/templates', async (req, res) => {
  try {
    const { client } = await connectDBFunction();
    const db = client.db('resourceManagement');
    const templatesCollection = db.collection('templates');

    const templates = req.body;
    console.log('伺服器: 收到保存模板請求，請求體類型:', typeof templates);

    if (!Array.isArray(templates) && !templates.id) {
      console.error('伺服器: 無效的模板格式，缺少ID屬性或非數組格式');
      return res.status(400).json({ error: '無效的模板格式' });
    }

    const saveTemplates = Array.isArray(templates) ? templates : [templates];
    console.log('伺服器: 準備保存的模板數量:', saveTemplates.length);

    const results = [];

    for (const template of saveTemplates) {
      if (!template.id) {
        console.warn('伺服器: 跳過無ID的模板');
        continue;
      }

      console.log('伺服器: 處理模板 ID:', template.id, '名稱:', template.name,
        '系統模板:', template.isSystemTemplate ? '是' : '否',
        '門店ID:', template.storeId || '無');

      const templateToSave = { ...template };
      if (templateToSave._id) {
        console.log('伺服器: 刪除模板中的 _id 欄位以避免衝突');
        delete templateToSave._id;
      }

      try {
        // 檢查模板名稱是否重複
        // 查詢條件：相同名稱，相同門店ID（或都是系統模板），但不是同一個模板ID
        const nameQuery = {
          name: template.name,
          id: { $ne: template.id } // 排除自己
        };

        // 如果是門店模板，則檢查同一門店內是否有重複名稱
        if (template.storeId) {
          nameQuery.storeId = template.storeId;
        } else if (template.isSystemTemplate) {
          // 如果是系統模板，則檢查系統模板中是否有重複名稱
          nameQuery.isSystemTemplate = true;
        }

        console.log('伺服器: 檢查模板名稱是否重複，查詢條件:', JSON.stringify(nameQuery));

        const existingTemplate = await templatesCollection.findOne(nameQuery);

        if (existingTemplate) {
          console.log('伺服器: 發現重複的模板名稱:', template.name);
          throw {
            status: 400,
            message: `模板名稱 "${template.name}" 已存在，請使用其他名稱`,
            code: 'DUPLICATE_TEMPLATE_NAME',
            field: 'name'
          };
        }

        const result = await templatesCollection.updateOne(
          { id: template.id },  // 查詢條件
          { $set: templateToSave },   // 更新內容
          { upsert: true }      // 如果不存在則插入
        );

        console.log('伺服器: MongoDB 操作結果:', {
          matchedCount: result.matchedCount,
          modifiedCount: result.modifiedCount,
          upsertedCount: result.upsertedCount,
          upsertedId: result.upsertedId
        });

        results.push({
          id: template.id,
          name: template.name,
          saved: true,
          isNew: result.upsertedCount > 0,
          matchedCount: result.matchedCount,
          modifiedCount: result.modifiedCount
        });
      } catch (dbError) {
        console.error('伺服器: 操作錯誤:', dbError);

        // 如果是我們自定義的錯誤（如重複名稱錯誤）
        if (dbError.status && dbError.code) {
          results.push({
            id: template.id,
            name: template.name,
            saved: false,
            error: dbError.message,
            code: dbError.code,
            field: dbError.field
          });
          continue; // 繼續處理下一個模板
        }

        // 其他資料庫錯誤
        throw dbError;
      }
    }

    // 計算成功和失敗的數量
    const successCount = results.filter(r => r.saved !== false).length;
    const failureCount = results.length - successCount;

    console.log('伺服器: 保存操作完成，成功:', successCount, '失敗:', failureCount);

    // 如果所有模板都保存失敗，返回 400 狀態碼
    if (successCount === 0 && failureCount > 0) {
      return res.status(400).json({
        error: '保存模板失敗',
        message: '所有模板保存都失敗',
        templates: results
      });
    }

    // 如果部分成功部分失敗，返回 207 Multi-Status 狀態碼
    if (failureCount > 0) {
      return res.status(207).json({
        message: `部分模板保存成功 (${successCount}/${results.length})`,
        templates: results
      });
    }

    // 推送WebSocket事件（只推送成功保存的模板）
    try {
      const successfulTemplates = results.filter(r => r.saved !== false);
      if (successfulTemplates.length > 0) {
        // 獲取完整的模板信息用於推送
        const templateIds = successfulTemplates.map(r => r.id);
        const savedTemplates = await templatesCollection.find({ id: { $in: templateIds } }).toArray();

        // 分別處理新增和更新的模板
        const newTemplates = [];
        const updatedTemplates = [];

        savedTemplates.forEach(template => {
          const result = successfulTemplates.find(r => r.id === template.id);
          const templateUpdate = {
            id: template.id,
            name: template.name,
            isSystemTemplate: template.isSystemTemplate || false,
            storeId: template.storeId,
            updatedFields: ['name', 'elements', 'screenSize', 'color', 'orientation']
          };

          if (result && result.isNew) {
            newTemplates.push(templateUpdate);
          } else {
            updatedTemplates.push(templateUpdate);
          }
        });

        // 推送新增模板事件
        if (newTemplates.length > 0) {
          const systemNewTemplates = newTemplates.filter(t => t.isSystemTemplate);
          const storeNewTemplates = newTemplates.filter(t => !t.isSystemTemplate);

          if (systemNewTemplates.length > 0) {
            broadcastTemplateUpdate(null, systemNewTemplates, 'create');
          }

          // 按門店分組推送新增事件
          const storeNewGroups = storeNewTemplates.reduce((groups, template) => {
            const storeId = template.storeId;
            if (!groups[storeId]) groups[storeId] = [];
            groups[storeId].push(template);
            return groups;
          }, {});

          Object.entries(storeNewGroups).forEach(([storeId, templates]) => {
            broadcastTemplateUpdate(storeId, templates, 'create');
          });
        }

        // 推送更新模板事件
        if (updatedTemplates.length > 0) {
          const systemUpdatedTemplates = updatedTemplates.filter(t => t.isSystemTemplate);
          const storeUpdatedTemplates = updatedTemplates.filter(t => !t.isSystemTemplate);

          if (systemUpdatedTemplates.length > 0) {
            broadcastTemplateUpdate(null, systemUpdatedTemplates, 'update');
          }

          // 按門店分組推送更新事件
          const storeUpdatedGroups = storeUpdatedTemplates.reduce((groups, template) => {
            const storeId = template.storeId;
            if (!groups[storeId]) groups[storeId] = [];
            groups[storeId].push(template);
            return groups;
          }, {});

          Object.entries(storeUpdatedGroups).forEach(([storeId, templates]) => {
            broadcastTemplateUpdate(storeId, templates, 'update');
          });
        }

        console.log(`推送模板WebSocket事件: 新增 ${newTemplates.length} 個, 更新 ${updatedTemplates.length} 個`);
      }
    } catch (wsError) {
      console.error('推送模板保存事件失敗:', wsError);
      // 不影響API響應
    }

    // 全部成功
    res.status(200).json({
      message: `成功保存 ${results.length} 個模板`,
      templates: results
    });
  } catch (error) {
    console.error('伺服器: 保存模板時發生錯誤:', error);

    // 檢查是否是請求體太大的錯誤
    if (error.type === 'entity.too.large' || error.status === 413) {
      return res.status(413).json({
        error: '模板數據太大',
        message: '模板包含的圖片數據過大，請嘗試壓縮圖片或減少圖片數量',
        code: 'PAYLOAD_TOO_LARGE'
      });
    }

    // 檢查是否是JSON解析錯誤
    if (error.type === 'entity.parse.failed') {
      return res.status(400).json({
        error: '模板數據格式錯誤',
        message: '無法解析模板數據，請檢查數據格式',
        code: 'INVALID_JSON'
      });
    }

    res.status(500).json({
      error: error.message || '保存模板時發生未知錯誤',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

// 獲取所有模板
router.get('/templates', async (req, res) => {
  try {
    const { client } = await connectDBFunction();
    const db = client.db('resourceManagement');
    const templatesCollection = db.collection('templates');

    // 從查詢參數中獲取門店ID
    const { storeId } = req.query;

    // 構建查詢條件
    let query = {};

    if (storeId) {
      console.log(`按門店ID過濾模板: ${storeId}`);
      // 查詢條件：模板的storeId等於指定值，或者是系統模板
      query = {
        $or: [
          { storeId: storeId },
          { isSystemTemplate: true }
        ]
      };

      // 記錄查詢條件
      console.log('模板查詢條件:', JSON.stringify(query));
    }

    const templates = await templatesCollection.find(query).toArray();

    // 僅返回必要的摘要信息
    const templateSummaries = templates.map(template => ({
      id: template.id,
      name: template.name,
      type: template.type,
      screenSize: template.screenSize,
      color: template.color,
      orientation: template.orientation,
      elementsCount: template.elements ? template.elements.length : 0,
      storeId: template.storeId,
      isSystemTemplate: template.isSystemTemplate
    }));

    res.json(templateSummaries);
  } catch (error) {
    console.error('獲取模板列表失敗:', error);
    res.status(500).json({ error: error.message });
  }
});

// 獲取單個模板
router.get('/templates/:id', async (req, res) => {
  try {
    const { client } = await connectDBFunction();
    const db = client.db('resourceManagement');
    const templatesCollection = db.collection('templates');

    const templateId = req.params.id;

    if (!templateId || typeof templateId !== 'string') {
      return res.status(400).json({ error: '無效的模板 ID' });
    }

    const template = await templatesCollection.findOne({ id: { $eq: templateId } });

    if (!template) {
      return res.status(404).json({ error: '找不到模板' });
    }

    res.json(template);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 刪除模板
router.delete('/templates/:id', async (req, res) => {
  try {
    const { client } = await connectDBFunction();
    const db = client.db('resourceManagement');
    const templatesCollection = db.collection('templates');

    const templateId = req.params.id;

    // 先獲取模板信息用於WebSocket事件
    const templateToDelete = await templatesCollection.findOne({ id: templateId });

    const result = await templatesCollection.deleteOne({ id: templateId });

    if (result.deletedCount === 0) {
      return res.status(404).json({ error: '找不到模板' });
    }

    // 推送WebSocket事件
    try {
      if (templateToDelete) {
        const templateUpdate = {
          id: templateToDelete.id,
          name: templateToDelete.name,
          isSystemTemplate: templateToDelete.isSystemTemplate || false,
          storeId: templateToDelete.storeId,
          updatedFields: []
        };

        broadcastTemplateUpdate(
          templateToDelete.isSystemTemplate ? null : templateToDelete.storeId,
          [templateUpdate],
          'delete'
        );
      }
    } catch (wsError) {
      console.error('推送模板刪除事件失敗:', wsError);
      // 不影響API響應
    }

    res.json({ message: '模板刪除成功' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = { router, initDB };
