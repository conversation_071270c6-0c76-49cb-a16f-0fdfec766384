# EPD Manager 安全機制設計

## 1. 概述

EPD Manager 系統採用了基於 JWT (JSON Web Token) 的認證機制，結合了 HTTP-only Cookie 和 localStorage 存儲，形成雙重認證保障。本文檔詳細說明系統的安全機制設計、認證流程以及相關的安全考量。

## 2. 認證流程

系統的認證流程如下圖所示：

```mermaid
sequenceDiagram
    participant User as 用戶
    participant Frontend as 前端應用
    participant Backend as 後端服務器
    participant DB as 數據庫

    User->>Frontend: 輸入用戶名和密碼
    Frontend->>Backend: 發送登入請求
    Backend->>DB: 驗證用戶憑證
    DB-->>Backend: 返回用戶信息
    Backend->>Backend: 生成 JWT Token
    Backend-->>Frontend: 返回 Token 和用戶信息
    Backend-->>Frontend: 設置 HTTP-only Cookie
    Frontend->>Frontend: 將 Token 存儲在 localStorage
    Frontend-->>User: 顯示登入成功，重定向到主頁
```

### 2.1 登入過程

1. 用戶在登入頁面輸入用戶名和密碼
2. 前端將憑證發送到後端的 `/api/auth/login` 端點
3. 後端驗證用戶憑證
4. 如果驗證成功，後端生成 JWT Token
5. 後端將 Token 通過兩種方式返回給前端：
   - 作為響應 JSON 的一部分
   - 設置為 HTTP-only Cookie
6. 前端將 Token 存儲在 localStorage 中
7. 用戶被重定向到應用主頁

### 2.2 登出過程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant Frontend as 前端應用
    participant Backend as 後端服務器

    User->>Frontend: 點擊登出按鈕
    Frontend->>Backend: 發送登出請求
    Backend->>Backend: 清除 Cookie
    Backend-->>Frontend: 返回登出成功
    Frontend->>Frontend: 清除 localStorage 中的 Token
    Frontend->>Frontend: 重置認證狀態
    Frontend-->>User: 重定向到登入頁面
```

1. 用戶點擊登出按鈕
2. 前端發送請求到 `/api/auth/logout` 端點
3. 後端清除 Token Cookie
4. 前端清除 localStorage 中的 Token
5. 前端重置認證狀態
6. 用戶被重定向到登入頁面

## 3. Token 存儲機制

系統採用了雙重 Token 存儲機制，如下圖所示：

```mermaid
graph TD
    A[JWT Token 生成] --> B[HTTP-only Cookie 存儲]
    A --> C[localStorage 存儲]
    B --> D[自動隨請求發送]
    C --> E[手動添加到請求頭]
    D --> F[後端 Token 驗證]
    E --> F
    F --> G[訪問受保護資源]
```

### 3.1 HTTP-only Cookie 存儲

- 由後端設置，前端無法通過 JavaScript 訪問
- 自動隨著每個請求發送到相同域的後端
- 提供較高的安全性，防止 XSS 攻擊竊取 Token
- 在用戶選擇"記住我"選項時，可設置較長的過期時間（30天）

### 3.2 localStorage 存儲

- 由前端使用 Zustand 的 persist 中間件存儲
- 存儲在名為 `auth-storage` 的 localStorage 鍵中
- 便於前端訪問和管理認證狀態
- 可以顯式添加到 API 請求的 Authorization 頭部

## 4. API 請求認證

系統中的 API 請求認證機制如下：

```mermaid
graph TD
    A[API 請求] --> B{是否需要認證?}
    B -->|是| C[添加認證信息]
    B -->|否| G[直接發送請求]
    C --> D[從 localStorage 獲取 Token]
    C --> E[設置 credentials: 'include']
    D --> F[添加 Authorization 頭部]
    E --> H[自動發送 Cookie]
    F --> I[發送請求]
    H --> I
    I --> J[後端驗證]
    J --> K{Token 有效?}
    K -->|是| L[處理請求]
    K -->|否| M[返回 401 未授權]
```

### 4.1 前端 API 請求

前端發送 API 請求時有兩種方式添加認證信息：

1. **Cookie 認證**：
   - 所有 API 請求都設置 `credentials: 'include'` 選項
   - 這會自動將 Cookie（包括 Token Cookie）隨請求發送

2. **Authorization 頭部認證**：
   - 某些 API 請求（如 `checkAuth` 和 `changePassword`）顯式從 localStorage 獲取 Token
   - 將 Token 添加到請求頭：`Authorization: Bearer ${token}`

### 4.2 後端 Token 驗證

後端使用 `authenticate` 中間件驗證請求：

1. 從請求中獲取 Token（優先從 Authorization 頭部，其次從 Cookie）
2. 驗證 Token 的有效性
3. 從 Token 中獲取用戶 ID，並查詢完整的用戶信息
4. 將用戶信息添加到請求對象，供後續處理使用
5. 如果 Token 無效或不存在，返回 401 未授權錯誤

## 5. 安全考量

### 5.1 雙重認證的優勢

- **深度防禦**：即使一種認證機制失效，另一種仍可提供保障
- **靈活性**：可以根據不同的 API 需求選擇適合的認證方式
- **用戶體驗**：無需用戶頻繁登入，同時保持安全性

### 5.2 潛在風險

- **localStorage 安全性**：localStorage 中的 Token 可能被 XSS 攻擊竊取
- **CSRF 風險**：雖然使用了 SameSite Cookie 策略，但仍需注意 CSRF 攻擊風險
- **Token 過期管理**：需要妥善處理 Token 過期和刷新機制

## 6. 改進建議

為進一步提高系統安全性，可考慮以下改進：

1. **僅使用 HTTP-only Cookie**：
   - 移除 localStorage 中的 Token 存儲
   - 所有 API 請求僅依賴 Cookie 認證
   - 前端通過專門的端點檢查認證狀態

2. **實現 Token 刷新機制**：
   - 使用短期訪問 Token 和長期刷新 Token
   - 當訪問 Token 過期時，使用刷新 Token 獲取新的訪問 Token

3. **增強 CSRF 保護**：
   - 實現 CSRF Token 機制
   - 在所有修改數據的請求中要求 CSRF Token

4. **添加請求簽名**：
   - 對關鍵請求添加基於時間戳和密鑰的簽名
   - 防止請求被篡改或重放

5. **實現 API 請求限流**：
   - 限制單個用戶的請求頻率
   - 防止暴力破解和 DoS 攻擊
