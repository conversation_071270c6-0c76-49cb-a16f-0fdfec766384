# Server端實現規格書

## 1. 概述

本文檔定義了網關與設備通信架構中Server端的實現規格。Server端負責提供WebSocket服務，接收並處理來自網關的數據，以及提供API接口供App註冊網關。

## 2. 負責範圍

Server端開發人員負責以下功能的實現：

1. WebSocket服務的建立與管理
2. 網關註冊API的擴展（添加WebSocket連接信息）
3. 網關狀態管理
4. 設備數據處理與存儲
5. 認證與授權機制

## 3. 技術選擇

- **框架**：Express.js
- **WebSocket庫**：ws 或 socket.io
- **數據庫**：MongoDB
- **認證機制**：JWT
- **開發語言**：JavaScript (Node.js)

## 4. 資料庫設計

### 4.1 用戶模型 (User)

```javascript
{
  _id: ObjectId,
  username: String,
  password: String,
  email: String,
  name: String,
  phone: String,
  status: String,       // 'active', 'inactive'
  createdAt: Date,
  updatedAt: Date
}
```

### 4.2 門店模型 (Store)

```javascript
{
  _id: ObjectId,
  id: String,          // 門店唯一識別碼
  name: String,        // 門店名稱
  address: String,     // 門店地址
  phone: String,       // 門店電話
  managerId: ObjectId,  // 門店管理員的使用者ID
  status: String,      // 門店狀態，例如 'active', 'inactive'
  createdAt: Date,     // 創建時間
  updatedAt: Date,     // 更新時間
  storeSpecificData: [  // 門店特定數據
    {
      _id: ObjectId,
      uid: String,      // 特定數據唯一識別碼
      id: String,       // 特定數據ID
      name: Mixed,      // 可為null或其他類型
      description: Mixed,
      price: Mixed,
      quantity: Mixed,
      date: Mixed
      // 其他動態欄位...
    }
  ],
  gatewayManagement: Object,  // 網關管理相關設置
  deviceManagement: Object,   // 設備管理相關設置
  storeSettings: Object       // 門店設置
}
```

### 4.3 網關模型 (Gateway)

```javascript
{
  _id: ObjectId,
  name: String,           // 網關名稱
  macAddress: String,     // 網關MAC地址，唯一標識
  model: String,          // 網關型號
  ipAddress: String,      // 當前IP地址
  status: String,         // 'online', 'offline'
  lastSeen: Date,         // 最後在線時間
  wifiFirmwareVersion: String, // WiFi晶片固件版本
  btFirmwareVersion: String,   // 藍牙晶片固件版本
  storeId: String,        // 關聯的門店ID
  
  // 網關發現的設備列表
  devices: [ObjectId],    // 此網關發現的所有設備ID
  
  // WebSocket連接信息
  websocket: {
    url: String,
    path: String,
    token: String,
    protocol: String
  },
  createdAt: Date,
  updatedAt: Date
}
```

### 4.4 設備模型 (Device)

```javascript
{
  _id: ObjectId,
  macAddress: String,    // 設備MAC地址，唯一標識
  status: String,        // 'online', 'offline'
  dataId: String,        // 關聯的數據ID
  storeId: String,       // 關聯的門店ID
  
  // 新增與網關綁定相關欄位
  initialized: Boolean,  // 是否已初始化
  primaryGatewayId: ObjectId, // 主要網關ID
  otherGateways: [ObjectId],  // 其他發現此設備的網關ID列表
  
  // 新增用戶綁定欄位
  userId: ObjectId,      // 綁定的用戶ID
  
  lastSeen: Date,        // 最後活動時間
  note: String,          // 設備備註
  
  // 設備本地相關數據，包含設備的硬件特性和狀態
  data: {
    size: String,        // 設備尺寸，例如 "10.3"
    rssi: Number,        // 信號強度 (-100-0)
    battery: Number,     // 電池電量 (0-100)
    imgcode: String,     // 設備圖片編碼
    // 其他設備特定數據...
  },
  
  createdAt: Date,
  updatedAt: Date
}
```

### 4.5 設備事件記錄模型 (DeviceEvent)

```javascript
{
  _id: ObjectId,
  deviceId: ObjectId,     // 關聯的設備ID
  eventType: String,      // 事件類型：'discovered', 'user_binding', 'gateway_changed', 'data_update', 'status_change'
  eventData: {            // 事件具體數據，根據不同事件類型有不同的結構
    // user_binding 事件數據示例
    action: String,       // 'bind', 'unbind', 'auto_bind', 'auto_unbind'
    userId: String,       // 用戶ID
    userName: String,     // 用戶名
    userEmail: String,    // 用戶郵箱
    
    // gateway_changed 事件數據示例
    oldPrimaryGatewayId: String,  // 舊主要網關ID
    newPrimaryGatewayId: String,  // 新主要網關ID
    gatewayName: String,          // 網關名稱
    
    // discovered 事件數據示例
    gatewayId: String,    // 發現設備的網關ID
    gatewayName: String,  // 網關名稱
    
    // 其他事件特定數據...
  },
  timestamp: Date,        // 事件發生時間
  deviceMac: String,      // 設備MAC地址 (冗餘儲存以便於查詢)
  storeId: String         // 設備所屬門店ID (冗餘儲存以便於查詢)
}
```

### 4.6 網關事件記錄模型 (GatewayEvent)

```javascript
{
  _id: ObjectId,
  gatewayId: ObjectId,    // 關聯的網關ID
  eventType: String,      // 事件類型：'online', 'offline', 'firmware_update', 'config_change'
  eventData: {            // 事件具體數據，根據不同事件類型有不同的結構
    // firmware_update 事件數據示例
    firmwareType: String, // 'wifi', 'bt'
    oldVersion: String,   // 舊版本
    newVersion: String,   // 新版本
    
    // 其他事件特定數據...
  },
  timestamp: Date,        // 事件發生時間
  gatewayMac: String,     // 網關MAC地址 (冗餘儲存以便於查詢)
  storeId: String         // 網關所屬門店ID (冗餘儲存以便於查詢)
}
```

### 4.7 資料庫索引設計

為了提高查詢效能，我們在資料庫中設定以下索引：

1. **Device集合索引**
   ```javascript
   // 設備MAC地址索引，確保唯一性
   db.devices.createIndex({ "macAddress": 1 }, { unique: true });
   
   // 設備所屬用戶索引，加速用戶查詢自己的設備
   db.devices.createIndex({ "userId": 1 });
   
   // 設備所屬門店索引，加速門店設備查詢
   db.devices.createIndex({ "storeId": 1 });
   
   // 設備主要網關索引
   db.devices.createIndex({ "primaryGatewayId": 1 });
   ```

2. **Gateway集合索引**
   ```javascript
   // 網關MAC地址索引，確保唯一性
   db.gateways.createIndex({ "macAddress": 1 }, { unique: true });
   
   // 網關所屬門店索引
   db.gateways.createIndex({ "storeId": 1 });
   
   // 網關狀態索引，加速查詢在線/離線網關
   db.gateways.createIndex({ "status": 1 });
   ```

3. **Store集合索引**
   ```javascript
   // 門店ID索引，確保唯一性
   db.stores.createIndex({ "id": 1 }, { unique: true });
   
   // 門店名稱索引，加速搜索
   db.stores.createIndex({ "name": 1 });
   
   // 門店狀態索引
   db.stores.createIndex({ "status": 1 });
   ```

4. **User集合索引**
   ```javascript
   // 用戶名索引，確保唯一性
   db.users.createIndex({ "username": 1 }, { unique: true });
   
   // 用戶郵箱索引（如果郵箱不為空，則確保唯一性）
   db.users.createIndex({ "email": 1 }, { 
     unique: true, 
     sparse: true, // 忽略空值
     partialFilterExpression: { email: { $exists: true, $ne: "" } }
   });
   ```
   
5. **DeviceEvent集合索引**
   ```javascript
   // 設備ID索引，加速按設備查詢事件記錄
   db.deviceEvents.createIndex({ "deviceId": 1 });
   
   // 時間戳索引，加速時間範圍查詢
   db.deviceEvents.createIndex({ "timestamp": -1 });
   
   // 事件類型索引，加速按類型過濾事件
   db.deviceEvents.createIndex({ "eventType": 1 });
   
   // 門店ID索引，加速按門店查詢事件
   db.deviceEvents.createIndex({ "storeId": 1 });
   ```
   
6. **GatewayEvent集合索引**
   ```javascript
   // 網關ID索引，加速按網關查詢事件記錄
   db.gatewayEvents.createIndex({ "gatewayId": 1 });
   
   // 時間戳索引，加速時間範圍查詢
   db.gatewayEvents.createIndex({ "timestamp": -1 });
   
   // 事件類型索引，加速按類型過濾事件
   db.gatewayEvents.createIndex({ "eventType": 1 });
   
   // 門店ID索引，加速按門店查詢事件
   db.gatewayEvents.createIndex({ "storeId": 1 });
   ```

### 4.6 資料庫集合關係圖

```
┌─────────┐           ┌───────────┐           ┌─────────┐
│  users  │◄────────►│  devices   │◄────────►│ gateways │
└─────────┘           └───────────┘           └─────────┘
     ▲                      ▲                      ▲
     │                      │                      │
     ▼                      │                      │
┌─────────┐                 │                      │
│  roles  │                 │                      │
└─────────┘                 │                      │
                           ▼                      ▼
                    ┌───────────┐           ┌─────────┐
                    │   stores  │◄────────►│templates │
                    └───────────┘           └─────────┘
```

- **設備與用戶的關係**: 
  - 一對多：一個用戶可以綁定多個設備，一個設備只能綁定到一個用戶
  - 設備通過 `userId` 欄位關聯到用戶
  - 我們可以通過查詢設備集合中 userId 字段來獲取用戶綁定的所有設備

- **設備與網關的關係**:
  - 多對多：一個設備可以被多個網關發現，一個網關可以發現多個設備
  - 設備通過 `primaryGatewayId` 標識主要網關
  - 設備通過 `otherGateways` 陣列存儲其他發現該設備的網關ID
  - 網關通過 `devices` 陣列存儲所有發現的設備ID

- **設備與門店的關係**:
  - 多對一：多個設備可以屬於一個門店
  - 設備通過 `storeId` 欄位關聯到門店
  - 門店可以通過 `deviceManagement` 欄位存儲與設備相關的設置

- **網關與門店的關係**:
  - 多對一：多個網關可以屬於一個門店
  - 網關通過 `storeId` 欄位關聯到門店
  - 門店可以通過 `gatewayManagement` 欄位存儲與網關相關的設置

### 4.7 設備事件記錄模型 (DeviceEvent)

```javascript
{
  _id: ObjectId,
  deviceId: ObjectId,       // 關聯的設備ID
  eventType: String,        // 事件類型，如 'discovered', 'user_binding', 'gateway_changed', 'data_update', 'status_change'
  eventData: Object,        // 事件詳細資料，根據事件類型有不同的內容
  timestamp: Date,          // 事件發生時間
  deviceMac: String,        // 設備MAC地址
  storeId: ObjectId         // 關聯的門店ID
}
```

這個集合用於記錄設備的各種事件歷史，包括但不限於：
1. 設備被發現 (`discovered`)
2. 設備綁定/解除綁定用戶 (`user_binding`)
   - `bind`: 手動綁定用戶
   - `unbind`: 解除綁定
   - `auto_bind`: 自動綁定
   - `auto_unbind`: 自動解除綁定
3. 設備主要網關變更 (`gateway_changed`)
4. 設備數據更新 (`data_update`)
5. 設備狀態變更 (`status_change`)

通過記錄這些事件，我們可以跟蹤設備的完整生命週期和活動歷史，有助於問題排查和使用情況分析。

### 4.8 網關事件記錄模型 (GatewayEvent)

```javascript
{
  _id: ObjectId,
  gatewayId: ObjectId,      // 關聯的網關ID
  eventType: String,        // 事件類型，如 'connect', 'disconnect', 'heartbeat-fail', 'error'
  eventData: Object,        // 事件詳細資料
  timestamp: Date,          // 事件發生時間
  storeId: ObjectId,        // 關聯的門店ID
  gatewayName: String       // 網關名稱
}
```

網關事件記錄用於追蹤網關的連接狀態和重要活動，便於監控和故障排除。

## 5. 詳細設計

### 5.1 WebSocket服務

#### 5.1.1 服務設置

WebSocket服務需要支持以下功能：

- 基於路徑的連接處理（包含門店ID和網關ID）
- Token驗證
- 連接狀態管理
- 消息處理

```javascript
// 路徑格式
/ws/store/{storeId}/gateway/{gatewayId}?token={jwt_token}
```

#### 5.1.2 連接管理

- 使用Map存儲連接的網關
- 在連接建立時更新網關狀態為"online"
- 在連接關閉時更新網關狀態為"offline"
- 處理連接錯誤

#### 5.1.3 消息處理

支持處理以下類型的消息：

1. **deviceStatus**：更新設備狀態
   ```javascript
   // 接收消息格式
   {
     "type": "deviceStatus",
     "devices": [
       {
         "macAddress": "AA:BB:CC:DD:EE:FF",
         "status": "online",
         "dataId": "SYS001",
         "data": { 
           "size": "10.3",
           "battery": 85,
           "rssi": -65,
           /* 其他設備本地數據 */ 
         }
       }
     ]
   }
   ```

2. **gatewayInfo**：更新網關信息
   ```javascript
   // 接收消息格式
   {
     "type": "gatewayInfo",
     "info": {
       "macAddress": "AA:BB:CC:DD:EE:FF",
       "name": "Gateway Name",
       "model": "Gateway Model",
       "wifiFirmwareVersion": "1.0.0",
       "btFirmwareVersion": "2.0.0",
       "ipAddress": "*************"
     }
   }
   ```

3. **ping**：心跳消息
   ```javascript
   // 接收消息格式
   {
     "type": "ping",
     "timestamp": 1620000000000
   }
   
   // 發送回應
   {
     "type": "pong",
     "timestamp": 1620000000001,
     "serverTime": 1620000000001
   }
   ```

當接收到設備狀態消息時，系統將：
1. 檢查每個設備是否存在
2. 對於新設備，創建記錄並標記為未初始化(`initialized: false`)
3. 對於首次被發現的設備，將該網關設置為主要網關並標記為已初始化(`initialized: true`)
4. 對於已有主要網關的設備，如果由非主要網關發現，則將該網關添加到其他網關列表中
5. 更新設備狀態和最後活動時間

### 5.2 API擴展

#### 5.2.1 網關註冊API擴展

擴展現有的網關API，在響應中添加WebSocket連接信息：

```javascript
{
  "_id": "gateway_id",
  "name": "Gateway Name",
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "storeId": "store_id",
  // 其他現有字段...
  
  // 新增WebSocket連接信息
  "websocket": {
    "url": "ws://server-address:port/ws/store/store_id/gateway/gateway_id",
    "path": "/ws/store/store_id/gateway/gateway_id",
    "token": "jwt_token_for_gateway",
    "protocol": "json"
  }
}
```

#### 5.2.2 網關Token生成

為每個網關生成專用的JWT Token，包含以下信息：

```javascript
{
  "gatewayId": "gateway_id",
  "storeId": "store_id",
  "type": "gateway",
  "iat": 1234567890,
  "exp": 1234567890 + (30 * 24 * 60 * 60) // 30天有效期
}
```

### 5.3 數據處理

#### 5.3.1 網關狀態更新

```javascript
async function updateGatewayStatus(gatewayId, status) {
  // 獲取數據庫連接
  const { collection } = await getCollection();
  
  // 更新網關狀態為online或offline
  await collection.updateOne(
    { _id: new ObjectId(gatewayId) },
    { 
      $set: { 
        status: status,
        lastSeen: new Date(),
        updatedAt: new Date()
      } 
    }
  );
}
```

#### 5.3.2 設備狀態更新

```javascript
async function updateDeviceStatus(gatewayId, devices, storeId) {
  // 獲取數據庫連接
  const { db } = await getDbConnection();
  const deviceCollection = db.collection('devices');
  const gatewayCollection = db.collection('gateways');
  
  for (const device of devices) {
    // 查找是否已存在相同MAC地址的設備
    const existingDevice = await deviceCollection.findOne({ macAddress: device.macAddress });
    
    if (!existingDevice) {
      // 如果設備不存在，創建新設備並設置為未初始化狀態
      const newDevice = {
        macAddress: device.macAddress,
        status: 'offline',
        dataId: device.dataId || '',
        initialized: false,
        storeId: storeId,
        otherGateways: [],
        lastSeen: new Date(),
        note: device.note || '',
        data: {
          size: device.data?.size || '10.3',
          battery: device.data?.battery || 100,
          rssi: device.data?.rssi || -50,
          ...(device.data || {})
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await deviceCollection.insertOne(newDevice);
      continue;
    }
    
    // 更新設備狀態和最後活動時間
    const updateData = {
      status: 'online',
      lastSeen: new Date(),
      updatedAt: new Date()
    };
    
    // 更新設備數據
    if (device.data) {
      const existingData = existingDevice.data || {};
      
      // 更新設備數據中的特定欄位
      updateData.data = { 
        ...existingData,
        ...(device.data)
      };
      
      // 確保必要的欄位存在
      if (device.data.battery !== undefined) updateData.data.battery = device.data.battery;
      if (device.data.rssi !== undefined) updateData.data.rssi = device.data.rssi;
      if (device.data.size !== undefined) updateData.data.size = device.data.size;
    }
    
    // 處理設備初始化和網關綁定邏輯
    if (!existingDevice.initialized) {
      // 設備首次被發現，將當前網關設為主要網關
      updateData.primaryGatewayId = new ObjectId(gatewayId);
      updateData.initialized = true;
      
      // 將設備ID添加到網關的設備列表中
      await gatewayCollection.updateOne(
        { _id: new ObjectId(gatewayId) },
        { $addToSet: { devices: existingDevice._id } }
      );
    } else if (!existingDevice.primaryGatewayId || 
              !existingDevice.primaryGatewayId.equals(new ObjectId(gatewayId))) {
      // 如果已有主要網關，且當前網關不是主要網關，則添加到其他網關列表
      const otherGateways = existingDevice.otherGateways || [];
      if (!otherGateways.some(id => id.toString() === gatewayId.toString())) {
        updateData.$addToSet = { otherGateways: new ObjectId(gatewayId) };
        
        // 將設備ID添加到網關的設備列表中
        await gatewayCollection.updateOne(
          { _id: new ObjectId(gatewayId) },
          { $addToSet: { devices: existingDevice._id } }
        );
      }
    }
    
    await deviceCollection.updateOne(
      { _id: existingDevice._id },
      { $set: updateData }
    );
  }
}
```

#### 5.3.3 設置主要網關

```javascript
async function setDevicePrimaryGateway(deviceId, gatewayId) {
  // 獲取數據庫連接
  const { db } = await getDbConnection();
  const deviceCollection = db.collection('devices');
  const gatewayCollection = db.collection('gateways');
  
  // 檢查設備是否存在
  const device = await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
  if (!device) {
    throw new Error('設備不存在');
  }
  
  // 檢查網關是否存在
  const gateway = await gatewayCollection.findOne({ _id: new ObjectId(gatewayId) });
  if (!gateway) {
    throw new Error('網關不存在');
  }
  
  // 檢查該網關是否已發現該設備
  const otherGateways = device.otherGateways || [];
  const isDiscoveredByGateway = 
    (device.primaryGatewayId && device.primaryGatewayId.toString() === gatewayId) || 
    otherGateways.some(id => id.toString() === gatewayId);
                              
  if (!isDiscoveredByGateway) {
    throw new Error('此網關尚未發現該設備');
  }
  
  const oldPrimaryGatewayId = device.primaryGatewayId;
  
  // 準備更新數據
  const updateData = {
    primaryGatewayId: new ObjectId(gatewayId),
    updatedAt: new Date()
  };
  
  // 處理其他網關列表
  if (oldPrimaryGatewayId && oldPrimaryGatewayId.toString() !== gatewayId) {
    // 如果已有主要網關，將其添加到其他網關列表
    updateData.$addToSet = { otherGateways: oldPrimaryGatewayId };
  }
  
  // 如果新的主要網關之前在其他網關列表中，則從列表中移除
  if (otherGateways.some(id => id.toString() === gatewayId)) {
    updateData.$pull = { otherGateways: new ObjectId(gatewayId) };
  }
  
  // 更新設備
  await deviceCollection.updateOne(
    { _id: new ObjectId(deviceId) },
    { $set: updateData }
  );
  
  // 返回更新後的設備
  return await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
}
```

#### 5.3.4 設備綁定用戶

```javascript
async function bindDeviceToUser(deviceId, userId) {
  // 獲取數據庫連接
  const { db } = await getDbConnection();
  const deviceCollection = db.collection('devices');
  const userCollection = db.collection('users');
  
  // 檢查設備是否存在
  const device = await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
  if (!device) {
    throw new Error('設備不存在');
  }
  
  // 檢查用戶是否存在
  const user = await userCollection.findOne({ _id: new ObjectId(userId) });
  if (!user) {
    throw new Error('用戶不存在');
  }
  
  // 直接更新設備的用戶ID，無需修改用戶集合中的設備列表
  await deviceCollection.updateOne(
    { _id: new ObjectId(deviceId) },
    { $set: { 
        userId: new ObjectId(userId),
        updatedAt: new Date()
      } 
    }
  );
  
  // 獲取更新後的設備信息
  const updatedDevice = await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
  
  return updatedDevice;
}
```

#### 5.3.5 解除設備與用戶的綁定

```javascript
async function unbindDeviceFromUser(deviceId) {
  // 獲取數據庫連接
  const { db } = await getDbConnection();
  const deviceCollection = db.collection('devices');
  const userCollection = db.collection('users');
  
  // 檢查設備是否存在
  const device = await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
  if (!device) {
    throw new Error('設備不存在');
  }
  
  // 如果設備已綁定用戶，則解除綁定
  if (device.userId) {
    // 移除設備的用戶ID
    await deviceCollection.updateOne(
      { _id: new ObjectId(deviceId) },
      { 
        $unset: { userId: "" },
        $set: { updatedAt: new Date() }
      }
    );
  }
  
  // 返回更新後的設備
  return await deviceCollection.findOne({ _id: new ObjectId(deviceId) });
}
```

## 6. API設計

### 6.1 與App的接口

Server端需要提供以下API供App調用：

#### 6.1.1 認證相關
1. **登入API**：`POST /api/auth/login`
2. **登出API**：`POST /api/auth/logout`
3. **獲取當前用戶API**：`GET /api/auth/current`

#### 6.1.2 網關相關
1. **網關列表API**：`GET /api/gateways?storeId={storeId}`
2. **網關註冊API**：`POST /api/gateways`
3. **網關詳情API**：`GET /api/gateways/{id}`
4. **網關更新API**：`PUT /api/gateways/{id}`
5. **網關刪除API**：`DELETE /api/gateways/{id}`

#### 6.1.3 設備相關
1. **設備列表API**：`GET /api/devices?storeId={storeId}&userId={userId}`
2. **設備詳情API**：`GET /api/devices/{id}`
3. **創建設備API**：`POST /api/devices`
4. **更新設備API**：`PUT /api/devices/{id}`
5. **刪除設備API**：`DELETE /api/devices/{id}`
6. **設備綁定用戶API**：`POST /api/devices/{id}/bind-user`
7. **設備解除綁定API**：`POST /api/devices/{id}/unbind-user`
8. **設備設置主要網關API**：`POST /api/devices/{id}/set-primary-gateway`
9. **設備事件記錄API**：`GET /api/devices/{id}/events`

### 6.1.4 設備API詳細設計

#### 設備列表API
```javascript
// GET /api/devices?storeId={storeId}&userId={userId}
// 返回指定門店或指定用戶的所有設備
```

#### 設備詳情API
```javascript
// GET /api/devices/{id}
// 返回設備詳細信息，包括主要網關和其他網關信息
{
  "_id": "device_id",
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "status": "online",
  "dataId": "SYS001",
  "initialized": true,
  "data": {
    "size": "10.3",
    "battery": 85,
    "rssi": -65
  },
  "primaryGateway": {
    "_id": "gateway_id",
    "name": "Primary Gateway",
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "status": "online"
  },
  "otherGateways": [
    {
      "_id": "other_gateway_id",
      "name": "Other Gateway",
      "macAddress": "GG:HH:II:JJ:KK:LL",
      "status": "online"
    }
  ],
  "lastSeen": "2025-05-10T12:34:56.789Z",
  "userId": "user_id",
  "storeId": "store_id",
  "data": { /* 設備數據 */ }
}
```

#### 設備綁定用戶API
```javascript
// POST /api/devices/{id}/bind-user
// 請求體
{
  "userId": "user_id" // 要綁定的用戶ID
}

// 響應
{
  "success": true,
  "device": { /* 設備詳情 */ }
}
```

#### 設備解除綁定API
```javascript
// POST /api/devices/{id}/unbind-user
// 不需要請求體，直接解除當前綁定

// 響應
{
  "success": true,
  "device": { /* 更新後的設備詳情 */ }
}
```

#### 設備設置主要網關API
```javascript
// POST /api/devices/{id}/set-primary-gateway
// 請求體
{
  "gatewayId": "gateway_id" // 要設置為主要網關的ID
}

// 響應
{
  "success": true,
  "device": { /* 更新後的設備詳情 */ }
}
```

#### 設備事件記錄API
```javascript
// GET /api/devices/{id}/events
// 可選查詢參數:
// - limit: 限制返回的記錄數 (默認 50)
// - skip: 跳過的記錄數，用於分頁 (默認 0)
// - eventType: 按事件類型過濾

// 響應
{
  "success": true,
  "total": 126, // 總記錄數
  "events": [
    {
      "_id": "event_id",
      "deviceId": "device_id",
      "eventType": "user_binding",
      "eventData": {
        "action": "bind",
        "userId": "user_id",
        "userName": "User Name",
        "userEmail": "<EMAIL>"
      },
      "timestamp": "2025-05-12T10:30:45.123Z",
      "deviceMac": "AA:BB:CC:DD:EE:FF",
      "storeId": "store_id"
    }
    // ... 更多事件記錄 ...
  ]
}
```javascript
// GET /api/devices/{id}/events?limit=10&skip=0&eventType=discovered
// 參數說明:
// - limit: 每頁記錄數，默認50
// - skip: 跳過記錄數，用於分頁
// - eventType: 可選，過濾特定類型的事件

// 響應
{
  "success": true,
  "total": 42,  // 總記錄數
  "events": [
    {
      "_id": "event_id_1",
      "deviceId": "device_id",
      "eventType": "discovered",
      "eventData": {
        "gatewayId": "gateway_id",
        "gatewayName": "Gateway Name"
      },
      "timestamp": "2025-05-12T09:15:30.123Z",
      "deviceMac": "AA:BB:CC:DD:EE:FF",
      "storeId": "store_id"
    },
    {
      "_id": "event_id_2",
      "deviceId": "device_id",
      "eventType": "user_binding",
      "eventData": {
        "action": "bind",
        "userId": "user_id",
        "userName": "User Name"
      },
      "timestamp": "2025-05-11T14:22:15.456Z",
      "deviceMac": "AA:BB:CC:DD:EE:FF",
      "storeId": "store_id"
    },
    {
      "_id": "event_id_3",
      "deviceId": "device_id",
      "eventType": "gateway_changed",
      "eventData": {
        "previousGatewayId": "old_gateway_id",
        "previousGatewayName": "Old Gateway Name",
        "newGatewayId": "new_gateway_id",
        "newGatewayName": "New Gateway Name"
      },
      "timestamp": "2025-05-10T18:05:42.789Z",
      "deviceMac": "AA:BB:CC:DD:EE:FF",
      "storeId": "store_id"
    }
    // ... 更多事件記錄 ...
  ]
}
```

### 6.2 與Gateway的接口

Server端通過WebSocket與Gateway通信，支持以下消息類型：

#### 接收消息：
1. **deviceStatus**：設備狀態更新
2. **gatewayInfo**：網關信息更新
3. **ping**：心跳消息

#### 發送消息：
1. **welcome**：歡迎消息
2. **pong**：心跳回應
3. **restart**：重啟命令
4. **updateFirmware**：固件更新命令

### 6.2.1 WebSocket消息處理

#### 設備狀態上報消息處理流程

1. **接收設備狀態消息**
   ```javascript
   // 網關上報設備狀態消息格式
   {
     "type": "deviceStatus",
     "gatewayId": "gateway_id",
     "storeId": "store_id",
     "devices": [
       {
         "macAddress": "AA:BB:CC:DD:EE:FF",
         "name": "Device Name",
         "type": "EPD",
         "status": "online",
         "data": { 
           "battery": 85,
           "rssi": -65,
           "size": "10.3",
           /* 其他設備數據 */ 
         }
       }
     ]
   }
   ```

2. **處理設備初始化與網關綁定邏輯**：
   - 對於每個上報的設備，檢查是否存在於系統中
   - 對於新設備，創建記錄並標記為未初始化
   - 首次被發現的未初始化設備，自動設置報告的網關為主要網關
   - 已初始化設備被其他網關發現時會將該網關添加到其他網關列表
   - 用戶可以從UI界面更改設備的主要網關

3. **響應消息**：
   ```javascript
   // 回覆網關的響應消息
   {
     "type": "deviceStatusAck",
     "timestamp": 1620000000001,
     "success": true,
     "message": "設備狀態更新成功",
     "detailStatus": [
       {
         "macAddress": "AA:BB:CC:DD:EE:FF",
         "status": "success",
         "deviceId": "device_id"  // 可選，返回系統中的設備ID
       }
     ]
   }
   ```

#### 網關網絡配置請求

當App成功註冊網關後，網關需要接收WebSocket連接信息：

```javascript
// App發送給網關的配置信息
{
  "type": "config",
  "websocket": {
    "url": "wss://server.example.com",
    "path": "/ws",
    "token": "jwt_token",
    "protocol": "json"
  }
}
```

網關收到配置後，會嘗試連接到Server的WebSocket服務，並發送身份驗證消息：

```javascript
// 網關發送給Server的身份驗證消息
{
  "type": "auth",
  "gatewayId": "gateway_id",
  "token": "jwt_token"
}
```

### 5.5 異常處理

系統需要處理以下可能的異常情況：

#### 5.5.1 數據庫連接異常

```javascript
// 數據庫連接錯誤處理
async function connectToDB() {
  try {
    const client = await MongoClient.connect(config.mongoUri, { 
      useNewUrlParser: true, 
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000 // 5秒超時
    });
    
    return { client, db: client.db(config.dbName) };
  } catch (error) {
    console.error('數據庫連接失敗:', error);
    
    // 發送警報通知
    await sendAlert('DATABASE_CONNECTION_ERROR', {
      message: '數據庫連接失敗',
      error: error.message,
      timestamp: new Date()
    });
    
    // 重試策略
    await new Promise(resolve => setTimeout(resolve, 5000));
    return connectToDB();
  }
}
```

#### 5.5.2 WebSocket連接異常

```javascript
// WebSocket錯誤處理
wss.on('error', async (error) => {
  console.error('WebSocket服務錯誤:', error);
  
  // 發送警報通知
  await sendAlert('WEBSOCKET_SERVICE_ERROR', {
    message: 'WebSocket服務錯誤',
    error: error.message,
    timestamp: new Date()
  });
  
  // 嘗試重啟WebSocket服務
  restartWebSocketService();
});

// 網關WebSocket連接錯誤處理
ws.on('error', async (error) => {
  console.error(`網關 ${gatewayId} WebSocket錯誤:`, error);
  
  // 更新網關狀態
  await updateGatewayStatus(gatewayId, 'error');
  
  // 記錄錯誤日誌
  await logError('GATEWAY_WEBSOCKET_ERROR', {
    gatewayId,
    error: error.message,
    timestamp: new Date()
  });
});
```

#### 5.5.3 設備資料處理異常

```javascript
async function processDeviceData(gateway, deviceData) {
  try {
    // 處理設備數據...
    await updateDeviceStatus(gateway.id, deviceData, gateway.storeId);
    return true;
  } catch (error) {
    console.error('處理設備數據錯誤:', error);
    
    // 記錄錯誤日誌
    await logError('DEVICE_DATA_PROCESSING_ERROR', {
      gatewayId: gateway.id,
      deviceData,
      error: error.message,
      timestamp: new Date()
    });
    
    return false;
  }
}
```

#### 5.5.4 設備事件記錄功能

```javascript
// 記錄設備事件
async function logDeviceEvent(deviceId, eventType, eventData = {}) {
  try {
    if (!deviceId || !(deviceId instanceof ObjectId || typeof deviceId === 'string')) {
      console.error('嘗試記錄設備事件時提供了無效的設備ID');
      return;
    }
    
    // 確保 deviceId 是 ObjectId 類型
    const deviceObjectId = deviceId instanceof ObjectId ? deviceId : new ObjectId(deviceId);
    
    const { db } = await getDbConnection();
    
    // 確保 deviceEvents 集合存在
    const collections = await db.listCollections({ name: 'deviceEvents' }).toArray();
    if (collections.length === 0) {
      await db.createCollection('deviceEvents');
      // 創建索引以提高查詢效率
      await db.collection('deviceEvents').createIndex({ deviceId: 1 });
      await db.collection('deviceEvents').createIndex({ timestamp: -1 });
      await db.collection('deviceEvents').createIndex({ eventType: 1 });
    }
    
    const eventCollection = db.collection('deviceEvents');
    
    // 獲取設備詳情以豐富事件數據
    const deviceCollection = db.collection('devices');
    const device = await deviceCollection.findOne({ _id: deviceObjectId });
    
    if (!device) {
      console.warn(`找不到ID為 ${deviceId} 的設備，但仍將記錄事件`);
    }
    
    // 創建標準化的事件對象
    const event = {
      deviceId: deviceObjectId,
      eventType, // 'discovered', 'user_binding', 'gateway_changed', 'data_update', 'status_change' 等
      eventData,
      timestamp: new Date(),
      deviceMac: device?.macAddress || 'unknown',
      storeId: device?.storeId || null
    };
    
    // 寫入事件紀錄
    await eventCollection.insertOne(event);
    
    console.log(`已記錄設備 ${deviceId} 的 ${eventType} 事件`);
    return event;
  } catch (error) {
    console.error(`記錄設備 ${deviceId} 事件失敗:`, error);
    return null;
  }
}

// 錯誤日誌記錄
async function logError(type, data) {
  try {
    const { db } = await getDbConnection();
    
    // 寫入錯誤日誌集合
    await db.collection('errorLogs').insertOne({
      type,
      data,
      createdAt: new Date()
    });
  } catch (error) {
    console.error('寫入錯誤日誌失敗:', error);
  }
}

// 告警通知
async function sendAlert(type, data) {
  try {
    // 根據告警類型發送不同的通知
    switch (type) {
      case 'DATABASE_CONNECTION_ERROR':
        // 發送郵件或短信通知管理員
        await sendAdminNotification({
          subject: '數據庫連接異常',
          body: `數據庫連接失敗: ${data.error}`,
          level: 'critical'
        });
        break;
        
      case 'WEBSOCKET_SERVICE_ERROR':
        // 發送系統監控通知
        await sendAdminNotification({
          subject: 'WebSocket服務異常',
          body: `WebSocket服務錯誤: ${data.error}`,
          level: 'high'
        });
        break;
        
      case 'GATEWAY_OFFLINE':
        // 通知相關門店管理員
        await sendStoreNotification(data.storeId, {
          subject: '網關離線',
          body: `網關 ${data.gatewayName} (${data.gatewayId}) 已離線，請檢查`,
          level: 'medium'
        });
        break;
    }
  } catch (error) {
    console.error('發送告警通知失敗:', error);
  }
}
```

## 7. 系統架構圖

### 7.1 整體系統架構

以下是系統整體架構圖，顯示了各組件之間的關係和數據流動：

```
┌───────────────────────────────────┐
│           客戶端應用               │
│  ┌─────────────┐  ┌─────────────┐  │
│  │    網頁應用   │  │   移動應用   │  │
│  └─────────────┘  └─────────────┘  │
└───────────────────────────────────┘
              ▲               ▲
              │               │
              ▼               ▼
┌───────────────────────────────────┐
│           負載均衡層               │
│      (Nginx/HAProxy/AWS ELB)       │
└───────────────────────────────────┘
              ▲
              │
              ▼
┌───────────────────────────────────┐
│           應用服務層               │
│  ┌─────────────┐  ┌─────────────┐  │
│  │  API服務器   │  │ WebSocket   │  │
│  │  (Express)   │  │   服務器    │  │
│  └─────────────┘  └─────────────┘  │
└───────────────────────────────────┘
              ▲
              │
              ▼
┌───────────────────────────────────┐
│           數據存儲層               │
│        ┌─────────────────┐        │
│        │     MongoDB     │        │
│        └─────────────────┘        │
└───────────────────────────────────┘
              ▲
              │
              ▼
┌───────────────────────────────────┐
│           IoT設備層                │
│  ┌─────────────┐  ┌─────────────┐  │
│  │    網關      │  │   電子標籤   │  │
│  └─────────────┘  └─────────────┘  │
└───────────────────────────────────┘
```

### 7.2 通信流程圖

#### 7.2.1 設備註冊與初始化流程

```
┌─────────┐          ┌─────────┐          ┌─────────┐          ┌─────────┐
│ App客戶端 │          │ Server  │          │ 網關設備 │          │ 電子標籤 │
└─────────┘          └─────────┘          └─────────┘          └─────────┘
     │                    │                   │                    │
     │  1.註冊網關       │                   │                    │
     │───────────────────>│                   │                    │
     │                    │                   │                    │
     │  2.返回網關信息    │                   │                    │
     │<───────────────────│                   │                    │
     │  (含WebSocket連接) │                   │                    │
     │                    │                   │                    │
     │  3.配置網關        │                   │                    │
     │───────────────────────────────────────>│                    │
     │                    │                   │                    │
     │                    │  4.WebSocket連接  │                    │
     │                    │<──────────────────│                    │
     │                    │                   │                    │
     │                    │                   │  5.掃描發現設備     │
     │                    │                   │<───────────────────│
     │                    │                   │                    │
     │                    │  6.上報設備狀態   │                    │
     │                    │<──────────────────│                    │
     │                    │                   │                    │
     │  7.查詢設備狀態    │                   │                    │
     │───────────────────>│                   │                    │
     │                    │                   │                    │
     │  8.返回設備列表    │                   │                    │
     │<───────────────────│                   │                    │
     │                    │                   │                    │
     │  9.綁定設備到用戶  │                   │                    │
     │───────────────────>│                   │                    │
     │                    │                   │                    │
     │ 10.返回綁定結果    │                   │                    │
     │<───────────────────│                   │                    │
     │                    │                   │                    │
```

#### 7.2.2 設備數據更新流程

```
┌─────────┐          ┌─────────┐          ┌─────────┐
│ 網關設備 │          │ Server  │          │ App客戶端 │
└─────────┘          └─────────┘          └─────────┘
     │                    │                    │
     │  1.設備狀態變化    │                    │
     │───────────────────>│                    │
     │                    │                    │
     │  2.確認接收        │                    │
     │<───────────────────│                    │
     │                    │                    │
     │                    │  3.WebSocket推送   │
     │                    │───────────────────>│
     │                    │                    │
     │                    │  4.查詢設備詳情    │
     │                    │<───────────────────│
     │                    │                    │
     │                    │  5.返回詳細數據    │
     │                    │───────────────────>│
     │                    │                    │
```

### 7.3 數據流圖

```
┌──────────────┐   註冊/認證   ┌──────────────┐
│              │◄─────────────►│              │
│   App客戶端   │              │   API服務器   │
│              │◄─────────────►│              │
└──────────────┘   數據查詢    └──────────────┘
                                     ▲
                                     │
                                     ▼
┌──────────────┐   WebSocket   ┌──────────────┐
│              │◄─────────────►│              │
│    網關設備   │              │ WebSocket服務 │
│              │              │              │
└──────────────┘              └──────────────┘
       ▲                              │
       │                              │
       ▼                              ▼
┌──────────────┐              ┌──────────────┐
│              │              │              │
│    電子標籤   │              │   數據庫     │
│              │              │              │
└──────────────┘              └──────────────┘
```

## 8. 測試計劃

### 8.1 單元測試

#### 8.1.1 測試範圍

單元測試將覆蓋以下關鍵組件：

1. WebSocket服務
   - 連接處理
   - 消息解析和處理
   - 錯誤處理

2. API路由
   - 請求驗證
   - 處理邏輯
   - 響應格式

3. 數據操作方法
   - 設備狀態更新
   - 網關綁定邏輯
   - 用戶設備綁定

#### 8.1.2 測試框架與工具

- **測試框架**: Jest
- **斷言庫**: Chai
- **Mock工具**: Sinon
- **覆蓋率工具**: Istanbul

#### 8.1.3 單元測試示例

```javascript
// WebSocket服務測試
describe('WebSocketService', () => {
  let mockWs;
  let mockGatewayId = 'test-gateway-id';
  
  beforeEach(() => {
    mockWs = {
      send: jest.fn(),
      close: jest.fn(),
      on: jest.fn()
    };
    // 初始化測試環境
  });
  
  test('應該處理認證成功的連接', async () => {
    const token = createMockToken(mockGatewayId);
    const result = await handleConnection(mockWs, mockGatewayId, token);
    
    expect(result).toBe(true);
    expect(mockWs.send).toHaveBeenCalledWith(expect.stringMatching(/welcome/i));
  });
  
  test('應該拒絕無效Token的連接', async () => {
    const token = 'invalid-token';
    const result = await handleConnection(mockWs, mockGatewayId, token);
    
    expect(result).toBe(false);
    expect(mockWs.close).toHaveBeenCalledWith(1008, expect.any(String));
  });
});

// 設備狀態更新測試
describe('DeviceStatusUpdater', () => {
  // 測試前準備
  beforeEach(async () => {
    // 設置測試數據庫
    await setupTestDatabase();
  });
  
  test('應該創建新設備並標記為未初始化', async () => {
    const deviceData = {
      macAddress: 'AA:BB:CC:DD:EE:FF',
      status: 'online',
      data: { battery: 90 }
    };
    
    await updateDeviceStatus('gateway1', [deviceData], 'store1');
    
    const device = await findDeviceByMac('AA:BB:CC:DD:EE:FF');
    expect(device).toBeTruthy();
    expect(device.initialized).toBe(false);
    expect(device.status).toBe('online');
  });
  
  test('應該將首次發現的設備設置為已初始化並設定主要網關', async () => {
    // 測試初始化邏輯
  });
});
```

### 8.2 整合測試

#### 8.2.1 測試範圍

整合測試將覆蓋以下系統間交互：

1. API與數據庫之間的交互
2. WebSocket與數據庫之間的交互
3. 完整的設備註冊和狀態更新流程
4. 用戶認證與權限驗證

#### 8.2.2 整合測試架構

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  測試客戶端   │◄────►│  API/WebSocket │◄────►│ 測試數據庫    │
│               │      │  (實際服務實現) │      │ (MongoDB)     │
│               │      │               │      │               │
└───────────────┘      └───────────────┘      └───────────────┘
```

#### 8.2.3 整合測試示例

```javascript
// 完整設備註冊和狀態更新流程測試
describe('設備註冊與狀態更新流程', () => {
  let app, server, client, dbConnection;
  
  beforeAll(async () => {
    // 啟動測試服務器和數據庫
    dbConnection = await setupTestDatabase();
    app = await createTestApp(dbConnection);
    server = app.listen(3000);
    client = createTestClient('http://localhost:3000');
  });
  
  afterAll(async () => {
    // 關閉資源
    await server.close();
    await dbConnection.close();
  });
  
  test('完整的設備初始化和狀態更新流程', async () => {
    // 1. 註冊網關
    const gateway = await client.registerGateway({
      name: 'Test Gateway',
      macAddress: 'AA:BB:CC:DD:EE:00',
      storeId: 'store1'
    });
    
    expect(gateway).toHaveProperty('_id');
    expect(gateway).toHaveProperty('websocket');
    
    // 2. 模擬WebSocket連接
    const ws = await createMockWebSocketConnection(gateway.websocket);
    
    // 3. 發送設備狀態更新
    const deviceData = {
      type: 'deviceStatus',
      devices: [
        {
          macAddress: 'DD:EE:FF:00:11:22',
          status: 'online',
          data: { battery: 85 }
        }
      ]
    };
    
    await ws.send(JSON.stringify(deviceData));
    
    // 4. 驗證設備是否被正確創建
    const devices = await client.getDevices({ storeId: 'store1' });
    expect(devices).toHaveLength(1);
    expect(devices[0].macAddress).toBe('DD:EE:FF:00:11:22');
    expect(devices[0].initialized).toBe(true);
    expect(devices[0].primaryGatewayId).toBe(gateway._id);
  });
});
```

### 8.3 性能測試

#### 8.3.1 測試目標

1. 確定系統在預期負載下的性能
2. 識別系統瓶頸
3. 確定系統可擴展性限制

#### 8.3.2 測試場景

1. **WebSocket連接測試**
   - 測試系統能處理的最大並發WebSocket連接數
   - 目標: 支持至少1000個並發WebSocket連接

2. **設備數據處理測試**
   - 測試系統處理大量設備狀態更新的能力
   - 目標: 每秒處理1000個設備狀態更新

3. **API吞吐量測試**
   - 測試API在高負載下的響應時間和吞吐量
   - 目標: API平均響應時間<200ms，支持100個並發用戶

#### 8.3.3 性能測試工具

- WebSocket負載測試: Artillery
- API負載測試: JMeter
- 監控: Prometheus + Grafana

#### 8.3.4 性能基準

| 測試場景 | 並發用戶/連接 | 預期吞吐量 | 預期響應時間 |
|---------|------------|---------|-----------|
| WebSocket連接 | 1,000 | N/A | <500ms 連接時間 |
| 設備狀態更新 | 100 | 1,000 更新/秒 | <100ms 處理時間 |
| API請求 | 100 | 500 請求/秒 | <200ms 響應時間 |

## 9. 部署與擴展

### 9.1 部署架構

對於生產環境，建議使用以下架構進行部署：

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  負載均衡器     │◄────►│  Server實例 1    │◄────►│  MongoDB 主節點  │
│  (Nginx/HAProxy) │      │                 │      │                 │
│                 │      └─────────────────┘      └─────────────────┘
│                 │             ▲                        ▲
│                 │             │                        │
│                 │             ▼                        ▼
│                 │      ┌─────────────────┐      ┌─────────────────┐
│                 │◄────►│  Server實例 2    │◄────►│  MongoDB 副本集  │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
        ▲
        │
        ▼
┌─────────────────┐
│                 │
│  網關/客戶端     │
│                 │
└─────────────────┘
```

### 9.2 可擴展性設計

系統設計需考慮以下可擴展性因素：

1. **水平擴展**:
   - 使用無狀態設計，允許多個Server實例並行運行
   - 使用Redis等分布式緩存存儲連接狀態
   - WebSocket連接使用粘性會話或共享狀態

2. **垂直擴展**:
   - 根據負載動態調整服務器資源
   - 使用高效的數據庫查詢和索引設計

3. **數據庫擴展**:
   - 使用MongoDB分片以支持大量設備數據
   - 考慮時序數據庫(如InfluxDB)存儲設備歷史數據

### 9.3 系統監控

推薦使用以下監控方案：

1. **服務器健康監控**:
   - 使用Prometheus收集系統指標
   - 使用Grafana進行可視化監控
   - 設置關鍵指標告警

2. **應用層監控**:
   - 使用Winston等工具進行日誌管理
   - 使用ELK Stack收集和分析日誌
   - 實現API調用跟踪和性能分析

3. **網關連接監控**:
   - 統計WebSocket連接數和消息處理速率
   - 監控網關在線率和響應時間
   - 記錄網關連接/斷開事件

4. **數據庫監控**:
   - 監控數據庫性能和查詢時間
   - 設置數據增長趨勢報告
   - 監控索引使用情況

### 9.4 備份與災難恢復

1. **數據備份策略**:
   - 每日全量備份MongoDB數據
   - 每小時增量備份
   - 保留至少30天的備份歷史

2. **災難恢復計劃**:
   - 建立跨區域備份
   - 制定恢復時間目標(RTO)和恢復點目標(RPO)
   - 定期測試恢復流程

3. **故障轉移**:
   - 使用MongoDB副本集實現自動故障轉移
   - 使用多區域部署提高可用性
   - 實現自動化故障檢測和恢復

## 10. 安全考慮

### 10.1 認證與授權

#### 10.1.1 用戶認證

```javascript
// JWT認證中間件
const authenticate = async (req, res, next) => {
  try {
    // 從請求頭獲取token
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ error: '未提供認證Token' });
    }

    // 驗證Token
    const decoded = jwt.verify(token, config.jwtSecret);
    
    // 從數據庫獲取用戶
    const { db } = await getDbConnection();
    const user = await db.collection('users').findOne({ _id: new ObjectId(decoded.userId) });
    
    if (!user) {
      return res.status(401).json({ error: '用戶不存在' });
    }
    
    // 將用戶信息附加到請求對象
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token已過期' });
    }
    
    return res.status(401).json({ error: '無效的Token' });
  }
};
```

#### 10.1.2 網關認證

```javascript
// WebSocket連接認證
wss.on('connection', async (ws, req) => {
  try {
    // 獲取URL參數
    const url = new URL(req.url, `http://${req.headers.host}`);
    const token = url.searchParams.get('token');
    
    // 驗證Token
    const decoded = jwt.verify(token, config.jwtSecret);
    
    // 檢查是否為網關類型Token
    if (decoded.type !== 'gateway') {
      ws.close(1008, '無效的Token類型');
      return;
    }
    
    // 驗證路徑中的網關ID是否與Token匹配
    const gatewayId = req.params.gatewayId;
    if (gatewayId !== decoded.gatewayId) {
      ws.close(1008, '網關ID與Token不匹配');
      return;
    }
    
    // 認證成功，設置連接屬性
    ws.gatewayId = gatewayId;
    ws.storeId = decoded.storeId;
    ws.isAuthenticated = true;
    
    // 更新網關狀態
    await updateGatewayStatus(gatewayId, 'online');
    
    // 發送歡迎消息
    ws.send(JSON.stringify({
      type: 'welcome',
      message: '認證成功',
      timestamp: Date.now()
    }));
  } catch (error) {
    console.error('WebSocket認證失敗:', error);
    ws.close(1008, '認證失敗');
  }
});
```

#### 10.1.3 基於角色的訪問控制

```javascript
// 權限檢查中間件
const checkPermission = (requiredPermissions) => {
  return async (req, res, next) => {
    try {
      const user = req.user;
      
      // 獲取用戶的角色
      const { db } = await getDbConnection();
      const role = await db.collection('roles').findOne({ name: user.role });
      
      if (!role) {
        return res.status(403).json({ error: '無效的用戶角色' });
      }
      
      // 檢查是否有所需權限
      const hasPermission = requiredPermissions.every(permission => 
        role.permissions.includes(permission)
      );
      
      if (!hasPermission) {
        return res.status(403).json({ error: '權限不足' });
      }
      
      next();
    } catch (error) {
      console.error('權限檢查失敗:', error);
      res.status(500).json({ error: '權限檢查失敗' });
    }
  };
};
```

### 10.2 數據安全

#### 10.2.1 密碼加密

```javascript
// 密碼加密
const bcrypt = require('bcrypt');

async function hashPassword(password) {
  const saltRounds = 10;
  return await bcrypt.hash(password, saltRounds);
}

async function comparePassword(plainPassword, hashedPassword) {
  return await bcrypt.compare(plainPassword, hashedPassword);
}
```

#### 10.2.2 數據驗證

```javascript
// 輸入驗證中間件
const validateDevice = (req, res, next) => {
  const { name, macAddress, type } = req.body;
  
  const errors = [];
  
  if (!name || name.trim() === '') {
    errors.push('設備名稱不能為空');
  }
  
  if (!macAddress || !isValidMacAddress(macAddress)) {
    errors.push('MAC地址格式不正確');
  }
  
  if (!type || type.trim() === '') {
    errors.push('設備類型不能為空');
  }
  
  if (errors.length > 0) {
    return res.status(400).json({ errors });
  }
  
  next();
};

function isValidMacAddress(mac) {
  return /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(mac);
}
```

#### 10.2.3 傳輸加密

```javascript
// HTTPS配置
const fs = require('fs');
const https = require('https');
const express = require('express');
const app = express();

const options = {
  key: fs.readFileSync('path/to/private.key'),
  cert: fs.readFileSync('path/to/certificate.crt'),
  ca: fs.readFileSync('path/to/ca_bundle.crt'),
  secureProtocol: 'TLSv1_2_method', // 強制使用TLS 1.2或更高版本
  ciphers: [
    'ECDHE-ECDSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES256-GCM-SHA384',
    'DHE-RSA-AES256-GCM-SHA384'
  ].join(':')
};

// WebSocket安全配置
const WebSocket = require('ws');
const wss = new WebSocket.Server({
  server: https.createServer(options, app),
  // WSS選項
  perMessageDeflate: {
    zlibDeflateOptions: {
      level: 6,
      memLevel: 8
    }
  }
});
```

### 10.3 攻擊防禦

#### 10.3.1 速率限制

```javascript
// 速率限制中間件
const rateLimit = require('express-rate-limit');

// API請求限制
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分鐘
  max: 100, // 每個IP最多100個請求
  standardHeaders: true,
  legacyHeaders: false,
  message: { error: '請求過於頻繁，請稍後再試' }
});

// 登入請求限制
const loginLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小時
  max: 10, // 每個IP最多10次嘗試
  standardHeaders: true,
  legacyHeaders: false,
  message: { error: '登入嘗試次數過多，請稍後再試' }
});

// 應用速率限制
app.use('/api/', apiLimiter);
app.use('/api/auth/login', loginLimiter);
```

#### 10.3.2 防止SQL注入

MongoDB本身具有抵抗SQL注入的特性，但仍需小心處理用戶輸入：

```javascript
// 安全處理用戶輸入的查詢參數
function sanitizeQuery(query) {
  // 過濾掉可能的JavaScript代碼執行
  if (typeof query === 'object') {
    Object.keys(query).forEach(key => {
      if (key.startsWith('$')) {
        delete query[key];
      } else if (typeof query[key] === 'object') {
        query[key] = sanitizeQuery(query[key]);
      }
    });
  }
  return query;
}

// 使用消毒過的查詢
const userInput = req.query.filter;
const safeFilter = sanitizeQuery(JSON.parse(userInput || '{}'));
const results = await collection.find(safeFilter).toArray();
```

#### 10.3.3 XSS防禦

```javascript
// 安裝依賴
// npm install helmet express-sanitizer xss

const helmet = require('helmet');
const expressSanitizer = require('express-sanitizer');
const xss = require('xss');

// 應用安全頭
app.use(helmet());

// 應用請求清理
app.use(expressSanitizer());

// 清理輸入
app.use((req, res, next) => {
  if (req.body) {
    Object.keys(req.body).forEach(key => {
      if (typeof req.body[key] === 'string') {
        req.body[key] = xss(req.body[key]);
      }
    });
  }
  next();
});
```

## 11. 總結

### 11.1 設計要點

本文檔詳細規劃了EPD管理系統的Server端實現，主要側重於以下幾個方面：

1. **資料庫設計**：
   - 增強現有的資料模型以支持設備與網關、用戶的綁定關係
   - 明確定義了各集合間的關係，並設計了適當的索引以提高查詢效能

2. **設備初始化與綁定邏輯**：
   - 當設備被新增到系統時默認為未初始化狀態
   - 首次被網關發現的設備會自動將該網關設為主要網關
   - 已初始化設備被其他網關發現時會將該網關添加到其他網關列表
   - 用戶可以從UI界面更改設備的主要網關

3. **API擴展**：
   - 提供設備綁定用戶的API
   - 提供設置設備主要網關的API
   - 增強設備詳情API，返回包含網關信息的完整數據

4. **異常處理**：
   - 數據庫連接異常處理
   - WebSocket連接異常處理
   - 設備數據處理異常處理
   - 錯誤日誌與監控

5. **安全考慮**：
   - 完善的認證與授權機制
   - 數據驗證與消毒
   - 防止常見的安全攻擊

### 11.2 實現建議

在實現本設計時，建議遵循以下步驟：

1. **更新數據庫結構**：
   - 執行遷移腳本，確保所有必要的欄位和索引都已創建
   - 驗證現有數據的兼容性

2. **擴展現有API**：
   - 在deviceApi.js中添加綁定用戶和設置主要網關的路由
   - 確保適當的權限驗證

3. **增強WebSocket處理**：
   - 實現設備初始化和網關綁定邏輯
   - 添加在線狀態檢測和自動重連機制

4. **測試與優化**：
   - 全面測試新的API和功能
   - 測試異常情況下的行為
   - 根據需要進行性能優化

### 11.3 後續擴展計劃

完成本設計後，以下是可能的擴展方向：

1. **設備分組功能**：
   - 允許用戶創建設備組並進行批量操作
   - 實現基於設備組的權限管理

2. **網關固件更新**：
   - 通過WebSocket推送固件更新指令
   - 實現固件版本管理和回滾功能

3. **高級設備數據分析**：
   - 實現設備數據的時序存儲
   - 提供數據可視化和趨勢分析

4. **多租戶支持**：
   - 擴展系統以支持多個獨立的租戶
   - 實現租戶間的數據隔離

通過實施本文檔中的設計，系統將能夠滿足設備綁定網關和用戶的需求，並為未來的擴展奠定堅實基礎。

## 12. 補充範例

### 12.1 門店特定數據API實現範例

門店模型中包含了 `storeSpecificData` 字段，這是一個數組，存儲門店特定的數據。以下是操作這些數據的API實現範例：

```javascript
// storeApi.js

// 獲取門店特定數據
router.get('/stores/:storeId/specific-data', authenticate, async (req, res) => {
  try {
    const { storeId } = req.params;
    
    // 獲取數據庫連接
    const { db } = await getDbConnection();
    const storeCollection = db.collection('stores');
    
    // 查找門店
    const store = await storeCollection.findOne({ _id: new ObjectId(storeId) });
    if (!store) {
      return res.status(404).json({ error: '門店不存在' });
    }
    
    // 返回門店特定數據
    res.json({
      success: true,
      storeSpecificData: store.storeSpecificData || []
    });
  } catch (error) {
    console.error('獲取門店特定數據失敗:', error);
    res.status(500).json({ error: '獲取門店特定數據失敗' });
  }
});

// 添加門店特定數據
router.post('/stores/:storeId/specific-data', authenticate, async (req, res) => {
  try {
    const { storeId } = req.params;
    const specificData = req.body;
    
    // 數據驗證
    if (!specificData || !specificData.id) {
      return res.status(400).json({ error: '特定數據ID不能為空' });
    }
    
    // 獲取數據庫連接
    const { db } = await getDbConnection();
    const storeCollection = db.collection('stores');
    
    // 查找門店
    const store = await storeCollection.findOne({ _id: new ObjectId(storeId) });
    if (!store) {
      return res.status(404).json({ error: '門店不存在' });
    }
    
    // 生成唯一ID
    const uid = new ObjectId();
    specificData._id = uid;
    specificData.uid = uid.toString();
    
    // 添加特定數據
    await storeCollection.updateOne(
      { _id: new ObjectId(storeId) },
      { 
        $push: { storeSpecificData: specificData },
        $set: { updatedAt: new Date() }
      }
    );
    
    // 獲取更新後的門店
    const updatedStore = await storeCollection.findOne({ _id: new ObjectId(storeId) });
    
    // 返回成功響應
    res.status(201).json({
      success: true,
      message: '門店特定數據添加成功',
      storeSpecificData: updatedStore.storeSpecificData
    });
  } catch (error) {
    console.error('添加門店特定數據失敗:', error);
    res.status(500).json({ error: '添加門店特定數據失敗' });
  }
});

// 更新門店特定數據
router.put('/stores/:storeId/specific-data/:dataId', authenticate, async (req, res) => {
  try {
    const { storeId, dataId } = req.params;
    const updatedData = req.body;
    
    // 獲取數據庫連接
    const { db } = await getDbConnection();
    const storeCollection = db.collection('stores');
    
    // 查找並更新特定數據
    const result = await storeCollection.updateOne(
      { 
        _id: new ObjectId(storeId), 
        "storeSpecificData.uid": dataId 
      },
      { 
        $set: { 
          "storeSpecificData.$": { ...updatedData, _id: new ObjectId(updatedData._id), uid: dataId },
          updatedAt: new Date()
        }
      }
    );
    
    if (result.matchedCount === 0) {
      return res.status(404).json({ error: '門店或特定數據不存在' });
    }
    
    // 獲取更新後的門店
    const updatedStore = await storeCollection.findOne({ _id: new ObjectId(storeId) });
    
    // 返回成功響應
    res.json({
      success: true,
      message: '門店特定數據更新成功',
      storeSpecificData: updatedStore.storeSpecificData
    });
  } catch (error) {
    console.error('更新門店特定數據失敗:', error);
    res.status(500).json({ error: '更新門店特定數據失敗' });
  }
});

// 刪除門店特定數據
router.delete('/stores/:storeId/specific-data/:dataId', authenticate, async (req, res) => {
  try {
    const { storeId, dataId } = req.params;
    
    // 獲取數據庫連接
    const { db } = await getDbConnection();
    const storeCollection = db.collection('stores');
    
    // 查找並刪除特定數據
    const result = await storeCollection.updateOne(
      { _id: new ObjectId(storeId) },
      { 
        $pull: { storeSpecificData: { uid: dataId } },
        $set: { updatedAt: new Date() }
      }
    );
    
    if (result.matchedCount === 0) {
      return res.status(404).json({ error: '門店不存在' });
    }
    
    // 獲取更新後的門店
    const updatedStore = await storeCollection.findOne({ _id: new ObjectId(storeId) });
    
    // 返回成功響應
    res.json({
      success: true,
      message: '門店特定數據刪除成功',
      storeSpecificData: updatedStore.storeSpecificData || []
    });
  } catch (error) {
    console.error('刪除門店特定數據失敗:', error);
    res.status(500).json({ error: '刪除門店特定數據失敗' });
  }
});
```
