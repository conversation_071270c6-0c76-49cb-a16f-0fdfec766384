import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, AlertCircle } from 'lucide-react';
import { Gateway } from '../types/gateway';
import { updateGateway } from '../utils/api/gatewayApi';

interface EditGatewayModalProps {
  isOpen: boolean;
  gateway: Gateway | null;
  storeId?: string; // 當前選中的門店ID
  onClose: () => void;
  onSuccess: () => void;
}

export const EditGatewayModal: React.FC<EditGatewayModalProps> = ({ isOpen, gateway, storeId, onClose, onSuccess }) => {
  const { t } = useTranslation();
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 當選中的網關變更時，更新表單
  useEffect(() => {
    if (gateway) {
      setName(gateway.name || '');
    }
  }, [gateway]);

  // 處理表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!gateway) {
      setError(t('gateways.noGatewaySelected'));
      return;
    }

    // 驗證表單
    if (!name.trim()) {
      setError(t('gateways.nameRequired'));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // 檢查網關ID是否有效
      if (!gateway._id) {
        setError('網關ID無效');
        return;
      }

      // 更新網關，傳遞門店ID，使用 _id 作為識別符
      await updateGateway(
        gateway._id,
        {
          name,
        },
        storeId || gateway.storeId // 使用傳入的storeId或網關自身的storeId
      );

      // 通知成功
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('更新網關失敗:', err);
      setError(err.message || t('gateways.updateFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 如果模態窗口未開啟或沒有選中的網關，不渲染任何內容
  if (!isOpen || !gateway) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">{t('gateways.editGateway')}</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          {/* 錯誤提示 */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700 flex items-center">
              <AlertCircle className="mr-2" size={20} />
              <span>{error}</span>
            </div>
          )}

          {/* 網關名稱 */}
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              {t('gateways.name')} <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={t('gateways.namePlaceholder')}
              required
            />
          </div>

          {/* MAC 地址（唯讀） */}
          <div className="mb-4">
            <label htmlFor="macAddress" className="block text-sm font-medium text-gray-700 mb-1">
              {t('gateways.macAddress')}
            </label>
            <input
              type="text"
              id="macAddress"
              value={gateway.macAddress}
              className="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md"
              readOnly
            />
            <p className="mt-1 text-xs text-gray-500">
              {t('gateways.macAddressReadOnly')}
            </p>
          </div>

          {/* 型號（唯讀） */}
          <div className="mb-4">
            <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
              {t('gateways.model')}
            </label>
            <input
              type="text"
              id="model"
              value={gateway.model || 'Unknown'}
              className="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md"
              readOnly
            />
          </div>

          {/* IP 地址（唯讀） */}
          <div className="mb-4">
            <label htmlFor="ipAddress" className="block text-sm font-medium text-gray-700 mb-1">
              {t('gateways.ipAddress')}
            </label>
            <input
              type="text"
              id="ipAddress"
              value={gateway.ipAddress || '未知'}
              className="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md"
              readOnly
            />
          </div>

          {/* 按鈕 */}
          <div className="flex justify-end gap-2 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={loading}
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-75 disabled:cursor-not-allowed"
              disabled={loading}
            >
              {loading ? t('common.processing') : t('common.confirm')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
