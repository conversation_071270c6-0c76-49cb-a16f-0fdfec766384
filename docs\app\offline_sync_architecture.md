# APP 數據同步與離線操作機制

## 1. 初次登入流程

```mermaid
flowchart TD
    A[開始] --> B[用戶輸入帳號密碼]
    B --> C[驗證登入]
    C --> D{驗證成功?}
    D -->|否| B
    D -->|是| E[獲取 Token]
    E --> F[從 MongoDB 同步數據]
    F --> G[存入 Redux + Redux Persist]
    G --> H[大型數據存入 AsyncStorage]
    H --> I[完成初始化]
    I --> J[結束]
```

### 代碼實現
```javascript
const loginAndSync = async (username, password) => {
  try {
    // 1. 登入獲取 token
    const authResponse = await api.login(username, password);
    
    // 2. 從 MongoDB 同步必要數據
    const syncData = {
      storeData: await api.getStoreData(),
      userSettings: await api.getUserSettings(),
      tagTemplates: await api.getTemplates(),
    };

    // 3. 存儲到 Redux + Redux Persist
    dispatch(setAuthData(authResponse));
    dispatch(setSyncedData(syncData));
    
    // 4. 大型數據存入 AsyncStorage
    await AsyncStorage.setItem('offlineData', JSON.stringify({
      lastSync: new Date().toISOString(),
      data: syncData
    }));
  } catch (error) {
    console.error('Login and sync failed:', error);
  }
}
```

## 2. 本地操作流程

```mermaid
flowchart TD
    A[開始] --> B[用戶操作]
    B --> C[更新本地狀態]
    C --> D[記錄待同步操作]
    D --> E{是否在線?}
    E -->|是| F[立即同步到服務器]
    E -->|否| G[存入待同步隊列]
    F --> H[結束]
    G --> H[結束]
```

### 代碼實現
```javascript
const handleLocalOperation = async (operation) => {
  try {
    // 1. 更新本地狀態
    dispatch(updateLocalState(operation));
    
    // 2. 記錄待同步操作
    const pendingOps = await AsyncStorage.getItem('pendingOperations') || '[]';
    const operations = JSON.parse(pendingOps);
    operations.push({
      type: operation.type,
      data: operation.data,
      timestamp: new Date().toISOString()
    });
    await AsyncStorage.setItem('pendingOperations', JSON.stringify(operations));

    // 3. 在線則立即同步
    if (isOnline) {
      syncWithServer();
    }
  } catch (error) {
    console.error('Local operation failed:', error);
  }
}
```

## 3. 數據同步流程

```mermaid
flowchart TD
    A[開始] --> B[獲取待同步操作]
    B --> C[批量同步到 MongoDB]
    C --> D{同步成功?}
    D -->|是| E[清除已同步操作]
    D -->|否| F[保留待同步操作]
    E --> G[更新最後同步時間]
    F --> H[等待下次同步]
    G --> I[結束]
    H --> I[結束]
```

### 代碼實現
```javascript
const syncWithServer = async () => {
  try {
    // 1. 獲取待同步操作
    const pendingOps = JSON.parse(
      await AsyncStorage.getItem('pendingOperations')
    ) || [];

    // 2. 批量同步到 MongoDB
    const syncResults = await api.batchSync(pendingOps);

    // 3. 清除已同步的操作
    await AsyncStorage.setItem('pendingOperations', '[]');

    // 4. 更新最後同步時間
    await AsyncStorage.setItem('lastSyncTime', new Date().toISOString());
  } catch (error) {
    console.error('Sync failed:', error);
  }
}
```

## 4. 離線處理流程

```mermaid
flowchart TD
    A[開始] --> B[監聽網絡狀態]
    B --> C{網絡連接?}
    C -->|是| D[執行待同步操作]
    C -->|否| E[啟用離線模式]
    D --> F[更新同步狀態]
    E --> G[繼續本地操作]
    F --> H[結束]
    G --> H[結束]
```

### 代碼實現
```javascript
const handleOfflineMode = () => {
  NetInfo.addEventListener(state => {
    if (state.isConnected) {
      // 重新連接時同步
      syncWithServer();
    } else {
      // 離線時啟用本地模式
      dispatch(setOfflineMode(true));
    }
  });
}
```

## 5. 數據衝突處理流程

```mermaid
flowchart TD
    A[開始] --> B{比較版本號}
    B -->|本地版本高| C[使用本地數據]
    B -->|服務器版本高| D[更新本地數據]
    B -->|版本相同| E[合併數據]
    C --> F[完成衝突處理]
    D --> F
    E --> F
    F --> G[結束]
```

### 代碼實現
```javascript
const handleSyncConflict = async (localData, serverData) => {
  if (localData.version > serverData.version) {
    return localData;
  } else if (serverData.version > localData.version) {
    dispatch(updateLocalData(serverData));
    return serverData;
  } else {
    return mergeData(localData, serverData);
  }
}
```

## 注意事項

1. **數據安全**
   - 敏感數據需加密存儲
   - 定期清理過期數據

2. **性能優化**
   - 控制本地存儲大小
   - 批量處理同步操作

3. **用戶體驗**
   - 顯示同步狀態
   - 提供手動同步選項

4. **錯誤處理**
   - 同步失敗重試機制
   - 衝突解決策略

5. **備份機制**
   - 定期備份本地數據
   - 提供數據恢復功能