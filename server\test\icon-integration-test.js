const { regeneratePreviewBeforeSend } = require('../services/previewService');
const fs = require('fs');
const path = require('path');

console.log('=== 開始測試圖標集成功能 ===\n');

// 創建一個包含圖標的測試模板
const testTemplate = {
  id: 'test-icon-template',
  name: '圖標測試模板',
  width: 200,
  height: 200,
  color: 'BW',
  elements: [
    {
      id: 'icon1',
      type: 'icon',
      x: 50,
      y: 50,
      width: 40,
      height: 40,
      iconType: 'star',
      lineColor: '#000000',
      lineWidth: 2
    },
    {
      id: 'icon2',
      type: 'icon',
      x: 110,
      y: 50,
      width: 40,
      height: 40,
      iconType: 'heart',
      lineColor: '#000000',
      lineWidth: 2
    },
    {
      id: 'icon3',
      type: 'icon',
      x: 50,
      y: 110,
      width: 40,
      height: 40,
      iconType: 'alert-circle',
      lineColor: '#000000',
      lineWidth: 2
    },
    {
      id: 'icon4',
      type: 'icon',
      x: 110,
      y: 110,
      width: 40,
      height: 40,
      iconType: 'home',
      lineColor: '#000000',
      lineWidth: 2
    }
  ]
};

async function testIconIntegration() {
  try {
    console.log('生成包含圖標的預覽圖...');

    // 創建模擬設備數據
    const mockDevice = {
      _id: 'test-device-id',
      templateId: testTemplate.id,
      dataBindings: {},
      storeId: 'test-store-id'
    };

    // 生成預覽圖
    const previewData = await regeneratePreviewBeforeSend(
      mockDevice,
      [], // storeData
      testTemplate,
      [] // dataFields
    );
    
    if (previewData) {
      console.log('✓ 預覽圖生成成功');
      console.log(`預覽圖數據長度: ${previewData.length} bytes`);

      // 將預覽圖保存到文件以便檢查
      const outputPath = path.join(__dirname, 'icon-test-output.png');

      // 從 data URL 中提取 base64 數據
      const base64Data = previewData.replace(/^data:image\/png;base64,/, '');
      const imageBuffer = Buffer.from(base64Data, 'base64');
      fs.writeFileSync(outputPath, imageBuffer);

      console.log(`✓ 預覽圖已保存到: ${outputPath}`);
      console.log('請檢查生成的圖片以確認圖標是否正確渲染');

    } else {
      console.log('✗ 預覽圖生成失敗');
    }
    
  } catch (error) {
    console.error('測試過程中發生錯誤:', error);
  }
}

// 測試不同的圖標類型
async function testDifferentIcons() {
  const { getSupportedIconTypes } = require('../utils/iconRenderer');
  const supportedIcons = getSupportedIconTypes();
  
  console.log('\n=== 測試不同圖標類型 ===');
  console.log(`支援的圖標類型: ${supportedIcons.join(', ')}`);
  
  // 創建包含多種圖標的模板
  const multiIconTemplate = {
    id: 'multi-icon-template',
    name: '多圖標測試模板',
    width: 300,
    height: 200,
    color: 'BW',
    elements: []
  };
  
  // 添加前8個圖標進行測試
  const testIcons = supportedIcons.slice(0, 8);
  testIcons.forEach((iconType, index) => {
    const x = (index % 4) * 70 + 20;
    const y = Math.floor(index / 4) * 80 + 20;
    
    multiIconTemplate.elements.push({
      id: `icon-${index}`,
      type: 'icon',
      x: x,
      y: y,
      width: 50,
      height: 50,
      iconType: iconType,
      lineColor: '#000000',
      lineWidth: 2
    });
  });
  
  try {
    console.log(`生成包含 ${testIcons.length} 個不同圖標的預覽圖...`);

    // 創建模擬設備數據
    const mockDevice = {
      _id: 'multi-icon-device-id',
      templateId: multiIconTemplate.id,
      dataBindings: {},
      storeId: 'test-store-id'
    };

    const previewData = await regeneratePreviewBeforeSend(
      mockDevice,
      [], // storeData
      multiIconTemplate,
      [] // dataFields
    );
    
    if (previewData) {
      console.log('✓ 多圖標預覽圖生成成功');
      
      // 保存多圖標測試結果
      const outputPath = path.join(__dirname, 'multi-icon-test-output.png');
      const base64Data = previewData.replace(/^data:image\/png;base64,/, '');
      const imageBuffer = Buffer.from(base64Data, 'base64');
      fs.writeFileSync(outputPath, imageBuffer);
      
      console.log(`✓ 多圖標預覽圖已保存到: ${outputPath}`);
      
    } else {
      console.log('✗ 多圖標預覽圖生成失敗');
    }
    
  } catch (error) {
    console.error('多圖標測試過程中發生錯誤:', error);
  }
}

// 執行測試
async function runAllTests() {
  await testIconIntegration();
  await testDifferentIcons();
  console.log('\n=== 測試完成 ===');
}

runAllTests();
