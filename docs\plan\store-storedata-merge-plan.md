# Store 和 StoreData 合併計劃

## 背景

目前系統中的 `Store` 和 `StoreData` 是兩個獨立的集合，這導致每個門店的數據分散在不同的集合中，不利於數據管理和查詢。本計劃旨在將 `StoreData` 合併到 `Store` 集合中，使每個門店能夠擁有自己獨立的數據。

## 目標結構

```javascript
// 合併後的 Store 集合結構
{
  _id: ObjectId('680e4f1422fd791e74bce226'),
  id: 'TP001',                               // 門店編號
  name: '台北總店',                           // 門店名稱
  address: '台北市信義區101號',               // 門店地址
  phone: '02-12345678',                      // 門店電話
  managerId: '60d5ec9af682727e4e0a012c',      // 門店管理員的使用者ID
  status: 'active',                          // 門店狀態
  createdAt: '2025-04-27T15:36:52.099Z',     // 創建時間
  updatedAt: '2025-04-27T15:36:52.099Z',     // 更新時間

  // 新增字段
  storeSpecificData: [                       // 門店專屬數據（原 StoreData）
    {
      _id: ObjectId('680e4f3f22fd791e74bce233'),
      sn: 1,                                 // 序號
      id: '22',                              // 商品 ID
      name: '123',                           // 商品名稱
      description: '123',                    // 商品描述
      price: '123',                          // 價格
      quantity: '123',                       // 數量
      date: '213'                            // 日期
      // 其他動態欄位...
    }
    // 可以有多筆門店專屬數據...
  ],
  gatewayManagement: {},                     // 網關管理數據（預留）
  deviceManagement: {},                      // 設備管理數據（預留）
  storeSettings: {}                          // 門店設置（預留）
}
```

## 修改步驟

### 階段一：準備工作

1. **備份數據**
   - 備份現有的 `stores` 和 `storeData` 集合
   - 創建測試環境進行驗證

2. **更新類型定義**
   - 修改 `src/types/store.ts` 中的 `Store` 接口，添加新字段
   - 創建 `StoreSpecificData` 接口（原 `StoreData`）
   - 更新相關的類型引用

3. **階段一測試**
   - 確認備份數據的完整性
   - 使用 TypeScript 編譯器檢查類型定義是否有錯誤
   - 確認現有代碼在新類型定義下能夠正常編譯

### 階段二：後端 API 修改

4. **修改 Store API**
   - 更新 `server/routes/storeApi.js`
   - 創建門店時初始化 `storeSpecificData` 為空陣列
   - 修改獲取門店的邏輯，確保返回完整結構

5. **重構 StoreData API**
   - 修改 `server/routes/storeDataApi.js`
   - 將所有操作重定向到 `stores` 集合的 `storeSpecificData` 字段
   - 實現對 `storeSpecificData` 陣列的 CRUD 操作

6. **階段二測試**
   - 編寫單元測試確認 Store API 的修改是否正確
   - 測試創建門店時是否正確初始化 `storeSpecificData` 字段
   - 測試 StoreData API 是否能正確操作 `storeSpecificData` 陣列
   - 使用 Postman 或類似工具手動測試 API 端點

### 階段三：前端 API 調用修改

7. **更新前端 API 調用**
   - 修改 `src/utils/api/storeApi.ts`
   - 修改 `src/utils/api/storeDataApi.ts`
   - 確保 API 調用適應新的數據結構

8. **階段三測試**
   - 使用 Jest 或類似工具編寫單元測試
   - 測試 API 調用是否能正確處理新的數據結構
   - 模擬 API 響應，確保前端能正確解析數據
   - 檢查 TypeScript 類型是否正確應用

### 階段四：前端組件修改

9. **更新數據顯示組件**
   - 修改 `src/components/DatabasePage.tsx`
   - 修改 `src/components/StoreOverviewPage.tsx`
   - 其他顯示門店數據的組件

10. **更新數據編輯組件**
    - 修改 `src/components/AddStoreDataModal.tsx`
    - 修改 `src/components/EditStoreDataModal.tsx`
    - 其他編輯門店數據的組件

11. **階段四測試**
    - 使用 React Testing Library 或類似工具編寫組件測試
    - 測試組件是否能正確顯示新的數據結構
    - 測試編輯功能是否能正確保存數據
    - 進行手動 UI 測試，確保用戶體驗一致
    - 測試不同數據狀態下的組件行為（空數據、大量數據等）

### 階段五：數據遷移

12. **創建數據遷移腳本**
    - 編寫腳本將 `storeData` 集合中的數據遷移到 `stores` 集合
    - 確保數據關聯正確

13. **執行數據遷移**
    - 在測試環境中執行遷移腳本
    - 驗證數據完整性

14. **階段五測試**
    - 比較遷移前後的數據數量，確保沒有數據丟失
    - 檢查隨機抽樣的數據，確保內容正確
    - 測試遷移腳本的錯誤處理和恢復機制
    - 測試遷移後的系統功能是否正常
    - 測試遷移腳本的性能，評估在生產環境中的運行時間

### 階段六：測試與部署

15. **全面測試**
    - 測試所有門店相關功能
    - 確保數據顯示和編輯正常
    - 進行端到端測試，模擬真實用戶操作
    - 測試不同權限用戶的功能訪問
    - 進行性能測試，確保系統在高負載下仍能正常運行

16. **部署到生產環境**
    - 備份生產數據
    - 執行遷移腳本
    - 部署新代碼
    - 監控系統運行狀況

17. **部署後測試**
    - 驗證生產環境中的數據是否正確
    - 測試關鍵功能是否正常運行
    - 收集用戶反饋
    - 監控系統性能和錯誤日誌

## 詳細實施計劃

### 1. 更新類型定義

#### 修改 `src/types/store.ts`

```typescript
// 門店數據模型
export interface Store {
  _id?: string;
  id: string;        // 門店編號，如 TP001
  name: string;      // 門店名稱
  address: string;   // 門店地址
  phone?: string;    // 門店電話
  manager?: string;  // 門店負責人
  status?: 'active' | 'inactive'; // 門店狀態
  createdAt?: string; // 創建時間
  updatedAt?: string; // 更新時間

  // 新增字段
  storeSpecificData?: StoreSpecificData[];
  gatewayManagement?: Record<string, any>;
  deviceManagement?: Record<string, any>;
  storeSettings?: Record<string, any>;
}

// 門店專屬數據（原 StoreData）
export interface StoreSpecificData {
  _id?: string;
  sn: number;        // 序號
  id?: string;       // 商品 ID
  name?: string;     // 商品名稱
  description?: string; // 商品描述
  price?: string;    // 價格
  quantity?: string; // 數量
  date?: string;     // 日期
  [key: string]: any; // 動態欄位
}
```

#### 修改 `src/types.ts`（如果需要）

```typescript
// 可能需要更新或棄用原有的 StoreData 接口
// 或者添加類型別名以保持向後兼容
import { StoreSpecificData } from './types/store';

// 類型別名，保持向後兼容
export type StoreData = StoreSpecificData;
```

### 2. 修改 Store API

#### 更新 `server/routes/storeApi.js`

```javascript
// 創建門店
router.post('/stores', authenticate, checkPermission('store:create'), async (req, res) => {
  try {
    const storeData = req.body;

    // 驗證必填字段
    if (!storeData.id || !storeData.name) {
      return res.status(400).json({ error: '門店 ID 和名稱為必填項' });
    }

    const { collection } = await getCollection();

    // 檢查 ID 是否重複
    const existingStore = await collection.findOne({ id: storeData.id });
    if (existingStore) {
      return res.status(400).json({ error: '門店 ID 已存在' });
    }

    // 添加創建時間和更新時間
    const now = new Date().toISOString();
    const newStore = {
      ...storeData,
      status: storeData.status || 'active',
      createdAt: now,
      updatedAt: now,
      // 初始化新字段
      storeSpecificData: [],
      gatewayManagement: {},
      deviceManagement: {},
      storeSettings: {}
    };

    const result = await collection.insertOne(newStore);
    res.status(201).json({ ...newStore, _id: result.insertedId });
  } catch (error) {
    console.error('創建門店失敗:', error);
    res.status(500).json({ error: '創建門店失敗' });
  }
});
```

### 3. 重構 StoreData API

#### 修改 `server/routes/storeDataApi.js`

```javascript
// 獲取所有門店專屬數據
router.get('/storeData', authenticate, checkPermission('store:view'), async (req, res) => {
  try {
    const { storeId } = req.query;
    const { collection } = await getCollection();

    console.log('獲取門店專屬數據請求，storeId:', storeId);

    if (storeId) {
      // 獲取特定門店的專屬數據
      const store = await collection.findOne({ id: storeId });
      if (!store) {
        return res.status(404).json({ error: '門店不存在' });
      }

      // 返回門店專屬數據
      res.json(store.storeSpecificData || []);
    } else {
      // 獲取所有門店的專屬數據（扁平化）
      const stores = await collection.find().toArray();
      const allStoreData = [];

      stores.forEach(store => {
        if (store.storeSpecificData && store.storeSpecificData.length > 0) {
          // 為每個數據項添加 storeId
          const storeDataWithId = store.storeSpecificData.map(item => ({
            ...item,
            storeId: store.id
          }));
          allStoreData.push(...storeDataWithId);
        }
      });

      res.json(allStoreData);
    }
  } catch (error) {
    console.error('獲取門店專屬數據失敗:', error);
    res.status(500).json({ error: '獲取門店專屬數據失敗' });
  }
});

// 創建門店專屬數據
router.post('/storeData', authenticate, checkPermission('store:create'), async (req, res) => {
  try {
    const storeData = req.body;
    const storeId = storeData.storeId;

    if (!storeId) {
      return res.status(400).json({ error: '門店 ID 為必填項' });
    }

    const { collection, dataFieldsCol } = await getCollection();

    // 獲取門店
    const store = await collection.findOne({ id: storeId });
    if (!store) {
      return res.status(404).json({ error: '門店不存在' });
    }

    // 獲取一般資料欄位定義
    const dataFields = await dataFieldsCol.find({ section: "ordinary" }).toArray();

    // 獲取當前最大的 sn 值
    const storeSpecificData = store.storeSpecificData || [];
    const lastRecord = storeSpecificData.length > 0
      ? storeSpecificData.reduce((max, item) => item.sn > max ? item.sn : max, 0)
      : 0;
    const nextSn = lastRecord + 1;

    // 初始化門店專屬數據
    const newStoreData = {
      _id: new ObjectId(),
      sn: nextSn
    };

    // 根據資料欄位定義添加欄位
    dataFields.forEach(field => {
      newStoreData[field.id] = storeData[field.id] !== undefined ? storeData[field.id] : null;
    });

    // 添加到門店的 storeSpecificData 陣列
    await collection.updateOne(
      { id: storeId },
      { $push: { storeSpecificData: newStoreData } }
    );

    res.status(201).json(newStoreData);
  } catch (error) {
    console.error('創建門店專屬數據失敗:', error);
    res.status(500).json({ error: '創建門店專屬數據失敗' });
  }
});
```

### 4. 更新前端 API 調用

#### 修改 `src/utils/api/storeDataApi.ts`

```typescript
// 獲取所有門店專屬數據
export async function getAllStoreData(storeId?: string): Promise<StoreSpecificData[]> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 如果提供了 storeId，則添加到查詢參數
    const url = storeId
      ? `${buildEndpointUrl('storeData')}?storeId=${encodeURIComponent(storeId)}`
      : buildEndpointUrl('storeData');

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('獲取門店專屬數據失敗:', error);
    throw error;
  }
}
```

### 5. 數據遷移腳本

#### 創建 `server/scripts/migrateStoreData.js`

```javascript
const { MongoClient } = require('mongodb');

// MongoDB 連接信息
const uri = 'mongodb://127.0.0.1:27017';
const dbName = 'resourceManagement';

async function migrateStoreData() {
  let client;

  try {
    console.log('開始遷移門店數據...');

    // 連接數據庫
    client = new MongoClient(uri);
    await client.connect();
    console.log('MongoDB 連接成功');

    const db = client.db(dbName);

    // 獲取所有門店
    const stores = await db.collection('stores').find().toArray();
    console.log(`找到 ${stores.length} 個門店`);

    // 獲取所有門店數據
    const storeData = await db.collection('storeData').find().toArray();
    console.log(`找到 ${storeData.length} 筆門店數據`);

    // 遍歷門店，將對應的門店數據添加到 storeSpecificData 字段
    for (const store of stores) {
      // 找出屬於該門店的數據
      const specificData = storeData.filter(data => data.storeId === store.id);
      console.log(`門店 ${store.id} (${store.name}) 有 ${specificData.length} 筆數據`);

      // 更新門店
      await db.collection('stores').updateOne(
        { _id: store._id },
        {
          $set: {
            storeSpecificData: specificData,
            gatewayManagement: {},
            deviceManagement: {},
            storeSettings: {}
          }
        }
      );

      console.log(`門店 ${store.id} (${store.name}) 更新成功`);
    }

    console.log('數據遷移完成');

    // 驗證遷移結果
    const updatedStores = await db.collection('stores').find().toArray();
    let totalDataCount = 0;

    for (const store of updatedStores) {
      const dataCount = store.storeSpecificData ? store.storeSpecificData.length : 0;
      totalDataCount += dataCount;
      console.log(`門店 ${store.id} (${store.name}) 現有 ${dataCount} 筆數據`);
    }

    console.log(`總共遷移了 ${totalDataCount} 筆門店數據`);

  } catch (error) {
    console.error('數據遷移失敗:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('MongoDB 連接已關閉');
    }
  }
}

// 執行遷移
migrateStoreData();
```

## 注意事項

1. **數據備份**：在進行任何修改前，確保備份現有數據
2. **測試環境**：先在測試環境中進行修改和測試
3. **向後兼容**：考慮保持一定程度的向後兼容，以減少對現有功能的影響
4. **分階段實施**：按照計劃分階段實施，每個階段完成後進行測試
5. **監控系統**：部署後密切監控系統，確保功能正常

## 回滾計劃

如果在實施過程中遇到問題，可以按照以下步驟回滾：

1. 恢復備份的數據
2. 回滾代碼到修改前的版本
3. 重新啟動服務

## 時間估計

- 準備工作：1 天
- 後端 API 修改：2 天
- 前端 API 調用修改：1 天
- 前端組件修改：2 天
- 數據遷移：1 天
- 測試與部署：2 天

總計：約 9 個工作日

## 測試計劃摘要

每個階段的測試重點：

1. **階段一測試**：確保類型定義正確，不影響現有代碼編譯
2. **階段二測試**：確保後端 API 能正確處理新的數據結構
3. **階段三測試**：確保前端 API 調用能正確處理新的數據結構
4. **階段四測試**：確保前端組件能正確顯示和編輯新的數據結構
5. **階段五測試**：確保數據遷移的完整性和正確性
6. **階段六測試**：全面測試系統功能和性能

測試方法包括：
- 單元測試：使用 Jest 等工具測試單個函數和組件
- 集成測試：測試多個組件或模塊的交互
- 端到端測試：模擬真實用戶操作
- 手動測試：開發人員和測試人員進行功能驗證
- 性能測試：評估系統在高負載下的表現
