docker run -d -p 27017:27017 --name mongodb -v ./:/data/db mongo

docker update --restart always mongodb

docker run -d --restart always --name mongodb -p 27017:27017 -v ./db:/data/db mongo


docker exec -it mongodb mongosh




當你使用以下指令進入 MongoDB Shell (`mongosh`) 之後：  
```sh
docker exec -it mongodb mongosh
```
你可以開始操作 MongoDB 資料庫！以下是常見的基本指令：

---

## **1️⃣ 查看當前資料庫**
```js
show dbs
```
👉 顯示 MongoDB 內所有的資料庫。

---

## **2️⃣ 切換或建立新資料庫**
```js
use mydatabase
```
👉 如果 `mydatabase` 存在，則切換過去；如果不存在，則會在**第一次插入資料時**自動建立。

---

## **3️⃣ 建立與查看集合（表格）**
### **建立集合**
```js
db.createCollection("users")
```
👉 創建 `users` 集合（類似 SQL 的表）。

### **查看所有集合**
```js
show collections
```
👉 顯示目前資料庫中的所有集合。

---

## **4️⃣ 插入資料**
### **插入單筆資料**
```js
db.users.insertOne({ name: "Alice", age: 25, email: "<EMAIL>" })
```

### **插入多筆資料**
```js
db.users.insertMany([
  { name: "Bob", age: 30 },
  { name: "Charlie", age: 35, email: "<EMAIL>" }
])
```

---

## **5️⃣ 查詢資料**
### **查詢所有資料**
```js
db.users.find()
```

### **格式化顯示**
```js
db.users.find().pretty()
```

### **條件查詢**
```js
db.users.find({ age: 30 })
```
👉 找出 `age` 為 30 的用戶。

---

## **6️⃣ 更新資料**
### **更新單筆**
```js
db.users.updateOne(
  { name: "Alice" }, 
  { $set: { age: 26 } }
)
```

### **更新多筆**
```js
db.users.updateMany(
  { age: { $lt: 30 } }, 
  { $set: { status: "young" } }
)
```
👉 更新所有 `age < 30` 的用戶，新增 `status: "young"` 欄位。

---

## **7️⃣ 刪除資料**
### **刪除單筆**
```js
db.users.deleteOne({ name: "Charlie" })
```

### **刪除多筆**
```js
db.users.deleteMany({ age: { $gt: 30 } })
```
👉 刪除 `age > 30` 的用戶。

---

## **8️⃣ 刪除集合或資料庫**
### **刪除集合**
```js
db.users.drop()
```
👉 刪除 `users` 集合。

### **刪除整個資料庫**
```js
db.dropDatabase()
```
👉 刪除當前資料庫。

---

## **9️⃣ 退出 MongoDB**
```js
exit
```
👉 離開 `mongosh`，回到 Docker 容器的 Shell 或主機端。

---

### 🎯 **總結**
- **`show dbs`** → 查看所有資料庫  
- **`use mydb`** → 切換（或建立）資料庫  
- **`db.createCollection("users")`** → 建立集合（表）  
- **`db.users.insertOne({...})`** → 插入資料  
- **`db.users.find()`** → 查詢資料  
- **`db.users.updateOne({...}, {...})`** → 更新資料  
- **`db.users.deleteOne({...})`** → 刪除資料  
- **`exit`** → 退出 `mongosh`  

你可以試試這些指令 🚀！有問題隨時問我～ 😊



# debug
mongo
use resourceManagement
db.fs.files.find().pretty()