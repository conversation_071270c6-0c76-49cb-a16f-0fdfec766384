// tests/helpers/mockSysConfigApi.js
/**
 * 系統配置 API 的模擬路由
 */
const express = require('express');

// 創建模擬路由
function createMockSysConfigApi(mockCollection) {
  const router = express.Router();
  
  // 初始化資料庫連接函數
  router.initDB = jest.fn();

  // 獲取所有系統配置
  router.get('/sysConfig', async (req, res) => {
    try {
      const configs = await mockCollection.find().toArray();
      res.json(configs);
    } catch (error) {
      res.status(500).json({ error: '獲取系統配置失敗' });
    }
  });
  
  // 獲取指定類型的系統配置
  router.get('/sysConfig/:type', async (req, res) => {
    try {
      const { type } = req.params;
      const config = await mockCollection.findOne({ type });
      res.json(config || {});
    } catch (error) {
      res.status(500).json({ error: '獲取系統配置失敗' });
    }
  });
  
  // 新增或更新系統配置
  router.post('/sysConfig', async (req, res) => {
    try {
      const { type } = req.body;
      const existingConfig = await mockCollection.findOne({ type });
      
      if (existingConfig) {
        // 更新現有配置
        await mockCollection.updateOne({ type }, { $set: { config: req.body.config } });
        res.json({ message: '系統配置更新成功' });
      } else {
        // 創建新配置
        const result = await mockCollection.insertOne(req.body);
        res.status(201).json({ id: result.insertedId });
      }
    } catch (error) {
      res.status(500).json({ error: '保存系統配置失敗' });
    }
  });
  
  return router;
}

module.exports = createMockSysConfigApi;
