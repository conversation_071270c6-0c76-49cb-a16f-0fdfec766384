import { DataField } from '../../types';
import { StoreSpecificData } from '../../types/store';
import { buildEndpointUrl } from './apiConfig';
import { useAuthStore } from '../../store/authStore';

// 使用 StoreSpecificData 作為主要類型
import { StoreData } from '../../types';

// 獲取所有門店資料
export async function getAllStoreData(storeId?: string): Promise<StoreData[]> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 如果提供了 storeId，則添加到查詢參數
    const url = storeId
      ? `${buildEndpointUrl('storeData')}?storeId=${encodeURIComponent(storeId)}`
      : buildEndpointUrl('storeData');

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }

    // 獲取數據
    const data = await response.json();

    // 確保返回的數據是數組
    if (!Array.isArray(data)) {
      console.warn('API 返回的數據不是數組格式，可能是新的數據結構');
      return [];
    }

    return data;
  } catch (error) {
    console.error('獲取門店資料失敗:', error);
    throw error;
  }
}

// 檢查門店特定數據 ID 是否已存在於特定門店中
export async function checkStoreIdExists(id: string, storeId: string): Promise<boolean> {
  try {
    // 獲取特定門店的數據
    const storeData = await getAllStoreData(storeId);

    // 檢查是否有相同 ID 的數據
    return storeData.some(item => item.id === id);
  } catch (error) {
    console.error('檢查門店特定數據 ID 失敗:', error);
    throw error;
  }
}

// 創建門店資料
export async function createStoreData(data: Omit<StoreData, 'sn'>, storeId: string): Promise<StoreData> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建請求 URL，不在數據中包含 storeId
    const url = `${buildEndpointUrl('storeData')}?storeId=${encodeURIComponent(storeId)}`;

    // 創建一個不包含 storeId 的數據副本
    const { storeId: _, ...dataWithoutStoreId } = data;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(dataWithoutStoreId),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      const errorData = await response.json();
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    // 獲取響應數據
    return await response.json();
  } catch (error) {
    console.error('創建門店資料失敗:', error);
    throw error;
  }
}

// 更新門店資料
export async function updateStoreData(uid: string, data: Partial<StoreData>, storeId: string): Promise<StoreData> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建 URL，添加 storeId 查詢參數
    const url = `${buildEndpointUrl('storeData', uid)}?storeId=${encodeURIComponent(storeId)}`;

    // 創建一個不包含 storeId 的數據副本
    const { storeId: _, ...dataWithoutStoreId } = data;

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(dataWithoutStoreId),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      const errorData = await response.json();
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    // 獲取響應數據
    return await response.json();
  } catch (error) {
    console.error('更新門店資料失敗:', error);
    throw error;
  }
}

// 刪除門店資料
export async function deleteStoreData(uid: string, storeId: string): Promise<void> {
  try {
    // 檢查參數
    if (!uid) {
      throw new Error('刪除門店資料失敗: 資料ID不能為空');
    }

    if (!storeId) {
      throw new Error('刪除門店資料失敗: 門店ID不能為空');
    }

    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建 URL，添加 storeId 查詢參數
    const url = `${buildEndpointUrl('storeData', uid)}?storeId=${encodeURIComponent(storeId)}`;

    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
  } catch (error) {
    console.error('刪除門店資料失敗:', error);
    throw error;
  }
}

// 同步資料欄位到門店資料結構
export async function syncDataFieldsToStoreData(): Promise<boolean> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('storeData', 'sync-fields'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return (await response.json()).success;
  } catch (error) {
    console.error('同步資料欄位失敗:', error);
    throw error;
  }
}
