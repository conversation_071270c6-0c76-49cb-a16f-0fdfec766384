import { DataField, DataFieldSectionType } from '../../types';
import { buildEndpointUrl } from './apiConfig';

// 獲取所有資料欄位
export async function getAllDataFields(): Promise<DataField[]> {
  try {
    const response = await fetch(buildEndpointUrl('dataFields'));
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('獲取資料欄位失敗:', error);
    throw error;
  }
}

// 根據區塊類型獲取資料欄位
export async function getDataFieldsBySection(section: DataFieldSectionType): Promise<DataField[]> {
  try {
    const response = await fetch(buildEndpointUrl('dataFields', `section/${section}`));
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`獲取 ${section} 區塊資料欄位失敗:`, error);
    throw error;
  }
}

// 創建新資料欄位
export async function createDataField(dataField: Omit<DataField, 'sortOrder'>): Promise<DataField> {
  try {
    const response = await fetch(buildEndpointUrl('dataFields'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(dataField),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('創建資料欄位失敗:', error);
    throw error;
  }
}

// 更新資料欄位
export async function updateDataField(id: string, dataField: Partial<DataField>): Promise<DataField> {
  try {
    const response = await fetch(buildEndpointUrl('dataFields', id), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(dataField),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('更新資料欄位失敗:', error);
    throw error;
  }
}

// 刪除資料欄位
export async function deleteDataField(id: string): Promise<void> {
  try {
    const response = await fetch(buildEndpointUrl('dataFields', id), {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
  } catch (error) {
    console.error('刪除資料欄位失敗:', error);
    throw error;
  }
}

// 批量更新資料欄位排序
export async function updateDataFieldsOrder(dataFields: Pick<DataField, 'id' | 'sortOrder'>[]): Promise<DataField[]> {
  try {
    const response = await fetch(buildEndpointUrl('dataFields', 'batch/order'), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(dataFields),
    });
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('批量更新資料欄位排序失敗:', error);
    throw error;
  }
}

// 確保 id 欄位存在
export async function ensureIdField(): Promise<DataField> {
  try {
    const response = await fetch(buildEndpointUrl('dataFields', 'ensure-id-field'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('確保 id 欄位存在失敗:', error);
    throw error;
  }
}

// 同步資料欄位與資料庫
export async function syncDataFields(): Promise<boolean> {
  try {
    const response = await fetch(buildEndpointUrl('dataFields', 'sync'), {
      method: 'POST',
    });
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('同步資料欄位失敗:', error);
    throw error;
  }
}

// 檢查 ID 或名稱是否重複
export async function checkDuplicateField(field: { id?: string, name?: string }, excludeId?: string): Promise<{ isDuplicateId: boolean, isDuplicateName: boolean }> {
  try {
    const query = new URLSearchParams();
    if (field.id) query.append('id', field.id);
    if (field.name) query.append('name', field.name);
    if (excludeId) query.append('excludeId', excludeId);

    const response = await fetch(`${buildEndpointUrl('dataFields', 'check-duplicate')}?${query}`, {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('檢查重複失敗:', error);
    throw error;
  }
}