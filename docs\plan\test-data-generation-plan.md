# 測試數據生成計劃

本文檔描述了如何使用HTTP請求生成測試數據，包括角色、用戶和權限分配。

## 測試數據內容

### 1. 角色數據
- 系統角色：
  - 系統管理員（具有所有系統權限）
  - 操作員（基本操作權限）
  - 審核員（審核相關權限）
- 門店角色：
  - 店長（門店管理權限）
  - 店員（基本門店操作權限）

### 2. 用戶數據
- 管理員用戶（已有root用戶）
- 普通操作員用戶（3-5個）
- 門店管理用戶（3-5個）

### 3. 門店數據
- 測試門店1（台北總店）
- 測試門店2（台中分店）
- 測試門店3（高雄分店）

### 4. 權限分配
- 為不同用戶分配不同的系統角色
- 為不同用戶分配不同的門店角色和門店

## 執行步驟

1. 登入系統獲取認證token
2. 創建角色數據
3. 創建門店數據
4. 創建用戶數據
5. 分配權限

## 執行命令記錄

以下是執行測試數據生成的命令記錄（Linux/macOS 環境下）：

### 1. 登入系統
```bash
# 使用管理員帳號登入並保存 cookie 到檔案
curl -c cookies.txt -X POST http://localhost:3001/api/auth/login -H "Content-Type: application/json" -d '{"username":"root", "password":"123456789"}'
```

> **關於 cookies.txt 檔案**：
> - 使用 `-c cookies.txt` 參數可以將伺服器返回的 cookie 保存到 cookies.txt 檔案中
> - cookies.txt 是 Netscape HTTP Cookie 文件格式，由 libcurl 生成
> - 檔案包含了登入後的 JWT token，可用於後續 API 請求的身份驗證
> - 使用 `-b cookies.txt` 參數可以在後續請求中使用已保存的 cookie
> - 這樣就不需要在每個請求中手動添加 token，特別是在執行多個命令時非常方便

> **Windows 環境注意事項**：Windows 環境下可以使用 PowerShell 或安裝 curl 工具來執行上述命令。在 PowerShell 中，可能需要使用以下格式：
> ```powershell
> Invoke-WebRequest -Uri "http://localhost:3001/api/auth/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"username":"root", "password":"123456789"}'
> ```
>
> 或者使用 Windows 10 內建的 curl：
> ```cmd
> curl -v -X POST http://localhost:3001/api/auth/login -H "Content-Type: application/json" -d "{\"username\":\"root\", \"password\":\"123456789\"}"
> ```
>
> 不過，我們強烈建議使用提供的 JavaScript 腳本來自動化這個過程，特別是在 Windows 環境下。

### 2. 創建角色數據
```bash
# 創建系統管理員角色
curl -b cookies.txt -X POST "http://localhost:3001/api/roles" -H "Content-Type: application/json" -d '{"name":"系統管理員", "description":"具有所有系統權限", "type":"system", "permissions":["all"]}'

# 創建操作員角色
curl -b cookies.txt -X POST "http://localhost:3001/api/roles" -H "Content-Type: application/json" -d '{"name":"操作員", "description":"基本操作權限", "type":"system", "permissions":["user:view", "role:view", "store:view"]}'

# 創建審核員角色
curl -b cookies.txt -X POST "http://localhost:3001/api/roles" -H "Content-Type: application/json" -d '{"name":"審核員", "description":"審核相關權限", "type":"system", "permissions":["user:view", "role:view", "store:view", "store:update"]}'

# 創建店長角色
curl -b cookies.txt -X POST "http://localhost:3001/api/roles" -H "Content-Type: application/json" -d '{"name":"店長", "description":"門店管理者", "type":"store", "permissions":["store:view", "store:update"]}'

# 創建店員角色
curl -b cookies.txt -X POST "http://localhost:3001/api/roles" -H "Content-Type: application/json" -d '{"name":"店員", "description":"門店基本操作", "type":"store", "permissions":["store:view"]}'
```

### 3. 創建門店數據
```bash
# 創建測試門店1
curl -b cookies.txt -X POST "http://localhost:3001/api/storeData" -H "Content-Type: application/json" -d '{"name":"台北總店", "id":"TP001", "address":"台北市信義區101號"}'

# 創建測試門店2
curl -b cookies.txt -X POST "http://localhost:3001/api/storeData" -H "Content-Type: application/json" -d '{"name":"台中分店", "id":"TC001", "address":"台中市西區中港路123號"}'

# 創建測試門店3
curl -b cookies.txt -X POST "http://localhost:3001/api/storeData" -H "Content-Type: application/json" -d '{"name":"高雄分店", "id":"KH001", "address":"高雄市前鎮區中山路456號"}'
```

### 4. 創建用戶數據
```bash
# 創建操作員用戶
curl -b cookies.txt -X POST "http://localhost:3001/api/users" -H "Content-Type: application/json" -d '{"username":"operator1", "password":"123456", "name":"操作員1", "email":"<EMAIL>", "phone":"0912345678"}'
curl -b cookies.txt -X POST "http://localhost:3001/api/users" -H "Content-Type: application/json" -d '{"username":"operator2", "password":"123456", "name":"操作員2", "email":"<EMAIL>", "phone":"0923456789"}'

# 創建門店管理用戶
curl -b cookies.txt -X POST "http://localhost:3001/api/users" -H "Content-Type: application/json" -d '{"username":"manager1", "password":"123456", "name":"店長1", "email":"<EMAIL>", "phone":"0934567890"}'
curl -b cookies.txt -X POST "http://localhost:3001/api/users" -H "Content-Type: application/json" -d '{"username":"manager2", "password":"123456", "name":"店長2", "email":"<EMAIL>", "phone":"0945678901"}'
curl -b cookies.txt -X POST "http://localhost:3001/api/users" -H "Content-Type: application/json" -d '{"username":"staff1", "password":"123456", "name":"店員1", "email":"<EMAIL>", "phone":"0956789012"}'
```

### 5. 分配權限
```bash
# 為操作員1分配操作員角色
curl -b cookies.txt -X POST "http://localhost:3001/api/permissions" -H "Content-Type: application/json" -d '{"userId":"OPERATOR1_ID", "roleId":"OPERATOR_ROLE_ID", "scope":"system", "scopeType":"system"}'

# 為操作員2分配審核員角色
curl -b cookies.txt -X POST "http://localhost:3001/api/permissions" -H "Content-Type: application/json" -d '{"userId":"OPERATOR2_ID", "roleId":"REVIEWER_ROLE_ID", "scope":"system", "scopeType":"system"}'

# 為店長1分配店長角色（台北總店）
curl -b cookies.txt -X POST "http://localhost:3001/api/permissions" -H "Content-Type: application/json" -d '{"userId":"MANAGER1_ID", "roleId":"MANAGER_ROLE_ID", "scope":"TP001", "scopeType":"store"}'

# 為店長2分配店長角色（台中分店）
curl -b cookies.txt -X POST "http://localhost:3001/api/permissions" -H "Content-Type: application/json" -d '{"userId":"MANAGER2_ID", "roleId":"MANAGER_ROLE_ID", "scope":"TC001", "scopeType":"store"}'

# 為店員1分配店員角色（高雄分店）
curl -b cookies.txt -X POST "http://localhost:3001/api/permissions" -H "Content-Type: application/json" -d '{"userId":"STAFF1_ID", "roleId":"STAFF_ROLE_ID", "scope":"KH001", "scopeType":"store"}'

# 注意：在某些情況下，門店的 scope 可能需要使用門店的 sn 值而非 id 值
```

注意：
1. 在實際執行時，需要將上述命令中的 `OPERATOR1_ID`、`OPERATOR_ROLE_ID` 等替換為實際的值。
2. 使用 cookies.txt 文件可以避免手動複製和粘貼 token，提高效率和準確性。
3. 如果 cookies.txt 文件中的 token 過期，只需重新執行登入命令即可更新文件。

## 使用自動化腳本生成測試數據

為了簡化測試數據的生成過程，我們提供了三個 JavaScript 腳本：`generate-test-data.js`、`assign-permissions.js` 和 `generate-store-template-data.js`。這些腳本可以自動執行上述所有步驟，無需手動執行 curl 命令。

### 前置條件

1. 確保 MongoDB 數據庫已啟動
2. 確保後端服務已啟動（運行在 http://localhost:3001）
3. 確保已安裝 Node.js 和 npm
4. 確保已安裝 node-fetch 套件（如果沒有，請運行以下指令）：

   Linux/macOS:
   ```bash
   npm install node-fetch
   ```

   Windows:
   ```cmd
   npm install node-fetch
   ```

### 使用方法

#### 步驟 1: 啟動後端服務

Linux/macOS:
```bash
npm run server
```

Windows:
```cmd
npm run server
```

#### 步驟 2: 啟動前端服務（可選）

Linux/macOS:
```bash
npm run client
```

Windows:
```cmd
npm run client
```

#### 步驟 3: 執行測試數據生成腳本

Linux/macOS:
```bash
node scripts/generate-test-data.js
```

Windows:
```cmd
node scripts\generate-test-data.js
```

此腳本會執行以下操作：
- 使用管理員帳號（root/123456789）登入系統獲取 token
- 創建角色數據（系統管理員、操作員、審核員、店長、店員）
- 創建門店數據（台北總店、台中分店、高雄分店）
- 創建用戶數據（操作員1、操作員2、店長1、店長2、店員1）
- 分配基本權限

#### 步驟 4: 執行權限分配腳本（如果需要更新權限）

Linux/macOS:
```bash
node scripts/assign-permissions.js
```

Windows:
```cmd
node scripts\assign-permissions.js
```

此腳本會執行以下操作：
- 使用管理員帳號（root/123456789）登入系統獲取 token
- 獲取所有角色、用戶和門店數據
- 為用戶分配適當的權限（如果用戶已有權限，會檢查並更新）

#### 步驟 5: 執行門店數據和模板數據生成腳本

Linux/macOS:
```bash
node scripts/generate-store-template-data.js
```

Windows:
```cmd
node scripts\generate-store-template-data.js
```

或者直接執行批處理文件（僅限 Windows）：
```cmd
scripts\generate-store-template-data.cmd
```

此腳本會執行以下操作：
- 使用管理員帳號（root/123456789）登入系統獲取 token
- 為台北總店添加 1 筆門店數據
- 為台中分店添加 2 筆門店數據
- 為高雄分店添加 3 筆門店數據
- 創建 1 個系統模板（所有門店可用）
- 為台北總店創建 1 個專屬模板
- 為台中分店創建 2 個專屬模板
- 為高雄分店創建 3 個專屬模板

### 腳本特性

1. **自動登入**：腳本會自動使用管理員帳號登入系統獲取 token
2. **錯誤處理**：腳本會處理已存在的數據，避免重複創建
3. **智能權限分配**：`assign-permissions.js` 會檢查用戶是否已有權限，避免重複分配
4. **詳細日誌**：腳本會輸出詳細的執行日誌，方便排查問題
5. **門店 ID 處理**：腳本會嘗試使用門店的 id 屬性查找門店，如果找不到，會嘗試使用門店的名稱查找
6. **門店數據和模板數據**：`generate-store-template-data.js` 會為每個門店創建不同數量的門店數據和模板數據，以便測試門店專屬功能
7. **模板類型支持**：創建的模板包括系統模板和門店專屬模板，以便測試模板過濾功能

### 測試用戶登入信息

生成的測試用戶可以使用以下信息登入系統：

| 用戶名 | 密碼 | 角色 | 權限範圍 |
|--------|------|------|----------|
| root | 123456789 | 系統管理員 | 系統 |
| operator1 | 123456 | 操作員 | 系統 |
| operator2 | 123456 | 審核員 | 系統 |
| manager1 | 123456 | 店長 | 台北總店 |
| manager2 | 123456 | 店長 | 台中分店 |
| staff1 | 123456 | 店員 | 高雄分店 |

### 常見問題排解

1. **腳本執行失敗**：
   - 確保 MongoDB 數據庫已啟動
   - 確保後端服務已啟動並運行在 http://localhost:3001
   - 確保管理員帳號（root/123456789）可以正常登入

2. **權限分配失敗**：
   - 檢查門店 ID 是否正確
   - 檢查用戶和角色是否已創建成功
   - 檢查用戶是否已有相同範圍的權限分配

3. **找不到門店**：
   - 在某些情況下，門店的 scope 可能需要使用門店的 sn 值而非 id 值
   - 可以修改 `assign-permissions.js` 中的門店查找邏輯，使用 sn 值查找門店

4. **用戶無法登入**：
   - 確保用戶已創建成功
   - 確保密碼正確（默認為 123456）
   - 確保用戶狀態為 active

5. **cookies.txt 相關問題**：
   - 如果 curl 命令返回 401 未授權錯誤，可能是 token 已過期，重新執行登入命令更新 cookies.txt
   - 確保 cookies.txt 文件有讀寫權限
   - 如果使用 Windows 系統，確保路徑使用正確的斜線格式
   - 可以使用 `curl -v` 參數查看詳細的請求和響應信息，幫助排查問題
