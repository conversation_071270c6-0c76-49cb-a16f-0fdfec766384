import React from 'react';

// 表單欄位容器
interface FormFieldProps {
  label: string;
  children: React.ReactNode;
}

export const FormField: React.FC<FormFieldProps> = ({ label, children }) => (
  <div className="flex items-center justify-between">
    <label className="text-xs text-gray-400 mr-2 min-w-0 flex-shrink-0">{label}</label>
    {children}
  </div>
);

// 緊湊型表單欄位容器 - 用於行內多欄位佈局
interface CompactFormFieldProps {
  label: string;
  children: React.ReactNode;
  className?: string;
}

export const CompactFormField: React.FC<CompactFormFieldProps> = ({ label, children, className = "" }) => (
  <div className={`flex-1 ${className}`}>
    <label className="text-xs text-gray-400 block mb-1">{label}</label>
    {children}
  </div>
);

// 行內表單組 - 用於將多個欄位放在同一行
interface InlineFormGroupProps {
  children: React.ReactNode;
  gap?: string;
}

export const InlineFormGroup: React.FC<InlineFormGroupProps> = ({ children, gap = "gap-2" }) => (
  <div className={`flex ${gap} mb-3`}>
    {children}
  </div>
);

// 數字輸入元件
interface NumberInputProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  placeholder?: string;
  unit?: string; // 新增：單位顯示
}

export const NumberInput: React.FC<NumberInputProps> = ({
  value,
  onChange,
  min,
  max,
  step,
  placeholder,
  unit
}) => {
  if (unit) {
    // 如果有單位，使用帶單位的緊湊佈局
    return (
      <div className="flex items-center w-20">
        <input
          type="number"
          value={value}
          onChange={(e) => onChange(Number(e.target.value))}
          className="flex-1 bg-gray-700 text-white px-1 py-1 rounded-l text-sm border-r-0 focus:outline-none focus:ring-1 focus:ring-blue-500 min-w-0"
          min={min}
          max={max}
          step={step}
          placeholder={placeholder}
        />
        <span className="bg-gray-600 text-gray-300 px-1 py-1 rounded-r text-xs border-l border-gray-500 whitespace-nowrap">
          {unit}
        </span>
      </div>
    );
  }

  // 標準數字輸入 - 使用更小的寬度，適合位置和大小設定
  return (
    <input
      type="number"
      value={value}
      onChange={(e) => onChange(Number(e.target.value))}
      className="w-16 bg-gray-700 text-white px-1 py-1 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors"
      min={min}
      max={max}
      step={step}
      placeholder={placeholder}
    />
  );
};

// 文字輸入元件
interface TextInputProps {
  value: string;
  onChange: (value: string) => void;
  rows?: number;
  disabled?: boolean;
  isBound?: boolean;
  previewOptions?: Array<{value: string, label: string}>;
  onSelectPreview?: (value: string) => void;
  selectedPreviewValue?: string; // 新增：當前選中的預覽值
  showPrefix?: boolean;
  prefix?: string;
}

export const TextInput: React.FC<TextInputProps> = ({
  value,
  onChange,
  rows,
  disabled,
  isBound,
  previewOptions,
  onSelectPreview,
  selectedPreviewValue,
  showPrefix,
  prefix
}) => {
  // 文字輸入元件
  const textInput = () => {
    if (rows && rows > 1) {
      return (
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-full bg-gray-700 text-white px-2 py-1 rounded text-sm"
          rows={rows}
          disabled={disabled}
        />
      );
    }
    return (
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full bg-gray-700 text-white px-2 py-1 rounded text-sm"
        disabled={disabled}
      />
    );
  };

  // 如果文字已經綁定且有預覽選項，則顯示預覽門店數據選單
  const renderPreviewSelect = () => {
    if (!isBound || !previewOptions || previewOptions.length === 0) {
      return null;
    }

    return (
      <div className="mt-2">
        <label className="text-xs text-gray-400 block mb-1">預覽數據</label>
        <select
          className="w-full bg-gray-700 text-white px-2 py-1 rounded text-sm"
          value={selectedPreviewValue || ''}
          onChange={(e) => {
            if (onSelectPreview) {
              onSelectPreview(e.target.value);
            }
          }}
        >
          <option value="">-- 選擇ID --</option>
          {previewOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
    );
  };

  return (
    <div>
      {textInput()}
      {renderPreviewSelect()}
    </div>
  );
};

// 顏色輸入元件
interface ColorInputProps {
  value: string;
  onChange: (value: string) => void;
}

export const ColorInput: React.FC<ColorInputProps> = ({ value, onChange }) => (
  <div className="flex items-center gap-2">
    <input
      type="color"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="bg-gray-700 border-none rounded h-8 w-8"
    />
    <input
      type="text"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="flex-1 bg-gray-700 text-white px-2 py-1 rounded text-sm"
    />
  </div>
);

// 導出受限制的顏色選擇器
export { default as RestrictedColorInput } from './RestrictedColorInput';

// 下拉選單元件
interface SelectInputProps {
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
}

export const SelectInput: React.FC<SelectInputProps> = ({ value, onChange, options }) => (
  <select
    value={value}
    onChange={(e) => onChange(e.target.value)}
    className="w-full bg-gray-700 text-white px-2 py-1 rounded text-sm"
  >
    {options.map((option) => (
      <option key={option.value} value={option.value}>
        {option.label}
      </option>
    ))}
  </select>
);

// 複選框元件
interface CheckboxInputProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
}

export const CheckboxInput: React.FC<CheckboxInputProps> = ({ checked, onChange, label }) => (
  <div className="flex items-center gap-2">
    <input
      type="checkbox"
      checked={checked}
      onChange={(e) => onChange(e.target.checked)}
      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
    />
    {label && <span className="text-sm text-gray-300">{label}</span>}
  </div>
);

// 高質感分隔線組件
interface PropertySeparatorProps {
  className?: string;
}

export const PropertySeparator: React.FC<PropertySeparatorProps> = ({ className = "" }) => (
  <div className={`relative my-4 ${className}`}>
    {/* 主分隔線 */}
    <div className="flex items-center">
      <div className="w-full border-t border-gray-600"></div>
    </div>
    {/* 漸變效果 */}
    <div className="absolute inset-0 flex items-center">
      <div className="w-full h-px bg-gradient-to-r from-transparent via-gray-500 to-transparent opacity-50"></div>
    </div>
  </div>
);

// 小型分隔線組件 - 用於屬性內部分隔
export const PropertyDivider: React.FC<PropertySeparatorProps> = ({ className = "" }) => (
  <div className={`relative my-3 ${className}`}>
    {/* 較細的分隔線 */}
    <div className="flex items-center">
      <div className="w-full border-t border-gray-700"></div>
    </div>
    {/* 較淡的漸變效果 */}
    <div className="absolute inset-0 flex items-center">
      <div className="w-full h-px bg-gradient-to-r from-transparent via-gray-600 to-transparent opacity-30"></div>
    </div>
  </div>
);

// 常用的字體選項
export const fontOptions = [
  { value: 'Arial', label: 'Arial' },
  { value: 'Times New Roman', label: 'Times New Roman' },
  { value: 'Courier New', label: 'Courier New' },
  { value: 'sans-serif', label: 'Sans-serif' },
  { value: 'serif', label: 'Serif' },
  { value: 'monospace', label: 'Monospace' },
];