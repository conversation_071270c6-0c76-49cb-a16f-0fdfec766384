import React, { useEffect, useState } from 'react';
import { Info, ChevronDown, ChevronUp } from 'lucide-react';
import { Template } from '../../types';
import ColorTypeGradient from '../ui/ColorTypeGradient';
import { getScreenConfigs } from '../../screens/screenSizeMap';

interface InfoPanelProps {
  selectedTemplate: Template;
  zoom: number;
  setZoom: (zoom: number) => void;
}

export const InfoPanel: React.FC<InfoPanelProps> = ({
  selectedTemplate,
  zoom,
  setZoom
}) => {
  // 模板資訊區塊的收折狀態，預設為收起
  const [isInfoExpanded, setIsInfoExpanded] = useState(false);

  // 格式化螢幕尺寸顯示為"尺寸(解析度)"格式
  const formatScreenSizeDisplay = (templateScreenSize: string): string => {
    if (!templateScreenSize) return '';

    // 獲取屏幕配置
    const screenConfigs = getScreenConfigs();

    // 嘗試匹配屏幕配置
    for (const config of screenConfigs) {
      const targetResolution = `${config.width}x${config.height}`;
      const rotatedResolution = `${config.height}x${config.width}`;

      // 如果模板的screenSize匹配任何已知配置
      if (templateScreenSize === config.name ||
          templateScreenSize === config.displayName ||
          templateScreenSize === targetResolution ||
          templateScreenSize === rotatedResolution) {
        return `${config.name}(${targetResolution})`;
      }
    }

    // 如果沒有匹配到配置，檢查是否已經是"尺寸(解析度)"格式
    if (templateScreenSize.includes('(') && templateScreenSize.includes(')')) {
      return templateScreenSize;
    }

    // 如果是純解析度格式（如 "128x296"），嘗試找到對應的尺寸名稱
    const resolutionMatch = templateScreenSize.match(/^(\d+)x(\d+)$/);
    if (resolutionMatch) {
      const width = parseInt(resolutionMatch[1]);
      const height = parseInt(resolutionMatch[2]);

      for (const config of screenConfigs) {
        if ((config.width === width && config.height === height) ||
            (config.width === height && config.height === width)) {
          return `${config.name}(${width}x${height})`;
        }
      }

      // 如果找不到對應配置，直接返回解析度
      return templateScreenSize;
    }

    // 其他情況直接返回原始值
    return templateScreenSize;
  };

  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (e.ctrlKey) {
        e.preventDefault();

        // 根據滾輪方向增加或減少縮放值，每次變化 10%
        const newZoom = e.deltaY < 0
          ? Math.min(500, zoom + 10)
          : Math.max(50, zoom - 10);

        setZoom(newZoom);
      }
    };

    // 添加事件監聽器
    window.addEventListener('wheel', handleWheel, { passive: false });

    // 清除事件監聽器
    return () => {
      window.removeEventListener('wheel', handleWheel);
    };
  }, [zoom, setZoom]);

  return (
    <div className="w-64 bg-gray-800 text-white">
      {/* 模板資訊區塊 - 可收折 */}
      <div className="border-b border-gray-700">
        {/* 標題欄 - 可點擊收折 */}
        <button
          onClick={() => setIsInfoExpanded(!isInfoExpanded)}
          className="w-full flex items-center justify-between px-3 py-2 hover:bg-gray-700 transition-colors"
        >
          <div className="flex items-center gap-2">
            <Info size={14} />
            <span className="text-sm font-medium">模板資訊</span>
          </div>
          {isInfoExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
        </button>

        {/* 可收折的內容區域 */}
        <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
          isInfoExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}>
          <div className="px-3 pb-3 space-y-2">
            {/* 螢幕尺寸 */}
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-400">螢幕尺寸</span>
              <span className="text-xs text-white">{formatScreenSizeDisplay(selectedTemplate.screenSize)}</span>
            </div>

            {/* 顏色類型 */}
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-400">顏色類型</span>
              <div className="w-16 h-3">
                <ColorTypeGradient colorType={selectedTemplate.color} size="sm" />
              </div>
            </div>

            {/* 螢幕方向 */}
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-400">螢幕方向</span>
              <span className="text-xs text-white">
                {selectedTemplate.orientation === 'landscape' ? '橫向' : '縱向'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 縮放控制區域 - 不可收折，始終顯示 */}
      <div className="px-3 py-2 bg-gray-900">
        <div className="space-y-1">
          {/* 縮放標題和數值 - 緊湊排列 */}
          <div className="flex items-center justify-between">
            <span className="text-xs font-medium text-gray-300">縮放</span>
            <span className="text-xs font-mono text-white bg-gray-700 px-2 py-0.5 rounded">{zoom}%</span>
          </div>

          {/* 縮放滑塊 - 更緊湊的設計 */}
          <div className="relative">
            <input
              type="range"
              min="50"
              max="500"
              step="10"
              value={zoom}
              onChange={(e) => setZoom(Number(e.target.value))}
              className="w-full h-1.5 bg-gray-600 rounded-full appearance-none cursor-pointer
                         focus:outline-none focus:ring-1 focus:ring-blue-400 focus:ring-opacity-50
                         slider-thumb:appearance-none slider-thumb:w-3 slider-thumb:h-3
                         slider-thumb:rounded-full slider-thumb:bg-blue-500 slider-thumb:cursor-pointer
                         slider-thumb:border-2 slider-thumb:border-white slider-thumb:shadow-sm"
              style={{
                background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${((zoom - 50) / (500 - 50)) * 100}%, #4b5563 ${((zoom - 50) / (500 - 50)) * 100}%, #4b5563 100%)`
              }}
            />

            {/* 快速縮放按鈕 */}
            <div className="flex justify-between items-center mt-1">
              <div className="flex gap-1">
                <button
                  onClick={() => setZoom(50)}
                  className="text-xs px-1.5 py-0.5 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
                  title="最小縮放"
                >
                  50%
                </button>
                <button
                  onClick={() => setZoom(100)}
                  className="text-xs px-1.5 py-0.5 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
                  title="原始大小"
                >
                  100%
                </button>
              </div>
              <div className="flex gap-1">
                <button
                  onClick={() => setZoom(200)}
                  className="text-xs px-1.5 py-0.5 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
                  title="放大2倍"
                >
                  200%
                </button>
                <button
                  onClick={() => setZoom(500)}
                  className="text-xs px-1.5 py-0.5 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
                  title="最大縮放"
                >
                  500%
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};