import React from 'react';
import { getAvailableColorsForColorType, mapColorToAvailableColor } from '../../../utils/colorConversion';
import { DisplayColorType } from '../../../types';

interface RestrictedColorInputProps {
  value: string;
  onChange: (value: string) => void;
  colorType?: string | DisplayColorType;
  disabled?: boolean;
}

/**
 * 受限制的顏色選擇器組件
 * 根據 template 的 colortype 限制可選顏色
 */
export const RestrictedColorInput: React.FC<RestrictedColorInputProps> = ({
  value,
  onChange,
  colorType,
  disabled = false
}) => {
  // 如果沒有指定 colorType，回退到普通的顏色選擇器
  if (!colorType) {
    return (
      <div className="flex items-center gap-2">
        <input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          className="bg-gray-700 border-none rounded h-8 w-8 disabled:opacity-50"
        />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          className="flex-1 bg-gray-700 text-white px-2 py-1 rounded text-sm disabled:opacity-50"
        />
      </div>
    );
  }

  // 獲取可用顏色列表
  const availableColors = getAvailableColorsForColorType(colorType);

  // 添加透明選項
  const allAvailableColors = ['transparent', ...availableColors];

  // 確保當前值在可用範圍內
  const currentValue = value === 'transparent' || allAvailableColors.includes(value.toUpperCase())
    ? value.toLowerCase() === 'transparent' ? 'transparent' : value.toUpperCase()
    : mapColorToAvailableColor(value, colorType);

  // 處理顏色選擇
  const handleColorSelect = (color: string) => {
    if (!disabled) {
      onChange(color);
    }
  };

  // 獲取顏色名稱（用於顯示）
  const getColorName = (color: string): string => {
    if (color === 'transparent') return '透明';

    switch (color.toUpperCase()) {
      case '#000000': return '黑色';
      case '#FFFFFF': return '白色';
      case '#FF0000': return '紅色';
      case '#FFFF00': return '黃色';
      default:
        // 對於灰度顏色，顯示灰度級別
        if (color.match(/^#([0-9A-F])\1\1$/i)) {
          const grayValue = parseInt(color.substring(1, 3), 16);
          const level = Math.round((grayValue / 255) * 15);
          return `灰度 ${level}`;
        }
        return color;
    }
  };

  return (
    <div className="space-y-2">
      {/* 當前選中的顏色顯示 */}
      <div className="flex items-center gap-2 p-2 bg-gray-700 rounded">
        <div
          className="w-6 h-6 rounded border border-gray-500"
          style={{
            backgroundColor: currentValue === 'transparent' ? 'transparent' : currentValue,
            backgroundImage: currentValue === 'transparent' ? 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)' : 'none',
            backgroundSize: currentValue === 'transparent' ? '8px 8px' : 'auto',
            backgroundPosition: currentValue === 'transparent' ? '0 0, 0 4px, 4px -4px, -4px 0px' : 'auto'
          }}
        />
        <span className="text-sm text-white flex-1">
          {getColorName(currentValue)}
        </span>
      </div>

      {/* 可用顏色選擇器 */}
      <div className="grid grid-cols-4 gap-1 p-2 bg-gray-800 rounded">
        {/* 透明選項 */}
        <button
          key="transparent"
          type="button"
          disabled={disabled}
          onClick={() => handleColorSelect('transparent')}
          className={`
            w-8 h-8 rounded border-2 transition-all duration-200
            ${currentValue === 'transparent'
              ? 'border-blue-400 ring-2 ring-blue-400 ring-opacity-50'
              : 'border-gray-500 hover:border-gray-300'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-110'}
          `}
          style={{
            backgroundColor: 'transparent',
            backgroundImage: 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)',
            backgroundSize: '6px 6px',
            backgroundPosition: '0 0, 0 3px, 3px -3px, -3px 0px'
          }}
          title="透明 (transparent)"
        >
          {/* 選中標記 */}
          {currentValue === 'transparent' && (
            <div className="w-full h-full flex items-center justify-center">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
            </div>
          )}
        </button>

        {/* 顏色選項 */}
        {availableColors.map((color) => (
          <button
            key={color}
            type="button"
            disabled={disabled}
            onClick={() => handleColorSelect(color)}
            className={`
              w-8 h-8 rounded border-2 transition-all duration-200
              ${currentValue === color
                ? 'border-blue-400 ring-2 ring-blue-400 ring-opacity-50'
                : 'border-gray-500 hover:border-gray-300'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-110'}
            `}
            style={{ backgroundColor: color }}
            title={`${getColorName(color)} (${color})`}
          >
            {/* 選中標記 */}
            {currentValue === color && (
              <div className="w-full h-full flex items-center justify-center">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default RestrictedColorInput;
