# 軟體管理編輯功能更新

## 更新概述

根據用戶需求，對軟體管理系統進行了以下兩個主要改進：

1. **詳細頁面添加編輯名稱功能**
2. **上傳頁面每次打開時重置表單內容**

## 詳細修改內容

### 1. 軟體詳細頁面編輯功能

#### 前端修改 (`src/components/system-config/SoftwareDetailModal.tsx`)

**新增狀態管理：**
- `isEditing`: 控制編輯模式開關
- `editName`: 編輯中的軟體名稱
- `editDescription`: 編輯中的軟體描述
- `checkingName`: 名稱檢查狀態
- `nameExists`: 名稱是否已存在
- `originalName`: 原始名稱（用於比較）

**新增功能函數：**
- `checkNameExists()`: 檢查軟體名稱是否已存在
- `handleNameChange()`: 處理名稱變更並延遲檢查重複
- `startEdit()`: 開始編輯模式
- `cancelEdit()`: 取消編輯並恢復原始值
- `saveEdit()`: 保存編輯內容

**UI 改進：**
- 軟體名稱欄位添加編輯按鈕
- 編輯模式下顯示輸入框和即時驗證
- 描述欄位在編輯模式下顯示為文本區域
- 底部操作區根據編輯狀態顯示不同按鈕組合

#### API 擴展 (`src/utils/api/softwareApi.ts`)

**新增 API 函數：**
```typescript
export async function updateSoftware(
  id: string,
  updateData: { name?: string; description?: string }
): Promise<void>
```

#### 後端 API 擴展 (`server/routes/softwareApi.js`)

**新增 PUT 路由：**
```javascript
PUT /api/software/:id
```

**功能特性：**
- 驗證軟體 ID 有效性
- 檢查軟體是否存在
- 驗證名稱不為空
- 檢查新名稱是否與其他軟體重複
- 更新軟體基本資訊（名稱、描述）
- 記錄修改時間和修改者

### 2. 上傳頁面表單重置功能

#### 前端修改 (`src/components/system-config/SoftwareUploadModal.tsx`)

**新增 useEffect 鉤子：**
```typescript
React.useEffect(() => {
  if (isOpen) {
    resetForm();
  }
}, [isOpen]);
```

**功能說明：**
- 每次模態框打開時自動調用 `resetForm()` 函數
- 清除所有表單狀態和檔案選擇
- 重置驗證狀態和錯誤訊息
- 確保用戶每次都從乾淨的表單開始

## 技術實現細節

### 編輯功能的用戶體驗

1. **即時驗證**：名稱輸入時延遲 500ms 檢查重複
2. **視覺反饋**：檢查狀態顯示載入動畫和結果圖標
3. **錯誤處理**：清晰的錯誤訊息和狀態指示
4. **操作確認**：保存和取消按鈕的適當禁用邏輯

### 表單重置的完整性

1. **狀態清理**：所有相關狀態變數都被重置
2. **檔案輸入**：清除檔案輸入元素的值
3. **驗證重置**：清除所有驗證狀態和錯誤訊息

## 安全性考慮

1. **權限檢查**：後端 API 使用認證和權限中間件
2. **輸入驗證**：前後端都進行輸入驗證
3. **重複檢查**：防止名稱衝突
4. **錯誤處理**：適當的錯誤訊息而不洩露敏感資訊

## 測試建議

### 編輯功能測試

1. **基本編輯**：
   - 點擊編輯按鈕進入編輯模式
   - 修改名稱和描述
   - 保存並驗證更新成功

2. **名稱驗證**：
   - 輸入已存在的名稱，確認顯示錯誤
   - 輸入空名稱，確認無法保存
   - 輸入有效名稱，確認可以保存

3. **取消操作**：
   - 修改內容後點擊取消
   - 確認內容恢復到原始狀態

### 表單重置測試

1. **重新打開測試**：
   - 填寫表單內容
   - 關閉模態框
   - 重新打開確認表單已清空

2. **檔案選擇重置**：
   - 選擇檔案
   - 關閉並重新打開
   - 確認檔案選擇已清除

## 後續改進建議

1. **批量編輯**：考慮添加批量編輯多個軟體的功能
2. **編輯歷史**：記錄軟體資訊的修改歷史
3. **更多欄位**：根據需要擴展可編輯的欄位
4. **拖拽排序**：在列表中添加拖拽排序功能
