/**
 * 預覽服務圖標測試 - 測試後端預覽服務中的圖標渲染
 */

const fs = require('fs');
const path = require('path');

/**
 * 測試預覽服務中的圖標渲染
 */
async function testPreviewServiceIcon() {
  try {
    console.log('=== 開始預覽服務圖標測試 ===\n');

    // 引入預覽服務
    const { regeneratePreviewBeforeSend } = require('../services/previewService');

    // 創建測試模板
    const testTemplate = {
      id: 'preview-service-icon-test',
      name: '預覽服務圖標測試',
      width: 400,
      height: 300,
      screenSize: '400x300',
      elements: [
        {
          id: 'icon1',
          type: 'icon',
          x: 50,
          y: 50,
          width: 60,
          height: 60,
          iconType: 'star',
          lineColor: '#000000',
          lineWidth: 2
        },
        {
          id: 'icon2',
          type: 'icon',
          x: 150,
          y: 50,
          width: 60,
          height: 60,
          iconType: 'heart',
          lineColor: '#ff0000',
          lineWidth: 2
        },
        {
          id: 'icon3',
          type: 'icon',
          x: 250,
          y: 50,
          width: 60,
          height: 60,
          iconType: 'alert-circle',
          lineColor: '#0000ff',
          lineWidth: 2
        },
        {
          id: 'icon4',
          type: 'icon',
          x: 50,
          y: 150,
          width: 60,
          height: 60,
          iconType: 'home',
          lineColor: '#00aa00',
          lineWidth: 2
        },
        {
          id: 'icon5',
          type: 'icon',
          x: 150,
          y: 150,
          width: 60,
          height: 60,
          iconType: 'circle',
          lineColor: '#ff8800',
          lineWidth: 2
        },
        {
          id: 'icon6',
          type: 'icon',
          x: 250,
          y: 150,
          width: 60,
          height: 60,
          iconType: 'square',
          lineColor: '#8800ff',
          lineWidth: 2
        }
      ]
    };

    // 創建測試設備數據
    const testDevice = {
      id: 'test-device',
      dataBindings: {}
    };

    // 創建測試門店數據
    const testStoreData = [
      {
        id: 'test-store',
        name: '測試門店',
        storeSpecificData: []
      }
    ];

    console.log('生成預覽圖...');
    const result = await regeneratePreviewBeforeSend(testDevice, testStoreData, testTemplate, []);

    if (result) {
      console.log('✓ 預覽圖生成成功');

      // 將 base64 圖片保存到檔案
      const base64Data = result.replace(/^data:image\/png;base64,/, '');
      const outputPath = path.join(__dirname, 'preview-service-icon-test-output.png');
      fs.writeFileSync(outputPath, base64Data, 'base64');

      console.log(`✓ 預覽圖已保存到: ${outputPath}`);
      console.log(`  圖片大小: ${Math.round(base64Data.length * 0.75 / 1024)} KB`);
    } else {
      console.log('✗ 預覽圖生成失敗');
    }

    console.log('\n=== 預覽服務圖標測試完成 ===');

  } catch (error) {
    console.error('測試過程中發生錯誤:', error);
    console.error('錯誤堆疊:', error.stack);
  }
}

// 執行測試
testPreviewServiceIcon();
