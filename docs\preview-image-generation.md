# 預覽圖生成方法比較文檔

本文檔詳細說明了系統中兩種不同的預覽圖生成方法，包括它們的工作原理、呼叫流程和適用場景。

## 目錄

1. [概述](#概述)
2. [方法一：使用 generatePreviewImage](#方法一使用-generatepreviewimage)
   - [工作原理](#工作原理-1)
   - [呼叫流程](#呼叫流程-1)
   - [適用場景](#適用場景-1)
   - [代碼示例](#代碼示例-1)
3. [方法二：使用 PreviewComponent](#方法二使用-generatetemplatepreview)
   - [工作原理](#工作原理-2)
   - [呼叫流程](#呼叫流程-2)
   - [適用場景](#適用場景-2)
   - [代碼示例](#代碼示例-2)
4. [兩種方法的比較](#兩種方法的比較)
5. [最佳實踐建議](#最佳實踐建議)

## 概述

系統中存在兩種生成預覽圖的方法：

1. **使用 generatePreviewImage**：直接從現有的畫布元素生成預覽圖，主要用於模板編輯器和保存模板時。
2. **使用 PreviewComponent**：根據模板數據和綁定數據動態創建臨時畫布並生成預覽圖，主要用於數據綁定模態框。

這兩種方法雖然目的相同（生成預覽圖），但在實現方式和適用場景上有所不同。

## 方法一：使用 generatePreviewImage

### 工作原理

`generatePreviewImage` 函數接收一個已存在於 DOM 中的畫布元素，然後使用 HTML2Canvas 將其轉換為圖像。這個函數不負責創建或渲染畫布元素，它只是將已經渲染好的畫布元素轉換為圖像。

### 呼叫流程

1. 調用者（如 `saveTemplateToServer`）使用 `document.querySelector` 找到畫布元素
2. 調用 `generatePreviewImage(canvasRef, effectType, threshold)`
3. `generatePreviewImage` 函數內部：
   - 獲取畫布尺寸
   - 備份選中狀態的元素
   - 處理綁定文字元素，使其顯示實際數據
   - 使用 `renderCanvasToImage` 函數將畫布渲染為圖像
   - 恢復選中狀態和綁定元素的原始樣式
   - 應用選定的效果（如黑白效果）
   - 將預覽圖轉換為 base64 數據並返回
4. 調用者接收預覽圖數據並使用（如保存到模板中）

#### Mermaid 流程圖

```mermaid
sequenceDiagram
    participant Client as 調用者 (saveTemplateToServer)
    participant Preview as generatePreviewImage
    participant Canvas as renderCanvasToImage
    participant Effect as applyImageEffect
    participant Binding as processBindingElements

    Client->>Client: 使用 document.querySelector 找到畫布元素
    Client->>Preview: 調用 generatePreviewImage(canvasRef, effectType, threshold)
    Preview->>Preview: 獲取畫布尺寸
    Preview->>Preview: 備份選中狀態的元素
    Preview->>Binding: 調用 processBindingElements()
    Binding-->>Preview: 返回原始樣式
    Preview->>Canvas: 調用 renderCanvasToImage(canvasRef, width, height)
    Canvas-->>Preview: 返回渲染後的 Canvas
    Preview->>Preview: 恢復選中狀態
    Preview->>Binding: 調用 restoreBindingElements(originalStyles)
    Preview->>Effect: 調用 applyImageEffect(renderedCanvas, effectType, threshold)
    Effect-->>Preview: 返回處理後的 Canvas
    Preview->>Preview: 轉換為 base64 數據
    Preview-->>Client: 返回預覽圖數據
    Client->>Client: 使用預覽圖數據 (如保存到模板中)
```

### 適用場景

- 模板編輯器中生成預覽圖
- 保存模板時生成預覽圖
- 任何已經有渲染好的畫布元素的場景

### 代碼示例

```javascript
// 在 templateApi.ts 中的 saveTemplateToServer 函數中
const canvasRef = document.querySelector('[data-canvas-width]');
if (canvasRef) {
  const previewImageData = await generatePreviewImage(canvasRef, 'blackAndWhite', 128);
  if (previewImageData) {
    template.previewImage = previewImageData;
  }
}
```

```javascript
// previewUtils.ts 中的 generatePreviewImage 函數
export const generatePreviewImage = async (
  canvasElement: HTMLElement,
  effectType: EffectType = 'blackAndWhite',
  threshold: number = 128
): Promise<string | null> => {
  try {
    // 獲取畫布尺寸
    const canvasWidth = parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10);
    const canvasHeight = parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10);

    // 備份選中狀態的元素
    const selectedElements = document.querySelectorAll('.selected');

    // 臨時隱藏選中狀態以獲得乾淨的預覽圖
    selectedElements.forEach(el => {
      el.classList.remove('selected');
    });

    // 處理綁定文字元素，使其顯示實際數據
    const originalStyles = await processBindingElements();

    // 渲染畫布為圖像
    const canvasRef = {
      current: canvasElement as unknown as HTMLDivElement
    };

    const renderedCanvas = await renderCanvasToImage(
      canvasRef,
      canvasWidth,
      canvasHeight
    );

    // 恢復選中狀態和綁定元素的原始樣式
    selectedElements.forEach(el => {
      el.classList.add('selected');
    });
    restoreBindingElements(originalStyles);

    if (!renderedCanvas) {
      return null;
    }

    // 應用選定的效果
    const previewCanvas = applyImageEffect(renderedCanvas, effectType, threshold);

    // 將預覽圖轉換為 base64 數據
    return previewCanvas.toDataURL('image/png');
  } catch (error) {
    console.error('生成預覽圖時出錯:', error);
    return null;
  }
};
```

## 方法二：使用 PreviewComponent 和 generatePreview

### 工作原理

PreviewComponent 是一個 React 組件，它接收模板數據和綁定數據，然後在內部創建一個臨時畫布，根據模板數據和綁定數據渲染元素到臨時畫布，最後使用 HTML2Canvas 將臨時畫布轉換為圖像。這個組件負責創建和渲染畫布元素，然後將其轉換為圖像。

### 呼叫流程

1. 調用者（如 `BindDeviceDataModal`）提供模板數據和綁定數據，使用 `PreviewComponent` 組件
2. `PreviewComponent` 組件內部使用 `generatePreview` 函數
3. `generatePreview` 函數內部：
   - 在已存在的隱藏容器中創建元素
   - 設置畫布尺寸
   - 為每個元素創建 DOM 元素，並添加到畫布容器
   - 處理綁定元素，使其顯示實際數據
   - 使用 `renderCanvasToImage` 函數將畫布容器渲染為圖像
   - 恢復綁定元素的原始樣式
   - 使用 `applyImageEffect` 函數應用選定的效果（如黑白效果）
   - 將預覽圖轉換為 base64 數據
   - 設置預覽圖並調用回調函數（如果有的話）
4. 調用者接收預覽圖數據並使用（如顯示在界面上）

#### Mermaid 流程圖

```mermaid
sequenceDiagram
    participant Client as 調用者 (BindDeviceDataModal)
    participant Preview as PreviewComponent
    participant Generate as generatePreview
    participant Canvas as renderCanvasToImage
    participant Effect as applyImageEffect
    participant Binding as processBindingElements

    Client->>Preview: 渲染 PreviewComponent 組件
    Note over Client,Preview: 傳遞模板數據、綁定數據、效果類型等
    Preview->>Generate: 調用 generatePreview()
    Generate->>Generate: 創建元素容器
    Generate->>Generate: 設置畫布尺寸
    Generate->>Generate: 為每個元素創建 DOM 元素
    Note over Generate: 根據元素類型設置不同的樣式和內容
    Generate->>Binding: 調用 processBindingElements()
    Binding-->>Generate: 返回原始樣式
    Generate->>Canvas: 調用 renderCanvasToImage(canvasRef, width, height)
    Canvas-->>Generate: 返回渲染後的 Canvas
    Generate->>Binding: 調用 restoreBindingElements(originalStyles)
    Generate->>Effect: 調用 applyImageEffect(renderedCanvas, effectType, threshold)
    Effect-->>Generate: 返回處理後的 Canvas
    Generate->>Generate: 轉換為 base64 數據
    Generate->>Generate: 設置預覽圖狀態
    Generate->>Client: 調用 onPreviewGenerated 回調
    Generate-->>Preview: 返回預覽圖數據
    Preview->>Preview: 更新預覽圖顯示
    Client->>Client: 使用預覽圖數據 (如顯示在界面上)
```

### 適用場景

- 數據綁定模態框中生成預覽圖
- 任何需要根據模板數據和綁定數據動態生成預覽圖的場景
- 當畫布元素不在 DOM 中時

### 代碼示例

```javascript
// 在 BindDeviceDataModal.tsx 中使用 PreviewComponent
<PreviewComponent
  template={selectedTemplate}
  bindingData={formData.dataBindings}
  storeData={storeData}
  effectType="blackAndWhite"
  threshold={128}
  onPreviewGenerated={handlePreviewGenerated}
/>
```

```javascript
// PreviewComponent.tsx 中的 generatePreview 函數
const generatePreview = async () => {
  if (!template || !template.elements || !Array.isArray(template.elements) || !containerRef.current) {
    return null;
  }

  try {
    // 創建元素容器
    const elementsContainer = document.createElement('div');
    elementsContainer.className = 'elements-container';
    elementsContainer.style.position = 'relative';
    elementsContainer.style.width = '100%';
    elementsContainer.style.height = '100%';

    // 清除容器中的所有元素
    containerRef.current.innerHTML = '';
    containerRef.current.appendChild(elementsContainer);

    // 設置畫布尺寸
    let canvasWidth = 250;  // 默認值
    let canvasHeight = 122; // 默認值

    if (template && template.screenSize) {
      // 解析模板的螢幕尺寸
      const sizeMatch = template.screenSize.match(/(\d+)x(\d+)/);
      if (sizeMatch && sizeMatch[1] && sizeMatch[2]) {
        canvasWidth = parseInt(sizeMatch[1], 10);
        canvasHeight = parseInt(sizeMatch[2], 10);
      }
    }

    // 設置容器尺寸
    containerRef.current.style.width = `${canvasWidth}px`;
    containerRef.current.style.height = `${canvasHeight}px`;
    containerRef.current.setAttribute('data-canvas-width', String(canvasWidth));
    containerRef.current.setAttribute('data-canvas-height', String(canvasHeight));

    // 為每個元素創建 DOM 元素
    template.elements.forEach((element, index) => {
      // 創建元素容器
      const elementDiv = document.createElement('div');
      elementDiv.setAttribute('data-element-id', String(index));
      elementDiv.style.position = 'absolute';

      // 根據元素類型設置不同的樣式和內容
      // ... 省略部分代碼 ...
    });

    // 處理綁定元素，使其顯示實際數據
    const originalStyles = await processBindingElements();

    // 創建一個符合 HTMLDivElement 的對象
    const canvasRef = {
      current: containerRef.current as unknown as HTMLDivElement
    };

    // 渲染畫布為圖像
    const renderedCanvas = await renderCanvasToImage(
      canvasRef,
      canvasWidth,
      canvasHeight
    );

    // 恢復綁定元素的原始樣式和內容
    restoreBindingElements(originalStyles);

    if (!renderedCanvas) {
      return null;
    }

    // 應用選定的效果
    const previewCanvas = applyImageEffect(renderedCanvas, effectType, threshold);

    // 將預覽圖轉換為 base64 數據
    const previewData = previewCanvas.toDataURL('image/png');

    // 設置預覽圖
    setPreviewImage(previewData);

    // 如果有回調函數，調用它
    if (onPreviewGenerated) {
      onPreviewGenerated(previewData);
    }

    return previewData;
  } catch (error) {
    console.error('生成預覽圖時發生錯誤:', error);

    // 如果有回調函數，調用它
    if (onPreviewGenerated) {
      onPreviewGenerated(null);
    }

    return null;
  }
};
```

```javascript
// previewUtils.ts 中的 generatePreviewImage 函數
export const generatePreviewImage = async (
  canvasElement: HTMLElement,
  effectType: EffectType = 'blackAndWhite',
  threshold: number = 128
): Promise<string | null> => {
  try {
    // 獲取畫布尺寸
    const canvasWidth = parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10);
    const canvasHeight = parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10);

    // 備份選中狀態的元素
    const selectedElements = document.querySelectorAll('.selected');

    // 臨時隱藏選中狀態以獲得乾淨的預覽圖
    selectedElements.forEach(el => {
      el.classList.remove('selected');
    });

    // 處理綁定文字元素，使其顯示實際數據
    const originalStyles = await processBindingElements();

    // 渲染畫布為圖像
    // 創建一個符合 HTMLDivElement 的對象
    const canvasRef = {
      current: canvasElement as unknown as HTMLDivElement
    };

    const renderedCanvas = await renderCanvasToImage(
      canvasRef,
      canvasWidth,
      canvasHeight
    );

    // 恢復選中狀態和綁定元素的原始樣式
    selectedElements.forEach(el => {
      el.classList.add('selected');
    });

    // 恢復綁定元素的原始樣式和內容
    restoreBindingElements(originalStyles);

    if (!renderedCanvas) {
      return null;
    }

    // 應用選定的效果
    const previewCanvas = applyImageEffect(renderedCanvas, effectType, threshold);

    // 將預覽圖轉換為 base64 數據
    return previewCanvas.toDataURL('image/png');
  } catch (error) {
    console.error('生成預覽圖時出錯:', error);
    return null;
  }
};
```

## 兩種方法的比較

| 特性 | 方法一：generatePreviewImage | 方法二：PreviewComponent 和 generatePreview |
|------|------------------------------|--------------------------------|
| **輸入參數** | 畫布元素、效果類型、閾值 | 模板數據、綁定數據、門店數據、效果類型、閾值 |
| **畫布來源** | 使用已存在於 DOM 中的畫布元素 | 在 React 組件中創建隱藏的畫布容器 |
| **元素渲染** | 不負責渲染元素，使用已渲染好的畫布 | 根據模板數據和綁定數據動態渲染元素 |
| **數據綁定** | 處理已存在的綁定元素 | 根據綁定數據創建和處理綁定元素 |
| **適用場景** | 模板編輯器、保存模板 | 數據綁定模態框、動態預覽 |
| **優點** | 簡單直接，不需要重新渲染元素 | 靈活，可以根據不同的數據動態生成預覽圖 |
| **缺點** | 依賴已存在的畫布元素 | 實現複雜，需要動態創建和渲染元素 |

## 最佳實踐建議

為了確保預覽圖生成的一致性，建議採取以下最佳實踐：

1. **統一渲染邏輯**：確保兩種方法使用相同的元素渲染邏輯，特別是對於特殊元素（如線條和圖標）。

2. **共用核心函數**：兩種方法都應該使用相同的核心函數（如 `renderCanvasToImage` 和 `applyImageEffect`）來確保一致性。

3. **考慮統一方法**：如果可能，考慮統一使用一種方法。例如，可以修改 `saveTemplateToServer` 函數，使其也使用 PreviewComponent 來生成預覽圖。

4. **保持同步更新**：當修改一種方法的渲染邏輯時，確保另一種方法也進行相應的更新。

5. **添加測試**：為兩種方法添加測試，確保它們在各種情況下都能正確工作。

6. **文檔化**：保持文檔的更新，確保開發人員了解兩種方法的差異和適用場景。

### 系統架構圖

下面的架構圖展示了這兩種預覽圖生成方法在整個系統中的位置和關係：

```mermaid
graph TB
    subgraph 模板編輯器[模板編輯器]
        TemplateEditor[模板編輯器組件] --> SaveTemplate[saveTemplateToServer]
        SaveTemplate --> FindCanvas[document.querySelector]
        FindCanvas --> GenPreviewImg[generatePreviewImage]
    end

    subgraph 數據綁定[數據綁定模態框]
        BindModal[BindDeviceDataModal] --> PreviewComp[PreviewComponent]
        PreviewComp --> GenPreview[generatePreview]
    end

    subgraph 共用核心[共用核心函數]
        GenPreviewImg --> RenderCanvas[renderCanvasToImage]
        GenPreviewImg --> ProcessBinding[processBindingElements]
        GenPreviewImg --> ApplyEffect[applyImageEffect]

        GenPreview --> RenderCanvas
        GenPreview --> ProcessBinding
        GenPreview --> ApplyEffect
    end

    subgraph 結果[預覽圖結果]
        GenPreviewImg --> SaveToTemplate["\u4fdd\u5b58\u5230\u6a21\u677f\u4e2d"]
        GenPreview --> DisplayInUI["\u986f\u793a\u5728\u754c\u9762\u4e0a"]
    end

    style 共用核心 fill:#f9f,stroke:#333,stroke-width:2px
    style 模板編輯器 fill:#bbf,stroke:#333,stroke-width:1px
    style 數據綁定 fill:#bfb,stroke:#333,stroke-width:1px
```

通過遵循這些最佳實踐，可以確保預覽圖生成的一致性，提高用戶體驗。
