#!/usr/bin/env node

/**
 * 圖片URL遷移腳本
 * 使用方法: node server/scripts/migrate-image-urls.js
 */

const path = require('path');
const { migrateImageUrls } = require('../utils/imageUrlMigration');

// 設定環境變數
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

async function main() {
  const mongoUrl = process.env.MONGODB_URI || 'mongodb://localhost:27017';
  const dbName = process.env.DB_NAME || 'epd_manager';

  console.log('=== EPD Manager 圖片URL遷移工具 ===');
  console.log(`MongoDB URL: ${mongoUrl}`);
  console.log(`資料庫名稱: ${dbName}`);
  console.log('');

  try {
    console.log('開始遷移...');
    const results = await migrateImageUrls(mongoUrl, dbName);

    console.log('\n=== 遷移完成 ===');
    let totalProcessed = 0;
    let totalUpdated = 0;

    results.forEach(result => {
      console.log(`${result.collection}: 處理 ${result.processed} 筆，更新 ${result.updated} 筆`);
      totalProcessed += result.processed;
      totalUpdated += result.updated;
    });

    console.log(`\n總計: 處理 ${totalProcessed} 筆記錄，更新 ${totalUpdated} 筆記錄`);
    
    if (totalUpdated > 0) {
      console.log('\n✅ 遷移成功！圖片URL已轉換為文件ID格式。');
      console.log('現在圖片URL將在使用時動態組裝，避免IP固定的問題。');
    } else {
      console.log('\n✅ 沒有需要遷移的記錄。');
    }

  } catch (error) {
    console.error('\n❌ 遷移失敗:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 執行遷移
main();
