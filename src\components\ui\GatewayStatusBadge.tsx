import React from 'react';
import { useTranslation } from 'react-i18next';
import { GatewayStatus } from '../../types/gateway';

interface GatewayStatusBadgeProps {
  status: GatewayStatus;
}

export const GatewayStatusBadge: React.FC<GatewayStatusBadgeProps> = ({ status }) => {
  const { t } = useTranslation();

  // 根據狀態設置不同的樣式
  const getStatusStyles = () => {
    switch (status) {
      case 'online':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border-green-300 dark:border-green-700';
      case 'offline':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border-red-300 dark:border-red-700';
      default:
        return 'bg-gray-100 dark:bg-gray-800/30 text-gray-800 dark:text-gray-300 border-gray-300 dark:border-gray-600';
    }
  };

  // 根據狀態獲取對應的文本
  const getStatusText = () => {
    switch (status) {
      case 'online':
        return t('gateways.online');
      case 'offline':
        return t('gateways.offline');
      default:
        return t('common.unknown');
    }
  };

  return (
    <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusStyles()}`}>
      {getStatusText()}
    </span>
  );
};
