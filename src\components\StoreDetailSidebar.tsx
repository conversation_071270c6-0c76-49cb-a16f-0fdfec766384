import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  LayoutTemplate,
  Settings,
  Upload,
  BarChart3,
  Users,
  HardDrive,
  Menu,
  Database,
  Store,
  ArrowLeft
} from 'lucide-react';

type SidebarItem = {
  id: string;
  translationKey: string;
  icon: React.ReactNode;
};

// 第二層選單項目
const sidebarItems: SidebarItem[] = [
  { id: 'store-overview', translationKey: 'sidebar.storeOverview', icon: <Store size={20} /> },
  { id: 'database', translationKey: 'sidebar.database', icon: <Database size={20} /> },
  { id: 'templates', translationKey: 'sidebar.templates', icon: <LayoutTemplate size={20} /> },
  { id: 'deploy', translationKey: 'sidebar.deploy', icon: <Upload size={20} /> },
  { id: 'devices', translationKey: 'sidebar.devices', icon: <HardDrive size={20} /> },
  { id: 'users', translationKey: 'sidebar.users', icon: <Users size={20} /> },
  { id: 'analytics', translationKey: 'sidebar.analytics', icon: <BarChart3 size={20} /> },
  { id: 'settings', translationKey: 'sidebar.systemConfig', icon: <Settings size={20} /> },
];

interface StoreDetailSidebarProps {
  activeItem: string;
  setActiveItem: (id: string) => void;
  isSidebarCollapsed: boolean;
  toggleSidebar: () => void;
  onBackToStoreList: () => void;
  storeName: string;
}

export const StoreDetailSidebar: React.FC<StoreDetailSidebarProps> = ({
  activeItem,
  setActiveItem,
  isSidebarCollapsed,
  toggleSidebar,
  onBackToStoreList,
  storeName
}) => {
  const { t } = useTranslation();
  
  return (
    <div className={`bg-slate-700 text-white h-screen flex flex-col ${isSidebarCollapsed ? 'w-16' : 'w-58'} transition-all duration-300`}>
      <div className="flex items-center p-4 border-b border-slate-600">
        <button
          onClick={toggleSidebar}
          className="p-1 rounded hover:bg-slate-800"
        >
          <Menu size={24} />
        </button>
        {!isSidebarCollapsed && (
          <h1 className="ml-3 text-xl font-semibold truncate">EPD Manager</h1>
        )}
      </div>

      {/* 返回按鈕 */}
      <div className="p-2 border-b border-slate-600">
        <button
          onClick={onBackToStoreList}
          className="flex items-center w-full p-2 rounded-md hover:bg-slate-600"
        >
          <ArrowLeft size={20} />
          {!isSidebarCollapsed && (
            <span className="ml-3">{t('common.back')}</span>
          )}
        </button>
      </div>

      {/* 門店名稱 */}
      {!isSidebarCollapsed && (
        <div className="p-3 border-b border-slate-600">
          <h2 className="text-sm font-medium text-slate-300">{t('storeManagement.storeName')}</h2>
          <p className="text-white font-semibold truncate">{storeName}</p>
        </div>
      )}

      <div className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-2 px-2">
          {sidebarItems.map((item) => (
            <li key={item.id}>
              <button
                onClick={() => setActiveItem(item.id)}
                className={`flex items-center ${isSidebarCollapsed ? 'justify-center' : 'justify-start'} w-full p-2 rounded-md ${
                  activeItem === item.id
                    ? 'bg-blue-600 text-white'
                    : 'hover:bg-slate-600'
                }`}
              >
                <span className="flex-shrink-0">{item.icon}</span>
                {!isSidebarCollapsed && (
                  <span className="ml-3">{t(item.translationKey)}</span>
                )}
              </button>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};
