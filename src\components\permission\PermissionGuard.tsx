import React from 'react';
import { usePermission } from '../../hooks/usePermission';

interface PermissionGuardProps {
  /**
   * 需要的權限標識符
   */
  permission?: string;
  
  /**
   * 需要的權限標識符列表（任意一個）
   */
  anyPermissions?: string[];
  
  /**
   * 需要的權限標識符列表（全部）
   */
  allPermissions?: string[];
  
  /**
   * 當沒有權限時是否渲染備用內容
   * 如果為 true，則在沒有權限時渲染 fallback
   * 如果為 false，則在沒有權限時不渲染任何內容
   */
  renderFallback?: boolean;
  
  /**
   * 沒有權限時的備用內容
   */
  fallback?: React.ReactNode;
  
  /**
   * 子元素
   */
  children: React.ReactNode;
}

/**
 * 權限守衛組件
 * 根據用戶權限控制子元素的顯示
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  anyPermissions,
  allPermissions,
  renderFallback = false,
  fallback = null,
  children
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermission();
  
  // 如果正在加載權限，不渲染任何內容
  if (loading) {
    return null;
  }
  
  // 檢查權限
  let hasAccess = true;
  
  if (permission) {
    hasAccess = hasPermission(permission);
  }
  
  if (anyPermissions && anyPermissions.length > 0) {
    hasAccess = hasAccess && hasAnyPermission(anyPermissions);
  }
  
  if (allPermissions && allPermissions.length > 0) {
    hasAccess = hasAccess && hasAllPermissions(allPermissions);
  }
  
  // 根據權限渲染內容
  if (hasAccess) {
    return <>{children}</>;
  }
  
  // 沒有權限時，根據設置渲染備用內容或不渲染
  return renderFallback ? <>{fallback}</> : null;
};

export default PermissionGuard;
