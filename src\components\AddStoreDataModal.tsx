import React, { useState, useEffect } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { DataField, DataFieldType } from '../types';
import { createStoreData, checkStoreIdExists } from '../utils/api/storeDataApi';

interface AddStoreDataModalProps {
  isOpen: boolean;
  dataFields: DataField[];
  storeId: string; // 關聯的門店ID
  onClose: () => void;
  onSuccess: () => void;
}

export function AddStoreDataModal({ isOpen, dataFields, storeId, onClose, onSuccess }: AddStoreDataModalProps) {  // 初始化表單數據
  const initialFormData: Record<string, any> = {};
  dataFields.forEach(field => {
    // 如果是數值類型，則預設為 0
    if (field.type === DataFieldType.NUMBER) {
      initialFormData[field.id] = field.defaultValue || 0;
    } else {
      initialFormData[field.id] = field.defaultValue || '';
    }
  });

  const [formData, setFormData] = useState<Record<string, any>>(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCheckingId, setIsCheckingId] = useState(false);
  const [idExists, setIdExists] = useState(false);
  // 當欄位值變更時
  const handleChange = (fieldId: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }));

    // 清除該欄位的錯誤
    if (errors[fieldId]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }

    // 如果是 ID 欄位，且值不為空，則檢查 ID 是否已存在
    if (fieldId === 'id' && value && value.trim() !== '') {
      checkIdExistence(value);
    } else if (fieldId === 'id') {
      // 如果 ID 欄位為空，重置檢查狀態
      setIdExists(false);
    }
  };

  // 檢查 ID 是否已存在於當前門店中
  const checkIdExistence = async (id: string) => {
    try {
      setIsCheckingId(true);
      const exists = await checkStoreIdExists(id, storeId);
      setIdExists(exists);

      if (exists) {
        setErrors(prev => ({
          ...prev,
          id: `ID 已存在於此門店中，請使用其他 ID`
        }));
      }
    } catch (error) {
      console.error('檢查 ID 失敗:', error);
    } finally {
      setIsCheckingId(false);
    }
  };  // 表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 驗證表單
    const newErrors: Record<string, string> = {};

    // 在提交前處理表單數據
    const processedFormData = { ...formData };

    dataFields.forEach(field => {
      // 處理必填欄位
      if ((field.id === 'id') && (!formData[field.id] || formData[field.id].trim() === '')) {
        newErrors[field.id] = '此欄位為必填';
      }

      // 處理數字類型
      if (field.type === DataFieldType.NUMBER) {
        // 如果是空字串或未定義，則設為 0
        if (formData[field.id] === '' || formData[field.id] === undefined) {
          processedFormData[field.id] = 0;
        } else if (isNaN(Number(formData[field.id]))) {
          newErrors[field.id] = '必須是數字';
        } else {
          // 確保是數字類型
          processedFormData[field.id] = Number(formData[field.id]);
        }
      }
    });

    // 更新表單數據為處理後的數據
    setFormData(processedFormData);

    // 檢查 ID 是否存在（即使前面已經檢查過，這裡再次檢查以防漏掉）
    if (formData.id && !newErrors.id) {
      try {
        const exists = await checkStoreIdExists(formData.id, storeId);
        if (exists) {
          newErrors.id = `ID 已存在於此門店中，請使用其他 ID`;
          setIdExists(true);
        }
      } catch (error) {
        console.error('提交時檢查 ID 失敗:', error);
      }
    }

    // 如果有錯誤，顯示錯誤並停止提交
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // 如果 ID 已存在，停止提交
    if (idExists) {
      setErrors(prev => ({
        ...prev,
        id: 'ID 已存在，請使用其他 ID'
      }));
      return;
    }

    try {
      setIsSubmitting(true);

      // 呼叫 API 創建門店資料，傳遞 storeId 作為參數
      await createStoreData(formData, storeId);

      // 重置表單並關閉模態窗
      setFormData(initialFormData);
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('創建門店資料失敗:', err);
      setErrors({
        form: err.message || '創建門店資料失敗，請重試'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 如果模態窗口不開啟，不渲染任何內容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center overflow-y-auto">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        {/* 標題欄 */}
        <div className="flex justify-between items-center border-b border-gray-200 px-6 py-4">
          <h2 className="text-xl font-bold text-gray-800">新增門店資料</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        {/* 表單 */}
        <form onSubmit={handleSubmit} className="px-6 py-4">
          {/* 一般錯誤信息 */}
          {errors.form && (
            <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700">
              {errors.form}
            </div>
          )}

          {/* 動態生成表單欄位 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {dataFields
              .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
              .map(field => (
                <div key={field.id} className="mb-4">
                  <label
                    htmlFor={`field-${field.id}`}
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {field.name}
                    {field.id === 'id' && <span className="text-red-500 ml-1">*</span>}
                  </label>

                  {/* 根據欄位類型生成對應輸入控件 */}
                  {field.type === DataFieldType.NUMBER ? (
                    <input
                      type="number"
                      id={`field-${field.id}`}
                      value={formData[field.id] || ''}
                      onChange={(e) => handleChange(field.id, e.target.value)}
                      className={`w-full px-3 py-2 border ${
                        errors[field.id] ? 'border-red-500' : 'border-gray-300'
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      placeholder={field.description || ''}
                    />
                  ) : field.type === DataFieldType.BARCODE_CODE128 ||
                      field.type === DataFieldType.BARCODE_EAN13 ||
                      field.type === DataFieldType.BARCODE_UPC_A ? (
                    <input
                      type="text"
                      id={`field-${field.id}`}
                      value={formData[field.id] || ''}
                      onChange={(e) => handleChange(field.id, e.target.value)}
                      className={`w-full px-3 py-2 border ${
                        errors[field.id] ? 'border-red-500' : 'border-gray-300'
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      placeholder={`${field.description || ''} (${field.type})`}
                    />
                  ) : field.type === DataFieldType.QR_CODE ? (
                    <input
                      type="text"
                      id={`field-${field.id}`}
                      value={formData[field.id] || ''}
                      onChange={(e) => handleChange(field.id, e.target.value)}
                      className={`w-full px-3 py-2 border ${
                        errors[field.id] ? 'border-red-500' : 'border-gray-300'
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      placeholder={`${field.description || ''} (QR Code)`}
                    />
                  ) : (
                    // 預設為純文本輸入框
                    <input
                      type="text"
                      id={`field-${field.id}`}
                      value={formData[field.id] || ''}
                      onChange={(e) => handleChange(field.id, e.target.value)}
                      className={`w-full px-3 py-2 border ${
                        errors[field.id] ? 'border-red-500' : 'border-gray-300'
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      placeholder={field.description || ''}
                    />
                  )}
                    {/* 錯誤訊息 */}
                  {errors[field.id] && (
                    <div className="flex items-center mt-1">
                      <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                      <p className="text-sm text-red-500">{errors[field.id]}</p>
                    </div>
                  )}

                </div>
              ))}
          </div>

          {/* 按鈕 */}
          <div className="flex justify-end gap-3 mt-6 border-t border-gray-200 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              disabled={isSubmitting}
            >
              取消
            </button>
            <button
              type="submit"
              className={`px-4 py-2 bg-violet-500 text-white rounded-md ${
                isSubmitting ? 'opacity-75 cursor-not-allowed' : 'hover:bg-violet-600'
              }`}
              disabled={isSubmitting}
            >
              {isSubmitting ? '提交中...' : '確認新增'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
