/**
 * EPD 轉換功能測試
 */

const { createCanvas } = require('canvas');
const epdConversion = require('./utils/epdConversion');

async function testEPDConversion() {
  console.log('開始測試 EPD 轉換功能...\n');

  // 創建測試 Canvas
  const canvas = createCanvas(128, 296);
  const ctx = canvas.getContext('2d');

  // 繪製測試圖案
  ctx.fillStyle = '#FFFFFF'; // 白色背景
  ctx.fillRect(0, 0, 128, 296);

  ctx.fillStyle = '#000000'; // 黑色文字
  ctx.font = '20px Arial';
  ctx.fillText('EPD Test', 10, 30);

  ctx.fillStyle = '#FF0000'; // 紅色矩形
  ctx.fillRect(10, 50, 50, 30);

  ctx.fillStyle = '#FFFF00'; // 黃色矩形
  ctx.fillRect(70, 50, 50, 30);

  // 測試 BW 轉換
  console.log('=== 測試 BW/GRAY16 轉換 ===');
  const bwOptions = {
    colorType: epdConversion.DisplayColorType.BW,
    width: 128,
    height: 296,
    imagecode: 0x12345678,
    x: 0,
    y: 0,
    templateRotation: 0
  };

  const bwResult = await epdConversion.convertImageToEPDRawData(canvas, bwOptions);
  console.log('BW 轉換結果:', {
    success: bwResult.success,
    totalBytes: bwResult.metadata?.totalBytes,
    imageInfoBytes: bwResult.metadata?.imageInfoBytes,
    pixelDataBytes: bwResult.metadata?.pixelDataBytes,
    processingTime: bwResult.metadata?.processingTime + 'ms'
  });

  if (bwResult.success) {
    console.log('ImageInfo:', bwResult.imageInfo);
    console.log('前10個字節的 rawdata:', Array.from(bwResult.rawdata.slice(0, 10)));
  }

  console.log('\n=== 測試 BWR 轉換 ===');
  const bwrOptions = {
    colorType: epdConversion.DisplayColorType.BWR,
    width: 128,
    height: 296,
    imagecode: 0xABCDEF12,
    x: 0,
    y: 0,
    templateRotation: 0
  };

  const bwrResult = await epdConversion.convertImageToEPDRawData(canvas, bwrOptions);
  console.log('BWR 轉換結果:', {
    success: bwrResult.success,
    totalBytes: bwrResult.metadata?.totalBytes,
    imageInfoBytes: bwrResult.metadata?.imageInfoBytes,
    pixelDataBytes: bwrResult.metadata?.pixelDataBytes,
    processingTime: bwrResult.metadata?.processingTime + 'ms'
  });

  if (bwrResult.success) {
    console.log('ImageInfo:', bwrResult.imageInfo);
    console.log('前10個字節的 rawdata:', Array.from(bwrResult.rawdata.slice(0, 10)));
  }

  console.log('\n=== 測試 BWRY 轉換 ===');
  const bwryOptions = {
    colorType: epdConversion.DisplayColorType.BWRY,
    width: 128,
    height: 296,
    imagecode: 0x87654321,
    x: 0,
    y: 0,
    templateRotation: 0
  };

  const bwryResult = await epdConversion.convertImageToEPDRawData(canvas, bwryOptions);
  console.log('BWRY 轉換結果:', {
    success: bwryResult.success,
    totalBytes: bwryResult.metadata?.totalBytes,
    imageInfoBytes: bwryResult.metadata?.imageInfoBytes,
    pixelDataBytes: bwryResult.metadata?.pixelDataBytes,
    processingTime: bwryResult.metadata?.processingTime + 'ms'
  });

  if (bwryResult.success) {
    console.log('ImageInfo:', bwryResult.imageInfo);
    console.log('前10個字節的 rawdata:', Array.from(bwryResult.rawdata.slice(0, 10)));
  }

  console.log('\n=== 測試旋轉功能 ===');
  const rotationOptions = {
    colorType: epdConversion.DisplayColorType.BW,
    width: 128,
    height: 296,
    imagecode: 0x11223344,
    x: 0,
    y: 0,
    templateRotation: 90 // 測試 90 度旋轉
  };

  const rotationResult = await epdConversion.convertImageToEPDRawData(canvas, rotationOptions);
  console.log('旋轉轉換結果:', {
    success: rotationResult.success,
    totalBytes: rotationResult.metadata?.totalBytes,
    processingTime: rotationResult.metadata?.processingTime + 'ms'
  });

  console.log('\n=== 測試完成 ===');
  
  // 檢查所有測試是否通過
  const allTestsPassed = bwResult.success && bwrResult.success && bwryResult.success && rotationResult.success;
  console.log(`所有測試${allTestsPassed ? '通過' : '失敗'}！`);
  
  return allTestsPassed;
}

// 執行測試
if (require.main === module) {
  testEPDConversion()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('測試執行失敗:', error);
      process.exit(1);
    });
}

module.exports = { testEPDConversion };
