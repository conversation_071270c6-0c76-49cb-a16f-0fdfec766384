/**
 * 系統配置工具函數
 * 提供後端獲取系統配置的便利函數
 */

// MongoDB 連接信息
const collectionName = 'sysConfigs';

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取數據集合
const getCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection(collectionName);
  return { collection, client };
};

// 系統配置默認值
const DEFAULT_SYSTEM_CONFIG = {
  // 連接逾時時間 (毫秒)
  timeout: 5000,

  // 連接重試次數
  retryCount: 3,

  // 緩衝區大小 (KB)
  bufferSize: 1024,

  // 最大資料綁定數量
  maxBindingDataCount: 8,

  // Gateway併發數量
  gatewayConcurrency: 20,

  // 隊列循環上限數量
  maxQueueCycles: 100,

  // 最大等待循環次數
  maxWaitCycles: 10
};

/**
 * 驗證並限制配置值在合理範圍內
 * @param {Object} config 配置物件
 * @returns {Object} 驗證後的配置物件
 */
function validateAndClampConfig(config) {
  const validatedConfig = { ...config };

  // 連接逾時時間 (1000-60000ms)
  if (validatedConfig.timeout !== undefined) {
    validatedConfig.timeout = Math.min(Math.max(validatedConfig.timeout, 1000), 60000);
  }

  // 連接重試次數 (1-10次)
  if (validatedConfig.retryCount !== undefined) {
    validatedConfig.retryCount = Math.min(Math.max(validatedConfig.retryCount, 1), 10);
  }

  // 緩衝區大小 (256-8192KB)
  if (validatedConfig.bufferSize !== undefined) {
    validatedConfig.bufferSize = Math.min(Math.max(validatedConfig.bufferSize, 256), 8192);
  }

  // 最大資料綁定數量 (1-20個)
  if (validatedConfig.maxBindingDataCount !== undefined) {
    validatedConfig.maxBindingDataCount = Math.min(Math.max(validatedConfig.maxBindingDataCount, 1), 20);
  }

  // Gateway併發數量 (1-100個)
  if (validatedConfig.gatewayConcurrency !== undefined) {
    validatedConfig.gatewayConcurrency = Math.min(Math.max(validatedConfig.gatewayConcurrency, 1), 100);
  }

  // 隊列循環上限數量 (10-1000次)
  if (validatedConfig.maxQueueCycles !== undefined) {
    validatedConfig.maxQueueCycles = Math.min(Math.max(validatedConfig.maxQueueCycles, 10), 1000);
  }

  // 最大等待循環次數 (1-100次)
  if (validatedConfig.maxWaitCycles !== undefined) {
    validatedConfig.maxWaitCycles = Math.min(Math.max(validatedConfig.maxWaitCycles, 1), 100);
  }

  return validatedConfig;
}

/**
 * 獲取系統配置
 * @returns {Promise<Object>} 系統配置物件
 */
async function getSysConfig() {
  try {
    const { collection } = await getCollection();
    const config = await collection.findOne({ key: 'system' });

    if (!config) {
      console.log('找不到系統配置，返回默認配置');
      return DEFAULT_SYSTEM_CONFIG;
    }

    // 合併默認配置和數據庫配置，確保所有必要的欄位都存在
    const mergedConfig = { ...DEFAULT_SYSTEM_CONFIG, ...config.value };

    // 驗證並限制配置值在合理範圍內
    return validateAndClampConfig(mergedConfig);
  } catch (error) {
    console.error('獲取系統配置失敗:', error);
    console.log('返回默認系統配置');
    return DEFAULT_SYSTEM_CONFIG;
  }
}

/**
 * 獲取Gateway併發數量
 * @returns {Promise<number>} Gateway併發數量
 */
async function getGatewayConcurrency() {
  try {
    const sysConfig = await getSysConfig();
    return sysConfig.gatewayConcurrency || DEFAULT_SYSTEM_CONFIG.gatewayConcurrency;
  } catch (error) {
    console.error('獲取Gateway併發數量失敗:', error);
    return DEFAULT_SYSTEM_CONFIG.gatewayConcurrency;
  }
}

/**
 * 獲取隊列循環上限數量
 * @returns {Promise<number>} 隊列循環上限數量
 */
async function getMaxQueueCycles() {
  try {
    const sysConfig = await getSysConfig();
    return sysConfig.maxQueueCycles || DEFAULT_SYSTEM_CONFIG.maxQueueCycles;
  } catch (error) {
    console.error('獲取隊列循環上限數量失敗:', error);
    return DEFAULT_SYSTEM_CONFIG.maxQueueCycles;
  }
}

/**
 * 獲取最大等待循環次數
 * @returns {Promise<number>} 最大等待循環次數
 */
async function getMaxWaitCycles() {
  try {
    const sysConfig = await getSysConfig();
    return sysConfig.maxWaitCycles || DEFAULT_SYSTEM_CONFIG.maxWaitCycles;
  } catch (error) {
    console.error('獲取最大等待循環次數失敗:', error);
    return DEFAULT_SYSTEM_CONFIG.maxWaitCycles;
  }
}

/**
 * 獲取指定配置值
 * @param {string} key 配置鍵名
 * @param {any} defaultValue 默認值
 * @returns {Promise<any>} 配置值
 */
async function getConfigValue(key, defaultValue = null) {
  try {
    const sysConfig = await getSysConfig();
    return sysConfig[key] !== undefined ? sysConfig[key] : defaultValue;
  } catch (error) {
    console.error(`獲取配置值失敗 (${key}):`, error);
    return defaultValue;
  }
}

module.exports = {
  initDB,
  getSysConfig,
  getGatewayConcurrency,
  getMaxQueueCycles,
  getMaxWaitCycles,
  getConfigValue,
  DEFAULT_SYSTEM_CONFIG
};
