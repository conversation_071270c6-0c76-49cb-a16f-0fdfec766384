/**
 * 圖片壓縮工具函數
 * 用於在保存模板前壓縮圖片以減少請求大小
 */

/**
 * 壓縮 base64 圖片
 * @param base64String - base64 格式的圖片字符串
 * @param quality - 壓縮質量 (0.1 - 1.0)
 * @param maxWidth - 最大寬度
 * @param maxHeight - 最大高度
 * @returns 壓縮後的 base64 字符串
 */
export const compressBase64Image = async (
  base64String: string,
  quality: number = 0.8,
  maxWidth: number = 1920,
  maxHeight: number = 1080
): Promise<string> => {
  return new Promise((resolve, reject) => {
    try {
      // 創建圖片元素
      const img = new Image();
      
      img.onload = () => {
        try {
          // 創建 canvas
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            reject(new Error('無法獲取 canvas context'));
            return;
          }
          
          // 計算新的尺寸，保持寬高比
          let { width, height } = img;
          
          if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width = Math.floor(width * ratio);
            height = Math.floor(height * ratio);
          }
          
          // 設置 canvas 尺寸
          canvas.width = width;
          canvas.height = height;
          
          // 繪製圖片
          ctx.drawImage(img, 0, 0, width, height);
          
          // 轉換為壓縮後的 base64
          const compressedBase64 = canvas.toDataURL('image/jpeg', quality);
          
          console.log('圖片壓縮完成:', {
            原始大小: base64String.length,
            壓縮後大小: compressedBase64.length,
            壓縮比: ((1 - compressedBase64.length / base64String.length) * 100).toFixed(1) + '%',
            原始尺寸: `${img.width}x${img.height}`,
            壓縮後尺寸: `${width}x${height}`
          });
          
          resolve(compressedBase64);
        } catch (error) {
          reject(error);
        }
      };
      
      img.onerror = () => {
        reject(new Error('圖片載入失敗'));
      };
      
      // 載入圖片
      img.src = base64String;
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * 壓縮模板中的所有圖片元素
 * @param template - 模板對象
 * @param quality - 壓縮質量
 * @returns 壓縮後的模板對象
 */
export const compressTemplateImages = async (
  template: any,
  quality: number = 0.8
): Promise<any> => {
  try {
    const compressedTemplate = { ...template };
    
    // 壓縮預覽圖
    if (compressedTemplate.previewImage && compressedTemplate.previewImage.startsWith('data:image/')) {
      console.log('正在壓縮模板預覽圖...');
      compressedTemplate.previewImage = await compressBase64Image(
        compressedTemplate.previewImage,
        quality,
        800, // 預覽圖最大寬度
        600  // 預覽圖最大高度
      );
    }
    
    // 壓縮元素中的圖片
    if (compressedTemplate.elements && Array.isArray(compressedTemplate.elements)) {
      console.log('正在壓縮模板元素中的圖片...');
      
      for (let i = 0; i < compressedTemplate.elements.length; i++) {
        const element = compressedTemplate.elements[i];
        
        if (element.type === 'image' && element.src && element.src.startsWith('data:image/')) {
          console.log(`正在壓縮第 ${i + 1} 個圖片元素...`);
          
          try {
            compressedTemplate.elements[i] = {
              ...element,
              src: await compressBase64Image(
                element.src,
                quality,
                1920, // 圖片元素最大寬度
                1080  // 圖片元素最大高度
              )
            };
          } catch (error) {
            console.error(`壓縮第 ${i + 1} 個圖片元素失敗:`, error);
            // 如果壓縮失敗，保持原圖
          }
        }
      }
    }
    
    return compressedTemplate;
  } catch (error) {
    console.error('壓縮模板圖片失敗:', error);
    // 如果壓縮失敗，返回原模板
    return template;
  }
};

/**
 * 檢查模板大小並建議是否需要壓縮
 * @param template - 模板對象
 * @returns 檢查結果
 */
export const checkTemplateSize = (template: any): {
  size: number;
  sizeInMB: number;
  needsCompression: boolean;
  recommendation: string;
} => {
  const templateJson = JSON.stringify(template);
  const sizeInBytes = new Blob([templateJson]).size;
  const sizeInMB = sizeInBytes / (1024 * 1024);
  
  const needsCompression = sizeInMB > 10; // 超過 10MB 建議壓縮
  
  let recommendation = '';
  if (sizeInMB > 40) {
    recommendation = '模板非常大，強烈建議壓縮圖片';
  } else if (sizeInMB > 20) {
    recommendation = '模板較大，建議壓縮圖片';
  } else if (sizeInMB > 10) {
    recommendation = '模板大小適中，可考慮壓縮圖片';
  } else {
    recommendation = '模板大小正常';
  }
  
  return {
    size: sizeInBytes,
    sizeInMB: parseFloat(sizeInMB.toFixed(2)),
    needsCompression,
    recommendation
  };
};
