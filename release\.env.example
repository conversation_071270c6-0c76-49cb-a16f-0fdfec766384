# ========================================
# EPD Manager 環境配置檔案
# ========================================
# 請將此檔案複製為 .env 並修改以下設定

# === 必須設置 ===
# JWT 密鑰 - 請務必修改為隨機字串
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-12345

# === 端口配置 ===
# 注意：這些是外部訪問端口，容器內部端口固定為 5173 和 3001
FRONTEND_PORT=5173  # 外部訪問前端的端口
SERVER_PORT=3001    # 外部訪問後端的端口  
MONGO_PORT=27017    # 外部訪問 MongoDB 的端口

# === 基本配置 ===
NODE_ENV=development
MONGO_DB=resourceManagement

# === 重要提醒 ===
# 1. 程式碼中硬編碼了端口 3001 和 5173，如需修改請同時更新程式碼
# 2. JWT_SECRET 請務必設置為強密碼
# 3. 生產環境建議修改預設端口以提高安全性
# 4. 如果修改端口，請確保防火牆允許對應端口通過

# === 可選配置 ===
# 如果需要綁定特定網路介面，可以取消註解以下設定
# BIND_IP=0.0.0.0  # 綁定所有網路介面
# BIND_IP=127.0.0.1  # 僅本機訪問

# === 測試模式配置 ===
# 設置為 dev 啟用測試功能（bug回報等）
TEST_MODE=dev

# === 懸浮球配置 ===
# 設置為 enable 啟用懸浮球功能
FLOATING_BUTTON=enable