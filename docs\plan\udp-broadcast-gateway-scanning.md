# UDP廣播方式網關掃描功能詳細實現步驟

本文檔詳細說明如何在App端實現UDP廣播方式的網關掃描功能，作為第二階段開發的一部分。

## 1. 技術原理

UDP廣播掃描的基本原理是：
1. App發送特定格式的UDP廣播包到網絡中
2. 支持該協議的網關設備接收到廣播包後回應自身信息
3. App接收這些回應並識別網關設備

## 2. 詳細實現步驟

### 步驟1: 添加必要的依賴庫

在App項目中添加UDP通信所需的庫：

```bash
# React Native項目
npm install --save react-native-udp

# 或使用yarn
yarn add react-native-udp
```

對於iOS，還需要在Podfile中添加：

```ruby
pod 'react-native-udp', :path => '../node_modules/react-native-udp'
```

然後執行：

```bash
cd ios && pod install
```

### 步驟2: 添加網絡權限

#### Android (AndroidManifest.xml)
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
```

#### iOS (Info.plist)
```xml
<key>NSLocalNetworkUsageDescription</key>
<string>需要訪問本地網絡以發現網關設備</string>
```

### 步驟3: 創建UDP廣播掃描工具類

創建一個專門的工具類來處理UDP廣播掃描：

```javascript
// src/utils/UdpGatewayScanner.js
import dgram from 'react-native-udp';
import { NetworkInfo } from 'react-native-network-info';

class UdpGatewayScanner {
  constructor() {
    this.socket = null;
    this.discoveredGateways = [];
    this.isScanning = false;
    this.scanTimeout = null;
    
    // 廣播端口 (網關設備應監聽此端口)
    this.BROADCAST_PORT = 8888;
    
    // 廣播消息格式
    this.discoveryMessage = JSON.stringify({
      type: 'discovery',
      app: 'EPDManager',
      protocol: 'v1',
      timestamp: new Date().toISOString()
    });
  }
  
  // 獲取本地網絡信息
  async getNetworkInfo() {
    try {
      const ipAddress = await NetworkInfo.getIPAddress();
      const subnet = await NetworkInfo.getSubnet();
      return { ipAddress, subnet };
    } catch (error) {
      console.error('獲取網絡信息失敗:', error);
      throw error;
    }
  }
  
  // 開始掃描
  startScan(scanDuration = 5000) {
    return new Promise((resolve, reject) => {
      if (this.isScanning) {
        reject(new Error('掃描已在進行中'));
        return;
      }
      
      this.isScanning = true;
      this.discoveredGateways = [];
      
      // 創建UDP socket
      this.socket = dgram.createSocket('udp4');
      
      // 設置掃描超時
      this.scanTimeout = setTimeout(() => {
        this.stopScan();
        resolve(this.discoveredGateways);
      }, scanDuration);
      
      // 處理錯誤
      this.socket.on('error', (err) => {
        console.error('UDP socket錯誤:', err);
        this.stopScan();
        reject(err);
      });
      
      // 處理接收到的消息
      this.socket.on('message', (msg, rinfo) => {
        try {
          console.log(`收到來自 ${rinfo.address}:${rinfo.port} 的消息`);
          
          // 解析響應
          const response = JSON.parse(msg.toString());
          
          // 驗證是否是網關響應
          if (this.isValidGatewayResponse(response)) {
            const gateway = {
              ip: rinfo.address,
              port: rinfo.port,
              mac: response.mac,
              model: response.model || 'Unknown',
              name: response.name || `Gateway-${response.mac.replace(/:/g, '')}`,
              firmware: response.firmware,
              status: response.status || 'unknown',
              discoveryMethod: 'udp',
              storeId: response.storeId,
              timestamp: new Date().toISOString()
            };
            
            // 檢查是否已經發現過該網關
            const existingIndex = this.discoveredGateways.findIndex(g => g.mac === gateway.mac);
            if (existingIndex >= 0) {
              // 更新現有網關信息
              this.discoveredGateways[existingIndex] = gateway;
            } else {
              // 添加新發現的網關
              this.discoveredGateways.push(gateway);
            }
            
            // 觸發發現事件 (如果有設置回調)
            if (this.onGatewayDiscovered) {
              this.onGatewayDiscovered(gateway);
            }
          }
        } catch (error) {
          console.warn('解析網關響應失敗:', error);
        }
      });
      
      // 綁定socket
      this.socket.bind(this.BROADCAST_PORT, () => {
        console.log(`UDP socket已綁定到端口 ${this.BROADCAST_PORT}`);
        
        // 設置廣播模式
        this.socket.setBroadcast(true);
        
        // 發送廣播消息
        this.broadcastDiscoveryMessage();
      });
    });
  }
  
  // 停止掃描
  stopScan() {
    if (this.scanTimeout) {
      clearTimeout(this.scanTimeout);
      this.scanTimeout = null;
    }
    
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    this.isScanning = false;
  }
  
  // 發送廣播消息
  broadcastDiscoveryMessage() {
    if (!this.socket || !this.isScanning) return;
    
    console.log('發送UDP廣播發現消息');
    
    // 更新時間戳
    const message = JSON.stringify({
      ...JSON.parse(this.discoveryMessage),
      timestamp: new Date().toISOString()
    });
    
    // 發送廣播
    this.socket.send(
      message,
      0,
      message.length,
      this.BROADCAST_PORT,
      '255.255.255.255',
      (err) => {
        if (err) {
          console.error('發送廣播消息失敗:', err);
        } else {
          console.log('廣播消息已發送');
          
          // 每隔1秒重發一次廣播，直到掃描結束
          setTimeout(() => {
            if (this.isScanning) {
              this.broadcastDiscoveryMessage();
            }
          }, 1000);
        }
      }
    );
  }
  
  // 驗證網關響應
  isValidGatewayResponse(response) {
    // 檢查必要字段
    return (
      response &&
      response.type === 'gateway' &&
      response.mac &&
      typeof response.mac === 'string' &&
      /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(response.mac)
    );
  }
  
  // 設置網關發現回調
  setOnGatewayDiscovered(callback) {
    this.onGatewayDiscovered = callback;
  }
}

export default new UdpGatewayScanner();
```

### 步驟4: 網關響應格式

網關設備應響應以下格式的JSON數據：

```json
{
  "type": "gateway",
  "mac": "AA:BB:CC:DD:EE:FF",
  "model": "GW-3000",
  "name": "Gateway-AABBCCDDEEFF",
  "firmware": "1.2.0",
  "status": "online",
  "storeId": "TP001"
}
```

### 步驟5: 集成到網關掃描流程

在App的網關掃描流程中集成UDP廣播掃描：

```javascript
// 完整的網關掃描流程
async function scanGateways() {
  try {
    // 開始UDP廣播掃描
    const udpGateways = await UdpGatewayScanner.startScan(10000); // 10秒掃描時間
    
    // 處理掃描結果
    if (udpGateways.length > 0) {
      console.log(`通過UDP廣播發現了 ${udpGateways.length} 個網關`);
      
      // 將發現的網關顯示在UI上
      displayGateways(udpGateways);
    } else {
      console.log('未通過UDP廣播發現網關');
    }
    
    return udpGateways;
  } catch (error) {
    console.error('UDP廣播掃描失敗:', error);
    return [];
  }
}
```

### 步驟6: 網關註冊與WebSocket連接

掃描到網關後，進行註冊並提供WebSocket連接信息：

```javascript
// 註冊網關並提供WebSocket連接信息
async function registerAndConfigureGateway(gateway, storeId) {
  try {
    // 1. 準備網關數據
    const gatewayData = {
      name: gateway.name || `Gateway-${gateway.mac.replace(/:/g, '')}`,
      macAddress: gateway.mac,
      ipAddress: gateway.ip,
      model: gateway.model || 'Unknown',
      status: 'offline',
      storeId: storeId,
      wifiFirmwareVersion: gateway.firmware || '1.0.0',
      btFirmwareVersion: '1.0.0'
    };
    
    // 2. 調用API註冊網關
    const result = await registerGateway(gatewayData);
    
    // 3. 檢查響應中是否包含WebSocket連接信息
    if (result.websocket) {
      // 4. 發送WebSocket連接命令給網關
      await sendConnectCommand(gateway.ip, result.websocket);
      return { success: true, gateway: result };
    } else {
      throw new Error('註冊響應中未包含WebSocket連接信息');
    }
  } catch (error) {
    console.error('註冊和配置網關失敗:', error);
    throw error;
  }
}
```

## 3. 網關設備實現

網關設備需要實現以下功能：

1. 監聽UDP廣播端口(8888)
2. 解析接收到的發現請求
3. 響應自身信息
4. 接收WebSocket連接命令並連接到Server

```javascript
// 網關設備端UDP監聽示例代碼 (偽代碼)
const dgram = require('dgram');
const server = dgram.createSocket('udp4');

server.on('error', (err) => {
  console.error(`UDP server error:\n${err.stack}`);
  server.close();
});

server.on('message', (msg, rinfo) => {
  try {
    const message = JSON.parse(msg.toString());
    
    // 檢查是否是發現請求
    if (message.type === 'discovery' && message.app === 'EPDManager') {
      // 準備響應
      const response = JSON.stringify({
        type: 'gateway',
        mac: getDeviceMac(), // 獲取設備MAC地址
        model: getDeviceModel(), // 獲取設備型號
        name: getDeviceName(), // 獲取設備名稱
        firmware: getDeviceFirmware(), // 獲取固件版本
        status: getDeviceStatus(), // 獲取設備狀態
        storeId: getStoreId() // 獲取門店ID (如果已配置)
      });
      
      // 發送響應
      server.send(response, 0, response.length, rinfo.port, rinfo.address);
    }
  } catch (error) {
    console.error('處理UDP消息失敗:', error);
  }
});

server.on('listening', () => {
  const address = server.address();
  console.log(`UDP server listening on ${address.address}:${address.port}`);
});

// 綁定到廣播端口
server.bind(8888);
```

## 4. 安全考慮

1. **驗證請求來源**：網關應驗證發現請求的來源
2. **限制響應頻率**：防止DoS攻擊
3. **加密敏感信息**：避免明文傳輸敏感信息
4. **使用隨機標識符**：在請求中包含隨機標識符，響應時返回相同標識符

## 5. 性能優化

1. **限制廣播頻率**：避免網絡擁塞
2. **智能重試**：根據網絡條件調整重試策略
3. **緩存發現結果**：避免重複掃描
4. **批量處理**：合併多個請求/響應

## 6. 與第一階段的整合

UDP廣播掃描功能與第一階段的設計完全兼容：

1. 使用現有的網關註冊API
2. 獲取包含門店ID和網關ID的WebSocket連接信息
3. 將WebSocket連接信息提供給網關設備

這種方式確保了網關設備能夠正確連接到其所屬門店的WebSocket端點，避免了同一台網關被註冊到多個門店的問題。
