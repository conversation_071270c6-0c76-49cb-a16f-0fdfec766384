import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../../store/authStore';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select';
import { DialogFooter, DialogClose } from '../ui/dialog';
import { getAllUsers } from '../../utils/api/userApi';
import { getAllRoles } from '../../utils/api/roleApi';
import { createPermission } from '../../utils/api/permissionApi';
import { getAllStores } from '../../utils/api/storeApi';
import { Plus, X } from 'lucide-react';

// 用戶接口
interface User {
  _id: string;
  username: string;
  name: string;
}

// 角色接口
interface Role {
  _id: string;
  name: string;
  type: 'system' | 'store';
}

// 門店接口
interface Store {
  sn: number;
  id?: string;
  name?: string;
}

// 門店權限接口
interface StorePermission {
  storeId: string;
  roleId: string;
}

interface PermissionFormProps {
  permissionId?: string;
  initialData?: {
    userId: string;
    roleId: string;
    scope: string;
    scopeType: 'system' | 'store';
  };
  onClose: () => void;
  onSuccess: () => void;
}

export const PermissionForm: React.FC<PermissionFormProps> = ({
  permissionId,
  initialData,
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation();
  const { token } = useAuthStore();

  // 狀態
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 表單數據
  const [userId, setUserId] = useState(initialData?.userId || '');
  const [systemRoleId, setSystemRoleId] = useState(initialData?.scopeType === 'system' ? initialData?.roleId : 'none');
  const [storePermissions, setStorePermissions] = useState<StorePermission[]>(
    initialData?.scopeType === 'store' ? [{ storeId: initialData.scope, roleId: initialData.roleId }] : []
  );

  // 獲取用戶列表
  const fetchUsers = async () => {
    try {
      const data = await getAllUsers();
      setUsers(data.users);
    } catch (error) {
      console.error('獲取用戶列表錯誤:', error);
    }
  };

  // 獲取角色列表
  const fetchRoles = async () => {
    try {
      const data = await getAllRoles();
      setRoles(data);
    } catch (error) {
      console.error('獲取角色列表錯誤:', error);
    }
  };

  // 獲取門店列表
  const fetchStores = async () => {
    try {
      const data = await getAllStores();
      setStores(data);
    } catch (error) {
      console.error('獲取門店列表錯誤:', error);
    }
  };

  // 初始化
  useEffect(() => {
    fetchUsers();
    fetchRoles();
    fetchStores();
  }, [token]);

  // 獲取選中用戶的姓名
  const selectedUserName = users.find(user => user._id === userId)?.name || '';

  // 獲取系統角色
  const systemRoles = roles.filter(role => role.type === 'system');

  // 獲取門店角色
  const storeRoles = roles.filter(role => role.type === 'store');

  // 添加門店權限
  const addStorePermission = () => {
    setStorePermissions([...storePermissions, { storeId: '', roleId: '' }]);
  };

  // 刪除門店權限
  const removeStorePermission = (index: number) => {
    setStorePermissions(storePermissions.filter((_, i) => i !== index));
  };

  // 更新門店權限
  const updateStorePermission = (index: number, field: keyof StorePermission, value: string) => {
    const newPermissions = [...storePermissions];
    newPermissions[index] = { ...newPermissions[index], [field]: value };
    setStorePermissions(newPermissions);
  };

  // 處理表單提交
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      // 驗證表單
      if (!userId) {
        setError('請選擇用戶');
        return;
      }

      // 檢查是否有系統權限或門店權限
      if ((systemRoleId === 'none' || !systemRoleId) && storePermissions.length === 0) {
        setError('請至少分配一個系統權限或門店權限');
        return;
      }

      // 檢查門店權限是否完整
      const invalidStorePermission = storePermissions.find(p => !p.storeId || !p.roleId);
      if (invalidStorePermission) {
        setError('請完整填寫門店權限信息');
        return;
      }

      // 創建系統權限
      if (systemRoleId && systemRoleId !== 'none') {
        try {
          await createPermission({
            userId,
            roleId: systemRoleId,
            scope: 'system',
            scopeType: 'system'
          });
        } catch (error: any) {
          if (!error.message.includes('用戶已有相同範圍的權限分配')) {
            throw error;
          }
        }
      }

      // 創建門店權限
      for (const permission of storePermissions) {
        try {
          await createPermission({
            userId,
            roleId: permission.roleId,
            scope: permission.storeId,
            scopeType: 'store'
          });
        } catch (error: any) {
          if (!error.message.includes('用戶已有相同範圍的權限分配')) {
            throw error;
          }
        }
      }

      // 成功回調
      onSuccess();
    } catch (error: any) {
      console.error('權限分配操作錯誤:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4 py-2">
      {/* 錯誤提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-2 text-sm">
          {error}
        </div>
      )}

      {/* 基本信息 */}
      <div className="bg-gray-50 p-3 rounded-md">
        <h3 className="text-base font-medium mb-3">基本信息</h3>

        <div className="space-y-3">
          {/* 用戶選擇 */}
          <div className="grid grid-cols-6 items-center gap-2">
            <Label htmlFor="userId" className="text-right col-span-1">
              <span className="text-red-500">*</span> {t('permission.user')}
            </Label>
            <div className="col-span-5">
              <Select
                value={userId}
                onValueChange={setUserId}
                disabled={!!permissionId} // 編輯時不允許修改用戶
              >
                <SelectTrigger>
                  <SelectValue placeholder="請選擇" />
                </SelectTrigger>
                <SelectContent>
                  {users.map(user => (
                    <SelectItem key={user._id} value={user._id}>
                      {user.username} ({user.name})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 姓名顯示 */}
          <div className="grid grid-cols-6 items-center gap-2">
            <Label htmlFor="userName" className="text-right col-span-1">
              姓名
            </Label>
            <div className="col-span-5">
              <Input
                id="userName"
                value={selectedUserName}
                disabled
                className="bg-gray-100"
              />
            </div>
          </div>
        </div>
      </div>

      {/* 系統權限分配 */}
      <div className="bg-gray-50 p-3 rounded-md">
        <h3 className="text-base font-medium mb-3">系統權限分配</h3>

        <div className="space-y-3">
          {/* 系統角色選擇 */}
          <div className="grid grid-cols-6 items-center gap-2">
            <Label htmlFor="systemRoleId" className="text-right col-span-1">
              系統角色
            </Label>
            <div className="col-span-5">
              <Select
                value={systemRoleId}
                onValueChange={setSystemRoleId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="無" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">無</SelectItem>
                  {systemRoles.map(role => (
                    <SelectItem key={role._id} value={role._id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* 門店權限分配 */}
      <div className="bg-gray-50 p-3 rounded-md relative">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-base font-medium">門店權限分配</h3>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="text-blue-500 hover:text-blue-700 h-7 px-2"
            onClick={addStorePermission}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {storePermissions.length === 0 ? (
          <div className="text-center text-gray-500 py-3 text-sm">
            暫無門店權限，點擊右上角添加
          </div>
        ) : (
          <div className="space-y-4 max-h-[30vh] overflow-y-auto pr-1">
            {storePermissions.map((permission, index) => (
              <div key={index} className="space-y-3 border-b pb-3 relative">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">門店權限 #{index + 1}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="text-red-500 hover:text-red-700 h-6 w-6 p-0"
                    onClick={() => removeStorePermission(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* 門店選擇 */}
                <div className="grid grid-cols-6 items-center gap-2">
                  <Label className="text-right col-span-1">
                    <span className="text-red-500">*</span> 門店
                  </Label>
                  <div className="col-span-5">
                    <Select
                      value={permission.storeId}
                      onValueChange={(value) => updateStorePermission(index, 'storeId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="請選擇" />
                      </SelectTrigger>
                      <SelectContent>
                        {stores.map(store => (
                          <SelectItem key={store.sn} value={store.id || store.sn.toString()}>
                            {store.name || store.id || `門店 ${store.sn}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* 門店角色選擇 */}
                <div className="grid grid-cols-6 items-center gap-2">
                  <Label className="text-right col-span-1">
                    <span className="text-red-500">*</span> 門店角色
                  </Label>
                  <div className="col-span-5">
                    <Select
                      value={permission.roleId}
                      onValueChange={(value) => updateStorePermission(index, 'roleId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="請選擇" />
                      </SelectTrigger>
                      <SelectContent>
                        {storeRoles.map(role => (
                          <SelectItem key={role._id} value={role._id}>
                            {role.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <DialogFooter className="pt-2">
        <DialogClose asChild>
          <Button variant="outline" onClick={onClose} size="sm">
            取消
          </Button>
        </DialogClose>
        <Button onClick={handleSubmit} disabled={loading} size="sm">
          {loading ? '保存中...' : '確定'}
        </Button>
      </DialogFooter>
    </div>
  );
};
