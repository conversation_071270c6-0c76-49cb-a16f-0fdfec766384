/* 懸浮球動畫效果 */

/* 重置全局邊框樣式，避免方格線 */
.fab-main-button,
.fab-sub-button,
.fab-main-button *,
.fab-sub-button * {
  border: none !important;
}

/* 只保留需要的邊框 */
.fab-apple-glass {
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.fab-main-button .fab-decorator-ring {
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
}

/* 脈衝動畫 */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

/* Apple風格彈性進入動畫 */
@keyframes apple-bounce-in {
  0% {
    opacity: 0;
    transform: scale(0) rotate(-90deg);
  }
  60% {
    opacity: 0.9;
    transform: scale(1.15) rotate(10deg);
  }
  80% {
    opacity: 1;
    transform: scale(0.95) rotate(-5deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Apple風格彈性退出動畫 */
@keyframes apple-bounce-out {
  0% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
  30% {
    opacity: 0.8;
    transform: scale(1.1) rotate(15deg);
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(90deg);
  }
}

/* 波紋擴散動畫 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 旋轉光暈動畫 */
@keyframes rotate-glow {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.2;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 0.4;
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.2;
  }
}

/* 懸浮效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* 主按鈕類 */
.fab-main-button {
  animation: float 3s ease-in-out infinite;
}

.fab-main-button:hover {
  animation: none;
}

/* Apple風格子按鈕進入動畫 */
.fab-sub-button-enter {
  animation: apple-bounce-in 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

/* Apple風格子按鈕退出動畫 */
.fab-sub-button-exit {
  animation: apple-bounce-out 0.5s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
}

/* 光暈脈衝效果 */
.fab-glow-pulse {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 旋轉光暈效果 */
.fab-rotate-glow {
  animation: rotate-glow 4s linear infinite;
}

/* 波紋效果 */
.fab-ripple {
  animation: ripple 0.6s ease-out forwards;
}

/* 玻璃效果 */
.fab-glass {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 漸變邊框動畫 */
@keyframes gradient-border {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.fab-gradient-border {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 300% 300%;
  animation: gradient-border 3s ease infinite;
}

/* 懸停時的陰影效果 */
.fab-shadow-hover {
  transition: all 0.3s ease;
}

.fab-shadow-hover:hover {
  box-shadow: 
    0 10px 25px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(147, 51, 234, 0.3),
    0 0 40px rgba(236, 72, 153, 0.2);
}

/* 響應式調整 */
@media (max-width: 768px) {
  .fab-main-button {
    width: 3rem !important;
    height: 3rem !important;
  }

  .fab-sub-button {
    width: 2.5rem !important;
    height: 2.5rem !important;
  }

  /* 移動設備上的懸浮球位置調整 */
  .fab-container-mobile {
    bottom: 1rem !important;
    right: 1rem !important;
  }

  /* 移動設備上的標籤提示調整 */
  .fab-tooltip-mobile {
    right: 3rem !important;
    font-size: 0.75rem !important;
    padding: 0.25rem 0.5rem !important;
  }
}

/* Apple風格磨砂玻璃效果 */
.fab-apple-glass {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* Apple風格按鈕懸停效果 */
.fab-apple-hover {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fab-apple-hover:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Apple風格主按鈕脈衝 */
@keyframes apple-main-pulse {
  0%, 100% {
    box-shadow:
      0 0 0 0 rgba(147, 51, 234, 0.4),
      0 8px 25px rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow:
      0 0 0 8px rgba(147, 51, 234, 0),
      0 12px 35px rgba(0, 0, 0, 0.2);
  }
}

.fab-main-pulse {
  animation: apple-main-pulse 2s ease-in-out infinite;
}

/* Apple風格展開指示器 */
@keyframes apple-expand-indicator {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.6;
  }
}

.fab-expand-indicator {
  animation: apple-expand-indicator 3s linear infinite;
}

/* 拖拽相關樣式 */
.fab-dragging {
  cursor: grabbing !important;
  user-select: none;
  z-index: 9999;
}

.fab-draggable {
  cursor: grab;
  transition: all 0.3s ease-out;
}

.fab-draggable:hover {
  transform: scale(1.02);
}

/* 最小化狀態樣式 */
.fab-minimized-trigger {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.8), rgba(236, 72, 153, 0.8));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fab-minimized-trigger:hover {
  width: 1.5rem !important;
  background: linear-gradient(135deg, rgba(147, 51, 234, 1), rgba(236, 72, 153, 1));
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* 智能隱藏動畫 */
.fab-auto-hide {
  transform: translateY(100px);
  opacity: 0.3;
  pointer-events: none;
}

.fab-auto-show {
  transform: translateY(0);
  opacity: 1;
  pointer-events: auto;
}

/* 最小化按鈕樣式 */
.fab-minimize-button {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.fab-minimize-button:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 拖拽提示動畫 */
@keyframes drag-hint {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

.fab-drag-hint {
  animation: drag-hint 2s ease-in-out infinite;
}

/* 位置過渡動畫 */
.fab-position-transition {
  transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              bottom 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 深色模式適配 */
@media (prefers-color-scheme: dark) {
  .fab-glass {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .fab-apple-glass {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .fab-minimized-trigger {
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.9), rgba(236, 72, 153, 0.9));
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .fab-minimize-button {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}
