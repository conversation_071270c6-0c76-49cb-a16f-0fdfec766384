// 測試 test-ws-client-interactive.js 的存檔功能
console.log('=== 測試 test-ws-client-interactive.js 存檔功能 ===');

const fs = require('fs');
const path = require('path');

// 模擬解壓縮函數（簡化版）
const RAWDATA_FORMATS = {
  RAWDATA: 'rawdata',
  RUNLENDATA: 'runlendata'
};

function decompressRawdata(rawdata, format) {
  switch (format) {
    case RAWDATA_FORMATS.RAWDATA:
      return rawdata; // 無需解壓縮
    case RAWDATA_FORMATS.RUNLENDATA:
      // 模擬解壓縮：假設壓縮比為 50%
      const decompressed = new Uint8Array(rawdata.length * 2);
      decompressed.fill(0xFF); // 填充模擬數據
      return decompressed;
    default:
      console.warn(`未知的 rawdata 格式: ${format}，當作未壓縮處理`);
      return rawdata;
  }
}

// 模擬 saveRawData 函數
async function testSaveRawData(rawdata, deviceMac = 'unknown', imageCode = null, format = 'rawdata') {
  try {
    console.log(`\n📁 測試保存 ${format} 格式的數據，裝置 MAC: ${deviceMac}`);

    let rawBuffer;

    // 處理不同格式的 rawdata
    if (typeof rawdata === 'string') {
      console.log('處理 base64 編碼的原始數據');
      rawBuffer = Buffer.from(rawdata, 'base64');
    } else if (Buffer.isBuffer(rawdata)) {
      rawBuffer = rawdata;
    } else if (Array.isArray(rawdata)) {
      rawBuffer = Buffer.from(rawdata);
    } else {
      console.error('不支援的 rawdata 格式:', typeof rawdata);
      return;
    }

    if (!Buffer.isBuffer(rawBuffer) || rawBuffer.length === 0) {
      console.warn('無效的原始數據 buffer，跳過保存');
      return;
    }

    // 建立保存原始數據的目錄
    const saveDir = path.join(__dirname, 'test_saved_images');
    if (!fs.existsSync(saveDir)) {
      fs.mkdirSync(saveDir, { recursive: true });
    }

    // 根據格式調整檔案名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `${format}_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
    const filePath = path.join(saveDir, fileName);

    // 寫入文件
    fs.writeFileSync(filePath, rawBuffer);

    console.log(`✅ 已成功將 ${format} 數據保存到: ${fileName}`);
    console.log(`📊 數據大小: ${rawBuffer.length} 字節`);

    // 如果是壓縮格式，嘗試解壓縮驗證
    if (format !== 'rawdata') {
      try {
        const decompressed = decompressRawdata(rawBuffer, format);
        console.log(`🔧 解壓縮驗證成功，解壓縮後大小: ${decompressed.length} 字節`);

        if (rawBuffer.length < decompressed.length) {
          const compressionRatio = (rawBuffer.length / decompressed.length * 100).toFixed(1);
          console.log(`📈 壓縮比: ${compressionRatio}%`);
        }

        // 保存解壓縮後的數據
        const decompressedFileName = `rawdata_decompressed_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
        const decompressedFilePath = path.join(saveDir, decompressedFileName);
        fs.writeFileSync(decompressedFilePath, Buffer.from(decompressed));
        console.log(`💾 解壓縮數據已保存到: ${decompressedFileName}`);
      } catch (decompressError) {
        console.error('❌ 解壓縮驗證失敗:', decompressError.message);
      }
    }

    // 如果提供了 imageCode，記錄關聯
    if (imageCode) {
      console.log(`🔗 關聯的 imageCode: ${imageCode}`);
    }

    // 顯示原始數據的前幾個字節（用於調試）
    const previewBytes = rawBuffer.subarray(0, Math.min(16, rawBuffer.length));
    console.log(`🔍 數據前 ${previewBytes.length} 字節 (hex): ${Buffer.from(previewBytes).toString('hex')}`);

    return true;
  } catch (err) {
    console.error('❌ 保存原始數據時出錯:', err.message);
    return false;
  }
}

// 模擬 saveBase64Image 函數
async function testSaveBase64Image(base64Data, deviceMac, imageCode = null) {
  try {
    console.log(`\n🖼️ 測試保存圖片，裝置 MAC: ${deviceMac}`);

    if (imageCode) {
      console.log(`🔗 關聯的 imageCode: ${imageCode}`);
    }

    // 從 Data URL 中提取 base64 數據部分
    const matches = base64Data.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);

    if (!matches || matches.length !== 3) {
      console.log('❌ 無效的 data URL 格式');
      return false;
    }

    // 提取 MIME 類型和 base64 數據
    const mimeType = matches[1];
    const base64Buffer = matches[2];
    const extension = mimeType.split('/')[1] || 'png';

    // 創建圖像數據的 buffer
    const imageBuffer = Buffer.from(base64Buffer, 'base64');

    // 建立保存圖像的目錄
    const saveDir = path.join(__dirname, 'test_saved_images');
    if (!fs.existsSync(saveDir)) {
      fs.mkdirSync(saveDir, { recursive: true });
    }

    // 創建文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `preview_${deviceMac.replace(/:/g, '')}_${timestamp}.${extension}`;
    const filePath = path.join(saveDir, fileName);

    // 寫入文件
    fs.writeFileSync(filePath, imageBuffer);

    console.log(`✅ 已成功將圖像保存到: ${fileName}`);
    console.log(`📊 圖像大小: ${imageBuffer.length} 字節`);

    return true;
  } catch (err) {
    console.error('❌ 保存圖像文件時出錯:', err.message);
    return false;
  }
}

// 執行測試
async function runTests() {
  console.log('\n🧪 開始測試存檔功能...\n');

  // 測試案例 1: rawdata 格式
  console.log('=== 測試案例 1: rawdata 格式 ===');
  const rawdataArray = new Array(100).fill(0).map((_, i) => i % 256);
  await testSaveRawData(rawdataArray, '00:11:22:33:44:55', 'img_001', 'rawdata');

  // 測試案例 2: runlendata 格式
  console.log('\n=== 測試案例 2: runlendata 格式 ===');
  const runlendataArray = new Array(50).fill(0).map((_, i) => i % 128);
  await testSaveRawData(runlendataArray, '00:11:22:33:44:66', 'img_002', 'runlendata');

  // 測試案例 3: base64 字符串
  console.log('\n=== 測試案例 3: base64 字符串 ===');
  const base64String = Buffer.from('Hello World Test Data').toString('base64');
  await testSaveRawData(base64String, '00:11:22:33:44:77', 'img_003', 'rawdata');

  // 測試案例 4: 圖片數據
  console.log('\n=== 測試案例 4: 圖片數據 ===');
  // 模擬一個簡單的 PNG 圖片 data URL
  const mockPngData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
  await testSaveBase64Image(mockPngData, '00:11:22:33:44:88', 'img_004');

  // 測試案例 5: 分片重組數據
  console.log('\n=== 測試案例 5: 分片重組數據 ===');
  const chunkedData = new Array(200).fill(0).map((_, i) => (i * 3) % 256);
  await testSaveRawData(chunkedData, '00:11:22:33:44:99', 'img_005', 'runlendata');

  console.log('\n🎉 所有測試完成！');
  
  // 顯示保存的文件
  const saveDir = path.join(__dirname, 'test_saved_images');
  if (fs.existsSync(saveDir)) {
    const files = fs.readdirSync(saveDir);
    console.log(`\n📁 已保存 ${files.length} 個文件到 test_saved_images 目錄:`);
    files.forEach(file => {
      const filePath = path.join(saveDir, file);
      const stats = fs.statSync(filePath);
      console.log(`  - ${file} (${stats.size} bytes)`);
    });
  }
}

// 清理測試文件
function cleanup() {
  const saveDir = path.join(__dirname, 'test_saved_images');
  if (fs.existsSync(saveDir)) {
    const files = fs.readdirSync(saveDir);
    files.forEach(file => {
      fs.unlinkSync(path.join(saveDir, file));
    });
    fs.rmdirSync(saveDir);
    console.log('\n🧹 已清理測試文件');
  }
}

// 主函數
async function main() {
  try {
    await runTests();
    
    console.log('\n📋 功能驗證總結:');
    console.log('✅ rawdata 格式存檔');
    console.log('✅ runlendata 格式存檔');
    console.log('✅ 解壓縮驗證和壓縮比計算');
    console.log('✅ 解壓縮數據單獨存檔');
    console.log('✅ base64 字符串處理');
    console.log('✅ 圖片數據存檔');
    console.log('✅ imageCode 關聯記錄');
    console.log('✅ 數據預覽顯示');
    
    console.log('\n🔧 test-ws-client-interactive.js 存檔功能已與 ws-client-from-copied-info.js 同步！');
    
    // 詢問是否清理測試文件
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    rl.question('\n是否清理測試文件？(y/n): ', (answer) => {
      if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
        cleanup();
      }
      rl.close();
    });
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
  }
}

// 啟動測試
main();
