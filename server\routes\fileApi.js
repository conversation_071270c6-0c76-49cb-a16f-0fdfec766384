// server/routes/fileApi.js
const express = require('express');
const { ObjectId } = require('mongodb');
const multer = require('multer');
const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });

// 數據庫連接函數引用
let connectDBFunction;

// 初始化數據庫連接
function initDB(connectDB) {
  connectDBFunction = connectDB;
  return connectDBFunction;
}

// 中間件處理文件名亂碼問題
const fixFileNameEncoding = (req, res, next) => {
  if (req.file) {
    // 單文件上傳情況
    req.file.originalname = Buffer.from(req.file.originalname, "latin1").toString("utf-8");
  } 
  
  if (req.files) {
    // 多文件上傳情況
    req.files.forEach(file => {
      file.originalname = Buffer.from(file.originalname, "latin1").toString("utf-8");
    });
  }
  next();
};

// API 路由 - 單文件上傳
router.post('/files/upload', upload.single('file'), fixFileNameEncoding, async (req, res) => {
  try {
    const { gridFSBucket } = await connectDBFunction();
    const { originalname, mimetype, buffer } = req.file;
    
    // 保存原始檔案名到 metadata 中
    const uploadStream = gridFSBucket.openUploadStream(originalname, {
      metadata: { 
        mimetype, 
        originalFilename: originalname 
      }
    });

    uploadStream.write(buffer);
    uploadStream.end();

    uploadStream.on('finish', () => {
      res.status(200).json({ message: '文件上傳成功', id: uploadStream.id });
    });

    uploadStream.on('error', (error) => {
      res.status(500).json({ error: '上傳失敗' });
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// API路由 - 多文件上傳
router.post('/files/upload-multiple', upload.any(), fixFileNameEncoding, async (req, res) => {
  try {
    const { gridFSBucket } = await connectDBFunction();
    const uploadPromises = [];
    
    for (let file of req.files) {
      const { originalname, mimetype, buffer } = file;
      
      const promise = new Promise((resolve, reject) => {
        const uploadStream = gridFSBucket.openUploadStream(originalname, {
          metadata: { 
            mimetype, 
            originalFilename: originalname 
          }
        });
        
        uploadStream.write(buffer);
        uploadStream.end();
        
        uploadStream.on('finish', () => {
          resolve({
            id: uploadStream.id,
            filename: originalname
          });
        });
        
        uploadStream.on('error', (error) => {
          reject(error);
        });
      });
      
      uploadPromises.push(promise);
    }
    
    const results = await Promise.all(uploadPromises);
    res.status(200).json({
      message: `成功上傳 ${results.length} 個文件`,
      files: results
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 獲取所有文件
router.get('/files', async (req, res) => {
  try {
    const { client } = await connectDBFunction();
    const db = client.db('resourceManagement');
    let files = await db.collection('fs.files').find().toArray();
    
    res.json(files);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 獲取單個文件
router.get('/files/:id', async (req, res) => {
  try {
    const { client, gridFSBucket } = await connectDBFunction();
    const db = client.db('resourceManagement');

    // 驗證 ObjectId 格式
    if (!ObjectId.isValid(req.params.id)) {
      return res.status(400).json({ error: '無效的文件ID格式' });
    }

    const id = new ObjectId(req.params.id);

    const fileInfo = await db.collection('fs.files').findOne({ _id: id });
    if (!fileInfo) {
      return res.status(404).json({ error: '找不到檔案' });
    }

    const contentType = fileInfo.metadata?.mimetype || 'application/octet-stream';
    const fileName = fileInfo.metadata?.originalFilename || fileInfo.filename;

    res.set('Content-Type', contentType);

    if (req.query.download === 'true') {
      res.set('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(fileName)}`);
    }

    const downloadStream = gridFSBucket.openDownloadStream(id);

    downloadStream.on('error', (error) => {
      console.error('文件下載流錯誤:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: '文件讀取失敗' });
      }
    });

    downloadStream.pipe(res);
  } catch (error) {
    console.error('獲取文件失敗:', error);
    if (!res.headersSent) {
      res.status(500).json({ error: error.message });
    }
  }
});

// 刪除文件
router.delete('/files/:id', async (req, res) => {
  try {
    const { gridFSBucket } = await connectDBFunction();
    const id = new ObjectId(req.params.id);
    
    await gridFSBucket.delete(id);
    res.json({ message: '文件刪除成功' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = { router, initDB };
