import React from 'react';
import { TemplateElement, DisplayColorType } from '../../../types';
import { FormField, NumberInput, RestrictedColorInput, PropertyDivider } from './FormComponents';

interface RectanglePropertiesProps {
  element: TemplateElement;
  updateElement: (updates: Partial<TemplateElement>) => void;
  colorType?: string | DisplayColorType; // 新增：模板的顏色類型
}

export const RectangleProperties: React.FC<RectanglePropertiesProps> = ({
  element,
  updateElement,
  colorType
}) => {
  // 只有當元素類型為矩形或正方形時才顯示
  if (element.type !== 'rectangle' && element.type !== 'square') {
    return null;
  }

  return (
    <div>
      <FormField label="線條寬度">
        <NumberInput
          value={element.lineWidth || 1}
          onChange={(value) => updateElement({ lineWidth: value })}
          min={0}
          max={10}
        />
      </FormField>

      <PropertyDivider />

      <FormField label="線條顏色">
        <RestrictedColorInput
          value={element.lineColor || '#000000'}
          onChange={(value) => updateElement({ lineColor: value })}
          colorType={colorType}
        />
      </FormField>

      <PropertyDivider />

      <FormField label="填充顏色">
        <RestrictedColorInput
          value={element.fillColor || 'transparent'}
          onChange={(value) => updateElement({ fillColor: value })}
          colorType={colorType}
        />
      </FormField>
    </div>
  );
};
