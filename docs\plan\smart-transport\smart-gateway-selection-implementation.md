# 智能網關選擇實現文檔

## 概述

本文檔描述了智能網關選擇機制的完整實現，包括單設備發送和批量發送的智能選擇邏輯。系統支持任務隊列機制，當主要網關正在進行chunk傳輸時，會自動選擇其他空閒網關來發送，或將任務重新排隊等待處理。

## 核心功能

### 1. Chunk傳輸狀態追蹤

在 `websocketService.js` 中實現了完整的狀態追蹤系統：

- **activeChunkTransmissions Map**: 追蹤每個網關的chunk傳輸狀態
- **isGatewayBusyWithChunk(gatewayId)**: 檢查網關是否正在進行chunk傳輸
- **startChunkTransmission(gatewayId, chunkId, deviceMac)**: 開始chunk傳輸狀態追蹤
- **endChunkTransmission(gatewayId, chunkId)**: 結束chunk傳輸狀態追蹤
- **getAvailableGateways(gatewayIds)**: 獲取指定網關列表中所有空閒的網關
- **getGatewayTransmissionStatus()**: 獲取所有網關的傳輸狀態

### 2. 智能網關選擇邏輯

#### 單設備發送邏輯
在 `sendPreviewToGateway.js` 的 `sendDevicePreviewToGateway` 函數中：

**選擇條件**：
- 只有當設備的 `gatewaySelectionMode` 為 `'auto'` 時才啟用智能選擇
- 檢查主要網關是否正在進行chunk傳輸
- 如果主要網關忙碌，從 `otherGateways` 中選擇空閒的備用網關

**選擇流程**：
1. **檢查主要網關狀態**: 使用 `isGatewayBusyWithChunk()` 檢查主要網關是否忙碌
2. **尋找備用網關**: 從設備的 `otherGateways` 中找到空閒的網關
3. **優先選擇**: 選擇第一個空閒的備用網關
4. **等待機制**: 如果沒有備用網關，等待主要網關完成傳輸（最多30秒）

#### 批量發送任務隊列邏輯
在 `sendMultipleDevicePreviewsToGateways` 函數中實現了更高級的任務隊列機制：

**任務隊列處理**：
1. **創建任務隊列**: 將所有設備ID轉換為任務對象
2. **隊列循環處理**: 持續處理隊列中的任務，直到全部完成
3. **並發控制**: 限制同時處理的任務數量
4. **智能網關選擇**: 每個任務獨立進行網關選擇
5. **任務重新排隊**: 無法立即處理的任務重新排隊

### 3. 預防性標記機制

為了防止多個任務同時使用同一個網關造成衝突，實現了預防性標記機制：

**預防性標記流程**：
1. **檢查網關狀態**: 使用 `isGatewayBusyWithChunk()` 檢查網關是否忙碌
2. **預防性標記**: 使用 `startChunkTransmission()` 預先標記網關為忙碌
3. **執行任務**: 執行實際的設備發送任務
4. **清理標記**: 使用 `endChunkTransmission()` 清理預防性標記

**標記ID生成**：
```javascript
// 單設備發送
const preChunkId = `pre_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

// 任務隊列
const preTaskChunkId = `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
```

### 4. 錯誤處理和狀態清理

#### 狀態清理機制
- **成功情況**: 任務完成後自動清理預防性標記
- **失敗情況**: 使用 try-catch-finally 確保標記被清理
- **異常情況**: 即使發生未預期的錯誤也會清理標記

#### 任務重試機制
- **重試條件**: 網關忙碌、網關離線等可恢復錯誤
- **重試次數**: 最多重試3次
- **重試策略**: 失敗任務重新排隊到隊列末尾

## 使用方式

### 1. 啟用智能網關選擇

設備需要設置為自動網關選擇模式：
```javascript
device.gatewaySelectionMode = 'auto'
```

### 2. 配置備用網關

設備需要配置備用網關：
```javascript
device.otherGateways = ['gateway2_id', 'gateway3_id']
```

### 3. 單設備發送

```javascript
const result = await sendDevicePreviewToGateway(deviceId, options);

// 檢查是否使用了備用網關
if (result.actualUsedGateway.isUsingBackupGateway) {
  console.log(`使用了備用網關: ${result.actualUsedGateway.gatewayId}`);
  console.log(`原因: ${result.actualUsedGateway.backupGatewayReason}`);
}
```

### 4. 批量發送

```javascript
const result = await sendMultipleDevicePreviewsToGateways(deviceIds, {
  concurrency: 3,
  enableSmartSelection: true
});

// 檢查任務隊列統計
console.log(`隊列循環: ${result.performanceStats.queueCycles} 次`);
console.log(`智能模式設備: ${result.smartSelectionStats.totalAutoModeDevices} 個`);
console.log(`使用備用網關: ${result.smartSelectionStats.usedBackupGateway} 次`);
```

## 返回結果結構

### 單設備發送結果
```javascript
{
  success: true,
  deviceId: "device123",
  deviceMac: "AA:BB:CC:DD:EE:FF",
  primaryGateway: {
    gatewayId: "primary_gateway_id",
    gatewayName: "主要網關",
    success: true,
    message: "..."
  },
  actualUsedGateway: {
    gatewayId: "backup_gateway_id",
    gatewayName: "備用網關",
    isUsingBackupGateway: true,
    backupGatewayReason: "主要網關正在進行chunk傳輸",
    backupGatewayFailed: false
  },
  smartGatewaySelection: {
    enabled: true,
    primaryGatewayBusy: true,
    usedBackupGateway: true
  },
  otherGateways: [],
  timestamp: "2024-01-01T12:00:00.000Z"
}
```

### 批量發送結果
```javascript
{
  success: true,
  totalCount: 10,
  successCount: 9,
  failedCount: 1,
  results: [...], // 每個設備的詳細結果
  smartSelectionStats: {
    totalAutoModeDevices: 8,
    usedBackupGateway: 5,
    primaryGatewayBusy: 6
  },
  performanceStats: {
    totalProcessingTime: 15000,
    avgProcessingTime: 1500,
    queueCycles: 12,
    concurrency: 3,
    retryCount: 3
  },
  failedDevices: [...], // 失敗設備的詳細信息
  timestamp: "2024-01-01T12:00:00.000Z"
}
```

## 日誌記錄

### 單設備發送日誌
```
設備 device123 處於智能網關選擇模式，檢查主要網關是否忙碌
主要網關 gateway1 正在進行chunk傳輸，尋找備用網關
可用的空閒網關: gateway2, gateway3
選擇備用網關 gateway2 進行傳輸
🔒 預先標記網關 gateway2 為忙碌，防止併發傳輸
✅ 圖片已通過備用網關 gateway2 智能傳輸發送
🔓 清理網關 gateway2 的預先標記
```

### 批量發送任務隊列日誌
```
🚀 開始批量發送 5 個設備，使用任務隊列機制，智能選擇: true
🌐 當前連接的網關數量: 3，最大建議並發數: 6，實際使用並發數: 3
📋 創建任務隊列，總任務數: 5
🔄 任務隊列循環 1，剩餘任務: 5，正在處理: 0
🤖 設備 device1 智能模式 - 檢查主要網關 gateway1 狀態
✅ 主要網關 gateway1 空閒，直接使用
🔒 任務隊列預先標記網關 gateway1 為忙碌，防止併發
🚀 開始處理設備 device1，使用網關 gateway1 (主要)
✅ 設備 device1 發送成功，使用網關 gateway1
🔓 任務隊列清理網關 gateway1 的預防性標記
🎯 任務隊列處理完成，總循環: 8，完成任務: 5
```

## 性能優勢

### 1. 任務隊列機制
- **避免阻塞等待**: 網關忙碌時任務重新排隊，而不是等待
- **真正的並發處理**: 多個任務可以同時使用不同的網關
- **智能重試**: 失敗任務自動重新排隊，提高成功率

### 2. 預防性標記
- **防止併發衝突**: 預先標記網關為忙碌，避免多個任務同時使用同一網關
- **精確狀態追蹤**: 使用真實的chunk傳輸狀態進行判斷
- **自動清理**: 確保標記狀態的一致性

### 3. 智能負載均衡
- **動態網關選擇**: 每個任務都重新檢查網關狀態
- **充分利用資源**: 自動分散到不同網關，避免單點瓶頸
- **備用網關利用**: 充分利用備用網關資源

### 4. 詳細監控
- **實時統計**: 提供詳細的處理統計和性能數據
- **問題診斷**: 豐富的日誌信息便於問題排查
- **性能調優**: 幫助優化並發數量和網關配置

## 配置建議

### 1. 網關配置
- **網關數量**: 建議每個門店至少配置2-3個網關
- **設備分配**: 將設備均勻分配到不同的主要網關
- **備用網關**: 為每個設備配置2-3個備用網關

### 2. 並發配置
- **動態調整**: 系統自動根據網關數量調整最大並發數
- **建議值**: 並發數 = 網關數量 × 2（但不超過8）
- **監控調整**: 根據實際性能表現調整並發數量

### 3. 任務隊列配置
- **重試次數**: 默認最多重試3次
- **隊列循環限制**: 默認最多50次循環
- **超時設置**: chunk傳輸狀態5分鐘後自動清理

## 注意事項

### 1. 兼容性
- **智能模式**: 只有設置為 `auto` 模式的設備才會使用智能選擇
- **手動模式**: `manual` 模式設備只使用主要網關
- **向後兼容**: 現有功能完全不受影響

### 2. 狀態管理
- **網關在線**: 備用網關必須在線才能被選擇
- **狀態同步**: 確保網關狀態的實時性和準確性
- **清理機制**: 自動清理過期的傳輸狀態

### 3. 錯誤處理
- **重試邏輯**: 可恢復錯誤會觸發重試
- **失敗處理**: 最終失敗的任務會被正確記錄
- **狀態清理**: 無論成功失敗都會清理相關狀態

## 測試建議

### 1. 功能測試
- **智能選擇**: 測試主要網關忙碌時的備用網關選擇
- **任務隊列**: 測試任務重新排隊和重試機制
- **混合模式**: 測試auto和manual模式設備的混合處理

### 2. 性能測試
- **並發處理**: 測試不同並發數量下的性能表現
- **負載均衡**: 測試網關負載分布是否均勻
- **大批量**: 測試大批量設備的處理效率

### 3. 錯誤測試
- **網關離線**: 測試備用網關離線時的處理
- **傳輸失敗**: 測試各種傳輸失敗情況的恢復
- **狀態異常**: 測試狀態不一致時的處理機制

## 實現完成狀態

✅ **已完成的核心功能**:
1. **Chunk傳輸狀態追蹤機制**: 完整的網關狀態追蹤系統
2. **智能網關選擇邏輯**: 單設備和批量發送的智能選擇
3. **任務隊列系統**: 真正的任務重新排隊機制
4. **預防性標記機制**: 防止併發衝突的標記系統
5. **詳細的日誌記錄**: 完整的處理過程日誌
6. **狀態清理機制**: 確保狀態一致性的清理邏輯
7. **統計信息收集**: 詳細的性能和使用統計

✅ **測試結果**:
- **基本功能**: 智能選擇邏輯正確工作
- **任務隊列**: 任務重新排隊和重試機制正常
- **併發處理**: 並發控制和預防性標記有效
- **狀態追蹤**: 網關狀態追蹤準確可靠
- **錯誤處理**: 各種錯誤情況處理正確
- **性能表現**: 批量發送效率顯著提升

## 部署說明

### 1. 代碼更新
- **websocketService.js**: 新增狀態追蹤和網關選擇API
- **sendPreviewToGateway.js**: 實現任務隊列和智能選擇邏輯
- **deviceApi.js**: 更新批量發送API路由
- **deviceApi.ts**: 更新前端API調用

### 2. API擴展
- **isGatewayBusyWithChunk**: 檢查網關忙碌狀態
- **getAvailableGateways**: 獲取可用網關列表
- **startChunkTransmission**: 開始傳輸狀態追蹤
- **endChunkTransmission**: 結束傳輸狀態追蹤
- **getGatewayTransmissionStatus**: 獲取傳輸狀態

### 3. 向後兼容
- **現有功能**: 完全兼容，不影響現有設備
- **漸進式啟用**: 只有 `auto` 模式設備使用新功能
- **配置靈活**: 可以選擇性啟用智能選擇功能

### 4. 無需額外配置
- **自動啟用**: 功能自動啟用，無需手動配置
- **動態調整**: 並發數量根據網關數量自動調整
- **智能適應**: 系統自動適應不同的網關配置

## 架構優勢

### 1. 任務隊列系統
- **真正的非阻塞**: 任務重新排隊而不是等待
- **高效並發**: 充分利用多網關資源
- **智能重試**: 自動處理可恢復的錯誤

### 2. 預防性標記
- **併發安全**: 防止多個任務同時使用同一網關
- **狀態一致**: 確保網關狀態的準確性
- **自動清理**: 避免狀態洩漏和不一致

### 3. 智能選擇
- **動態決策**: 每個任務都重新評估網關狀態
- **負載均衡**: 自動分散到不同網關
- **容錯能力**: 備用網關提供冗餘保障

### 4. 詳細監控
- **全面統計**: 涵蓋性能、使用情況、錯誤信息
- **實時反饋**: 即時了解系統運行狀態
- **問題診斷**: 豐富的日誌信息便於排查

## 使用效果總結

### 單設備發送
- **智能選擇**: 主要網關忙碌時自動選擇備用網關
- **預防標記**: 避免併發衝突，確保傳輸穩定
- **狀態追蹤**: 精確追蹤網關狀態，避免誤判
- **詳細日誌**: 完整記錄選擇過程，便於問題排查

### 批量發送
- **任務隊列**: 真正的非阻塞批量處理
- **並發控制**: 可配置的並發數量，平衡效率和穩定性
- **智能分配**: 自動分散到不同網關，避免單點瓶頸
- **統計報告**: 詳細的處理統計和性能數據
- **混合支援**: auto和manual模式設備可以混合處理

這個實現確保了智能網關選擇功能在各種場景下都能穩定、高效地工作，大幅提升了系統的整體性能和可靠性。
