# 系統專屬數據實現計劃

## 1. 概述

「系統數據」頁面將與「門店數據」頁面共用大部分代碼，但有以下關鍵差異：
- 系統專屬數據不與特定門店關聯，而是存儲在系統級別
- 當新增門店時，系統專屬數據會被複製到新門店的 storeSpecificData 中
- 系統專屬數據的 API 端點需要與門店數據區分開

## 2. 數據結構設計

在 MongoDB 中創建一個新的集合 `systemSpecificData` 來存儲系統專屬數據，結構與 `storeSpecificData` 相似：

```javascript
{
  _id: ObjectId('...'),
  uid: '...',  // 唯一識別碼
  id: '...',   // 數據ID
  // 其他動態欄位，與門店數據相同
}
```

## 3. 後端實現方案

### 3.1 創建新的 API 路由

- 新增 `systemSpecificDataApi.js` 文件，實現與 `storeDataApi.js` 類似的功能
- 提供 CRUD 操作：獲取、創建、更新、刪除系統專屬數據

```javascript
// 獲取所有系統專屬數據
router.get('/systemSpecificData', authenticate, checkPermission(['system:view', 'system-data:view']), async (req, res) => {
  try {
    const { systemSpecificDataCollection } = await getCollection();
    const systemSpecificData = await systemSpecificDataCollection.find().toArray();
    res.json(systemSpecificData);
  } catch (error) {
    console.error('獲取系統專屬數據失敗:', error);
    res.status(500).json({ error: '獲取系統專屬數據失敗' });
  }
});

// 創建系統專屬數據
router.post('/systemSpecificData', authenticate, checkPermission(['system:create', 'system-data:create']), async (req, res) => {
  try {
    const systemSpecificData = req.body;
    // 檢查 ID 是否已存在
    // 創建新的系統專屬數據
    // 返回創建的數據
  } catch (error) {
    console.error('創建系統專屬數據失敗:', error);
    res.status(500).json({ error: '創建系統專屬數據失敗' });
  }
});

// 更新和刪除系統專屬數據的路由...
```

### 3.2 新增門店時的數據複製機制

在 `storeApi.js` 的創建門店路由中，獲取所有系統專屬數據並複製到新門店：

```javascript
// 創建門店
router.post('/stores', authenticate, checkPermission('store:create'), async (req, res) => {
  try {
    // 現有的門店創建邏輯...

    // 獲取系統專屬數據
    const systemSpecificDataCollection = db.collection('systemSpecificData');
    const systemSpecificData = await systemSpecificDataCollection.find().toArray();

    // 複製系統專屬數據到新門店的 storeSpecificData
    const systemSpecificDataCopy = systemSpecificData.map(item => {
      // 創建新的 _id 和 uid，但保留其他所有欄位
      const { _id, uid, ...rest } = item;
      return {
        _id: new ObjectId(),
        uid: new ObjectId().toString(),
        ...rest
      };
    });

    // 更新新門店，添加系統專屬數據副本
    const newStore = {
      ...storeData,
      status: storeData.status || 'active',
      createdAt: now,
      updatedAt: now,
      storeSpecificData: systemSpecificDataCopy,
      gatewayManagement: {},
      deviceManagement: {},
      storeSettings: {}
    };

    // 保存新門店...
  } catch (error) {
    console.error('創建門店失敗:', error);
    res.status(500).json({ error: '創建門店失敗' });
  }
});
```

## 4. 前端實現方案

### 4.1 創建 SystemSpecificDataPage 組件

基於 DatabasePage 組件，但移除門店相關邏輯：

```tsx
export function SystemSpecificDataPage() {
  // 與 DatabasePage 類似，但使用系統專屬數據 API
  // 移除所有與門店相關的邏輯和 UI 元素
}
```

### 4.2 API 工具函數

創建 `systemSpecificDataApi.ts` 文件，實現與 `storeDataApi.ts` 類似的功能：

```typescript
// 獲取所有系統專屬數據
export async function getAllSystemSpecificData(): Promise<SystemSpecificData[]> {
  try {
    const { token } = useAuthStore.getState();
    const response = await fetch(buildEndpointUrl('systemSpecificData'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include',
    });

    // 處理響應...
  } catch (error) {
    console.error('獲取系統專屬數據失敗:', error);
    throw error;
  }
}

// 創建、更新、刪除系統專屬數據的函數...
```

### 4.3 模態窗口組件

創建系統專屬數據的模態窗口組件：

```tsx
export function AddSystemSpecificDataModal({ isOpen, dataFields, onClose, onSuccess }: AddSystemSpecificDataModalProps) {
  // 與 AddStoreDataModal 類似，但使用系統專屬數據 API
  // 移除所有與門店相關的邏輯
}

export function EditSystemSpecificDataModal({ isOpen, dataFields, systemSpecificData, onClose, onSuccess }: EditSystemSpecificDataModalProps) {
  // 與 EditStoreDataModal 類似，但使用系統專屬數據 API
  // 移除所有與門店相關的邏輯
}
```

## 5. 代碼重用策略

為了最大化代碼重用，建議以下策略：

### 5.1 創建共用基礎組件

提取 DatabasePage 和 SystemSpecificDataPage 的共同邏輯到一個基礎組件：

```tsx
// 基礎數據頁面組件
export function BaseDataPage({
  fetchData,
  createData,
  updateData,
  deleteData,
  title,
  ...props
}: BaseDataPageProps) {
  // 共用的狀態和邏輯
  // 共用的 UI 結構
}

// 門店數據頁面
export function DatabasePage({ store }: DatabasePageProps) {
  return (
    <BaseDataPage
      fetchData={() => getAllStoreData(store.id)}
      createData={(data) => createStoreData(data, store.id)}
      updateData={(uid, data) => updateStoreData(uid, data, store.id)}
      deleteData={(uid) => deleteStoreData(uid, store.id)}
      title="門店數據"
      store={store}
    />
  );
}

// 系統專屬數據頁面
export function SystemSpecificDataPage() {
  return (
    <BaseDataPage
      fetchData={() => getAllSystemSpecificData()}
      createData={(data) => createSystemSpecificData(data)}
      updateData={(uid, data) => updateSystemSpecificData(uid, data)}
      deleteData={(uid) => deleteSystemSpecificData(uid)}
      title="系統數據"
    />
  );
}
```

### 5.2 參數化 API 調用

創建通用的數據操作函數：

```typescript
// 通用數據操作函數
export async function fetchData(type: 'store' | 'system', storeId?: string): Promise<any[]> {
  if (type === 'store') {
    return getAllStoreData(storeId);
  } else {
    return getAllSystemSpecificData();
  }
}

export async function createData(type: 'store' | 'system', data: any, storeId?: string): Promise<any> {
  if (type === 'store') {
    return createStoreData(data, storeId!);
  } else {
    return createSystemSpecificData(data);
  }
}

// 更新和刪除數據的通用函數...
```

### 5.3 共用模態窗口組件

擴展現有模態窗口組件：

```tsx
export function DataModal({
  isOpen,
  dataFields,
  data,
  onClose,
  onSuccess,
  type = 'store',
  storeId
}: DataModalProps) {
  // 根據 type 決定使用哪個 API
  const saveData = async (formData) => {
    if (type === 'store') {
      return createStoreData(formData, storeId!);
    } else {
      return createSystemSpecificData(formData);
    }
  };

  // 共用的表單邏輯和 UI
}
```

## 6. 權限控制

確保系統專屬數據頁面有適當的權限控制：

### 6.1 前端權限控制

```tsx
// 在側邊欄中根據權限顯示系統專屬數據選項
{hasPermission(['system:view', 'system-data:view']) && (
  <SidebarItem
    to="/system-specific-data"
    icon={<Database size={20} />}
    label="系統數據"
  />
)}
```

### 6.2 後端權限控制

在所有系統專屬數據 API 路由中檢查權限：

```javascript
router.get('/systemSpecificData', authenticate, checkPermission(['system:view', 'system-data:view']), async (req, res) => {
  // API 邏輯...
});
```

## 7. 路由配置

更新路由配置以包含系統專屬數據頁面：

```tsx
<Routes>
  {/* 現有路由... */}
  <Route
    path="/system-specific-data"
    element={
      <ProtectedRoute permissions={['system:view', 'system-data:view']}>
        <SystemSpecificDataPage />
      </ProtectedRoute>
    }
  />
</Routes>
```

## 8. 實施計劃

1. 創建後端 API 路由和數據庫集合
2. 實現前端 API 工具函數
3. 創建共用基礎組件
4. 實現 SystemSpecificDataPage 組件
5. 更新門店創建邏輯以複製系統專屬數據
6. 更新路由配置
7. 測試所有功能
