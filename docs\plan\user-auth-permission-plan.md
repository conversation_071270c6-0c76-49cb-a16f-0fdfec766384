# 用戶登入與權限管理系統實現計劃

## 1. 需求分析

根據提供的圖片和要求，我們需要實現以下功能：

### 用戶登入功能
*   登入頁面
*   JWT/Token 認證
*   Cookie 管理
*   記住登入狀態

### 權限管理功能
*   **角色管理**
    *   角色名稱
    *   角色描述
    *   角色類型
*   **人員管理**
    *   用戶名
    *   姓名
    *   手機
    *   郵箱
    *   狀態
*   **權限分配**
    *   用戶名
    *   姓名
    *   權限範圍
    *   角色

### 用戶管理功能
*   新增用戶
*   編輯用戶
*   刪除用戶
*   重設密碼
*   搜索用戶

## 2. 技術選擇

*   **前端**：React + TypeScript + Zustand（狀態管理）
*   **後端**：Express + MongoDB
*   **認證**：JWT（JSON Web Token）
*   **加密**：bcrypt（密碼加密）
*   **Cookie 管理**：js-cookie

## 3. 詳細實施計劃

### 3.1 後端實現

#### 3.1.1 模型設計 (MongoDB Schema)
*   創建用戶模型 (`User`)
*   創建角色模型 (`Role`)
*   創建權限分配模型 (`PermissionAssignment` 或類似名稱)

#### 3.1.2 API 路由設計

*   **認證相關 API (`/api/auth`)**
    *   `POST /login`：用戶登入
    *   `POST /logout`：用戶登出
    *   `GET /status`：檢查登入狀態
    *   `POST /reset-password`：重設密碼
*   **用戶管理 API (`/api/users`)**
    *   `GET /`：獲取用戶列表 (可帶搜索參數)
    *   `POST /`：創建用戶
    *   `PUT /:id`：更新用戶
    *   `DELETE /:id`：刪除用戶
*   **角色管理 API (`/api/roles`)**
    *   `GET /`：獲取角色列表
    *   `POST /`：創建角色
    *   `PUT /:id`：更新角色
    *   `DELETE /:id`：刪除角色
*   **權限分配 API (`/api/permissions`)**
    *   `GET /`：獲取權限分配列表
    *   `POST /`：創建權限分配
    *   `PUT /:id`：更新權限分配
    *   `DELETE /:id`：刪除權限分配

#### 3.1.3 中間件設計
*   **認證中間件**：驗證 JWT Token，附加用戶資訊到請求對象。
*   **權限檢查中間件**：根據用戶角色和請求的資源/操作檢查權限。

### 3.2 前端實現

#### 3.2.1 狀態管理設計 (Zustand Stores)
*   創建用戶認證狀態管理 (`authStore`)
    *   用戶資訊
    *   登入狀態
    *   Token
*   創建角色管理狀態 (`roleStore`)
    *   角色列表
    *   當前操作的角色
*   創建權限管理狀態 (`permissionStore`)
    *   用戶列表 (用於權限分配)
    *   權限分配列表

#### 3.2.2 頁面組件設計
*   `LoginPage.tsx`：登入表單頁面
*   `RoleManagementPage.tsx`：角色列表、新增/編輯/刪除角色功能
*   `UserManagementPage.tsx`：人員列表、新增/編輯/刪除/重設密碼/搜索用戶功能
*   `PermissionAssignmentPage.tsx`：權限分配列表、分配權限功能

#### 3.2.3 共用組件設計 (`src/components/ui` 或類似路徑)
*   `FormInput.tsx`, `FormSelect.tsx`, etc.：表單相關組件
*   `Modal.tsx`：模態窗口組件 (用於新增/編輯)
*   `DataTable.tsx`：可排序、分頁、選擇的表格組件
*   `SearchBar.tsx`：搜索輸入框組件
