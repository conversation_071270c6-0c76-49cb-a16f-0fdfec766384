# 門店刷圖計畫系統 - 技術實現詳細說明

## API 設計

### 1. 刷圖計畫管理 API

#### 1.1 獲取門店計畫列表
```http
GET /api/stores/:storeId/refresh-plans
Authorization: Bearer <token>
Cookie: token=<jwt_token>

Query Parameters:
- page: 頁碼 (default: 1)
- limit: 每頁數量 (default: 10)
- status: 計畫狀態篩選 (active|inactive|running|error)
- search: 搜索關鍵字

Response:
{
  "success": true,
  "data": {
    "plans": [...],
    "total": 25,
    "page": 1,
    "limit": 10
  }
}

實現狀態: ✅ 已完成
- 使用 buildEndpointUrl 構建 API URL
- 支援 Bearer token 和 Cookie 雙重認證
- 完整的分頁和篩選功能
- 門店權限驗證
```

#### 1.2 創建刷圖計畫
```http
POST /api/stores/:storeId/refresh-plans
Authorization: Bearer <token>
Cookie: token=<jwt_token>
Content-Type: application/json

Body:
{
  "name": "每日早晨更新",
  "description": "每天早上8點更新所有設備",
  "enabled": true,
  "priority": "medium",
  "targetSelection": {
    "type": "mac_addresses",
    "macAddresses": ["AA:BB:CC:DD:EE:01", "AA:BB:CC:DD:EE:02"]
  },
  "trigger": {
    "type": "daily",
    "executeTime": "08:00"
  },
  "execution": {
    "useSystemSettings": true,
    "enableSmartGateway": true,
    "sendToAllGateways": false
  }
}

實現狀態: ✅ 已完成
- 完整的數據驗證機制
- 自動任務調度器註冊
- 計畫名稱重複檢查
- 創建者信息記錄
```

#### 1.3 更新刷圖計畫
```http
PUT /api/stores/:storeId/refresh-plans/:planId
Authorization: Bearer <token>
Content-Type: application/json
```

#### 1.4 刪除刷圖計畫
```http
DELETE /api/stores/:storeId/refresh-plans/:planId
Authorization: Bearer <token>
```

#### 1.5 手動執行計畫
```http
POST /api/stores/:storeId/refresh-plans/:planId/execute
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "executionId": "exec_123456",
    "message": "計畫執行已啟動"
  }
}
```

### 2. 執行記錄 API

#### 2.1 獲取執行記錄
```http
GET /api/stores/:storeId/refresh-plans/:planId/executions
Authorization: Bearer <token>

Query Parameters:
- page: 頁碼
- limit: 每頁數量
- status: 執行狀態篩選
- startDate: 開始日期
- endDate: 結束日期
```

#### 2.2 獲取執行詳情
```http
GET /api/stores/:storeId/refresh-plans/executions/:executionId
Authorization: Bearer <token>
```

### 3. 統計報表 API

#### 3.1 獲取計畫統計
```http
GET /api/stores/:storeId/refresh-plans/statistics
Authorization: Bearer <token>

Query Parameters:
- period: 統計週期 (day|week|month)
- planIds: 指定計畫ID列表
```

## 數據庫設計

### 1. 刷圖計畫表 (refresh_plans)
```sql
CREATE TABLE refresh_plans (
  _id ObjectId PRIMARY KEY,
  store_id VARCHAR(50) NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  enabled BOOLEAN DEFAULT true,
  priority ENUM('high', 'medium', 'low') DEFAULT 'medium',
  
  -- 刷圖對象選擇配置 (JSON)
  target_selection JSON NOT NULL,

  -- 觸發配置 (JSON)
  trigger_config JSON NOT NULL,

  -- 執行策略配置 (JSON)
  execution_config JSON NOT NULL,
  
  -- 狀態信息
  status ENUM('active', 'inactive', 'running', 'error') DEFAULT 'active',
  last_run DATETIME,
  next_run DATETIME,
  
  -- 統計信息 (JSON)
  statistics JSON,
  
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by VARCHAR(50),
  
  INDEX idx_store_id (store_id),
  INDEX idx_status (status),
  INDEX idx_next_run (next_run)
);
```

### 2. 執行記錄表 (execution_records)
```sql
CREATE TABLE execution_records (
  _id ObjectId PRIMARY KEY,
  plan_id ObjectId NOT NULL,
  store_id VARCHAR(50) NOT NULL,
  start_time DATETIME NOT NULL,
  end_time DATETIME,
  status ENUM('running', 'completed', 'failed', 'cancelled') DEFAULT 'running',
  
  -- 執行結果 (JSON)
  result JSON,
  
  -- 設備結果詳情 (JSON)
  device_results JSON,
  
  -- 錯誤信息 (JSON)
  errors JSON,
  
  INDEX idx_plan_id (plan_id),
  INDEX idx_store_id (store_id),
  INDEX idx_start_time (start_time),
  INDEX idx_status (status)
);
```

## 核心服務實現

### 1. 計畫管理服務 (RefreshPlanService)

```javascript
// server/services/refreshPlanService.js
class RefreshPlanService {
  constructor() {
    this.scheduler = require('./taskScheduler');
    this.executionEngine = require('./executionEngine');
  }

  // 創建計畫
  async createPlan(storeId, planData) {
    try {
      // 驗證計畫數據
      this.validatePlanData(planData);
      
      // 保存到數據庫
      const plan = await this.savePlan(storeId, planData);
      
      // 如果計畫啟用，註冊到調度器
      if (plan.enabled) {
        await this.scheduler.registerPlan(plan);
      }
      
      return plan;
    } catch (error) {
      throw new Error(`創建計畫失敗: ${error.message}`);
    }
  }

  // 更新計畫
  async updatePlan(storeId, planId, updateData) {
    try {
      const existingPlan = await this.getPlan(storeId, planId);
      if (!existingPlan) {
        throw new Error('計畫不存在');
      }

      // 更新數據庫
      const updatedPlan = await this.updatePlanInDB(planId, updateData);
      
      // 重新註冊調度任務
      await this.scheduler.unregisterPlan(planId);
      if (updatedPlan.enabled) {
        await this.scheduler.registerPlan(updatedPlan);
      }
      
      return updatedPlan;
    } catch (error) {
      throw new Error(`更新計畫失敗: ${error.message}`);
    }
  }

  // 刪除計畫
  async deletePlan(storeId, planId) {
    try {
      // 取消調度任務
      await this.scheduler.unregisterPlan(planId);
      
      // 從數據庫刪除
      await this.deletePlanFromDB(storeId, planId);
      
      return { success: true };
    } catch (error) {
      throw new Error(`刪除計畫失敗: ${error.message}`);
    }
  }

  // 手動執行計畫
  async executePlan(storeId, planId) {
    try {
      const plan = await this.getPlan(storeId, planId);
      if (!plan) {
        throw new Error('計畫不存在');
      }

      // 創建執行記錄
      const executionRecord = await this.createExecutionRecord(plan);
      
      // 異步執行
      this.executionEngine.execute(plan, executionRecord._id)
        .catch(error => {
          console.error(`計畫執行失敗: ${error.message}`);
        });
      
      return { executionId: executionRecord._id };
    } catch (error) {
      throw new Error(`執行計畫失敗: ${error.message}`);
    }
  }
}
```

### 2. 任務調度器 (TaskScheduler)

實現狀態: ✅ 已完成

```javascript
// server/services/taskScheduler.js
const schedule = require('node-schedule');

class TaskScheduler {
  constructor() {
    this.jobs = new Map(); // 存儲所有調度任務
    this.executionEngine = null;
  }

  // 註冊計畫到調度器
  async registerPlan(plan) {
    try {
      // 如果已存在，先取消
      if (this.activeTasks.has(plan._id)) {
        await this.unregisterPlan(plan._id);
      }

      const triggerConfig = plan.trigger;
      let scheduledTask;

      switch (triggerConfig.type) {
        case 'once':
          // 單次執行：在指定日期的指定時間執行
          const executeDateTime = new Date(`${triggerConfig.executeDate} ${triggerConfig.executeTime}`);
          scheduledTask = schedule.scheduleJob(
            executeDateTime,
            () => this.executePlan(plan)
          );
          break;

        case 'daily':
          // 每日執行：每天在指定時間執行
          const [hour, minute] = triggerConfig.executeTime.split(':');
          const dailyRule = new schedule.RecurrenceRule();
          dailyRule.hour = parseInt(hour);
          dailyRule.minute = parseInt(minute);
          scheduledTask = schedule.scheduleJob(dailyRule, () => this.executePlan(plan));
          break;

        case 'weekly':
          // 每週執行：在指定星期的指定時間執行
          const [weekHour, weekMinute] = triggerConfig.executeTime.split(':');
          const weeklyRule = new schedule.RecurrenceRule();
          weeklyRule.dayOfWeek = triggerConfig.weekDays || [1]; // 預設週一
          weeklyRule.hour = parseInt(weekHour);
          weeklyRule.minute = parseInt(weekMinute);
          scheduledTask = schedule.scheduleJob(weeklyRule, () => this.executePlan(plan));
          break;

        default:
          throw new Error(`不支援的觸發類型: ${triggerConfig.type}`);
      }

      this.activeTasks.set(plan._id, {
        plan,
        scheduledTask,
        registeredAt: new Date()
      });

      console.log(`計畫 ${plan.name} 已註冊到調度器`);
    } catch (error) {
      throw new Error(`註冊計畫失敗: ${error.message}`);
    }
  }

  // 取消計畫調度
  async unregisterPlan(planId) {
    try {
      const taskInfo = this.activeTasks.get(planId);
      if (taskInfo) {
        taskInfo.scheduledTask.cancel();
        this.activeTasks.delete(planId);
        console.log(`計畫 ${planId} 已從調度器移除`);
      }
    } catch (error) {
      console.error(`取消計畫調度失敗: ${error.message}`);
    }
  }

  // 執行計畫
  async executePlan(plan) {
    try {
      console.log(`開始執行計畫: ${plan.name}`);
      
      // 創建執行記錄
      const executionRecord = await this.createExecutionRecord(plan);
      
      // 調用執行引擎
      await this.executionEngine.execute(plan, executionRecord._id);
      
    } catch (error) {
      console.error(`計畫執行失敗: ${error.message}`);
    }
  }

  // 獲取所有活動任務
  getActiveTasks() {
    return Array.from(this.activeTasks.values()).map(taskInfo => ({
      planId: taskInfo.plan._id,
      planName: taskInfo.plan.name,
      storeId: taskInfo.plan.storeId,
      registeredAt: taskInfo.registeredAt,
      nextRun: taskInfo.scheduledTask.nextInvocation()
    }));
  }
}
```

### 3. 執行引擎 (ExecutionEngine)

實現狀態: ✅ 已完成

```javascript
// server/services/executionEngine.js
class ExecutionEngine {
  constructor() {
    this.sendPreviewService = require('./sendPreviewToGateway');
  }

  // 執行計畫
  async execute(plan, executionId) {
    const startTime = new Date();
    
    try {
      // 更新執行記錄狀態
      await this.updateExecutionStatus(executionId, 'running', { startTime });

      // 獲取目標設備列表
      const devices = await this.getTargetDevices(plan);
      
      if (devices.length === 0) {
        throw new Error('沒有找到符合條件的設備');
      }

      console.log(`計畫 ${plan.name} 開始處理 ${devices.length} 個設備`);

      // 執行批量刷圖
      const result = await this.executeBatchRefresh(plan, devices);

      // 更新執行記錄
      await this.updateExecutionResult(executionId, 'completed', {
        endTime: new Date(),
        result,
        deviceResults: result.detailResults || []
      });

      // 更新計畫統計
      await this.updatePlanStatistics(plan._id, result);

      console.log(`計畫 ${plan.name} 執行完成`);
      
    } catch (error) {
      console.error(`計畫執行失敗: ${error.message}`);
      
      // 更新執行記錄為失敗
      await this.updateExecutionStatus(executionId, 'failed', {
        endTime: new Date(),
        errors: [{
          type: 'execution_error',
          message: error.message,
          timestamp: new Date()
        }]
      });
    }
  }

  // 獲取目標設備
  async getTargetDevices(plan) {
    const { targetSelection } = plan;
    const { collection: deviceCollection } = await require('./deviceApi').getCollection();

    let devices = [];

    switch (targetSelection.type) {
      case 'mac_addresses':
        // 指定 MAC 地址
        const query = {
          storeId: plan.storeId,
          macAddress: { $in: targetSelection.macAddresses }
        };
        devices = await deviceCollection.find(query).toArray();
        break;

      case 'store_data':
        // 指定門店數據 - 只有綁定了模板且數據綁定包含指定項目的設備
        const storeDataQuery = {
          storeId: plan.storeId,
          templateId: { $exists: true, $ne: null },  // 必須綁定模板
          'dataBindings.dataId': { $in: targetSelection.storeDataIds }  // 數據綁定包含指定項目
        };
        devices = await deviceCollection.find(storeDataQuery).toArray();
        break;

      default:
        throw new Error(`不支援的對象選擇類型: ${targetSelection.type}`);
    }

    return devices;
  }

  // 執行批量刷圖
  async executeBatchRefresh(plan, devices) {
    const deviceIds = devices.map(device => device._id.toString());
    const { execution } = plan;

    // 從系統設定讀取傳送參數
    const systemConfig = await this.getSystemConfig();

    // 使用現有的批量發送服務，完全沿用設備管理的邏輯
    // 智能網關選擇取決於裝置本身的設定，不再從計畫配置中讀取
    const result = await this.sendPreviewService.sendMultipleDevicePreviewsToGateways(
      deviceIds,
      {
        sendToAllGateways: false,  // 固定為 false，使用智能選擇
        storeId: plan.storeId,
        concurrency: systemConfig.gatewayConcurrency || 20,        // 從系統設定讀取
        enableSmartSelection: true, // 固定啟用，具體行為取決於裝置設定
        // 以下參數都從系統設定讀取，確保與設備管理批量傳送一致
        timeout: systemConfig.gatewayTimeout || 30000,
        retryAttempts: systemConfig.queueRetryAttempts || 3,
        retryInterval: systemConfig.queueRetryInterval || 5000
      }
    );

    return result;
  }

  // 獲取系統配置
  async getSystemConfig() {
    // 這裡應該調用系統設定 API 獲取配置
    // 確保與設備管理使用相同的配置來源
    const configService = require('./systemConfigService');
    return await configService.getConfig();
  }
}
```

## 前端組件設計

### 1. 計畫列表組件

實現狀態: ✅ 已完成 (RefreshPlanManagement.tsx)

```typescript
// src/components/RefreshPlanManagement.tsx
interface RefreshPlan {
  _id: string;
  name: string;
  description?: string;
  enabled: boolean;
  status: 'active' | 'inactive' | 'running' | 'error';
  lastRun?: Date;
  nextRun?: Date;
  statistics: {
    totalRuns: number;
    successRuns: number;
    failedRuns: number;
  };
}

const RefreshPlanList: React.FC = () => {
  const [plans, setPlans] = useState<RefreshPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const { store } = useStore();

  useEffect(() => {
    if (store?.id) {
      fetchPlans();
    }
  }, [store?.id]);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      // 使用統一的 API 服務
      const response = await refreshPlanApi.getPlans(store.id);
      setPlans(response.data.plans);
    } catch (error) {
      console.error('獲取計畫列表失敗:', error);
    } finally {
      setLoading(false);
    }
  };

  const togglePlanStatus = async (planId: string, enabled: boolean) => {
    try {
      await fetch(`/api/stores/${store.id}/refresh-plans/${planId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled })
      });
      await fetchPlans();
    } catch (error) {
      console.error('更新計畫狀態失敗:', error);
    }
  };

  const executePlan = async (planId: string) => {
    try {
      const response = await fetch(`/api/stores/${store.id}/refresh-plans/${planId}/execute`, {
        method: 'POST'
      });
      const data = await response.json();
      console.log('計畫執行已啟動:', data.data.executionId);
    } catch (error) {
      console.error('執行計畫失敗:', error);
    }
  };

  return (
    <div className="space-y-4">
      {plans.map(plan => (
        <RefreshPlanCard
          key={plan._id}
          plan={plan}
          onToggleStatus={togglePlanStatus}
          onExecute={executePlan}
        />
      ))}
    </div>
  );
};
```

### 2. 計畫配置組件

```typescript
// src/components/RefreshPlanConfig.tsx
const RefreshPlanConfig: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [planData, setPlanData] = useState<Partial<RefreshPlan>>({});

  const steps = [
    { id: 1, title: '基礎設定', component: BasicSettingsStep },
    { id: 2, title: '設備選擇', component: DeviceSelectionStep },
    { id: 3, title: '觸發條件', component: TriggerConfigStep },
    { id: 4, title: '執行策略', component: ExecutionConfigStep },
    { id: 5, title: '確認設定', component: ConfirmationStep }
  ];

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSave = async () => {
    try {
      const response = await fetch(`/api/stores/${store.id}/refresh-plans`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(planData)
      });
      
      if (response.ok) {
        // 返回計畫列表
        onSuccess();
      }
    } catch (error) {
      console.error('保存計畫失敗:', error);
    }
  };

  const CurrentStepComponent = steps[currentStep - 1].component;

  return (
    <div className="max-w-4xl mx-auto">
      {/* 步驟指示器 */}
      <StepIndicator steps={steps} currentStep={currentStep} />
      
      {/* 步驟內容 */}
      <div className="mt-8">
        <CurrentStepComponent
          data={planData}
          onChange={setPlanData}
        />
      </div>
      
      {/* 導航按鈕 */}
      <div className="flex justify-between mt-8">
        <button
          onClick={handlePrevious}
          disabled={currentStep === 1}
          className="px-4 py-2 bg-gray-300 text-gray-700 rounded disabled:opacity-50"
        >
          上一步
        </button>
        
        {currentStep === steps.length ? (
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            保存計畫
          </button>
        ) : (
          <button
            onClick={handleNext}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            下一步
          </button>
        )}
      </div>
    </div>
  );
};
```

## 系統整合要點

### 1. 權限控制
- 所有 API 都需要驗證用戶對指定門店的操作權限
- 計畫執行時需要檢查設備的操作權限

### 2. 錯誤處理
- 實現多層次的錯誤捕獲和記錄
- 提供詳細的錯誤信息和建議解決方案

### 3. 性能優化
- 使用數據庫索引優化查詢性能
- 實現執行記錄的定期清理機制

### 4. 監控告警
- 實現計畫執行狀態的實時監控
- 提供執行失敗的告警機制

### 5. 數據備份
- 定期備份計畫配置和執行記錄
- 提供數據恢復機制

這個技術實現方案提供了完整的門店刷圖計畫系統架構，確保各門店獨立運作、系統穩定可靠，並為未來的功能擴展預留了充足的空間。
