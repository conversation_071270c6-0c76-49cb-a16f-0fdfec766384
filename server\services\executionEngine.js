const { ObjectId } = require('mongodb');

class ExecutionEngine {
  constructor() {
    this.sendPreviewService = require('./sendPreviewToGateway');
    this.getDbConnection = null;
  }

  // 初始化數據庫連接
  setDbConnection(dbConnectionFunc) {
    this.getDbConnection = dbConnectionFunc;
  }

  // 創建執行記錄
  async createExecutionRecord(plan, executedBy = 'scheduler') {
    try {
      if (!this.getDbConnection) {
        throw new Error('數據庫連接未初始化');
      }

      const { client, db } = await this.getDbConnection();
      const executionCollection = db.collection('executionRecords');

      const executionRecord = {
        planId: plan._id,
        planName: plan.name,
        storeId: plan.storeId,
        startTime: new Date(),
        endTime: null,
        status: 'running',
        result: {
          totalDevices: 0,
          successDevices: 0,
          failedDevices: 0,
          skippedDevices: 0,
          processingTime: 0,
          // 新增詳細統計信息
          gatewayStats: {},  // 各網關的處理統計 { gatewayId: { deviceCount, successCount, failedCount } }
          deviceDetails: [], // 設備級別的詳細結果
          summary: {
            totalGatewaysUsed: 0,
            averageProcessingTimePerDevice: 0,
            successRate: 0
          }
        },
        deviceResults: [],
        errors: [],
        // 新增執行配置記錄
        executionConfig: {
          targetSelection: plan.targetSelection,
          triggerType: plan.trigger.type,
          executedBy: executedBy // 'scheduler' 或 'manual'
        }
      };

      const result = await executionCollection.insertOne(executionRecord);
      return result.insertedId.toString();
    } catch (error) {
      console.error('創建執行記錄失敗:', error);
      throw error;
    }
  }

  // 更新執行記錄狀態
  async updateExecutionStatus(executionId, status, updateData = {}) {
    try {
      const { client, db } = await this.getDbConnection();
      const executionCollection = db.collection('executionRecords');

      await executionCollection.updateOne(
        { _id: new ObjectId(executionId) },
        { $set: { status, ...updateData } }
      );
    } catch (error) {
      console.error('更新執行記錄狀態失敗:', error);
    }
  }

  // 更新執行結果
  async updateExecutionResult(executionId, status, resultData) {
    try {
      const { client, db } = await this.getDbConnection();
      const executionCollection = db.collection('executionRecords');

      await executionCollection.updateOne(
        { _id: new ObjectId(executionId) },
        { $set: { status, ...resultData } }
      );
    } catch (error) {
      console.error('更新執行結果失敗:', error);
    }
  }

  // 更新計畫統計信息
  async updatePlanStatistics(planId, result) {
    try {
      const { client, db } = await this.getDbConnection();
      const plansCollection = db.collection('refreshPlans');

      // 確保 planId 是 ObjectId 類型
      const { ObjectId } = require('mongodb');
      let objectId;

      if (typeof planId === 'string') {
        objectId = new ObjectId(planId);
      } else if (ObjectId.isValid(planId) && planId.constructor.name === 'ObjectId') {
        objectId = planId;
      } else {
        // 如果是其他類型，嘗試轉換為字符串再轉為ObjectId
        objectId = new ObjectId(planId.toString());
      }



      const isSuccess = result.success || (result.failedDevices === 0);

      const updateResult = await plansCollection.findOneAndUpdate(
        { _id: objectId },
        {
          $set: {
            lastRun: new Date(),
            'statistics.lastRunResult': result
          },
          $inc: {
            'statistics.totalRuns': 1,
            'statistics.successRuns': isSuccess ? 1 : 0,
            'statistics.failedRuns': isSuccess ? 0 : 1
          }
        },
        { returnDocument: 'after' }
      );

      // 廣播計畫統計更新事件
      if (updateResult.value) {
        try {
          const websocketService = require('./websocketService');
          websocketService.broadcastRefreshPlanUpdate(updateResult.value.storeId, {
            planId: planId.toString(),
            planData: {
              ...updateResult.value,  // 廣播完整的計畫數據
              _id: planId.toString(),
              updatedFields: ['lastRun', 'statistics']
            }
          }, 'update');
        } catch (error) {
          console.error('廣播計畫統計更新事件失敗:', error);
        }
      }
    } catch (error) {
      console.error('更新計畫統計失敗:', error);
    }
  }

  // 執行計畫
  async execute(plan, executionId) {
    const startTime = new Date();
    
    try {
      console.log(`開始執行計畫: ${plan.name} (執行ID: ${executionId})`);

      // 更新計畫狀態為運行中
      await this.updatePlanStatus(plan._id, 'running');

      // 更新執行記錄狀態
      await this.updateExecutionStatus(executionId, 'running', { startTime });

      // 獲取目標設備列表
      const devices = await this.getTargetDevices(plan);

      console.log(`計畫 ${plan.name} 找到 ${devices.length} 個符合條件的設備`);

      if (devices.length === 0) {
        console.log(`計畫 ${plan.name} 沒有找到符合條件的設備，將標記為失敗`);
        throw new Error('沒有找到符合條件的設備');
      }

      console.log(`計畫 ${plan.name} 開始處理 ${devices.length} 個設備`);

      // 執行批量刷圖
      const result = await this.executeBatchRefresh(plan, devices);

      // 處理詳細統計信息
      const enhancedResult = await this.enhanceExecutionResult(result, devices, startTime);

      // 根據批量發送的實際結果決定執行狀態
      const executionStatus = result.success ? 'completed' : 'failed';
      const planStatus = result.success ? 'active' : 'error';

      console.log(`批量發送結果: success=${result.success}, successCount=${result.successCount}, failedCount=${result.failedCount}`);
      console.log(`執行狀態將設為: ${executionStatus}, 計畫狀態將設為: ${planStatus}`);

      // 更新執行記錄
      await this.updateExecutionResult(executionId, executionStatus, {
        endTime: new Date(),
        result: enhancedResult,
        deviceResults: enhancedResult.deviceDetails || [],
        // 如果批量發送失敗，添加錯誤信息
        ...(result.success ? {} : {
          errors: [{
            type: 'batch_send_error',
            message: `批量發送失敗: ${result.failedCount}/${result.totalCount} 設備發送失敗`,
            timestamp: new Date(),
            details: {
              totalDevices: result.totalCount,
              successDevices: result.successCount,
              failedDevices: result.failedCount,
              mainError: result.error || '部分或全部設備發送失敗'
            }
          }]
        })
      });

      // 更新計畫統計
      await this.updatePlanStatistics(plan._id, result);

      // 更新計畫狀態
      await this.updatePlanStatus(plan._id, planStatus);

      if (result.success) {
        console.log(`計畫 ${plan.name} 執行完成，所有設備發送成功`);
      } else {
        console.log(`計畫 ${plan.name} 執行完成，但有設備發送失敗 (${result.successCount}/${result.totalCount} 成功)`);
      }
      
    } catch (error) {
      console.error(`計畫執行失敗: ${error.message}`);
      
      // 更新執行記錄為失敗
      await this.updateExecutionStatus(executionId, 'failed', {
        endTime: new Date(),
        errors: [{
          type: 'execution_error',
          message: error.message,
          timestamp: new Date()
        }]
      });

      // 更新計畫狀態為錯誤
      await this.updatePlanStatus(plan._id, 'error');

      // 更新計畫統計（失敗）
      await this.updatePlanStatistics(plan._id, { success: false, error: error.message });
    }
  }

  // 更新計畫狀態
  async updatePlanStatus(planId, status) {
    try {
      const { client, db } = await this.getDbConnection();
      const plansCollection = db.collection('refreshPlans');

      // 確保 planId 是 ObjectId 類型
      const { ObjectId } = require('mongodb');
      let objectId;

      if (typeof planId === 'string') {
        objectId = new ObjectId(planId);
      } else if (ObjectId.isValid(planId) && planId.constructor.name === 'ObjectId') {
        objectId = planId;
      } else {
        // 如果是其他類型，嘗試轉換為字符串再轉為ObjectId
        objectId = new ObjectId(planId.toString());
      }

      const result = await plansCollection.findOneAndUpdate(
        { _id: objectId },
        { $set: { status, updatedAt: new Date() } },
        { returnDocument: 'after' }
      );

      // 廣播計畫狀態更新
      if (result && (result.value || result)) {
        const planData = result.value || result;

        const websocketService = require('./websocketService');
        websocketService.broadcastRefreshPlanUpdate(planData.storeId, {
          planId: planId.toString(),
          planData: {
            // 只廣播狀態相關的欄位，不包含完整計畫數據
            _id: planId.toString(),
            name: planData.name,
            status: status,
            enabled: planData.enabled,
            lastRun: planData.lastRun,
            statistics: planData.statistics,
            // 不包含 nextRun，避免錯誤覆蓋
            updatedFields: ['status', 'updatedAt']
          }
        }, 'status_change');
        console.log(`計畫狀態更新廣播已發送`);
      } else {
        console.error(`更新計畫狀態失敗: 找不到計畫 ${planId}`);
      }
    } catch (error) {
      console.error('更新計畫狀態失敗:', error);
    }
  }

  // 獲取目標設備
  async getTargetDevices(plan) {
    try {
      const { targetSelection } = plan;
      const { client, db } = await this.getDbConnection();
      const deviceCollection = db.collection('devices');

      let devices = [];

      switch (targetSelection.type) {
        case 'mac_addresses':
          // 指定 MAC 地址
          const query = { 
            storeId: plan.storeId,
            macAddress: { $in: targetSelection.macAddresses }
          };
          devices = await deviceCollection.find(query).toArray();
          break;

        case 'store_data':
          // 指定門店數據 - 只有綁定了模板且數據綁定包含指定項目的設備
          // 由於 dataBindings 是 JSON 字符串，無法直接在 MongoDB 查詢中解析
          // 所以先獲取所有符合基本條件的設備，然後在應用層過濾
          console.log(`開始處理門店數據模式，目標門店數據IDs:`, targetSelection.storeDataIds);

          const baseQuery = {
            storeId: plan.storeId,
            templateId: { $exists: true, $ne: null }  // 必須綁定模板
          };
          const allDevices = await deviceCollection.find(baseQuery).toArray();

          console.log(`門店 ${plan.storeId} 中符合基本條件的設備數量: ${allDevices.length}`);
          if (allDevices.length > 0) {
            console.log('設備樣本:', allDevices.slice(0, 2).map(d => ({
              macAddress: d.macAddress,
              dataId: d.dataId,
              dataBindings: d.dataBindings,
              templateId: d.templateId
            })));
          }

          // 在應用層過濾設備，使用與 storeDataApi.js 相同的邏輯
          devices = allDevices.filter(device => {
            // 檢查是否匹配任何一個目標門店數據ID
            return targetSelection.storeDataIds.some(storeDataId => {
              let isMatched = false;

              // 檢查舊版本的單一dataId綁定
              if (device.dataId === storeDataId) {
                isMatched = true;
                console.log(`設備 ${device.macAddress} 通過 dataId 匹配: ${device.dataId} === ${storeDataId}`);
              }

              // 檢查新版本的dataBindings
              if (!isMatched && device.dataBindings) {
                try {
                  let bindings = device.dataBindings;

                  // 如果是字符串，嘗試解析為JSON
                  if (typeof bindings === 'string') {
                    bindings = JSON.parse(bindings);
                  }

                  // 檢查綁定對象中是否包含該數據ID
                  if (typeof bindings === 'object' && bindings !== null) {
                    for (const [key, value] of Object.entries(bindings)) {
                      if (value === storeDataId) {
                        isMatched = true;
                        console.log(`設備 ${device.macAddress} 通過 dataBindings 匹配: ${key} -> ${value}`);
                        break;
                      }
                    }
                  }
                } catch (error) {
                  console.log(`解析設備 ${device.macAddress} 的 dataBindings 失敗:`, error.message);
                  // 如果解析失敗，嘗試字符串匹配
                  if (typeof device.dataBindings === 'string' && device.dataBindings.includes(storeDataId)) {
                    isMatched = true;
                    console.log(`設備 ${device.macAddress} 通過字符串匹配`);
                  }
                }
              }

              return isMatched;
            });
          });

          console.log(`門店 ${plan.storeId} 中匹配門店數據的設備數量: ${devices.length}`);
          break;

        default:
          throw new Error(`不支援的對象選擇類型: ${targetSelection.type}`);
      }

      return devices;
    } catch (error) {
      console.error('獲取目標設備失敗:', error);
      throw error;
    }
  }

  // 執行批量刷圖
  async executeBatchRefresh(plan, devices) {
    try {
      const deviceIds = devices.map(device => device._id.toString());
      const { execution } = plan;

      // 從系統設定讀取傳送參數
      const systemConfig = await this.getSystemConfig();
      
      console.log(`使用系統配置進行批量刷圖:`, {
        concurrency: systemConfig.gatewayConcurrency,
        retryAttempts: systemConfig.queueRetryAttempts,
        timeout: systemConfig.gatewayTimeout
      });
      
      // 使用現有的批量發送服務，完全沿用設備管理的邏輯
      // 智能網關選擇取決於裝置本身的設定，不再從計畫配置中讀取
      const result = await this.sendPreviewService.sendMultipleDevicePreviewsToGateways(
        deviceIds,
        {
          sendToAllGateways: false,  // 固定為 false，使用智能選擇
          storeId: plan.storeId,
          concurrency: systemConfig.gatewayConcurrency || 20,        // 從系統設定讀取
          enableSmartSelection: true, // 固定啟用，具體行為取決於裝置設定
          // 以下參數都從系統設定讀取，確保與設備管理批量傳送一致
          timeout: systemConfig.gatewayTimeout || 30000,
          retryAttempts: systemConfig.queueRetryAttempts || 3,
          retryInterval: systemConfig.queueRetryInterval || 5000
        }
      );

      return result;
    } catch (error) {
      console.error('執行批量刷圖失敗:', error);
      throw error;
    }
  }

  // 增強執行結果，添加詳細統計信息
  async enhanceExecutionResult(originalResult, devices, startTime) {
    try {
      const endTime = new Date();
      const processingTime = endTime.getTime() - startTime.getTime();

      // 統計網關使用情況
      const gatewayStats = {};
      const deviceDetails = [];

      // 處理設備詳細結果
      if (originalResult.detailResults && Array.isArray(originalResult.detailResults)) {
        for (const deviceResult of originalResult.detailResults) {
          const device = devices.find(d => d._id.toString() === deviceResult.deviceId);

          // 從批量傳送結果中提取網關信息
          let gatewayId = null;
          let gatewayName = null;

          if (deviceResult.actualUsedGateway) {
            gatewayId = deviceResult.actualUsedGateway.gatewayId;
            gatewayName = deviceResult.actualUsedGateway.gatewayName;
          } else if (deviceResult.primaryGateway) {
            gatewayId = deviceResult.primaryGateway.gatewayId;
            gatewayName = deviceResult.primaryGateway.gatewayName;
          } else if (deviceResult.gatewayId) {
            gatewayId = deviceResult.gatewayId;
            gatewayName = deviceResult.gatewayName;
          }

          // 統計網關使用
          if (gatewayId) {
            if (!gatewayStats[gatewayId]) {
              gatewayStats[gatewayId] = {
                deviceCount: 0,
                successCount: 0,
                failedCount: 0,
                gatewayName: gatewayName || gatewayId
              };
            }

            gatewayStats[gatewayId].deviceCount++;
            if (deviceResult.success) {
              gatewayStats[gatewayId].successCount++;
            } else {
              gatewayStats[gatewayId].failedCount++;
            }
          }

          // 設備詳細信息
          deviceDetails.push({
            deviceId: deviceResult.deviceId,
            deviceName: device ? (device.code || device.name || '未知設備') : '未知設備',
            macAddress: device ? device.macAddress : '未知',
            success: deviceResult.success,
            error: deviceResult.error,
            gatewayId: gatewayId,
            gatewayName: gatewayName || gatewayId || '-',
            processingTime: deviceResult.processingTime || 0,
            timestamp: deviceResult.timestamp || new Date()
          });
        }
      }

      // 計算統計摘要
      const totalGatewaysUsed = Object.keys(gatewayStats).length;

      // 確保統計數據是數字類型，而不是字符串
      let totalDevices = 0;
      let successDevices = 0;
      let failedDevices = 0;

      // 從設備詳細結果中重新計算統計數據
      if (deviceDetails.length > 0) {
        totalDevices = deviceDetails.length;
        successDevices = deviceDetails.filter(d => d.success).length;
        failedDevices = deviceDetails.filter(d => !d.success).length;
      } else {
        // 如果沒有設備詳細結果，嘗試從原始結果中解析
        totalDevices = Number(originalResult.totalDevices) || devices.length;

        // 處理可能是字符串的 successDevices
        if (typeof originalResult.successDevices === 'string') {
          // 如果是逗號分隔的設備ID字符串，計算數量
          successDevices = originalResult.successDevices ? originalResult.successDevices.split(',').length : 0;
        } else {
          successDevices = Number(originalResult.successDevices) || 0;
        }

        // 處理可能是字符串的 failedDevices
        if (typeof originalResult.failedDevices === 'string') {
          // 如果是逗號分隔的字符串，計算數量
          failedDevices = originalResult.failedDevices ? originalResult.failedDevices.split(',').length : 0;
        } else {
          failedDevices = Number(originalResult.failedDevices) || 0;
        }

        // 確保總數一致
        if (successDevices + failedDevices !== totalDevices && totalDevices > 0) {
          failedDevices = totalDevices - successDevices;
        }
      }

      const successRate = totalDevices > 0 ? (successDevices / totalDevices * 100) : 0;
      const averageProcessingTimePerDevice = totalDevices > 0 ? (processingTime / totalDevices) : 0;

      console.log('執行結果統計計算:', {
        totalDevices,
        successDevices,
        failedDevices,
        successRate: successRate.toFixed(2) + '%',
        deviceDetailsCount: deviceDetails.length
      });

      return {
        ...originalResult,
        // 確保基本統計數據正確，使用重新計算的數字
        totalDevices: totalDevices,
        successDevices: successDevices,
        failedDevices: failedDevices,
        skippedDevices: Number(originalResult.skippedDevices) || 0,
        processingTime,
        gatewayStats,
        deviceDetails,
        summary: {
          totalGatewaysUsed,
          averageProcessingTimePerDevice: Math.round(averageProcessingTimePerDevice),
          successRate: Math.round(successRate * 100) / 100
        }
      };
    } catch (error) {
      console.error('增強執行結果失敗:', error);
      return originalResult;
    }
  }

  // 獲取系統配置
  async getSystemConfig() {
    try {
      const { client, db } = await this.getDbConnection();
      const configCollection = db.collection('sysConfigs');

      const systemConfigDoc = await configCollection.findOne({ key: 'system' });
      const systemConfig = systemConfigDoc ? systemConfigDoc.value : {};

      // 設置預設值
      return {
        gatewayConcurrency: systemConfig.gatewayConcurrency || 20,
        queueRetryAttempts: systemConfig.retryCount || 3,
        queueRetryInterval: systemConfig.retryInterval || 5000,
        gatewayTimeout: systemConfig.timeout || 30000
      };
    } catch (error) {
      console.error('獲取系統配置失敗:', error);
      // 返回預設配置
      return {
        gatewayConcurrency: 20,
        queueRetryAttempts: 3,
        queueRetryInterval: 5000,
        gatewayTimeout: 30000
      };
    }
  }
}

// 創建單例實例
const executionEngine = new ExecutionEngine();

module.exports = executionEngine;
