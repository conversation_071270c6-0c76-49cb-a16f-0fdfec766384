# 智能網關選擇機制完整文檔

## 概述

智能網關選擇機制允許裝置在主要網關不可用時自動選擇備用網關，提高批量傳輸的成功率和系統可靠性。本文檔與 `server/services/sendPreviewToGateway.js` 實際程式碼完全同步。

## 核心配置

### 裝置配置結構
```javascript
{
  _id: ObjectId("deviceId"),
  gatewaySelectionMode: "auto",           // 智能模式標識
  primaryGatewayId: ObjectId("gateway1"), // 主要網關ID
  otherGateways: [                        // 備用網關ID陣列
    ObjectId("gateway2"), 
    ObjectId("gateway3")
  ],
  status: "online" | "offline",           // 裝置狀態
  macAddress: "AA:BB:CC:DD:EE:FF"        // 裝置MAC地址
}
```

### 系統配置參數
- **maxQueueCycles**: 最大隊列循環次數 (預設: 100)
- **maxWaitCycles**: 最大等待循環次數 (預設: 10)  
- **concurrency**: 並發處理任務數 (預設: 3)
- **maxRetries**: 單個任務最大重試次數 (預設: 3)

## 雙階段處理架構

### 階段1: checkTaskCanProcess (行 1175-1200)
**目的**: 快速判斷任務是否可以立即處理，避免無效的任務取出

**智能模式邏輯**:
```javascript
if (device.gatewaySelectionMode === 'auto') {
  // 1. 檢查主要網關是否可用 (在線且空閒)
  const isPrimaryAvailable = websocketService.isGatewayOnline(primaryGatewayId) &&
                            !websocketService.isGatewayBusyWithChunk(primaryGatewayId);
  
  if (isPrimaryAvailable) {
    return true; // 主要網關可用，可以立即處理
  }
  
  // 2. 檢查是否有可用的備用網關
  if (Array.isArray(device.otherGateways) && device.otherGateways.length > 0) {
    const allGatewayIds = [primaryGatewayId, ...device.otherGateways];
    const availableGateways = websocketService.getAvailableGateways(allGatewayIds);
    const backupGateway = availableGateways.find(gw => gw !== primaryGatewayId);
    
    return !!backupGateway; // 有備用網關可用則返回true
  }
  
  return false; // 沒有可用網關
}
```

### 階段2: processTask (行 1517-1581)
**目的**: 執行詳細的網關選擇和任務處理

## 智能網關選擇詳細流程

### 步驟1: 裝置狀態檢查 (行 1485-1494)
```javascript
// 檢查設備是否離線 - 如果離線則直接失敗，不重試
if (device.status === 'offline') {
  return {
    success: false,
    shouldRetry: false,
    error: '設備離線'
  };
}
```

### 步驟2: 主要網關檢查 (行 1520-1528)
```javascript
const isPrimaryBusy = websocketService.isGatewayBusyWithChunk(primaryGatewayId);
const isPrimaryOnline = websocketService.isGatewayOnline(primaryGatewayId);

if (!isPrimaryBusy && isPrimaryOnline) {
  // 主要網關空閒且在線，可以直接使用
  canProcessNow = true;
  selectedGatewayId = primaryGatewayId;
  console.log(`✅ 主要網關 ${primaryGatewayId} 空閒，直接使用`);
}
```

### 步驟3: 備用網關搜尋 (行 1532-1565)
```javascript
else {
  console.log(`⚠️ 主要網關 ${primaryGatewayId} ${isPrimaryBusy ? '忙碌' : '離線'}，尋找備用網關`);
  
  if (Array.isArray(device.otherGateways) && device.otherGateways.length > 0) {
    // 3.1 構建所有網關ID列表
    const allGatewayIds = [primaryGatewayId];
    const otherGatewayIds = device.otherGateways.map(gw => {
      if (typeof gw === 'string') return gw;
      if (gw instanceof ObjectId) return gw.toString();
      return String(gw);
    });
    allGatewayIds.push(...otherGatewayIds);
    
    // 3.2 獲取可用的備用網關
    const availableGateways = websocketService.getAvailableGateways(allGatewayIds);
    const backupGateway = availableGateways.find(gw => gw !== primaryGatewayId);
    
    if (backupGateway) {
      // 3.3 找到可用備用網關
      selectedGatewayId = backupGateway;
      useBackupGateway = true;
      canProcessNow = true;
      console.log(`✅ 找到備用網關 ${backupGateway}，使用備用網關`);
    } else {
      // 3.4 沒有可用備用網關，檢查失敗條件
      const allGatewaysOffline = allGatewayIds.every(gwId => !websocketService.isGatewayOnline(gwId));
      if (allGatewaysOffline) {
        // 所有網關都離線 - 直接失敗
        return {
          success: false,
          shouldRetry: false,
          error: '所有網關都離線'
        };
      } else {
        // 有網關在線但都忙碌 - 重新排隊
        canProcessNow = false;
      }
    }
  }
}
```

### 步驟4: 無備用網關情況處理 (行 1566-1580)
```javascript
else {
  // 智能模式但沒有備用網關，檢查主要網關是否離線
  if (!isPrimaryOnline) {
    console.log(`❌ 智能模式設備 ${task.deviceId} 主要網關離線且無備用網關，直接標記為失敗`);
    return {
      success: false,
      shouldRetry: false,
      error: '主要網關離線且無備用網關'
    };
  } else {
    // 主要網關在線但忙碌，且沒有備用網關 - 重新排隊
    console.log(`❌ 設備沒有配置備用網關，任務需要重新排隊`);
    canProcessNow = false;
  }
}
```

## 決策矩陣

| 主要網關狀態 | 備用網關配置 | 備用網關狀態 | 處理結果 | shouldRetry | 錯誤訊息 |
|-------------|-------------|-------------|----------|-------------|----------|
| 在線+空閒 | 任意 | 任意 | 使用主要網關 | N/A | N/A |
| 在線+忙碌 | 無 | N/A | 重新排隊 | true | 網關忙碌，任務重新排隊 |
| 在線+忙碌 | 有 | 有可用 | 使用備用網關 | N/A | N/A |
| 在線+忙碌 | 有 | 全忙碌 | 重新排隊 | true | 網關忙碌，任務重新排隊 |
| 離線 | 無 | N/A | 直接失敗 | false | 主要網關離線且無備用網關 |
| 離線 | 有 | 有可用 | 使用備用網關 | N/A | N/A |
| 離線 | 有 | 全離線 | 直接失敗 | false | 所有網關都離線 |
| 離線 | 有 | 全忙碌 | 重新排隊 | true | 網關忙碌，任務重新排隊 |

## 預防性標記機制 (行 1615-1631)

### 目的
防止併發任務選擇同一個網關造成衝突

### 實現機制
```javascript
// 1. 生成唯一的預防性標記ID
const preTaskChunkId = `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

// 2. 標記網關為忙碌
console.log(`🔒 任務隊列預先標記網關 ${selectedGatewayId} 為忙碌，防止併發`);
websocketService.startChunkTransmission(selectedGatewayId, preTaskChunkId, device.macAddress);

try {
  // 3. 執行實際傳輸
  const result = await sendDevicePreviewToGateway(task.deviceId, {
    forceGatewayId: selectedGatewayId
  });
} finally {
  // 4. 清理預防性標記
  console.log(`🔓 任務隊列清理網關 ${selectedGatewayId} 的預防性標記`);
  websocketService.endChunkTransmission(selectedGatewayId, preTaskChunkId);
}
```

### 異常處理清理 (行 1668-1672)
```javascript
// 如果有預防性標記，也要清理
if (typeof selectedGatewayId !== 'undefined' && typeof preTaskChunkId !== 'undefined') {
  console.log(`🔓 異常處理，清理網關 ${selectedGatewayId} 的預防性標記`);
  websocketService.endChunkTransmission(selectedGatewayId, preTaskChunkId);
}
```

## 錯誤處理和重試邏輯 (行 1674-1687)

### 不可重試的錯誤條件
```javascript
const shouldRetry = (
  error.message.includes('網關忙碌') ||
  error.message.includes('chunk傳輸') ||
  error.message.includes('timeout') ||
  error.message.includes('連接') ||
  error.message.includes('傳輸失敗')
) && !(
  // 以下情況不應重試
  error.message.includes('設備離線') ||
  error.message.includes('主要網關離線') ||
  error.message.includes('所有網關都離線') ||
  error.message.includes('主要網關離線且無備用網關')
);
```

### 具體錯誤類型
1. **裝置離線**: `device.status === 'offline'` → shouldRetry: false
2. **所有網關都離線**: 智能模式下所有網關都不在線 → shouldRetry: false  
3. **主要網關離線且無備用網關**: 智能模式下只有主要網關但離線 → shouldRetry: false
4. **網關忙碌**: 所有可用網關都在處理其他任務 → shouldRetry: true
5. **傳輸失敗**: 網路問題、超時等 → shouldRetry: true

## 統計信息收集

### smartSelectionStats 結構
```javascript
{
  totalAutoModeDevices: 0,      // 智能模式裝置總數
  usedBackupGateway: 0,         // 使用備用網關次數  
  primaryGatewayBusy: 0         // 主要網關忙碌次數
}
```

### 收集邏輯 (行 1270-1280)
```javascript
if (result.result.smartGatewaySelection) {
  if (result.result.smartGatewaySelection.enabled) {
    smartSelectionStats.totalAutoModeDevices++;
  }
  if (result.result.smartGatewaySelection.primaryGatewayBusy) {
    smartSelectionStats.primaryGatewayBusy++;
  }
  if (result.result.smartGatewaySelection.usedBackupGateway) {
    smartSelectionStats.usedBackupGateway++;
  }
}
```

## 關鍵日誌和調試信息

### 智能模式啟動
```
🤖 設備 ${deviceId} 智能模式 - 檢查主要網關 ${primaryGatewayId} 狀態
```

### 主要網關狀態
```
✅ 主要網關 ${primaryGatewayId} 空閒，直接使用
⚠️ 主要網關 ${primaryGatewayId} 忙碌，尋找備用網關
⚠️ 主要網關 ${primaryGatewayId} 離線，尋找備用網關
```

### 備用網關選擇
```
✅ 找到備用網關 ${backupGateway}，使用備用網關
❌ 沒有可用的備用網關，任務需要重新排隊
❌ 設備沒有配置備用網關，任務需要重新排隊
```

### 失敗條件
```
❌ 智能模式設備 ${deviceId} 所有網關都離線，直接標記為失敗
❌ 智能模式設備 ${deviceId} 主要網關離線且無備用網關，直接標記為失敗
```

### 預防性標記
```
🔒 任務隊列預先標記網關 ${selectedGatewayId} 為忙碌，防止併發
🔓 任務隊列清理網關 ${selectedGatewayId} 的預防性標記
🔓 發送失敗，清理網關 ${selectedGatewayId} 的預防性標記
🔓 異常處理，清理網關 ${selectedGatewayId} 的預防性標記
```

## 性能優化特性

### 1. 快速預檢機制
- `checkTaskCanProcess` 函數避免無效任務的取出和處理
- 減少不必要的資料庫查詢和網關狀態檢查

### 2. 併發控制
- 預防性標記機制防止網關過載
- 動態併發數量控制

### 3. 智能等待機制
- `waitForAnyGatewayAvailable` 等待網關變為可用
- 避免無效的輪詢和資源浪費

### 4. 統計信息收集
- 實時收集智能選擇統計信息
- 支援性能分析和優化決策

## 限制和約束

### 1. 網關數量限制
- 主要網關: 必須配置，只能有1個
- 備用網關: 可選配置，支援多個

### 2. 網關狀態依賴
- 依賴 WebSocket 連接狀態判斷網關在線狀態
- 依賴 chunk 傳輸狀態判斷網關忙碌狀態

### 3. 重試限制
- 單個任務最大重試次數限制
- 隊列循環次數限制
- 等待循環次數限制

### 4. 併發限制
- 系統級併發數量限制
- 網關級併發能力限制

## 故障排除

### 常見問題
1. **智能模式不生效**: 檢查 `gatewaySelectionMode` 是否設為 'auto'
2. **備用網關不被使用**: 檢查 `otherGateways` 配置和網關在線狀態
3. **任務一直重新排隊**: 檢查所有網關是否都忙碌或離線
4. **預防性標記未清理**: 檢查異常處理邏輯和 finally 區塊

### 調試步驟
1. 檢查裝置配置結構
2. 驗證網關在線狀態
3. 檢查網關忙碌狀態
4. 查看詳細日誌輸出
5. 檢查統計信息收集

## 與非智能模式的對比

### 非智能模式 (gatewaySelectionMode !== 'auto')
```javascript
// 非智能模式邏輯 (行 1582-1603)
const isPrimaryOnline = websocketService.isGatewayOnline(primaryGatewayId);
const isPrimaryBusy = websocketService.isGatewayBusyWithChunk(primaryGatewayId);

if (isPrimaryOnline && !isPrimaryBusy) {
  canProcessNow = true;
  console.log(`✅ 設備 ${task.deviceId} 非智能模式 - 主要網關可用`);
} else if (!isPrimaryOnline) {
  // 非智能模式：主要網關離線直接失敗
  console.log(`❌ 非智能模式設備 ${task.deviceId} 主要網關離線，直接標記為失敗`);
  return {
    success: false,
    shouldRetry: false,
    error: '主要網關離線'
  };
} else {
  console.log(`❌ 設備 ${task.deviceId} 非智能模式 - 主要網關忙碌，任務需要重新排隊`);
  canProcessNow = false;
}
```

### 模式對比表

| 特性 | 智能模式 | 非智能模式 |
|------|----------|------------|
| 備用網關支援 | ✅ 支援 | ❌ 不支援 |
| 主要網關離線處理 | 嘗試備用網關或失敗 | 直接失敗 |
| 主要網關忙碌處理 | 嘗試備用網關或重試 | 重試 |
| 配置複雜度 | 高 (需配置備用網關) | 低 (只需主要網關) |
| 可靠性 | 高 | 中等 |
| 性能開銷 | 中等 (需檢查多個網關) | 低 |

## 隊列處理機制整合

### 任務隊列循環 (行 1229-1465)
智能模式與任務隊列處理機制緊密整合：

1. **任務預檢**: `checkTaskCanProcess` 快速判斷任務可處理性
2. **任務處理**: `processTask` 執行詳細的智能選擇邏輯
3. **結果處理**: 根據 `shouldRetry` 決定重新排隊或標記失敗
4. **統計收集**: 收集智能選擇相關統計信息

### 等待機制 (行 1395-1415)
```javascript
// 收集所有任務涉及的網關ID
const allGatewayIds = new Set();
for (const task of taskQueue) {
  const device = await deviceCollection.findOne({ _id: safeObjectId(task.deviceId) });
  if (device) {
    const primaryGatewayId = device.primaryGatewayId?.toString();
    if (primaryGatewayId) {
      allGatewayIds.add(primaryGatewayId);

      // 如果是智能模式，也添加備用網關
      if (device.gatewaySelectionMode === 'auto' && Array.isArray(device.otherGateways)) {
        device.otherGateways.forEach(gw => {
          const gwId = typeof gw === 'string' ? gw : gw.toString();
          allGatewayIds.add(gwId);
        });
      }
    }
  }
}

// 等待任一網關變為可用
await websocketService.waitForAnyGatewayAvailable(Array.from(allGatewayIds), 10000);
```

## WebSocket 服務整合

### 必需的 WebSocket 服務方法
```javascript
// 網關狀態檢查
websocketService.isGatewayOnline(gatewayId)
websocketService.isGatewayBusyWithChunk(gatewayId)

// 可用網關獲取
websocketService.getAvailableGateways(gatewayIds)

// 預防性標記管理
websocketService.startChunkTransmission(gatewayId, chunkId, macAddress)
websocketService.endChunkTransmission(gatewayId, chunkId)

// 等待機制
websocketService.waitForAnyGatewayAvailable(gatewayIds, timeout)

// 進度廣播
websocketService.broadcastBatchProgress(batchId, progressData)
websocketService.broadcastBatchComplete(batchId, resultData)
```

### 網關狀態管理
- **在線狀態**: 基於 WebSocket 連接狀態
- **忙碌狀態**: 基於 chunk 傳輸狀態追蹤
- **可用狀態**: 在線且不忙碌的網關

## 配置最佳實踐

### 1. 網關配置建議
```javascript
// 推薦配置
{
  gatewaySelectionMode: "auto",
  primaryGatewayId: "gateway_main",
  otherGateways: ["gateway_backup1", "gateway_backup2"]
}

// 最小配置
{
  gatewaySelectionMode: "auto",
  primaryGatewayId: "gateway_main",
  otherGateways: ["gateway_backup1"]
}
```

### 2. 系統參數調優
```javascript
// 高可靠性配置
{
  maxQueueCycles: 200,    // 增加循環次數
  maxWaitCycles: 20,      // 增加等待次數
  concurrency: 2,         // 降低併發避免衝突
  maxRetries: 5           // 增加重試次數
}

// 高性能配置
{
  maxQueueCycles: 50,     // 減少循環次數
  maxWaitCycles: 5,       // 減少等待次數
  concurrency: 5,         // 提高併發
  maxRetries: 2           // 減少重試次數
}
```

### 3. 網關部署建議
- **地理分散**: 備用網關部署在不同位置
- **負載均衡**: 合理分配裝置到不同主要網關
- **容量規劃**: 確保備用網關有足夠處理能力
- **監控告警**: 實時監控網關狀態和性能

## 版本兼容性

### 向後兼容
- 非智能模式裝置不受影響
- 未配置 `otherGateways` 的智能模式裝置降級為單網關模式
- 舊版本網關仍可正常工作

### 升級路徑
1. 部署新版本服務端代碼
2. 配置備用網關
3. 逐步將裝置切換到智能模式
4. 監控和調優系統參數

## 監控和指標

### 關鍵指標
- **智能模式使用率**: `totalAutoModeDevices / totalDevices`
- **備用網關使用率**: `usedBackupGateway / totalAutoModeDevices`
- **主要網關忙碌率**: `primaryGatewayBusy / totalAutoModeDevices`
- **任務成功率**: `successCount / totalCount`
- **平均處理時間**: `avgProcessingTime`

### 告警條件
- 所有網關離線率 > 10%
- 主要網關忙碌率 > 80%
- 任務失敗率 > 5%
- 平均處理時間 > 30秒
- 隊列循環次數接近上限

---

**文檔版本**: 1.0
**最後更新**: 2024年
**對應代碼版本**: server/services/sendPreviewToGateway.js
**維護者**: EPD Manager 開發團隊
