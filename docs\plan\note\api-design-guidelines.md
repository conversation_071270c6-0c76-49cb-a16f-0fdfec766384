# API設計規範指南

## 概述

本文檔定義了EPD Manager系統中API設計的標準規範，確保所有API都能正確整合認證、權限控制和數據庫連接。

## 🔐 認證與權限控制

### 後端API設計規範

#### 1. 中間件使用順序
```javascript
// 正確的中間件順序
router.get('/api/resource', 
  authenticate,                    // 1. 認證中間件
  checkPermission('resource:read'), // 2. 權限檢查中間件
  async (req, res) => {
    // API邏輯
  }
);
```

#### 2. 數據庫連接獲取
```javascript
// ✅ 正確：使用req.db（來自中間件）
const { user, db } = req;

// ❌ 錯誤：直接調用connectDBFunction()
const { db } = await connectDBFunction();
```

#### 3. 用戶信息獲取
```javascript
// ✅ 正確：從req.user獲取當前用戶
const userId = req.user._id;
const username = req.user.username;

// ❌ 錯誤：從token或其他地方獲取
```

#### 4. 權限定義規範
```javascript
// 資源權限命名規範：resource:action
'software:read'    // 讀取軟體
'software:create'  // 創建軟體
'software:update'  // 更新軟體
'software:delete'  // 刪除軟體

// 多權限檢查
checkPermission(['store:view', 'device:view'])  // 任一權限
checkPermission('admin:all', { requireAll: true }) // 所有權限
```

### 前端API設計規範

#### 1. 認證Headers設置
```typescript
// ✅ 正確：包含完整認證信息
import { useAuthStore } from '../../store/authStore';
import { buildEndpointUrl } from './apiConfig';

export async function apiFunction() {
  const { token } = useAuthStore.getState();
  
  const response = await fetch(buildEndpointUrl('resource'), {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
    credentials: 'include', // 包含 cookie
  });
}
```

#### 2. 錯誤處理標準
```typescript
// ✅ 正確：統一錯誤處理
if (!response.ok) {
  if (response.status === 401) {
    throw new Error('未登入或登入已過期');
  }
  const error = await response.json();
  throw new Error(error.error?.message || '操作失敗');
}
```

#### 3. 日誌記錄規範
```typescript
// ✅ 正確：包含錯誤日誌
try {
  // API調用
} catch (error) {
  console.error('API操作失敗:', error);
  throw error;
}
```

## 🗄️ 數據庫操作規範

### 1. 連接獲取方式
```javascript
// 後端路由中
router.get('/api/resource', authenticate, async (req, res) => {
  const { db } = req; // 從中間件獲取
  // 使用db進行操作
});

// 服務類中
class ResourceService {
  static async findAll(db, filters) {
    // 接收db參數
  }
}
```

### 2. 事務處理
```javascript
// 需要事務的操作
const session = client.startSession();
try {
  await session.withTransaction(async () => {
    // 事務操作
  });
} finally {
  await session.endSession();
}
```

## 📁 文件上傳規範

### 1. Multer配置
```javascript
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
  },
  fileFilter: (req, file, cb) => {
    // 文件類型驗證
  }
});
```

### 2. 文件名編碼處理
```javascript
const fixFileNameEncoding = (req, res, next) => {
  if (req.file) {
    req.file.originalname = Buffer.from(req.file.originalname, "latin1").toString("utf-8");
  }
  next();
};
```

## 🔄 API響應格式

### 1. 成功響應
```javascript
// 列表響應
res.json({
  success: true,
  data: {
    items: [...],
    total: 100,
    page: 1,
    limit: 20,
    totalPages: 5
  }
});

// 單項響應
res.json({
  success: true,
  data: item
});
```

### 2. 錯誤響應
```javascript
res.status(400).json({
  success: false,
  error: {
    code: 'VALIDATION_ERROR',
    message: '驗證失敗',
    details: error.message
  }
});
```

## 🚀 API路由組織

### 1. 路由文件結構
```
server/routes/
├── resourceApi.js     # 資源管理API
├── authApi.js         # 認證API
└── ...
```

### 2. 路由導出規範
```javascript
// 路由文件末尾
module.exports = { router, initDB };
```

### 3. 主文件註冊
```javascript
// server/index.js
const { router: resourceApiRouter, initDB: initResourceApi } = require('./routes/resourceApi');

// 初始化數據庫連接
initResourceApi(connectDB);

// 註冊路由
app.use('/api', resourceApiRouter);
```

## ⚠️ 常見錯誤避免

### 1. 數據庫連接錯誤
```javascript
// ❌ 錯誤：混用連接方式
const { db } = await connectDBFunction(); // 在有中間件的路由中
const { db } = req; // 應該統一使用這種方式
```

### 2. 認證遺漏
```javascript
// ❌ 錯誤：忘記添加認證中間件
router.post('/api/sensitive-operation', async (req, res) => {
  // 敏感操作但沒有認證
});

// ✅ 正確：添加認證和權限檢查
router.post('/api/sensitive-operation', 
  authenticate, 
  checkPermission('operation:create'),
  async (req, res) => {
    // 安全的操作
  }
);
```

### 3. 前端認證遺漏
```typescript
// ❌ 錯誤：沒有包含認證headers
const response = await fetch('/api/resource');

// ✅ 正確：包含完整認證信息
const { token } = useAuthStore.getState();
const response = await fetch('/api/resource', {
  headers: {
    'Content-Type': 'application/json',
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
  },
  credentials: 'include',
});
```

## 📋 檢查清單

新增API時請確認以下項目：

### 後端檢查
- [ ] 使用`authenticate`中間件
- [ ] 使用`checkPermission`中間件（如需要）
- [ ] 從`req.db`獲取數據庫連接
- [ ] 從`req.user`獲取用戶信息
- [ ] 統一的錯誤響應格式
- [ ] 適當的HTTP狀態碼

### 前端檢查
- [ ] 導入`useAuthStore`和`buildEndpointUrl`
- [ ] 包含`Authorization`header
- [ ] 設置`credentials: 'include'`
- [ ] 處理401認證錯誤
- [ ] 添加錯誤日誌記錄
- [ ] 統一的錯誤處理

### 安全檢查
- [ ] 敏感操作需要權限驗證
- [ ] 文件上傳有大小和類型限制
- [ ] 輸入數據驗證
- [ ] SQL注入防護（使用參數化查詢）

## 📚 參考示例

完整的API實現示例請參考：
- `server/routes/softwareApi.js` - 後端實現
- `src/utils/api/softwareApi.ts` - 前端實現
- `server/routes/storeApi.js` - 權限控制示例

---

**注意**：本規範基於EPD Manager系統的實際需求制定，所有新增API都應嚴格遵循這些規範以確保系統的安全性和一致性。
