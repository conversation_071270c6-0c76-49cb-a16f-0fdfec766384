# EPD Manager App 實現指南

本文檔提供了按照規劃完成 EPD Manager App 開發的詳細指南，包括環境設置、開發步驟和測試方法。

## 1. 專案初始化

### 1.1 創建 React Native 專案

```bash
# 使用 Expo 創建專案
npx create-expo-app epd-manager-app
cd epd-manager-app

# 安裝必要的依賴
npm install @react-native-async-storage/async-storage @react-navigation/bottom-tabs @react-navigation/native @react-navigation/native-stack @reduxjs/toolkit axios expo-status-bar react-native-elements react-native-gesture-handler react-native-reanimated react-native-safe-area-context react-native-screens react-native-vector-icons react-redux redux redux-persist

# 安裝網關掃描相關依賴
npm install react-native-udp react-native-network-info react-native-permissions
```

### 1.2 創建目錄結構

按照 `docs/project-structure.md` 中定義的目錄結構創建必要的目錄：

```bash
mkdir -p src/{api,components,navigation,screens,services,store,utils,constants,hooks,localization,theme}
mkdir -p src/components/{common,auth,store,gateway,device,websocket}
mkdir -p src/screens/{auth,store,gateway,device,console,settings}
mkdir -p assets/{images,fonts,animations}
```

## 2. 核心功能實現

### 2.1 認證功能

1. 實現 `src/api/auth.js` 中的登入/登出 API
2. 創建 `src/store/slices/authSlice.js` 管理認證狀態
3. 實現 `src/screens/auth/LoginScreen.js` 登入頁面
4. 創建 `src/navigation/AuthNavigator.js` 處理認證相關導航

### 2.2 門店管理

1. 實現 `src/api/store.js` 中的門店相關 API
2. 創建 `src/store/slices/storeSlice.js` 管理門店狀態
3. 實現 `src/screens/store/StoreListScreen.js` 門店列表頁面
4. 實現 `src/screens/store/StoreDetailScreen.js` 門店詳情頁面

### 2.3 網關管理

1. 實現 `src/api/gateway.js` 中的網關相關 API
2. 創建 `src/store/slices/gatewaySlice.js` 管理網關狀態
3. 實現 `src/screens/gateway/GatewayListScreen.js` 網關列表頁面
4. 實現 `src/screens/gateway/GatewayDetailScreen.js` 網關詳情頁面
5. 實現 `src/screens/gateway/GatewayScanScreen.js` 網關掃描頁面
6. 實現 `src/screens/gateway/AddGatewayScreen.js` 添加網關頁面

### 2.4 網關掃描功能

1. 創建 `src/services/gatewayScanner.js` 實現 UDP 掃描功能
2. 實現 `src/hooks/useGatewayScanner.js` 封裝掃描邏輯
3. 在 `src/screens/gateway/GatewayScanScreen.js` 中使用掃描功能
4. 實現 `src/api/gateway.js` 中的網關註冊 API

### 2.5 設備管理

1. 實現 `src/api/device.js` 中的設備相關 API
2. 創建 `src/store/slices/deviceSlice.js` 管理設備狀態
3. 實現 `src/screens/device/DeviceListScreen.js` 設備列表頁面
4. 實現 `src/screens/device/DeviceDetailScreen.js` 設備詳情頁面
5. 實現 `src/screens/device/AddDeviceScreen.js` 添加設備頁面

### 2.6 網關配置功能

1. 創建 `src/services/gatewayConfigService.js` 實現網關配置功能
2. 實現 `src/hooks/useGatewayConfig.js` 封裝配置邏輯
3. 在 `src/screens/gateway/GatewayConfigScreen.js` 中使用配置功能
4. 實現 HTTP 和 TCP 兩種配置方式

## 3. 用戶界面實現

### 3.1 主題設置

1. 創建 `src/theme/colors.js` 定義顏色方案
2. 創建 `src/theme/typography.js` 定義排版樣式
3. 創建 `src/theme/index.js` 導出主題配置

### 3.2 導航結構

1. 創建 `src/navigation/AppNavigator.js` 作為主導航
2. 創建 `src/navigation/AuthNavigator.js` 處理認證導航
3. 創建 `src/navigation/TabNavigator.js` 處理主標籤導航
4. 創建 `src/navigation/GatewayNavigator.js` 處理網關相關導航
5. 創建 `src/navigation/DeviceNavigator.js` 處理設備相關導航

### 3.3 共用組件

1. 創建 `src/components/common/Header.js` 頁面頭部組件
2. 創建 `src/components/common/LoadingIndicator.js` 加載指示器
3. 創建 `src/components/common/ErrorMessage.js` 錯誤消息組件
4. 創建 `src/components/gateway/GatewayItem.js` 網關列表項組件
5. 創建 `src/components/device/DeviceItem.js` 設備列表項組件

## 4. 狀態管理

### 4.1 Redux 配置

1. 創建 `src/store/index.js` 配置 Redux 存儲
2. 實現 Redux 持久化存儲

### 4.2 Redux 切片

1. 創建 `src/store/slices/authSlice.js` 管理認證狀態
2. 創建 `src/store/slices/storeSlice.js` 管理門店狀態
3. 創建 `src/store/slices/gatewaySlice.js` 管理網關狀態
4. 創建 `src/store/slices/deviceSlice.js` 管理設備狀態
5. 創建 `src/store/slices/websocketSlice.js` 管理 WebSocket 狀態

### 4.3 Redux 動作

1. 創建 `src/store/actions/authActions.js` 處理認證相關動作
2. 創建 `src/store/actions/storeActions.js` 處理門店相關動作
3. 創建 `src/store/actions/gatewayActions.js` 處理網關相關動作
4. 創建 `src/store/actions/deviceActions.js` 處理設備相關動作
5. 創建 `src/store/actions/websocketActions.js` 處理 WebSocket 相關動作

## 5. 網關掃描與配置功能實現

### 5.1 UDP 掃描服務

按照 `docs/gateway-scanning-feature.md` 中的設計，實現 UDP 掃描服務：

```javascript
// src/services/gatewayScanner.js
import { NativeModules, Platform } from 'react-native';
import UdpSocket from 'react-native-udp';
import { NetworkInfo } from 'react-native-network-info';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';

class GatewayScanner {
  constructor() {
    this.socket = null;
    this.discoveredGateways = [];
    this.isScanning = false;
    this.scanTimeout = null;
    this.listeners = [];
  }

  // 檢查並請求網絡權限
  async checkPermissions() {
    // 實現權限檢查邏輯
  }

  // 開始掃描
  async startScan(timeout = 10000) {
    // 實現掃描邏輯
  }

  // 停止掃描
  stopScan() {
    // 實現停止掃描邏輯
  }

  // 處理網關回應
  handleResponse(msg, rinfo) {
    // 實現回應處理邏輯
  }

  // 其他方法...
}

export default new GatewayScanner();
```

### 5.2 網關配置服務

按照 `docs/gateway-configuration-protocol.md` 中的設計，實現網關配置服務：

```javascript
// src/services/gatewayConfigService.js
import axios from 'axios';
import TcpSocket from 'react-native-tcp-socket';

class GatewayConfigService {
  // 使用 HTTP 方法配置網關
  async configureGatewayViaHttp(gatewayIp, config, timeout = 5000) {
    try {
      const response = await axios.post(`http://${gatewayIp}/api/config/websocket`, config, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: timeout
      });

      return response.data;
    } catch (error) {
      console.error('Failed to configure gateway via HTTP:', error);
      throw error;
    }
  }

  // 使用 TCP 方法配置網關
  configureGatewayViaTcp(gatewayIp, gatewayPort, config, timeout = 5000) {
    return new Promise((resolve, reject) => {
      // 實現 TCP 配置邏輯
    });
  }

  // 自動選擇配置方法
  async configureGateway(gateway, config) {
    // 根據網關信息選擇合適的配置方法
  }
}

export default new GatewayConfigService();
```

### 5.3 掃描頁面

按照 `docs/gateway-scanning-code-example.md` 中的示例，實現掃描頁面：

```javascript
// src/screens/gateway/GatewayScanScreen.js
import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { Button, Card, Icon } from 'react-native-elements';
import GatewayScanner from '../../services/gatewayScanner';
import { COLORS } from '../../theme';

const GatewayScanScreen = () => {
  // 實現掃描頁面邏輯
};

export default GatewayScanScreen;
```

### 5.4 添加網關頁面

按照 `docs/gateway-scanning-code-example.md` 中的示例，實現添加網關頁面：

```javascript
// src/screens/gateway/AddGatewayScreen.js
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useSelector, useDispatch } from 'react-redux';
import { Button, Input, Card, Divider, Icon } from 'react-native-elements';
import { Picker } from '@react-native-picker/picker';
import { registerGateway } from '../../api/gateway';
import { fetchStores } from '../../store/actions/storeActions';
import { addGateway } from '../../store/actions/gatewayActions';
import { COLORS } from '../../theme';

const AddGatewayScreen = () => {
  // 實現添加網關頁面邏輯
};

export default AddGatewayScreen;
```

### 5.5 網關配置頁面

實現網關配置頁面，用於將 WebSocket 連接信息發送給網關：

```javascript
// src/screens/gateway/GatewayConfigScreen.js
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Button, Card, Icon } from 'react-native-elements';
import GatewayConfigService from '../../services/gatewayConfigService';
import { COLORS } from '../../theme';

const GatewayConfigScreen = () => {
  // 實現網關配置頁面邏輯
};

export default GatewayConfigScreen;
```

## 6. 測試

### 6.1 使用測試工具

使用 `test` 目錄下的測試工具進行測試：

```bash
# 進入測試目錄
cd test

# 安裝依賴
npm install

# 啟動網關模擬器
npm run gateway

# 在另一個終端中運行測試腳本
npm run test-scan

# 或者啟動多個網關模擬器
npm run multi-gateway
```

### 6.2 單元測試

為核心功能編寫單元測試：

```bash
# 安裝測試依賴
npm install --save-dev jest @testing-library/react-native @testing-library/jest-native

# 運行測試
npm test
```

### 6.3 端到端測試

使用 Detox 進行端到端測試：

```bash
# 安裝 Detox
npm install --save-dev detox

# 配置 Detox
npx detox init

# 運行測試
npx detox test
```

## 7. 打包與發布

### 7.1 Android 打包

```bash
# 使用 Expo EAS 構建
eas build --platform android
```

### 7.2 iOS 打包

```bash
# 使用 Expo EAS 構建
eas build --platform ios
```

## 8. 開發流程建議

1. **增量開發**：按照功能模塊逐步實現，先完成基礎功能，再添加高級功能
2. **頻繁測試**：每實現一個功能就進行測試，確保功能正常
3. **代碼審查**：定期進行代碼審查，確保代碼質量
4. **版本控制**：使用 Git 進行版本控制，定期提交代碼
5. **文檔更新**：隨著開發進展更新文檔，保持文檔與代碼的一致性

## 9. 參考資源

- [React Native 官方文檔](https://reactnative.dev/docs/getting-started)
- [Expo 文檔](https://docs.expo.dev/)
- [React Navigation 文檔](https://reactnavigation.org/docs/getting-started)
- [Redux Toolkit 文檔](https://redux-toolkit.js.org/introduction/getting-started)
- [React Native Elements 文檔](https://reactnativeelements.com/docs)

## 10. 故障排除

### 10.1 常見問題

- **構建錯誤**：檢查依賴版本兼容性
- **網關掃描失敗**：檢查網絡權限和防火牆設置
- **WebSocket 連接問題**：檢查網關 Token 和連接 URL
- **Redux 狀態更新問題**：檢查 action 和 reducer 邏輯

### 10.2 調試技巧

- 使用 React Native Debugger 進行調試
- 使用 console.log 輸出關鍵信息
- 使用 Redux DevTools 監控狀態變化
- 使用 Wireshark 分析網絡通信
