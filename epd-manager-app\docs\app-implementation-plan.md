# EPD Manager App 實現計劃

## 1. 專案概述

本文檔提供了 EPD Manager App 的開發計劃和設計細節，旨在將現有的 `test-ws-client-interactive.js` 測試腳本功能轉換為一個功能完整、用戶體驗良好的 React Native 移動應用程序。

### 1.1 背景介紹

EPD Manager 是一個電子紙顯示器管理系統，通過雲平台能實現對系統內所有網關、電子紙顯示器的控制。移動 App 是該平台的重要組成部分，使用戶能夠在移動設備上進行網關註冊、設備管理等操作。

### 1.2 目標用戶

- 系統管理員
- 門店管理人員
- 設備維護人員

### 1.3 應用名稱

應用名稱為 "EPD Manager"，在各大應用商店中以此名稱發布。

## 2. 技術架構

### 2.1 開發框架選擇

#### 移動端開發框架
- **React Native**: 選擇 React Native 作為主要開發框架，實現跨平台(iOS 和 Android)開發
- **Expo**: 使用 Expo 工具加速開發流程，簡化配置和部署

#### 後端服務
- 使用現有的 EPD Manager 後端服務
- 通過 RESTful API 和 WebSocket 與後端通信

#### 數據存儲
- **AsyncStorage**: 用於本地數據緩存
- **Redux**: 用於應用狀態管理
- **Redux Persist**: 實現持久化存儲

### 2.2 系統架構圖

```mermaid
graph TB
    subgraph "移動應用架構"
        App["EPD Manager App\nReact Native + Expo"]
        UI["UI 組件\nReact Native Elements"]
        State["狀態管理\nRedux + Redux Persist"]
        Storage["本地存儲\nAsyncStorage"]
        Network["網絡層\nAxios + WebSocket"]
        
        App --> UI
        App --> State
        State --> Storage
        App --> Network
    end
    
    subgraph "後端服務"
        Server["EPD Manager Server\nExpress.js"]
        DB["數據庫\nMongoDB"]
        WSService["WebSocket 服務"]
        
        Server <--> DB
        Server <--> WSService
    end
    
    Network <--> Server
    Network <--> WSService
    
    subgraph "外部設備"
        Gateway["網關設備"]
        EPD["電子紙顯示器"]
        
        Gateway <--> EPD
    end
    
    WSService <--> Gateway
```

### 2.3 技術依賴

- React Native v0.70+
- Expo SDK 48+
- Redux & Redux Toolkit
- React Navigation v6+
- Axios (HTTP 請求)
- React Native Elements (UI 組件庫)
- React Native Vector Icons
- React Native NetInfo (網絡狀態檢測)
- AsyncStorage (本地存儲)
- React Native WebSocket (WebSocket 通信)

## 3. 功能規劃

### 3.1 核心功能列表

根據 `test-ws-client-interactive.js` 的功能，應用需要實現以下核心功能：

1. **用戶認證**
   - 登入/登出功能
   - 伺服器地址配置
   - 記住登入狀態

2. **門店管理**
   - 門店列表查看
   - 門店選擇

3. **網關管理**
   - 網關列表查看
   - 網關創建
   - 網關詳情查看
   - 網關狀態監控

4. **設備管理**
   - 設備列表查看
   - 設備添加
   - 設備移除
   - 設備狀態監控

5. **WebSocket 通信**
   - 與服務器建立 WebSocket 連接
   - 發送心跳消息
   - 發送設備狀態消息
   - 發送網關信息消息
   - 接收服務器命令

6. **圖像處理**
   - 請求設備預覽圖像
   - 顯示和保存接收到的圖像

### 3.2 功能流程圖

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant Server as 後端服務器
    participant Gateway as 網關設備
    
    User->>App: 登入
    App->>Server: 發送登入請求
    Server-->>App: 返回認證 Token
    
    User->>App: 選擇門店
    App->>Server: 獲取門店的網關列表
    Server-->>App: 返回網關列表
    
    User->>App: 選擇網關或創建新網關
    
    alt 創建新網關
        App->>Server: 發送創建網關請求
        Server-->>App: 返回新網關信息
    else 選擇現有網關
        App->>App: 使用選定的網關
    end
    
    App->>Server: 建立 WebSocket 連接
    Server-->>App: 連接確認
    
    loop 定期通信
        App->>Server: 發送心跳消息
        App->>Server: 發送設備狀態
        App->>Server: 發送網關信息
        Server-->>App: 發送命令或回應
    end
    
    User->>App: 添加自定義設備
    App->>App: 更新設備列表
    App->>Server: 發送更新的設備狀態
    
    User->>App: 請求設備預覽圖像
    App->>Server: 發送圖像請求
    Server->>Gateway: 轉發圖像請求
    Gateway-->>Server: 返回圖像數據
    Server-->>App: 發送圖像數據
    App->>App: 顯示並保存圖像
```

## 4. 用戶界面設計

### 4.1 主要頁面

1. **登入頁面**
   - 用戶名/密碼輸入
   - 伺服器地址配置
   - 記住我選項

2. **門店選擇頁面**
   - 門店列表
   - 搜索/篩選功能

3. **網關管理頁面**
   - 網關列表
   - 創建新網關按鈕
   - 網關詳情顯示

4. **設備管理頁面**
   - 設備列表
   - 添加設備按鈕
   - 設備詳情顯示
   - 設備狀態指示

5. **WebSocket 控制台頁面**
   - 連接狀態顯示
   - 消息日誌
   - 手動命令輸入

6. **設置頁面**
   - 應用配置
   - 用戶信息
   - 登出選項

### 4.2 導航結構

```mermaid
graph TD
    Login[登入頁面] --> StoreSelect[門店選擇頁面]
    StoreSelect --> MainTabs[主標籤導航]
    
    MainTabs --> GatewayTab[網關管理]
    MainTabs --> DeviceTab[設備管理]
    MainTabs --> ConsoleTab[WebSocket 控制台]
    MainTabs --> SettingsTab[設置]
    
    GatewayTab --> GatewayDetail[網關詳情]
    GatewayTab --> CreateGateway[創建網關]
    
    DeviceTab --> DeviceDetail[設備詳情]
    DeviceTab --> AddDevice[添加設備]
    
    ConsoleTab --> ImageViewer[圖像查看器]
```

## 5. 技術實現細節

### 5.1 用戶認證

使用 JWT 認證機制，與後端服務保持一致：

```javascript
// 登入函數
async function login(username, password) {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });

    if (!response.ok) {
      throw new Error(`登入失敗: ${response.status}`);
    }

    const data = await response.json();
    return data.token;
  } catch (error) {
    console.error('登入錯誤:', error);
    throw error;
  }
}
```

### 5.2 WebSocket 連接

實現與後端的 WebSocket 連接：

```javascript
// 建立 WebSocket 連接
function connectWebSocket(gateway, storeId, token) {
  // 構建 WebSocket URL
  const url = `ws://${API_HOST}:${API_PORT}/ws/store/${storeId}/gateway/${gateway._id}?token=${token}`;
  
  const ws = new WebSocket(url);
  
  ws.onopen = () => {
    console.log('WebSocket 連接已建立');
    
    // 發送 ping 消息
    sendPingMessage(ws);
    
    // 設置定期發送心跳
    const pingInterval = setInterval(() => {
      sendPingMessage(ws);
    }, 25000);
    
    // 設置定期發送設備狀態
    const deviceStatusInterval = setInterval(() => {
      sendDeviceStatusMessage(ws);
    }, 5000);
    
    // 設置發送網關信息
    setTimeout(() => {
      sendGatewayInfoMessage(ws, gateway);
    }, 30000);
    
    return {
      ws,
      intervals: {
        ping: pingInterval,
        deviceStatus: deviceStatusInterval
      }
    };
  };
  
  ws.onmessage = (event) => {
    handleWebSocketMessage(event.data);
  };
  
  ws.onerror = (error) => {
    console.error('WebSocket 錯誤:', error);
  };
  
  ws.onclose = (event) => {
    console.log(`WebSocket 連接已關閉: ${event.code} ${event.reason}`);
    // 處理重連邏輯
  };
  
  return ws;
}
```

## 6. 開發計劃

### 6.1 開發階段

1. **準備階段** (1週)
   - 環境搭建
   - 項目初始化
   - 技術選型確認

2. **基礎功能開發** (2週)
   - 用戶認證
   - 門店管理
   - 網關管理基礎功能

3. **核心功能開發** (3週)
   - WebSocket 通信
   - 設備管理
   - 圖像處理

4. **UI 優化與測試** (2週)
   - 界面美化
   - 用戶體驗優化
   - 功能測試與修復

5. **發布準備** (1週)
   - 性能優化
   - 文檔完善
   - 打包與發布準備

### 6.2 里程碑

1. **M1**: 完成基礎框架搭建與用戶認證 (第1週結束)
2. **M2**: 完成門店與網關管理功能 (第3週結束)
3. **M3**: 完成 WebSocket 通信與設備管理 (第6週結束)
4. **M4**: 完成所有功能與 UI 優化 (第8週結束)
5. **M5**: 應用發布準備就緒 (第9週結束)

## 7. 總結

EPD Manager App 將為用戶提供便捷的移動端管理體驗，使其能夠隨時隨地管理電子紙顯示系統。通過本開發計劃的實施，我們將在9週內完成一個功能完整、用戶體驗良好的移動應用，滿足用戶的各種管理需求。
