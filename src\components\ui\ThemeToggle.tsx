import React from 'react';
import { <PERSON>, Sun } from 'lucide-react';
import { useThemeStore } from '../../store/themeStore';
import { useTranslation } from 'react-i18next';

interface ThemeToggleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  className = '', 
  size = 'md',
  showLabel = false 
}) => {
  const { theme, isDark, toggleTheme } = useThemeStore();
  const { t } = useTranslation();

  // 根據尺寸設置圖標大小和按鈕樣式
  const sizeConfig = {
    sm: {
      iconSize: 14,
      buttonClass: 'px-2 py-1 text-xs',
    },
    md: {
      iconSize: 16,
      buttonClass: 'px-3 py-1 text-sm',
    },
    lg: {
      iconSize: 20,
      buttonClass: 'px-4 py-2 text-base',
    }
  };

  const config = sizeConfig[size];

  return (
    <button
      onClick={toggleTheme}
      className={`
        flex items-center gap-2 
        ${config.buttonClass}
        bg-background hover:bg-muted
        border border-border
        rounded-md 
        transition-all duration-200 
        hover:scale-105 
        focus:outline-none 
        focus:ring-2 
        focus:ring-ring 
        focus:ring-offset-2
        ${className}
      `}
      title={isDark ? t('theme.switchToLight') : t('theme.switchToDark')}
      aria-label={isDark ? t('theme.switchToLight') : t('theme.switchToDark')}
    >
      {/* 主題圖標 */}
      <div className="relative">
        {isDark ? (
          <Moon 
            size={config.iconSize} 
            className="text-foreground transition-transform duration-200" 
          />
        ) : (
          <Sun 
            size={config.iconSize} 
            className="text-foreground transition-transform duration-200" 
          />
        )}
      </div>
      
      {/* 可選的文字標籤 */}
      {showLabel && (
        <span className="text-foreground font-medium">
          {isDark ? t('theme.darkMode') : t('theme.lightMode')}
        </span>
      )}
    </button>
  );
};

export default ThemeToggle;
