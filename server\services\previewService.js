const fetch = require('node-fetch');
const { JSDOM } = require('jsdom');
const { createCanvas, loadImage, Path2D } = require('canvas');

// 預覽服務配置
const PREVIEW_SERVICE_URL = process.env.PREVIEW_SERVICE_URL || 'http://localhost:3005'; // 修改為 3005 避免端口衝突

/**
 * 處理圖片 URL，確保能正確載入圖片
 * 支援多種格式：完整URL、相對路徑、文件ID
 * @param {string} imageUrl 原始圖片 URL 或文件ID
 * @returns {string} 處理後的完整圖片 URL
 */
function processImageUrl(imageUrl) {
  if (!imageUrl) return imageUrl;

  // 如果看起來像文件 ID（24位十六進制字符），直接構建 API URL
  if (/^[a-f0-9]{24}$/i.test(imageUrl)) {
    return `http://localhost:3001/api/files/${imageUrl}`;
  }

  // 如果是相對路徑，構建完整的 API URL
  if (imageUrl.startsWith('/api/files/')) {
    // 已經是 API 路徑，構建完整 URL
    return `http://localhost:3001${imageUrl}`;
  }

  // 如果是完整的 HTTP/HTTPS URL，檢查是否需要轉換
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    // 檢查是否是 localhost URL，如果是則需要替換為當前主機
    if (imageUrl.includes('localhost:3001') || imageUrl.includes('127.0.0.1:3001')) {
      return imageUrl; // 已經是正確的格式
    }
    return imageUrl;
  }

  // 其他情況直接返回原 URL
  return imageUrl;
}

// 創建虛擬DOM環境
function createVirtualDom() {
  const dom = new JSDOM(`
    <!DOCTYPE html>
    <html>
      <head>
        <style>
          body { margin: 0; padding: 0; }
          .preview-component { position: relative; }
          .elements-container { position: relative; }
        </style>
      </head>
      <body>
        <div id="root"></div>
      </body>
    </html>
  `, {
    url: "http://localhost",
    pretendToBeVisual: true,
    resources: "usable"
  });

  return dom;
}

/**
 * 從預覽服務生成預覽圖
 * @param {Object} template 模板數據
 * @param {Object} bindingData 綁定數據
 * @param {Array} storeData 門店數據
 * @param {Object} options 選項
 * @param {Array} dataFields 資料欄位數據，用於獲取前綴
 * @returns {Promise<string>} 預覽圖數據URL
 */
async function generatePreviewFromService(template, bindingData, storeData, options = {}, dataFields = []) {
  try {
    console.log('調用預覽服務生成預覽圖...');
    console.log('模板ID:', template.id);
    console.log('綁定數據字段數:', Object.keys(bindingData).length);

    const response = await fetch(`${PREVIEW_SERVICE_URL}/api/preview/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        template,
        bindingData,
        storeData,
        dataFields,
        effectType: options.effectType || 'blackAndWhite',
        threshold: options.threshold || 128
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`預覽服務錯誤: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || '預覽服務返回失敗結果');
    }

    console.log('預覽圖生成成功，數據長度:', result.previewData ? result.previewData.length : 0);
    return result.previewData;
  } catch (error) {
    console.error('調用預覽服務失敗:', error);
    // 不拋出錯誤，而是返回 null，讓調用者可以處理失敗情況
    return null;
  }
}

/**
 * 批量生成預覽圖
 * @param {Array} items 項目數組，每個項目包含id、template、bindingData和storeData
 * @param {Object} options 選項
 * @returns {Promise<Array>} 結果數組
 */
async function batchGeneratePreviewsFromService(items, options = {}) {
  try {
    console.log(`批量生成預覽圖，項目數量: ${items.length}`);

    const response = await fetch(`${PREVIEW_SERVICE_URL}/api/preview/batch-generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        items,
        effectType: options.effectType || 'blackAndWhite',
        threshold: options.threshold || 128
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`預覽服務錯誤: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || '預覽服務返回失敗結果');
    }

    console.log(`批量預覽圖生成完成，成功: ${result.results.filter(r => r.success).length}/${result.results.length}`);
    return result.results;
  } catch (error) {
    console.error('調用批量預覽服務失敗:', error);
    // 不拋出錯誤，而是返回空數組，讓調用者可以處理失敗情況
    return [];
  }
}

/**
 * 檢查預覽服務健康狀態
 * @returns {Promise<boolean>} 服務是否健康
 */
async function checkPreviewServiceHealth() {
  try {
    const response = await fetch(`${PREVIEW_SERVICE_URL}/health`);

    if (!response.ok) {
      return false;
    }

    const result = await response.json();
    return result.status === 'ok';
  } catch (error) {
    console.error('檢查預覽服務健康狀態失敗:', error);
    return false;
  }
}

/**
 * 在發送預覽圖到網關前重新生成最新預覽圖
 * 使用前端的 PreviewComponent 邏輯來生成預覽圖
 * @param {Object} device 設備數據
 * @param {Array} storeData 門店數據
 * @param {Object} template 模板數據
 * @param {Array} dataFields 資料欄位數據，用於獲取前綴
 * @returns {Promise<string|null>} 生成的預覽圖數據，如失敗則返回null
 */
async function regeneratePreviewBeforeSend(
  device,
  storeData,
  template,
  dataFields = []
) {
  if (!device || !template) {
    console.error('無法重新生成預覽圖：缺少設備或模板數據');
    return null;
  }

  try {
    // 處理數據綁定格式，確保是對象類型
    let bindingData = device.dataBindings;
    if (typeof bindingData === 'string') {
      try {
        bindingData = JSON.parse(bindingData);
        console.log('成功將數據綁定從字符串解析為對象，字段數:', Object.keys(bindingData).length);
      } catch (error) {
        console.error('解析數據綁定字符串失敗:', error);
        bindingData = {};
      }
    } else if (!bindingData) {
      // 如果沒有數據綁定，創建一個空對象
      bindingData = {};
      console.log('設備沒有數據綁定，使用空對象');
    }

    // 調試輸出 bindingData
    console.log('重新生成預覽圖時的 bindingData 類型:', typeof bindingData);
    console.log('重新生成預覽圖時的 bindingData 值:', JSON.stringify(bindingData, null, 2));
    console.log('device.dataBindings 原始值:', device.dataBindings);

    // 創建虛擬DOM環境
    const dom = createVirtualDom();
    const { window } = dom;
    const { document } = window;

    // 創建一個臨時容器用於渲染
    const tempContainer = document.createElement('div');
    tempContainer.className = 'preview-component';
    document.body.appendChild(tempContainer);

    // 創建一個隱藏的渲染容器
    const containerRef = document.createElement('div');
    containerRef.style.position = 'absolute';
    containerRef.style.left = '-9999px';
    containerRef.style.top = '0';
    containerRef.style.visibility = 'hidden';
    tempContainer.appendChild(containerRef);

    // 創建預覽顯示區域
    const previewDisplay = document.createElement('div');
    previewDisplay.className = 'preview-display';
    tempContainer.appendChild(previewDisplay);

    // 獲取模板尺寸
    let canvasWidth = 250;  // 默認值
    let canvasHeight = 122; // 默認值

    if (template && template.screenSize) {
      // 解析模板的螢幕尺寸
      const sizeMatch = template.screenSize.match(/(\d+)x(\d+)/);
      if (sizeMatch && sizeMatch[1] && sizeMatch[2]) {
        canvasWidth = parseInt(sizeMatch[1], 10);
        canvasHeight = parseInt(sizeMatch[2], 10);
      }
    } else if (template.width && template.height) {
      canvasWidth = template.width;
      canvasHeight = template.height;
    }

    // 設置容器尺寸
    containerRef.style.width = `${canvasWidth}px`;
    containerRef.style.height = `${canvasHeight}px`;
    containerRef.setAttribute('data-canvas-width', String(canvasWidth));
    containerRef.setAttribute('data-canvas-height', String(canvasHeight));

    // 創建元素容器
    const elementsContainer = document.createElement('div');
    elementsContainer.className = 'elements-container';
    elementsContainer.style.position = 'relative';
    elementsContainer.style.width = '100%';
    elementsContainer.style.height = '100%';
    containerRef.appendChild(elementsContainer);

    // 分析模板中的數據綁定
    analyzeTemplateBindings(template.elements || []);

    // 構造數據樣本 - 從 bindingData 和 storeData 中提取實際數據
    const sampleDataByIndex = {};
    const storeItemsByDataIndex = {};

    console.log('開始處理綁定數據:', bindingData);
    console.log('門店數據數量:', storeData.length);

    // 創建一個映射來存儲每個數據索引對應的數據ID
    const dataIdsByIndex = {};

    // 第一步：解析所有的數據索引和對應的ID
    Object.entries(bindingData).forEach(([key, value]) => {
      console.log(`處理綁定數據: ${key} -> ${value}`);

      // 檢查是否是 dataIndex 格式的鍵
      const dataIndexMatch = key.match(/dataIndex(\d+)/);
      if (dataIndexMatch) {
        const dataIndex = parseInt(dataIndexMatch[1]);
        console.log(`從 ${key} 提取數據索引: ${dataIndex}`);

        // 將數據ID存儲到映射中
        dataIdsByIndex[dataIndex] = value;
      }

      // 檢查是否是 name_idx 格式的鍵 (這是另一種可能的格式)
      const nameIdxMatch = key.match(/name_idx(\d+)/);
      if (nameIdxMatch) {
        const dataIndex = parseInt(nameIdxMatch[1]);
        console.log(`從 ${key} 提取數據索引 (name_idx格式): ${dataIndex}`);

        // 將數據ID存儲到映射中
        dataIdsByIndex[dataIndex] = value;
      }
    });

    console.log('解析後的數據索引映射:', dataIdsByIndex);

    // 輸出整個 storeData 的結構，幫助調試
    console.log('===== DEBUG: storeData 結構 =====');
    console.log('storeData 類型:', typeof storeData);
    console.log('storeData 是否為數組:', Array.isArray(storeData));
    console.log('storeData 長度:', storeData ? storeData.length : 0);

    // 檢查 storeData 是否為空
    if (!storeData || storeData.length === 0) {
      console.error('警告: storeData 為空或不是數組，這可能導致數據綁定失敗');
      console.log('無法生成預覽圖：缺少門店數據');
      // 不使用模擬數據，而是直接返回錯誤
      return null;
    }

    // 輸出 storeData 的詳細結構
    storeData.forEach((store, index) => {
      console.log(`門店 ${index}:`, {
        id: store.id,
        name: store.name,
        _id: store._id,
        hasStoreSpecificData: !!store.storeSpecificData,
        storeSpecificDataCount: store.storeSpecificData ? store.storeSpecificData.length : 0
      });

      // 如果有 storeSpecificData，輸出其中的前 3 個項目（避免輸出過多）
      if (store.storeSpecificData && Array.isArray(store.storeSpecificData)) {
        console.log(`門店 ${index} 的 storeSpecificData 前 3 個項目:`);
        store.storeSpecificData.slice(0, 3).forEach((item, itemIndex) => {
          console.log(`  項目 ${itemIndex}:`, {
            id: item.id,
            uid: item.uid,
            _id: item._id,
            name: item.name,
            keys: Object.keys(item)
          });
        });

        if (store.storeSpecificData.length > 3) {
          console.log(`  ... 還有 ${store.storeSpecificData.length - 3} 個項目未顯示`);
        }
      }
    });
    console.log('===== DEBUG: storeData 結構結束 =====');

    // 第二步：對於每個數據索引，查找對應的門店數據
    Object.entries(dataIdsByIndex).forEach(([dataIndex, dataId]) => {
      dataIndex = parseInt(dataIndex);
      console.log(`\n===== 處理數據索引 ${dataIndex} 對應的ID: ${dataId} =====`);

      if (dataId) {
        // 首先嘗試在 storeSpecificData 中查找對應的數據
        let foundStoreItem = null;
        let foundInStoreSpecificData = false;

        console.log(`開始在 storeSpecificData 中查找 ID 為 ${dataId} 的數據項...`);

        // 遍歷所有門店數據
        for (const store of storeData) {
          // 檢查是否有 storeSpecificData 數組
          if (store.storeSpecificData && Array.isArray(store.storeSpecificData)) {
            console.log(`檢查門店 id=${store.id}, name=${store.name} 的 storeSpecificData (${store.storeSpecificData.length} 個項目)`);

            // 輸出 storeSpecificData 中的 ID 列表，幫助調試
            const idList = store.storeSpecificData.map(item => item.id || 'undefined').slice(0, 10);
            console.log(`該門店 storeSpecificData 中的前 10 個 ID:`, idList);

            // 在 storeSpecificData 中查找 id 匹配的項目
            const specificDataItem = store.storeSpecificData.find(item => {
              const idMatch = item.id === dataId;
              const uidMatch = item.uid === dataId;
              const _idMatch = item._id && item._id.toString() === dataId;

              if (idMatch || uidMatch || _idMatch) {
                console.log(`匹配詳情 - id匹配: ${idMatch}, uid匹配: ${uidMatch}, _id匹配: ${_idMatch}`);
                return true;
              }
              return false;
            });

            if (specificDataItem) {
              console.log(`在 store.id=${store.id} 的 storeSpecificData 中找到數據項:`, {
                id: specificDataItem.id,
                uid: specificDataItem.uid,
                _id: specificDataItem._id,
                name: specificDataItem.name,
                keys: Object.keys(specificDataItem)
              });

              foundStoreItem = specificDataItem;
              foundInStoreSpecificData = true;

              // 添加門店信息到找到的數據項中
              foundStoreItem._storeId = store.id;
              foundStoreItem._storeName = store.name;
              break;
            }
          }
        }

        // 如果在 storeSpecificData 中沒有找到，則嘗試在頂層查找
        if (!foundStoreItem) {
          console.log(`在 storeSpecificData 中未找到 ID 為 ${dataId} 的數據項，嘗試在頂層查找...`);

          // 輸出頂層門店數據的 ID 列表，幫助調試
          const topLevelIds = storeData.map(item => ({
            id: item.id,
            uid: item.uid,
            _id: item._id ? item._id.toString() : 'undefined'
          }));
          console.log(`頂層門店數據的 ID 列表:`, topLevelIds);

          foundStoreItem = storeData.find(item => {
            const idMatch = item.id === dataId;
            const uidMatch = item.uid === dataId;
            const _idMatch = item._id && item._id.toString() === dataId;

            if (idMatch || uidMatch || _idMatch) {
              console.log(`頂層匹配詳情 - id匹配: ${idMatch}, uid匹配: ${uidMatch}, _id匹配: ${_idMatch}`);
              return true;
            }
            return false;
          });

          if (foundStoreItem) {
            console.log(`在頂層門店數據中找到項目:`, {
              id: foundStoreItem.id,
              uid: foundStoreItem.uid,
              _id: foundStoreItem._id,
              name: foundStoreItem.name,
              keys: Object.keys(foundStoreItem)
            });
          }
        }

        if (foundStoreItem) {
          console.log(`成功找到數據項，來源: ${foundInStoreSpecificData ? 'storeSpecificData' : '頂層門店數據'}`);

          // 保存整個門店項目，以便後續使用
          storeItemsByDataIndex[dataIndex] = foundStoreItem;

          // 為這個數據索引初始化一個對象
          if (!sampleDataByIndex[dataIndex]) {
            sampleDataByIndex[dataIndex] = {};
          }

          // 將門店數據中的所有欄位加入到對應的數據索引中
          console.log(`將數據項的所有欄位加入到數據索引 ${dataIndex} 中...`);
          for (const [key, value] of Object.entries(foundStoreItem)) {
            if (key !== 'id' && key !== '_id') { // 排除 id 欄位
              sampleDataByIndex[dataIndex][key] = value;
              console.log(`  添加欄位 ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`);
            }
          }

          // 特別處理：將 name 欄位添加到數據索引中
          if (foundStoreItem.name) {
            sampleDataByIndex[dataIndex]['name'] = foundStoreItem.name;
            console.log(`為數據索引 ${dataIndex} 添加 name 欄位: ${foundStoreItem.name}`);
          }

          // 如果是從 storeSpecificData 中找到的，添加一些額外信息
          if (foundInStoreSpecificData) {
            console.log(`數據項來自 storeSpecificData，添加門店信息`);
            if (foundStoreItem._storeId) {
              sampleDataByIndex[dataIndex]['_storeId'] = foundStoreItem._storeId;
              console.log(`  添加 _storeId: ${foundStoreItem._storeId}`);
            }
            if (foundStoreItem._storeName) {
              sampleDataByIndex[dataIndex]['_storeName'] = foundStoreItem._storeName;
              console.log(`  添加 _storeName: ${foundStoreItem._storeName}`);
            }
          }

          console.log(`數據索引 ${dataIndex} 的最終欄位:`, Object.keys(sampleDataByIndex[dataIndex]));
        } else {
          console.log(`未找到 ID 為 ${dataId} 的門店數據項（在 storeSpecificData 和頂層都未找到）`);
          console.log(`嘗試使用默認值...`);

          // 為這個數據索引初始化一個對象，即使沒有找到對應的數據項
          if (!sampleDataByIndex[dataIndex]) {
            sampleDataByIndex[dataIndex] = {};
          }

          // 添加一些默認值，以便渲染時不會出錯
          sampleDataByIndex[dataIndex]['name'] = `未找到 ID 為 ${dataId} 的數據`;
          sampleDataByIndex[dataIndex]['_notFound'] = true;
          console.log(`為數據索引 ${dataIndex} 添加默認值`);
        }
      }
    });

    // 輸出最終的數據樣本
    console.log('最終的數據樣本:', sampleDataByIndex);
    console.log('門店項目映射:', storeItemsByDataIndex);

    // 為每個元素創建 DOM 元素，並添加到臨時畫布
    await renderTemplateElements(template.elements || [], elementsContainer, sampleDataByIndex, storeItemsByDataIndex, document, dataFields);

    // 處理綁定元素，使其顯示實際數據
    console.log('開始處理綁定文字元素...');
    const originalStyles = processTextBindings(document);
    console.log('綁定文字元素處理完成，共處理了', originalStyles.length, '個元素');

    // 渲染畫布為圖像
    const renderedCanvas = await renderCanvasToImage(containerRef, canvasWidth, canvasHeight, sampleDataByIndex, dataFields);

    // 恢復綁定元素的原始樣式和內容
    restoreTextBindings(originalStyles);

    if (!renderedCanvas) {
      return null;
    }

    // 應用選定的效果
    // 使用簡化版的顏色轉換模組
    const { convertImageByColorType, shouldUseColorConversion, getConversionInfo } = require('../utils/colorConversion');

    let previewCanvas;
    if (shouldUseColorConversion(template)) {
      const conversionInfo = getConversionInfo(template.color);
      console.log(`後端顏色轉換 - 模板顏色類型: ${conversionInfo.colorType}, 算法: ${conversionInfo.name}`);
      previewCanvas = convertImageByColorType(renderedCanvas, template.color, { threshold: 128 });
    } else {
      console.log('後端使用傳統黑白轉換');
      previewCanvas = await applyImageEffect(renderedCanvas, 'blackAndWhite', 128);
    }

    // 將預覽圖轉換為 base64 數據
    const previewData = previewCanvas.toDataURL('image/png');

    // 清理DOM
    document.body.removeChild(tempContainer);

    return previewData;
  } catch (error) {
    console.error('重新生成預覽圖失敗:', error);
    return null;
  }
}

/**
 * 分析模板中的數據綁定關係
 * @param {Array} elements 模板元素數組
 * @returns {Object} 數據綁定分析結果
 */
function analyzeTemplateBindings(elements) {
  // 收集所有帶有資料綁定的元素
  const elementsWithDataBinding = [];
  const dataIndicesByIndex = [];
  const fieldMappings = new Map();

  // 調試信息：輸出模板元素總數
  console.log(`分析模板中的數據綁定，元素總數: ${elements.length}`);

  for (const element of elements) {
    // 檢查是否有數據綁定
    if (element.dataBinding && element.dataBinding.dataIndex !== undefined && element.dataBinding.fieldId) {
      const dataIndex = element.dataBinding.dataIndex;
      const fieldId = element.dataBinding.fieldId;

      console.log(`找到帶有數據綁定的元素: type=${element.type}, dataIndex=${dataIndex}, fieldId=${fieldId}`);

      elementsWithDataBinding.push({
        element,
        dataIndex,
        fieldId
      });

      // 記錄每個 dataIndex 對應的欄位
      if (!fieldMappings.has(dataIndex)) {
        fieldMappings.set(dataIndex, [fieldId]);
        dataIndicesByIndex.push({
          dataIndex,
          displayName: `資料${dataIndex + 1}`,
          fieldId: `dataIndex${dataIndex}`
        });
        console.log(`為 dataIndex=${dataIndex} 創建新的映射，欄位: ${fieldId}`);
      } else {
        const fields = fieldMappings.get(dataIndex);
        if (fields && !fields.includes(fieldId)) {
          fields.push(fieldId);
          console.log(`為 dataIndex=${dataIndex} 添加欄位: ${fieldId}`);
        }
      }
    } else if (element.dataFieldId) {
      // 處理舊版數據綁定格式
      const match = element.dataFieldId.match(/(\d+)/);
      if (match) {
        const dataIndex = parseInt(match[1]) - 1; // 將 "data1" 映射到索引 0
        const fieldId = element.dataFieldId;

        console.log(`找到帶有舊版數據綁定的元素: type=${element.type}, dataIndex=${dataIndex}, fieldId=${fieldId}`);

        elementsWithDataBinding.push({
          element,
          dataIndex,
          fieldId
        });

        // 記錄每個 dataIndex 對應的欄位
        if (!fieldMappings.has(dataIndex)) {
          fieldMappings.set(dataIndex, [fieldId]);
          dataIndicesByIndex.push({
            dataIndex,
            displayName: `資料${dataIndex + 1}`,
            fieldId: `dataIndex${dataIndex}`
          });
          console.log(`為舊版 dataIndex=${dataIndex} 創建新的映射，欄位: ${fieldId}`);
        } else {
          const fields = fieldMappings.get(dataIndex);
          if (fields && !fields.includes(fieldId)) {
            fields.push(fieldId);
            console.log(`為舊版 dataIndex=${dataIndex} 添加欄位: ${fieldId}`);
          }
        }
      }
    }
  }

  // 調試信息：輸出分析結果
  console.log(`找到 ${elementsWithDataBinding.length} 個帶有數據綁定的元素`);
  console.log(`數據索引總數: ${dataIndicesByIndex.length}`);
  console.log(`欄位映射: `, Object.fromEntries(fieldMappings));

  return { dataIndicesByIndex, fieldMappings };
}

/**
 * 渲染模板元素到DOM
 * @param {Array} elements 模板元素數組
 * @param {HTMLElement} elementsContainer 元素容器
 * @param {Object} sampleDataByIndex 數據樣本
 * @param {Object} storeItemsByDataIndex 門店項目映射
 * @param {Document} document DOM文檔
 * @param {Array} dataFields 資料欄位數據，用於獲取前綴
 */
async function renderTemplateElements(elements, elementsContainer, sampleDataByIndex, storeItemsByDataIndex, document, dataFields = []) {
  for (let index = 0; index < elements.length; index++) {
    const element = elements[index];
    const elementDiv = document.createElement('div');
    elementDiv.setAttribute('data-element-id', String(index));
    elementDiv.setAttribute('data-element-type', element.type);
    elementDiv.style.position = 'absolute';

    // 對於線條元素，需要特別處理其位置和尺寸
    if (element.type === 'line') {
      // 計算虛擬選取框的實際位置和尺寸
      let boxLeft = element.x;
      let boxTop = element.y;
      let boxWidth = Math.abs(element.width);
      let boxHeight = Math.abs(element.height);

      // 根據線段的方向調整選取框位置
      if (element.width < 0) {
        boxLeft = element.x + element.width;
      }
      if (element.height < 0) {
        boxTop = element.y + element.height;
      }

      elementDiv.style.left = `${boxLeft}px`;
      elementDiv.style.top = `${boxTop}px`;
      elementDiv.style.width = `${boxWidth + 1}px`; // 確保有足夠的空間顯示線條
      elementDiv.style.height = `${boxHeight + 1}px`;
    } else {
      // 其他元素使用原始值
      elementDiv.style.left = `${element.x}px`;
      elementDiv.style.top = `${element.y}px`;
      elementDiv.style.width = `${element.width}px`;
      elementDiv.style.height = `${element.height}px`;
    }

    // 處理元素旋轉
    if (element.rotation) {
      elementDiv.style.transform = `rotate(${element.rotation}deg)`;
      elementDiv.style.transformOrigin = 'center center';
    }

    // 檢查是否為文字元素且有數據綁定
    if ((element.type === 'text' || element.type === 'multiline-text')) {
      // 創建文字容器
      const textContainer = document.createElement('div');
      textContainer.className = 'text-element-content';
      textContainer.style.width = '100%';
      textContainer.style.height = '100%';
      textContainer.style.fontSize = `${element.fontSize || 12}px`;
      textContainer.style.fontFamily = element.fontFamily || 'Arial';
      textContainer.style.color = element.lineColor || '#000';

      // 檢查是否有數據綁定
      if (element.dataBinding && element.dataBinding.fieldId) {
        const dataIndex = element.dataBinding.dataIndex;
        const fieldId = element.dataBinding.fieldId;
        // 根據 template editor 的邏輯，只有當 displayOptions.showPrefix 為 true 時才顯示前綴
        const showPrefix = element.dataBinding.displayOptions?.showPrefix === true;

        // 設置綁定數據屬性
        elementDiv.setAttribute('data-has-binding', 'true');
        elementDiv.setAttribute('data-field-id', fieldId);
        elementDiv.setAttribute('data-index', String(dataIndex));
        elementDiv.setAttribute('data-show-prefix', String(showPrefix));
        elementDiv.setAttribute('data-original-content', element.content || 'Text');

        // 設置前綴屬性，以便後續渲染時使用
        if (element.dataBinding && element.dataBinding.prefix) {
          elementDiv.setAttribute('data-prefix', element.dataBinding.prefix);
        }

        // 如果有對應的門店數據，設置門店 ID 和 item-uid
        if (storeItemsByDataIndex[dataIndex]) {
          // 設置門店 ID
          if (storeItemsByDataIndex[dataIndex].storeId) {
            elementDiv.setAttribute('data-store-id', storeItemsByDataIndex[dataIndex].storeId);
          }
          // 如果沒有 storeId 屬性，則使用 id 屬性
          else if (storeItemsByDataIndex[dataIndex].id) {
            elementDiv.setAttribute('data-store-id', storeItemsByDataIndex[dataIndex].id);
          }

          // 如果門店數據有 uid 欄位，設置 item-uid
          if (storeItemsByDataIndex[dataIndex].uid) {
            elementDiv.setAttribute('data-item-uid', storeItemsByDataIndex[dataIndex].uid);
          }
        }

        // 設置文字內容
        // 首先檢查是否有對應的數據索引
        if (sampleDataByIndex[dataIndex]) {
          // 檢查該數據索引下是否有對應的欄位數據
          if (sampleDataByIndex[dataIndex][fieldId] !== undefined) {
            // 如果有實際數據，使用實際數據
            let displayValue = String(sampleDataByIndex[dataIndex][fieldId]);
            console.log(`找到綁定數據: dataIndex=${dataIndex}, fieldId=${fieldId}, value=${displayValue}`);

            // 如果需要顯示前綴，添加前綴
            if (showPrefix) {
              // 從 dataFields 中查找欄位的前綴
              let prefix = '';
              const field = dataFields.find(f => f.id === fieldId);
              if (field && field.prefix) {
                prefix = field.prefix;
              }
              // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
              displayValue = prefix ? `${prefix}: ${displayValue}` : `: ${displayValue}`;
            }

            textContainer.textContent = displayValue;
          } else {
            console.log(`未找到欄位數據: dataIndex=${dataIndex}, fieldId=${fieldId}`);
            // 如果該數據索引下沒有對應的欄位數據，顯示默認文本 'TEXT'
            let displayValue = 'TEXT';

            // 即使沒有實際數據，也要檢查是否需要顯示前綴
            if (showPrefix) {
              // 從 dataFields 中查找欄位的前綴
              let prefix = '';
              const field = dataFields.find(f => f.id === fieldId);
              if (field && field.prefix) {
                prefix = field.prefix;
              }
              // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
              displayValue = prefix ? `${prefix}: ${displayValue}` : `: ${displayValue}`;
            }

            textContainer.textContent = displayValue;
          }
        } else {
          console.log(`未找到數據索引: dataIndex=${dataIndex}`);
          // 如果沒有對應的數據索引，顯示默認文本 'TEXT'
          let displayValue = 'TEXT';

          // 即使沒有實際數據，也要檢查是否需要顯示前綴
          if (showPrefix) {
            // 從 dataFields 中查找欄位的前綴
            let prefix = '';
            const field = dataFields.find(f => f.id === fieldId);
            if (field && field.prefix) {
              prefix = field.prefix;
            }
            // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
            displayValue = prefix ? `${prefix}: ${displayValue}` : `: ${displayValue}`;
          }

          textContainer.textContent = displayValue;
        }
      } else if (element.dataFieldId) {
        // 處理舊版數據綁定格式
        elementDiv.setAttribute('data-has-binding', 'true');
        elementDiv.setAttribute('data-field-id', element.dataFieldId);
        elementDiv.setAttribute('data-original-content', element.content || 'Text');

        // 從 dataFieldId 中提取 dataIndex (如 "data1" -> 0)
        const match = element.dataFieldId.match(/(\d+)/);
        if (match) {
          const dataIndex = parseInt(match[1]) - 1; // 將 "data1" 映射到索引 0
          elementDiv.setAttribute('data-index', String(dataIndex));
        } else {
          elementDiv.setAttribute('data-index', '0');
        }

        // 顯示默認文本 'TEXT'
        textContainer.textContent = 'TEXT';
      } else {
        // 沒有綁定，使用元素的原始內容
        elementDiv.setAttribute('data-has-binding', 'false');
        elementDiv.setAttribute('data-original-content', element.content || 'Text');
        textContainer.textContent = element.content || '';
      }

      elementDiv.appendChild(textContainer);
    } else if (element.type === 'rectangle' || element.type === 'square') {
      // 矩形/正方形元素渲染
      elementDiv.style.border = `${element.lineWidth || 1}px solid ${element.lineColor || '#000'}`;
      elementDiv.style.backgroundColor = element.fillColor || 'transparent';
    } else if (element.type === 'circle' || element.type === 'ellipse') {
      // 圓形/橢圓形元素渲染
      elementDiv.style.border = `${element.lineWidth || 1}px solid ${element.lineColor || '#000'}`;
      elementDiv.style.backgroundColor = element.fillColor || 'transparent';
      elementDiv.style.borderRadius = '50%';
    } else if (element.type === 'line') {
      // 線條元素渲染
      const lineWidth = element.lineWidth || 1;
      const lineColor = element.lineColor || '#000';

      // 清除默認的邊框和背景
      elementDiv.style.border = 'none';
      elementDiv.style.backgroundColor = 'transparent';
      elementDiv.style.overflow = 'visible'; // 確保線條不會被裁剪

      // 使用線條的方向來決定如何渲染

      // 創建一個簡單的線條元素
      const lineElement = document.createElement('div');
      lineElement.style.position = 'absolute';
      lineElement.style.width = '100%';
      lineElement.style.height = '100%';
      lineElement.style.backgroundColor = 'transparent';

      // 使用邊框模擬線條
      if (Math.abs(element.width) > Math.abs(element.height)) {
        // 水平線
        lineElement.style.borderTop = `${lineWidth}px solid ${lineColor}`;
        lineElement.style.top = '50%';
        lineElement.style.transform = 'translateY(-50%)';
      } else {
        // 垂直線
        lineElement.style.borderLeft = `${lineWidth}px solid ${lineColor}`;
        lineElement.style.left = '50%';
        lineElement.style.transform = 'translateX(-50%)';
      }

      elementDiv.appendChild(lineElement);
    } else if (element.type === 'image') {
      // 圖像元素渲染
      elementDiv.style.backgroundColor = 'transparent';
      elementDiv.style.border = 'none';

      // 保存圖片 URL 到元素屬性中，以便後續渲染時使用
      if (element.imageUrl) {
        elementDiv.setAttribute('data-image-url', element.imageUrl);
        console.log(`圖片元素設置 URL: ${element.imageUrl}`);
      } else {
        console.log(`圖片元素沒有 URL`);
      }

      elementDiv.textContent = '[圖片]';
    } else if (element.type === 'qr-code') {
      // QR Code 元素渲染 - 使用真實的 QR Code 生成
      const { renderQRCode } = require('../utils/qrCodeRenderer');
      await renderQRCode(elementDiv, element, sampleDataByIndex, dataFields);
    } else if (element.type === 'barcode') {
      // 條碼元素渲染 - 使用真實的條碼生成
      const { renderBarcode } = require('../utils/barcodeRenderer');
      await renderBarcode(elementDiv, element, sampleDataByIndex, dataFields);
    } else if (element.type === 'icon') {
      // 圖標元素渲染 - 使用 Lucide SVG 檔案
      const { renderIconSvg } = require('../utils/iconRenderer');

      // 計算圖標參數 - 與前端保持一致
      const iconType = element.iconType || 'circle';
      const iconColor = element.lineColor || '#000';
      const iconSize = Math.min(element.width, element.height) * 0.8;
      const strokeWidth = element.lineWidth || 2;

      // 生成 SVG 內容
      const iconSvg = renderIconSvg(iconType, {
        size: iconSize,
        color: iconColor,
        strokeWidth: strokeWidth
      });

      // 設置容器樣式 - 與前端完全一致
      elementDiv.style.border = 'none';
      elementDiv.style.backgroundColor = 'transparent';
      elementDiv.style.display = 'flex';
      elementDiv.style.justifyContent = 'center';
      elementDiv.style.alignItems = 'center';
      elementDiv.style.overflow = 'visible';

      // 直接插入 SVG 內容
      elementDiv.innerHTML = iconSvg;
    } else {
      // 未知元素類型，使用通用樣式
      elementDiv.style.border = '1px dashed #999';
      elementDiv.style.display = 'flex';
      elementDiv.style.justifyContent = 'center';
      elementDiv.style.alignItems = 'center';
      elementDiv.textContent = element.type || '未知元素';
    }

    // 將元素添加到畫布
    elementsContainer.appendChild(elementDiv);
  }
}

/**
 * 處理綁定文字元素，使其顯示實際數據
 * @param {Document} document DOM文檔
 * @returns {Array} 原始樣式信息數組，用於後續還原
 */
function processTextBindings(document) {
  const originalStyles = [];

  try {
    // 找到所有文字元素，包括單行和多行文字元素
    const textElements = document.querySelectorAll('[data-element-type="text"], [data-element-type="multiline-text"]');
    console.log('找到文字元素數量:', textElements.length);

    // 找到所有綁定數據的文字元素
    const boundTextElements = document.querySelectorAll('[data-has-binding="true"]');
    console.log('找到綁定數據的文字元素數量:', boundTextElements.length);

    // 處理每個文字元素
    textElements.forEach(element => {
      // 找到文字容器
      const textContainer = element.querySelector('.text-element-content');
      if (textContainer) {
        // 獲取元素的原始內容
        const originalContent = element.getAttribute('data-original-content') || 'TEXT';

        // 存儲原始樣式
        const styleInfo = {
          element,
          textContainer,
          backgroundColor: textContainer.style.backgroundColor,
          border: textContainer.style.border,
          originalContent
        };

        // 移除綁定標記樣式
        textContainer.style.backgroundColor = 'transparent';
        textContainer.style.border = 'none';

        // 將樣式信息添加到數組
        originalStyles.push(styleInfo);
      }
    });
  } catch (error) {
    console.error('處理綁定元素時出錯:', error);
  }

  return originalStyles;
}

/**
 * 恢復綁定元素的原始樣式和內容
 * @param {Array} originalStyles 原始樣式信息數組
 */
function restoreTextBindings(originalStyles) {
  console.log('恢復綁定數據的文字元素原始樣式:', originalStyles.length);
  originalStyles.forEach(styleInfo => {
    // 恢復樣式
    styleInfo.textContainer.style.backgroundColor = styleInfo.backgroundColor;
    styleInfo.textContainer.style.border = styleInfo.border;
  });
}

/**
 * 渲染畫布為圖像
 * @param {HTMLElement} containerRef 畫布容器
 * @param {number} canvasWidth 畫布寬度
 * @param {number} canvasHeight 畫布高度
 * @param {Object} sampleDataByIndex 數據樣本
 * @param {Array} dataFields 資料欄位數據，用於獲取前綴
 * @param {number} scale 渲染縮放比例，用於提高畫質
 * @returns {Promise<HTMLCanvasElement>} 渲染後的畫布
 */
async function renderCanvasToImage(containerRef, canvasWidth, canvasHeight, sampleDataByIndex = {}, dataFields = [], scale = 2) {
  try {
    console.log(`渲染畫布為圖像，原始尺寸: ${canvasWidth}x${canvasHeight}，縮放比例: ${scale}`);

    // 創建高解析度畫布 (放大版本)
    const highResCanvas = createCanvas(canvasWidth * scale, canvasHeight * scale);
    const highResCtx = highResCanvas.getContext('2d');

    // 設置背景色為白色
    highResCtx.fillStyle = '#FFFFFF';
    highResCtx.fillRect(0, 0, canvasWidth * scale, canvasHeight * scale);

    // 設置縮放
    highResCtx.scale(scale, scale);

    // 繪製容器中的元素到畫布
    if (containerRef) {
      // 獲取容器中的所有元素
      const elements = containerRef.querySelectorAll('[data-element-type]');
      console.log(`找到 ${elements.length} 個元素需要渲染`);

      // 創建一個數組來存儲所有的渲染任務
      const renderTasks = [];

      // 遍歷所有元素，創建渲染任務
      for (const element of elements) {
        const elementType = element.getAttribute('data-element-type');
        const x = parseInt(element.style.left, 10) || 0;
        const y = parseInt(element.style.top, 10) || 0;
        const width = parseInt(element.style.width, 10) || 0;
        const height = parseInt(element.style.height, 10) || 0;

        // 讀取元素的旋轉角度
        const transform = element.style.transform || '';
        const rotationMatch = transform.match(/rotate\(([^)]+)deg\)/);
        const rotation = rotationMatch ? parseFloat(rotationMatch[1]) : 0;

        // 根據元素類型創建不同的渲染任務
        if (elementType === 'rectangle' || elementType === 'square') {
          // 繪製矩形
          renderTasks.push(() => {
            // 讀取元素的樣式屬性
            const borderStyle = element.style.border || '1px solid #000000';
            const borderMatch = borderStyle.match(/(\d+(?:\.\d+)?)px\s+solid\s+(.+)/);
            const lineWidth = borderMatch ? parseFloat(borderMatch[1]) : 1;
            const borderColor = borderMatch ? borderMatch[2] : '#000000';
            const backgroundColor = element.style.backgroundColor || 'transparent';

            highResCtx.save(); // 保存當前狀態

            if (rotation !== 0) {
              // 計算旋轉中心點
              const centerX = x + width / 2;
              const centerY = y + height / 2;

              // 移動到旋轉中心點
              highResCtx.translate(centerX, centerY);
              // 應用旋轉
              highResCtx.rotate((rotation * Math.PI) / 180);
              // 移回原點，但現在是相對於旋轉後的坐標系
              highResCtx.translate(-centerX, -centerY);
            }

            // 填充背景色（如果不是透明）
            if (backgroundColor !== 'transparent' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
              highResCtx.fillStyle = backgroundColor;
              highResCtx.fillRect(x, y, width, height);
            }

            // 繪製邊框
            highResCtx.strokeStyle = borderColor;
            highResCtx.lineWidth = lineWidth;
            highResCtx.strokeRect(x, y, width, height);

            highResCtx.restore(); // 恢復狀態
          });
        } else if (elementType === 'circle' || elementType === 'ellipse') {
          // 繪製圓形/橢圓形
          renderTasks.push(() => {
            // 讀取元素的樣式屬性
            const borderStyle = element.style.border || '1px solid #000000';
            const borderMatch = borderStyle.match(/(\d+(?:\.\d+)?)px\s+solid\s+(.+)/);
            const lineWidth = borderMatch ? parseFloat(borderMatch[1]) : 1;
            const borderColor = borderMatch ? borderMatch[2] : '#000000';
            const backgroundColor = element.style.backgroundColor || 'transparent';

            highResCtx.save(); // 保存當前狀態

            if (rotation !== 0) {
              // 計算旋轉中心點
              const centerX = x + width / 2;
              const centerY = y + height / 2;

              // 移動到旋轉中心點
              highResCtx.translate(centerX, centerY);
              // 應用旋轉
              highResCtx.rotate((rotation * Math.PI) / 180);
              // 移回原點，但現在是相對於旋轉後的坐標系
              highResCtx.translate(-centerX, -centerY);
            }

            // 繪製橢圓路徑
            highResCtx.beginPath();
            highResCtx.ellipse(x + width / 2, y + height / 2, width / 2, height / 2, 0, 0, 2 * Math.PI);

            // 填充背景色（如果不是透明）
            if (backgroundColor !== 'transparent' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
              highResCtx.fillStyle = backgroundColor;
              highResCtx.fill();
            }

            // 繪製邊框
            highResCtx.strokeStyle = borderColor;
            highResCtx.lineWidth = lineWidth;
            highResCtx.stroke();

            highResCtx.restore(); // 恢復狀態
          });
        } else if (elementType === 'line') {
          // 繪製線條
          renderTasks.push(() => {
            // 線條元素的樣式是設置在子元素上的，需要從子元素讀取
            const lineElement = element.querySelector('div');
            let lineWidth = 1;
            let lineColor = '#000000';

            if (lineElement) {
              // 檢查是水平線還是垂直線
              if (lineElement.style.borderTop) {
                const borderMatch = lineElement.style.borderTop.match(/(\d+(?:\.\d+)?)px\s+solid\s+(.+)/);
                if (borderMatch) {
                  lineWidth = parseFloat(borderMatch[1]);
                  lineColor = borderMatch[2];
                }
              } else if (lineElement.style.borderLeft) {
                const borderMatch = lineElement.style.borderLeft.match(/(\d+(?:\.\d+)?)px\s+solid\s+(.+)/);
                if (borderMatch) {
                  lineWidth = parseFloat(borderMatch[1]);
                  lineColor = borderMatch[2];
                }
              }
            }

            highResCtx.save(); // 保存當前狀態

            if (rotation !== 0) {
              // 計算旋轉中心點
              const centerX = x + width / 2;
              const centerY = y + height / 2;

              // 移動到旋轉中心點
              highResCtx.translate(centerX, centerY);
              // 應用旋轉
              highResCtx.rotate((rotation * Math.PI) / 180);
              // 移回原點，但現在是相對於旋轉後的坐標系
              highResCtx.translate(-centerX, -centerY);
            }

            highResCtx.strokeStyle = lineColor;
            highResCtx.lineWidth = lineWidth;
            highResCtx.beginPath();
            highResCtx.moveTo(x, y);
            highResCtx.lineTo(x + width, y + height);
            highResCtx.stroke();

            highResCtx.restore(); // 恢復狀態
          });
        } else if (elementType === 'text' || elementType === 'multiline-text') {
          // 繪製文字
          const textContainer = element.querySelector('.text-element-content');
          if (textContainer) {
            const text = textContainer.textContent || '';
            const fontSize = parseInt(textContainer.style.fontSize, 10) || 12;
            const fontColor = textContainer.style.color || '#000000';
            const fontFamily = textContainer.style.fontFamily || 'Arial';

            // 檢查是否有綁定數據
            const hasBinding = element.getAttribute('data-has-binding') === 'true';
            const fieldId = element.getAttribute('data-field-id');
            const dataIndex = parseInt(element.getAttribute('data-index') || '0', 10);

            renderTasks.push(() => {
              // 如果有綁定數據，嘗試從 sampleDataByIndex 中獲取
              let displayText = text;
              if (hasBinding && fieldId) {
                if (sampleDataByIndex[dataIndex] && sampleDataByIndex[dataIndex][fieldId] !== undefined) {
                  displayText = String(sampleDataByIndex[dataIndex][fieldId]);
                  console.log(`渲染文字元素: 找到綁定數據 dataIndex=${dataIndex}, fieldId=${fieldId}, value=${displayText}`);
                } else {
                  console.log(`渲染文字元素: 未找到綁定數據 dataIndex=${dataIndex}, fieldId=${fieldId}, 使用默認文字`);
                }

                // 檢查是否需要顯示前綴
                const showPrefix = element.getAttribute('data-show-prefix') === 'true';
                if (showPrefix) {
                  // 從 dataFields 中查找欄位的前綴
                  let prefix = '';
                  const field = dataFields.find(f => f.id === fieldId);
                  if (field && field.prefix) {
                    prefix = field.prefix;
                  }
                  // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
                  displayText = prefix ? `${prefix}: ${displayText}` : `: ${displayText}`;
                }
              }

              highResCtx.save(); // 保存當前狀態

              if (rotation !== 0) {
                // 計算旋轉中心點
                const centerX = x + width / 2;
                const centerY = y + height / 2;

                // 移動到旋轉中心點
                highResCtx.translate(centerX, centerY);
                // 應用旋轉
                highResCtx.rotate((rotation * Math.PI) / 180);
                // 移回原點，但現在是相對於旋轉後的坐標系
                highResCtx.translate(-centerX, -centerY);
              }

              highResCtx.fillStyle = fontColor;
              highResCtx.font = `${fontSize}px ${fontFamily}`;
              highResCtx.fillText(displayText, x, y + fontSize);

              highResCtx.restore(); // 恢復狀態
            });
          }
        } else if (elementType === 'image') {
          // 獲取圖片 URL
          const imageUrl = element.getAttribute('data-image-url');

          if (imageUrl) {
            // 創建一個異步的渲染任務
            renderTasks.push(async () => {
              try {
                // 處理圖片 URL，確保能正確載入
                const processedImageUrl = processImageUrl(imageUrl);
                console.log(`嘗試加載圖片: ${imageUrl} -> ${processedImageUrl}`);
                // 使用 loadImage 異步加載圖片
                const image = await loadImage(processedImageUrl);

                // 計算圖片的縮放比例，保持原始比例
                const imgRatio = image.width / image.height;
                const boxRatio = width / height;

                let drawWidth, drawHeight, offsetX, offsetY;

                if (imgRatio > boxRatio) {
                  // 圖片較寬，以寬度為基準
                  drawWidth = width;
                  drawHeight = width / imgRatio;
                  offsetX = 0;
                  offsetY = (height - drawHeight) / 2;
                } else {
                  // 圖片較高，以高度為基準
                  drawHeight = height;
                  drawWidth = height * imgRatio;
                  offsetX = (width - drawWidth) / 2;
                  offsetY = 0;
                }

                highResCtx.save(); // 保存當前狀態

                if (rotation !== 0) {
                  // 計算旋轉中心點
                  const centerX = x + width / 2;
                  const centerY = y + height / 2;

                  // 移動到旋轉中心點
                  highResCtx.translate(centerX, centerY);
                  // 應用旋轉
                  highResCtx.rotate((rotation * Math.PI) / 180);
                  // 移回原點，但現在是相對於旋轉後的坐標系
                  highResCtx.translate(-centerX, -centerY);
                }

                // 繪製圖片
                highResCtx.drawImage(image, x + offsetX, y + offsetY, drawWidth, drawHeight);
                console.log(`圖片加載成功: ${imageUrl}`);

                highResCtx.restore(); // 恢復狀態
              } catch (error) {
                console.error(`加載圖片失敗: ${imageUrl}`, error);

                highResCtx.save(); // 保存當前狀態

                if (rotation !== 0) {
                  // 計算旋轉中心點
                  const centerX = x + width / 2;
                  const centerY = y + height / 2;

                  // 移動到旋轉中心點
                  highResCtx.translate(centerX, centerY);
                  // 應用旋轉
                  highResCtx.rotate((rotation * Math.PI) / 180);
                  // 移回原點，但現在是相對於旋轉後的坐標系
                  highResCtx.translate(-centerX, -centerY);
                }

                // 繪製錯誤佔位符
                highResCtx.fillStyle = '#ffeeee';
                highResCtx.fillRect(x, y, width, height);
                highResCtx.strokeStyle = '#ff0000';
                highResCtx.strokeRect(x, y, width, height);

                // 在佔位符中心繪製錯誤訊息
                highResCtx.fillStyle = '#ff0000';
                highResCtx.font = '12px sans-serif';
                highResCtx.textAlign = 'center';
                highResCtx.textBaseline = 'middle';
                highResCtx.fillText('圖片加載失敗', x + width / 2, y + height / 2);

                // 重置文字對齊方式
                highResCtx.textAlign = 'start';
                highResCtx.textBaseline = 'alphabetic';

                highResCtx.restore(); // 恢復狀態
              }
            });
          } else {
            // 沒有圖片 URL，繪製一個佔位符
            renderTasks.push(() => {
              highResCtx.save(); // 保存當前狀態

              if (rotation !== 0) {
                // 計算旋轉中心點
                const centerX = x + width / 2;
                const centerY = y + height / 2;

                // 移動到旋轉中心點
                highResCtx.translate(centerX, centerY);
                // 應用旋轉
                highResCtx.rotate((rotation * Math.PI) / 180);
                // 移回原點，但現在是相對於旋轉後的坐標系
                highResCtx.translate(-centerX, -centerY);
              }

              highResCtx.fillStyle = '#f0f0f0';
              highResCtx.fillRect(x, y, width, height);
              highResCtx.strokeStyle = '#000000';
              highResCtx.strokeRect(x, y, width, height);

              // 在佔位符中心繪製 "無圖片" 文字
              highResCtx.fillStyle = '#000000';
              highResCtx.font = '12px sans-serif';
              highResCtx.textAlign = 'center';
              highResCtx.textBaseline = 'middle';
              highResCtx.fillText('無圖片', x + width / 2, y + height / 2);

              // 重置文字對齊方式
              highResCtx.textAlign = 'start';
              highResCtx.textBaseline = 'alphabetic';

              highResCtx.restore(); // 恢復狀態
            });
          }
        } else if (elementType === 'qr-code' || elementType === 'barcode') {
          // 繪製 QR Code 或條碼 - 需要渲染容器背景、邊框和內容圖像
          renderTasks.push(async () => {
            try {
              highResCtx.save(); // 保存當前狀態

              if (rotation !== 0) {
                // 計算旋轉中心點
                const centerX = x + width / 2;
                const centerY = y + height / 2;

                // 移動到旋轉中心點
                highResCtx.translate(centerX, centerY);
                // 應用旋轉
                highResCtx.rotate((rotation * Math.PI) / 180);
                // 移回原點，但現在是相對於旋轉後的坐標系
                highResCtx.translate(-centerX, -centerY);
              }

              // 獲取元素的樣式屬性
              const backgroundColor = element.style.backgroundColor || '#FFFFFF';
              const borderStyle = element.style.border || '';

              // 繪製容器背景
              if (backgroundColor !== 'transparent') {
                highResCtx.fillStyle = backgroundColor;
                highResCtx.fillRect(x, y, width, height);
              }

              // 繪製邊框
              if (borderStyle && borderStyle !== 'none') {
                const borderMatch = borderStyle.match(/(\d+(?:\.\d+)?)px\s+solid\s+(.+)/);
                if (borderMatch) {
                  const borderWidth = parseFloat(borderMatch[1]);
                  const borderColor = borderMatch[2];

                  highResCtx.strokeStyle = borderColor;
                  highResCtx.lineWidth = borderWidth;
                  highResCtx.strokeRect(x, y, width, height);
                }
              }

              // 獲取並繪製 QR code/barcode 圖像
              const backgroundImage = element.style.backgroundImage;
              if (backgroundImage && backgroundImage.startsWith('url(')) {
                // 提取 data URL
                const dataUrlMatch = backgroundImage.match(/url\(["']?(data:image\/[^"']+)["']?\)/);
                if (dataUrlMatch && dataUrlMatch[1]) {
                  const dataUrl = dataUrlMatch[1];
                  console.log(`渲染 ${elementType} 元素，從 backgroundImage 加載圖像`);

                  // 使用 loadImage 加載 data URL
                  const image = await loadImage(dataUrl);

                  // 計算內容區域（考慮 8px padding）
                  const padding = 8;
                  const contentX = x + padding;
                  const contentY = y + padding;
                  const contentWidth = width - padding * 2;
                  const contentHeight = height - padding * 2;

                  // 確保內容區域有效
                  if (contentWidth > 0 && contentHeight > 0) {
                    // 繪製圖像到內容區域，使用 contain 模式
                    const imageAspect = image.width / image.height;
                    const contentAspect = contentWidth / contentHeight;

                    let drawWidth, drawHeight, drawX, drawY;

                    if (imageAspect > contentAspect) {
                      // 圖像較寬，以寬度為基準
                      drawWidth = contentWidth;
                      drawHeight = contentWidth / imageAspect;
                      drawX = contentX;
                      drawY = contentY + (contentHeight - drawHeight) / 2;
                    } else {
                      // 圖像較高，以高度為基準
                      drawHeight = contentHeight;
                      drawWidth = contentHeight * imageAspect;
                      drawX = contentX + (contentWidth - drawWidth) / 2;
                      drawY = contentY;
                    }

                    highResCtx.drawImage(image, drawX, drawY, drawWidth, drawHeight);
                    console.log(`${elementType} 元素渲染完成: 容器(${x},${y} ${width}x${height}) 內容(${drawX},${drawY} ${drawWidth}x${drawHeight})`);
                  }
                } else {
                  console.warn(`${elementType} 元素的 backgroundImage 格式不正確:`, backgroundImage);
                  // 繪製錯誤文字
                  highResCtx.fillStyle = '#ff0000';
                  highResCtx.font = '12px sans-serif';
                  highResCtx.textAlign = 'center';
                  highResCtx.textBaseline = 'middle';
                  highResCtx.fillText(`${elementType} 錯誤`, x + width / 2, y + height / 2);
                  highResCtx.textAlign = 'start';
                  highResCtx.textBaseline = 'alphabetic';
                }
              } else {
                console.warn(`${elementType} 元素沒有 backgroundImage 樣式`);
                // 繪製空白佔位符文字
                highResCtx.fillStyle = '#999999';
                highResCtx.font = '12px sans-serif';
                highResCtx.textAlign = 'center';
                highResCtx.textBaseline = 'middle';
                highResCtx.fillText(`無 ${elementType}`, x + width / 2, y + height / 2);
                highResCtx.textAlign = 'start';
                highResCtx.textBaseline = 'alphabetic';
              }

              highResCtx.restore(); // 恢復狀態
            } catch (error) {
              console.error(`渲染 ${elementType} 元素時出錯:`, error);
              // 繪製錯誤佔位符
              highResCtx.save();

              if (rotation !== 0) {
                const centerX = x + width / 2;
                const centerY = y + height / 2;
                highResCtx.translate(centerX, centerY);
                highResCtx.rotate((rotation * Math.PI) / 180);
                highResCtx.translate(-centerX, -centerY);
              }

              highResCtx.fillStyle = '#ffeeee';
              highResCtx.fillRect(x, y, width, height);
              highResCtx.strokeStyle = '#ff0000';
              highResCtx.lineWidth = 1;
              highResCtx.strokeRect(x, y, width, height);

              highResCtx.fillStyle = '#ff0000';
              highResCtx.font = '12px sans-serif';
              highResCtx.textAlign = 'center';
              highResCtx.textBaseline = 'middle';
              highResCtx.fillText(`${elementType} 錯誤`, x + width / 2, y + height / 2);

              highResCtx.textAlign = 'start';
              highResCtx.textBaseline = 'alphabetic';
              highResCtx.restore();
            }
          });
        } else if (elementType === 'icon') {
          // 繪製圖標 - 模擬前端 html2canvas 的特殊處理邏輯
          renderTasks.push(() => {
            // 獲取圖標的 SVG 內容
            let iconElement = element.querySelector('svg');

            if (iconElement) {
              console.log('找到 SVG 元素，開始使用新的路徑渲染器渲染圖標');

              // 模擬前端 html2canvas 的 icon 處理邏輯
              // 前端會重新計算 icon 的位置和大小，我們需要在後端也這樣做

              // 計算 icon 在容器中的實際大小（與前端邏輯一致）
              const containerWidth = width;
              const containerHeight = height;
              const iconSize = Math.min(containerWidth, containerHeight) * 0.8;

              // 計算 icon 在容器中的居中偏移（與前端邏輯一致）
              const iconOffsetX = (containerWidth - iconSize) / 2;
              const iconOffsetY = (containerHeight - iconSize) / 2;

              // 計算 icon 的實際渲染位置（與前端邏輯一致）
              const actualIconX = x + iconOffsetX;
              const actualIconY = y + iconOffsetY;
              const actualIconWidth = iconSize;
              const actualIconHeight = iconSize;

              console.log(`Icon 位置調整: 容器(${x}, ${y}, ${width}x${height}) → 實際(${actualIconX}, ${actualIconY}, ${actualIconWidth}x${actualIconHeight})`);

              // 獲取 SVG 的 viewBox
              const viewBox = iconElement.getAttribute('viewBox') || '0 0 24 24';
              const [, , vbWidth, vbHeight] = viewBox.split(' ').map(Number);

              // 設置縮放比例，將 SVG 坐標系映射到實際 icon 尺寸
              const scaleX = actualIconWidth / vbWidth;
              const scaleY = actualIconHeight / vbHeight;

              // 獲取 SVG 樣式
              const strokeColor = iconElement.getAttribute('stroke') || '#000';
              const strokeWidth = parseFloat(iconElement.getAttribute('stroke-width') || '2');
              const fillColor = iconElement.getAttribute('fill') || 'none';

              highResCtx.save(); // 保存當前狀態

              if (rotation !== 0) {
                // 計算旋轉中心點（使用原始容器的中心）
                const centerX = x + width / 2;
                const centerY = y + height / 2;

                // 移動到旋轉中心點
                highResCtx.translate(centerX, centerY);
                // 應用旋轉
                highResCtx.rotate((rotation * Math.PI) / 180);
                // 移回原點，但現在是相對於旋轉後的坐標系
                highResCtx.translate(-centerX, -centerY);
              }

              // 移動到實際 icon 位置並應用縮放
              highResCtx.translate(actualIconX, actualIconY);
              highResCtx.scale(scaleX, scaleY);

              // 使用簡化的 SVG 路徑渲染器
              const { renderSvgElement } = require('../utils/simpleSvgRenderer');

              renderSvgElement(highResCtx, iconElement, {
                scaleX: 1, // 已經在上面應用了縮放
                scaleY: 1,
                offsetX: 0,
                offsetY: 0,
                strokeColor: strokeColor,
                strokeWidth: strokeWidth,
                fillColor: fillColor
              });

              highResCtx.restore(); // 恢復狀態
              console.log('✓ 圖標渲染到畫布完成（使用新的路徑渲染器，已應用前端位置調整邏輯）');
            } else {
              console.log('未找到 SVG 元素，繪製佔位符');

              // 如果沒有找到 SVG，繪製一個佔位符
              highResCtx.save();

              if (rotation !== 0) {
                const centerX = x + width / 2;
                const centerY = y + height / 2;
                highResCtx.translate(centerX, centerY);
                highResCtx.rotate((rotation * Math.PI) / 180);
                highResCtx.translate(-centerX, -centerY);
              }

              highResCtx.fillStyle = '#f0f0f0';
              highResCtx.fillRect(x, y, width, height);
              highResCtx.strokeStyle = '#000000';
              highResCtx.strokeRect(x, y, width, height);

              highResCtx.fillStyle = '#000000';
              highResCtx.font = `${12 * scale}px sans-serif`;
              highResCtx.textAlign = 'center';
              highResCtx.textBaseline = 'middle';
              highResCtx.fillText('Icon', x + width / 2, y + height / 2);

              highResCtx.textAlign = 'start';
              highResCtx.textBaseline = 'alphabetic';
              highResCtx.restore();
            }
          });
        }
      }

      // 執行所有渲染任務
      for (const task of renderTasks) {
        // 檢查任務是否是異步的
        const result = task();
        if (result instanceof Promise) {
          await result;
        }
      }
    }

    // 創建最終尺寸的畫布 (縮小版本)
    console.log(`創建最終尺寸的畫布: ${canvasWidth}x${canvasHeight}`);
    const finalCanvas = createCanvas(canvasWidth, canvasHeight);
    const finalCtx = finalCanvas.getContext('2d');

    // 使用高質量的縮放算法
    // 注意: node-canvas 可能不支持 imageSmoothingQuality，但我們仍然設置它
    finalCtx.imageSmoothingEnabled = true;
    try {
      finalCtx.imageSmoothingQuality = 'high';
    } catch (e) {
      console.log('imageSmoothingQuality 不被支持，使用默認值');
    }

    // 將高解析度畫布縮小到最終尺寸
    finalCtx.drawImage(
      highResCanvas,
      0, 0, canvasWidth * scale, canvasHeight * scale,
      0, 0, canvasWidth, canvasHeight
    );

    console.log(`畫布渲染完成，從 ${canvasWidth * scale}x${canvasHeight * scale} 縮小到 ${canvasWidth}x${canvasHeight}`);
    return finalCanvas;
  } catch (error) {
    console.error('渲染畫布為圖像時出錯:', error);
    return null;
  }
}

/**
 * 應用圖像效果
 * @param {HTMLCanvasElement} canvas 原始畫布
 * @param {string} effectType 效果類型
 * @param {number} threshold 閾值
 * @returns {Promise<HTMLCanvasElement>} 處理後的畫布
 */
async function applyImageEffect(canvas, effectType, threshold) {
  try {
    // 創建一個新畫布
    const resultCanvas = createCanvas(canvas.width, canvas.height);
    const ctx = resultCanvas.getContext('2d');

    // 繪製原始畫布
    ctx.drawImage(canvas, 0, 0);

    // 根據效果類型應用不同的處理
    switch (effectType) {
      case 'blackAndWhite':
        // 黑白效果 (二值化)
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
          // 計算灰度值 (加權平均法)
          const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
          // 二值化處理
          const color = gray > threshold ? 255 : 0;
          data[i] = data[i + 1] = data[i + 2] = color;
        }

        ctx.putImageData(imageData, 0, 0);
        break;

      case 'grayscale':
        // 灰度效果
        const grayImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const grayData = grayImageData.data;

        for (let i = 0; i < grayData.length; i += 4) {
          // 計算灰度值 (加權平均法)
          const gray = 0.299 * grayData[i] + 0.587 * grayData[i + 1] + 0.114 * grayData[i + 2];
          grayData[i] = grayData[i + 1] = grayData[i + 2] = gray;
        }

        ctx.putImageData(grayImageData, 0, 0);
        break;

      case 'inverted':
        // 反轉效果
        const invertedImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const invertedData = invertedImageData.data;

        for (let i = 0; i < invertedData.length; i += 4) {
          invertedData[i] = 255 - invertedData[i];         // R
          invertedData[i + 1] = 255 - invertedData[i + 1]; // G
          invertedData[i + 2] = 255 - invertedData[i + 2]; // B
        }

        ctx.putImageData(invertedImageData, 0, 0);
        break;

      case 'dithering':
        // 抖動效果 (Floyd-Steinberg 抖動算法)
        const ditherImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const ditherData = ditherImageData.data;
        const width = canvas.width;

        // 先轉換為灰度
        for (let i = 0; i < ditherData.length; i += 4) {
          const gray = 0.299 * ditherData[i] + 0.587 * ditherData[i + 1] + 0.114 * ditherData[i + 2];
          ditherData[i] = ditherData[i + 1] = ditherData[i + 2] = gray;
        }

        // 應用 Floyd-Steinberg 抖動
        for (let y = 0; y < canvas.height; y++) {
          for (let x = 0; x < width; x++) {
            const idx = (y * width + x) * 4;
            const oldPixel = ditherData[idx];
            const newPixel = oldPixel > threshold ? 255 : 0;
            ditherData[idx] = ditherData[idx + 1] = ditherData[idx + 2] = newPixel;

            const error = oldPixel - newPixel;

            // 分配誤差到相鄰像素
            if (x + 1 < width) {
              ditherData[idx + 4] += error * 7 / 16;
            }
            if (y + 1 < canvas.height) {
              if (x > 0) {
                ditherData[idx + 4 * (width - 1)] += error * 3 / 16;
              }
              ditherData[idx + 4 * width] += error * 5 / 16;
              if (x + 1 < width) {
                ditherData[idx + 4 * (width + 1)] += error * 1 / 16;
              }
            }
          }
        }

        ctx.putImageData(ditherImageData, 0, 0);
        break;

      default:
        // 不應用任何效果
        break;
    }

    return resultCanvas;
  } catch (error) {
    console.error('應用圖像效果時出錯:', error);
    return canvas; // 如果出錯，返回原始畫布
  }
}

module.exports = {
  generatePreviewFromService,
  batchGeneratePreviewsFromService,
  checkPreviewServiceHealth,
  regeneratePreviewBeforeSend
};
