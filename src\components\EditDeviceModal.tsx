import React, { useState, useEffect } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { Device } from '../types/device';
import { updateDevice, checkMacAddressExists } from '../utils/api/deviceApi';

interface EditDeviceModalProps {
  isOpen: boolean;
  device: Device | null;
  storeId?: string; // 當前選中的門店ID
  onClose: () => void;
  onSuccess: () => void;
}

export function EditDeviceModal({ isOpen, device, storeId, onClose, onSuccess }: EditDeviceModalProps) {
  const [formData, setFormData] = useState<Partial<Device>>({
    note: '',
    code: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 當設備數據變更時更新表單
  useEffect(() => {
    if (device) {
      setFormData({
        note: device.note || '',
        code: device.code || '',
      });
      setErrors({});
    }
  }, [device]);

  if (!isOpen || !device) return null;
  const validateForm = () => {
    // 由於只有備註和編號，且沒有特殊驗證要求，所以不需要驗證
    return true;
  };
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const isValid = validateForm();
      if (!isValid) {
        setIsSubmitting(false);
        return;
      }

      // 只更新備註和編號，傳遞門店ID
      await updateDevice(device._id || '', {
        note: formData.note,
        code: formData.code,
        updatedAt: new Date()
      }, storeId);

      onSuccess();
      onClose();
    } catch (error) {
      console.error('更新設備失敗:', error);
      setErrors(prev => ({
        ...prev,
        submit: '更新設備失敗，請稍後再試'
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">編輯設備</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        {errors.submit && (
          <div className="p-4 bg-red-50 border-l-4 border-red-500 text-red-700 flex items-center">
            <AlertCircle className="w-5 h-5 mr-2" />
            <span>{errors.submit}</span>
          </div>
        )}        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            {/* MAC 地址 (唯讀) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                MAC 地址
              </label>
              <input
                type="text"
                value={device.macAddress}
                readOnly
                className="w-full p-2 border border-gray-300 rounded-md bg-gray-100"
              />
            </div>

            {/* 編號 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                編號
              </label>
              <input
                type="text"
                name="code"
                value={formData.code}
                onChange={handleChange}
                placeholder="設備編號"
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* 備註 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                備註
              </label>
              <textarea
                name="note"
                value={formData.note}
                onChange={handleChange}
                placeholder="輸入備註"
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={isSubmitting}
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
              disabled={isSubmitting}
            >
              {isSubmitting ? '處理中...' : '更新'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
