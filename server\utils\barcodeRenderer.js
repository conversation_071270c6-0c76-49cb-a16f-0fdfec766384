const JsBarcode = require('jsbarcode');
const { createCanvas } = require('canvas');

/**
 * 條碼渲染器
 * 支援多種條碼類型和完整的參數控制
 */

/**
 * 處理條碼的資料綁定
 * @param {Object} element - 元素配置
 * @param {Object} sampleDataByIndex - 按索引組織的範例數據
 * @param {Array} dataFields - 資料欄位定義
 * @returns {string} 處理後的內容
 */
function processDataBinding(element, sampleDataByIndex, dataFields) {
  // 1. 檢查是否有資料綁定
  if (!element.dataBinding || !element.dataBinding.fieldId) {
    return element.codeContent || getSampleValueForBarcodeType(element.barcodeType || 'code128');
  }

  // 2. 獲取綁定的資料欄位
  const field = dataFields.find(f => f.id === element.dataBinding.fieldId);
  if (!field) {
    console.warn(`找不到欄位 ${element.dataBinding.fieldId}`);
    return element.codeContent || 'ERROR: Field not found';
  }

  // 3. 獲取數據值
  const dataIndex = element.dataBinding.dataIndex || 0;
  const sampleData = sampleDataByIndex[dataIndex];
  if (!sampleData || !sampleData[field.id]) {
    console.warn(`找不到數據 ${field.id} at index ${dataIndex}`);
    return field.defaultValue || element.codeContent || getSampleValueForBarcodeType(element.barcodeType || 'code128');
  }

  // 4. 格式驗證和清理
  let content = sampleData[field.id];

  // 根據條碼類型進行驗證
  const validation = validateBarcodeContent(content, element.barcodeType);
  if (!validation.isValid) {
    console.error(`條碼內容驗證失敗: ${validation.error}`);
    return `ERROR: ${validation.error}`;
  }
  content = validation.sanitizedContent || content;

  // 5. 添加前綴（如果需要）
  if (element.dataBinding.displayOptions?.showPrefix && field.prefix) {
    content = field.prefix + content;
  }

  return content;
}

/**
 * 根據條碼類型獲取範例值
 * @param {string} type - 條碼類型
 * @returns {string} 範例值
 */
function getSampleValueForBarcodeType(type) {
  switch (type) {
    case 'ean13':
      return '1234567890123';
    case 'upc-a':
      return '123456789012';
    case 'code39':
      return 'SAMPLE123';
    case 'code93':
      return 'SAMPLE123';
    case 'code128':
    default:
      return 'Sample123';
  }
}

/**
 * 驗證條碼內容格式
 * @param {string} content - 要驗證的內容
 * @param {string} barcodeType - 條碼類型
 * @returns {Object} 驗證結果
 */
function validateBarcodeContent(content, barcodeType = 'code128') {
  if (!content || typeof content !== 'string') {
    return {
      isValid: false,
      error: '內容不能為空'
    };
  }

  // 條碼驗證規則
  const BARCODE_LIMITS = {
    code128: { maxLength: 80, charset: 'ascii' },
    ean13: { length: 13, charset: 'numeric' },
    'upc-a': { length: 12, charset: 'numeric' },
    code39: { maxLength: 43, charset: 'code39' },
    code93: { maxLength: 47, charset: 'ascii' }
  };

  const limits = BARCODE_LIMITS[barcodeType] || BARCODE_LIMITS.code128;

  // 長度檢查
  if (limits.length) {
    // 固定長度檢查
    if (content.length !== limits.length) {
      return {
        isValid: false,
        error: `${barcodeType.toUpperCase()} 需要 ${limits.length} 位數字`
      };
    }
  } else if (limits.maxLength) {
    // 最大長度檢查
    if (content.length > limits.maxLength) {
      return {
        isValid: false,
        error: `內容長度超過限制 (${limits.maxLength} 字符)`
      };
    }
  }

  // 字符集檢查
  switch (limits.charset) {
    case 'numeric':
      if (!/^\d+$/.test(content)) {
        return {
          isValid: false,
          error: '僅支援數字字符',
          sanitizedContent: content.replace(/\D/g, '')
        };
      }
      break;
    case 'ascii':
      if (!/^[\x00-\x7F]*$/.test(content)) {
        return {
          isValid: false,
          error: '僅支援 ASCII 字符',
          sanitizedContent: content.replace(/[^\x00-\x7F]/g, '?')
        };
      }
      break;
    case 'code39':
      // Code39 支援的字符集：A-Z, 0-9, 空格, -, ., $, /, +, %, *
      if (!/^[A-Z0-9 \-.$\/+%*]*$/.test(content.toUpperCase())) {
        return {
          isValid: false,
          error: 'Code39 僅支援 A-Z, 0-9, 空格, -, ., $, /, +, %, * 字符',
          sanitizedContent: content.toUpperCase().replace(/[^A-Z0-9 \-.$\/+%*]/g, '')
        };
      }
      break;
  }

  return {
    isValid: true,
    sanitizedContent: content
  };
}

/**
 * 渲染條碼到 DOM 元素
 * @param {HTMLElement} elementDiv - 目標 DOM 元素
 * @param {Object} element - 元素配置
 * @param {Object} sampleDataByIndex - 範例數據
 * @param {Array} dataFields - 資料欄位定義
 */
async function renderBarcode(elementDiv, element, sampleDataByIndex, dataFields) {
  try {
    // 處理資料綁定
    const content = processDataBinding(element, sampleDataByIndex, dataFields);
    
    // 獲取條碼配置
    const barcodeType = element.barcodeType || 'code128';
    const quietZone = element.quietZone || 10;
    // 條碼顏色不支援透明，如果是透明則使用黑色
    const foregroundColor = (element.lineColor === 'transparent' || !element.lineColor) ? '#000000' : element.lineColor;
    const backgroundColor = element.fillColor || '#FFFFFF';

    // 創建 Canvas
    const canvas = createCanvas(element.width, element.height);

    // 檢查是否顯示文字（預設為 false）
    const showText = element.showText === true;

    // 根據是否顯示文字調整條碼高度
    const barcodeHeight = showText ? element.height - 20 : element.height - 10;

    // 條碼選項
    const barcodeOptions = {
      format: barcodeType.toUpperCase().replace('-', ''),
      width: 2,
      height: barcodeHeight,
      displayValue: showText, // 根據設置決定是否顯示文字
      fontSize: 12,
      textAlign: 'center',
      textPosition: 'bottom',
      textMargin: 2,
      fontOptions: '',
      font: 'monospace',
      background: backgroundColor,
      lineColor: foregroundColor,
      margin: quietZone,
      marginTop: 10,
      marginBottom: showText ? 10 : 5, // 如果不顯示文字，減少底部邊距
      marginLeft: quietZone,
      marginRight: quietZone
    };

    // 生成條碼
    JsBarcode(canvas, content, barcodeOptions);

    // 將 Canvas 內容設置到 DOM 元素
    elementDiv.style.backgroundImage = `url(${canvas.toDataURL()})`;
    elementDiv.style.backgroundSize = 'contain';
    elementDiv.style.backgroundRepeat = 'no-repeat';
    elementDiv.style.backgroundPosition = 'center';

    // 設置元素樣式 - 與前端 BarcodeRenderer.tsx 保持一致
    elementDiv.style.width = `${element.width}px`;
    elementDiv.style.height = `${element.height}px`;
    elementDiv.style.position = 'absolute';
    elementDiv.style.left = `${element.x}px`;
    elementDiv.style.top = `${element.y}px`;

    // 設置容器背景色 - 與前端保持一致
    elementDiv.style.backgroundColor = backgroundColor;

    // 處理邊框 - 與前端 BarcodeRenderer.tsx 保持一致
    if (element.showBorder !== false) {
      const borderWidth = element.lineWidth || 2;
      const borderColor = element.borderColor || '#000000';
      elementDiv.style.border = `${borderWidth}px solid ${borderColor}`;
      elementDiv.style.borderRadius = '4px';
    } else {
      elementDiv.style.border = 'none';
    }

    // 設置容器樣式 - 與前端保持一致
    elementDiv.style.display = 'flex';
    elementDiv.style.flexDirection = 'column';
    elementDiv.style.alignItems = 'center';
    elementDiv.style.justifyContent = 'center';
    elementDiv.style.padding = '8px';
    elementDiv.style.boxSizing = 'border-box';

    if (element.rotation) {
      elementDiv.style.transform = `rotate(${element.rotation}deg)`;
      elementDiv.style.transformOrigin = 'center center';
    }

    console.log(`條碼渲染完成: ${barcodeType}, 內容: ${content}`);

  } catch (error) {
    console.error('條碼渲染失敗:', error);

    // 錯誤時顯示佔位符 - 與前端保持一致的樣式
    elementDiv.style.width = `${element.width}px`;
    elementDiv.style.height = `${element.height}px`;
    elementDiv.style.position = 'absolute';
    elementDiv.style.left = `${element.x}px`;
    elementDiv.style.top = `${element.y}px`;

    // 設置錯誤時的背景色和邊框
    elementDiv.style.backgroundColor = '#ffe6e6'; // 淺紅色背景表示錯誤
    elementDiv.style.border = '1px solid #ff0000'; // 紅色邊框表示錯誤
    elementDiv.style.borderRadius = '4px';

    // 設置容器樣式
    elementDiv.style.display = 'flex';
    elementDiv.style.flexDirection = 'column';
    elementDiv.style.alignItems = 'center';
    elementDiv.style.justifyContent = 'center';
    elementDiv.style.padding = '8px';
    elementDiv.style.boxSizing = 'border-box';
    elementDiv.style.fontSize = '12px';
    elementDiv.style.color = '#ff0000'; // 紅色文字表示錯誤

    if (element.rotation) {
      elementDiv.style.transform = `rotate(${element.rotation}deg)`;
      elementDiv.style.transformOrigin = 'center center';
    }

    elementDiv.textContent = '條碼錯誤';
  }
}

/**
 * 生成條碼預覽圖片（用於 API）
 * @param {Object} options - 條碼選項
 * @returns {Promise<Buffer>} 圖片 Buffer
 */
async function generateBarcodePreview(options) {
  const {
    content,
    barcodeType = 'code128',
    quietZone = 10,
    backgroundColor = '#FFFFFF',
    width = 200,
    height = 100,
    showText = false // 預設不顯示文字
  } = options;

  // 條碼顏色不支援透明，如果是透明則使用黑色
  const foregroundColor = (options.foregroundColor === 'transparent' || !options.foregroundColor) ? '#000000' : options.foregroundColor;

  try {
    // 驗證內容
    const validation = validateBarcodeContent(content, barcodeType);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // 創建 Canvas
    const canvas = createCanvas(width, height);

    // 根據是否顯示文字調整條碼高度
    const barcodeHeight = showText ? height - 20 : height - 10;

    const barcodeOptions = {
      format: barcodeType.toUpperCase().replace('-', ''),
      width: 2,
      height: barcodeHeight,
      displayValue: showText, // 根據設置決定是否顯示文字
      fontSize: 12,
      textAlign: 'center',
      textPosition: 'bottom',
      textMargin: 2,
      fontOptions: '',
      font: 'monospace',
      background: backgroundColor,
      lineColor: foregroundColor,
      margin: quietZone,
      marginTop: 10,
      marginBottom: showText ? 10 : 5, // 如果不顯示文字，減少底部邊距
      marginLeft: quietZone,
      marginRight: quietZone
    };

    // 生成條碼
    JsBarcode(canvas, validation.sanitizedContent || content, barcodeOptions);

    // 返回 Buffer
    return canvas.toBuffer('image/png');

  } catch (error) {
    console.error('條碼預覽生成失敗:', error);
    throw error;
  }
}

module.exports = {
  renderBarcode,
  generateBarcodePreview,
  validateBarcodeContent,
  processDataBinding,
  getSampleValueForBarcodeType
};
