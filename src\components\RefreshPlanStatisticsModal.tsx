import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, BarChart3, TrendingUp, Calendar, Clock, CheckCircle, XCircle, Activity } from 'lucide-react';
import { RefreshPlan } from '../types/refreshPlan';
import { refreshPlanApi, formatDateTime, calculateSuccessRate } from '../services/refreshPlanApi';
import { Store } from '../types/store';
import ExecutionDetailsModal from './ExecutionDetailsModal';

interface RefreshPlanStatisticsModalProps {
  isOpen: boolean;
  onClose: () => void;
  store: Store;
  plan: RefreshPlan;
}

interface StatisticsData {
  overview: {
    totalRuns: number;
    successRuns: number;
    failedRuns: number;
    successRate: string;
    lastRun?: string | null;
    nextRun?: string | null;
  };
  recentExecutions: Array<{
    _id?: string;
    id?: string;
    startTime: string;
    endTime?: string;
    status: 'success' | 'failed' | 'running';
    totalDevices: number;
    successDevices: number;
    failedDevices: number;
    duration?: number;
    result?: any; // 添加 result 字段以支持網關統計
  }>;
  periodStats: {
    period: string;
    totalRuns: number;
    successRuns: number;
    failedRuns: number;
    avgDuration: number;
  };
  deviceStats: {
    totalDevices: number;
    successDevices: number;
    failedDevices: number;
    successRate: number;
  };
}

export const RefreshPlanStatisticsModal: React.FC<RefreshPlanStatisticsModalProps> = ({
  isOpen,
  onClose,
  store,
  plan,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statistics, setStatistics] = useState<StatisticsData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month'>('week');
  const [selectedExecution, setSelectedExecution] = useState<any>(null);
  const [showExecutionDetails, setShowExecutionDetails] = useState(false);

  // 載入統計數據
  const loadStatistics = async () => {
    if (!isOpen) return;

    setLoading(true);
    setError(null);

    try {
      // 調試信息
      console.log('統計組件調試信息:', {
        storeId: store?.id,
        storeName: store?.name,
        planId: plan?._id,
        planName: plan?.name,
        selectedPeriod
      });

      // 驗證必要參數
      if (!store || !store.id) {
        throw new Error('門店信息不完整，無法載入統計數據');
      }

      if (!plan || !plan._id) {
        throw new Error('計畫信息不完整，無法載入統計數據');
      }

      // 獲取計畫統計
      const statsResponse = await refreshPlanApi.getStatistics(store.id, {
        period: selectedPeriod,
        planIds: [plan._id]
      });

      // 獲取最近執行記錄
      const executionsResponse = await refreshPlanApi.getExecutions(store.id, plan._id, {
        page: 1,
        limit: 10
      });

      console.log('統計API響應:', statsResponse); // 調試用

      // 組合統計數據
      const statsData: StatisticsData = {
        overview: {
          totalRuns: plan.statistics?.totalRuns || 0,
          successRuns: plan.statistics?.successRuns || 0,
          failedRuns: plan.statistics?.failedRuns || 0,
          successRate: calculateSuccessRate(
            plan.statistics?.successRuns || 0,
            plan.statistics?.totalRuns || 0
          ),
          lastRun: typeof plan.lastRun === 'string' ? plan.lastRun : (plan.lastRun ? new Date(plan.lastRun).toISOString() : null),
          nextRun: typeof plan.nextRun === 'string' ? plan.nextRun : (plan.nextRun ? new Date(plan.nextRun).toISOString() : null),
        },
        recentExecutions: executionsResponse.success ? executionsResponse.data.executions.map((exec: any) => {
          // 安全地處理可能的對象字段
          const safeExec = {
            ...exec,
            // 確保有正確的字段映射
            totalDevices: Number(exec.result?.totalDevices) || 0,
            successDevices: Number(exec.result?.successDevices) || 0,
            failedDevices: Number(exec.result?.failedDevices) || 0,
            duration: exec.result?.processingTime ? Math.round(Number(exec.result.processingTime) / 1000) : 0,
            // 將 completed 狀態映射為 success
            status: exec.status === 'completed' ? 'success' : exec.status,
            // 確保 startTime 是字符串
            startTime: typeof exec.startTime === 'string' ? exec.startTime : new Date(exec.startTime).toISOString(),
            // 安全處理 result 對象
            result: exec.result && typeof exec.result === 'object' ? exec.result : {}
          };

          // 移除可能導致渲染問題的複雜對象
          Object.keys(safeExec).forEach(key => {
            if (safeExec[key] && typeof safeExec[key] === 'object' && key !== 'result') {
              // 如果是對象但不是 result，嘗試轉換為字符串或移除
              if (Array.isArray(safeExec[key])) {
                // 保留數組
              } else if (safeExec[key].toString && typeof safeExec[key].toString === 'function') {
                safeExec[key] = safeExec[key].toString();
              } else {
                delete safeExec[key];
              }
            }
          });

          return safeExec;
        }) : [],
        periodStats: {
          period: selectedPeriod,
          totalRuns: Number(statsResponse.success ? (statsResponse.data.totalExecutions || 0) : 0),
          successRuns: Number(statsResponse.success ? (statsResponse.data.successExecutions || 0) : 0),
          failedRuns: Number(statsResponse.success ? (statsResponse.data.failedExecutions || 0) : 0),
          avgDuration: Number(statsResponse.success ? (statsResponse.data.avgProcessingTime || 0) : 0),
        },
        deviceStats: statsResponse.success && statsResponse.data.deviceStats ? {
          totalDevices: Number(statsResponse.data.deviceStats.totalDevices || 0),
          successDevices: Number(statsResponse.data.deviceStats.successDevices || 0),
          failedDevices: Number(statsResponse.data.deviceStats.failedDevices || 0),
          successRate: statsResponse.data.deviceStats.totalDevices > 0 ?
            Math.round((statsResponse.data.deviceStats.successDevices / statsResponse.data.deviceStats.totalDevices) * 100) : 0
        } : {
          // 如果API沒有返回設備統計，嘗試從執行記錄中計算
          totalDevices: executionsResponse.success && executionsResponse.data.executions ?
            executionsResponse.data.executions.reduce((sum: number, exec: any) => sum + (Number(exec.result?.totalDevices) || 0), 0) : 0,
          successDevices: executionsResponse.success && executionsResponse.data.executions ?
            executionsResponse.data.executions.reduce((sum: number, exec: any) => sum + (Number(exec.result?.successDevices) || 0), 0) : 0,
          failedDevices: executionsResponse.success && executionsResponse.data.executions ?
            executionsResponse.data.executions.reduce((sum: number, exec: any) => sum + (Number(exec.result?.failedDevices) || 0), 0) : 0,
          successRate: 0 // 稍後計算
        }
      };

      // 計算設備統計的成功率
      if (statsData.deviceStats.totalDevices > 0 && statsData.deviceStats.successRate === 0) {
        statsData.deviceStats.successRate = Math.round(
          (statsData.deviceStats.successDevices / statsData.deviceStats.totalDevices) * 100
        );
      }



      setStatistics(statsData);
    } catch (err) {
      console.error('載入統計數據失敗:', err);

      // 如果API調用失敗，嘗試使用計畫本身的統計數據作為備用
      const fallbackStats: StatisticsData = {
        overview: {
          totalRuns: plan.statistics?.totalRuns || 0,
          successRuns: plan.statistics?.successRuns || 0,
          failedRuns: plan.statistics?.failedRuns || 0,
          successRate: calculateSuccessRate(
            plan.statistics?.successRuns || 0,
            plan.statistics?.totalRuns || 0
          ),
          lastRun: typeof plan.lastRun === 'string' ? plan.lastRun : (plan.lastRun ? new Date(plan.lastRun).toISOString() : null),
          nextRun: typeof plan.nextRun === 'string' ? plan.nextRun : (plan.nextRun ? new Date(plan.nextRun).toISOString() : null),
        },
        recentExecutions: [],
        periodStats: {
          period: selectedPeriod,
          totalRuns: 0,
          successRuns: 0,
          failedRuns: 0,
          avgDuration: 0,
        },
        deviceStats: {
          totalDevices: 0,
          successDevices: 0,
          failedDevices: 0,
          successRate: 0
        }
      };

      console.log('使用備用統計數據:', fallbackStats);
      setStatistics(fallbackStats);
      const errorMessage = err instanceof Error ? err.message : (typeof err === 'string' ? err : '未知錯誤');
      setError(`載入統計數據失敗: ${errorMessage}。顯示基本統計信息。`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStatistics();
  }, [isOpen, selectedPeriod, plan._id]);

  // 格式化持續時間
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}分${seconds % 60}秒`;
    } else {
      return `${Math.floor(seconds / 3600)}小時${Math.floor((seconds % 3600) / 60)}分`;
    }
  };

  // 獲取狀態顏色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      case 'running':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 獲取狀態圖標
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4" />;
      case 'failed':
        return <XCircle className="w-4 h-4" />;
      case 'running':
        return <Activity className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  // 處理查看執行詳情
  const handleViewExecutionDetails = async (execution: any) => {
    try {
      console.log('執行記錄數據:', execution); // 調試用
      // 獲取執行詳情 - 使用 _id 字段
      const executionId = execution._id || execution.id;
      if (!executionId) {
        console.error('執行記錄缺少ID字段:', execution);
        alert('執行記錄缺少ID字段，無法查看詳情');
        return;
      }

      console.log('正在獲取執行詳情，ID:', executionId);
      const response = await refreshPlanApi.getExecutionDetail(store.id, executionId);
      console.log('執行詳情響應:', response);
      setSelectedExecution(response.data);
      setShowExecutionDetails(true);
    } catch (error) {
      console.error('獲取執行詳情失敗:', error);
      const errorMessage = error instanceof Error ? error.message : '未知錯誤';
      alert(`獲取執行詳情失敗: ${errorMessage}`);
    }
  };

  // 計算成功率
  const calculateSuccessRate = (success: number, total: number): string => {
    if (total === 0) return '0%';
    return `${Math.round((success / total) * 100)}%`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* 標題欄 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <BarChart3 className="w-6 h-6 text-purple-500 mr-2" />
            <h2 className="text-xl font-semibold text-gray-900">
              計畫統計 - {plan.name}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 內容 */}
        <div className="p-6">
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              <span className="ml-2 text-gray-600">載入統計數據中...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {statistics && !loading && (
            <div className="space-y-6">
              {/* 總覽統計 */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <TrendingUp className="w-8 h-8 text-blue-600" />
                    <div className="ml-3">
                      <p className="text-sm font-medium text-blue-600">總執行次數</p>
                      <p className="text-2xl font-bold text-blue-900">{statistics.overview.totalRuns}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-600">成功次數</p>
                      <p className="text-2xl font-bold text-green-900">{statistics.overview.successRuns}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-red-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <XCircle className="w-8 h-8 text-red-600" />
                    <div className="ml-3">
                      <p className="text-sm font-medium text-red-600">失敗次數</p>
                      <p className="text-2xl font-bold text-red-900">{statistics.overview.failedRuns}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <BarChart3 className="w-8 h-8 text-purple-600" />
                    <div className="ml-3">
                      <p className="text-sm font-medium text-purple-600">成功率</p>
                      <p className="text-2xl font-bold text-purple-900">{statistics.overview.successRate}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 時間信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {statistics.overview.lastRun && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-gray-600 mb-2">最後執行時間</h3>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatDateTime(statistics.overview.lastRun)}
                    </p>
                  </div>
                )}

                {statistics.overview.nextRun && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-gray-600 mb-2">下次執行時間</h3>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatDateTime(statistics.overview.nextRun)}
                    </p>
                  </div>
                )}
              </div>

              {/* 週期統計 */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">週期統計</h3>
                  <select
                    value={selectedPeriod}
                    onChange={(e) => setSelectedPeriod(e.target.value as 'day' | 'week' | 'month')}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="day">最近一天</option>
                    <option value="week">最近一週</option>
                    <option value="month">最近一月</option>
                  </select>
                </div>

                {/* 執行統計 */}
                <div className="mb-6">
                  <h4 className="text-md font-medium text-gray-700 mb-3">執行統計</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">總執行</p>
                      <p className="text-xl font-bold text-gray-900">{statistics.periodStats.totalRuns}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">成功</p>
                      <p className="text-xl font-bold text-green-600">{statistics.periodStats.successRuns}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">失敗</p>
                      <p className="text-xl font-bold text-red-600">{statistics.periodStats.failedRuns}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">平均耗時</p>
                      <p className="text-xl font-bold text-blue-600">
                        {formatDuration(statistics.periodStats.avgDuration)}
                      </p>
                    </div>
                  </div>
                </div>


              </div>

              {/* 最近執行記錄 */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">最近執行記錄</h3>
                
                {statistics.recentExecutions.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                    <p>暫無執行記錄</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            狀態
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            開始時間
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            設備統計
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            網關統計
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            耗時
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {statistics.recentExecutions.map((execution) => (
                          <tr key={execution._id || execution.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(execution.status)}`}>
                                {getStatusIcon(execution.status)}
                                <span className="ml-1">
                                  {execution.status === 'success' ? '成功' : 
                                   execution.status === 'failed' ? '失敗' : '執行中'}
                                </span>
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatDateTime(execution.startTime)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div className="flex items-center space-x-1">
                                <span className="text-green-600 font-medium">{execution.successDevices}</span>
                                <span className="text-gray-400">/</span>
                                <span className="text-red-600 font-medium">{execution.failedDevices}</span>
                                <span className="text-gray-400">/</span>
                                <span className="text-gray-600 font-medium">{execution.totalDevices}</span>
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                成功率: {execution.totalDevices > 0 ? Math.round((execution.successDevices / execution.totalDevices) * 100) : 0}%
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {execution.result?.summary?.totalGatewaysUsed ? (
                                <div>
                                  <div className="font-medium">{execution.result.summary.totalGatewaysUsed} 個網關</div>
                                  <div className="text-xs text-gray-500">
                                    平均 {Math.round((execution.result.summary.averageProcessingTimePerDevice || 0) / 1000)}s/設備
                                  </div>
                                </div>
                              ) : (
                                <span className="text-gray-400">-</span>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {execution.duration ? formatDuration(execution.duration) : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <button
                                onClick={() => handleViewExecutionDetails(execution)}
                                className="text-purple-600 hover:text-purple-900 text-sm font-medium"
                              >
                                查看詳情
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 底部按鈕 */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            關閉
          </button>
        </div>
      </div>

      {/* 執行詳情模態框 */}
      <ExecutionDetailsModal
        isOpen={showExecutionDetails}
        onClose={() => setShowExecutionDetails(false)}
        execution={selectedExecution}
      />
    </div>
  );
};

export default RefreshPlanStatisticsModal;
