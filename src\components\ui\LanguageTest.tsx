import { useTranslation } from 'react-i18next';
import { useEffect } from 'react';

/**
 * 測試組件，用於驗證語言切換是否正常工作
 */
export const LanguageTest = () => {
  const { t, i18n } = useTranslation();
  
  // 監聽語言變化
  useEffect(() => {
    console.log('LanguageTest 組件已渲染，當前語言:', i18n.language);
    
    const handleLanguageChanged = (lng: string) => {
      console.log('LanguageTest 檢測到語言變化:', lng);
    };
    
    i18n.on('languageChanged', handleLanguageChanged);
    
    return () => {
      i18n.off('languageChanged', handleLanguageChanged);
    };
  }, [i18n]);
  
  return (
    <div className="p-4 bg-gray-100 rounded mb-4">
      <h3 className="font-bold mb-2">語言測試</h3>
      <p>當前語言: {i18n.language}</p>
      <p>翻譯測試:</p>
      <ul className="list-disc pl-5">
        <li>添加: {t('common.add')}</li>
        <li>編輯: {t('common.edit')}</li>
        <li>刪除: {t('common.delete')}</li>
        <li>保存: {t('common.save')}</li>
      </ul>
    </div>
  );
};
