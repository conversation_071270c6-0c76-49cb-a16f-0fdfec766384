# 預覽圖生成流程文檔

本文檔詳細記錄了前端 PreviewComponent 和後端預覽服務的預覽圖生成流程，包含詳細的 call flow 和相關流程圖。

## 1. 系統架構概述

預覽圖生成系統由以下幾個主要部分組成：

1. **前端 PreviewComponent**：React 組件，負責在瀏覽器中渲染模板和生成預覽圖
2. **前端工具函數**：包括 `previewUtils.ts` 和 `previewImageManager.ts` 等
3. **後端預覽服務**：Node.js 服務，提供預覽圖生成 API
4. **後端發送預覽圖功能**：負責將預覽圖發送到網關設備

## 2. 前端預覽圖生成流程

### 2.1 PreviewComponent 組件流程

```mermaid
sequenceDiagram
    participant App as 應用程序
    participant PC as PreviewComponent
    participant PU as previewUtils
    participant CU as canvasUtils
    participant PIM as previewImageManager

    App->>PC: 渲染組件(template, bindingData, storeData)
    activate PC
    PC->>PC: useEffect 觸發 generatePreview()
    PC->>PC: 創建 DOM 元素並渲染模板
    PC->>PU: processTextBindings()
    PU-->>PC: 返回原始樣式信息
    PC->>CU: renderCanvasToImage(canvasRef, width, height)
    CU-->>PC: 返回渲染後的 Canvas
    PC->>PU: restoreTextBindings(originalStyles)
    PC->>PU: applyImageEffect(renderedCanvas, effectType, threshold)
    PU-->>PC: 返回處理後的 Canvas
    PC->>PC: 轉換為 base64 數據
    PC->>PC: setPreviewImage(previewData)

    alt 如果提供了設備 ID
        PC->>PIM: savePreviewImageToDevice(deviceId, previewData, storeId)
    end

    alt 如果提供了回調函數
        PC->>App: onPreviewGenerated(previewData)
    end

    PC-->>App: 返回預覽圖組件
    deactivate PC
```

### 2.2 regeneratePreviewBeforeSend 流程

當需要發送預覽圖到網關設備時，系統會使用 `regeneratePreviewBeforeSend` 函數重新生成最新的預覽圖：

```mermaid
sequenceDiagram
    participant DP as DevicesPage
    participant PIM as previewImageManager
    participant PC as PreviewComponent
    participant API as deviceApi

    DP->>DP: handleSendPreviewToGateway(device)
    DP->>API: 獲取模板數據
    API-->>DP: 返回模板
    DP->>API: 獲取門店數據
    API-->>DP: 返回門店數據
    DP->>PIM: regeneratePreviewBeforeSend(device, storeData, template)
    activate PIM
    PIM->>PIM: 創建臨時 DOM 容器
    PIM->>PC: 渲染 PreviewComponent
    PC->>PC: 生成預覽圖
    PC->>PIM: onPreviewGenerated 回調
    PIM-->>DP: 返回預覽圖數據
    deactivate PIM
    DP->>API: sendDevicePreviewToGateway(deviceId, options)
    API-->>DP: 返回發送結果
```

### 2.3 關鍵函數調用流程

1. **generatePreview 函數**：
   - 創建 DOM 元素並渲染模板
   - 分析模板中的數據綁定
   - 構造數據樣本
   - 處理綁定元素，使其顯示實際數據
   - 渲染畫布為圖像
   - 應用圖像效果
   - 返回預覽圖數據

2. **processTextBindings 函數**：
   - 獲取當前門店 ID
   - 獲取資料欄位和門店數據
   - 找到所有綁定數據的文字元素
   - 處理每個文字元素，顯示實際數據
   - 返回原始樣式信息

3. **renderCanvasToImage 函數**：
   - 創建臨時畫布容器
   - 移除選擇框和控制點
   - 使用 html2canvas 轉換為圖片
   - 返回渲染後的 Canvas

4. **applyImageEffect 函數**：
   - 根據效果類型選擇不同的處理函數
   - 返回處理後的 Canvas

## 3. 後端預覽圖生成流程

### 3.1 後端預覽服務流程

```mermaid
sequenceDiagram
    participant API as API 路由
    participant PS as previewService
    participant JSDOM as JSDOM 環境
    participant PC as 模擬 PreviewComponent

    API->>PS: generatePreviewFromService(template, bindingData, storeData, options)
    activate PS
    PS->>JSDOM: 創建 JSDOM 環境
    PS->>PC: 在 JSDOM 中渲染 PreviewComponent
    PC->>PC: 生成預覽圖
    PC->>PS: 返回預覽圖數據
    PS-->>API: 返回預覽圖數據
    deactivate PS
```

### 3.2 發送預覽圖到網關流程

```mermaid
sequenceDiagram
    participant API as API 路由
    participant SPG as sendPreviewToGateway
    participant PS as previewService
    participant WS as websocketService

    API->>SPG: sendDevicePreviewToGateway(deviceId, options)
    activate SPG
    SPG->>SPG: 獲取設備資料

    alt 設備有模板和綁定數據
        SPG->>PS: regeneratePreviewBeforeSend(device, storeData, template)

        alt 直接渲染失敗
            SPG->>PS: generatePreviewFromService(template, bindingData, storeData)
        end

        PS-->>SPG: 返回預覽圖數據
        SPG->>SPG: 更新設備的預覽圖
    end

    SPG->>WS: sendCommandToGateway(gatewayId, message)
    WS-->>SPG: 返回發送結果
    SPG-->>API: 返回結果
    deactivate SPG
```

### 3.3 後端 regeneratePreviewBeforeSend 函數流程

後端的 `regeneratePreviewBeforeSend` 函數與前端類似，但在 Node.js 環境中運行：

1. 創建 JSDOM 環境
2. 在 JSDOM 中渲染模板元素
3. 處理綁定元素，使其顯示實際數據
4. 渲染畫布為圖像
5. 應用圖像效果
6. 返回預覽圖數據

## 4. 前後端共享的關鍵邏輯

前端和後端共享以下關鍵邏輯：

1. **模板渲染邏輯**：如何將模板元素渲染為 DOM 元素
2. **數據綁定處理**：如何處理數據綁定，將實際數據顯示在模板中
3. **畫布渲染**：如何將 DOM 元素渲染為圖像
4. **圖像效果處理**：如何應用黑白、灰階等效果

## 5. 預覽圖生成的完整流程

```mermaid
graph TD
    A[開始] --> B[獲取模板數據]
    B --> C[獲取綁定數據]
    C --> D[獲取門店數據]
    D --> E[創建 DOM 元素並渲染模板]
    E --> F[處理綁定元素，顯示實際數據]
    F --> G[渲染畫布為圖像]
    G --> H[應用圖像效果]
    H --> I[轉換為 base64 數據]
    I --> J[保存預覽圖]
    J --> K[發送預覽圖到網關]
    K --> L[結束]
```

## 6. 預覽圖生成的關鍵挑戰和解決方案

1. **跨環境渲染**：前端和後端需要使用相同的渲染邏輯
   - 解決方案：在後端使用 JSDOM 模擬瀏覽器環境
   - 確保後端使用與前端相同的 html2canvas 版本
   - 在後端實現 DOM 操作的適配層

2. **數據綁定處理**：確保正確顯示綁定的數據
   - 解決方案：使用共享的數據綁定處理邏輯
   - 確保前後端使用相同的數據結構和處理流程
   - 在後端正確模擬前端的數據綁定邏輯

3. **圖像效果處理**：確保前後端生成的圖像效果一致
   - 解決方案：使用共享的圖像效果處理邏輯
   - 確保黑白、灰階等效果在前後端表現一致
   - 使用相同的閾值和處理參數

4. **無綁定數據的模板渲染**：確保沒有綁定數據的模板也能正確渲染
   - 解決方案：修改 PreviewComponent 的 useEffect 條件，使其在沒有綁定數據的情況下也能生成預覽圖
   - 在 generatePreview 函數中添加對 bindingData 是否存在的檢查

5. **性能優化**：確保預覽圖生成過程不會阻塞用戶界面
   - 解決方案：使用非阻塞式的預覽圖生成和保存
   - 在後端使用獨立的預覽服務處理大量預覽圖生成請求

## 7. 前端 PreviewComponent 的詳細實現

### 7.1 核心代碼結構

```typescript
const PreviewComponent: React.FC<PreviewComponentProps> = ({
  template,
  bindingData,
  storeData,
  effectType = 'blackAndWhite',
  threshold = 128,
  onPreviewGenerated,
  deviceId,
  storeId
}): JSX.Element => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  // 生成預覽圖的核心函數
  const generatePreview = async () => {
    // 1. 檢查模板和容器是否有效
    // 2. 創建元素容器
    // 3. 分析模板中的數據綁定
    // 4. 構造數據樣本
    // 5. 為每個元素創建 DOM 元素
    // 6. 處理綁定元素，顯示實際數據
    // 7. 渲染畫布為圖像
    // 8. 應用圖像效果
    // 9. 設置預覽圖並調用回調函數
  };

  // 當模板或綁定數據變更時生成預覽圖
  useEffect(() => {
    if (template) {
      // 即使沒有綁定數據也生成預覽圖
      generatePreview();
    }
  }, [template, bindingData, storeData, effectType, threshold]);

  return (
    <div className="preview-component">
      {/* 隱藏的渲染容器 */}
      <div ref={containerRef} style={{...}} />

      {/* 顯示預覽圖 */}
      <div className="preview-display">
        {previewImage ? (
          <img src={previewImage} alt="預覽" className="max-h-full max-w-full" />
        ) : (
          <p className="text-gray-500">生成預覽中...</p>
        )}
      </div>
    </div>
  );
};
```

### 7.2 關鍵函數實現

#### 7.2.1 processTextBindings 函數

```typescript
export const processTextBindings = async (): Promise<BindingStyleInfo[]> => {
  const originalStyles: BindingStyleInfo[] = [];

  try {
    // 1. 獲取當前門店ID
    // 2. 獲取資料欄位和門店數據
    // 3. 找到所有綁定數據的文字元素
    // 4. 處理每個文字元素，顯示實際數據
    // 5. 返回原始樣式信息
  } catch (error) {
    console.error('處理綁定元素時出錯:', error);
  }

  return originalStyles;
};
```

#### 7.2.2 renderCanvasToImage 函數

```typescript
export const renderCanvasToImage = async (
  canvasRef: React.RefObject<HTMLDivElement>,
  screenWidth: number,
  screenHeight: number
): Promise<HTMLCanvasElement | null> => {
  if (!canvasRef.current) return null;

  try {
    // 1. 保存當前狀態
    // 2. 創建臨時的畫布容器
    // 3. 移除所有選擇框和控制點
    // 4. 使用 html2canvas 轉換為圖片
    // 5. 清理臨時容器
    // 6. 返回渲染後的 Canvas
  } catch (error) {
    console.error('渲染畫布時發生錯誤:', error);
    return null;
  }
};
```

## 8. 後端預覽服務的詳細實現

### 8.1 預覽服務 API

```javascript
// 預覽圖生成 API 端點
app.post('/api/preview/generate', async (req, res) => {
  try {
    const { template, bindingData, storeData, effectType = 'blackAndWhite', threshold = 128 } = req.body;

    // 1. 驗證請求參數
    // 2. 創建 JSDOM 環境
    // 3. 在 JSDOM 中渲染模板
    // 4. 處理綁定元素，顯示實際數據
    // 5. 渲染畫布為圖像
    // 6. 應用圖像效果
    // 7. 返回預覽圖數據

    res.json({
      success: true,
      previewData: previewData
    });
  } catch (error) {
    console.error('生成預覽圖失敗:', error);
    res.status(500).json({
      success: false,
      error: error.message || '生成預覽圖失敗'
    });
  }
});
```

### 8.2 後端 regeneratePreviewBeforeSend 函數

```javascript
async function regeneratePreviewBeforeSend(device, storeData, template, dataFields = []) {
  if (!device || !template) {
    console.error('無法重新生成預覽圖：缺少設備或模板數據');
    return null;
  }

  try {
    // 1. 處理數據綁定格式
    // 2. 創建 JSDOM 環境
    // 3. 在 JSDOM 中渲染模板
    // 4. 處理綁定元素，顯示實際數據
    // 5. 渲染畫布為圖像
    // 6. 應用圖像效果
    // 7. 返回預覽圖數據
  } catch (error) {
    console.error('重新生成預覽圖失敗:', error);
    return null;
  }
}
```

## 9. 前後端整合與最佳實踐

### 9.1 前後端整合流程

```mermaid
graph TD
    A[前端應用] -->|1. 發送請求| B[API 路由]
    B -->|2. 調用服務| C[預覽服務]
    C -->|3. 生成預覽圖| D[返回預覽圖數據]
    D -->|4. 發送到網關| E[網關設備]

    F[後端直接生成] -->|1. 使用 JSDOM| G[模擬瀏覽器環境]
    G -->|2. 渲染模板| H[生成預覽圖]
    H -->|3. 發送到網關| E

    I[前端直接生成] -->|1. 使用 PreviewComponent| J[瀏覽器環境]
    J -->|2. 渲染模板| K[生成預覽圖]
    K -->|3. 通過 API 發送| E
```

### 9.2 最佳實踐

1. **代碼共享**
   - 將關鍵的渲染邏輯抽取為共享模塊
   - 確保前後端使用相同版本的依賴庫
   - 使用環境適配器模式處理不同環境的差異

2. **性能優化**
   - 使用緩存機制避免重複生成相同的預覽圖
   - 在後端使用工作隊列處理大量預覽圖生成請求
   - 優化 html2canvas 的配置參數提高渲染性能

3. **錯誤處理**
   - 實現完善的錯誤處理和日誌記錄
   - 提供降級方案，當預覽圖生成失敗時使用備用方案
   - 監控預覽圖生成過程的性能和成功率

4. **測試策略**
   - 為前後端預覽圖生成邏輯編寫單元測試
   - 使用視覺回歸測試確保前後端生成的預覽圖一致
   - 進行負載測試確保系統能夠處理大量並發請求

### 9.3 解決無綁定數據模板渲染問題的具體實現

```typescript
// 修改前
useEffect(() => {
  if (template && bindingData && Object.keys(bindingData).length > 0) {
    generatePreview();
  }
}, [template, bindingData, storeData, effectType, threshold]);

// 修改後
useEffect(() => {
  if (template) {
    // 即使沒有綁定數據也生成預覽圖
    generatePreview();
  }
}, [template, bindingData, storeData, effectType, threshold]);

// 在 generatePreview 函數中添加對 bindingData 的檢查
// 對於每個綁定的數據索引，查找對應的門店數據
// 確保 bindingData 存在且不為空對象
if (bindingData && Object.keys(bindingData).length > 0) {
  Object.entries(bindingData).forEach(([dataIndexKey, dataId]) => {
    // 處理綁定數據...
  });
}
```

## 10. 總結與未來改進方向

### 10.1 當前系統的優勢

1. **統一的渲染邏輯**：前後端使用相同的渲染邏輯，確保預覽圖一致性
2. **靈活的部署選項**：支持前端直接生成和後端服務生成兩種模式
3. **完善的數據綁定處理**：能夠正確處理各種數據綁定場景
4. **可擴展的效果處理**：支持多種圖像效果處理選項

### 10.2 未來改進方向

1. **性能優化**
   - 實現更高效的渲染算法
   - 使用 WebWorker 在前端進行非阻塞式渲染
   - 優化後端渲染服務的資源使用

2. **功能擴展**
   - 支持更多圖像效果和處理選項
   - 實現預覽圖的批量生成和處理
   - 添加預覽圖的版本控制和差異比較功能

3. **架構優化**
   - 將預覽服務完全獨立為微服務
   - 實現更好的前後端代碼共享機制
   - 優化預覽圖的存儲和傳輸方式
