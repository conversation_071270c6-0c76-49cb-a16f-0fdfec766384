import { buildEndpointUrl } from './api/apiConfig';

/**
 * 處理圖片 URL，確保在遠端連線時能正確載入圖片
 * 支援多種格式：完整URL、相對路徑、文件ID
 * @param imageUrl 原始圖片 URL 或文件ID
 * @returns 處理後的完整圖片 URL
 */
export const processImageUrl = (imageUrl: string): string => {
  if (!imageUrl) return imageUrl;

  // 如果看起來像文件 ID（24位十六進制字符），直接構建 API URL
  if (/^[a-f0-9]{24}$/i.test(imageUrl)) {
    return buildEndpointUrl('files', imageUrl);
  }

  // 如果是相對路徑，構建完整的 API URL
  if (imageUrl.startsWith('/api/files/')) {
    // 已經是 API 路徑，使用 buildEndpointUrl 構建完整 URL
    const fileId = imageUrl.replace('/api/files/', '');
    return buildEndpointUrl('files', fileId);
  }

  // 如果是完整的 HTTP/HTTPS URL，檢查是否需要轉換
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    // 檢查是否是 localhost URL，如果是則需要替換為當前主機
    if (imageUrl.includes('localhost:3001') || imageUrl.includes('127.0.0.1:3001')) {
      // 提取文件 ID
      const fileIdMatch = imageUrl.match(/\/api\/files\/([a-f0-9]+)/);
      if (fileIdMatch) {
        const fileId = fileIdMatch[1];
        return buildEndpointUrl('files', fileId);
      }
    }
    return imageUrl;
  }

  // 其他情況直接返回原 URL
  return imageUrl;
};

/**
 * 從完整的圖片URL中提取文件ID
 * @param imageUrl 完整的圖片URL
 * @returns 文件ID，如果無法提取則返回原URL
 */
export const extractFileIdFromUrl = (imageUrl: string): string => {
  if (!imageUrl) return imageUrl;

  // 如果已經是文件ID，直接返回
  if (/^[a-f0-9]{24}$/i.test(imageUrl)) {
    return imageUrl;
  }

  // 從相對路徑提取文件ID
  if (imageUrl.startsWith('/api/files/')) {
    return imageUrl.replace('/api/files/', '');
  }

  // 從完整URL提取文件ID
  const fileIdMatch = imageUrl.match(/\/api\/files\/([a-f0-9]{24})/i);
  if (fileIdMatch) {
    return fileIdMatch[1];
  }

  // 無法提取，返回原URL
  return imageUrl;
};

/**
 * 檢查URL是否為文件ID格式
 * @param url 要檢查的URL
 * @returns 是否為文件ID格式
 */
export const isFileId = (url: string): boolean => {
  return /^[a-f0-9]{24}$/i.test(url);
};

/**
 * 檢查URL是否為相對路徑格式
 * @param url 要檢查的URL
 * @returns 是否為相對路徑格式
 */
export const isRelativePath = (url: string): boolean => {
  return url.startsWith('/api/files/');
};

/**
 * 檢查URL是否為完整URL格式
 * @param url 要檢查的URL
 * @returns 是否為完整URL格式
 */
export const isFullUrl = (url: string): boolean => {
  return url.startsWith('http://') || url.startsWith('https://');
};
