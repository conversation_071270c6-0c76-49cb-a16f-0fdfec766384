/**
 * 簡化的 SVG 渲染器 - 不依賴外部庫
 * 直接解析和渲染基本的 SVG 路徑命令
 */

/**
 * 解析 SVG 路徑數據中的基本命令
 * @param {string} pathData SVG 路徑數據
 * @returns {Array} 解析後的命令數組
 */
function parsePathData(pathData) {
  const commands = [];

  // 更精確的預處理：保護小數點和負號
  let processedData = pathData
    // 首先在命令字母前後加空格
    .replace(/([MmLlHhVvCcSsQqTtAaZz])/g, ' $1 ')
    // 在數字前的正負號前加空格，但不破壞小數
    .replace(/(\d)([+-])/g, '$1 $2') // 數字後面的正負號前加空格
    .replace(/([MmLlHhVvCcSsQqTtAaZz]\s*)([+-])/g, '$1 $2') // 命令後面的正負號前加空格
    // 合併多個空格
    .replace(/\s+/g, ' ')
    .trim();

  console.log(`預處理後的路徑數據: ${processedData.substring(0, 100)}...`);

  // 使用更精確的正則表達式來匹配命令和參數
  const regex = /([MmLlHhVvCcSsQqTtAaZz])\s*((?:[-+]?(?:\d*\.?\d+(?:[eE][-+]?\d+)?)\s*,?\s*)*)/g;

  let match;
  while ((match = regex.exec(processedData)) !== null) {
    const command = match[1];
    const params = match[2].trim();

    if (params) {
      // 更精確的數字解析
      const numbers = [];
      // 使用正則表達式直接匹配數字
      const numberRegex = /[-+]?(?:\d*\.?\d+(?:[eE][-+]?\d+)?)/g;
      let numberMatch;
      while ((numberMatch = numberRegex.exec(params)) !== null) {
        const num = parseFloat(numberMatch[0]);
        if (!isNaN(num)) {
          numbers.push(num);
        }
      }

      // 根據命令類型分割參數，避免單個命令包含過多參數
      if (numbers.length > 0) {
        const commandParamCounts = {
          'M': 2, 'm': 2, 'L': 2, 'l': 2,
          'H': 1, 'h': 1, 'V': 1, 'v': 1,
          'C': 6, 'c': 6, 'S': 4, 's': 4,
          'Q': 4, 'q': 4, 'T': 2, 't': 2,
          'A': 7, 'a': 7, 'Z': 0, 'z': 0
        };

        const paramCount = commandParamCounts[command] || numbers.length;

        // 特殊處理 M/m 命令：第一對參數是移動，後續參數是隱式的 L/l 命令
        if ((command === 'M' || command === 'm') && numbers.length > 2) {
          // 第一個 M/m 命令
          commands.push({ command, params: numbers.slice(0, 2) });

          // 後續參數轉換為 L/l 命令
          const implicitCommand = command === 'M' ? 'L' : 'l';
          for (let i = 2; i < numbers.length; i += 2) {
            if (i + 1 < numbers.length) {
              commands.push({ command: implicitCommand, params: numbers.slice(i, i + 2) });
            }
          }
        } else if (paramCount > 0 && numbers.length > paramCount && numbers.length % paramCount === 0) {
          // 其他命令：如果參數數量是預期的倍數，分割成多個命令
          for (let i = 0; i < numbers.length; i += paramCount) {
            const commandParams = numbers.slice(i, i + paramCount);
            commands.push({ command, params: commandParams });
          }
        } else {
          commands.push({ command, params: numbers });
        }
      } else {
        commands.push({ command, params: [] });
      }
    } else {
      commands.push({ command, params: [] });
    }
  }

  console.log(`解析路徑數據: ${pathData.substring(0, 50)}... → ${commands.length} 個命令`);
  commands.forEach((cmd, i) => {
    if (i < 10) { // 顯示前10個命令的詳細信息
      console.log(`  命令 ${i + 1}: ${cmd.command} [${cmd.params.join(', ')}]`);
    }
  });

  return commands;
}

/**
 * 簡化的 SVG 路徑渲染器
 * @param {CanvasRenderingContext2D} ctx Canvas 上下文
 * @param {string} pathData SVG 路徑數據
 * @param {boolean} fill 是否填充
 * @param {boolean} stroke 是否描邊
 */
function renderSvgPath(ctx, pathData, fill = false, stroke = true) {
  try {
    console.log(`開始渲染 SVG 路徑，填充: ${fill}, 描邊: ${stroke}`);
    const commands = parsePathData(pathData);
    let currentX = 0;
    let currentY = 0;
    let startX = 0;
    let startY = 0;
    let pathStarted = false;

    ctx.beginPath();

    for (const { command, params } of commands) {
      console.log(`執行命令: ${command}, 參數: [${params.join(', ')}]`);

      switch (command.toUpperCase()) {
        case 'M': // Move to
          if (params.length >= 2) {
            if (command === 'M') {
              // 絕對座標
              currentX = params[0];
              currentY = params[1];
            } else {
              // 相對座標
              currentX += params[0];
              currentY += params[1];
            }
            console.log(`移動到: (${currentX}, ${currentY})`);
            ctx.moveTo(currentX, currentY);
            startX = currentX;
            startY = currentY;
            pathStarted = true;

            // 處理後續的隱式 lineTo 命令
            for (let i = 2; i < params.length; i += 2) {
              if (i + 1 < params.length) {
                if (command === 'M') {
                  currentX = params[i];
                  currentY = params[i + 1];
                } else {
                  currentX += params[i];
                  currentY += params[i + 1];
                }
                console.log(`隱式線條到: (${currentX}, ${currentY})`);
                ctx.lineTo(currentX, currentY);
              }
            }
          }
          break;
          
        case 'L': // Line to
          for (let i = 0; i < params.length; i += 2) {
            if (i + 1 < params.length) {
              if (command === 'L') {
                currentX = params[i];
                currentY = params[i + 1];
              } else {
                currentX += params[i];
                currentY += params[i + 1];
              }
              console.log(`線條到: (${currentX}, ${currentY})`);
              ctx.lineTo(currentX, currentY);
            }
          }
          break;
          
        case 'H': // Horizontal line
          for (const param of params) {
            if (command === 'H') {
              currentX = param;
            } else {
              currentX += param;
            }
            console.log(`水平線到: (${currentX}, ${currentY})`);
            ctx.lineTo(currentX, currentY);
          }
          break;

        case 'V': // Vertical line
          for (const param of params) {
            if (command === 'V') {
              currentY = param;
            } else {
              currentY += param;
            }
            console.log(`垂直線到: (${currentX}, ${currentY})`);
            ctx.lineTo(currentX, currentY);
          }
          break;
          
        case 'C': // Cubic Bezier curve
          for (let i = 0; i < params.length; i += 6) {
            if (i + 5 < params.length) {
              let x1, y1, x2, y2, x, y;
              if (command === 'C') {
                x1 = params[i];
                y1 = params[i + 1];
                x2 = params[i + 2];
                y2 = params[i + 3];
                x = params[i + 4];
                y = params[i + 5];
              } else {
                x1 = currentX + params[i];
                y1 = currentY + params[i + 1];
                x2 = currentX + params[i + 2];
                y2 = currentY + params[i + 3];
                x = currentX + params[i + 4];
                y = currentY + params[i + 5];
              }
              console.log(`貝塞爾曲線: 控制點1(${x1}, ${y1}), 控制點2(${x2}, ${y2}), 終點(${x}, ${y})`);
              ctx.bezierCurveTo(x1, y1, x2, y2, x, y);
              currentX = x;
              currentY = y;
            } else {
              console.warn(`貝塞爾曲線參數不足: 需要6個參數，但只有${params.length - i}個`);
              break;
            }
          }
          break;
          
        case 'Q': // Quadratic Bezier curve
          for (let i = 0; i < params.length; i += 4) {
            if (i + 3 < params.length) {
              let x1, y1, x, y;
              if (command === 'Q') {
                x1 = params[i];
                y1 = params[i + 1];
                x = params[i + 2];
                y = params[i + 3];
              } else {
                x1 = currentX + params[i];
                y1 = currentY + params[i + 1];
                x = currentX + params[i + 2];
                y = currentY + params[i + 3];
              }
              ctx.quadraticCurveTo(x1, y1, x, y);
              currentX = x;
              currentY = y;
            }
          }
          break;
          
        case 'A': // Arc
          // 弧形命令：A rx ry x-axis-rotation large-arc-flag sweep-flag x y
          for (let i = 0; i < params.length; i += 7) {
            if (i + 6 < params.length) {
              const rx = Math.abs(params[i]);     // x半徑
              const ry = Math.abs(params[i + 1]); // y半徑
              const xAxisRotation = params[i + 2]; // x軸旋轉角度
              const largeArcFlag = params[i + 3];  // 大弧標誌
              const sweepFlag = params[i + 4];     // 掃描方向標誌
              let x, y;

              if (command === 'A') {
                x = params[i + 5];
                y = params[i + 6];
              } else {
                x = currentX + params[i + 5];
                y = currentY + params[i + 6];
              }

              console.log(`弧形: 半徑(${rx}, ${ry}), 旋轉${xAxisRotation}°, 標誌(${largeArcFlag}, ${sweepFlag}), 終點(${x}, ${y})`);

              // 如果起點和終點相同，跳過
              if (Math.abs(x - currentX) < 0.001 && Math.abs(y - currentY) < 0.001) {
                console.log('弧形起點終點相同，跳過');
                continue;
              }

              // 如果半徑為0，畫直線
              if (rx < 0.001 || ry < 0.001) {
                console.log('弧形半徑為0，畫直線');
                ctx.lineTo(x, y);
              } else {
                // 實現精確的SVG弧形算法
                try {
                  const arcResult = convertSvgArcToCanvas(currentX, currentY, x, y, rx, ry, xAxisRotation, largeArcFlag, sweepFlag);

                  if (arcResult && arcResult.centerX !== undefined) {
                    // 使用Canvas的arc方法繪製精確弧形
                    const { centerX, centerY, radius, startAngle, endAngle, anticlockwise } = arcResult;
                    console.log(`精確弧形: 中心(${centerX.toFixed(2)}, ${centerY.toFixed(2)}), 半徑${radius.toFixed(2)}, 角度${startAngle.toFixed(2)}→${endAngle.toFixed(2)}`);

                    ctx.arc(centerX, centerY, radius, startAngle, endAngle, anticlockwise);
                  } else {
                    // 回退到貝塞爾曲線近似
                    console.log('弧形計算失敗，使用貝塞爾曲線近似');
                    const midX = (currentX + x) / 2;
                    const midY = (currentY + y) / 2;
                    const controlX = midX + (sweepFlag ? rx : -rx) * 0.3;
                    const controlY = midY + (sweepFlag ? ry : -ry) * 0.3;
                    ctx.quadraticCurveTo(controlX, controlY, x, y);
                  }
                } catch (error) {
                  console.warn('弧形渲染錯誤，使用直線:', error.message);
                  ctx.lineTo(x, y);
                }
              }

              currentX = x;
              currentY = y;
            }
          }
          break;
          
        case 'Z': // Close path
          console.log(`關閉路徑，回到起點: (${startX}, ${startY})`);
          ctx.closePath();
          currentX = startX;
          currentY = startY;
          break;

        default:
          console.warn(`不支援的 SVG 路徑命令: ${command}`);
          break;
      }
    }

    // 執行填充和描邊
    if (pathStarted) {
      if (fill) {
        console.log('執行路徑填充');
        ctx.fill();
      }
      if (stroke) {
        console.log('執行路徑描邊');
        ctx.stroke();
      }
    } else {
      console.warn('路徑未開始，跳過渲染');
    }

  } catch (error) {
    console.error('渲染 SVG 路徑時發生錯誤:', error);
    console.error('錯誤堆疊:', error.stack);
    // 如果路徑渲染失敗，繪製一個簡單的佔位符
    renderFallbackShape(ctx, fill, stroke);
  }
}

/**
 * 渲染備用形狀（當路徑解析失敗時）
 */
function renderFallbackShape(ctx, fill, stroke) {
  ctx.beginPath();
  ctx.arc(12, 12, 8, 0, 2 * Math.PI); // 在 24x24 viewBox 中心繪製圓形
  
  if (fill) {
    ctx.fill();
  }
  if (stroke) {
    ctx.stroke();
  }
}

/**
 * 將SVG弧形轉換為Canvas弧形參數
 * 實現SVG規範中的弧形到中心點參數化的轉換
 * @param {number} x1 起點X
 * @param {number} y1 起點Y
 * @param {number} x2 終點X
 * @param {number} y2 終點Y
 * @param {number} rx X軸半徑
 * @param {number} ry Y軸半徑
 * @param {number} phi X軸旋轉角度（度）
 * @param {number} fA 大弧標誌
 * @param {number} fS 掃描方向標誌
 * @returns {Object|null} Canvas弧形參數或null
 */
function convertSvgArcToCanvas(x1, y1, x2, y2, rx, ry, phi, fA, fS) {
  try {
    // 如果起點終點相同，返回null
    if (Math.abs(x2 - x1) < 1e-6 && Math.abs(y2 - y1) < 1e-6) {
      return null;
    }

    // 確保半徑為正數
    rx = Math.abs(rx);
    ry = Math.abs(ry);

    // 如果任一半徑為0，返回null（應該畫直線）
    if (rx < 1e-6 || ry < 1e-6) {
      return null;
    }

    // 轉換角度為弧度
    const phiRad = (phi * Math.PI) / 180;
    const cosPhi = Math.cos(phiRad);
    const sinPhi = Math.sin(phiRad);

    // 計算中點
    const dx = (x1 - x2) / 2;
    const dy = (y1 - y2) / 2;

    // 旋轉到橢圓坐標系
    const x1Prime = cosPhi * dx + sinPhi * dy;
    const y1Prime = -sinPhi * dx + cosPhi * dy;

    // 修正半徑
    const lambda = (x1Prime * x1Prime) / (rx * rx) + (y1Prime * y1Prime) / (ry * ry);
    if (lambda > 1) {
      rx *= Math.sqrt(lambda);
      ry *= Math.sqrt(lambda);
    }

    // 計算中心點
    const sign = fA === fS ? -1 : 1;
    const sq = Math.max(0, (rx * rx * ry * ry - rx * rx * y1Prime * y1Prime - ry * ry * x1Prime * x1Prime) /
                           (rx * rx * y1Prime * y1Prime + ry * ry * x1Prime * x1Prime));
    const coeff = sign * Math.sqrt(sq);

    const cxPrime = coeff * ((rx * y1Prime) / ry);
    const cyPrime = coeff * (-(ry * x1Prime) / rx);

    // 轉換回原坐標系
    const cx = cosPhi * cxPrime - sinPhi * cyPrime + (x1 + x2) / 2;
    const cy = sinPhi * cxPrime + cosPhi * cyPrime + (y1 + y2) / 2;

    // 計算角度
    const ux = (x1Prime - cxPrime) / rx;
    const uy = (y1Prime - cyPrime) / ry;
    const vx = (-x1Prime - cxPrime) / rx;
    const vy = (-y1Prime - cyPrime) / ry;

    const n = Math.sqrt(ux * ux + uy * uy);
    const p = ux;
    const theta1 = Math.acos(p / n) * (uy < 0 ? -1 : 1);

    const numer = ux * vx + uy * vy;
    const denom = Math.sqrt((ux * ux + uy * uy) * (vx * vx + vy * vy));
    const p2 = numer / denom;
    let dtheta = Math.acos(Math.max(-1, Math.min(1, p2)));

    if (ux * vy - uy * vx < 0) {
      dtheta = -dtheta;
    }

    if (fS === 0 && dtheta > 0) {
      dtheta -= 2 * Math.PI;
    } else if (fS === 1 && dtheta < 0) {
      dtheta += 2 * Math.PI;
    }

    // 對於Canvas，我們需要使用圓形而不是橢圓
    // 使用平均半徑作為近似
    const radius = (rx + ry) / 2;

    return {
      centerX: cx,
      centerY: cy,
      radius: radius,
      startAngle: theta1,
      endAngle: theta1 + dtheta,
      anticlockwise: dtheta < 0
    };
  } catch (error) {
    console.warn('SVG弧形轉換失敗:', error.message);
    return null;
  }
}

/**
 * 渲染完整的 SVG 元素到 Canvas
 * @param {CanvasRenderingContext2D} ctx Canvas 上下文
 * @param {Element} svgElement SVG DOM 元素
 * @param {Object} options 渲染選項
 */
function renderSvgElement(ctx, svgElement, options = {}) {
  const {
    scaleX = 1,
    scaleY = 1,
    offsetX = 0,
    offsetY = 0,
    strokeColor = '#000',
    strokeWidth = 2,
    fillColor = 'none'
  } = options;

  console.log(`開始渲染 SVG 元素，選項:`, {
    scaleX, scaleY, offsetX, offsetY, strokeColor, strokeWidth, fillColor
  });

  ctx.save();

  // 應用變換
  ctx.translate(offsetX, offsetY);
  ctx.scale(scaleX, scaleY);

  // 設置樣式 - 使用更精確的線條設置
  ctx.strokeStyle = strokeColor;
  ctx.lineWidth = strokeWidth / Math.min(scaleX, scaleY); // 調整線條寬度以適應縮放
  ctx.lineCap = 'round';
  ctx.lineJoin = 'round';
  ctx.miterLimit = 10; // 設置斜接限制

  if (fillColor !== 'none') {
    ctx.fillStyle = fillColor;
  }

  // 獲取所有路徑元素
  const pathElements = svgElement.querySelectorAll('path, circle, line, rect, polygon, polyline');
  console.log(`找到 ${pathElements.length} 個 SVG 子元素需要渲染`);

  for (const element of pathElements) {
    try {
      console.log(`渲染 ${element.tagName} 元素`);

      if (element.tagName === 'path') {
        const d = element.getAttribute('d');
        if (d) {
          console.log(`渲染路徑: ${d.substring(0, 100)}...`);
          renderSvgPath(ctx, d, fillColor !== 'none', true);
        } else {
          console.warn('路徑元素缺少 d 屬性');
        }
      } else if (element.tagName === 'circle') {
        const cx = parseFloat(element.getAttribute('cx') || '0');
        const cy = parseFloat(element.getAttribute('cy') || '0');
        const r = parseFloat(element.getAttribute('r') || '0');

        console.log(`渲染圓形: 中心(${cx}, ${cy}), 半徑${r}`);
        ctx.beginPath();
        ctx.arc(cx, cy, r, 0, 2 * Math.PI);
        if (fillColor !== 'none') {
          ctx.fill();
        }
        ctx.stroke();
      } else if (element.tagName === 'line') {
        const x1 = parseFloat(element.getAttribute('x1') || '0');
        const y1 = parseFloat(element.getAttribute('y1') || '0');
        const x2 = parseFloat(element.getAttribute('x2') || '0');
        const y2 = parseFloat(element.getAttribute('y2') || '0');

        console.log(`渲染線條: 從(${x1}, ${y1})到(${x2}, ${y2})`);
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
      } else if (element.tagName === 'polyline') {
        const points = element.getAttribute('points');
        if (points) {
          console.log(`渲染折線: ${points}`);

          // 解析點座標
          const pointPairs = points.trim().split(/\s+|,/).filter(p => p.length > 0);
          const coordinates = [];

          for (let i = 0; i < pointPairs.length; i += 2) {
            if (i + 1 < pointPairs.length) {
              coordinates.push({
                x: parseFloat(pointPairs[i]),
                y: parseFloat(pointPairs[i + 1])
              });
            }
          }

          if (coordinates.length >= 2) {
            ctx.beginPath();
            ctx.moveTo(coordinates[0].x, coordinates[0].y);

            for (let i = 1; i < coordinates.length; i++) {
              ctx.lineTo(coordinates[i].x, coordinates[i].y);
            }

            ctx.stroke();
            console.log(`折線渲染完成，共 ${coordinates.length} 個點`);
          } else {
            console.warn('折線點數不足，無法渲染');
          }
        } else {
          console.warn('polyline 元素缺少 points 屬性');
        }
      } else if (element.tagName === 'rect') {
        const x = parseFloat(element.getAttribute('x') || '0');
        const y = parseFloat(element.getAttribute('y') || '0');
        const width = parseFloat(element.getAttribute('width') || '0');
        const height = parseFloat(element.getAttribute('height') || '0');
        const rx = parseFloat(element.getAttribute('rx') || '0');
        const ry = parseFloat(element.getAttribute('ry') || '0');

        console.log(`渲染矩形: 位置(${x}, ${y}), 尺寸${width}x${height}, 圓角(${rx}, ${ry})`);

        ctx.beginPath();

        // 如果有圓角，使用圓角矩形
        if (rx > 0 || ry > 0) {
          const radiusX = rx || ry;
          const radiusY = ry || rx;

          // 繪製圓角矩形
          ctx.moveTo(x + radiusX, y);
          ctx.lineTo(x + width - radiusX, y);
          ctx.quadraticCurveTo(x + width, y, x + width, y + radiusY);
          ctx.lineTo(x + width, y + height - radiusY);
          ctx.quadraticCurveTo(x + width, y + height, x + width - radiusX, y + height);
          ctx.lineTo(x + radiusX, y + height);
          ctx.quadraticCurveTo(x, y + height, x, y + height - radiusY);
          ctx.lineTo(x, y + radiusY);
          ctx.quadraticCurveTo(x, y, x + radiusX, y);
          ctx.closePath();
        } else {
          // 普通矩形
          ctx.rect(x, y, width, height);
        }

        if (fillColor !== 'none') {
          ctx.fill();
        }
        ctx.stroke();
      } else {
        console.warn(`不支援的 SVG 元素類型: ${element.tagName}`);
      }
    } catch (error) {
      console.error(`渲染 ${element.tagName} 元素時發生錯誤:`, error);
      console.error('錯誤堆疊:', error.stack);
    }
  }
  
  ctx.restore();
}

module.exports = {
  renderSvgPath,
  renderSvgElement,
  parsePathData
};
