// multi-gateway.js
// 多網關模擬器 - 同時模擬多個網關設備
// 此腳本創建多個網關模擬器，每個模擬器使用不同的網關信息

const dgram = require('dgram');
const os = require('os');

// 配置
const UDP_PORT = 5000;
const PROTOCOL = "EPD-GATEWAY-DISCOVERY";
const VERSION = "1.0";
const GATEWAY_COUNT = 3; // 模擬的網關數量

// 生成隨機 MAC 地址
function generateMacAddress() {
  return Array.from({ length: 6 }, () => 
    Math.floor(Math.random() * 256).toString(16).padStart(2, '0')
  ).join(':').toUpperCase();
}

// 獲取本機 IP 地址
function getLocalIpAddress() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return '*************'; // 默認 IP 地址
}

// 創建網關模擬器
function createGatewaySimulator(index) {
  // 網關型號列表
  const models = ["GW-2000", "GW-3000", "GW-Pro", "GW-Lite", "GW-Enterprise"];
  
  // 網關功能列表
  const capabilitiesList = [
    ["wifi", "bluetooth"],
    ["wifi", "bluetooth", "zigbee"],
    ["wifi", "bluetooth", "lora"],
    ["wifi", "bluetooth", "nfc"],
    ["wifi", "bluetooth", "zigbee", "lora", "nfc"]
  ];
  
  // 網關信息
  const gatewayInfo = {
    macAddress: generateMacAddress(),
    model: models[index % models.length],
    name: `Gateway-${index}-${Math.floor(1000 + Math.random() * 9000)}`,
    firmwareVersion: `${1 + Math.floor(index / 2)}.${index % 5}.${Math.floor(Math.random() * 10)}`,
    ipAddress: getLocalIpAddress(),
    capabilities: capabilitiesList[index % capabilitiesList.length],
    status: ["ready", "busy", "updating"][index % 3]
  };
  
  // 創建 UDP 服務器
  const server = dgram.createSocket('udp4');
  
  // 處理錯誤
  server.on('error', (err) => {
    console.error(`網關 ${index} UDP 服務器錯誤: ${err.stack}`);
    server.close();
  });
  
  // 處理消息
  server.on('message', (msg, rinfo) => {
    try {
      // 解析 JSON 數據
      const request = JSON.parse(msg.toString());
      
      console.log(`網關 ${index} 收到來自 ${rinfo.address}:${rinfo.port} 的消息:`, request);
      
      // 驗證消息格式和協議
      if (request.type === 'discovery' && 
          request.protocol === PROTOCOL && 
          request.version === VERSION) {
        
        console.log(`網關 ${index} 收到有效的發現請求，準備回應`);
        
        // 創建回應消息
        const response = {
          type: "discovery-response",
          protocol: PROTOCOL,
          version: VERSION,
          timestamp: Date.now(),
          ...gatewayInfo
        };
        
        // 添加隨機延遲，避免網絡擁塞
        const delay = Math.random() * 400 + 100; // 100-500ms
        setTimeout(() => {
          // 發送回應
          const responseBuffer = Buffer.from(JSON.stringify(response));
          server.send(responseBuffer, 0, responseBuffer.length, rinfo.port, rinfo.address, (err) => {
            if (err) {
              console.error(`網關 ${index} 發送回應時出錯: ${err}`);
            } else {
              console.log(`網關 ${index} 已回應發現請求: ${rinfo.address}:${rinfo.port}`);
            }
          });
        }, delay);
      }
    } catch (error) {
      console.error(`網關 ${index} 處理消息時出錯: ${error}`);
    }
  });
  
  // 啟動服務器
  server.on('listening', () => {
    const address = server.address();
    console.log(`網關 ${index} UDP 服務已啟動，監聽 ${address.address}:${address.port}`);
    console.log(`網關 ${index} 信息:`, gatewayInfo);
  });
  
  // 綁定端口
  server.bind(UDP_PORT);
  
  return {
    server,
    gatewayInfo
  };
}

// 創建多個網關模擬器
const gateways = [];
for (let i = 0; i < GATEWAY_COUNT; i++) {
  gateways.push(createGatewaySimulator(i));
}

// 處理進程終止
process.on('SIGINT', () => {
  console.log('多網關模擬器已停止');
  gateways.forEach((gateway, index) => {
    gateway.server.close();
    console.log(`網關 ${index} 已關閉`);
  });
  process.exit();
});

console.log(`啟動 ${GATEWAY_COUNT} 個網關模擬器...`);
