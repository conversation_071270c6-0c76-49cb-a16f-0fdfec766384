# WebSocket 服務整合指南

## WebSocket 服務更新

### 1. websocketService.js 更新

#### 1.1 引入壓縮模組
```javascript
// 在文件頂部添加
const rawdataCompression = require('../utils/rawdataCompression');
const { RAWDATA_FORMATS } = rawdataCompression;

// 更新 Gateway 能力存儲結構
const gatewayCapabilities = new Map(); // 現有
// 新增：Gateway 偏好格式存儲
const gatewayPreferredFormats = new Map();
```

#### 1.2 更新 Gateway 信息處理
```javascript
// 修改 handleGatewayInfo 函數
async function handleGatewayInfo(ws, data) {
  try {
    const info = data.info || {};
    const gatewayId = ws.gatewayId;
    const tokenMacAddress = ws.macAddress;

    console.log(`更新網關 ${gatewayId} 的信息`);

    // 處理Gateway能力上報
    if (info.chunkingSupport) {
      const chunkingSupport = info.chunkingSupport;
      gatewayCapabilities.set(tokenMacAddress, chunkingSupport);

      // 新增：處理偏好的 rawdata 格式
      if (chunkingSupport.supportedFormat && typeof chunkingSupport.supportedFormat === 'string') {
        const validFormats = [RAWDATA_FORMATS.RAWDATA, RAWDATA_FORMATS.RUNLENDATA];
        if (validFormats.includes(chunkingSupport.supportedFormat)) {
          gatewayPreferredFormats.set(tokenMacAddress, chunkingSupport.supportedFormat);
          console.log(`Gateway ${tokenMacAddress} 偏好 rawdata 格式: ${chunkingSupport.supportedFormat}`);
        } else {
          gatewayPreferredFormats.set(tokenMacAddress, RAWDATA_FORMATS.RAWDATA);
          console.log(`Gateway ${tokenMacAddress} 指定了無效格式 ${chunkingSupport.supportedFormat}，使用預設 rawdata`);
        }
      } else {
        // 預設使用 rawdata
        gatewayPreferredFormats.set(tokenMacAddress, RAWDATA_FORMATS.RAWDATA);
        console.log(`Gateway ${tokenMacAddress} 未指定格式偏好，使用預設 rawdata`);
      }

      console.log(`Gateway ${tokenMacAddress} 分片能力:`, {
        enabled: chunkingSupport.enabled,
        maxChunkSize: chunkingSupport.maxChunkSize,
        maxSingleMessageSize: chunkingSupport.maxSingleMessageSize,
        embeddedIndexSupport: chunkingSupport.embeddedIndex,
        preferredFormat: gatewayPreferredFormats.get(tokenMacAddress)
      });
    } else {
      console.log(`Gateway ${tokenMacAddress} 未上報分片能力，視為不支援`);
      gatewayPreferredFormats.set(tokenMacAddress, RAWDATA_FORMATS.RAWDATA);
    }

    // ... 其餘現有代碼
  } catch (error) {
    console.error('處理網關信息時出錯:', error);
    // ... 錯誤處理
  }
}
```

#### 1.3 新增格式選擇函數
```javascript
/**
 * 獲取 Gateway 偏好的 rawdata 格式
 * @param {string} gatewayMac - Gateway MAC 地址
 * @returns {string} 偏好的格式
 */
function getGatewayPreferredFormat(gatewayMac) {
  const preferredFormat = gatewayPreferredFormats.get(gatewayMac) || RAWDATA_FORMATS.RAWDATA;

  console.log(`Gateway ${gatewayMac} 偏好格式: ${preferredFormat}`);
  return preferredFormat;
}

/**
 * 處理 rawdata 格式轉換
 * @param {string} format - 目標格式
 * @param {Uint8Array} rawBuffer - 完整的 rawdata（包含 ImageInfo）
 * @returns {Object} 處理結果
 */
function processRawdataFormat(format, rawBuffer) {
  try {
    // 如果是 rawdata 格式，直接返回
    if (format === RAWDATA_FORMATS.RAWDATA) {
      console.log(`使用原始 rawdata 格式，無需轉換`);
      return {
        success: true,
        format: format,
        data: rawBuffer,
        compressionResult: {
          originalSize: rawBuffer.length,
          compressedSize: rawBuffer.length,
          compressionRatio: 1.0,
          processingTime: 0
        }
      };
    }

    // 分離 ImageInfo 和像素數據
    const IMAGE_INFO_SIZE = 12; // ImageInfo 固定 12 bytes

    if (rawBuffer.length < IMAGE_INFO_SIZE) {
      throw new Error('Rawdata too small to contain ImageInfo');
    }

    const imageInfoBytes = rawBuffer.slice(0, IMAGE_INFO_SIZE);
    const pixelData = rawBuffer.slice(IMAGE_INFO_SIZE);

    console.log(`處理 rawdata 格式轉換: 格式=${format}, ImageInfo=${IMAGE_INFO_SIZE} bytes, 像素數據=${pixelData.length} bytes`);

    // 壓縮像素數據
    const compressionResult = rawdataCompression.compressPixelData(format, pixelData);

    if (!compressionResult.success) {
      throw new Error(`格式轉換失敗: ${compressionResult.error}`);
    }

    // 重新組合 ImageInfo + 處理後的像素數據
    const finalBuffer = new Uint8Array(IMAGE_INFO_SIZE + compressionResult.data.length);
    finalBuffer.set(imageInfoBytes, 0);
    finalBuffer.set(compressionResult.data, IMAGE_INFO_SIZE);

    console.log(`格式轉換完成:`, {
      originalPixelSize: pixelData.length,
      processedPixelSize: compressionResult.data.length,
      compressionRatio: `${(compressionResult.compressionRatio * 100).toFixed(1)}%`,
      finalSize: finalBuffer.length,
      processingTime: `${compressionResult.processingTime.toFixed(2)}ms`
    });

    return {
      success: true,
      format: format,
      data: finalBuffer,
      compressionResult: compressionResult
    };

  } catch (error) {
    console.error('Rawdata 格式轉換失敗:', error.message);
    return {
      success: false,
      format: RAWDATA_FORMATS.RAWDATA,
      data: rawBuffer, // 返回原始數據
      error: error.message
    };
  }
}
```

#### 1.4 更新圖像傳輸函數
```javascript
// 修改 sendImageToGateway 函數
const sendImageToGateway = async (ws, deviceMac, imageCode, rawBuffer, imageData = null) => {
  console.log(`開始發送圖片到網關，設備: ${deviceMac}, imageCode: ${imageCode}`);

  try {
    const gatewayMac = ws.macAddress;
    const gatewayId = ws.gatewayId;
    if (!gatewayMac) {
      throw new Error('無法獲取網關MAC地址，WebSocket連接缺少macAddress屬性');
    }

    console.log(`目標設備: ${deviceMac}, 通過網關: ${gatewayMac}`);

    // 新增：獲取 Gateway 偏好的 rawdata 格式
    const preferredFormat = getGatewayPreferredFormat(gatewayMac);

    // 處理 rawdata 格式轉換
    const formatResult = processRawdataFormat(preferredFormat, rawBuffer);
    const finalRawBuffer = formatResult.data;
    const selectedFormat = formatResult.format;

    console.log(`使用格式: ${selectedFormat}, 數據大小: ${finalRawBuffer.length} bytes`);

    // 根據 Gateway 上報的能力判斷傳輸方式
    if (shouldUseChunking(finalRawBuffer.length, gatewayMac)) {
      // 使用分片傳輸
      const chunkSize = getChunkSize(gatewayMac);
      console.log(`使用分片傳輸，分片大小: ${chunkSize} bytes`);

      await sendChunkedRawdataWithEmbeddedIndex(
        ws, deviceMac, imageCode, finalRawBuffer, chunkSize, selectedFormat
      );
    } else {
      // 直接傳輸
      console.log('使用直接傳輸');
      await sendDirectRawdata(ws, deviceMac, imageCode, finalRawBuffer, imageData, selectedFormat);
    }

    console.log('✅ 圖片傳輸完成');
    return { success: true };

  } catch (error) {
    console.error('❌ 圖片傳輸失敗:', error);
    return { success: false, error: error.message };
  }
};
```

#### 1.5 更新分片傳輸函數
```javascript
// 修改 sendChunkedRawdataWithEmbeddedIndex 函數簽名
const sendChunkedRawdataWithEmbeddedIndex = async (ws, deviceMac, imageCode, rawBuffer, chunkSize, rawdataFormat = 'rawdata') => {
  const INDEX_SIZE = 4;
  const chunkId = generateChunkId();
  const totalChunks = Math.ceil(rawBuffer.length / chunkSize);

  // ... 現有的性能檢查代碼

  // 1. 發送分片開始訊息（添加格式信息）
  const chunkStartMessage = {
    type: 'image_chunk_start',
    chunkId: chunkId,
    deviceMac: deviceMac,
    imageCode: imageCode,
    totalChunks: totalChunks,
    totalSize: rawBuffer.length,
    chunkSize: chunkSize,
    indexSize: INDEX_SIZE,
    dataType: 'rawdata',
    rawdataFormat: rawdataFormat,  // 新增：格式標識
    mode: 'embedded_index',
    timestamp: new Date().toISOString()
  };
  
  ws.send(JSON.stringify(chunkStartMessage));

  // ... 其餘現有代碼保持不變
};

// 修改 sendDirectRawdata 函數簽名
const sendDirectRawdata = async (ws, deviceMac, imageCode, rawBuffer, imageData = null, rawdataFormat = 'rawdata') => {
  const directMessage = {
    type: 'update_preview',
    deviceMac: deviceMac,
    imageCode: imageCode,
    rawdata: Array.from(rawBuffer),
    rawdataFormat: rawdataFormat,  // 新增：格式標識
    timestamp: new Date().toISOString()
  };

  // 如果提供了 imageData，也一併發送（向後兼容）
  if (imageData) {
    directMessage.imageData = imageData;
    console.log(`✅ 直接傳輸包含 imageData: ${imageData.length} 字符`);
  }

  // ... 其餘現有代碼保持不變
};
```

### 2. sendPreviewToGateway.js 更新

#### 2.1 整合壓縮模組
```javascript
// 在文件頂部添加
const rawdataCompression = require('./rawdataCompression');

// 修改 generateRawData 函數，移除壓縮邏輯
async function generateRawData(imageDataStr, device, imageCode, template) {
  try {
    // 創建臨時 Canvas
    const canvas = await epdConversion.createCanvasFromImageData(imageDataStr);

    // 獲取設備的 colorType 和尺寸
    const colorType = mapDeviceColorType(device.data?.colorType);
    const { width, height } = parseDeviceSize(device.data?.size);
    const templateRotation = epdConversion.getTemplateRotation(template);

    console.log(`設備 ${device.macAddress} - 模板旋轉角度: ${templateRotation}°`);

    // 轉換選項
    const options = {
      colorType,
      width,
      height,
      imagecode: parseInt(imageCode, 16),
      x: 0,
      y: 0,
      templateRotation
    };

    // 執行 EPD 轉換（生成未壓縮的 rawdata）
    const result = epdConversion.EpdConverter.convert(canvas, options);

    if (!result.success) {
      throw new Error(`EPD轉換失敗: ${result.error}`);
    }

    console.log(`EPD轉換成功，rawdata 大小: ${result.rawdata.length} bytes`);
    
    // 注意：這裡返回的是未壓縮的 rawdata
    // 壓縮處理將在 websocketService.js 中進行
    return result.rawdata;

  } catch (error) {
    console.error('生成 rawdata 失敗:', error);
    throw error;
  }
}
```

### 3. 測試工具更新

#### 3.1 ws-client-from-copied-info.js 主要更新
```javascript
// 在文件頂部添加解壓縮函數
const { RAWDATA_FORMATS } = {
  RAWDATA_FORMATS: {
    RAWDATA: 'rawdata',
    RUNLENDATA: 'runlendata'
  }
};

// Run-Length 壓縮函數（與 Go 版本輸出相同）
function compressRunLength(buf) {
  const result = [];
  const n = buf.length;
  let inx = 0;

  while (inx < n) {
    // 檢查重複序列
    let runLength = 1;
    while (inx + runLength < n && buf[inx + runLength] === buf[inx] && runLength < 0x7F) {
      runLength++;
    }

    if (runLength >= 2) {
      // 編碼重複序列：[runLength, value] (bit7 = 0)
      result.push(runLength);
      result.push(buf[inx]);
      inx += runLength;
    } else {
      // 非重複序列
      const start = inx;
      while (inx < n &&
             (inx + 1 >= n || buf[inx] !== buf[inx + 1]) &&
             (inx - start) < 0x7F) {
        inx++;
      }
      const length = inx - start;
      result.push(0x80 | length); // bit7 = 1
      for (let i = start; i < inx; i++) {
        result.push(buf[i]);
      }
    }
  }

  return new Uint8Array(result);
}

// Run-Length 解壓縮函數（與 Go 版本對應）
function decompressRunLength(compressedData) {
  const decompressed = [];
  let i = 0;

  while (i < compressedData.length) {
    const header = compressedData[i];
    i++;

    if ((header & 0x80) === 0) {
      // 重複序列：bit7 = 0
      const runLength = header;
      if (i >= compressedData.length) {
        throw new Error('Incomplete RLE data: missing value byte');
      }
      const value = compressedData[i];
      i++;

      for (let j = 0; j < runLength; j++) {
        decompressed.push(value);
      }
    } else {
      // 非重複序列：bit7 = 1
      const length = header & 0x7F;
      if (i + length > compressedData.length) {
        throw new Error('Incomplete RLE data: insufficient data bytes');
      }

      for (let j = 0; j < length; j++) {
        decompressed.push(compressedData[i + j]);
      }
      i += length;
    }
  }

  return new Uint8Array(decompressed);
}

// 通用解壓縮函數
function decompressRawdata(rawdata, format) {
  switch (format) {
    case RAWDATA_FORMATS.RAWDATA:
      return rawdata; // 無需解壓縮
    case RAWDATA_FORMATS.RUNLENDATA:
      return decompressRunLength(rawdata);
    default:
      console.warn(`未知的 rawdata 格式: ${format}，當作未壓縮處理`);
      return rawdata;
  }
}

// 更新 main() 函數中的格式選擇邏輯
async function main() {
  try {
    console.log('EPD 網關模擬器（從複製的WebSocket資訊啟動）');

    // ... 現有的分片大小選擇代碼

    // 新增：詢問偏好的 rawdata 格式
    console.log('\n===== Rawdata 格式偏好設定 =====');
    console.log('請選擇要模擬的偏好 rawdata 格式：');
    console.log('1. rawdata (預設，未壓縮格式)');
    console.log('2. runlendata (Run-Length 壓縮格式)');

    const formatChoice = await prompt('請選擇格式偏好選項 (1-2，預設為1): ') || '1';
    let preferredFormat = 'rawdata';

    switch (formatChoice) {
      case '1':
        preferredFormat = 'rawdata';
        break;
      case '2':
        preferredFormat = 'runlendata';
        break;
      default:
        preferredFormat = 'rawdata';
    }

    console.log(`已設定偏好格式: ${preferredFormat}`);
    console.log('===============================\n');

    // ... 其餘現有代碼

    // 將格式信息添加到 wsInfo
    wsInfo.preferredFormat = preferredFormat;

    // ... 其餘現有代碼
  } catch (error) {
    console.error('錯誤:', error.message);
    rl.close();
    process.exit(1);
  }
}

// 更新 connectWebSocket 函數中的 gatewayInfo 消息
function connectWebSocket(wsInfo, maxChunkSize = 20) {
  // ... 現有代碼

  // 在發送 gatewayInfo 時包含格式支援信息
  const gatewayInfoMessage = {
    type: 'gatewayInfo',
    info: {
      macAddress: wsInfo.macAddress || 'AA:BB:CC:DD:EE:FF',
      model: wsInfo.model || 'Gateway Model 003',
      wifiFirmwareVersion: wsInfo.wifiFirmwareVersion || '1.0.0',
      btFirmwareVersion: wsInfo.btFirmwareVersion || '2.0.0',
      ipAddress: wsInfo.ipAddress || '*************',
      chunkingSupport: {
        enabled: true,
        maxChunkSize: maxChunkSize,
        maxSingleMessageSize: 2048,
        embeddedIndex: true,
        jsonHeader: true,
        // 新增：偏好的格式
        supportedFormat: wsInfo.preferredFormat || 'rawdata'
      }
    }
  };

  // ... 其餘代碼
}

// 更新消息處理邏輯
ws.on('message', async (data) => {
  try {
    // ... 現有的消息類型檢測代碼

    if (isJsonMessage) {
      const message = JSON.parse(data.toString());

      console.log('\n===== 收到消息 =====');
      console.log('原始數據長度:', data.length, '字節');
      console.log('消息類型:', message.type || '未知類型');

      // 檢查是否包含 rawdataFormat 欄位
      if (message.hasOwnProperty('rawdataFormat')) {
        console.log('Rawdata 格式:', message.rawdataFormat);
      }

      // 處理分片傳輸相關訊息
      if (message.type === 'image_chunk_start') {
        console.log('🚀 開始接收分片傳輸:', message);

        // 檢查格式信息
        if (message.rawdataFormat) {
          console.log(`分片數據格式: ${message.rawdataFormat}`);
        }

        chunkReceiver = new ChunkReceiver(ws);
        chunkReceiver.globalSimulateFailure = globalSimulateFailure;
        chunkReceiver.expectedFormat = message.rawdataFormat || 'rawdata'; // 記錄預期格式
        await chunkReceiver.handleChunkStart(message);
        return;
      }

      // 處理圖像更新消息
      if (message.type === 'update_preview') {
        console.log('說明: 收到圖像更新消息');

        const deviceMac = message.deviceMac;
        const imageCode = message.imageCode;
        const imageData = message.imageData;
        const rawdataFormat = message.rawdataFormat || 'rawdata';

        console.log(`圖像更新格式: ${rawdataFormat}`);

        // 處理 rawdata
        if (message.hasOwnProperty('rawdata')) {
          console.log('說明: 檢測到消息中包含 rawdata 欄位');
          try {
            saveRawData(message.rawdata, deviceMac || 'unknown', imageCode, rawdataFormat);
          } catch (err) {
            console.error('處理 rawdata 時出錯:', err.message);
          }
        }

        // ... 其餘現有處理邏輯
      }

      // ... 其餘現有代碼
    }
  } catch (error) {
    // ... 現有錯誤處理
  }
});

// 更新 ChunkReceiver 類
class ChunkReceiver {
  constructor(ws) {
    this.ws = ws;
    this.receivedChunks = new Map();
    this.chunkBuffer = new Map();
    this.currentChunkId = null;
    this.expectedTotalChunks = 0;
    this.duplicateCount = 0;
    this.receivedCount = 0;
    this.deviceMac = null;
    this.imageCode = null;
    this.expectedFormat = 'rawdata'; // 新增：預期的數據格式
    this.simulateFailure = false;
    this.globalSimulateFailure = false;
  }

  // 處理分片開始訊息
  async handleChunkStart(message) {
    this.cleanup();

    this.currentChunkId = message.chunkId;
    this.expectedTotalChunks = message.totalChunks;
    this.deviceMac = message.deviceMac;
    this.imageCode = message.imageCode;
    this.expectedFormat = message.rawdataFormat || 'rawdata'; // 記錄格式

    console.log(`📦 開始接收分片: ${message.chunkId}, 總分片數: ${message.totalChunks}, 設備: ${this.deviceMac}, 格式: ${this.expectedFormat}`);

    // 發送開始確認
    this.sendStartAck(message.chunkId, 'ready');
  }

  // 重組完整圖片
  async reassembleImage() {
    try {
      // 按順序重組分片
      const totalSize = Array.from(this.chunkBuffer.values())
        .reduce((sum, chunk) => sum + chunk.length, 0);

      const completeData = new Uint8Array(totalSize);
      let offset = 0;

      for (let i = 0; i < this.expectedTotalChunks; i++) {
        const chunkData = this.chunkBuffer.get(i);
        if (!chunkData) {
          throw new Error(`缺少分片: ${i}`);
        }

        completeData.set(chunkData, offset);
        offset += chunkData.length;
      }

      console.log(`🎯 圖片重組完成: ${totalSize} bytes, 格式: ${this.expectedFormat}`);

      // 保存重組後的圖片
      await this.saveReassembledData(completeData, this.deviceMac, this.imageCode, this.expectedFormat);

    } catch (error) {
      console.error(`❌ 重組失敗:`, error);
    }
  }

  // 保存重組後的數據
  async saveReassembledData(rawBuffer, deviceMac, imageCode, format = 'rawdata') {
    try {
      console.log(`準備保存重組後的 ${format} 數據為 bin 檔案，裝置 MAC: ${deviceMac}`);

      // ... 現有的數據驗證代碼

      // 根據格式調整檔案名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${format}_chunked_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
      const filePath = path.join(saveDir, fileName);

      // 寫入文件
      fs.writeFileSync(filePath, finalBuffer);

      console.log(`已成功將重組後的 ${format} 數據保存到: ${filePath}`);
      console.log(`數據大小: ${finalBuffer.length} 字節`);

      // 如果是壓縮格式，嘗試解壓縮驗證
      if (format !== 'rawdata') {
        try {
          const decompressed = decompressRawdata(finalBuffer, format);
          console.log(`解壓縮驗證成功，解壓縮後大小: ${decompressed.length} 字節`);

          if (finalBuffer.length < decompressed.length) {
            const compressionRatio = (finalBuffer.length / decompressed.length * 100).toFixed(1);
            console.log(`壓縮比: ${compressionRatio}%`);
          }

          // 保存解壓縮後的數據
          const decompressedFileName = `rawdata_decompressed_chunked_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
          const decompressedFilePath = path.join(saveDir, decompressedFileName);
          fs.writeFileSync(decompressedFilePath, Buffer.from(decompressed));
          console.log(`解壓縮數據已保存到: ${decompressedFilePath}`);
        } catch (decompressError) {
          console.error('解壓縮驗證失敗:', decompressError.message);
        }
      }

      // ... 其餘現有代碼
    } catch (error) {
      console.error('保存重組後的數據失敗:', error);
    }
  }
}

// 更新 saveRawData 函數
function saveRawData(rawdata, deviceMac = 'unknown', imageCode = null, format = 'rawdata') {
  try {
    console.log(`準備保存 ${format} 格式的數據為 bin 檔案，裝置 MAC: ${deviceMac}`);

    // ... 現有的數據處理邏輯

    // 根據格式調整檔案名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `${format}_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
    const filePath = path.join(saveDir, fileName);

    // 寫入文件
    fs.writeFileSync(filePath, rawBuffer);

    console.log(`已成功將 ${format} 數據保存到: ${filePath}`);
    console.log(`數據大小: ${rawBuffer.length} 字節`);

    // 如果是壓縮格式，嘗試解壓縮驗證
    if (format !== 'rawdata') {
      try {
        const decompressed = decompressRawdata(rawBuffer, format);
        console.log(`解壓縮驗證成功，解壓縮後大小: ${decompressed.length} 字節`);

        if (rawBuffer.length < decompressed.length) {
          const compressionRatio = (rawBuffer.length / decompressed.length * 100).toFixed(1);
          console.log(`壓縮比: ${compressionRatio}%`);
        }

        // 保存解壓縮後的數據
        const decompressedFileName = `rawdata_decompressed_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
        const decompressedFilePath = path.join(saveDir, decompressedFileName);
        fs.writeFileSync(decompressedFilePath, Buffer.from(decompressed));
        console.log(`解壓縮數據已保存到: ${decompressedFilePath}`);
      } catch (decompressError) {
        console.error('解壓縮驗證失敗:', decompressError.message);
      }
    }

    // ... 其餘現有代碼
  } catch (err) {
    console.error('保存數據時出錯:', err.message);
  }
}
```

## 測試驗證計劃

### 1. 單元測試
- Run-Length 壓縮器功能測試
- 格式選擇邏輯測試
- 壓縮效果驗證測試

### 2. 整合測試
- WebSocket 消息格式驗證
- Gateway 格式回報測試
- 端到端壓縮傳輸測試

### 3. 性能測試
- 壓縮處理時間測試
- 記憶體使用量測試
- 網絡傳輸效率測試

### 4. 兼容性測試
- 舊版 Gateway 兼容性
- 混合格式環境測試
- 錯誤處理和回退機制測試
```
