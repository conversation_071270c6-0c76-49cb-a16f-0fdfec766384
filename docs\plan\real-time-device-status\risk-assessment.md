# 風險評估與應對

## 1. 技術風險

### 1.1 WebSocket連接穩定性風險

#### 1.1.1 風險描述
- **網路不穩定**：用戶網路環境不穩定可能導致WebSocket連接頻繁中斷
- **服務器負載**：大量WebSocket連接可能對服務器造成負載壓力
- **瀏覽器限制**：某些瀏覽器對WebSocket連接數量有限制

#### 1.1.2 風險等級
**中等風險** - 可能影響用戶體驗，但有成熟的應對方案

#### 1.1.3 應對策略
```javascript
// 1. 增強重連機制
class RobustWebSocketClient {
  constructor() {
    this.maxReconnectAttempts = 10;
    this.reconnectDelay = 1000;
    this.exponentialBackoff = true;
    this.heartbeatInterval = 30000; // 30秒心跳
  }

  // 指數退避重連
  scheduleReconnect() {
    this.reconnectAttempts++;
    let delay = this.reconnectDelay;
    
    if (this.exponentialBackoff) {
      delay = Math.min(
        this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
        30000 // 最大30秒
      );
    }
    
    setTimeout(() => this.connect(), delay);
  }

  // 心跳檢測
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ping();
      }
    }, this.heartbeatInterval);
  }
}

// 2. 連接池管理
class ConnectionPoolManager {
  constructor() {
    this.maxConnections = 100;
    this.connectionTimeout = 60000; // 60秒超時
    this.idleConnections = new Set();
  }

  // 清理閒置連接
  cleanupIdleConnections() {
    const now = Date.now();
    this.idleConnections.forEach(ws => {
      if (now - ws.lastActivity > this.connectionTimeout) {
        ws.close();
        this.idleConnections.delete(ws);
      }
    });
  }
}
```

#### 1.1.4 監控指標
- WebSocket連接成功率
- 平均重連次數
- 連接持續時間
- 心跳響應時間

### 1.2 性能影響風險

#### 1.2.1 風險描述
- **頻繁更新**：大量設備狀態變更可能導致頻繁的WebSocket推送
- **前端渲染**：頻繁的狀態更新可能造成前端渲染性能問題
- **記憶體洩漏**：長時間運行可能導致記憶體洩漏

#### 1.2.2 風險等級
**高風險** - 直接影響系統性能和用戶體驗

#### 1.2.3 應對策略
```javascript
// 1. 智能防抖策略
class AdaptiveDebouncer {
  constructor() {
    this.baseDelay = 500;
    this.maxDelay = 2000;
    this.loadFactor = 0;
  }

  // 根據系統負載調整防抖延遲
  getAdaptiveDelay() {
    const loadMultiplier = 1 + (this.loadFactor * 2);
    return Math.min(this.baseDelay * loadMultiplier, this.maxDelay);
  }

  // 監控系統負載
  updateLoadFactor(cpuUsage, memoryUsage, connectionCount) {
    this.loadFactor = Math.max(
      cpuUsage / 100,
      memoryUsage / 100,
      connectionCount / 1000
    );
  }
}

// 2. 批量處理策略
class BatchProcessor {
  constructor() {
    this.batchSize = 50;
    this.maxWaitTime = 1000;
    this.pendingBatches = new Map();
  }

  // 智能批量大小調整
  adjustBatchSize(processingTime, queueLength) {
    if (processingTime > 500 && this.batchSize > 10) {
      this.batchSize = Math.max(this.batchSize - 5, 10);
    } else if (processingTime < 200 && queueLength > this.batchSize) {
      this.batchSize = Math.min(this.batchSize + 5, 100);
    }
  }
}
```

#### 1.2.4 性能監控
```javascript
// 性能監控器
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      updateFrequency: 0,
      averageProcessingTime: 0,
      memoryUsage: 0,
      connectionCount: 0
    };
  }

  // 收集性能指標
  collectMetrics() {
    const memUsage = process.memoryUsage();
    this.metrics.memoryUsage = memUsage.heapUsed / 1024 / 1024; // MB
    
    // 設置警告閾值
    if (this.metrics.memoryUsage > 500) {
      console.warn('記憶體使用過高:', this.metrics.memoryUsage, 'MB');
    }
    
    if (this.metrics.updateFrequency > 100) {
      console.warn('更新頻率過高:', this.metrics.updateFrequency, '/秒');
    }
  }
}
```

### 1.3 數據一致性風險

#### 1.3.1 風險描述
- **時序問題**：WebSocket推送和API查詢的數據可能存在時序不一致
- **網路延遲**：不同客戶端接收到更新的時間可能不同
- **部分失敗**：某些客戶端可能無法接收到狀態更新

#### 1.3.2 風險等級
**中等風險** - 可能導致數據顯示不一致

#### 1.3.3 應對策略
```javascript
// 1. 版本控制機制
class DataVersionManager {
  constructor() {
    this.deviceVersions = new Map(); // deviceId -> version
    this.globalVersion = 0;
  }

  // 生成新版本號
  generateVersion(deviceId) {
    this.globalVersion++;
    this.deviceVersions.set(deviceId, this.globalVersion);
    return this.globalVersion;
  }

  // 檢查版本新舊
  isNewerVersion(deviceId, version) {
    const currentVersion = this.deviceVersions.get(deviceId) || 0;
    return version > currentVersion;
  }
}

// 2. 數據校驗機制
class DataValidator {
  // 校驗設備狀態更新
  validateDeviceUpdate(update, existingDevice) {
    const errors = [];
    
    // 檢查時間戳
    if (update.lastSeen && existingDevice.lastSeen) {
      const updateTime = new Date(update.lastSeen);
      const existingTime = new Date(existingDevice.lastSeen);
      
      if (updateTime < existingTime) {
        errors.push('更新時間早於現有時間');
      }
    }
    
    // 檢查狀態轉換合理性
    if (update.status === 'online' && !update.lastSeen) {
      errors.push('在線狀態必須有lastSeen時間');
    }
    
    return errors;
  }
}
```

## 2. 業務風險

### 2.1 用戶體驗風險

#### 2.1.1 風險描述
- **視覺閃爍**：頻繁的狀態更新可能導致界面閃爍
- **信息過載**：過多的即時更新可能讓用戶感到困擾
- **學習成本**：新功能可能增加用戶學習成本

#### 2.1.2 應對策略
```typescript
// 1. 平滑動畫過渡
const DeviceStatusTransition: React.FC<{
  status: DeviceStatus;
  previousStatus?: DeviceStatus;
}> = ({ status, previousStatus }) => {
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (previousStatus && previousStatus !== status) {
      setIsTransitioning(true);
      const timer = setTimeout(() => setIsTransitioning(false), 300);
      return () => clearTimeout(timer);
    }
  }, [status, previousStatus]);

  return (
    <div className={`status-badge ${isTransitioning ? 'transitioning' : ''}`}>
      <DeviceStatusBadge status={status} />
    </div>
  );
};

// 2. 用戶偏好設置
interface UserPreferences {
  enableRealTimeUpdates: boolean;
  updateFrequency: 'high' | 'medium' | 'low';
  showUpdateNotifications: boolean;
  enableSoundAlerts: boolean;
}

const useUserPreferences = () => {
  const [preferences, setPreferences] = useState<UserPreferences>({
    enableRealTimeUpdates: true,
    updateFrequency: 'medium',
    showUpdateNotifications: false,
    enableSoundAlerts: false
  });

  // 根據用戶偏好調整更新行為
  const getUpdateDelay = () => {
    switch (preferences.updateFrequency) {
      case 'high': return 100;
      case 'medium': return 500;
      case 'low': return 2000;
      default: return 500;
    }
  };

  return { preferences, setPreferences, getUpdateDelay };
};
```

### 2.2 系統兼容性風險

#### 2.2.1 風險描述
- **瀏覽器兼容性**：舊版瀏覽器可能不支援某些WebSocket功能
- **移動設備**：移動設備的網路環境可能不穩定
- **企業防火牆**：某些企業環境可能阻擋WebSocket連接

#### 2.2.2 應對策略
```javascript
// 1. 功能檢測和降級
class FeatureDetector {
  static checkWebSocketSupport() {
    return typeof WebSocket !== 'undefined';
  }

  static checkConnectionStability() {
    // 檢測網路連接穩定性
    return navigator.connection?.effectiveType !== 'slow-2g';
  }

  static shouldEnableRealTimeUpdates() {
    return (
      this.checkWebSocketSupport() &&
      this.checkConnectionStability() &&
      !this.isMobileWithLowBattery()
    );
  }

  static isMobileWithLowBattery() {
    return (
      /Mobi|Android/i.test(navigator.userAgent) &&
      navigator.getBattery?.()?.then(battery => battery.level < 0.2)
    );
  }
}

// 2. 漸進式增強
class ProgressiveEnhancement {
  constructor() {
    this.fallbackMode = false;
    this.retryCount = 0;
    this.maxRetries = 3;
  }

  // 嘗試啟用即時更新
  async enableRealTimeUpdates() {
    if (!FeatureDetector.shouldEnableRealTimeUpdates()) {
      this.enableFallbackMode();
      return;
    }

    try {
      await this.initWebSocket();
      console.log('即時更新已啟用');
    } catch (error) {
      this.retryCount++;
      
      if (this.retryCount >= this.maxRetries) {
        console.warn('WebSocket連接失敗，切換到輪詢模式');
        this.enableFallbackMode();
      } else {
        setTimeout(() => this.enableRealTimeUpdates(), 2000);
      }
    }
  }

  // 啟用降級模式（輪詢）
  enableFallbackMode() {
    this.fallbackMode = true;
    this.startPolling();
  }

  startPolling() {
    setInterval(async () => {
      try {
        await this.fetchDeviceStatus();
      } catch (error) {
        console.error('輪詢獲取設備狀態失敗:', error);
      }
    }, 10000); // 10秒輪詢一次
  }
}
```

## 3. 運維風險

### 3.1 部署風險

#### 3.1.1 風險描述
- **版本兼容性**：新功能可能與現有版本不兼容
- **配置錯誤**：錯誤的配置可能導致功能異常
- **回滾困難**：出現問題時可能難以快速回滾

#### 3.1.2 應對策略
```javascript
// 1. 功能開關
const FeatureFlags = {
  ENABLE_REAL_TIME_DEVICE_STATUS: process.env.ENABLE_REAL_TIME_DEVICE_STATUS === 'true',
  DEVICE_STATUS_DEBOUNCE_DELAY: parseInt(process.env.DEVICE_STATUS_DEBOUNCE_DELAY) || 500,
  MAX_WEBSOCKET_CONNECTIONS: parseInt(process.env.MAX_WEBSOCKET_CONNECTIONS) || 1000
};

// 2. 漸進式部署
class GradualRollout {
  constructor() {
    this.rolloutPercentage = parseInt(process.env.ROLLOUT_PERCENTAGE) || 0;
  }

  shouldEnableForUser(userId) {
    if (!FeatureFlags.ENABLE_REAL_TIME_DEVICE_STATUS) {
      return false;
    }

    // 基於用戶ID的哈希值決定是否啟用
    const hash = this.hashUserId(userId);
    return (hash % 100) < this.rolloutPercentage;
  }

  hashUserId(userId) {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 轉換為32位整數
    }
    return Math.abs(hash);
  }
}
```

### 3.2 監控和告警

#### 3.2.1 關鍵監控指標
```javascript
// 監控指標收集器
class MetricsCollector {
  constructor() {
    this.metrics = {
      // WebSocket相關
      activeConnections: 0,
      connectionErrors: 0,
      reconnectionAttempts: 0,
      
      // 性能相關
      averageUpdateLatency: 0,
      updateThroughput: 0,
      memoryUsage: 0,
      
      // 業務相關
      deviceStatusUpdates: 0,
      failedUpdates: 0,
      subscriberCount: 0
    };
  }

  // 設置告警閾值
  checkAlerts() {
    const alerts = [];

    if (this.metrics.connectionErrors > 10) {
      alerts.push({
        level: 'error',
        message: `WebSocket連接錯誤過多: ${this.metrics.connectionErrors}`
      });
    }

    if (this.metrics.averageUpdateLatency > 1000) {
      alerts.push({
        level: 'warning',
        message: `更新延遲過高: ${this.metrics.averageUpdateLatency}ms`
      });
    }

    if (this.metrics.memoryUsage > 1000) {
      alerts.push({
        level: 'critical',
        message: `記憶體使用過高: ${this.metrics.memoryUsage}MB`
      });
    }

    return alerts;
  }
}
```

## 4. 回滾計畫

### 4.1 快速回滾策略

#### 4.1.1 功能開關回滾
```javascript
// 緊急關閉功能
const emergencyDisable = () => {
  // 1. 關閉功能開關
  process.env.ENABLE_REAL_TIME_DEVICE_STATUS = 'false';
  
  // 2. 斷開所有WebSocket連接
  websocketService.disconnectAllDeviceStatusSubscribers();
  
  // 3. 清理相關資源
  websocketService.cleanupDeviceStatusResources();
  
  console.log('即時設備狀態功能已緊急關閉');
};

// 監聽緊急關閉信號
process.on('SIGUSR2', emergencyDisable);
```

#### 4.1.2 數據庫回滾
```sql
-- 如果需要回滾數據庫變更
-- 1. 移除新增的索引
DROP INDEX IF EXISTS idx_devices_store_status_lastseen;

-- 2. 移除新增的字段（如果有）
-- ALTER TABLE devices DROP COLUMN IF EXISTS realtime_enabled;

-- 3. 恢復原有的觸發器或存儲過程
```

### 4.2 回滾檢查清單

#### 4.2.1 技術檢查
- [ ] 關閉功能開關
- [ ] 斷開WebSocket連接
- [ ] 清理記憶體資源
- [ ] 恢復原有API行為
- [ ] 驗證現有功能正常

#### 4.2.2 業務檢查
- [ ] 確認設備列表正常顯示
- [ ] 確認批量傳送功能正常
- [ ] 確認手動同步功能正常
- [ ] 通知相關用戶功能變更
- [ ] 更新用戶文檔

這個風險評估文檔涵蓋了實施即時設備狀態更新功能可能遇到的主要風險，並提供了相應的應對策略和回滾計畫，確保項目能夠安全、穩定地實施。
