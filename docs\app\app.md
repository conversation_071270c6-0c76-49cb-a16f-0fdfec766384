![](images/4fa7d281d0b389b51b44871a4b855db4b1d2b943056b9f6a31e70cfa42c54f35.jpg)  

# Domore 智慧云平台操作文档  

版本：V5.15.0  

# 深圳云里物里科技股份有限公司SHENZHENMINEWTECHNOLOGIESCOLTD  

# 目录  

一、简介 1  
二、适用对象  
三、功能说明.  
四、操作说明 n.  
五、授权认证.  
六、账号管理 3  
七、设备固件管理， 6  
八、设备查询.. 8  
九、配置管理 9  
十、模版仓库. 12  
十一、登录退出 13  
十二、门店管理 .15  
十三、系统数据 .18  
十四、权限管理 23  
十五、系统设置 .29  
十六、系统记录. .31  
十七、数据管理 .32  
十八、模板管理 .40  
十九、网关管理 52  
二十、设备管理 .57  
二十一、门店设置 .85  
二十二、统计分析. 93  
二十三、版本信息 .99  
二十四、DOMORE智慧云平台 APP操作简介 100  

# 一、简介  

云里物里Domore智慧云平台是ESL电子标签的控制管理平台，通过云平台能实现对系统内所有网关、电子标签的控制，并能与应用企业的系统对接，实现信息在电子标签上的实时显示。Domore智慧云平台主要功能包括：  

一门店管理一数据管理一设备管理一网关管理一记录管理一模板管理一用户管理一系统设置  

# 二、适用对象  

可应用于所有能替代传统纸质标签使用的场景，例如零售、办公、仓储、医疗等等领域，通过使用ESL电子标签代替传统纸质标签，实时控制电子标签的信息显示，实现传统场景的智能化、数字化升级。  

# 三、功能说明  

1.门店管理：在云平台上可以添加、修改、关闭/开启门店，2.数据管理：可以通过下载电子表格模板，填写数据信息后批量导入数据，也可以新增单个数据，修改和删除数据。3.标签管理：在门店里，通过下载电子表格模板，填写标签MAC地址后，批量导入标签，更新全部/单个标签，用标签绑定数据，更新标签显示内容，以及删除标签。  

4.网关管理：在门店里，可以新增、修改、删除、重新启动网关，给网关固件版本进行升级。5.记录管理：把用户的刷图和升级操作记录下来，对其结果进行筛选。6.模板管理：可以新增和删除模板，对模板进行编辑，并将模板绑定到指定的数据。7.系统管理：可以配置角色、用户以及权限，管理标签和网关的升级包，以及设置数据的字段等功能。  

# 四、操作说明  

在浏览器中输入URL地址：https://cloud.ylwlesl.com（公有云地址），按回车键，输入账号密码进入云平台登录页面，如下图所示：  

![](images/bd26f3709569ebea4135dcaeccbe225f00c1bc545e9fd50b6020d600e2465769.jpg)  

# 五、授权认证  

为了确保数据安全性，客户新部署的系统需要我司授权认证后才能进行操作，否则不能使用平台主要功能，例如刷图等功能。  

授权认证操作步骤如下（该步骤仅针对运维人员，客户无需获知运维账号信息）：  

1.登录运维账号进入授权认证菜单页；  
2.点击“授权申请”，填写授权所需信息，例如公司名称、联系人、电话等；  

# 填写完成后点击确定，提交申请；  

![](images/d9dcb5218762d414fb71320dedacfae06f3254d4e541264ebb165f8f1630dc08.jpg)  

3.后台审核，审核完成之后会生出一个证书文件：  

4.将证书文件上传至安装系统对应的目录下，即可止常使用平台功能；平台上也会展示授权开始日期和到期日期等相关信息。  

![](images/5d52cc337829ecc4ae96408fadd2cdb874bffee2a8aca20628369695f1d1c567.jpg)  

# 六、账号管理  

私有部署的客户可对平台账号进行管理，公有云用户则无需管理账号  

# 1.新增账号  

登录运维账号，选择“账号管理”，点击“新增”，在弹窗内输入对应的账号信息后点击确定即可新增账号。  

![](images/ad82be154ac3a969f65913a14e28e16d5a7c257f9be653735a51e455b9e719f8.jpg)  

# 2.删除账号  

登录运维账号，进入“账号管理”，点击“删除”按钮，若该账号下没有门店、数据、设备等信息即可删除该账号。  

![](images/bbb7a624e4fb217b1bdbf8a9a1cca6c34be5956a3a73d58bc763d104d372a5c6.jpg)  

# 3.编辑账号  

登录运维账号，进入“账号管理”，点击“编辑”按钮，即可编辑该账号信息。  

![](images/b76f797c6b3a6fbe784d410cb9be0bee89f3ad8352c5ae9dc0b33db18a7af67f.jpg)  

# 4.重置账号密码  

点击“重置密码”，将账号的密码重置为默认密码。  

![](images/0c2e6a06c9bc214d9a8564f3b2e755038338d35dc5749048a0e16aa35610a921.jpg)  

# 5.设置账号的状态  

点击“状态设置”按钮，启用/禁用账号。  

![](images/03f1eba371a06413369363fe484055cc9e374cda1c95755b8d61ab912113ac88.jpg)  

# 七、设备固件管理  

# 1.设备固件版本上传  

在运维账号的“固件管理”页面，显示设备固件列表，可以查看设备固件升级包的程序名称、上传时间、版本号和类型，上传设备固件升级包。首先选择设备固件版本tab页的“设备固件版本”，然后点击右上角的“上传”按钮，可以从本地上传设备固件升级包，上传成功后会在设备固件列表中显示。在门店-设备管理页，可以选择上传的设备固件升级包进行空中升级。  

![](images/ace58ac6222828d1fc36724a5dfe4de6a6c2a2726d23fad668460ac3283904b3.jpg)  

# 2.设备固件删除  

删除设备固件升级包：点击设备固件列表右边的“删除”按钮，弹出删除设备固件升级包的确认窗口，点击“确定”，该设备固件升级包删除成功。  

![](images/d038baaddd994cdc44567bc81bb01362fb1c5b1dedcf02cbeee7e9f2bf6b7a42.jpg)  

# 3.网关固件上传  

在“固件管理”页，点击“网关固件版本”，显示网关固件列表，可以查看网关固件升级包的程序名称、上传时间、版本号和类型  

上传网关固件升级包：首选选择网关固件版本，然后点击右上角的“上传WiFi固件”按钮，可以从本地上传网关"iFi固件升级包，上传成功后会在网关固件列表中显示；点击右上角的“上传蓝牙固件”按钮，可以从本地上传网关蓝牙固件升级包，上传成功后会在网关固件列表中显示。在门店-网关管理页，可以选择上传的网关WiFi固件升级包或者网关蓝牙固件升级包进行空中升级。  

![](images/b3b72e250089b1fe432f8ba2a4adcfa9ea235ed1d14a7b9a29186dfed06455ee.jpg)  

# 4.网关固件删除  

删除网关固件升级包：点击网关固件列表右边的“删除”按钮，弹出删除网关固件升级包的确认窗口，点击“确定”，该网关固件升级包删除成功。  

![](images/c83c6b918cd6abdd36c569cd1183df8b142c998f17f52009decc4b9158daecda.jpg)  

# 八、设备查询  

登录运维账号，在设备管理页面用户可查询当前服务器网关/标签设备具体在哪一个门店中。  

![](images/c8c43ac4ab2614ec0c15654dbbf9df5daf28b015054d35f25c714b50a3ddba90.jpg)  

# 九、配置管理  

# 1、服务配置  

登录运维账号，在“配置管理”的“服务配置”页面，用户可配置开启或关闭系统日志服务；用户可以启用或禁用系统日志服务。启用后，操作日志页面将显示在所有账号/账号的主页中。用户可以启用或禁用标签位置服务。启用后，标签位置将显示在所有账号/子账号的主页上。向服务器添加新屏幕信息的用户可以单击“设置”，启用默认标签覆盖模板。  

![](images/0e8b93787272af769a8aafe7ee75e96059ad45273718f529cec1be86bbf6dda9.jpg)  

![](images/729cbe22471bf7f87fcc31a84e8e06e3cb0fd9d5432180dd9974a140c41c7c55.jpg)  

# 2、屏幕管理  

登录运维账号，在“配置管理”的“屏幕管理”页面，用户可新增或修改服务器屏幕信息（注：1、请联系技术支持提供新增的屏幕信息；2、已存在的屏幕信息修改将会影响整个服务器请谨慎操作）。  

![](images/35737b88bbab69fc81ef1b7f96d3ac3f9d6ea033faac8c01036492f5db84b80f.jpg)  

![](images/2e6d68eeed3b2edf97dabab04ce24affd49043a892dee224fe60cca543149591.jpg)  

# 3、莱单管理  

登录运维账号，在“配置管理”的“菜单管理”页面，用户可修改所有账号显示的菜单名称。  

![](images/a2ff2d43b25458c239510e775997f807e86b3736a2aff8725b5fd828899b29d2.jpg)  

# 4、商户信息  

以管理员账号的身份登录。在“配置管理”模块中的商户信息上，可以对商  

# 户的1ogo信息进行设置，以及编辑和填写商家的基本信息。  

![](images/1c08be683c9adf54f64b1a86c3844ab76d984c662f9d72ef4a03bd493a4e16be.jpg)  

# 5、设备管理  

以管理员账号的身份登录。在“配置管理”模块中的“设备管理”菜单中可新增该服务器可用的设备列表。（注意：不在该列表内的设备将无法添加到门店账号内使用）  

![](images/a799f0c86d9778345376095b383dbb55a0e2af780a151b9f3c477a037f8c92c4.jpg)  

# 6、字体管理  

以管理员账号的身份登录。在“配置管理”模块中的“字体管理”菜单中可新增该服务器可用的字体库列表。在制作模板时可选择使用上传的字体做为设备显示字体；  

![](images/d804d827d69b41212ec9a76544d1dc73e41cad97aff676056b76e5b4b4c7beb9.jpg)  

# 十、模版仓库  

# 1、模板仓库  

以管理员账号的身份登录。在“模板仓库”页面上，管理员可以添加或编辑模板。所有账号都可以通过从“系统模板”页面的下拉菜单中选择“模版仓库”来查看模版仓库。账号无法编辑模版仓库，但只能单击“复制”按钮，将所需的模板复制到系统模板中进行编辑。如果“系统模板”中的模板需要在不同的门店中进行编辑，用户需要点击“复制”按钮将其复制到“门店模板”中进行编辑。  

![](images/e8c03cff2acdfea405c81a6ba93fc63960e88a74f98ad7c3875b8310a0e3e265.jpg)  

# 2、初始模板  

以管理员账号的身份登录。在“模板仓库”菜单页面上切换至“初始模板”，  

# 该页面允许用户自定义标签设备在解除绑定关系后刷的封面图。  

![](images/c8bba7ac33f76bf21ab75b6d0bc397ce75a8ef63ddd1e7aa2013b189601dd250.jpg)  

# 十一、登录退出  

# 1.用户登录  

云平台同一账号只能在同一设备上登陆操作，如同一账号在不同设备上同时登陆，后面登陆的账号会将前面登陆的账号挤下线。在云平台登录页面，输入账号名，密码默认“123456789”（请及时修改密码，以确保安全），验证码，点击“登录”即可。  

![](images/14d50904ef8cfb0cde6f249625c58424bcf828382679fe72117a1bc104e55a28.jpg)  

# 2.修改用户信息  

用户登录云平台后，点击右上角的头像或用户名，将会弹出下拉菜单，用户  

可以在这里修改密码。  

![](images/7c2204c6734f3e7da873b55cf2ccab368e9e00b3d8b3b77946c1ba8f209394cd.jpg)  

# 3.用户退出  

用户登录云平台后，点击右上角的头像或用户名，在弹出下拉菜单中，点击“退出登录”，用户退出登录，回到登录页面。  

![](images/2bfc92f1c105ef00a8b8f9ad287088bf152ad7e8d25030e2472ebfdb7bcbdeda.jpg)  

# 十二、门店管理  

使用账号登录云平台后，进入门店首页，在这个页面可以查看、添加、查找门店。  

![](images/fffc3676523f4ea3c5258d780407f285949c6fcd541191241a1fa84aaeb360a3.jpg)  

# 1.添加门店  

在门店首页，点击“新增+”按钮，会弹出添加门店窗口。  

![](images/7040ed7c61e56033ef5874391dbbe54009bb27522f76ab7d93134794df3002e2.jpg)  

在窗口中可以输入门店编号、门店名称、门店地址等信息，点击门店封面“+”会弹出选择上传文件窗口，可以选择JPG或PNG格式的图片文件，图片尺寸必须在370\*370以内。门店信息输入完后，点击“确定”，门店添加成功，可以在门店首页查看刚添加的门店。  

# 2.查找门店  

在门店首页，查找门店文本框内，输入门店的名称、编号或地址，可以查找出包含此条件的门店。点击“所有门店”，弹出下拉菜单，选择“所有门店”，可以查找出所有已开启的门店；选择“已关闭门店”，可以查找出所有已关闭的门店。  

![](images/773bdcbdb766860075c794eead4f6f5e9a9dc50e191a6dac075919833d95767e.jpg)  

# 3.关闭门店  

点击“关闭门店”按钮，弹出确认是否关闭当前门店。  

![](images/90396506f2b39cca85d635c9c97b443979b560d8054ab46c401de6c1ffc22cd2.jpg)  

点击“确定”，门店关闭成功，页面跳转到门店首页。  

![](images/b09dd3e369f31519dc0b75b7e03ab6a408e1b6015f3a5042c35836f72577ff7e.jpg)  

# 4.开启门店  

在门店首页，点击“所有门店”，弹出下拉菜单，选择“已关闭门店”，可以查找出所有已关闭的门店。点击要开启的门店，点击“开启门店”按钮，弹出确认是否开启当前门店，点击“确定”，门店开启成功，页面跳转到门店首页。  

![](images/29dd6e9ffd903c1b4418e632a3819a5445cdcca6cc74d7ce2ef085605a416f2c.jpg)  

# 5.修改门店  

点击“修改门店”按钮，弹出修改门店窗口，在窗口中可以修改当前门店的名称和地址，点击“保存”，门店信息修改成功。  

![](images/ebdeab1f07d25f6490f33e0198f907a062c203866120c04141c605196973c0e3.jpg)  

# 十三、系统数据  

系统数据包括系统数据和门店数据。对系统数据的修改将同步到所有门店或选定的门店，而对门店数据的修改将只应用于此门店。系统数据管理将在“系统数据”页面上进行操作。  

![](images/cee25d1754e512eef156f5aec4cd54160046ba73caca6129916418c2dfdae4ab.jpg)  

# 1.新增数据  

新增单个数据：在数据管理页，点击“新增””按钮。  

![](images/89fe6f06b9f0d9f3ffbd810a4460d1c300b2ea7c009c734c3be9f245be435bdf.jpg)  

弹出新增数据窗口，输入数据信息，其中ID为必填项。将数据信息填好后，点击“确定”按钮，数据添加成功（如果门店中没有此数据，此数据将添加到所有门店中；如果门店中已存在此数据，将覆盖所有门店中此数据的信息）。  

![](images/6b7495702dfdf65d0350c96a1daa68f8c136f7678a16a7a7f6c7c4bc100f459a.jpg)  

批量新增数据：在系统数据管理页，先点击“导入”按钮，选择下载模板将会下一个Excle表格的模板。  

![](images/2b0cb7b2e8d83c2ec83acc7dd69b757369c92a5bbdbd89ade5ad695044dc0772.jpg)  

在模板里输入数据的信息，其中ID为必填项。每行代表一个数据，可以输入多个数据信息，数据信息填好后，保存表格即可。  

再点击“导入”按钮，选择刚刚保存的表格，可将表格中的数据一次性导入到系统中，导入的数据会同时添加到指定的门店。  

![](images/bff9e2ae30686a651c4a78f9de5d2f16aa5981f3469db61737cccff1694a2298.jpg)  

# 2.查找数据  

在数据管理页，在搜索文本框内输入关键字，可以查找出包含此关键字的数据。  

![](images/be19de4e7be69c7a83ddf065a1ddcd0f3ceace25e1b89cd147663b3d15330337.jpg)  

# 3.数据导出  

在数据管理页，选择需要导出的数据点击批量导出按钮，可导出选择的数据点击全部导出按钮可导出全部数据。  

![](images/ddfa24f11b15e0c1a6ce4954fb2e7b67d8c2d79311475c54625af7be5476857d.jpg)  

# 4.修改数据  

在数据管理页，点击数据列表右边的“修改”按钮，弹出修改数据窗口，除ID外，该数据的所有信息都可以修改。修改完后，点击“确定”按钮，该数据  

修改成功，同时所有门店中的该数据信息都被修改。  

![](images/02b9c6c6a786d42c3f071b081bc7c1720bb5bbc3d10cf9279019647340dc914b.jpg)  

# 5.删除数据  

删除单个数据：在数据管理页，点击数据列表右边的“删除”按钮，弹出删除数据确认窗口，点击“确定”，该数据删除成功，同时所有门店中的该数据都被删除。  

![](images/061089b5542a6842a086d546acddd6a970a7dede221a1022b7316bc9d7b3857b.jpg)  

批量删除数据：在数据管理页，先勾选数据列表左边的复选框，再点击数据列表上方的“删除”按钮，弹出确认是否删除所选择的数据，点击“确定”，可将所选择的数据批量删除，同时所有门店中的所选择的数据都被删除。  

![](images/5de712326ec1aec7ef6570cd022b41a10f85ff5c71290273246e9fb213e9c259.jpg)  

# 十四、权限管理  

使用账号用登录云平台后，点击“权限管理”，进入角色/人员/权限管理页面。  

![](images/89001d73015fd0c25d8c9c3c2f8a3949b4ecc4b272b38ae4bffffeff2f6c55d5.jpg)  

# 1.角色管理  

# （1）.新增角色  

点击“新增”按钮，添加新角色，输入角色名称、角色描述并分配其角色权限，然后点击“确认”按钮。新增角色时可以指定该角色的字段编辑权限，指定后该角色可以拥有相应的字段编辑权限。  

![](images/c0a164fe3c1f5643d8707bdfb3b3f148b85a63b40b346c85eec77c5f11a21686.jpg)  

# （2）.修改角色  

点击“编辑”按钮，角色的名称和角色类型不能修改，其他信息，比如描述和权限，可以根据实际需要进行修改。  

![](images/197ece01bb55c2794e69f742ad0f3425c1e7185b3b4ae249fd71e4f1a7169136.jpg)  

# （3）.删除角色  

选择角色，点击“删除”按钮，在二次提示框中点击“确定”按钮，删除角色。  

![](images/33651acc9ead019789f998d3cc1eb7da569a49806c48b1dfdc0e3b88b896e5b1.jpg)  

# 2.人员管理  

# （1）.新增人员  

点击权限管理，选择人员管理界面，点击“新增”按钮，用户可根据实际需要，输入和配置新增人员的基本信息，比如手机号，邮箱等。输入完成后，点击“确定”按钮即可。批量新增人员，点击批量新增按钮即可，下载模板后，在模板内输入正确信息，然后导入，就可以一次性新增多个人员。  

![](images/aba06fcd843083eb8b9a66fe0b93e814896db9f8bfa158f7510d9ee5a1b2588f.jpg)  

# （2）.修改人员  

点击权限管理，选择人员管理界面，点击修改按钮。用户名不能修改，其他信息则可根据实际需要进行修改，修改完成后，点击“确定”按钮保存即可。  

![](images/2591d4bd909a5b0e8e36233416c92ba9c18c589480b07980aa7cc74e3f65d4d4.jpg)  

# （3）.删除人员  

点击权限管理，选择人员管理界面，点击删除按钮，在二次提示框处，点击确定按钮，人员即被删除。  

![](images/e6593c689375fa415b87f9303eddbdcab2094bbc65c977c90535bb28235ceece.jpg)  

# （5）重置人员账号密码  

进入人员管理页面，点击“重置密码”按钮，在弹出页面“确认”，将员工  

# 密码重置为默认密码。  

![](images/9ce429ccd969e3da68face592d1743e4bb0778b14f79c609ae84bf49f1423954.jpg)  

# （4）.查找人员  

点击权限管理，选择人员管理界面，点击查找按钮，可根据用户的状态、姓名、手机号等，查找人员。  

![](images/8bcef87990e26ed150c34c0133ad52dc195a39ea94b99b9d38c9b63910426fb1.jpg)  

# 3.权限分配  

# （1）.新增权限  

点击权限分配，点击“新增”，选择需要新增权限的用户（已新增的用户不可再次新增，若需变更请点击修改按钮），选择需要分配的权限类型，这里可选择系统权限和门店权限；选择好角色后点击确定即可分配成功；也可批量新增用户权限。  

![](images/8dcf821000b0a77c99ae26cb59aa39882673b5e17bcb5eab0138394c92ff222a.jpg)  

# （2）.修改权限  

点击权限管理，权限分配页面，点击编辑按钮可编辑当前选中人员的权限信息。  

![](images/1d06e1fc1a83427a112f71ad3c70c3bdd8a4e652ac92903566aaa768b4efa29a.jpg)  

# （3）.删除权限  

点击权限管理，权限分配页面，点击删除按钮可删除选中成员的当前权限。  

![](images/b6d7e3028ae8fe97e00b903bf5ed1b60c1ff19454f92ca3ed9b9e2003ebc7648.jpg)  
网址：wwwwwles.com  
电话：0755-2103 8160 邮箱：<EMAIL>  

# 十五、系统设置  

# 1.数据字段  

使用账号登录云平台后，点击系统设置的“数据字段”，这里可以设置多种字段类型，比如文本字段、数字字段、二维码字段和条形码字段，也有图标字段和图片字段。设置的字段将会显示在门店数据或者系统数据中。  

![](images/cfc45656a6724acebe366a6e1a34591c38f662ceaf83aae9a0ad9a6a741cecd0.jpg)  

# 2.商户信息  

使用账号登录云平台后，点击系统设置的“商户信息”，这里可以设置菜单1ogo（系统页面左上角显示1ogo）和标签栏1ogo（当前浏览器打开页的1ogo），还可以填写、修改“商户基本信息”。  

![](images/4e904ed1e9c56c07fbd60f605265303286a019b3d926d7de5e573ba0510d2665.jpg)  

# 3.网关设置  

使用账号登录云平台后，点击系统设置中的“网关设置”，这里可设置多个区域用于唤醒同一个标签的网关。唤醒网关适用于标签频繁移动的情况，例如一个标签从一个区域移动到另一个区域，而系统中未添加该网关，则需要在该功能板块中添加新移入区域的网关（该网关需未绑定于其他账号中），若区域唤醒网关未设置正确将无法在短时间内唤醒其他区域移动过来的标签。若无移动标签的情形或标签是长广播固件，可不用设置唤醒网关。  

![](images/5edb7e044ca4e85981db991f1732ee4eff7c916957403fa0af32ce678fb76ad0.jpg)  

# 4.参数设置  

使用账号登录云平台后，点击系统设置的“参数设置”，这里可以设置网关重启是否刷图、系统重推策略、标签离线时间设置、按键信息接收地址和配置接口公钥。  

![](images/4c6875812bcd26c3aefc35dcd95f009c6e03773ce81674929be528ff5de502ac.jpg)  
网址：wwwwesl.com  

电话：0755-2103 8160 邮箱：<EMAIL>  

# 5.密钥工具  

使用账号登录云平台后，点击系统设置的“密钥工具”，可在这里生成应用私钥和应用公钥，用于需要接口签名的接口调用。  

![](images/3fd68dbd41d6ce5bdc36e2fa6e2738fbe9e6557387012d85cce3027e0841387c.jpg)  

# 十六、系统记录  

在系统数据中导入门店数据后，会在系统记录模块中生成一条记录数据，如导人的数据中已有门店有该数据，且该数据已有绑定关系，则标签设备会刷图，系统记录模块也会展示该次导入的数据影响的门店数，以及刷图成功、失败数量和刷图详情，对于刷图失败的数据，可以点击“一键重推”，一键重推将对最新导人的数据中刷图失败的门店进行重推！  

![](images/e213885f75720bfd3473385450413f42b13d2034b994a346cb06a5d4b84ef260.jpg)  

![](images/21c04807d0b9bf228abacb3399a55732b17f90c8140614971a427ab919038317.jpg)  

# 十七、数据管理  

在门店概览页，点击导航“系统数据”按钮，进入数据管理页，在这个页面可以查找、新增、修改、删除数据。  

![](images/02a80940031ebc469b355c34633296298abe300bc5486f96eaaff490412affde.jpg)  

# 1.新增数据  

新增单个数据：在数据管理页，点击“新增””按钮，弹出新增数据窗口。  

![](images/edacc8715f493af1d8decc5672f4072735cbcc3ad9f28efffc3aa6baccd8ecd4.jpg)  

数据的字段可在系统初始化配置时设置，也可在系统管理里面设置。将数据信息填好后，点击“确定”按钮，数据添加成功（如果该数据ID已存在，会提示数据已添加）。  

![](images/909421d01c33cc228ae284a2d32ee0e7e2820aa32ee35b84f4d6d98f6d9b68b8.jpg)  

# 2.批量新增数据  

批量新增数据：在数据管理页，先点击“导入”按钮，选择“导入数据”。  

![](images/ced8659695dc75bf4f581681941b84b5da26804b254a6b58ffd78397651c04e2.jpg)  

然后选择下载模板，下载一个Exc1e表格的模板，在模板里输入数据的信息，其中ID为必填项。每行代表一个数据，可以输入多个数据信息，数据信息填好后，保存表格。  

![](images/bb61b7c406c590e31e05e2f1a0c9366ac92ecc1d0b22fb841e23155d5c4aecfe.jpg)  

再点击“导人”按钮，选择刚刚保存的表格，可将表格中的数据一次性导人到系统中。  

![](images/6a9cc767d84a270bb41f313112f3ace8abc4326a974aeaf174672c08db06d0a9.jpg)  
电话：0755-2103 8160 邮箱：<EMAIL>  

# 3.导出数据  

在数据管理页面，可选择需要导出的数据进行导出，或者点击“所有数据”按钮导出当前所有数据；也可以选择标签或LCD绑定关系选项，导出当前数据中存在的绑定关系。  

![](images/331d159b24232b23307b62be1c8b146d4b831a8fe42f932cad166be765767ca0.jpg)  

# 4.查找数据  

在数据管理页，在搜索文本框内输入关键字，可以查找出ID等包含此关键字的数据。  

![](images/418ff4f7909c68b3b8f7046eef3f3a2a7f3e02ddcb0dc5788310d0c9fd392a3a.jpg)  

# 5.修改数据  

在数据管理页，点击数据列表中的数据，在右边会显示该数据的详细信息（如图1），这里可以修改数据的属性，点击“保存”，数据修改成功。  

![](images/a1fdc2b03531b795e508216f7825d3d19ec5224f25b17b3a2a6eb51206bbbfb3.jpg)  

如果该数据已经绑定了标签，所有绑定的标签将进行刷图操作，刷图时页面左上角会显示刷图进度。若标签更新失败，则会显示在预警消息处。  

![](images/4f03f2380e8d2e77c89b41cd4c25a7f3d29af7d7482307c67f06ecfa08ba419e.jpg)  

点开预警消息，可以查看到标签刷图、绑定或升级失败，可直接点击重推按钮，让失败的标签再次进行刷图或绑定、升级操作。  

# 6.删除数据  

删除单个数据：在数据管理页，点击数据列表中的“删除”按钮，弹出确认是否删除数据，点击“确定”，数据删除成功。  

![](images/67e5441c230569f5dfad52233646674d4208797fdc549284431e0df42fcfb9ee.jpg)  

批量删除数据：在数据管理页，先勾选数据列表左边的复选框，再点击数据列表上方的“删除”按钮，选择“批量删除”，点击“确定”，可将选中的数据批量删除。也可直接点击“全部删除”，删除门店所有数据。  

![](images/43d1ca435920e109d8f8e0f10713a89adccd65089cf94e4cd43d9266fffc9dae.jpg)  

# 7.RGB设置  

进入门店数据，选择需要点灯的数据，点击RGB设置按钮，设置亮灯颜色、亮灯时长、亮灯亮度点击确定后，可将绑定了数据的标签点亮。  

![](images/cd657d0857490593f074dd07af2c152dc8585c9253eeec908e45c8df0cab9d1d.jpg)  

# 8.绑定标签  

进入门店数据，选择需要绑定标签的数据，点击操作中的绑定按钮，输入标签Mac和需要绑定的模板名称，点击确定即可将该数据与标签和模板进行绑定。（注：绑定的标签必须已经先绑定至该门店内，绑定的模板也必须是模板库中存在的模板）  

![](images/79faa2c32f9db7a034a053340442edb467d3c259e4d0f838986caac0da450e27.jpg)  

# 9.导入预存图  

在数据管理页面，点击“导入”按钮，再点击“预存图”按钮，可先下载模版，将预存图的数据输入表格中，表格完成后，将表格导入，点击“确认”按钮预存图导入完成。  

![](images/f889e732616dc77b5538f10200277c97a069b79620c2281b0933723b7b995c46.jpg)  

# 10.标签切页  

导人预存图后，在设备管理页面，选择对应的设备，选择完成后，点击“更多”中的“切页”按钮，即可完成该标签的切页。  

![](images/1c63e982c16503b1f1fb578c5c4104eb396e3eaa2cb3077773b4878f5e85bcd5.jpg)  

# 十八、模板管理  

模板分为系统模板和门店模板，系统模板会同步至所有门店中，门店内创建的模板只能在该门店内使用、修改和删除，门店内用户不能修改系统模板。使用账号登录云平台后，点击导航“模板”，进入模板管理页，在这个页面可以预览、新增、查找、编辑和删除模板。  

![](images/6d02c68703c1dd0248bfea86c6fe1cde9aa31340d5ebdd280e876a6a587f7c51.jpg)  
（系统模板）  

![](images/2f7cd6c55e94d563a707bdadc2e2aaa40a0b6e1d48fbbdc3853fa3c5f7e3547e.jpg)  
（门店模板）  
UM  

# 1．标签模板  

# （1）新增标签模板  

在模板管理页，选择标签模版，在点击模板列表右上角的“新增”按钮，弹出新建模板窗口，在窗口中输入模板名称，选择屏幕尺寸和颜色、模版类型（单数据模板/多数据模板）、屏幕方向（可选择显示内容旋转角度），点击“确定”按钮，模板添加成功，进入模板编辑页面。  

![](images/17a7cef3d359e05973cb236b3d07a2101a7b35d8e003dd8588b4db1abdafcd6c.jpg)  

# （2）查找标签模板  

在模板管理页，选择标签模版，点击模板列表左上方的全部模板、全部尺寸、全部颜色、查找模板名，将分别查找出门店\系统下对应尺寸的模板；选择颜色可以分别查找出黑白、黑白红、黑白黄屏幕的模板；在查找模板名中输入要查找的关键字，将查找出模板名称中包含此关键字的所有模板。  

![](images/9932b3885ca16cbb2ee5a014eee02190519fda43f0f709253f2ef5fdbe3d1d96.jpg)  

# （3）编辑标签模板  

在模板管理页，在点击模板列表中的模板，进人该模板的编辑页面。在编辑页面的左侧从上到下分别是动态元素、静态元素和图层三个子栏目。  

动态元素分为单行文本、多行文本、图片、图标、二维码、条形码；静态元素分为矩形、方形、圆形、椭圆、线段、文字、图片、图标。  

动态元素需要添加对应的动态字段后绑定对应的字段数据（若需要在标签上显示二维码，则需要在计划绑定的数据信息的二维码字段中输人相关信息，编辑模板的时候添加二维码元素，并建立该二维码元素与二维码字段的绑定关系，后续需要更新二维码内容时只需更新字段内容即可），实际刷图的时候需要在对应字段中添加、修改数据，即可完成对应字段信息的更新。  

静态元素可直接添加在模板中即可显示，无需与数据进行绑定，在后续刷图过程中，静态元素展示的内容均不会发生变化；如需修改静态元素的内容，需要在对应的模板中进行修改。  

![](images/407458802d10a36576f389bcd5cf0c6235623be5cd0269418b5e6acc8cb8bc3f.jpg)  

编辑页面的中部可显示当前的具体编辑样式，可以修改模板名称，放大缩小模板，可以在里面添加模板内容；选择一个文本，在编辑页面的右侧可以修改该文本的对齐方式、坐标、加粗、下划线、竖版排列等，编辑器中的所有元素支持自由旋转。  

选择编辑页面的任意元素后，点击左侧的图层栏目，可对图层的上下展示顺序进行移动调整。  

编辑完成后，点击“预览”按钮，可预览当前模板的整体展示效果，“确定”按钮，保存编辑后的模板并返回模板管理页；点击“取消”按钮，不保存编辑后的模板，直接返回模板管理页。  

![](images/f50896d359bf7be36e70ff855bc6ab1c94e8aac16ed038a58a570b612fa8ebb6.jpg)  

# （4）复制标签模板  

在模板管理页面，点击模板列表中的复制按钮，在弹窗中新输入模板名称，点击确定按钮即可复制该模板。  

![](images/8f9d7ede7d5b61b2f5d62736c64517c19585dc8588f85ac83bf8fea7af564cde.jpg)  

# （5）删除标签模板  

在模板管理页，点击模板列表中模板右下角的“删除”按钮，弹出删除该模  

# 板确认窗口，点击“确定”按钮，该模板删除成功。  

![](images/7da4468f0302213877c3b68b188400a09e1c83aec9319dbf8bcd83309592a8ed.jpg)  

# （6）导入标签模版  

在标签模版管理页，点击右上方的“更多”按钮，选择“批量导入”，在弹出的弹窗中点击“选择文件”，文件选择完成后，点击“确定”按钮，即可将模版导入到系统中。  

![](images/6130048c1ac0ad88b25dfe6425b644d4aa96376a29738a91cf86f129cdb4f02d.jpg)  

# （7）导出标签模版  

在模版管理页面，可选中需要导出的模版，选择完成后，点击右上方“更多”按钮，选择“导出选中/导出所有”，模版导出完成。  

![](images/b5d0b1a02760804729b5cbd754ff8a8091fdc9f3377a9fcb3c535aee90f059f8.jpg)  

# （8）新增标签多数据模版  

在模版管理页面，点击“新增”按钮，在新增模版时，可在“模版类型”中多数据模版，并选择数据绑定的数量。选择完成后，点击“确认”按钮，进入模版编辑页面。  

![](images/1a089225f53843259604321dad875226c59930c0853991711e1d37ecd4959085.jpg)  

多数据模版制作。进入模版编辑页面，可自定义相关元素，将元素与数据进行一一绑定。绑定完成后，点击“确定”按钮，多数据模版制作完成。  

![](images/ca619e354673aa2c695e6a7f845252195a7b6640829d53a6558035259b39dd06.jpg)  

# （9）标签多数据模版刷图  

建立好多数据模版后，在设备管理页面，选择设备后，对设备建立绑定关系。点击“绑定”按钮，在绑定数据的弹窗中，首先选择“多数据绑定”，然后选择一个多数据模版，多数据模版选择后，根据模版中可以绑定的数据，输入对应的数据id，最后点击“确认”按钮，完成多数据刷图。  

![](images/eec2ecf2711f7126e6f2ae63a42745dd8b393a1fd9520e511d8bafadca8f1e97.jpg)  

# 2.LCD模版  

# （1）新增LCD模版  

在模板管理页，选择LCD模版，在点击模板列表右上角的“新增”按钮，弹出新建模板窗口，在窗口中输入模板名称，选择屏幕尺寸和颜色、模版类型、屏幕方向，点击“确定”按钮，模板添加成功，进入模板编辑页面。  

![](images/d618fe15022801eb91c79a9400531dd62da68efd451ecb981df4d4c10e669631.jpg)  

# （2）查找LCD模板  

在模板管理页，选择LCD模版，点击模板列表左上方的全部模板、全部尺寸、全部颜色、查找模板名，将分别查找出门店\系统下对应的模板；在查找模板名中输人要查找的关键字，将查找出模板名称中包含此关键字的所有模板。  

![](images/de3ce5dc1236a7dc0ce3886ce9f2019e916906a38e17448d58d816f6c637fc83.jpg)  

# （3）编辑LCD模板  

模板管理页，在点击LCD模板列表中的模板，进入该模板的编辑页面。在编辑页面的左侧从上到下分别是动态元素、静态元素和图层三个子栏目。  

动态元素分为单行文本、多行文本、图片、图标、二维码、条形码、视频：  

静态元素分为矩形、方形、圆形、椭圆、线段、文字、图片、图标、多行文本。  

动态元素需要添加对应的动态字段后绑定对应的字段数据（若需要在LCD上显示二维码，则需要在计划绑定的数据信息的二维码字段中输入相关信息，编辑模板的时候添加二维码元素，并建立该二维码元素与二维码字段的绑定关系，后续需要更新二维码内容时只需更新字段内容即可），实际刷图的时候需要在对应字段中添加、修改数据，即可完成对应字段信息的更新。  

静态元素可直接添加在模板中即可显示，无需与数据进行绑定，在后续刷图过程中，静态元素展示的内容均不会发生变化；如需修改静态元素的内容，需要在对应的模板中进行修改。  

![](images/b4fa1629ca35dc327858b4fadc46040fed732f1711b45a2f556774575dfb5c8c.jpg)  

# （4）复制LCD模板  

在LCD模板管理页面，点击模板列表中的复制按钮，在弹窗中新输入模板名称，点击确定按钮即可复制该模板。  

![](images/f9a5853a46aeec691b51c1c31efd24577321095abb7ffeb375a6a4ef80ba90f5.jpg)  

# （5）删除LCD模版  

在LCD模板管理页，点击模板列表中模板右下角的“删除”按钮，弹出删除该模板确认窗口，点击“确定”按钮，该模板删除成功。  

![](images/03c669ffbcd67c487de9be155d9d81b541e07ed3db28e54af1d7d4a232313cd8.jpg)  

# （6）导入LCD模版  

在LCD模版管理页，点击右上方的“更多”按钮，选择“批量导入”，在弹出的弹窗中点击“选择文件”，文件选择完成后，点击“确定”按钮，即可将模版导入到系统中。  

![](images/b20d354799773d3ccedf1a81e9d2f2495ff1991d1bd03577c4ecc46910ece9f4.jpg)  

# （7）导出LCD模版  

在LCD模版管理页面，可选中需要导出的模版，选择完成后，点击右上方“更多”按钮，选择“批量导出/导出所有”，模版导出完成。  

![](images/6dfbbdd386f2f78109acbb858f93292e427367ad2d04620810e50bf4b8fea32d.jpg)  

# 十九、网关管理  

在门店概览页，点击顶部导航“网关”按钮，进入网关管理页，在这个页面可以新增、修改、删除、升级、刷新和重启网关。  

![](images/805da65126045523c16a634d10f20750eff70d96dda5ad6565dabdac3d5c9ffa.jpg)  

# 1.新增网关  

在网关管理页，在点击网关列表右上角的“新增”按钮，弹出新增网关窗口，在窗口中输入网关名称和MAC地址，点击“确定”按钮，网关添加成功，将在网关列表中显示。（如果网关MAC地址已经添加了，会提示网关已添加。）  

![](images/8262f167af8716650612bcea68402918dd96fd697a27085992d8fae6fa066856.jpg)  

# 2.修改网关  

在网关管理页，点击网关列表中的“修改”按钮，弹出修改网关窗口，在窗口中只能修改网关名称，MAC地址不能修改，点击“确定”按钮，网关修改成功。  

![](images/18df6450636732cb4a654b689efbd8d5e271d2ab4a82a50964a5f20d327644b2.jpg)  

# 3.删除网关  

在网关管理页，点击网关列表中的“删除”按钮，弹出删除网关确认窗口，点击“确定”按钮，该网关将从门店中删除。  

![](images/01b7f36694901db21d90e6d4afe0e61887736080e8db00b630abaea392430354.jpg)  

# 4.重新启动网关  

在网关管理页，点击网关列表中的“重新启动”按钮，弹出重启网关确认窗口，点击“确定”按钮，该网关将重新启动。  

![](images/8f84612f4ef5b38028ce5cdfe851fda7e68fb68aa7796d7c69c851547160fb7f.jpg)  

# 5.刷新网关  

在网关管理页，点击网关列表中的“刷新”按钮，弹出重启网关确认窗口，点击“确定”按钮，该网关状态将重新获取；  

![](images/2dc4cb73cedf1c4cf73715b57395ccacd30c3ffd6501cb072327023b84226665.jpg)  

# 6.升级网关  

升级网关蓝牙固件：首先选择要升级的网关，在点击网关列表右上角的“蓝牙固件升级”按钮，弹出蓝牙固件升级确认窗口如下图所示，选择要升级的固件版本，点击“确定”，确保网关的型号与选择的固件版本型号一致（否则会提示错误），门店内所有的网关将升级到选择的固件版本。  

![](images/490e22552ea9ee9ff45a9c52eb7caa098a6e67c545a9719bead5ccf2926a8b53.jpg)  

升级网关wifi固件：首先选择要升级的网关，在点击网关列表右上角的“wifi固件升级”按钮，弹出wifi固件升级确认窗口如下图所示，选择要升级的固件版本，点击“确定”，确保网关的型号与选择的固件版本型号一致（否则会提示错误），门店内所有的网关将升级到选择的固件版本。  

![](images/3fcb054a0b086829a80b5957deb6fc094965f3b06fa6a6df9de80d0b782b9784.jpg)  

# 7.配置网关  

网关添加成功后还要对网关进行配置，否则一直处于离线状态。先用手机连上网关的WiFi，然后在手机浏览器里输入：************进入网关配置页面，点击Service页，Service Access可以选择MQTT或HTTP，默认使用MQTT协议；UploadInterva1单位可以选，默认为1秒；URL选择ss1://+域名或服务器IP地址，端口号是9883，点击底部“APPLY”按钮，配置完成。  

![](images/ae8323adaa28a32a87ef4a6f1e57f73f64a86a9a587d7b046bc82d6e466ab3f9.jpg)  

# 二十、设备管理  

1.标签管理  

# （1）.查找标签  

在门店概览页，点击导航“设备管理”按钮，进入标签管理页。导入的标签会在显示标签列表里，可以查看标签的信息。  

![](images/447a17e1be59099332db075a9dd18dda2840b38111fe6931eeb47e152e585a99.jpg)  
电话：0755-2103 8160 邮植：contactamnew.com  

通过标签的屏幕尺寸查找：点击标签列表左上方的“全部设备”下拉按钮会弹出各个尺寸、颜色、设备状态等选项，选择相应的选项，将分别查找出对应屏幕尺寸的标签。  

![](images/32108a544a895c2a972d285cb2e158be2823f2c2c826fb3160e4e688dd2e7de4.jpg)  

通过标签MAC地址查找：在标签列表上方的搜索文本框内，输入要查找的MAC地址，将查找出对应的标签。  

![](images/ec8280faa0a610642c06a4518d9c23335dffc5ebcf456f4fceef3185cc62c4b7.jpg)  

# （2）.新增标签  

# 进人标签管理页。先点击“批量导人”按钮，选择导人标签表。  

![](images/9986b9ccdf9f9efe5e74b7ec322481c66a3b92a7223084220127ad2430f3a741.jpg)  

再到弹出的提示框里面点击“下载模板”，将会下一个Exc1e表格的模板，  

![](images/d0ec4880040f9c297bcee468c99a3717ff645e76ad7ccee5b001449915fbe085.jpg)  

在模板里输入标签的mac地址，每行代表一个标签，可以输入多个标签信息。标签信息填好后，保存表格，再点击“导入”按钮，选择刚刚保存的表格，可将表格中的标签一次性导人到系统中。  

若标签数量较少时可选择新增单个标签。进入“设备管理”页面，点击“更多”按钮，选择“新增”按钮后，填写新增标签的mac地址即可。  

![](images/473ebfb4b31bff2f77198d6bb99d102060fbf84b1274d03fdbec91fd06f02bc5.jpg)  

![](images/70254b1093ac3c41666545033908e65011ecdef3626a3d2b760f3ecec0a268a2.jpg)  

# （3）.标签唤醒  

第一次导入标签后，或者标签离线的时候，先选择标签，然后点击“更多”、“唤醒”按钮，唤醒标签，让标签在线（前提是网关正常在线，标签可以正常使用）。  

![](images/a95bf51ebfb93d8d008938e44c1652ede4fc3b22214ebe06ee76ae5253c1053f.jpg)  

全部唤醒：若标签离线数量过多，可先选择所有离线，然后点击“唤醒”按钮。  

![](images/906efec31a3990be094bbbc756598c37a347962ac1e66150fc4b44e40075a204.jpg)  

# （4）.标签绑定数据  

单个标签绑定数据：点击在线标签操作栏“绑定”图标，选择多数据绑定/单数据绑定，在添加该标签需要绑定的数据ID和模板名称，点击确定即可完成标签、数据、模板间的绑定关系。  

![](images/7802f38f0264df7b396f6fd5e2658289f07a329adaea8fbb691a0dfeb6f20fda.jpg)  

批量标签绑定数据：标签正常在线后，点击“批量导入”选择需要导入绑定关系的设备类型，单面屏幕和单条数据绑定选择单面屏绑定表-单数据绑定表'，单面屏和多条数据绑定选择“单面屏绑定表-多数据绑定表’，多面屏绑定关系的建立选择“多面屏绑定关系表’，若需获取模板点击提示框中的“下载模板”。  

![](images/6e802ad2285e6f03f274d918f4238e63e22018a2f96d0d0e61af7fd2cb4bbaac.jpg)  
网址：wwwwesl.com   
电话：0755-2103 8160  

点击“批量导人”按钮中的对应的绑定表，选择填写好的数据表后，选择“导入”，数据和标签绑定成功；若绑定失败，可下载导入处理结果文件查看绑定失败的原因。  

![](images/a3b8f3520946877dd329ae3028a779c84e7510d17007c46107ce9eafa7f83ff2.jpg)  

# （5）.删除标签  

删除单个标签：在标签管理页，点击标签列表中的“删除”按钮，弹出确认是否删除标签，点击“确定”，这个标签将会从门店中删除。  

![](images/f4a095657fd9f4e336ad3ecbf930cf09422026e13ae7f75793c1dc8205812e14.jpg)  

批量删除标签：在标签管理页，先勾选标签列表左边的复选框，再点击标签列表上方的“删除”按钮，弹出确认是否删除选中的标签，点击“确定”，可将选中的标签从门店中删除。  

![](images/b78df0004213b2913ec801ca0c37646ce29b1697476449ea4e383b10c11b25a3.jpg)  

# （6）.更新标签  

在标签管理页，选择标签，点击标签列表右上方的“刷图”按钮，弹出确认是否更新标签，点击“确定”，门店中所有已经绑定数据的标签会更新显示的图片。  

![](images/adc09d007ef4f52d6d8b377a06be830a62befacd0aa803caee76c342bc789cd1.jpg)  

# （7）.升级标签  

设备固件升级时，系统会验证每个价签升级的固件是否正确，如果不正确，记录中反馈，且升级错误的标签包无法在预警消息处重推。标签升级时只成功和标签相匹配的版本。  

升级单个标签：在标签管理页，点击标签列表中标签，在右边会显示该标签  

# 的设备信息（如果该标签已绑定了数据，还会显示预览）。  

![](images/a79fae1a609787ba47326a8dbe5dda99a9f12f68f914d0913358f73b151219c1.jpg)  

点击固件版本右边的“升级”按钮，弹出升级确认窗口如下图，选择要升级的固件版本，点击“确定”，确保网关在线，标签将升级到选择的固件版本。  

![](images/f543c8180b4412e2a0422a1b2a1e7f449d1437faf658091412868d85f59cbe96.jpg)  

升级全部标签：在标签管理页，选择标签，点击标签列表右上方的“更多”、“升级”按钮，弹出升级确认窗口，选择要升级的固件版本，点击“确定”，确保网关在线，门店中所有的标签将升级到选择的固件版本。  

![](images/b4e2a6e200c61513bca5963736b26a8a984ccf1833d63cc59b9361a45a5858f5.jpg)  

# （8）.备注/编号标签  

在标签管理页点击“批量导入”选择“导入备注/编号表”，可批量新增标签备注和标签编号。  

![](images/e3664c4ac397f58e2c6025f3234e5cd9813dfc3ef331ea1ca91bb3cd85ab7d1b.jpg)  

同时也可选择单个标签，点击标签后面的“编辑”按钮进行备注和编号。  

![](images/a2da54ec0a19ea3080e07dad394666ca7e0a01433e5109a0e468e15ba3286522.jpg)  

# （9）.标签导出  

在标签的管理页面，先勾选标签列表左边的复选框，然后在点击“导出”按钮，可选择只导出选中的标签，或直接导出全部标签。  

![](images/2a27c568840dc204498d82ecbdfb046a8d2a8d0a6b72afaaa7f241855b382672.jpg)  

# （10）.标签点灯  

如需点亮标签上的LED灯，可先选择标签设备，选择后，点击“更多”下的“点灯”按钮，选择需要亮灯的颜色和时长，也可关闭灯光。  

![](images/9a17c7241886aee3fa9f63eb81fcd1416e5b7f28d0f62fe147c5a121516a1db4.jpg)  

# 2.警示灯管理  

# （1）查找警示灯  

在“设备管理”栏目左上角选择“警示灯”，点击“全部设备”，可以根据警示灯的状态筛选警示灯，也可在“搜索”框中输入警示灯的Mac地址查找警示灯。  

![](images/6daf6af5ecc9d5d3ffa54a4f5e12fcab034e5041431d2b103a713299c99fc985.jpg)  

# （2）导入警示灯  

进入设备管理菜单，选择警示灯设备，点击批量导入按钮，在弹出的窗口中选择“下载模板”，在下载的模板中将需要导入的警示灯Mac地址和激活秘钥填写后，再导入该文件。  

![](images/1e36e90dd6a70636b0c9eacebcbf704b64965e765beb9c52ed1f8fd1ec344c62.jpg)  

# （3）唤醒警示灯  

第一次导入警示灯或者警示灯离线时，可以选择警示灯，然后点击唤醒按钮即可唤醒离线的警示灯。  

![](images/c41de223c32ee6b50b0c2b7f4f06f7d33c2b21b3d957c4c0bf4ec968b3b811eb.jpg)  

# （4）升级警示灯  

进入设备管理，选择警示灯，选择需要升级的警示灯，点击升级按钮，在弹窗中选择需要升级到的版本确定即可。升级结果在操作记录中可查看。  

![](images/01ab0157bab49c045063b85687f638b17dd7bf4e732d24e229403767200b95a0.jpg)  

# （5）警示灯点灯  

进入设备管理，选择警示灯，选择需要点亮的颜色，点击灯的颜色即可。  

![](images/f0b2308c552c3161d615c6d93b27918ef0d14a62129dadc0cb578362ebce100b.jpg)  

# （6）警示灯绑定标签  

进入设备管理，选择警示灯，点击绑定按钮或导入绑定表；选择需要与该警示灯绑定的标签，点击确定按钮即可完成警示灯与标签的绑定。  

![](images/86ee7baa0e4c6a39374189bfb52fac5fad829456e4c6d1281febed5108674e61.jpg)  

# （7）删除警示灯  

删除单个警示灯，点击警示灯后的“删除”按钮，在删除弹窗中点击“确认”按钮，即可删除警示灯。批量删除，先勾选需要删除的警示灯设备，再点击“更多”下的“删除”按钮，在批量删除弹窗中，点击“确认”按钮，即可批量删除设备。  

![](images/5d43d9526bf46b679076e2fc2e7e6cf36e078218aa7c445c8fb638ccb8fff679.jpg)  

![](images/8018e7d3982980d3975f84a2ac3c98b7d828a229f58f192afd44d32af7e9a821.jpg)  

# （8）导入备注/编号表  

在警示灯设备管理页点击“批量导入”选择“导入备注/编号表”，可批量新增标签备注和标签编号。  

![](images/d8d0c0dd0f368961358dd0e3f38e4e3acc30599dc4bff04a224e14ab305f9219.jpg)  

同时也可选择单个警示灯，点击警示灯后面的“编辑”按钮进行备注和编号。  

![](images/a0f54b67ea8e9af9407fe7c048bce0d0cbddf8cb47ca55d3d38da30615708623.jpg)  

# （9）导入绑定表  

在警示灯设备管理页，点击“批量导入”下的“导入绑定表”按钮，下载绑定表的模版，填入需要绑定的标签设备和警示灯设备，填写完成后，将表格导入平台，即可批量完成标签设备和警示灯设备的批量绑定。  

![](images/082e13b165e1f1ff399f0ca8fbafb09cd31795eb6ab646ceedcd63f08490aeb1.jpg)  

![](images/1fc27cf179418f2b520fac990013f4dd6091291ee5d600dfdfd4aff24bb78a29.jpg)  

# 3.毫米波雷达管理  

# （1）查找毫米波雷达  

在“设备管理”栏目左上角选择“毫米波雷达”，在“搜索”框中输入毫米波雷达的Mac地址查找毫米波雷达。  

![](images/d6a588ad99e8982086596b64035d5fd780e30768fa0009e12992bc19f082382f.jpg)  

# （2）导入毫米波雷达  

进入设备管理菜单，选择毫米波雷达设备，点击“批量导入”按钮，在弹出的窗口中选择“下载模板”，在下载的模板中将需要导入的毫米波雷达Mac地址填写后，再导入该文件。  

![](images/b7549148df1d3ab717eb9779b5b588e5563b79dc280c9cacf342c14d65f189f0.jpg)  

# （3）新增毫米波雷达  

进入设备管理菜单，选择毫米波雷达设备，点击右上角“更多”，点击“新增”按钮，在新增设备的弹窗中输入毫米波雷达的MAC地址，输入完成后，点击“确认”按钮，毫米波雷达设备新增完成。  

![](images/89e64d7b0ff5a7f323fb21847e9606c61254806e39476a3088c85c8f40477773.jpg)  

# （4）删除毫米波雷达  

在毫米波雷达设备管理页面，点击设备列表后的“删除”按钮，在删除弹窗中点击“确认”，即可删除设备。  

![](images/294b8d2a43a184e2882dd5ad74610fda6dd70a2802d904434ba16da819b03fef.jpg)  

# 4.LCD设备管理  

# （1）查找LCD设备  

在“设备管理”栏目左上角选择“LCD设备”，在“搜索”框中输入LCD的Mac地址查找LCD设备。另外可根据设备状态筛选LCD设备。  

![](images/74e81397eaa66968bf27d6ad4b94dc3f4eac1c6c332866efc51786c6a1868d2f.jpg)  

# （2）导入LCD设备  

进入设备管理菜单，选择LCD设备设备，点击“批量导入”按钮，在弹出的窗口中选择“下载模板”，在下载的模板中将需要导入的LCD设备的Mac地址填写后，再导入该文件。  

![](images/43375a96726e3ed2da2be0caeb9d5c733048ff99d0f110f2115f1ebdf31c9b6d.jpg)  

# （3）新增LCD设备  

进入设备管理菜单，选择LCD设备，点击右上角“更多”，点击“新增”按钮，在新增设备的弹窗中输入LCD设备的MAC地址、设备名称、蓝牙名称，完成后，点击“确认”按钮，LCD设备新增完成。  

![](images/265519292c65798ee5f927925d65f1ba4f009e188ab34890494ecae2eec582d5.jpg)  

# （4）删除LCD设备  

删除单个LCD设备，点击LCD设备后的“删除”按钮，在删除弹窗中点击“确认"按钮，即可删除警示灯。批量删除，先勾选需要删除的LCD设备，再点击“更多”下的“删除”按钮，在批量删除弹窗中，点击“确认”按钮，即可批量删除  

![](images/acf3607d6e62412702d54fc125d59866f0a34a1e8aabe294c05d34fc6375cc16.jpg)  
设备。  

# （5）导出LCD设备  

在设备的管理页面，先勾选LCD设备列表左边的复选框，然后在点击“导出按钮，可选择只导出选中的LCD设备，或直接导出全部LCD设备。  

![](images/3b621c04aa92ad89f5e533226611fb67093caa20f74988bde8be0b56f5a60260.jpg)  

# （6）升级LCD设备  

升级单个LCD设备：在设备管理页，选择LCD设备，点击设备列表中LCD设备，在右边会显示该LCD设备的设备信息，点击固件版本右边的“升级”按钮弹出升级确认窗口如下图，选择要升级的固件版本，点击“确定，LCD设备将升级到选择的固件版本。  

![](images/2a984e0771313a13aa78ee24d6eb31b186743b0458a280cf91c3be9114d1aba9.jpg)  

![](images/f1a63be2ee394a4963e68cbfa1eed7c904353d2e7016d16f3b9265575bcb06a6.jpg)  
Domore智慧云平台使用手册  

升级全部LCD设备：在设备管理页，选择LCD设备，点击设备列表右上方的“更多”、“升级”按钮，弹出升级确认窗口，选择要升级的固件版本，点击“确定”，门店中所有的LCD设备将升级到选择的固件版本。  

![](images/8221303db15c93f6ae3ab7ecef4efe7d1118a827044e49622c1d1f0c51ad31a4.jpg)  

![](images/f49e444e88cb8f4df7402b7acba3cc424613921aee55dad5f92182e5ae4b50d2.jpg)  

# （7）编辑LCD设备  

在LCD设备后点击编辑按钮，在弹出的弹框中可编辑设备的名称、备注和编号，点击确认，编辑完成。  

![](images/4a89554a7ab75a2a9e832cfc38c1a386195ee38121d9bdd0b6c137af1b68e823.jpg)  

# （8）LCD设备刷图  

LCD设备绑定数据：点击在线设备后的“绑定”图标，在弹出的弹窗中进选择多数据绑定/单数据绑定，再添加该标签需要绑定的数据ID和模板名称，点击确定即可完成设备、数据、模板间的绑定关系。  

![](images/041996ffd7a5de44d05600ac101163eadf09f9ffd50d269923911b7276acde6b.jpg)  

# （9）更新LCD设备  

在设备管理页，选择LCD设备，点击设备列表右上方的“刷图”按钮，弹出确认是否更新LCD设备，点击“确定”，门店中所有已经绑定数据的LCD设备会刷图。  

![](images/7acf84fe34a66ccac57b06811b4a5ebfbc126de2ff25844e3657a6d29cf0ad7a.jpg)  

# 二十一、门店设置  

# 1．标签分组  

# （1）新增分组  

使用账号在门店设置中，可以对门店内的标签进行分组管理，子账号不可进行分组操作。点击“新增”按钮，在新增弹窗中，输入分组名称、分组描述、以及该分组可操作账号。输入完成后，点击“确定”按钮，分组新增完成。【未新增分组时子账号默认可操作所有设备，新增该分组可操作的账号后，该账号只能操作该分组内的设备，不可操作其他设备】  

![](images/2b723dcaa7c0dbc1ccb9553e8d7f6af52ec35e968460d8e736a4fe09db103e26.jpg)  

# （2）编辑分组  

点击分建立的分组后的“编辑”按钮，即可修改分组的名称和描述，以及可以操作该分组的子账号。  

![](images/05074dae0373bf70de50f8baa173c019350c297a36fd13b57c2d83f53dd01757.jpg)  
网址：wwwwylwlesl.com   
邮箱：<EMAIL>  

# （3）分组中添加标签  

点击已建立的分组的“详情”按钮，进入标签详情页面，点击页面中的“新增”按钮，选择需要添加到该分组的标签mac，点击“确定”按钮，即可在分组中添加标签。  

![](images/245129623c86dc5c6409e411fbed76d8c97bb67c12f26160d38decb7dea8a5a2.jpg)  

# （4）标签批量导入  

点击分组操作的“导入”按钮，可进行导入模版的下载，下载后填入标签的mac地址，即可将标签批量添加进分组中。  

![](images/51cdbb0a4c40d5be58dceb119079cad6c4f06db11e57d7a7ee5b9566c32658ec.jpg)  

# （5）删除分组  

点击分组后的“删除”按钮，弹出删除弹窗，点击“确认”按钮，即可删除该分组。  

![](images/a0acd70b11d6d1880977c641481f3d265ec44b64883a302c79274b9bbe543684.jpg)  

# 2.参数设置  

# （1）基础设置  

可在参数设置页面，设置标签监听间隔时间和广播间隔时间。设置完成后点击“保存”按钮即可。  

另外可点击获取监听间隔和获取广播间隔选项，批量获取设备的监听间隔/广播间隔。  

邮箱：<EMAIL>  

![](images/3481b9e08f75ca28490e4681d992447b02366d9913480ac05915e734c42fb493.jpg)  

# （2）预警设置  

首先选择需要预警的项目，选择后，可根据不同的项目选择立即预警、按日预警或按月预警。选择好相关预警项后，需填入接受预警消息的邮箱，最多可填入三个邮箱，填写完成后点击“保存”按钮即可。  

![](images/92b7090f74d65911d97cce7122c52f5c33b0d78759be0621b0d0b25f2c5d4e33.jpg)  

# 3.刷图计划  

# （1）新增刷图计划  

在刷图计划页面，点击“新增”按钮，然后可输入刷图计划名称、选择“单词触发”或“循环触发”（如选择“循环触发”，还需选择“执行周期”以及“有  

# 效时间”）、选择执行时间、设置变更内容。  

![](images/b4278ca4b78b9ea3998be35dfc4754ac2467b3b9a11016872376772f14300ee9.jpg)  

设置变更内容。可选择“单次导入”，然后点击“导入数据”按钮，可先选择“下载模版”，在表单中输入刷图计划数据，信息输入完成后，导入excle表格，点击“确定”按钮，刷图计划数据导入完成。  

![](images/1c6052505c0ad3078f00e049e4a7192a0cb036968f786a420a8c700a72c9b690.jpg)  

择”或“按数据选择”。点击“按设备选择”后，选择需要的设备，然后对已选择的设备绑定模版和数据，以及设置相关字段。点击“按数据选择”，选择需要的数据，然后对已选择的数据绑定设备和模版，以及修改相关字段。设置完成，点击“确认”按钮，刷图计划设置完成。  

![](images/021363aad505aafcb9d7e5a10d2e6029bcc57dba6d81be26e642ff9846603fbe.jpg)  

![](images/8e89112846b99e4632e15950b7fc57afb69cf684a200be2377ea128ab0079af1.jpg)  

PTR  

![](images/bf57868500a1f44204f8e8d43bd25d794104d28340b131d1b862e630ddbf398a.jpg)  

![](images/b8f95b94bdbaf56c32104c4b1bf5652f68ea016b6b4298542463d08b3991a504.jpg)  

# 4.模板策略  

用户可在门店设置/模板策略出可新增模板策略，当门店数据发生变化且符合对应的策略后，可以实现模板自动切换的效果。模板策略可选择定义的某一个动态字段与另一个动态字段或输人的文本内容做运算比较，同事可选择一个模板进行切换，如门店的数据符合该策略则该数据绑定的标签的模板将自动完成切换。若多个策略同时存在且相互矛盾，系统将不执行任何一个策略。现支持的运算符有：大于、小于、等于、不等于、为空、不为空。  

![](images/deb61f6dd824ab64051a3a8454d88ea867d916db75e726b7f566e3395d21ed14.jpg)  

# 二十二、统计分析  

# 1.操作记录  

# （1）.搜索记录  

在操作记录中可以查看用户的操作，比如刷图和升级，记录中会显示操作的结果，时间以及操作人等信息。可以根据这些信息去筛选搜索记录。  

![](images/99c12a8aaac6f477f327904c25d180e38dd48e79641fbe22197b1c2390070301.jpg)  

# (2）.列管理  

# 在操作记录中可选择列管理按钮，管理页面需要显示的列。  

![](images/4c1a895d437746aaaada4dede3f74d12c2a70bf20a49ae3d02fcf129a316f6d0.jpg)  

# （3）.导出记录  

在操作记录中点击导出按钮，可选择导出的具体列数据，筛选后导出，  

![](images/ce4c3c71f6592be241593ffdd8e4a5316374129e2151a5a09796e47242391e0d.jpg)  

# 2.刷图统计  

在系统设置页，点击左侧“统计分析”，可在“刷图统计”中按天查看刷图的详细情况。  

![](images/a656f3633e1eade0826c62054eb36d3996078d9f2d0af0cad5e910559032df0d.jpg)  

# 3.网关离线  

在网关离线页面可查询网关状态变化情况：  

![](images/433f2a58f67769882e490f9dc198ed3a6994d20ace73d2adac45564b494ef034.jpg)  

# 4.数据变动  

# （1）搜索数据变更数据  

在数据变动记录页面，可以根据时间以及数据的ID去查询数据变更记录  

![](images/f2210bc0ba06e6930dd99d57175b55d4d9934306cf55a61a383fb819f31c9f6d.jpg)  

# （2）查看数据变更详情  

点击每条数据右侧的详情按钮，可查看该条数据的变更详情。  

![](images/08655b2efd1dcd7d781373913c2b013b2396bfaa9a25bdf86300b197f20b25a6.jpg)  

# （3）数据导出  

点击数据变动页面右上角的“导出”按钮，即可导出数据变动的数据  

![](images/a3785a8f9aadce414200f5cc3b26fb0bed803f54d3268df843c0a650fe276db6.jpg)  

# 5.人流统计  

# （1）查看人流情况  

在人流统计页面，输入毫米波雷达的mac，或选择日期，可查看毫米波雷达设备的人流统计情况。  

![](images/3d2f2b91298342a710c1a1a80db5cc5edac4d5519a33f7e07e8a3bc30d6b3c61.jpg)  

# （2）切换列表展示  

在人流统计页面，点击右上角的“列表展示”按钮，可选择列表展示或图表展示。  

![](images/b9127ae923ed2be20cff8150dc245e8feb629d84277cb69a63a61747cedcad18.jpg)  

# （3）导出人流统计数据  

在人流统计页面，点击右上角“导出”按钮，即可导出人流统计数据  

![](images/2f895cc5265858491dda98a6d075ac834f3fe7ce085a0ecfd8edefee31226dd0.jpg)  

# 6.温湿度统计  

在温湿度统计页面可查看温湿度传感器上传的温湿度情况，可以通过搜索指定设备Mac查看该设备数据，也可根据时间搜索，导出记录。  

![](images/e38ee537276883cf5ed6e54fcd692be72926b8ecf032ea228cb39ab1ac9239ca.jpg)  

# 二十三、版本信息  

使用账号登录云平台后进入系统管理页面，或者进入门店后，在页面的左下角即可查看版本信息。  

![](images/c3b0c459f5de0a88c6a371e2efcd1ff581d9adc78ddcf2108dd48242c11f9eb5.jpg)  

# 二十四、DOIORE智慧云平台APP操作简介  

# 1.APP下载  

苹果手机用户在APPstore中搜索C1oudTag即可下载  

![](images/80a6988dfb3ada983d395dd059e08b550c408a1fb3c93bf333b87ccbb557c18d.jpg)  

安卓用户在华为应用市场搜索下载C1oudTag即可；海外用户可在谷歌应用市场搜索下载C1oudTag。若无法正常下载，可联系我司商务经理直接提供对应手机系统的APP安装包。  

# 2.APP登录/退出  

APP登录账号和平台登录账号一致，登录服务器填写对应平台登录地址即可。  

![](images/cb651e0957021998323e765ec0e84c979761b564b0372662a5a3adb09ebbad7a.jpg)  

登录后若需退出可点击右下角“个人中心”、“设置”，点击退出“登录按钮”即可退出APP。  

# 3.切换语言  

目前APP支持中英文切换。登录APP后点击右下角“个人中心”、“设置”，即可切换语言。  

<html><body><table><tr><td><</td><td>09:39 设置</td><td></td></tr><tr><td>语言设置</td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td>退出登录</td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr></table></body></html>  

# 4.标签绑定  

标签如需完成刷图，需要绑定刷图数据和刷图模板。登录APP选择归属门店后，点击“标签绑定”，录入或扫描选择标签，选择需要绑定的数据和模板保存即可完成绑定。  

![](images/a410dbeffbd2eba38c0b7e3a8df0b05927e7a749565d4b946c90991124db4e32.jpg)  

# 5.标签解绑  

如果需要解除标签、数据、模板之间的绑定关系，可在登录APP选择归属门店后，点击“标签解绑”，录入或者扫描选择需要解绑的标签，点击确定按钮后即可完成解绑。  

![](images/e7dd35976751a611fd61faebf1e7c60f3fd9a91e74b4594f80da05182319ff64.jpg)  

# 6.标签移动  

如果标签从A网关覆盖范围移动到B网关覆盖范围使用时，无法快速完成刷图，此时需要选择“标签移动”功能，点击进入后，选择标签需要绑定的数据和模板后，还需要选择下发数据使用的网关，选择完毕后点击右上角“保存”即可快速完成标签刷图。  

![](images/ad086bfcd71f2e539bae77c6a0850dcb9329f6f4618d0cbce38d240a18472e35.jpg)  

# 7.数据修改  

如果需要修改用于刷图的数据，登录APP选择归属门店后可选择“数据修改”，在数据列表中填写或扫描需要修改的数据，点击数据后即可进行修改。  

![](images/17cff5bc44de03efe4f1f6f694ba614429fbe6ab8e9429282f9f7a675174caef.jpg)  

# 8.标签详情  

如需查询标签的详细情况，可在登录APP选择门店归属后点击“标签详情”，填写或扫描选择需要查询的标签，即可查询标签详情。  

![](images/10158b6397461ed2cfd84537bf8cc4efe896d8ad83a4780ce4a06353a25d53f8.jpg)  

# 9.离线刷图  

如需脱离网关单独使用手机更新标签，可首先将标签升级为长广播固件，然后登录APP选择门店归属，选择离线刷图，扫描或搜索需要刷图的标签Mac，选择或修改刷图数据，选择对应的模板，点击保存即可刷图。  

![](images/082f94d63a68f0982cad782dcbc7c65a1647dd1c9d7292c6ef791deb5b7ad4d5.jpg)  

# 10.NFC连接  

点击NFC连接，可选择使用NFC切页或刷图，点击NFC切页按钮，将手机靠近带NFC的标签，进入NFC的切页详情，可查看设备中预存图的预览图，并选择需要切换的页数，选择完成后，点击确认，即可切页成功。  

![](images/f5be466d322f9a9aa90d7d48f7443263c06d79e012c884e6612404920e920841.jpg)  

NFC刷图，点击NFC刷图按钮，将手机靠近带NFC的标签，进入标签绑定页面，选择对应的数据和模版，点击保存按钮，即可进行刷图。  

![](images/af259ae56e71d9f641e838958b1ab51e7ea6e1ff99548ad60145b79e6faecf35.jpg)  

![](images/e4f1faefbbc2adfe2192afe8d593b506ca7a8d005023c739b5d831cd516bac3a.jpg)  

# 11.标签点灯  

点击标签点灯按钮，即可进入数据列表页，点击任意数据，可查看该数据下绑定的所有标签，可对标签进行点灯操作，点击标签后的灯泡图标，在弹出的亮灯设置弹窗中选择亮灯时长和颜色，点击亮灯按钮，亮灯成功，也可在弹窗中选择关灯按钮，关闭标签上的灯光。  

![](images/ed60712765e404fd4a7768896a0a2734c10d144ae17f8fdb8aa49240677224e5.jpg)  

![](images/373348f68e62a05031a5c3464bd63a101ab735bb0d07e924c58fa9788633e40f.jpg)  

# 12.LCD配网  

在使用LCD设备前，需要对进行配网，使其连接WiFi。点击LCD配网按钮，进入LCD设备列表页，可查看到LCD的在线/离线状态，以及设备名称、mac地址等信息，再点击需要进行配网的设备，进入LCD设备配网页面，可查看设备mac、固件版本、蓝牙名称等信息，点击WiFi，在弹出的"iFi配置弹窗中选择可连接WiFi（LCD只适配2.4gWiFi），选择完成后，点击配置按钮，返回成功状态即配置成功。  

![](images/63bd8b7b318392823754e4dccb0fc42a64ee5f072217bd189f9ce16659ddae5a.jpg)  

![](images/44b138d9769502d2f34b671d6ae18d5780d53e0f0fd0ba7db0ce899cf288289e.jpg)  

C LCD设备配网  


<html><body><table><tr><td>设备信息</td><td></td></tr><tr><td>设备MAC</td><td>BCFDOCBC08CD</td></tr><tr><td>固件版本</td><td></td></tr><tr><td>蓝牙名称</td><td>@230904E9A2</td></tr><tr><td>WiFi</td><td>ESL</td></tr></table></body></html>  

配置状态  


<html><body><table><tr><td></td></tr><tr><td></td></tr><tr><td></td></tr><tr><td></td></tr><tr><td></td></tr></table></body></html>  

![](images/264860efa934a75317af3bee6782538310437f72eda4b1d9f06693fabdd19b62.jpg)  

< LCD设备配网  

配置状态  


<html><body><table><tr><td colspan="2">设备信息</td></tr><tr><td>设备MAC</td><td>BCFDOCBC08CD</td></tr><tr><td>固件版本</td><td></td></tr><tr><td>蓝牙名称</td><td>@230904E9A2</td></tr><tr><td>WiFi</td><td>ESL</td></tr></table></body></html>  

![](images/121d73290daa366bbd9039477ec09344650e309dab962424d51cfd21bec422e3.jpg)  

<html><body><table><tr><td colspan="2">设备信息</td></tr><tr><td>设备MAC</td><td>BCFDOCBC08CD</td></tr><tr><td>固件版本</td><td>V3.19</td></tr><tr><td>蓝牙名称</td><td>@230904E9A2</td></tr><tr><td>WiFi</td><td>ESL</td></tr><tr><td>配置状态</td><td>成功</td></tr></table></body></html>  

![](images/172ddd000dee06703324a3794da18603b7f5f788561ac98cc940c838fc65744d.jpg)  

![](images/f02e27254d7ca51d2c391af286a55d1a9805be819a646da905e249cf90fec24e.jpg)  

# 权利声明  

本手册及其包含的所有内容为深圳云里物里科技股份有限公司所有，受中国法律及适用之国际公约中有关著作权法律的保护。本公司有权根据技术发展的需要对本手册内容进行更改，且更改版本不另行通知。  

未经本公司书面充许与授权，  
任何个人或公司、组织不得将本手册内容进行改动或以其它方式使用本手册的部分或全部内容，  
违者将被依法追究责任。  
服务热线：0755-21038160  
电子邮箱：<EMAIL>  
网站地址：www.ylwlesl.com  
公司地址：深圳市龙华区龙华街道和平东路港之龙科技园i栋3楼  