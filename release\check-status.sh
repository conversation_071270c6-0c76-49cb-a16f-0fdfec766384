#!/bin/bash

# EPD Manager 狀態檢查工具 (Linux/macOS)
echo "========================================"
echo "EPD Manager 狀態檢查工具 (Linux/macOS)"
echo "========================================"
echo

# 檢查Docker是否運行
if ! docker info &> /dev/null; then
    echo "[錯誤] Docker 未運行，請先啟動 Docker 服務"
    exit 1
fi

# 檢查docker-compose是否安裝
if ! command -v docker-compose &> /dev/null; then
    echo "[錯誤] docker-compose 未安裝"
    exit 1
fi

# 檢查docker-compose.yml是否存在
if [ ! -f "docker-compose.yml" ]; then
    echo "[錯誤] 找不到 docker-compose.yml 檔案"
    exit 1
fi

echo "[信息] 檢查服務狀態..."
echo

# 顯示容器狀態
echo "========== 容器狀態 =========="
docker-compose ps
echo

# 檢查服務健康狀態
echo "========== 健康檢查 =========="
for container in $(docker-compose ps -q); do
    if [ ! -z "$container" ]; then
        name=$(docker inspect $container --format "{{.Name}}" | sed 's/\///')
        health=$(docker inspect $container --format "{{.State.Health.Status}}" 2>/dev/null || echo "no-healthcheck")
        echo "容器 $name: $health"
    fi
done
echo

# 檢查端口連接
echo "========== 端口檢查 =========="
echo -n "檢查前端端口 5173... "
if curl -s -o /dev/null -w "%{http_code}" http://localhost:5173 2>/dev/null | grep -q "200\|404"; then
    echo "✓ 前端服務正常"
else
    echo "✗ 前端服務無法訪問"
fi

echo -n "檢查後端端口 3001... "
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/api/health 2>/dev/null | grep -q "200"; then
    echo "✓ 後端服務正常"
else
    echo "✗ 後端服務無法訪問"
fi

echo

# 資源使用情況
echo "========== 資源使用情況 =========="
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

echo
echo "========== 最近日誌 =========="
echo "EPD Manager 日誌:"
docker-compose logs --tail=5 epd-manager 2>/dev/null

echo
echo "MongoDB 日誌:"
docker-compose logs --tail=5 mongodb 2>/dev/null

echo
echo "========== 訪問資訊 =========="
echo "前端界面: http://localhost:5173"
echo "後端API:  http://localhost:3001"
echo "初始化: 首次訪問時需設置管理員帳號"
echo

echo "如需查看完整日誌，請執行: docker-compose logs -f"
echo
