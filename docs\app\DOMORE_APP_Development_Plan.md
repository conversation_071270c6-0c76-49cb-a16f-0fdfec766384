# DOMORE智慧雲平台APP開發計劃

## 1. 專案概述

本文檔提供了DOMORE智慧雲平台APP的開發計劃和設計細節，旨在幫助開發團隊快速構建一個功能完整、用戶體驗良好的移動應用程序，用於管理ESL電子標籤系統。

### 1.1 背景介紹

DOMORE智慧雲平台是ESL電子標籤的控制管理平台，通過雲平台能實現對系統內所有閘道、電子標籤的控制，並能與應用企業的系統對接，實現資訊在電子標籤上的即時顯示。移動APP是該平台的重要組成部分，使用戶能夠在移動設備上進行標籤管理、資料修改等操作。

### 1.2 目標用戶

- 零售店鋪管理人員
- 倉儲管理人員
- 辦公場所管理人員
- 醫療機構管理人員
- 其他需要使用電子標籤的場景管理人員

### 1.3 應用名稱

應用名稱為"CloudTag"，在各大應用商店中以此名稱發布。

## 2. 技術架構

### 2.1 開發框架選擇

#### 移動端開發框架
- **React Native**: 選擇React Native作為主要開發框架，實現跨平台(iOS和Android)開發
- **Expo**: 使用Expo工具加速開發流程，簡化配置和部署

#### 後端服務
- **Node.js + Express**: 作為中間層API服務，連接移動端和DOMORE雲平台
- **RESTful API**: 設計標準化的API接口，確保與雲平台的穩定通信

#### 數據存儲
- **AsyncStorage**: 用於本地數據緩存
- **Redux**: 用於應用狀態管理
- **Redux Persist**: 實現持久化存儲

### 2.2 系統架構圖

```
+-------------------+      +-------------------+      +-------------------+
|                   |      |                   |      |                   |
|  移動應用(APP)     |<---->|  中間層API服務    |<---->|  DOMORE雲平台     |
|  (React Native)   |      |  (Node.js)        |      |  (現有系統)       |
|                   |      |                   |      |                   |
+-------------------+      +-------------------+      +-------------------+
         |
         |
+-------------------+
|                   |
|  本地存儲          |
|  (AsyncStorage)   |
|                   |
+-------------------+
```

### 2.3 技術依賴

- React Native v0.70+
- Expo SDK 48+
- Redux & Redux Toolkit
- React Navigation v6+
- Axios (HTTP請求)
- React Native Paper (UI組件庫)
- React Native Vector Icons
- React Native Camera (掃描功能)
- React Native NFC Manager (NFC功能)
- React Native BLE PLX (藍牙功能)
- React Native NetInfo (網絡狀態檢測)
- i18next (國際化)

## 3. 功能規劃

### 3.1 核心功能列表

根據DOMORE智慧雲平台APP操作簡介，應用需要實現以下核心功能：

1. **用戶認證**
   - 登入/登出功能
   - 伺服器地址配置
   - 記住登入狀態

2. **多語言支持**
   - 中文/英文切換
   - 語言設置保存

3. **標籤管理**
   - 標籤綁定
   - 標籤解綁
   - 標籤移動
   - 標籤詳情查詢
   - 標籤點燈控制

4. **資料管理**
   - 資料修改
   - 資料查詢

5. **刷圖功能**
   - 在線刷圖
   - 離線刷圖
   - NFC刷圖

6. **設備管理**
   - LCD設備配網
   - 設備狀態查詢

### 3.2 功能優先級

| 功能 | 優先級 | 開發週期(人天) |
|------|--------|--------------|
| 用戶認證 | P0 | 3 |
| 標籤綁定 | P0 | 5 |
| 標籤解綁 | P0 | 3 |
| 資料修改 | P0 | 4 |
| 在線刷圖 | P0 | 5 |
| 標籤移動 | P1 | 4 |
| 標籤詳情 | P1 | 3 |
| 離線刷圖 | P1 | 6 |
| NFC功能 | P1 | 7 |
| 標籤點燈 | P2 | 4 |
| LCD配網 | P2 | 8 |
| 多語言支持 | P2 | 3 |

## 4. UI/UX設計

### 4.1 設計風格指南

- **配色方案**:
  - 主色: #1976D2 (藍色)
  - 輔助色: #FF5722 (橙色)
  - 背景色: #F5F5F5 (淺灰)
  - 文字色: #212121 (深灰)
  - 警告色: #F44336 (紅色)
  - 成功色: #4CAF50 (綠色)

- **字體**:
  - 主要字體: Roboto (Android), San Francisco (iOS)
  - 標題大小: 20sp
  - 正文大小: 16sp
  - 小字體: 14sp

- **間距**:
  - 基礎間距單位: 8dp
  - 內邊距: 16dp
  - 元素間距: 8dp/16dp

### 4.2 頁面導航結構

```
App
├── 登入頁
│   └── 伺服器設置
├── 主頁 (底部導航)
│   ├── 門市選擇
│   ├── 功能頁
│   │   ├── 標籤綁定
│   │   ├── 標籤解綁
│   │   ├── 標籤移動
│   │   ├── 資料修改
│   │   ├── 標籤詳情
│   │   ├── 離線刷圖
│   │   ├── NFC連接
│   │   ├── 標籤點燈
│   │   └── LCD配網
│   └── 個人中心
│       ├── 設置
│       │   └── 語言設置
│       └── 登出
```

### 4.3 關鍵頁面原型

#### 4.3.1 登入頁面
- 用戶名輸入框
- 密碼輸入框
- 伺服器地址輸入框
- 登入按鈕
- 記住密碼選項

#### 4.3.2 門市選擇頁面
- 門市列表
- 搜索框
- 門市詳情顯示

#### 4.3.3 功能頁面
- 網格布局的功能卡片
- 每個功能卡片包含圖標和文字描述

#### 4.3.4 標籤綁定頁面
- 標籤MAC輸入/掃描
- 資料選擇列表
- 模板選擇列表
- 綁定確認按鈕

#### 4.3.5 資料修改頁面
- 資料搜索
- 資料列表
- 資料編輯表單
- 保存按鈕

## 5. API接口設計

### 5.1 認證接口

```
POST /api/auth/login
請求:
{
  "username": "string",
  "password": "string",
  "server": "string"
}
響應:
{
  "token": "string",
  "user": {
    "id": "string",
    "username": "string",
    "role": "string"
  }
}
```

### 5.2 門市接口

```
GET /api/stores
請求頭:
Authorization: Bearer {token}
響應:
{
  "stores": [
    {
      "id": "string",
      "name": "string",
      "address": "string",
      "image": "string"
    }
  ]
}
```

### 5.3 標籤接口

```
GET /api/tags
請求頭:
Authorization: Bearer {token}
請求參數:
storeId: "string"
mac: "string" (可選)
響應:
{
  "tags": [
    {
      "mac": "string",
      "status": "string",
      "model": "string",
      "firmware": "string",
      "bindingData": {
        "dataId": "string",
        "templateId": "string"
      }
    }
  ]
}

POST /api/tags/bind
請求頭:
Authorization: Bearer {token}
請求:
{
  "storeId": "string",
  "mac": "string",
  "dataId": "string",
  "templateId": "string"
}
響應:
{
  "success": true,
  "message": "string"
}

POST /api/tags/unbind
請求頭:
Authorization: Bearer {token}
請求:
{
  "storeId": "string",
  "mac": "string"
}
響應:
{
  "success": true,
  "message": "string"
}
```

### 5.4 資料接口

```
GET /api/data
請求頭:
Authorization: Bearer {token}
請求參數:
storeId: "string"
keyword: "string" (可選)
響應:
{
  "data": [
    {
      "id": "string",
      "fields": {
        "field1": "value1",
        "field2": "value2"
      }
    }
  ]
}

PUT /api/data/{id}
請求頭:
Authorization: Bearer {token}
請求:
{
  "storeId": "string",
  "fields": {
    "field1": "value1",
    "field2": "value2"
  }
}
響應:
{
  "success": true,
  "message": "string"
}
```

### 5.5 刷圖接口

```
POST /api/tags/refresh
請求頭:
Authorization: Bearer {token}
請求:
{
  "storeId": "string",
  "mac": "string",
  "gateway": "string" (可選，標籤移動時使用)
}
響應:
{
  "success": true,
  "message": "string"
}
```

### 5.6 LCD配網接口

```
GET /api/lcd-devices
請求頭:
Authorization: Bearer {token}
請求參數:
storeId: "string"
響應:
{
  "devices": [
    {
      "mac": "string",
      "status": "string",
      "name": "string",
      "bluetoothName": "string"
    }
  ]
}

POST /api/lcd-devices/configure
請求頭:
Authorization: Bearer {token}
請求:
{
  "storeId": "string",
  "mac": "string",
  "wifi": {
    "ssid": "string",
    "password": "string"
  }
}
響應:
{
  "success": true,
  "message": "string"
}
```

## 6. 數據模型設計

### 6.1 本地存儲模型

#### 用戶信息
```json
{
  "user": {
    "id": "string",
    "username": "string",
    "token": "string",
    "server": "string"
  }
}
```

#### 應用設置
```json
{
  "settings": {
    "language": "zh_TW | en",
    "rememberPassword": true | false,
    "lastStoreId": "string"
  }
}
```

#### 離線數據緩存
```json
{
  "offlineCache": {
    "tags": [...],
    "data": [...],
    "templates": [...]
  }
}
```

### 6.2 Redux狀態管理

```javascript
// Store結構
{
  auth: {
    isAuthenticated: boolean,
    user: Object,
    loading: boolean,
    error: string
  },
  store: {
    currentStore: Object,
    storeList: Array,
    loading: boolean
  },
  tags: {
    tagList: Array,
    currentTag: Object,
    loading: boolean,
    error: string
  },
  data: {
    dataList: Array,
    currentData: Object,
    loading: boolean
  },
  settings: {
    language: string,
    theme: string
  },
  offline: {
    isOffline: boolean,
    pendingOperations: Array
  }
}
```

## 7. 開發計劃

### 7.1 開發階段

| 階段 | 時間(週) | 主要任務 |
|------|---------|---------|
| 準備階段 | 1 | 環境搭建、技術選型確認、API設計確認 |
| 第一階段 | 2 | 實現P0級功能：用戶認證、標籤綁定/解綁、資料修改、在線刷圖 |
| 第二階段 | 2 | 實現P1級功能：標籤移動、標籤詳情、離線刷圖、NFC功能 |
| 第三階段 | 2 | 實現P2級功能：標籤點燈、LCD配網、多語言支持 |
| 測試階段 | 1 | 功能測試、性能測試、用戶體驗測試 |
| 優化階段 | 1 | 根據測試反饋進行優化、修復問題 |
| 發布階段 | 1 | 應用打包、上傳應用商店、準備發布材料 |

### 7.2 里程碑

1. **M1**: 完成開發環境搭建和基礎框架 (第1週結束)
2. **M2**: 完成P0級功能開發和單元測試 (第3週結束)
3. **M3**: 完成P1級功能開發和集成測試 (第5週結束)
4. **M4**: 完成P2級功能開發和系統測試 (第7週結束)
5. **M5**: 完成所有功能測試和優化 (第9週結束)
6. **M6**: 應用發布到應用商店 (第10週結束)

### 7.3 團隊配置

- 1名項目經理
- 2名前端開發工程師 (React Native)
- 1名後端開發工程師 (Node.js)
- 1名UI/UX設計師
- 1名測試工程師

## 8. 測試計劃

### 8.1 測試類型

- **單元測試**: 使用Jest測試框架
- **集成測試**: 使用React Native Testing Library
- **E2E測試**: 使用Detox
- **用戶體驗測試**: 真實設備測試和用戶反饋

### 8.2 測試場景

1. **認證測試**
   - 正確/錯誤的用戶名密碼
   - 不同伺服器地址
   - 網絡中斷情況

2. **標籤操作測試**
   - 標籤綁定/解綁
   - 標籤移動
   - 標籤刷圖

3. **資料操作測試**
   - 資料查詢
   - 資料修改

4. **離線功能測試**
   - 網絡中斷時的離線操作
   - 網絡恢復後的數據同步

5. **性能測試**
   - 大量數據加載
   - 電池消耗
   - 內存使用

## 9. 部署與發布計劃

### 9.1 應用打包

- 使用Expo EAS Build服務進行應用打包
- 為iOS和Android平台分別生成生產版本

### 9.2 應用商店發布

- **iOS**: App Store
- **Android**: Google Play, 華為應用市場

### 9.3 版本更新策略

- 採用漸進式更新策略
- 每2-4週發布一個小版本更新
- 每季度發布一個功能更新版本

## 10. 風險評估與應對策略

### 10.1 潛在風險

1. **API兼容性風險**
   - 風險: DOMORE雲平台API變更可能導致應用功能失效
   - 應對: 設計中間層API服務，隔離前端和後端變化

2. **設備兼容性風險**
   - 風險: 不同型號的標籤和閘道可能有不同的通信協議
   - 應對: 建立設備適配層，統一通信接口

3. **網絡穩定性風險**
   - 風險: 移動環境下網絡不穩定影響用戶體驗
   - 應對: 實現離線模式和數據緩存機制

4. **用戶體驗風險**
   - 風險: 複雜功能可能導致用戶操作困難
   - 應對: 簡化UI設計，提供操作引導和幫助文檔

### 10.2 應對措施

- 建立完善的錯誤處理和日誌記錄機制
- 實現應用崩潰自動報告系統
- 提供用戶反饋渠道
- 定期進行用戶體驗調研和優化

## 11. 維護與支持計劃

### 11.1 應用維護

- 每月進行一次安全更新
- 每季度進行一次功能更新
- 根據用戶反饋進行持續優化

### 11.2 用戶支持

- 應用內幫助文檔
- 在線客服支持
- 問題反饋機制

## 12. 結論

DOMORE智慧雲平台APP將為用戶提供便捷的移動端管理體驗，使其能夠隨時隨地管理電子標籤系統。通過本開發計劃的實施，我們將在10週內完成一個功能完整、用戶體驗良好的移動應用，滿足用戶的各種管理需求。

---

## 附錄

### A. 技術選型詳細比較

| 技術 | 優勢 | 劣勢 | 選擇理由 |
|------|------|------|---------|
| React Native | 跨平台、性能好、社區活躍 | 原生功能集成複雜 | 最佳的跨平台開發體驗 |
| Flutter | 性能優秀、UI一致性好 | 學習曲線陡峭、插件生態較弱 | - |
| Native (iOS/Android) | 性能最佳、平台特性支持最好 | 需要維護兩套代碼、開發成本高 | - |

### B. 參考資料

- DOMORE智慧雲平台操作文件 V5.15.0
- React Native官方文檔: https://reactnative.dev/docs/getting-started
- Expo文檔: https://docs.expo.dev/
- Redux文檔: https://redux.js.org/
