# 預覽圖自動更新系統實現說明

本文檔說明如何完成電子紙設備預覽圖自動更新系統的實現，遵循「微服務/API 方案」架構。

## 實現架構概述

該系統由兩個主要部分組成：

1. **前端預覽服務 API**：
   - 獨立的 Express 服務
   - 接收預覽圖生成請求
   - 處理預覽圖渲染
   - 計算 CRC32 校驗碼
   - 返回預覽結果

2. **後端定時任務模塊**：
   - 處理定時調度
   - 數據變更檢測
   - 批量處理設備更新
   - 調用前端 API 生成預覽圖
   - 透過 WebSocket 發送到設備

## 文件結構

```
server/
  services/
    previewService/             # 前端預覽服務 API
      server.js                 # 主服務器文件
      ssrEngine.js              # 服務端渲染引擎
      package.json              # 依賴和配置
      components/
        PreviewComponentSSR.js  # 服務端預覽組件
      utils/
        cryptoSSR.js            # CRC32 計算工具
        previewImageManagerSSR.js  # 預覽圖生成工具
    autoUpdateService.js        # 後端定時任務服務
  routes/
    autoUpdateRoutes.js         # API 路由
scripts/
  test-auto-preview-update.js   # 測試腳本
  test-auto-preview-update.cmd  # Windows 命令腳本
```

## 啟動和測試系統

### 啟動前端預覽服務

1. 安裝依賴：
   ```bash
   cd server/services/previewService
   npm install
   ```

2. 啟動服務：
   ```bash
   npm start
   ```

   服務將在 `http://localhost:3001` 運行。

### 測試系統

運行測試腳本以測試完整流程：

```bash
scripts/test-auto-preview-update.cmd
```

或者使用 Node.js 直接運行：

```bash
node scripts/test-auto-preview-update.js
```

## 整合到現有系統

要將自動預覽圖更新系統整合到現有系統中，需要執行以下步驟：

1. 在 `server/index.js` 中添加 API 路由：
   ```javascript
   const autoUpdateRoutes = require('./routes/autoUpdateRoutes');
   app.use('/api/auto-update', autoUpdateRoutes);
   ```

2. 在系統啟動時初始化自動更新服務：
   ```javascript
   const autoUpdateService = require('./services/autoUpdateService');
   
   // 初始化數據庫連接
   autoUpdateService.initDB(() => ({ client, db }));
   
   // 設置WebSocket服務
   autoUpdateService.setWebsocketService(websocketService);
   
   // 載入配置
   autoUpdateService.initConfig({
     PREVIEW_API_URL: process.env.PREVIEW_API_URL || 'http://localhost:3001',
     API_TOKEN: process.env.PREVIEW_API_TOKEN,
     // 其他配置...
   });
   
   // 創建默認任務
   autoUpdateService.createDefaultTasksFromConfig();
   ```

3. 確保前端預覽服務作為獨立服務運行：
   - 可以使用 PM2 管理服務
   - 或者作為 Docker 容器運行

## 注意事項

1. **安全性**：
   - 確保 API_TOKEN 是強隨機生成的
   - 限制 API 訪問範圍
   - 考慮使用 HTTPS

2. **性能優化**：
   - 調整批處理大小
   - 配置合適的更新間隔
   - 監控服務器資源使用情況

3. **錯誤處理**：
   - 確保所有錯誤都被適當記錄
   - 實現重試機制
   - 設置監控告警

4. **環境變量**：
   - 所有敏感配置都通過環境變量注入
   - 不要在代碼中硬編碼敏感信息
