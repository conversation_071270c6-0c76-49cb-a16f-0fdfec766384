@echo off
chcp 65001 >nul
echo ========================================
echo EPD Manager Status Check Tool (Windows)
echo ========================================
echo.

:: Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not running or not installed
    pause
    exit /b 1
)

:: Check if docker-compose.yml exists
if not exist "docker-compose.yml" (
    echo [ERROR] Cannot find docker-compose.yml file
    pause
    exit /b 1
)

echo [INFO] Checking service status...
echo.

:: Show container status
echo ========== Container Status ==========
docker-compose ps
echo.

:: Check service health status
echo ========== Health Check ==========
for /f "tokens=1" %%i in ('docker-compose ps -q') do (
    for /f "tokens=1,2" %%a in ('docker inspect %%i --format "{{.Name}} {{.State.Health.Status}}"') do (
        echo Container %%a: %%b
    )
)
echo.

:: Check port connectivity
echo ========== Port Check ==========
echo Checking frontend port 5173...
curl -s -o nul -w "HTTP Status: %%{http_code}" http://localhost:5173 2>nul
if %errorlevel% equ 0 (
    echo  - Frontend service OK
) else (
    echo  - Frontend service unavailable
)

echo.
echo Checking backend port 3001...
curl -s -o nul -w "HTTP Status: %%{http_code}" http://localhost:3001/api/health 2>nul
if %errorlevel% equ 0 (
    echo  - Backend service OK
) else (
    echo  - Backend service unavailable
)

echo.
echo ========== Resource Usage ==========
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

echo.
echo ========== Recent Logs ==========
echo EPD Manager logs:
docker-compose logs --tail=5 epd-manager 2>nul

echo.
echo MongoDB logs:
docker-compose logs --tail=5 mongodb 2>nul

echo.
echo ========== Access Information ==========
echo Frontend: http://localhost:5173
echo Backend API: http://localhost:3001
echo Initialization: Set admin account on first visit
echo.

echo For full logs, run: docker-compose logs -f
echo.
echo Press any key to exit...
pause >nul
