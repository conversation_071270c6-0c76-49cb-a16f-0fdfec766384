import { DataField } from '../../types';
import { buildEndpointUrl } from './apiConfig';
import { useAuthStore } from '../../store/authStore';

// 定義系統專屬數據類型
export interface SystemSpecificData {
  _id?: string;
  uid: string;
  id: string;
  [key: string]: any; // 動態欄位
}

// 獲取所有系統專屬數據
export async function getAllSystemSpecificData(): Promise<SystemSpecificData[]> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const url = buildEndpointUrl('systemSpecificData');

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }

    // 獲取數據
    const data = await response.json();

    // 確保返回的數據是數組
    if (!Array.isArray(data)) {
      console.warn('API 返回的數據不是數組格式，可能是新的數據結構');
      return [];
    }

    return data;
  } catch (error) {
    console.error('獲取系統專屬數據失敗:', error);
    throw error;
  }
}

// 檢查系統專屬數據 ID 是否已存在
export async function checkSystemSpecificDataIdExists(id: string): Promise<boolean> {
  try {
    // 獲取系統專屬數據
    const systemSpecificData = await getAllSystemSpecificData();

    // 檢查是否有相同 ID 的數據
    return systemSpecificData.some(item => item.id === id);
  } catch (error) {
    console.error('檢查系統專屬數據 ID 失敗:', error);
    throw error;
  }
}

// 創建系統專屬數據
export async function createSystemSpecificData(data: Omit<SystemSpecificData, 'uid'>): Promise<SystemSpecificData> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const url = buildEndpointUrl('systemSpecificData');

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(data),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      const errorData = await response.json();
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    // 獲取響應數據
    return await response.json();
  } catch (error) {
    console.error('創建系統專屬數據失敗:', error);
    throw error;
  }
}

// 更新系統專屬數據
export async function updateSystemSpecificData(uid: string, data: Partial<SystemSpecificData>): Promise<SystemSpecificData> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const url = buildEndpointUrl('systemSpecificData', uid);

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(data),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      const errorData = await response.json();
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    // 獲取響應數據
    return await response.json();
  } catch (error) {
    console.error('更新系統專屬數據失敗:', error);
    throw error;
  }
}

// 刪除系統專屬數據
export async function deleteSystemSpecificData(uid: string): Promise<void> {
  try {
    // 檢查參數
    if (!uid) {
      throw new Error('刪除系統專屬數據失敗: 資料ID不能為空');
    }

    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const url = buildEndpointUrl('systemSpecificData', uid);

    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
  } catch (error) {
    console.error('刪除系統專屬數據失敗:', error);
    throw error;
  }
}

// 同步資料欄位到系統專屬數據結構
export async function syncDataFieldsToSystemSpecificData(): Promise<boolean> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('systemSpecificData', 'sync-fields'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return (await response.json()).success;
  } catch (error) {
    console.error('同步資料欄位失敗:', error);
    throw error;
  }
}
