# 詳細實現計畫

## 1. 第一階段：後端WebSocket擴展 (第1-2天)

### 1.1 擴展 websocketService.js

#### 1.1.1 新增設備狀態訂閱管理
```javascript
// 在 websocketService.js 中新增
const deviceStatusSubscribers = new Map(); // storeId -> Set<WebSocket>
const globalDeviceStatusSubscribers = new Set(); // 全局訂閱者
const gatewayStatusSubscribers = new Map(); // storeId -> Set<WebSocket>
const globalGatewayStatusSubscribers = new Set(); // 全局網關狀態訂閱者

// 訂閱設備狀態
const subscribeDeviceStatus = (ws, storeId = null, options = {}) => {
  console.log(`前端客戶端訂閱設備狀態: storeId=${storeId}`);
  
  if (storeId) {
    if (!deviceStatusSubscribers.has(storeId)) {
      deviceStatusSubscribers.set(storeId, new Set());
    }
    deviceStatusSubscribers.get(storeId).add(ws);
  } else {
    globalDeviceStatusSubscribers.add(ws);
  }
  
  // 保存訂閱選項到WebSocket實例
  ws.deviceStatusOptions = options;
  ws.subscribedStoreId = storeId;
  
  // 發送訂閱確認
  ws.send(JSON.stringify({
    type: 'device_status_subscription_ack',
    storeId,
    subscribed: true,
    timestamp: new Date().toISOString()
  }));
};

// 取消訂閱設備狀態
const unsubscribeDeviceStatus = (ws, storeId = null) => {
  if (storeId && deviceStatusSubscribers.has(storeId)) {
    deviceStatusSubscribers.get(storeId).delete(ws);
  } else {
    globalDeviceStatusSubscribers.delete(ws);
  }
  
  delete ws.deviceStatusOptions;
  delete ws.subscribedStoreId;
};

// 獲取指定門店的訂閱者
const getDeviceStatusSubscribers = (storeId) => {
  const storeSubscribers = deviceStatusSubscribers.get(storeId) || new Set();
  return new Set([...storeSubscribers, ...globalDeviceStatusSubscribers]);
};

// 訂閱網關狀態
const subscribeGatewayStatus = (ws, storeId = null, options = {}) => {
  console.log(`前端客戶端訂閱網關狀態: storeId=${storeId}`);

  if (storeId) {
    if (!gatewayStatusSubscribers.has(storeId)) {
      gatewayStatusSubscribers.set(storeId, new Set());
    }
    gatewayStatusSubscribers.get(storeId).add(ws);
  } else {
    globalGatewayStatusSubscribers.add(ws);
  }

  // 保存訂閱選項到WebSocket實例
  ws.gatewayStatusOptions = options;
  ws.subscribedGatewayStoreId = storeId;

  // 發送訂閱確認
  ws.send(JSON.stringify({
    type: 'gateway_status_subscription_ack',
    storeId,
    subscribed: true,
    timestamp: new Date().toISOString()
  }));
};

// 取消訂閱網關狀態
const unsubscribeGatewayStatus = (ws, storeId = null) => {
  if (storeId && gatewayStatusSubscribers.has(storeId)) {
    gatewayStatusSubscribers.get(storeId).delete(ws);
  } else {
    globalGatewayStatusSubscribers.delete(ws);
  }

  delete ws.gatewayStatusOptions;
  delete ws.subscribedGatewayStoreId;
};

// 獲取指定門店的網關狀態訂閱者
const getGatewayStatusSubscribers = (storeId) => {
  const storeSubscribers = gatewayStatusSubscribers.get(storeId) || new Set();
  return new Set([...storeSubscribers, ...globalGatewayStatusSubscribers]);
};
```

#### 1.1.2 新增設備狀態廣播功能
```javascript
// 設備狀態廣播器
const deviceStatusBroadcaster = {
  pendingUpdates: new Map(), // storeId -> Array<deviceUpdates>
  broadcastTimer: null,
  DEBOUNCE_DELAY: 500,

  // 安排設備狀態更新廣播
  scheduleUpdate(storeId, deviceUpdates) {
    if (!this.pendingUpdates.has(storeId)) {
      this.pendingUpdates.set(storeId, []);
    }
    
    // 合併設備更新（避免重複）
    const existingUpdates = this.pendingUpdates.get(storeId);
    const mergedUpdates = this.mergeDeviceUpdates(existingUpdates, deviceUpdates);
    this.pendingUpdates.set(storeId, mergedUpdates);
    
    // 設置防抖定時器
    if (this.broadcastTimer) {
      clearTimeout(this.broadcastTimer);
    }
    
    this.broadcastTimer = setTimeout(() => {
      this.flushPendingUpdates();
    }, this.DEBOUNCE_DELAY);
  },

  // 合併設備更新，避免重複
  mergeDeviceUpdates(existing, newUpdates) {
    const deviceMap = new Map();
    
    // 先添加現有更新
    existing.forEach(device => {
      deviceMap.set(device._id, device);
    });
    
    // 覆蓋或添加新更新
    newUpdates.forEach(device => {
      deviceMap.set(device._id, device);
    });
    
    return Array.from(deviceMap.values());
  },

  // 刷新待處理的更新
  flushPendingUpdates() {
    for (const [storeId, updates] of this.pendingUpdates) {
      if (updates.length > 0) {
        this.broadcastToStore(storeId, updates);
      }
    }
    this.pendingUpdates.clear();
    this.broadcastTimer = null;
  },

  // 向指定門店廣播設備狀態
  broadcastToStore(storeId, deviceUpdates) {
    const subscribers = getDeviceStatusSubscribers(storeId);
    
    if (subscribers.size === 0) {
      console.log(`門店 ${storeId} 沒有設備狀態訂閱者`);
      return;
    }

    const event = {
      type: 'device_status_update',
      storeId,
      devices: deviceUpdates,
      timestamp: new Date().toISOString(),
      updateType: deviceUpdates.length > 10 ? 'batch' : 'single'
    };

    console.log(`廣播設備狀態到門店 ${storeId}: ${deviceUpdates.length} 個設備, ${subscribers.size} 個訂閱者`);

    subscribers.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify(event));
        } catch (error) {
          console.error('廣播設備狀態失敗:', error);
        }
      }
    });
  }
};

// 網關狀態廣播器
const gatewayStatusBroadcaster = {
  pendingUpdates: new Map(), // storeId -> Array<gatewayUpdates>
  broadcastTimer: null,
  DEBOUNCE_DELAY: 300, // 網關狀態變更較少，使用較短的防抖延遲

  // 安排網關狀態更新廣播
  scheduleUpdate(storeId, gatewayUpdates) {
    if (!this.pendingUpdates.has(storeId)) {
      this.pendingUpdates.set(storeId, []);
    }

    // 合併網關更新（避免重複）
    const existingUpdates = this.pendingUpdates.get(storeId);
    const mergedUpdates = this.mergeGatewayUpdates(existingUpdates, gatewayUpdates);
    this.pendingUpdates.set(storeId, mergedUpdates);

    // 設置防抖定時器
    if (this.broadcastTimer) {
      clearTimeout(this.broadcastTimer);
    }

    this.broadcastTimer = setTimeout(() => {
      this.flushPendingUpdates();
    }, this.DEBOUNCE_DELAY);
  },

  // 合併網關更新，避免重複
  mergeGatewayUpdates(existing, newUpdates) {
    const gatewayMap = new Map();

    // 先添加現有更新
    existing.forEach(gateway => {
      gatewayMap.set(gateway._id, gateway);
    });

    // 覆蓋或添加新更新
    newUpdates.forEach(gateway => {
      gatewayMap.set(gateway._id, gateway);
    });

    return Array.from(gatewayMap.values());
  },

  // 刷新待處理的更新
  flushPendingUpdates() {
    for (const [storeId, updates] of this.pendingUpdates) {
      if (updates.length > 0) {
        this.broadcastToStore(storeId, updates);
      }
    }
    this.pendingUpdates.clear();
    this.broadcastTimer = null;
  },

  // 向指定門店廣播網關狀態
  broadcastToStore(storeId, gatewayUpdates) {
    const subscribers = getGatewayStatusSubscribers(storeId);

    if (subscribers.size === 0) {
      console.log(`門店 ${storeId} 沒有網關狀態訂閱者`);
      return;
    }

    const event = {
      type: 'gateway_status_update',
      storeId,
      gateways: gatewayUpdates,
      timestamp: new Date().toISOString(),
      updateType: gatewayUpdates.some(g => g.updatedFields.includes('status')) ? 'status' : 'info'
    };

    console.log(`廣播網關狀態到門店 ${storeId}: ${gatewayUpdates.length} 個網關, ${subscribers.size} 個訂閱者`);

    subscribers.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify(event));
        } catch (error) {
          console.error('廣播網關狀態失敗:', error);
        }
      }
    });
  }
};
```

### 1.2 整合到現有消息處理

#### 1.2.1 擴展前端消息處理
```javascript
// 在 handleFrontendMessage 函數中新增
case 'subscribe_device_status':
  subscribeDeviceStatus(ws, data.storeId, data.options);
  break;

case 'unsubscribe_device_status':
  unsubscribeDeviceStatus(ws, data.storeId);
  break;

case 'subscribe_gateway_status':
  subscribeGatewayStatus(ws, data.storeId, data.options);
  break;

case 'unsubscribe_gateway_status':
  unsubscribeGatewayStatus(ws, data.storeId);
  break;
```

#### 1.2.2 連接關閉時清理訂閱
```javascript
// 在 WebSocket 關閉處理中新增
ws.on('close', () => {
  // 現有清理邏輯...

  // 清理設備狀態訂閱
  if (ws.subscribedStoreId) {
    unsubscribeDeviceStatus(ws, ws.subscribedStoreId);
  } else {
    unsubscribeDeviceStatus(ws);
  }

  // 清理網關狀態訂閱
  if (ws.subscribedGatewayStoreId) {
    unsubscribeGatewayStatus(ws, ws.subscribedGatewayStoreId);
  } else {
    unsubscribeGatewayStatus(ws);
  }
});
```

## 2. 第二階段：設備狀態更新整合 (第3-4天)

### 2.1 整合到設備狀態更新流程

#### 2.1.1 修改 updateDeviceStatus 函數
```javascript
// 在 updateDeviceStatus 函數末尾新增
const updateDeviceStatus = async (gatewayId, devices, storeId) => {
  // 現有邏輯...
  const processedDevices = [];
  
  for (const device of devices) {
    // 現有設備處理邏輯...
    
    // 收集需要廣播的設備狀態變更
    const deviceUpdate = {
      _id: existingDevice._id.toString(),
      macAddress: device.macAddress,
      status: updateData.status,
      lastSeen: updateData.lastSeen.toISOString(),
      updatedFields: Object.keys(updateData)
    };
    
    // 如果有圖片更新狀態變更，包含在廣播中
    if (updateData.imageUpdateStatus) {
      deviceUpdate.imageUpdateStatus = updateData.imageUpdateStatus;
    }
    
    // 如果有電池或信號強度更新，包含在廣播中
    if (device.data) {
      deviceUpdate.data = {};
      if (device.data.battery !== undefined) {
        deviceUpdate.data.battery = device.data.battery;
      }
      if (device.data.rssi !== undefined) {
        deviceUpdate.data.rssi = device.data.rssi;
      }
    }
    
    processedDevices.push(deviceUpdate);
  }
  
  // 廣播設備狀態更新
  if (processedDevices.length > 0) {
    deviceStatusBroadcaster.scheduleUpdate(storeId, processedDevices);
  }
  
  return processedDevices;
};
```

### 2.2 整合到定時檢查服務

#### 2.2.1 修改 deviceStatusService.js
```javascript
// 修改 checkAndUpdateDeviceStatus 函數
const checkAndUpdateDeviceStatus = async () => {
  try {
    if (!getDbConnection) {
      console.warn('設備狀態服務：數據庫連接未初始化');
      return;
    }

    const { db } = await getDbConnection();
    const collection = db.collection(DEVICE_COLLECTION);

    const now = new Date();
    const threshold = new Date(now.getTime() - OFFLINE_THRESHOLD);

    // 查找需要標記為離線的設備
    const devicesToUpdate = await collection.find({
      status: 'online',
      lastSeen: { $lt: threshold }
    }).toArray();

    if (devicesToUpdate.length > 0) {
      // 批量更新設備狀態
      const result = await collection.updateMany(
        {
          status: 'online',
          lastSeen: { $lt: threshold }
        },
        {
          $set: {
            status: 'offline',
            updatedAt: now
          }
        }
      );

      console.log(`設備狀態服務：${result.modifiedCount} 個設備已自動標記為離線`);

      // 廣播設備狀態變更
      if (result.modifiedCount > 0) {
        // 按門店分組廣播
        const devicesByStore = new Map();
        
        devicesToUpdate.forEach(device => {
          const storeId = device.storeId;
          if (!devicesByStore.has(storeId)) {
            devicesByStore.set(storeId, []);
          }
          
          devicesByStore.get(storeId).push({
            _id: device._id.toString(),
            macAddress: device.macAddress,
            status: 'offline',
            lastSeen: device.lastSeen.toISOString(),
            updatedFields: ['status', 'updatedAt']
          });
        });

        // 向WebSocket服務發送廣播請求
        const websocketService = require('./websocketService');
        devicesByStore.forEach((devices, storeId) => {
          websocketService.broadcastDeviceStatus(storeId, devices);
        });
      }
    }
  } catch (error) {
    console.error('設備狀態服務錯誤：', error);
  }
};
```

### 2.3 整合網關狀態變更觸發

#### 2.3.1 修改網關連接處理
```javascript
// 在現有的網關連接處理中新增廣播
const handleConnection = async (ws, req) => {
  // 現有網關連接邏輯...

  if (req.clientType === 'gateway') {
    const gatewayId = req.gatewayId;
    const storeId = req.storeId;

    // 網關連接成功後廣播狀態更新
    const gatewayUpdate = {
      _id: gatewayId,
      status: 'online',
      lastSeen: new Date().toISOString(),
      connectionInfo: {
        connectedAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        isWebSocketConnected: true
      },
      updatedFields: ['status', 'lastSeen', 'connectionInfo']
    };

    // 廣播網關狀態更新
    gatewayStatusBroadcaster.scheduleUpdate(storeId, [gatewayUpdate]);
  }

  // 網關斷開連接時的處理
  ws.on('close', async () => {
    if (ws.clientType === 'gateway') {
      const gatewayUpdate = {
        _id: ws.gatewayId,
        status: 'offline',
        lastSeen: new Date().toISOString(),
        connectionInfo: {
          isWebSocketConnected: false,
          disconnectedAt: new Date().toISOString()
        },
        updatedFields: ['status', 'lastSeen', 'connectionInfo']
      };

      // 廣播網關離線狀態
      gatewayStatusBroadcaster.scheduleUpdate(ws.storeId, [gatewayUpdate]);
    }
  });
};
```

#### 2.3.2 修改網關信息更新處理
```javascript
// 在 handleGatewayInfoMessage 函數中新增廣播
const handleGatewayInfoMessage = async (ws, data) => {
  // 現有網關信息更新邏輯...

  // 更新完成後廣播網關信息變更
  const gatewayUpdate = {
    _id: ws.gatewayId,
    name: data.info.name,
    model: data.info.model,
    wifiFirmwareVersion: data.info.wifiFirmwareVersion,
    btFirmwareVersion: data.info.btFirmwareVersion,
    ipAddress: data.info.ipAddress,
    lastSeen: new Date().toISOString(),
    updatedFields: ['name', 'model', 'wifiFirmwareVersion', 'btFirmwareVersion', 'ipAddress', 'lastSeen']
  };

  // 廣播網關信息更新
  gatewayStatusBroadcaster.scheduleUpdate(ws.storeId, [gatewayUpdate]);
};
```

### 2.4 新增廣播API到WebSocket服務

#### 2.4.1 公開廣播方法
```javascript
// 在 websocketService.js 的 module.exports 中新增
module.exports = {
  // 現有導出...

  // 新增設備狀態相關API
  broadcastDeviceStatus: (storeId, deviceUpdates) => {
    deviceStatusBroadcaster.scheduleUpdate(storeId, deviceUpdates);
  },

  // 新增網關狀態相關API
  broadcastGatewayStatus: (storeId, gatewayUpdates) => {
    gatewayStatusBroadcaster.scheduleUpdate(storeId, gatewayUpdates);
  },

  getDeviceStatusSubscribers: () => deviceStatusSubscribers,
  getGatewayStatusSubscribers: () => gatewayStatusSubscribers,

  subscribeDeviceStatus,
  unsubscribeDeviceStatus,
  subscribeGatewayStatus,
  unsubscribeGatewayStatus
};
```

## 3. 第三階段：前端WebSocket客戶端擴展 (第5-6天)

### 3.1 擴展 websocketClient.ts

#### 3.1.1 新增設備和網關狀態事件類型
```typescript
// 新增到現有事件類型
export interface DeviceStatusEvent {
  type: 'device_status_update';
  storeId: string;
  devices: Array<{
    _id: string;
    macAddress: string;
    status: 'online' | 'offline';
    lastSeen: string;
    imageUpdateStatus?: '已更新' | '未更新';
    data?: {
      battery?: number;
      rssi?: number;
      imageCode?: string;
    };
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'single' | 'batch';
}

export interface GatewayStatusEvent {
  type: 'gateway_status_update';
  storeId: string;
  gateways: Array<{
    _id: string;
    name: string;
    macAddress: string;
    status: 'online' | 'offline';
    lastSeen: string;
    ipAddress?: string;
    model?: string;
    wifiFirmwareVersion?: string;
    btFirmwareVersion?: string;
    connectionInfo?: {
      connectedAt: string;
      lastActivity: string;
      isWebSocketConnected: boolean;
    };
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'connection' | 'info' | 'status';
}

export interface StatusSubscriptionAck {
  type: 'device_status_subscription_ack' | 'gateway_status_subscription_ack';
  storeId?: string;
  subscribed: boolean;
  timestamp: string;
}

// 擴展現有的 WebSocketEvent 類型
export type WebSocketEvent =
  | BatchProgressEvent
  | BatchCompleteEvent
  | DeviceStatusEvent
  | GatewayStatusEvent
  | StatusSubscriptionAck;

export type DeviceStatusEventHandler = (event: DeviceStatusEvent) => void;
export type GatewayStatusEventHandler = (event: GatewayStatusEvent) => void;
```

#### 3.1.2 擴展 WebSocketClient 類
```typescript
class WebSocketClient {
  // 現有屬性...
  private deviceStatusHandlers: Set<DeviceStatusEventHandler> = new Set();
  private gatewayStatusHandlers: Set<GatewayStatusEventHandler> = new Set();
  private subscribedStores: Set<string> = new Set();
  private subscribedGatewayStores: Set<string> = new Set();

  // 現有方法...

  // 新增設備狀態訂閱方法
  public subscribeDeviceStatus(storeId?: string, options?: any) {
    if (storeId) {
      this.subscribedStores.add(storeId);
    }

    console.log(`訂閱設備狀態: storeId=${storeId}`);

    this.send({
      type: 'subscribe_device_status',
      storeId,
      options: options || {
        includeImageStatus: true,
        includeBatteryInfo: true
      },
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱設備狀態
  public unsubscribeDeviceStatus(storeId?: string) {
    if (storeId) {
      this.subscribedStores.delete(storeId);
    }

    console.log(`取消訂閱設備狀態: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_device_status',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 訂閱網關狀態
  public subscribeGatewayStatus(storeId?: string, options?: any) {
    if (storeId) {
      this.subscribedGatewayStores.add(storeId);
    }

    console.log(`訂閱網關狀態: storeId=${storeId}`);

    this.send({
      type: 'subscribe_gateway_status',
      storeId,
      options: options || {
        includeConnectionInfo: true,
        includeFirmwareInfo: true
      },
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱網關狀態
  public unsubscribeGatewayStatus(storeId?: string) {
    if (storeId) {
      this.subscribedGatewayStores.delete(storeId);
    }

    console.log(`取消訂閱網關狀態: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_gateway_status',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 添加設備狀態事件監聽器
  public addDeviceStatusListener(handler: DeviceStatusEventHandler) {
    this.deviceStatusHandlers.add(handler);
  }

  // 移除設備狀態事件監聽器
  public removeDeviceStatusListener(handler: DeviceStatusEventHandler) {
    this.deviceStatusHandlers.delete(handler);
  }

  // 添加網關狀態事件監聽器
  public addGatewayStatusListener(handler: GatewayStatusEventHandler) {
    this.gatewayStatusHandlers.add(handler);
  }

  // 移除網關狀態事件監聽器
  public removeGatewayStatusListener(handler: GatewayStatusEventHandler) {
    this.gatewayStatusHandlers.delete(handler);
  }

  // 擴展現有的 handleMessage 方法
  private handleMessage(data: any) {
    switch (data.type) {
      // 現有 case...

      case 'device_status_update':
        this.notifyDeviceStatusHandlers(data as DeviceStatusEvent);
        break;

      case 'gateway_status_update':
        this.notifyGatewayStatusHandlers(data as GatewayStatusEvent);
        break;

      case 'device_status_subscription_ack':
        console.log(`設備狀態訂閱確認: storeId=${data.storeId}, subscribed=${data.subscribed}`);
        break;

      case 'gateway_status_subscription_ack':
        console.log(`網關狀態訂閱確認: storeId=${data.storeId}, subscribed=${data.subscribed}`);
        break;

      // 其他現有 case...
    }
  }

  // 通知設備狀態事件處理器
  private notifyDeviceStatusHandlers(event: DeviceStatusEvent) {
    this.deviceStatusHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('設備狀態事件處理器錯誤:', error);
      }
    });
  }

  // 通知網關狀態事件處理器
  private notifyGatewayStatusHandlers(event: GatewayStatusEvent) {
    this.gatewayStatusHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('網關狀態事件處理器錯誤:', error);
      }
    });
  }

  // 擴展重連時的訂閱恢復
  private processPendingSubscriptions() {
    // 現有批量進度訂閱恢復...

    // 恢復設備狀態訂閱
    if (this.subscribedStores.size > 0) {
      console.log(`恢復 ${this.subscribedStores.size} 個設備狀態訂閱`);
      for (const storeId of this.subscribedStores) {
        this.subscribeDeviceStatus(storeId);
      }
    }

    // 恢復網關狀態訂閱
    if (this.subscribedGatewayStores.size > 0) {
      console.log(`恢復 ${this.subscribedGatewayStores.size} 個網關狀態訂閱`);
      for (const storeId of this.subscribedGatewayStores) {
        this.subscribeGatewayStatus(storeId);
      }
    }
  }

  // 擴展清理方法
  public destroy() {
    // 現有清理邏輯...
    this.deviceStatusHandlers.clear();
    this.gatewayStatusHandlers.clear();
    this.subscribedStores.clear();
    this.subscribedGatewayStores.clear();
  }
}
```

### 3.2 新增便捷方法
```typescript
// 新增到文件末尾
export const subscribeToDeviceStatus = (
  storeId: string,
  handler: DeviceStatusEventHandler,
  options?: any
): (() => void) => {
  const client = getWebSocketClient();
  client.addDeviceStatusListener(handler);
  client.subscribeDeviceStatus(storeId, options);

  // 返回清理函數
  return () => {
    client.removeDeviceStatusListener(handler);
    client.unsubscribeDeviceStatus(storeId);
  };
};

export const subscribeToGatewayStatus = (
  storeId: string,
  handler: GatewayStatusEventHandler,
  options?: any
): (() => void) => {
  const client = getWebSocketClient();
  client.addGatewayStatusListener(handler);
  client.subscribeGatewayStatus(storeId, options);

  // 返回清理函數
  return () => {
    client.removeGatewayStatusListener(handler);
    client.unsubscribeGatewayStatus(storeId);
  };
};
```

## 4. 第四階段：設備和網關列表頁面整合 (第7-8天)

### 4.1 修改 DevicesPage.tsx

#### 4.1.1 新增設備狀態更新Hook
```typescript
// 新增自定義Hook處理設備狀態更新
const useDeviceStatusUpdates = (storeId: string) => {
  const [devices, setDevices] = useState<Device[]>([]);
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const updateTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 處理設備狀態更新
  const handleDeviceStatusUpdate = useCallback((event: DeviceStatusEvent) => {
    if (event.storeId !== storeId) return;

    console.log(`收到設備狀態更新: ${event.devices.length} 個設備`);

    // 選擇性更新設備狀態
    setDevices(prevDevices => {
      return prevDevices.map(device => {
        const update = event.devices.find(d => d._id === device._id);
        if (update) {
          return {
            ...device,
            status: update.status as DeviceStatus,
            lastSeen: new Date(update.lastSeen),
            imageUpdateStatus: update.imageUpdateStatus || device.imageUpdateStatus,
            data: {
              ...device.data,
              ...update.data
            }
          };
        }
        return device;
      });
    });
  }, [storeId]);

  // 設置WebSocket訂閱
  useEffect(() => {
    if (!storeId || !isRealTimeEnabled) return;

    console.log(`訂閱門店 ${storeId} 的設備狀態更新`);

    const unsubscribe = subscribeToDeviceStatus(
      storeId,
      handleDeviceStatusUpdate,
      {
        includeImageStatus: true,
        includeBatteryInfo: true
      }
    );

    return () => {
      console.log(`取消訂閱門店 ${storeId} 的設備狀態更新`);
      unsubscribe();
    };
  }, [storeId, isRealTimeEnabled, handleDeviceStatusUpdate]);

  return {
    devices,
    setDevices,
    isRealTimeEnabled,
    setIsRealTimeEnabled
  };
};
```

#### 4.1.2 整合到DevicesPage組件
```typescript
// 在DevicesPage組件中整合即時更新
const DevicesPage: React.FC = () => {
  // 現有狀態...
  const { store } = useStore();

  // 使用新的設備狀態更新Hook
  const {
    devices: realtimeDevices,
    setDevices: setRealtimeDevices,
    isRealTimeEnabled,
    setIsRealTimeEnabled
  } = useDeviceStatusUpdates(store?.id || '');

  // 合併即時更新和手動獲取的設備列表
  const [devices, setDevices] = useState<Device[]>([]);

  // 初始化設備列表
  useEffect(() => {
    if (realtimeDevices.length === 0) {
      // 如果即時設備列表為空，使用手動獲取
      fetchDevices();
    } else {
      // 使用即時更新的設備列表
      setDevices(realtimeDevices);
    }
  }, [realtimeDevices]);

  // 修改fetchDevices函數，同時更新即時設備列表
  const fetchDevices = async () => {
    try {
      setLoading(true);
      setError(null);

      const deviceList = await getAllDevices(store?.id);
      const formattedDevices = deviceList.map(device => ({
        ...device,
        lastSeen: device.lastSeen ? new Date(device.lastSeen) : null,
        createdAt: device.createdAt ? new Date(device.createdAt) : null,
        updatedAt: device.updatedAt ? new Date(device.updatedAt) : null,
      }));

      setDevices(formattedDevices);

      // 同時更新即時設備列表
      if (isRealTimeEnabled) {
        setRealtimeDevices(formattedDevices);
      }

    } catch (err: any) {
      setError(err.message || t('devices.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  // 新增即時更新開關
  const toggleRealTimeUpdates = () => {
    setIsRealTimeEnabled(!isRealTimeEnabled);
    if (!isRealTimeEnabled) {
      // 重新獲取最新數據
      fetchDevices();
    }
  };

  // 現有其他邏輯...
};
```

### 4.2 新增即時更新控制UI

#### 4.2.1 即時更新狀態指示器
```typescript
// 新增即時更新狀態組件
const RealTimeStatusIndicator: React.FC<{
  isEnabled: boolean;
  onToggle: () => void;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
}> = ({ isEnabled, onToggle, connectionStatus }) => {
  const getStatusColor = () => {
    if (!isEnabled) return 'text-gray-400';
    switch (connectionStatus) {
      case 'connected': return 'text-green-500';
      case 'connecting': return 'text-yellow-500';
      case 'disconnected': return 'text-red-500';
      default: return 'text-gray-400';
    }
  };

  const getStatusText = () => {
    if (!isEnabled) return '即時更新已關閉';
    switch (connectionStatus) {
      case 'connected': return '即時更新已連接';
      case 'connecting': return '正在連接...';
      case 'disconnected': return '連接已斷開';
      default: return '未知狀態';
    }
  };

  return (
    <div className="flex items-center gap-2">
      <div className={`w-2 h-2 rounded-full ${getStatusColor().replace('text-', 'bg-')}`} />
      <span className={`text-sm ${getStatusColor()}`}>
        {getStatusText()}
      </span>
      <button
        onClick={onToggle}
        className="text-sm text-blue-600 hover:text-blue-800 underline"
      >
        {isEnabled ? '關閉' : '開啟'}
      </button>
    </div>
  );
};
```

#### 4.2.2 在設備列表頁面添加狀態指示器
```typescript
// 在DevicesPage的render中添加
return (
  <div className="space-y-6">
    {/* 現有標題和搜索區域... */}

    {/* 新增即時更新狀態區域 */}
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">設備狀態</h3>
        <RealTimeStatusIndicator
          isEnabled={isRealTimeEnabled}
          onToggle={toggleRealTimeUpdates}
          connectionStatus={getWebSocketClient().isConnected() ? 'connected' : 'disconnected'}
        />
      </div>

      {/* 統計信息 */}
      <div className="mt-4 grid grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {devices.filter(d => d.status === 'online').length}
          </div>
          <div className="text-sm text-gray-500">在線設備</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-600">
            {devices.filter(d => d.status === 'offline').length}
          </div>
          <div className="text-sm text-gray-500">離線設備</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {devices.filter(d => d.imageUpdateStatus === '已更新').length}
          </div>
          <div className="text-sm text-gray-500">已更新圖片</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">
            {devices.filter(d => d.imageUpdateStatus === '未更新').length}
          </div>
          <div className="text-sm text-gray-500">待更新圖片</div>
        </div>
      </div>
    </div>

    {/* 現有設備列表... */}
  </div>
);
```

### 4.3 優化設備狀態顯示

#### 4.3.1 增強設備狀態徽章
```typescript
// 增強DeviceStatusBadge組件，支持動畫效果
const EnhancedDeviceStatusBadge: React.FC<{
  status: DeviceStatus;
  lastSeen?: Date;
  showAnimation?: boolean;
}> = ({ status, lastSeen, showAnimation = false }) => {
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (showAnimation) {
      setIsUpdating(true);
      const timer = setTimeout(() => setIsUpdating(false), 500);
      return () => clearTimeout(timer);
    }
  }, [status, showAnimation]);

  const getStatusStyles = () => {
    const baseStyles = 'px-2 py-1 text-xs font-medium rounded-full border transition-all duration-300';
    const animationStyles = isUpdating ? 'scale-110 shadow-lg' : '';

    switch (status) {
      case 'online':
        return `${baseStyles} ${animationStyles} bg-green-100 text-green-800 border-green-300`;
      case 'offline':
        return `${baseStyles} ${animationStyles} bg-gray-100 text-gray-800 border-gray-300`;
      default:
        return `${baseStyles} ${animationStyles} bg-gray-100 text-gray-800 border-gray-300`;
    }
  };

  const getLastSeenText = () => {
    if (!lastSeen) return '';
    const now = new Date();
    const diffMs = now.getTime() - lastSeen.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);

    if (diffMinutes < 1) return '剛剛';
    if (diffMinutes < 60) return `${diffMinutes}分鐘前`;
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}小時前`;
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}天前`;
  };

  return (
    <div className="flex flex-col items-center gap-1">
      <span className={getStatusStyles()}>
        {status === 'online' ? '在線' : '離線'}
      </span>
      {lastSeen && (
        <span className="text-xs text-gray-400">
          {getLastSeenText()}
        </span>
      )}
    </div>
  );
};
```

## 5. 第五階段：性能優化與測試 (第9-10天)

### 5.1 性能監控整合

#### 5.1.1 新增性能監控組件
```typescript
// 性能監控Hook
const usePerformanceMonitoring = () => {
  const [metrics, setMetrics] = useState({
    updateCount: 0,
    averageLatency: 0,
    memoryUsage: 0,
    connectionStatus: 'disconnected' as 'connected' | 'disconnected' | 'connecting'
  });

  useEffect(() => {
    const client = getWebSocketClient();

    // 監控連接狀態
    const checkConnectionStatus = () => {
      setMetrics(prev => ({
        ...prev,
        connectionStatus: client.isConnected() ? 'connected' : 'disconnected'
      }));
    };

    // 監控性能指標
    const monitorPerformance = () => {
      if (performance.memory) {
        setMetrics(prev => ({
          ...prev,
          memoryUsage: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
        }));
      }
    };

    const statusInterval = setInterval(checkConnectionStatus, 1000);
    const performanceInterval = setInterval(monitorPerformance, 5000);

    return () => {
      clearInterval(statusInterval);
      clearInterval(performanceInterval);
    };
  }, []);

  return metrics;
};
```

#### 5.1.2 新增調試面板
```typescript
// 調試面板組件（僅在開發環境顯示）
const DebugPanel: React.FC = () => {
  const metrics = usePerformanceMonitoring();
  const [isVisible, setIsVisible] = useState(false);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-500 text-white px-3 py-2 rounded-lg shadow-lg hover:bg-blue-600"
      >
        調試
      </button>

      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-white border rounded-lg shadow-lg p-4 w-64">
          <h3 className="font-bold mb-2">即時更新調試信息</h3>
          <div className="space-y-2 text-sm">
            <div>連接狀態: {metrics.connectionStatus}</div>
            <div>更新次數: {metrics.updateCount}</div>
            <div>平均延遲: {metrics.averageLatency}ms</div>
            <div>記憶體使用: {metrics.memoryUsage}MB</div>
          </div>
        </div>
      )}
    </div>
  );
};
```

### 5.2 錯誤處理和重試機制

#### 5.2.1 錯誤邊界組件
```typescript
// WebSocket錯誤邊界
class WebSocketErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('WebSocket錯誤:', error, errorInfo);

    // 嘗試重新初始化WebSocket連接
    setTimeout(() => {
      const client = getWebSocketClient();
      client.reconnect();
    }, 1000);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-yellow-600 mr-2" />
            <div>
              <h3 className="text-yellow-800 font-medium">即時更新暫時不可用</h3>
              <p className="text-yellow-700 text-sm mt-1">
                正在嘗試重新連接，您仍可以使用手動同步功能。
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
```

#### 5.2.2 智能重試機制
```typescript
// 智能重試Hook
const useSmartRetry = () => {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const maxRetries = 5;

  const retry = useCallback(async (operation: () => Promise<any>) => {
    if (retryCount >= maxRetries) {
      throw new Error('已達到最大重試次數');
    }

    setIsRetrying(true);

    try {
      const result = await operation();
      setRetryCount(0); // 成功後重置重試計數
      return result;
    } catch (error) {
      setRetryCount(prev => prev + 1);

      // 指數退避延遲
      const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
      await new Promise(resolve => setTimeout(resolve, delay));

      throw error;
    } finally {
      setIsRetrying(false);
    }
  }, [retryCount, maxRetries]);

  return { retry, retryCount, isRetrying, maxRetries };
};
```

### 5.3 配置管理

#### 5.3.1 即時更新配置
```typescript
// 配置管理
interface RealTimeConfig {
  enabled: boolean;
  debounceDelay: number;
  maxRetries: number;
  heartbeatInterval: number;
  reconnectDelay: number;
  batchSize: number;
}

const useRealTimeConfig = (): [RealTimeConfig, (config: Partial<RealTimeConfig>) => void] => {
  const [config, setConfig] = useState<RealTimeConfig>({
    enabled: true,
    debounceDelay: 500,
    maxRetries: 5,
    heartbeatInterval: 30000,
    reconnectDelay: 1000,
    batchSize: 50
  });

  const updateConfig = useCallback((newConfig: Partial<RealTimeConfig>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));

    // 保存到localStorage
    localStorage.setItem('realTimeConfig', JSON.stringify({ ...config, ...newConfig }));
  }, [config]);

  // 從localStorage載入配置
  useEffect(() => {
    const savedConfig = localStorage.getItem('realTimeConfig');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setConfig(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('載入即時更新配置失敗:', error);
      }
    }
  }, []);

  return [config, updateConfig];
};
```

### 5.4 完整整合測試

#### 5.4.1 端到端測試腳本
```javascript
// E2E測試腳本
describe('即時設備狀態更新 E2E 測試', () => {
  beforeEach(async () => {
    // 設置測試環境
    await setupTestEnvironment();
  });

  test('完整的設備狀態更新流程', async () => {
    // 1. 打開設備列表頁面
    await page.goto('/devices');

    // 2. 驗證即時更新已啟用
    const statusIndicator = await page.waitForSelector('[data-testid="realtime-status"]');
    expect(await statusIndicator.textContent()).toContain('即時更新已連接');

    // 3. 模擬設備狀態變更
    await simulateDeviceStatusChange('AA:BB:CC:DD:EE:01', 'online');

    // 4. 驗證UI即時更新
    await page.waitForSelector('[data-device-mac="AA:BB:CC:DD:EE:01"][data-status="online"]');

    // 5. 驗證統計數據更新
    const onlineCount = await page.textContent('[data-testid="online-count"]');
    expect(parseInt(onlineCount)).toBeGreaterThan(0);

    // 6. 測試批量更新
    await simulateBatchDeviceUpdate([
      { mac: 'AA:BB:CC:DD:EE:02', status: 'offline' },
      { mac: 'AA:BB:CC:DD:EE:03', status: 'online' }
    ]);

    // 7. 驗證批量更新效果
    await page.waitForSelector('[data-device-mac="AA:BB:CC:DD:EE:02"][data-status="offline"]');
    await page.waitForSelector('[data-device-mac="AA:BB:CC:DD:EE:03"][data-status="online"]');
  });

  test('網路中斷恢復測試', async () => {
    await page.goto('/devices');

    // 模擬網路中斷
    await page.setOfflineMode(true);

    // 驗證狀態指示器顯示斷開
    await page.waitForSelector('[data-testid="realtime-status"]:has-text("連接已斷開")');

    // 恢復網路
    await page.setOfflineMode(false);

    // 驗證自動重連
    await page.waitForSelector('[data-testid="realtime-status"]:has-text("即時更新已連接")');

    // 驗證功能恢復正常
    await simulateDeviceStatusChange('AA:BB:CC:DD:EE:04', 'online');
    await page.waitForSelector('[data-device-mac="AA:BB:CC:DD:EE:04"][data-status="online"]');
  });
});
```

## 6. 部署和上線計畫

### 6.1 分階段部署

#### 6.1.1 灰度發布配置
```javascript
// 灰度發布配置
const ROLLOUT_CONFIG = {
  // 第一階段：內部測試（5%用戶）
  phase1: {
    percentage: 5,
    duration: '3天',
    criteria: ['內部用戶', '測試門店']
  },

  // 第二階段：小規模試點（20%用戶）
  phase2: {
    percentage: 20,
    duration: '1週',
    criteria: ['活躍用戶', '穩定網路環境']
  },

  // 第三階段：大規模部署（100%用戶）
  phase3: {
    percentage: 100,
    duration: '持續',
    criteria: ['所有用戶']
  }
};
```

#### 6.1.2 監控和告警設置
```javascript
// 部署監控配置
const DEPLOYMENT_MONITORING = {
  // 關鍵指標閾值
  thresholds: {
    errorRate: 0.05,        // 錯誤率不超過5%
    latency: 1000,          // 延遲不超過1秒
    connectionSuccess: 0.95  // 連接成功率不低於95%
  },

  // 告警規則
  alerts: [
    {
      condition: 'errorRate > 0.05',
      action: 'pauseRollout',
      notification: ['tech-team', 'product-team']
    },
    {
      condition: 'connectionSuccess < 0.90',
      action: 'rollback',
      notification: ['tech-team', 'ops-team']
    }
  ]
};
```

### 4.4 網關管理頁面整合

#### 4.4.1 新增網關狀態更新Hook
```typescript
// 新增自定義Hook處理網關狀態更新
const useGatewayStatusUpdates = (storeId: string) => {
  const [gateways, setGateways] = useState<Gateway[]>([]);
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);

  // 處理網關狀態更新
  const handleGatewayStatusUpdate = useCallback((event: GatewayStatusEvent) => {
    if (event.storeId !== storeId) return;

    console.log(`收到網關狀態更新: ${event.gateways.length} 個網關`);

    // 選擇性更新網關狀態
    setGateways(prevGateways => {
      return prevGateways.map(gateway => {
        const update = event.gateways.find(g => g._id === gateway._id);
        if (update) {
          return {
            ...gateway,
            status: update.status as GatewayStatus,
            lastSeen: new Date(update.lastSeen),
            name: update.name || gateway.name,
            model: update.model || gateway.model,
            wifiFirmwareVersion: update.wifiFirmwareVersion || gateway.wifiFirmwareVersion,
            btFirmwareVersion: update.btFirmwareVersion || gateway.btFirmwareVersion,
            ipAddress: update.ipAddress || gateway.ipAddress
          };
        }
        return gateway;
      });
    });
  }, [storeId]);

  // 設置WebSocket訂閱
  useEffect(() => {
    if (!storeId || !isRealTimeEnabled) return;

    console.log(`訂閱門店 ${storeId} 的網關狀態更新`);

    const unsubscribe = subscribeToGatewayStatus(
      storeId,
      handleGatewayStatusUpdate,
      {
        includeConnectionInfo: true,
        includeFirmwareInfo: true
      }
    );

    return () => {
      console.log(`取消訂閱門店 ${storeId} 的網關狀態更新`);
      unsubscribe();
    };
  }, [storeId, isRealTimeEnabled, handleGatewayStatusUpdate]);

  return {
    gateways,
    setGateways,
    isRealTimeEnabled,
    setIsRealTimeEnabled
  };
};
```

#### 4.4.2 整合到GatewaysPage組件
```typescript
// 在GatewaysPage組件中整合即時更新
const GatewaysPage: React.FC = () => {
  // 現有狀態...
  const { store } = useStore();

  // 使用新的網關狀態更新Hook
  const {
    gateways: realtimeGateways,
    setGateways: setRealtimeGateways,
    isRealTimeEnabled,
    setIsRealTimeEnabled
  } = useGatewayStatusUpdates(store?.id || '');

  // 合併即時更新和手動獲取的網關列表
  const [gateways, setGateways] = useState<Gateway[]>([]);

  // 初始化網關列表
  useEffect(() => {
    if (realtimeGateways.length === 0) {
      // 如果即時網關列表為空，使用手動獲取
      fetchGateways();
    } else {
      // 使用即時更新的網關列表
      setGateways(realtimeGateways);
    }
  }, [realtimeGateways]);

  // 修改fetchGateways函數，同時更新即時網關列表
  const fetchGateways = async () => {
    try {
      setLoading(true);
      setError(null);

      const gatewayList = await getAllGateways(store.id);
      const formattedGateways = gatewayList.map(gateway => ({
        ...gateway,
        lastSeen: gateway.lastSeen ? new Date(gateway.lastSeen) : null,
        createdAt: gateway.createdAt ? new Date(gateway.createdAt) : null,
        updatedAt: gateway.updatedAt ? new Date(gateway.updatedAt) : null,
      }));

      setGateways(formattedGateways);

      // 同時更新即時網關列表
      if (isRealTimeEnabled) {
        setRealtimeGateways(formattedGateways);
      }

    } catch (err: any) {
      setError(err.message || '獲取網關列表失敗');
    } finally {
      setLoading(false);
    }
  };

  // 新增即時更新開關
  const toggleRealTimeUpdates = () => {
    setIsRealTimeEnabled(!isRealTimeEnabled);
    if (!isRealTimeEnabled) {
      // 重新獲取最新數據
      fetchGateways();
    }
  };

  // 現有其他邏輯...
};
```

#### 4.4.3 網關連接狀態指示器
```typescript
// 新增網關連接狀態組件
const GatewayConnectionIndicator: React.FC<{
  gateway: Gateway;
  showDetails?: boolean;
}> = ({ gateway, showDetails = false }) => {
  const getConnectionStatus = () => {
    // 檢查WebSocket連接狀態
    const wsClient = getWebSocketClient();
    const connectedGateways = wsClient.getConnectedGateways?.() || new Map();
    const isWebSocketConnected = connectedGateways.has(gateway._id || '');

    if (gateway.status === 'online' && isWebSocketConnected) {
      return { status: 'connected', color: 'text-green-500', text: 'WebSocket已連接' };
    } else if (gateway.status === 'online') {
      return { status: 'partial', color: 'text-yellow-500', text: '在線但WebSocket未連接' };
    } else {
      return { status: 'offline', color: 'text-gray-500', text: '離線' };
    }
  };

  const connectionInfo = getConnectionStatus();

  return (
    <div className="flex items-center gap-2">
      <div className={`w-2 h-2 rounded-full ${connectionInfo.color.replace('text-', 'bg-')}`} />
      <GatewayStatusBadge status={gateway.status} />
      {showDetails && (
        <span className={`text-xs ${connectionInfo.color}`}>
          {connectionInfo.text}
        </span>
      )}
    </div>
  );
};
```
