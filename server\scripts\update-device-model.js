/**
 * 設備模型更新腳本
 * 按照規格書更新設備模型結構
 */

const { MongoClient, ObjectId } = require('mongodb');

// 資料庫連接配置
// 根據你的實際配置修改以下資訊
const DB_URI = 'mongodb://localhost:27017';
const DB_NAME = 'epd_manager';

async function updateDeviceModel() {
  let client;
  try {
    // 連接到資料庫
    client = new MongoClient(DB_URI);
    await client.connect();
    
    console.log('成功連接到資料庫');
    
    const db = client.db(DB_NAME);
    const deviceCollection = db.collection('devices');
    const gatewayCollection = db.collection('gateways');
    
    // 1. 確保存在必要的索引
    console.log('創建必要的索引...');
    await deviceCollection.createIndex({ "macAddress": 1 }, { unique: true });
    await deviceCollection.createIndex({ "userId": 1 });
    await deviceCollection.createIndex({ "storeId": 1 });
    await deviceCollection.createIndex({ "primaryGatewayId": 1 });
    console.log('索引創建完成');
    
    // 2. 查詢所有設備
    const devices = await deviceCollection.find({}).toArray();
    console.log(`找到 ${devices.length} 個設備需要更新`);
    
    // 批量更新操作
    const bulkOperations = [];
    
    for (const device of devices) {
      // 準備更新對象
      const updateObj = {
        // 添加缺失的欄位
        initialized: device.initialized !== undefined ? device.initialized : false,
        otherGateways: device.otherGateways || []
      };
      
      // 確保 data 對象存在
      if (!device.data) {
        updateObj.data = {};
      }
      
      // 處理 code 欄位，將其移動到 data.imgcode
      if (device.code !== undefined) {
        // 確保 data 對象存在
        if (!updateObj.data) {
          updateObj.data = { ...device.data } || {};
        }
        
        updateObj.data.imgcode = device.code;
      }
      
      // 確保 lastSeen 是 Date 類型
      if (device.lastSeen && typeof device.lastSeen === 'string') {
        updateObj.lastSeen = new Date(device.lastSeen);
      } else if (!device.lastSeen) {
        updateObj.lastSeen = new Date();
      }
      
      // 定義要$unset的字段
      const unsetObj = {};
      if (device.code !== undefined) {
        unsetObj.code = '';
      }
      
      // 構建更新操作
      const updateOperation = {
        filter: { _id: device._id },
        update: {}
      };
      
      if (Object.keys(updateObj).length > 0) {
        updateOperation.update.$set = updateObj;
      }
      
      if (Object.keys(unsetObj).length > 0) {
        updateOperation.update.$unset = unsetObj;
      }
      
      // 添加操作到批量操作列表
      if (Object.keys(updateOperation.update).length > 0) {
        bulkOperations.push({
          updateOne: updateOperation
        });
      }
    }
    
    // 執行批量更新
    if (bulkOperations.length > 0) {
      const result = await deviceCollection.bulkWrite(bulkOperations);
      console.log(`成功更新 ${result.modifiedCount} 個設備`);
    } else {
      console.log('沒有需要更新的設備');
    }
    
    // 3. 檢查 primaryGatewayId 和 otherGateways 的有效性
    console.log('檢查網關關聯的有效性...');
    const updatedDevices = await deviceCollection.find({
      $or: [
        { primaryGatewayId: { $exists: true } },
        { otherGateways: { $exists: true, $ne: [] } }
      ]
    }).toArray();
    
    for (const device of updatedDevices) {
      let needsUpdate = false;
      const deviceUpdate = {};
      
      // 檢查主要網關是否存在
      if (device.primaryGatewayId) {
        const primaryGateway = await gatewayCollection.findOne({ _id: device.primaryGatewayId });
        if (!primaryGateway) {
          deviceUpdate.primaryGatewayId = null;
          deviceUpdate.initialized = false;
          needsUpdate = true;
          console.log(`設備 ${device.macAddress} 的主要網關不存在，已重置`);
        } else {
          // 確保網關的設備列表包含這個設備
          const hasDevice = await gatewayCollection.findOne({
            _id: device.primaryGatewayId,
            devices: device._id
          });
          
          if (!hasDevice) {
            await gatewayCollection.updateOne(
              { _id: device.primaryGatewayId },
              { $addToSet: { devices: device._id } }
            );
            console.log(`已將設備 ${device.macAddress} 添加到網關 ${primaryGateway.macAddress || primaryGateway._id} 的設備列表中`);
          }
        }
      }
      
      // 檢查其他網關列表中的網關是否都存在
      if (device.otherGateways && device.otherGateways.length > 0) {
        const validGateways = [];
        for (const gatewayId of device.otherGateways) {
          const gateway = await gatewayCollection.findOne({ _id: gatewayId });
          if (gateway) {
            validGateways.push(gatewayId);
            
            // 確保網關的設備列表包含這個設備
            const hasDevice = await gatewayCollection.findOne({
              _id: gatewayId,
              devices: device._id
            });
            
            if (!hasDevice) {
              await gatewayCollection.updateOne(
                { _id: gatewayId },
                { $addToSet: { devices: device._id } }
              );
              console.log(`已將設備 ${device.macAddress} 添加到網關 ${gateway.macAddress || gateway._id} 的設備列表中`);
            }
          } else {
            console.log(`設備 ${device.macAddress} 的其他網關 ${gatewayId} 不存在，將從列表中移除`);
          }
        }
        
        // 如果有無效網關被過濾掉了
        if (validGateways.length !== device.otherGateways.length) {
          deviceUpdate.otherGateways = validGateways;
          needsUpdate = true;
        }
      }
      
      // 更新設備如果需要
      if (needsUpdate) {
        await deviceCollection.updateOne(
          { _id: device._id },
          { $set: deviceUpdate }
        );
        console.log(`已更新設備 ${device.macAddress} 的網關關聯`);
      }
    }
    
    console.log('設備模型更新完成');
  } catch (error) {
    console.error('更新設備模型時發生錯誤:', error);
  } finally {
    // 關閉資料庫連接
    if (client) {
      await client.close();
      console.log('資料庫連接已關閉');
    }
  }
}

// 執行更新
updateDeviceModel().catch(console.error);
