# CRC 計算更新修復

## 問題背景

在系統中，當用戶綁定數據後修改資料內容時，觀察到預覽圖的 CRC 校驗碼未能正確更新，導致即使資料內容發生變化，但網關接收到的 CRC 值仍然與之前相同。

## 根本原因

分析後發現問題的根本原因是：

1. 在發送預覽圖到網關時，系統使用的是頁面中已緩存的 `device` 對象，該對象中包含的是舊的資料綁定信息
2. 當使用此舊數據重新生成預覽圖時，生成的圖像和 CRC 校驗碼與之前的相同，未能反映最新修改的資料

## 解決方案

我們修改了 `previewImageManager.ts` 中的 `regeneratePreviewBeforeSend` 函數，讓它在重新生成預覽圖之前自動從服務器獲取最新的設備信息：

```typescript
/**
 * 在發送預覽圖到網關前重新生成最新預覽圖
 * @param device 設備數據
 * @param storeData 門店數據
 * @param template 模板數據
 * @param forceRefreshDevice 是否強制從服務器刷新設備數據
 * @returns Promise<string|null> 生成的預覽圖數據，如失敗則返回null
 */
export const regeneratePreviewBeforeSend = async (
  device: Device,
  storeData: any[],
  template: any,
  forceRefreshDevice: boolean = true
): Promise<string | null> => {
  // ...existing code...
  
  try {
    // 如果啟用了強制刷新，先從服務器獲取最新的設備信息
    let updatedDevice = device;
    if (forceRefreshDevice && device._id) {
      try {
        const { buildEndpointUrl } = await import('./api/apiConfig');
        
        console.log(`正在從服務器獲取設備 ${device._id} 的最新資料...`);
        const deviceResponse = await fetch(buildEndpointUrl(`devices/${device._id}`), {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
        });
        
        if (deviceResponse.ok) {
          updatedDevice = await deviceResponse.json();
          console.log('已獲取最新設備信息:', updatedDevice._id);
        } else {
          console.warn('獲取最新設備信息失敗，將使用當前設備信息');
        }
      } catch (error) {
        console.error('獲取最新設備信息時出錯:', error);
        // 出錯時使用傳入的設備數據繼續執行
      }
    }
    
    // 使用最新的設備數據生成預覽圖
    // ...rest of the function using updatedDevice instead of device...
  }
}
```

## 優勢

相比於之前在 DevicesPage.tsx 中的修改，這種解決方案有幾個顯著優勢：

1. **責任分離更清晰**：設備數據更新的邏輯應該由 `regeneratePreviewBeforeSend` 負責，而不是上層調用函數
2. **更好的重用性**：任何需要重新生成預覽圖的地方都能自動獲取最新的設備數據
3. **避免重複代碼**：無需在各處重複寫獲取最新設備數據的邏輯

## 效果

此修復確保了當用戶修改資料後，發送預覽圖到網關時，系統會使用最新的資料綁定信息生成預覽圖和計算 CRC 校驗碼，從而確保內容變更能夠被正確地反映在 CRC 值上。
