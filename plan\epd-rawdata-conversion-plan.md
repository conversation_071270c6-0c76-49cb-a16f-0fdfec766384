# EPD 圖片轉換為 Raw Data 實作計劃

## 概述

在 WebSocket 發送圖片至 Gateway 時，新增 `rawdata` 參數，內容為圖片轉換成 EPD 可用的真實格式 (binary data)。

## 需求分析

### 核心需求
1. **新增 rawdata 參數**：在 WebSocket 消息中添加 `rawdata` 字段
2. **支援多種 colorType**：BW/GRAY16、BWR、BWRY
3. **處理旋轉**：point(0,0) 為左上角，旋轉後需正確排列數據
4. **模組化設計**：方便後續擴充其他格式
5. **位元對齊**：各格式需要特定的寬度對齊要求

### 技術規格

#### 支援的 ColorType 格式
1. **DisplayColorType.BW ("Gray16")**: 4bit per pixel, 寬度需為2的倍數
2. **DisplayColorType.BWR ("Black & White & Red")**: 2 tables, 1bit per pixel, 寬度需為8的倍數
3. **DisplayColorType.BWRY ("Black & White & Red & Yellow")**: 2bit per pixel, 寬度需為4的倍數

#### ImageInfo 結構
所有格式的 rawdata 都需要在開頭包含 ImageInfo 結構：
```c
type ImageInfo struct {
    imagecode uint32  // 圖片編碼 (與 WebSocket 發送的 imageCode 相同)
    X         uint16  // X 座標
    Y         uint16  // Y 座標
    Width     uint16  // 寬度
    Height    uint16  // 高度
}
```
- 總共 12 字節 (uint32 + 4 * uint16)
- 使用 Little Endian 字節序
- rawdata = ImageInfo (12 bytes) + 像素數據
- imagecode 與 WebSocket 消息中的 imageCode 相同

## 系統架構設計

### 1. 模組結構
```
src/utils/epdConversion/
├── index.ts                 # 主要導出接口
├── types.ts                 # 類型定義
├── converters/
│   ├── bwConverter.ts       # BW/GRAY16 轉換器
│   ├── bwrConverter.ts      # BWR 轉換器
│   └── bwryConverter.ts     # BWRY 轉換器
├── utils/
│   ├── rotationUtils.ts     # 旋轉處理工具
│   ├── paddingUtils.ts      # 寬度對齊工具
│   └── colorUtils.ts        # 顏色處理工具
└── __tests__/
    ├── bwConverter.test.ts
    ├── bwrConverter.test.ts
    └── bwryConverter.test.ts
```

### 2. 核心接口設計

```typescript
// ImageInfo 結構定義
interface ImageInfo {
  imagecode: number; // uint32 - 圖片編碼 (與 WebSocket 發送的 imageCode 相同)
  x: number;         // uint16 - X 座標
  y: number;         // uint16 - Y 座標
  width: number;     // uint16 - 寬度
  height: number;    // uint16 - 高度
}

// 主要轉換接口
interface EPDConversionOptions {
  colorType: DisplayColorType;     // 使用現有的 DisplayColorType
  width: number;
  height: number;
  imagecode: number;               // 圖片編碼 (與 WebSocket 發送的 imageCode 相同)
  x?: number;                      // 圖片在設備上的 X 座標，默認 0
  y?: number;                      // 圖片在設備上的 Y 座標，默認 0
  templateRotation?: number;       // 模板旋轉角度 (0, 90, 180, 270)，需要反向旋轉回 0 度
}

interface EPDConversionResult {
  success: boolean;
  rawdata?: Uint8Array;            // 包含 ImageInfo + 圖片數據
  imageInfo?: ImageInfo;           // ImageInfo 結構
  pixelData?: Uint8Array;          // 純圖片像素數據
  error?: string;
  metadata?: {
    originalSize: { width: number; height: number };
    finalSize: { width: number; height: number };
    bytesPerPixel: number;
    totalBytes: number;
    imageInfoBytes: number;        // ImageInfo 佔用的字節數 (固定12字節)
    pixelDataBytes: number;        // 像素數據佔用的字節數
  };
}

// 主要轉換函數
function convertImageToEPDRawData(
  canvas: HTMLCanvasElement,
  options: EPDConversionOptions
): EPDConversionResult;
```

## 詳細實作計劃

### 階段一：基礎架構建立
1. **創建模組目錄結構**
2. **定義核心類型和接口**
3. **實作旋轉處理工具**
4. **實作寬度對齊工具**

### 階段二：轉換器實作
1. **BW/GRAY16 轉換器**
   - 4bit per pixel 實作
   - 16級灰度量化
   - 寬度2的倍數對齊

2. **BWR 轉換器**
   - 雙表格 1bit per pixel 實作
   - 黑白紅三色量化
   - 寬度8的倍數對齊

3. **BWRY 轉換器**
   - 2bit per pixel 實作
   - 黑白紅黃四色量化
   - 寬度4的倍數對齊

### 階段三：WebSocket 整合
1. **修改 sendPreviewToGateway.js**
   - 添加 rawdata 生成邏輯
   - 整合 EPD 轉換模組

2. **更新 WebSocket 消息格式**
   - 在現有消息中添加 rawdata 字段
   - 保持向後兼容性

### 階段四：測試和驗證
1. **單元測試**
   - 各轉換器的功能測試
   - 旋轉處理測試
   - 邊界條件測試

2. **整合測試**
   - WebSocket 消息格式驗證
   - Gateway 接收測試
   - 端到端流程測試

## 實作細節

### 1. 旋轉處理邏輯
```typescript
// 旋轉處理：確保 point(0,0) 始終為左上角
function applyRotation(
  imageData: ImageData,
  rotation: number
): ImageData {
  // 根據旋轉角度重新排列像素數據
  // 0°: 無變化
  // 90°: 順時針旋轉，重新計算像素位置
  // 180°: 翻轉
  // 270°: 逆時針旋轉
}
```

### 2. 寬度對齊處理
```typescript
// 確保寬度符合各格式要求
function padWidthForFormat(
  width: number,
  colorType: DisplayColorType
): number {
  switch (colorType) {
    case DisplayColorType.BW:
      return Math.ceil(width / 2) * 2; // 2的倍數
    case DisplayColorType.BWR:
      return Math.ceil(width / 8) * 8; // 8的倍數
    case DisplayColorType.BWRY:
      return Math.ceil(width / 4) * 4; // 4的倍數
  }
}
```

### 3. 預設調色板定義
```typescript
// 獲取預設調色板（與預覽圖生成時使用的相同）
function getColorPalette(colorType: DisplayColorType): ColorRGB[] {
  switch (colorType) {
    case DisplayColorType.BWR: // "Black & White & Red"
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 }      // 紅色
      ];

    case DisplayColorType.BWRY: // "Black & White & Red & Yellow"
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 },     // 紅色
        { r: 255, g: 255, b: 0 }    // 黃色
      ];

    case DisplayColorType.BW: // "Gray16"
    default:
      // 16級灰度調色板
      const grayPalette: ColorRGB[] = [];
      for (let i = 0; i < 16; i++) {
        const grayValue = Math.round((255 / 15) * i);
        grayPalette.push({ r: grayValue, g: grayValue, b: grayValue });
      }
      return grayPalette;
  }
}

// 精確顏色比對（與預覽圖生成時使用的相同邏輯）
function findExactColorMatch(r: number, g: number, b: number, palette: ColorRGB[]): ColorRGB {
  // 直接比對 RGB 值，找到完全匹配的顏色
  for (const color of palette) {
    if (color.r === r && color.g === g && color.b === b) {
      return color;
    }
  }

  // 如果沒有完全匹配，說明預覽圖生成有問題，記錄警告
  console.warn(`未找到精確匹配的顏色: RGB(${r}, ${g}, ${b})，調色板:`, palette);

  // 返回最接近的顏色作為備用
  return findClosestColor(r, g, b, palette);
}
```

### 4. BW/GRAY16 轉換實作參考
```typescript
// 使用精確顏色匹配的 BW 轉換器
class BWConverter {
  private buffer: Uint8Array;
  private index: number = 0;

  processPixel(x: number, y: number, pixel: PixelData): void {
    const grayValue = this.toGray16(pixel);

    if ((this.index & 1) === 0) {
      // 偶數索引：存儲在高4位
      this.buffer[this.index >> 1] = (grayValue & 0xF0);
    } else {
      // 奇數索引：存儲在低4位
      this.buffer[this.index >> 1] |= (grayValue >> 4);
    }
    this.index++;
  }

  private toGray16(pixel: PixelData): number {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BW);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 將匹配的灰度值轉換為 4bit 格式 (0-15)
    const grayLevel = Math.round(matchedColor.r / 17); // 255/15 ≈ 17
    return Math.min(15, Math.max(0, grayLevel)) << 4;
  }
}
```

### 5. BWR 轉換實作參考
```typescript
// 使用精確顏色匹配的 BWR 轉換器
class BWRConverter {
  private buffer: Uint8Array;   // 黑白數據
  private buffer2: Uint8Array;  // 紅色數據
  private index: number = 0;
  private width: number;

  processPixel(x: number, y: number, pixel: PixelData): void {
    const { isRed, isWhite } = this.analyzeColor(pixel);
    const bitMask = 0x80 >> (x & 7);

    if (isRed) {
      // 紅色：兩個buffer都設為0 (已經初始化為0)
    } else {
      this.buffer2[this.index] |= bitMask;
      if (isWhite) {
        this.buffer[this.index] |= bitMask;
      }
    }

    // 當處理完一個字節或到達行尾時，移動到下一個字節
    if ((bitMask === 1) || ((x + 1) === this.paddedWidth)) {
      this.index++;
    }
  }

  private analyzeColor(pixel: PixelData): { isRed: boolean; isWhite: boolean } {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BWR);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 根據匹配的顏色判斷類型
    const isRed = matchedColor.r === 255 && matchedColor.g === 0 && matchedColor.b === 0;
    const isWhite = matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 255;

    return { isRed, isWhite };
  }
}
```

### 6. BWRY 轉換實作參考
```typescript
// 使用精確顏色匹配的 BWRY 轉換器
class BWRYConverter {
  private buffer: Uint8Array;
  private index: number = 0;

  processPixel(x: number, y: number, pixel: PixelData): void {
    const colorValue = this.analyzeColorBWRY(pixel);
    const pixelInByte = x & 3; // 每字節4個像素
    const shift = (3 - pixelInByte) * 2; // 2bit per pixel

    this.buffer[this.index] |= (colorValue << shift);

    // 當處理完一個字節或到達行尾時，移動到下一個字節
    if (pixelInByte === 3 || (x + 1) === this.paddedWidth) {
      this.index++;
    }
  }

  private analyzeColorBWRY(pixel: PixelData): number {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BWRY);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 根據匹配的顏色返回對應的數值：0=黑, 1=白, 2=黃, 3=紅 (修正紅黃對應關係)
    if (matchedColor.r === 0 && matchedColor.g === 0 && matchedColor.b === 0) {
      return 0; // 黑色
    } else if (matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 255) {
      return 1; // 白色
    } else if (matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 0) {
      return 2; // 黃色 (修正：原本是3，現在改為2)
    } else if (matchedColor.r === 255 && matchedColor.g === 0 && matchedColor.b === 0) {
      return 3; // 紅色 (修正：原本是2，現在改為3)
    }

    // 備用邏輯（不應該到達這裡）
    console.warn(`BWRY: 未預期的顏色匹配結果: RGB(${matchedColor.r}, ${matchedColor.g}, ${matchedColor.b})`);
    return 0; // 默認黑色
  }
}
```

### 5. WebSocket 消息更新
```javascript
// 在 sendPreviewToGateway.js 中
const message = {
  type: 'update_preview',
  deviceMac: device.macAddress,
  imageData: imageDataStr,
  rawdata: Array.from(rawDataBuffer), // 新增：EPD 原始數據 (轉為數組便於JSON傳輸)
  imageCode: imageCode,
  timestamp: new Date().toISOString()
};
```

### 6. BWRY 轉換實作參考
```typescript
// 基於提供的 Go 範例邏輯 (需要修正為2bit per pixel)
class BWRYConverter {
  private buffer: Uint8Array;
  private index: number = 0;
  private width: number;

  push(x: number, y: number, r: number, g: number, b: number, a: number) {
    const colorValue = this.analyzeColorBWRY(r, g, b, a);
    const pixelInByte = x & 3; // 每字節4個像素
    const shift = (3 - pixelInByte) * 2; // 2bit per pixel

    this.buffer[this.index] |= (colorValue << shift);

    // 當處理完一個字節或到達行尾時，移動到下一個字節
    if (pixelInByte === 3 || (x + 1) === this.width) {
      this.index++;
    }
  }

  private analyzeColorBWRY(r: number, g: number, b: number, a: number): number {
    // 分析顏色：0=黑, 1=白, 2=紅, 3=黃
    const isRed = r > 200 && g < 100 && b < 100;
    const isYellow = r > 200 && g > 200 && b < 100;

    if (isRed) return 2;
    if (isYellow) return 3;

    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
    return gray > 128 ? 1 : 0; // 白或黑
  }
}
```

### 7. ImageInfo 序列化處理
```typescript
// 將 ImageInfo 序列化為字節數組 (Little Endian)
function serializeImageInfo(imageInfo: ImageInfo): Uint8Array {
  const buffer = new ArrayBuffer(12); // uint32 + 4 * uint16 = 12 bytes
  const view = new DataView(buffer);

  view.setUint32(0, imageInfo.imagecode, true);  // Little Endian
  view.setUint16(4, imageInfo.x, true);
  view.setUint16(6, imageInfo.y, true);
  view.setUint16(8, imageInfo.width, true);
  view.setUint16(10, imageInfo.height, true);

  return new Uint8Array(buffer);
}

// 組合 ImageInfo + 像素數據
function combineRawData(imageInfo: ImageInfo, pixelData: Uint8Array): Uint8Array {
  const imageInfoBytes = serializeImageInfo(imageInfo);
  const rawdata = new Uint8Array(imageInfoBytes.length + pixelData.length);

  rawdata.set(imageInfoBytes, 0);
  rawdata.set(pixelData, imageInfoBytes.length);

  return rawdata;
}
```

### 8. 模板旋轉處理
```typescript
// 獲取模板旋轉角度
function getTemplateRotation(template: Template): number {
  // 從模板的 orientation 或其他屬性獲取旋轉角度
  if (template && template.orientation) {
    // 解析 orientation 字符串，例如 "90°", "180°", "270°"
    const match = template.orientation.match(/(\d+)°/);
    if (match) {
      return parseInt(match[1], 10);
    }
  }
  return 0; // 默認無旋轉
}

// 應用反向旋轉，將模板旋轉回 0 度
function applyReverseRotation(
  imageData: ImageData,
  templateRotation: number
): ImageData {
  if (templateRotation === 0) {
    return imageData; // 無需旋轉
  }

  // 計算反向旋轉角度
  const reverseRotation = (360 - templateRotation) % 360;

  console.log(`模板旋轉角度: ${templateRotation}°，應用反向旋轉: ${reverseRotation}°`);

  return rotateImageData(imageData, reverseRotation);
}

function rotateImageData(
  imageData: ImageData,
  rotation: number
): ImageData {
  switch (rotation) {
    case 90:
      return rotateImageData90(imageData);
    case 180:
      return rotateImageData180(imageData);
    case 270:
      return rotateImageData270(imageData);
    default:
      return imageData; // 0度或無效值
  }
}
```

### 9. WebSocket 消息更新
```javascript
// 在 sendPreviewToGateway.js 中
// 需要獲取模板信息以確定旋轉角度
const template = await getTemplateById(device.templateId);

const message = {
  type: 'update_preview',
  deviceMac: device.macAddress,
  imageData: imageDataStr,
  rawdata: await generateRawData(imageDataStr, device, imageCode, template), // 新增：包含 ImageInfo + 像素數據
  imageCode: imageCode,
  timestamp: new Date().toISOString()
};
```

## 風險評估與緩解

### 技術風險
1. **性能問題**：大圖片轉換可能耗時
   - 緩解：實作異步處理和進度回報
2. **記憶體使用**：原始數據可能佔用大量記憶體
   - 緩解：及時釋放中間數據，使用 TypedArray
3. **精度損失**：顏色量化可能導致圖片品質下降
   - 緩解：提供可調整的量化參數

### 兼容性風險
1. **向後兼容**：現有 Gateway 可能不支援 rawdata
   - 緩解：保持 imageData 字段，rawdata 為可選
2. **數據格式**：不同 Gateway 版本可能期望不同格式
   - 緩解：版本檢測和格式適配

## 測試策略

### 單元測試覆蓋
- [ ] BW/GRAY16 轉換正確性
- [ ] BWR 轉換正確性
- [ ] BWRY 轉換正確性
- [ ] 旋轉處理正確性
- [ ] 寬度對齊正確性
- [ ] 邊界條件處理
- [ ] 錯誤處理機制

### 整合測試覆蓋
- [ ] WebSocket 消息格式
- [ ] 端到端圖片傳輸
- [ ] 多設備並發處理
- [ ] 大圖片處理性能

### 測試數據準備
- [ ] 各種尺寸的測試圖片
- [ ] 不同 colorType 的設備模擬
- [ ] 旋轉角度測試案例
- [ ] 邊界寬度測試案例

## 時程規劃

- **第1週**：基礎架構和工具函數
  - 建立模組目錄結構
  - 實作旋轉和對齊工具
- **第2週**：轉換器實作
  - BW/GRAY16 轉換器
  - BWR 轉換器
  - BWRY 轉換器
- **第3週**：WebSocket 整合
  - 修改 sendPreviewToGateway.js
  - 整合轉換邏輯
- **第4週**：測試和優化
  - 單元測試和整合測試
  - 性能優化

## 後續擴充考量

1. **新格式支援**：模組化設計便於添加新的 colorType
2. **壓縮優化**：可考慮對 rawdata 進行壓縮
3. **快取機制**：相同圖片避免重複轉換
4. **批次處理**：支援多設備同時更新
5. **進度回報**：大圖片轉換時提供進度信息
6. **品質控制**：提供轉換品質預覽功能

## 驗收標準

1. ✅ 支援所有指定的 colorType 格式 (BW/GRAY16, BWR, BWRY)
2. ✅ 正確處理圖片旋轉 (0°, 90°, 180°, 270°)
3. ✅ WebSocket 消息包含 rawdata 字段
4. ✅ 保持向後兼容性 (imageData 字段保留)
5. ✅ 通過所有單元測試和整合測試 (覆蓋率 > 90%)
6. ✅ 性能符合要求 (轉換時間 < 2秒，記憶體使用 < 100MB)
7. ✅ 模組化設計便於後續擴充
8. ✅ 完整的錯誤處理和日誌記錄
