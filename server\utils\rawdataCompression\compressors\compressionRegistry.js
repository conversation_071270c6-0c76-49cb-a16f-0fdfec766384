/**
 * 壓縮器註冊表
 * 管理所有可用的壓縮器
 */

const { RAWDATA_FORMATS } = require('../types');

/**
 * 壓縮器註冊表類
 */
class CompressionRegistry {
  constructor() {
    this.compressors = new Map();
    this.defaultFormat = RAWDATA_FORMATS.RAWDATA;
    this.initializationTime = Date.now();
  }
  
  /**
   * 註冊壓縮器
   * @param {BaseCompressor} compressor - 壓縮器實例
   */
  register(compressor) {
    if (!compressor) {
      throw new Error('Cannot register null or undefined compressor');
    }
    
    if (typeof compressor.getFormatName !== 'function') {
      throw new Error('Invalid compressor: must have getFormatName method');
    }
    
    if (typeof compressor.compress !== 'function') {
      throw new Error('Invalid compressor: must have compress method');
    }
    
    if (typeof compressor.decompress !== 'function') {
      throw new Error('Invalid compressor: must have decompress method');
    }
    
    const formatName = compressor.getFormatName();
    
    if (!formatName || typeof formatName !== 'string') {
      throw new Error('Invalid compressor: getFormatName must return a non-empty string');
    }
    
    if (this.compressors.has(formatName)) {
      console.warn(`Compressor for format '${formatName}' already registered, overwriting`);
    }
    
    // 初始化壓縮器
    try {
      if (typeof compressor.initialize === 'function') {
        compressor.initialize();
      }
    } catch (error) {
      console.error(`Failed to initialize compressor for format '${formatName}':`, error.message);
      throw error;
    }
    
    this.compressors.set(formatName, compressor);
    console.log(`✅ Registered compressor for format: ${formatName}`);
  }
  
  /**
   * 獲取壓縮器
   * @param {string} formatName - 格式名稱
   * @returns {BaseCompressor|null} 壓縮器實例
   */
  getCompressor(formatName) {
    if (!formatName || typeof formatName !== 'string') {
      console.warn('Invalid format name provided to getCompressor');
      return null;
    }
    
    return this.compressors.get(formatName) || null;
  }
  
  /**
   * 獲取所有支援的格式
   * @returns {string[]} 格式名稱列表
   */
  getSupportedFormats() {
    return Array.from(this.compressors.keys());
  }
  
  /**
   * 檢查格式是否支援
   * @param {string} formatName - 格式名稱
   * @returns {boolean} 是否支援
   */
  isFormatSupported(formatName) {
    if (!formatName || typeof formatName !== 'string') {
      return false;
    }
    
    return this.compressors.has(formatName);
  }
  
  /**
   * 移除壓縮器
   * @param {string} formatName - 格式名稱
   * @returns {boolean} 是否成功移除
   */
  unregister(formatName) {
    if (!formatName || typeof formatName !== 'string') {
      console.warn('Invalid format name provided to unregister');
      return false;
    }
    
    if (this.compressors.delete(formatName)) {
      console.log(`🗑️ Unregistered compressor for format: ${formatName}`);
      return true;
    } else {
      console.warn(`Compressor for format '${formatName}' was not registered`);
      return false;
    }
  }
  
  /**
   * 清空所有壓縮器
   */
  clear() {
    const formatCount = this.compressors.size;
    this.compressors.clear();
    console.log(`🧹 Cleared all compressors (${formatCount} removed)`);
  }
  
  /**
   * 設定預設格式
   * @param {string} formatName - 格式名稱
   */
  setDefaultFormat(formatName) {
    if (!formatName || typeof formatName !== 'string') {
      throw new Error('Default format must be a non-empty string');
    }
    
    this.defaultFormat = formatName;
    console.log(`📝 Set default format to: ${formatName}`);
  }
  
  /**
   * 獲取預設格式
   * @returns {string} 預設格式名稱
   */
  getDefaultFormat() {
    return this.defaultFormat;
  }
  
  /**
   * 獲取註冊表統計信息
   * @returns {Object} 統計信息
   */
  getStats() {
    const supportedFormats = this.getSupportedFormats();
    const readyCompressors = supportedFormats.filter(format => {
      const compressor = this.getCompressor(format);
      return compressor && typeof compressor.isReady === 'function' && compressor.isReady();
    });
    
    return {
      totalCompressors: this.compressors.size,
      supportedFormats: supportedFormats,
      readyCompressors: readyCompressors.length,
      defaultFormat: this.defaultFormat,
      initializationTime: this.initializationTime,
      uptime: Date.now() - this.initializationTime
    };
  }
  
  /**
   * 驗證所有註冊的壓縮器
   * @returns {Object} 驗證結果
   */
  validateAll() {
    const results = {
      valid: [],
      invalid: [],
      errors: []
    };
    
    for (const [formatName, compressor] of this.compressors) {
      try {
        // 檢查基本方法
        if (typeof compressor.compress !== 'function') {
          throw new Error('Missing compress method');
        }
        
        if (typeof compressor.decompress !== 'function') {
          throw new Error('Missing decompress method');
        }
        
        if (typeof compressor.estimateCompressionRatio !== 'function') {
          throw new Error('Missing estimateCompressionRatio method');
        }
        
        // 檢查是否已初始化
        if (typeof compressor.isReady === 'function' && !compressor.isReady()) {
          throw new Error('Compressor not properly initialized');
        }
        
        results.valid.push(formatName);
      } catch (error) {
        results.invalid.push(formatName);
        results.errors.push({
          format: formatName,
          error: error.message
        });
      }
    }
    
    return results;
  }
  
  /**
   * 列印註冊表狀態
   */
  printStatus() {
    const stats = this.getStats();
    const validation = this.validateAll();
    
    console.log('\n📊 Compression Registry Status:');
    console.log(`  Total Compressors: ${stats.totalCompressors}`);
    console.log(`  Ready Compressors: ${stats.readyCompressors}`);
    console.log(`  Default Format: ${stats.defaultFormat}`);
    console.log(`  Uptime: ${Math.round(stats.uptime / 1000)}s`);
    console.log(`  Supported Formats: ${stats.supportedFormats.join(', ')}`);
    
    if (validation.invalid.length > 0) {
      console.log(`  ⚠️ Invalid Compressors: ${validation.invalid.join(', ')}`);
      validation.errors.forEach(error => {
        console.log(`    - ${error.format}: ${error.error}`);
      });
    }
    
    console.log('');
  }
}

// 創建全局註冊表實例
const globalRegistry = new CompressionRegistry();

module.exports = {
  CompressionRegistry,
  globalRegistry
};
