/**
 * Rawdata 壓縮模組主要導出接口
 */

const { RAWDATA_FORMATS, isValidFormat, getImplementedFormats } = require('./types');
const { globalRegistry } = require('./compressors/compressionRegistry');
const RunLengthCompressor = require('./compressors/runLengthCompressor');
const FormatProcessor = require('./utils/formatProcessor');

// 初始化標記
let isInitialized = false;

/**
 * 初始化壓縮模組
 */
function initializeModule() {
  if (isInitialized) {
    console.log('⚠️ Rawdata compression module already initialized');
    return;
  }
  
  try {
    console.log('🚀 Initializing rawdata compression module...');
    
    // 註冊 Run-Length 壓縮器
    const runLengthCompressor = new RunLengthCompressor();
    globalRegistry.register(runLengthCompressor);
    
    // 驗證所有壓縮器
    const validation = globalRegistry.validateAll();
    if (validation.invalid.length > 0) {
      console.warn('⚠️ Some compressors failed validation:', validation.invalid);
      validation.errors.forEach(error => {
        console.warn(`  - ${error.format}: ${error.error}`);
      });
    }
    
    isInitialized = true;
    
    console.log('✅ Rawdata compression module initialized successfully');
    console.log(`📋 Supported formats: ${globalRegistry.getSupportedFormats().join(', ')}`);
    
  } catch (error) {
    console.error('❌ Failed to initialize rawdata compression module:', error.message);
    throw error;
  }
}

// 創建格式處理器實例
const formatProcessor = new FormatProcessor(globalRegistry);

/**
 * 處理像素數據
 * @param {string} format - 目標格式
 * @param {Uint8Array} pixelData - 像素數據
 * @returns {Object} 處理結果
 */
function processPixelData(format, pixelData) {
  ensureInitialized();
  return formatProcessor.processFormat(format, pixelData);
}

/**
 * 壓縮像素數據（向後兼容）
 * @param {string} format - 目標格式
 * @param {Uint8Array} pixelData - 像素數據
 * @returns {CompressionResult} 壓縮結果
 */
function compressPixelData(format, pixelData) {
  ensureInitialized();
  
  const result = formatProcessor.processFormat(format, pixelData);
  
  // 轉換為舊的 CompressionResult 格式
  return {
    success: result.success,
    format: result.format,
    originalSize: result.originalSize,
    compressedSize: result.processedSize,
    compressionRatio: result.processingRatio,
    processingTime: result.processingTime,
    data: result.data,
    error: result.error
  };
}

/**
 * 解壓縮數據
 * @param {string} format - 數據格式
 * @param {Uint8Array} compressedData - 壓縮數據
 * @returns {Uint8Array} 解壓縮後的數據
 */
function decompressData(format, compressedData) {
  ensureInitialized();
  
  if (!format || typeof format !== 'string') {
    throw new Error('Format must be a non-empty string');
  }
  
  if (!compressedData || !(compressedData instanceof Uint8Array)) {
    throw new Error('Compressed data must be Uint8Array');
  }
  
  if (format === RAWDATA_FORMATS.RAWDATA) {
    // 未壓縮數據，直接返回
    return compressedData;
  }
  
  const compressor = globalRegistry.getCompressor(format);
  if (!compressor) {
    throw new Error(`Unsupported format: ${format}`);
  }
  
  return compressor.decompress(compressedData);
}

/**
 * 檢查格式是否支援
 * @param {string} format - 格式名稱
 * @returns {boolean} 是否支援
 */
function isFormatSupported(format) {
  ensureInitialized();
  return formatProcessor.isFormatSupported(format);
}

/**
 * 獲取所有支援的格式
 * @returns {string[]} 支援的格式列表
 */
function getSupportedFormats() {
  ensureInitialized();
  return formatProcessor.getSupportedFormats();
}

/**
 * 分析格式對數據的適用性
 * @param {string} format - 格式名稱
 * @param {Uint8Array} pixelData - 像素數據
 * @returns {Object} 分析結果
 */
function analyzeFormatSuitability(format, pixelData) {
  ensureInitialized();
  return formatProcessor.analyzeFormatSuitability(format, pixelData);
}

/**
 * 獲取模組統計信息
 * @returns {Object} 統計信息
 */
function getModuleStats() {
  ensureInitialized();
  
  return {
    initialized: isInitialized,
    registry: globalRegistry.getStats(),
    processor: formatProcessor.getStats()
  };
}

/**
 * 列印模組狀態
 */
function printModuleStatus() {
  ensureInitialized();
  
  console.log('\n🔍 Rawdata Compression Module Status:');
  console.log(`  Initialized: ${isInitialized}`);
  
  globalRegistry.printStatus();
  formatProcessor.printStatus();
}

/**
 * 重置模組統計
 */
function resetModuleStats() {
  ensureInitialized();
  formatProcessor.resetStats();
  console.log('📊 Module statistics reset');
}

/**
 * 確保模組已初始化
 * @private
 */
function ensureInitialized() {
  if (!isInitialized) {
    initializeModule();
  }
}

/**
 * 獲取壓縮器詳細信息
 * @param {string} format - 格式名稱
 * @returns {Object|null} 壓縮器信息
 */
function getCompressorInfo(format) {
  ensureInitialized();
  
  if (format === RAWDATA_FORMATS.RAWDATA) {
    return {
      name: RAWDATA_FORMATS.RAWDATA,
      algorithm: 'None',
      description: 'Raw uncompressed data',
      compatibility: 'Universal'
    };
  }
  
  const compressor = globalRegistry.getCompressor(format);
  if (!compressor) {
    return null;
  }
  
  if (typeof compressor.getInfo === 'function') {
    return compressor.getInfo();
  }
  
  return {
    name: format,
    algorithm: 'Unknown',
    description: 'No detailed information available',
    compatibility: 'Unknown'
  };
}

/**
 * 測試壓縮和解壓縮的一致性
 * @param {string} format - 格式名稱
 * @param {Uint8Array} testData - 測試數據
 * @returns {Object} 測試結果
 */
function testCompressionConsistency(format, testData) {
  ensureInitialized();
  
  try {
    if (format === RAWDATA_FORMATS.RAWDATA) {
      return {
        success: true,
        format: format,
        consistent: true,
        originalSize: testData.length,
        compressedSize: testData.length,
        decompressedSize: testData.length,
        compressionRatio: 1.0,
        message: 'Raw data format - no compression/decompression needed'
      };
    }
    
    const compressor = globalRegistry.getCompressor(format);
    if (!compressor) {
      throw new Error(`Format ${format} not supported`);
    }
    
    // 壓縮
    const compressionResult = compressor.compress(testData);
    if (!compressionResult.success) {
      throw new Error(`Compression failed: ${compressionResult.error}`);
    }
    
    // 解壓縮
    const decompressedData = compressor.decompress(compressionResult.data);
    
    // 檢查一致性
    const consistent = testData.length === decompressedData.length &&
      testData.every((value, index) => value === decompressedData[index]);
    
    return {
      success: true,
      format: format,
      consistent: consistent,
      originalSize: testData.length,
      compressedSize: compressionResult.data.length,
      decompressedSize: decompressedData.length,
      compressionRatio: compressionResult.compressionRatio,
      message: consistent ? 'Compression/decompression consistent' : 'Data mismatch detected'
    };
    
  } catch (error) {
    return {
      success: false,
      format: format,
      consistent: false,
      originalSize: testData.length,
      compressedSize: 0,
      decompressedSize: 0,
      compressionRatio: 1.0,
      message: `Test failed: ${error.message}`
    };
  }
}

// 自動初始化模組
initializeModule();

module.exports = {
  // 常數
  RAWDATA_FORMATS,
  
  // 主要功能
  processPixelData,
  compressPixelData,  // 向後兼容
  decompressData,
  isFormatSupported,
  getSupportedFormats,
  
  // 分析功能
  analyzeFormatSuitability,
  getCompressorInfo,
  testCompressionConsistency,
  
  // 統計和狀態
  getModuleStats,
  printModuleStatus,
  resetModuleStats,
  
  // 進階功能
  globalRegistry,
  formatProcessor,
  
  // 壓縮器類（用於擴展）
  RunLengthCompressor,
  
  // 工具函數
  isValidFormat,
  getImplementedFormats
};
