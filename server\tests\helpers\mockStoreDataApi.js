// tests/helpers/mockStoreDataApi.js
/**
 * 商店資料 API 的模擬路由
 */
const express = require('express');

// 創建模擬路由
function createMockStoreDataApi(mockCollection) {
  const router = express.Router();
  
  // 初始化資料庫連接函數
  router.initDB = jest.fn();

  // 獲取所有商店資料
  router.get('/storeData', async (req, res) => {
    try {
      const storeData = await mockCollection.find().toArray();
      res.json(storeData);
    } catch (error) {
      res.status(500).json({ error: '獲取商店資料失敗' });
    }
  });
  
  // 獲取指定 ID 的商店資料
  router.get('/storeData/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const storeData = await mockCollection.findOne({ _id: id });
      
      if (!storeData) {
        return res.status(404).json({ error: '找不到商店資料' });
      }
      
      res.json(storeData);
    } catch (error) {
      res.status(500).json({ error: '獲取商店資料失敗' });
    }
  });
  
  // 創建新商店資料
  router.post('/storeData', async (req, res) => {
    try {
      const result = await mockCollection.insertOne(req.body);
      res.status(201).json({ id: result.insertedId });
    } catch (error) {
      res.status(500).json({ error: '創建商店資料失敗' });
    }
  });
  
  // 更新商店資料
  router.put('/storeData/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const result = await mockCollection.updateOne({ _id: id }, { $set: req.body });
      
      if (result.modifiedCount === 0) {
        return res.status(404).json({ error: '找不到商店資料' });
      }
      
      res.json({ message: '商店資料更新成功' });
    } catch (error) {
      res.status(500).json({ error: '更新商店資料失敗' });
    }
  });
  
  // 刪除商店資料
  router.delete('/storeData/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const result = await mockCollection.deleteOne({ _id: id });
      
      if (result.deletedCount === 0) {
        return res.status(404).json({ error: '找不到商店資料或刪除失敗' });
      }
      
      res.json({ message: '商店資料刪除成功' });
    } catch (error) {
      res.status(500).json({ error: '刪除商店資料失敗' });
    }
  });
  
  return router;
}

module.exports = createMockStoreDataApi;
