# 智能網關依序嘗試邏輯修復

## 問題描述

在批量發送功能中，當設備啟用智能網關功能時，存在一個重複發送的問題：

**問題場景：**
- 設備擁有兩個或以上的 gateway 且啟用智能網關功能
- 批量發送時，如果原始資料小於 `maxSingleMessageSize`，系統會選擇直接發送原始資料
- 系統會同時將相同的原始資料發送到主要網關和所有其他網關
- 這違反了智能網關的設計原則：應該依序嘗試，成功即停止

**問題根因：**
原有邏輯中，當 `sendToAllGateways=true` 時，系統會同時向設備的所有 `otherGateways` 發送相同的資料，而不是依序嘗試。這不符合智能網關的設計理念。

## 修復方案

### 修改位置
文件：`server/services/sendPreviewToGateway.js`
行數：第 870-1000 行

### 修改內容

**修改前的邏輯：**
```javascript
// 如需發送到其他網關
let otherResults = [];
if (options.sendToAllGateways && Array.isArray(device.otherGateways) && device.otherGateways.length > 0) {
  // 同時向所有其他網關發送相同資料
  for (const otherGateway of device.otherGateways) {
    // 發送邏輯...
  }
}
```

**修改後的邏輯：**
```javascript
// 智能網關依序嘗試邏輯
let otherResults = [];
let finalSendResult = primarySendResult;
let finalUsedGatewayId = usedGatewayId;
let finalUsedGatewayName = usedGatewayName;

// 只有當主要網關發送失敗且設備啟用智能網關功能時，才依序嘗試其他網關
if (!primarySendResult.success && device.gatewaySelectionMode === 'auto' && Array.isArray(device.otherGateways) && device.otherGateways.length > 0) {
  // 依序嘗試每個其他網關，直到成功或全部失敗
  for (const otherGateway of device.otherGateways) {
    // 發送邏輯...
    if (otherSendResult.success) {
      // 成功後立即停止循環
      break;
    }
  }
}
```

### 修復邏輯

1. **智能網關功能檢查**：
   - 只有當設備的 `gatewaySelectionMode === 'auto'` 時，才啟用智能網關功能
   - 智能網關功能允許依序嘗試多個網關

2. **依序嘗試機制**：
   - 首先嘗試主要網關
   - 只有當主要網關發送失敗時，才依序嘗試其他網關
   - 不是同時發送到所有網關

3. **成功即停止**：
   - 一旦有任何網關發送成功，立即停止嘗試其他網關
   - 使用 `break` 語句跳出循環

4. **結果追蹤**：
   - 使用 `finalSendResult`、`finalUsedGatewayId`、`finalUsedGatewayName` 追蹤最終使用的網關
   - 正確返回實際成功的網關信息

## 測試驗證

### 測試文件
- `server/tests/smart-gateway-sequential-test.js`

### 測試場景
**主要測試場景**：主要網關失敗，智能網關依序嘗試
- **設備配置**：啟用智能網關功能（`gatewaySelectionMode: 'auto'`）
- **網關配置**：主要網關 + 3個備用網關
- **模擬結果**：
  - 主要網關：失敗
  - 備用網關2：失敗
  - 備用網關3：成功
  - 備用網關4：成功（但不應該被嘗試）

### 預期結果驗證
1. **最終成功**：應該成功（通過備用網關3）
2. **使用網關**：應該是 gateway3
3. **使用備用網關**：應該為 true
4. **嘗試次數**：應該只嘗試2次（gateway2 和 gateway3）
5. **結果數量**：應該只有2個結果
6. **Gateway4未被嘗試**：gateway4 不應該被嘗試

### 測試執行
```bash
cd server
node tests/smart-gateway-sequential-test.js
```

## 影響範圍

### 正面影響
1. **避免重複發送**：解決了批量發送時的重複發送問題
2. **提高效率**：減少不必要的網路傳輸
3. **邏輯一致性**：任務隊列模式下的行為更加一致

### 無負面影響
1. **向後兼容**：不影響現有的單設備發送功能
2. **功能保持**：`sendToAllGateways` 功能在非任務隊列模式下正常運作
3. **性能無損**：不會影響系統性能

## 相關代碼

### 任務隊列調用
在 `processTask` 函數中（第 1624 行）：
```javascript
const result = await sendDevicePreviewToGateway(task.deviceId, {
  ...otherOptions,
  forceGatewayId: selectedGatewayId // 強制使用指定的網關
});
```

### 智能網關選擇
任務隊列機制會：
1. 選擇最適合的 gateway
2. 通過 `forceGatewayId` 強制指定使用該 gateway
3. 現在修復後，不會再向其他 gateway 重複發送

## 總結

這次修復解決了批量發送時的重複發送問題，確保：
- 任務隊列模式下，每個任務只會發送到一個指定的 gateway
- 保持了原有功能的完整性和向後兼容性
- 提高了系統的效率和邏輯一致性

修復已通過測試驗證，可以安全部署到生產環境。
