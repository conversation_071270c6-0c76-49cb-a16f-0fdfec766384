# 解決方案設計

## 1. 整體架構設計

### 1.1 系統架構圖
```
網關設備狀態更新 → WebSocket服務 → 前端即時更新
                ↓
            數據庫狀態變更 → 廣播給所有前端客戶端
                ↓
            定時檢查離線設備 → 批量狀態廣播

網關連接狀態變化 → WebSocket服務 → 網關管理頁面即時更新
                ↓
            網關上線/離線事件 → 廣播給訂閱客戶端
                ↓
            網關信息更新 → 即時推送狀態變更
```

### 1.2 數據流向
1. **設備狀態變更觸發**
   - 網關回報設備狀態 → WebSocket服務接收
   - 定時檢查發現離線設備 → 批量狀態更新
   - 圖片更新狀態變更 → 狀態推送

2. **網關狀態變更觸發**
   - 網關WebSocket連接建立/斷開 → 即時狀態更新
   - 網關信息更新 → 推送最新網關數據
   - 網關固件版本變更 → 即時同步到前端

3. **狀態處理與廣播**
   - 數據庫狀態更新 → 觸發廣播事件
   - 按門店ID分組 → 精確推送給相關客戶端
   - 防抖合併 → 減少頻繁推送

4. **前端即時更新**
   - 接收WebSocket事件 → 解析狀態變更
   - 選擇性更新 → 只更新變更的設備和網關
   - 視覺反饋 → 平滑的狀態轉換效果

## 2. 核心功能設計

### 2.1 WebSocket事件類型定義

#### 2.1.1 設備狀態更新事件
```typescript
interface DeviceStatusEvent {
  type: 'device_status_update';
  storeId: string;
  devices: Array<{
    _id: string;
    macAddress: string;
    status: 'online' | 'offline';
    lastSeen: string;
    imageUpdateStatus?: '已更新' | '未更新';
    data?: {
      battery?: number;
      rssi?: number;
      imageCode?: string;
    };
    updatedFields: string[]; // 標記哪些字段發生了變更
  }>;
  timestamp: string;
  updateType: 'single' | 'batch' | 'periodic'; // 更新類型
}
```

#### 2.1.2 網關狀態更新事件
```typescript
interface GatewayStatusEvent {
  type: 'gateway_status_update';
  storeId: string;
  gateways: Array<{
    _id: string;
    name: string;
    macAddress: string;
    status: 'online' | 'offline';
    lastSeen: string;
    ipAddress?: string;
    model?: string;
    wifiFirmwareVersion?: string;
    btFirmwareVersion?: string;
    connectionInfo?: {
      connectedAt: string;
      lastActivity: string;
      isWebSocketConnected: boolean;
    };
    updatedFields: string[]; // 標記哪些字段發生了變更
  }>;
  timestamp: string;
  updateType: 'connection' | 'info' | 'status'; // 更新類型
}
```

#### 2.1.3 設備狀態批量事件
```typescript
interface DeviceStatusBatchEvent {
  type: 'device_status_batch';
  storeId: string;
  summary: {
    totalUpdated: number;
    onlineCount: number;
    offlineCount: number;
    imageUpdatedCount: number;
  };
  timestamp: string;
}
```

#### 2.1.4 訂閱管理事件
```typescript
interface DeviceStatusSubscription {
  type: 'subscribe_device_status';
  storeId?: string; // 可選，訂閱特定門店
  options?: {
    includeImageStatus: boolean; // 是否包含圖片更新狀態
    includeBatteryInfo: boolean; // 是否包含電池信息
  };
  timestamp: string;
}

interface GatewayStatusSubscription {
  type: 'subscribe_gateway_status';
  storeId?: string; // 可選，訂閱特定門店
  options?: {
    includeConnectionInfo: boolean; // 是否包含連接詳細信息
    includeFirmwareInfo: boolean; // 是否包含固件版本信息
  };
  timestamp: string;
}

interface StatusUnsubscription {
  type: 'unsubscribe_device_status' | 'unsubscribe_gateway_status';
  storeId?: string;
  timestamp: string;
}
```

### 2.2 後端服務設計

#### 2.2.1 訂閱管理器
```javascript
class DeviceStatusSubscriptionManager {
  constructor() {
    this.subscribers = new Map(); // storeId -> Set<WebSocket>
    this.globalSubscribers = new Set(); // 全局訂閱者
  }

  subscribe(ws, storeId = null, options = {}) {
    if (storeId) {
      if (!this.subscribers.has(storeId)) {
        this.subscribers.set(storeId, new Set());
      }
      this.subscribers.get(storeId).add(ws);
    } else {
      this.globalSubscribers.add(ws);
    }
    
    ws.deviceStatusOptions = options;
  }

  unsubscribe(ws, storeId = null) {
    if (storeId && this.subscribers.has(storeId)) {
      this.subscribers.get(storeId).delete(ws);
    } else {
      this.globalSubscribers.delete(ws);
    }
  }

  getSubscribers(storeId) {
    const storeSubscribers = this.subscribers.get(storeId) || new Set();
    return new Set([...storeSubscribers, ...this.globalSubscribers]);
  }
}
```

#### 2.2.2 狀態廣播器
```javascript
class DeviceStatusBroadcaster {
  constructor(subscriptionManager) {
    this.subscriptionManager = subscriptionManager;
    this.pendingUpdates = new Map(); // storeId -> Array<updates>
    this.broadcastTimer = null;
    this.DEBOUNCE_DELAY = 500; // 500ms防抖延遲
  }

  scheduleUpdate(storeId, deviceUpdates) {
    if (!this.pendingUpdates.has(storeId)) {
      this.pendingUpdates.set(storeId, []);
    }
    
    this.pendingUpdates.get(storeId).push(...deviceUpdates);
    
    // 設置防抖定時器
    if (this.broadcastTimer) {
      clearTimeout(this.broadcastTimer);
    }
    
    this.broadcastTimer = setTimeout(() => {
      this.flushPendingUpdates();
    }, this.DEBOUNCE_DELAY);
  }

  flushPendingUpdates() {
    for (const [storeId, updates] of this.pendingUpdates) {
      this.broadcastToStore(storeId, updates);
    }
    this.pendingUpdates.clear();
    this.broadcastTimer = null;
  }

  broadcastToStore(storeId, deviceUpdates) {
    const subscribers = this.subscriptionManager.getSubscribers(storeId);
    
    if (subscribers.size === 0) return;

    const event = {
      type: 'device_status_update',
      storeId,
      devices: deviceUpdates,
      timestamp: new Date().toISOString(),
      updateType: deviceUpdates.length > 10 ? 'batch' : 'single'
    };

    subscribers.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify(event));
        } catch (error) {
          console.error('廣播設備狀態失敗:', error);
        }
      }
    });
  }
}
```

### 2.3 前端客戶端設計

#### 2.3.1 WebSocket客戶端擴展
```typescript
class ExtendedWebSocketClient extends WebSocketClient {
  private deviceStatusHandlers: Set<DeviceStatusEventHandler> = new Set();
  private subscribedStores: Set<string> = new Set();

  // 訂閱設備狀態
  public subscribeDeviceStatus(storeId?: string, options?: DeviceStatusOptions) {
    if (storeId) {
      this.subscribedStores.add(storeId);
    }

    this.send({
      type: 'subscribe_device_status',
      storeId,
      options,
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱
  public unsubscribeDeviceStatus(storeId?: string) {
    if (storeId) {
      this.subscribedStores.delete(storeId);
    }

    this.send({
      type: 'unsubscribe_device_status',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 處理設備狀態事件
  private handleDeviceStatusEvent(event: DeviceStatusEvent) {
    this.deviceStatusHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('設備狀態事件處理錯誤:', error);
      }
    });
  }

  // 添加設備狀態事件監聽器
  public addDeviceStatusListener(handler: DeviceStatusEventHandler) {
    this.deviceStatusHandlers.add(handler);
  }

  // 移除設備狀態事件監聽器
  public removeDeviceStatusListener(handler: DeviceStatusEventHandler) {
    this.deviceStatusHandlers.delete(handler);
  }
}
```

#### 2.3.2 設備列表整合
```typescript
// 在 DevicesPage.tsx 中的整合邏輯
const useDeviceStatusUpdates = (storeId: string) => {
  const [devices, setDevices] = useState<Device[]>([]);
  
  useEffect(() => {
    const client = getWebSocketClient();
    
    const handleDeviceStatusUpdate = (event: DeviceStatusEvent) => {
      if (event.storeId === storeId) {
        setDevices(prevDevices => {
          return updateDevicesSelectively(prevDevices, event.devices);
        });
      }
    };
    
    client.addDeviceStatusListener(handleDeviceStatusUpdate);
    client.subscribeDeviceStatus(storeId, {
      includeImageStatus: true,
      includeBatteryInfo: true
    });
    
    return () => {
      client.removeDeviceStatusListener(handleDeviceStatusUpdate);
      client.unsubscribeDeviceStatus(storeId);
    };
  }, [storeId]);
  
  return devices;
};

// 選擇性更新設備狀態
const updateDevicesSelectively = (
  currentDevices: Device[], 
  updatedDevices: DeviceStatusUpdate[]
): Device[] => {
  const updatedMap = new Map(
    updatedDevices.map(device => [device._id, device])
  );
  
  return currentDevices.map(device => {
    const update = updatedMap.get(device._id);
    if (update) {
      return {
        ...device,
        ...update,
        lastSeen: new Date(update.lastSeen)
      };
    }
    return device;
  });
};
```

## 3. 實現策略

### 3.1 漸進式實現
1. **階段一**：基礎WebSocket擴展
   - 擴展現有WebSocket服務支援設備狀態事件
   - 實現基本的訂閱管理機制

2. **階段二**：設備狀態推送實現
   - 整合到設備狀態更新流程
   - 實現防抖和批量廣播機制

3. **階段三**：前端整合與優化
   - 前端WebSocket客戶端擴展
   - 設備列表頁面即時更新整合

4. **階段四**：性能調優與測試
   - 性能優化和穩定性測試
   - 與現有功能的兼容性驗證

### 3.2 兼容性保證
- **無縫整合**：保持現有批量傳送功能不受影響
- **向後兼容**：現有的手動同步機制繼續可用
- **漸進啟用**：可以通過配置開關控制功能啟用
- **安全回滾**：出現問題時可以快速回滾到原有機制

### 3.3 配置化設計
```javascript
// 設備狀態即時更新配置
const DEVICE_STATUS_CONFIG = {
  // 功能開關
  ENABLE_REALTIME_UPDATES: true,
  ENABLE_BATTERY_UPDATES: true,
  ENABLE_IMAGE_STATUS_UPDATES: true,

  // 性能參數
  DEBOUNCE_DELAY: 500,           // 防抖延遲(ms)
  MAX_BATCH_SIZE: 50,            // 最大批量大小
  BROADCAST_INTERVAL: 1000,      // 廣播間隔(ms)

  // 訂閱選項
  DEFAULT_SUBSCRIPTION_OPTIONS: {
    includeImageStatus: true,
    includeBatteryInfo: true,
    includeRSSI: false
  }
};
```

### 3.4 整合點識別
- **設備狀態更新觸發點**：
  - `websocketService.js` 中的 `updateDeviceStatus` 函數
  - `deviceStatusService.js` 中的定時檢查函數
  - 圖片更新狀態變更時的觸發點

- **前端整合點**：
  - `DevicesPage.tsx` 設備列表頁面
  - `websocketClient.ts` WebSocket客戶端
  - 設備狀態相關的UI組件
