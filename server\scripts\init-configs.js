/**
 * 初始化系統配置腳本
 * 用於創建系統必要的配置
 */
const { MongoClient } = require('mongodb');

// MongoDB 連接信息
const MONGO_URL = 'mongodb://localhost:27017';
const DB_NAME = 'epd-manager';
const CONFIG_COLLECTION = 'sysConfigs';

async function initConfigs() {
  let client;
  try {
    // 連接到 MongoDB
    client = new MongoClient(MONGO_URL);
    await client.connect();
    console.log('已連接到 MongoDB');

    const db = client.db(DB_NAME);
    const configCollection = db.collection(CONFIG_COLLECTION);

    // 檢查並創建 fieldsView 配置
    const fieldsViewConfig = await configCollection.findOne({ key: 'fieldsView' });
    if (!fieldsViewConfig) {
      // 創建默認的 fieldsView 配置
      const defaultFieldsView = {
        columns: [
          { field: 'id', header: 'ID', width: 100, sortable: true },
          { field: 'name', header: '名稱', width: 150, sortable: true },
          { field: 'description', header: '描述', width: 200, sortable: true },
          { field: 'price', header: '價格', width: 100, sortable: true },
          { field: 'quantity', header: '數量', width: 100, sortable: true },
          { field: 'date', header: '日期', width: 150, sortable: true }
        ],
        defaultSortField: 'id',
        defaultSortOrder: 'asc'
      };

      await configCollection.insertOne({
        key: 'fieldsView',
        value: defaultFieldsView,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      console.log('已創建 fieldsView 配置');
    } else {
      console.log('fieldsView 配置已存在');
    }

    console.log('配置初始化完成');
  } catch (error) {
    console.error('初始化配置失敗:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('已關閉 MongoDB 連接');
    }
  }
}

// 執行初始化
initConfigs().catch(console.error);
