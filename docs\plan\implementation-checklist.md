# Rawdata 格式增強實作檢查清單

## 總覽

本計畫實現裝置回報 rawdata 格式功能，並添加 runlendata 壓縮格式，提供可擴展的壓縮架構。

## 實作時程

### 第一天：基礎架構建立
- [ ] 創建 `server/utils/rawdataCompression/` 目錄結構
- [ ] 實作 `types.js` - 類型定義和常數
- [ ] 實作 `baseCompressor.js` - 基礎壓縮器抽象類
- [ ] 實作 `compressionRegistry.js` - 壓縮器註冊管理
- [ ] 建立基本的單元測試框架

### 第二天：Run-Length 壓縮器實作
- [ ] 實作 `runLengthCompressor.js` - 完整的 RLE 壓縮器
- [ ] 優化壓縮算法，處理轉義字節
- [ ] 實作快速壓縮比估算功能
- [ ] 編寫 Run-Length 壓縮器單元測試
- [ ] 驗證壓縮和解壓縮的正確性

### 第三天：格式處理和主接口
- [ ] 實作 `formatProcessor.js` - 格式處理邏輯
- [ ] 實作格式支援檢查和數據處理功能
- [ ] 實作 `index.js` - 主要導出接口
- [ ] 編寫格式處理器單元測試
- [ ] 測試格式轉換效果

### 第四天：WebSocket 服務整合
- [ ] 更新 `websocketService.js` 引入格式處理模組
- [ ] 修改 `handleGatewayInfo` 處理格式偏好回報
- [ ] 實作 `getGatewayPreferredFormat` 和 `processRawdataFormat` 函數
- [ ] 更新 `sendImageToGateway` 整合格式處理
- [ ] 更新分片和直接傳輸函數添加格式標識

### 第五天：測試工具更新和驗證
- [ ] 更新 `ws-client-from-copied-info.js` 支援格式偏好設定
- [ ] 添加解壓縮函數和驗證邏輯
- [ ] 更新 ChunkReceiver 類處理格式信息
- [ ] 進行端到端功能測試
- [ ] 驗證格式轉換效果和兼容性

## 詳細檢查清單

### 基礎架構 (第一天)

#### 目錄結構創建
```bash
server/utils/rawdataCompression/
├── index.js
├── types.js
├── compressors/
│   ├── baseCompressor.js
│   ├── runLengthCompressor.js
│   └── compressionRegistry.js
├── utils/
│   ├── formatSelector.js
│   └── compressionAnalyzer.js
└── __tests__/
    ├── runLengthCompressor.test.js
    ├── formatSelector.test.js
    └── compressionAnalyzer.test.js
```

#### types.js 檢查項目
- [ ] 定義 `RAWDATA_FORMATS` 常數
- [ ] 定義 `CompressionResult` 結構
- [ ] 定義 `FormatSelectionResult` 結構
- [ ] 預留未來格式擴展空間

#### baseCompressor.js 檢查項目
- [ ] 抽象類正確實作，無法直接實例化
- [ ] 定義必須實作的抽象方法
- [ ] 提供通用的輔助方法
- [ ] 結果對象創建方法

#### compressionRegistry.js 檢查項目
- [ ] 壓縮器註冊和管理功能
- [ ] 格式支援檢查功能
- [ ] 全局註冊表實例
- [ ] 錯誤處理和日誌記錄

### Run-Length 壓縮器 (第二天)

#### runLengthCompressor.js 檢查項目
- [ ] 繼承 BaseCompressor 正確實作
- [ ] RLE 壓縮算法實作
- [ ] 轉義字節 (0xFF) 正確處理
- [ ] 解壓縮算法實作
- [ ] 快速壓縮比估算
- [ ] 數據適用性檢查
- [ ] 錯誤處理和性能監控

#### 壓縮算法驗證
- [ ] 測試各種數據模式的壓縮效果
- [ ] 驗證壓縮和解壓縮的一致性
- [ ] 測試邊界條件和錯誤情況
- [ ] 性能基準測試

### 格式選擇邏輯 (第三天)

#### formatSelector.js 檢查項目
- [ ] 三種選擇策略實作
- [ ] Gateway 能力考慮
- [ ] 數據特性分析
- [ ] 策略註冊和管理
- [ ] 選擇結果詳細信息

#### index.js 檢查項目
- [ ] 模組初始化邏輯
- [ ] 主要功能函數導出
- [ ] 壓縮器自動註冊
- [ ] 錯誤處理和日誌

### WebSocket 服務整合 (第四天)

#### websocketService.js 更新檢查
- [ ] 引入壓縮模組
- [ ] Gateway 格式回報處理
- [ ] 格式選擇邏輯整合
- [ ] 壓縮處理函數
- [ ] 傳輸函數格式標識
- [ ] 向後兼容性保持

#### 關鍵函數檢查
- [ ] `handleGatewayInfo` - 格式支援處理
- [ ] `selectRawdataFormat` - 格式選擇
- [ ] `processRawdataCompression` - 壓縮處理
- [ ] `sendImageToGateway` - 整合流程
- [ ] 分片和直接傳輸更新

#### sendPreviewToGateway.js 更新檢查
- [ ] 移除重複的壓縮邏輯
- [ ] 保持 EPD 轉換功能
- [ ] 與 websocketService 正確整合

### 測試工具更新 (第五天)

#### ws-client-from-copied-info.js 更新檢查
- [ ] 格式選擇互動界面
- [ ] 解壓縮函數實作
- [ ] ChunkReceiver 格式處理
- [ ] 消息處理邏輯更新
- [ ] 數據保存和驗證

#### 測試場景覆蓋
- [ ] 偏好 rawdata 格式的 Gateway
- [ ] 偏好 runlendata 格式的 Gateway
- [ ] 未指定格式偏好的 Gateway
- [ ] 壓縮效果顯著的數據
- [ ] 壓縮效果不佳的數據
- [ ] 分片傳輸各種格式數據
- [ ] 直接傳輸各種格式數據

## 驗證標準

### 功能驗證
- [ ] 所有格式正確實作並可正常使用
- [ ] Gateway 格式回報正確處理
- [ ] 格式選擇邏輯運作正常
- [ ] 壓縮和解壓縮數據一致
- [ ] 向後兼容性完全保持

### 性能驗證
- [ ] Run-Length 壓縮在適合數據上達到 30% 以上壓縮比
- [ ] 壓縮處理時間不超過原始傳輸時間的 10%
- [ ] 記憶體使用量在合理範圍內
- [ ] 網絡傳輸效率提升

### 兼容性驗證
- [ ] 舊版 Gateway 正常運作
- [ ] 混合格式環境穩定
- [ ] 錯誤處理和回退機制有效
- [ ] 日誌記錄完整清晰

### 可擴展性驗證
- [ ] 新格式可以輕鬆添加
- [ ] 壓縮器註冊機制靈活
- [ ] 選擇策略可自定義
- [ ] 配置參數可調整

## 風險緩解檢查

### 技術風險
- [ ] 壓縮效果監控和報告
- [ ] 性能影響測量和優化
- [ ] 錯誤處理和恢復機制
- [ ] 記憶體洩漏檢查

### 實作風險
- [ ] 模組化設計清晰
- [ ] 接口定義明確
- [ ] 測試覆蓋率充足
- [ ] 文檔完整準確

### 部署風險
- [ ] 漸進式部署計劃
- [ ] 回滾機制準備
- [ ] 監控和警報設置
- [ ] 用戶影響評估

## 完成標準

### 代碼質量
- [ ] 所有單元測試通過
- [ ] 代碼覆蓋率 > 80%
- [ ] 無嚴重的靜態分析警告
- [ ] 代碼審查通過

### 功能完整性
- [ ] 所有計劃功能實作完成
- [ ] 端到端測試通過
- [ ] 性能指標達標
- [ ] 兼容性測試通過

### 文檔完整性
- [ ] API 文檔完整
- [ ] 使用指南清晰
- [ ] 故障排除指南
- [ ] 架構設計文檔

### 部署準備
- [ ] 部署腳本準備
- [ ] 配置文件更新
- [ ] 監控指標定義
- [ ] 回滾計劃制定

## 後續維護

### 監控項目
- [ ] 壓縮比統計
- [ ] 處理時間監控
- [ ] 錯誤率追蹤
- [ ] 格式使用分布

### 優化機會
- [ ] 壓縮算法改進
- [ ] 選擇策略調優
- [ ] 性能瓶頸識別
- [ ] 新格式評估

### 擴展計劃
- [ ] LZ4 壓縮格式
- [ ] GZIP 壓縮格式
- [ ] 自適應壓縮
- [ ] 硬體加速支援
