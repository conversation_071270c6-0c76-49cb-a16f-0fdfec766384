# EPD Raw Data 轉換實作檢查清單

## 📋 實作階段檢查清單

### 階段一：基礎架構建立 ✅

#### 1.1 目錄結構創建
- [ ] 創建 `src/utils/epdConversion/` 主目錄
- [ ] 創建 `converters/` 子目錄
- [ ] 創建 `utils/` 子目錄
- [ ] 創建 `__tests__/` 測試目錄
- [ ] 設置 TypeScript 配置

#### 1.2 核心類型定義
- [ ] 定義 `ImageInfo` 介面 (包含 imagecode, x, y, width, height)
- [ ] 定義 `EPDConversionOptions` 介面 (使用 DisplayColorType + imagecode)
- [ ] 定義 `EPDConversionResult` 介面 (包含 rawdata, imageInfo, pixelData)
- [ ] 定義 `PixelData` 介面
- [ ] 導出所有類型到 `index.ts`

#### 1.3 基礎工具函數
- [ ] 實作 `rotationUtils.ts`
  - [ ] `rotateImageData()` 主函數
  - [ ] `rotateImageData90()` 90度旋轉
  - [ ] `rotateImageData180()` 180度旋轉
  - [ ] `rotateImageData270()` 270度旋轉
  - [ ] `getTemplateReverseRotation()` 計算反向旋轉角度
- [ ] 實作 `paddingUtils.ts`
  - [ ] `calculatePaddedWidth()` 寬度對齊計算
  - [ ] `padImageData()` 圖像填充
- [ ] 實作 `colorPalettes.ts`
  - [ ] `getColorPalette()` - 獲取預設調色板
  - [ ] `findExactColorMatch()` - 精確顏色比對
  - [ ] `findClosestColor()` - 備用最接近顏色查找
- [ ] 實作 `imageDataUtils.ts`
  - [ ] Canvas 到 ImageData 轉換
  - [ ] ImageData 處理工具

### 階段二：轉換器實作 ✅

#### 2.1 基礎轉換器抽象類
- [ ] 實作 `BaseConverter.ts`
  - [ ] 定義抽象方法
  - [ ] 實作通用轉換流程
  - [ ] `createImageInfo()` - 創建 ImageInfo 結構 (包含 imagecode)
  - [ ] `serializeImageInfo()` - 序列化 ImageInfo (Little Endian, 12 bytes)
  - [ ] `combineRawData()` - 組合 ImageInfo + 像素數據
  - [ ] `applyReverseRotation()` - 應用反向旋轉將模板轉回 0 度
  - [ ] 錯誤處理機制
  - [ ] 性能監控

#### 2.2 BW/GRAY16 轉換器
- [ ] 實作 `BWConverter.ts`
  - [ ] `calculatePaddedWidth()` - 2的倍數對齊
  - [ ] `calculateBufferSize()` - 4bit per pixel
  - [ ] `processPixel()` - 像素處理邏輯
  - [ ] `toGray16()` - 使用預設調色板進行精確匹配
  - [ ] `getPixelData()` - 返回純像素數據
  - [ ] 位元操作正確性驗證

#### 2.3 BWR 轉換器
- [ ] 實作 `BWRConverter.ts`
  - [ ] `calculatePaddedWidth()` - 8的倍數對齊
  - [ ] `calculateBufferSize()` - 1bit per pixel, 2 tables
  - [ ] `processPixel()` - 雙表格處理
  - [ ] `analyzeColor()` - 使用預設調色板進行精確匹配
  - [ ] 雙緩衝區管理

#### 2.4 BWRY 轉換器
- [ ] 實作 `BWRYConverter.ts`
  - [ ] `calculatePaddedWidth()` - 4的倍數對齊
  - [ ] `calculateBufferSize()` - 2bit per pixel
  - [ ] `processPixel()` - 四色處理邏輯
  - [ ] `analyzeColorBWRY()` - 使用預設調色板進行精確匹配
  - [ ] 2bit 位元操作

#### 2.5 主轉換器類
- [ ] 實作 `EpdConverter.ts`
  - [ ] `convert()` 靜態方法
  - [ ] `createConverter()` 工廠方法
  - [ ] 錯誤處理和日誌記錄
  - [ ] 性能優化

### 階段三：WebSocket 整合 ✅

#### 3.1 後端整合
- [ ] 修改 `sendPreviewToGateway.js`
  - [ ] 導入 EPD 轉換模組
  - [ ] 實作 `generateRawData()` 函數 (接收 imageCode 和 template 參數)
  - [ ] 實作 `getTemplateRotation()` 函數 (從模板獲取旋轉角度)
  - [ ] 設備 colorType 映射邏輯
  - [ ] 設備尺寸解析邏輯
  - [ ] imageCode 轉換邏輯 (hex string 轉 number)
  - [ ] 模板數據獲取邏輯

#### 3.2 消息格式更新
- [ ] 在 WebSocket 消息中添加 `rawdata` 字段
- [ ] 確保向後兼容性（保留 `imageData` 字段）
- [ ] 處理 rawdata 序列化（Uint8Array 轉 Array）
- [ ] 錯誤處理（rawdata 生成失敗時的處理）

#### 3.3 設備數據處理
- [ ] 實作 `mapDeviceColorType()` 函數
- [ ] 實作 `parseDeviceSize()` 函數
- [ ] 實作 `getDeviceRotation()` 函數
- [ ] Canvas 創建工具函數

### 階段四：測試實作 ✅

#### 4.1 單元測試
- [ ] `BWConverter.test.ts`
  - [ ] 純色像素轉換測試
  - [ ] 寬度對齊測試
  - [ ] 灰度級別測試
  - [ ] 邊界條件測試
- [ ] `BWRConverter.test.ts`
  - [ ] 三色分析測試
  - [ ] 雙表格生成測試
  - [ ] 位元操作測試
- [ ] `BWRYConverter.test.ts`
  - [ ] 四色分析測試
  - [ ] 2bit 編碼測試
- [ ] `rotationUtils.test.ts`
  - [ ] 各角度旋轉測試
  - [ ] 像素位置正確性測試
  - [ ] 反向旋轉計算測試
  - [ ] 模板旋轉處理測試
- [ ] `paddingUtils.test.ts`
  - [ ] 寬度對齊計算測試
  - [ ] 圖像填充測試

#### 4.2 整合測試
- [ ] WebSocket 消息格式測試
- [ ] 端到端圖片傳輸測試
- [ ] 多設備並發處理測試
- [ ] 大圖片處理性能測試
- [ ] 錯誤處理機制測試

#### 4.3 測試數據準備
- [ ] 創建各種尺寸的測試圖片
- [ ] 準備不同 colorType 的設備模擬數據
- [ ] 準備旋轉角度測試案例
- [ ] 準備邊界寬度測試案例

### 階段五：性能優化和驗證 ✅

#### 5.1 性能優化
- [ ] 記憶體使用優化
- [ ] 大圖片處理優化
- [ ] 異步處理實作
- [ ] 緩存機制考慮

#### 5.2 品質驗證
- [ ] 轉換結果視覺驗證
- [ ] 與原始 Go 代碼結果比較
- [ ] 不同設備尺寸測試
- [ ] 旋轉正確性驗證

#### 5.3 文檔完善
- [ ] API 文檔撰寫
- [ ] 使用範例提供
- [ ] 故障排除指南
- [ ] 性能調優建議

## 🔍 關鍵驗證點

### 技術正確性驗證
- [ ] **ImageInfo 結構**：正確序列化為 12 字節 (Little Endian)
- [ ] **imagecode 字段**：與 WebSocket 發送的 imageCode 相同
- [ ] **rawdata 格式**：ImageInfo (12 bytes) + 像素數據
- [ ] **位元對齊**：確保各格式的寬度對齊正確
- [ ] **旋轉處理**：模板反向旋轉正確，point(0,0) 始終為左上角
- [ ] **模板旋轉**：正確獲取模板旋轉角度並應用反向旋轉
- [ ] **精確顏色匹配**：使用預設調色板進行精確比對，避免範圍判斷
- [ ] **數據格式**：rawdata 格式符合 EPD 要求

### 性能要求驗證
- [ ] **轉換時間**：< 2秒（一般圖片）
- [ ] **記憶體使用**：< 100MB
- [ ] **並發處理**：支援多設備同時轉換
- [ ] **錯誤恢復**：轉換失敗時的優雅處理

### 兼容性驗證
- [ ] **向後兼容**：現有 Gateway 仍可正常工作
- [ ] **數據完整性**：imageData 和 rawdata 同時提供
- [ ] **錯誤處理**：rawdata 生成失敗時不影響原有流程

## 📊 測試覆蓋率目標

- **單元測試覆蓋率**：> 90%
- **整合測試覆蓋率**：> 80%
- **關鍵路徑覆蓋率**：100%

## 🚀 部署檢查清單

### 部署前檢查
- [ ] 所有測試通過
- [ ] 性能基準測試通過
- [ ] 文檔完整
- [ ] 代碼審查完成

### 部署後驗證
- [ ] WebSocket 消息格式正確
- [ ] Gateway 接收 rawdata 正常
- [ ] 現有功能無回歸
- [ ] 監控和日誌正常

## 📝 風險緩解檢查

- [ ] **回滾計劃**：準備代碼回滾方案
- [ ] **監控告警**：設置性能和錯誤監控
- [ ] **漸進部署**：考慮分階段部署策略
- [ ] **用戶通知**：準備功能更新通知

## ✅ 最終驗收標準

1. **功能完整性**
   - [ ] 支援所有指定的 DisplayColorType 格式
   - [ ] 正確處理圖片旋轉
   - [ ] WebSocket 消息包含 rawdata 字段 (ImageInfo + 像素數據)
   - [ ] ImageInfo 結構正確序列化 (12 bytes, Little Endian)
   - [ ] imagecode 字段與 WebSocket imageCode 一致

2. **品質標準**
   - [ ] 代碼品質符合團隊標準
   - [ ] 測試覆蓋率達標
   - [ ] 性能指標符合要求

3. **維護性**
   - [ ] 模組化設計便於擴充
   - [ ] 完整的錯誤處理和日誌
   - [ ] 清晰的文檔和註釋

4. **穩定性**
   - [ ] 通過所有測試案例
   - [ ] 無已知的嚴重缺陷
   - [ ] 向後兼容性保證
