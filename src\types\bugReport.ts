/**
 * Bug回報相關類型定義
 */

export interface BugReport {
  _id: string;
  title: string;
  content: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  currentPage: string;
  imageId?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy?: string;
  notes?: string;
}

export interface CreateBugReportRequest {
  title: string;
  content: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  currentPage?: string;
  image?: File;
}

export interface UpdateBugReportRequest {
  status?: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority?: 'low' | 'medium' | 'high' | 'critical';
  notes?: string;
}

export interface BugReportListResponse {
  data: BugReport[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface BugReportFilters {
  status?: string;
  priority?: string;
  page?: number;
  limit?: number;
}
