# 刷圖計畫即時更新控制項實現

## 概述

為刷圖計畫頁面添加了即時更新控制項，讓用戶可以選擇是否啟用即時更新功能。

## 實現功能

### 1. 即時更新開關

在刷圖計畫頁面的操作欄中添加了一個即時更新開關按鈕：

- **位置**: 頁面頂部操作欄，位於調度器狀態指示器之前
- **外觀**: 
  - 開啟時：藍色背景，帶有動畫脈衝效果的Activity圖標
  - 關閉時：灰色背景，靜態的Activity圖標
- **功能**: 點擊可切換即時更新的開啟/關閉狀態

### 2. 即時更新邏輯

```typescript
// 即時更新控制項狀態
const [realTimeEnabled, setRealTimeEnabled] = useState(true);

// 即時更新開關處理函數
const handleToggleRealTime = () => {
  setRealTimeEnabled(!realTimeEnabled);
};
```

### 3. WebSocket訂閱控制

當即時更新關閉時，組件不會訂閱WebSocket事件：

```typescript
useEffect(() => {
  if (!realTimeEnabled) {
    return; // 如果即時更新關閉，不訂閱WebSocket
  }
  
  // 訂閱刷圖計畫更新
  const unsubscribe = subscribeToRefreshPlanUpdate(
    store.id,
    handleRefreshPlanUpdate,
    {}
  );

  return () => {
    unsubscribe();
  };
}, [store.id, realTimeEnabled]);
```

## 用戶體驗

### 視覺反饋

1. **按鈕狀態**:
   - 開啟：`即時更新 開啟` (藍色背景，脈衝動畫)
   - 關閉：`即時更新 關閉` (灰色背景，無動畫)

2. **工具提示**:
   - 開啟時：`點擊關閉即時更新`
   - 關閉時：`點擊開啟即時更新`

### 功能行為

1. **即時更新開啟時**:
   - 自動接收WebSocket推送的計畫狀態更新
   - 計畫列表會實時反映後端變化
   - 運行狀態會即時更新

2. **即時更新關閉時**:
   - 不接收WebSocket推送
   - 需要手動點擊刷新按鈕更新數據
   - 減少網絡流量和CPU使用

## 技術實現

### 組件結構

```jsx
{/* 即時更新開關 */}
<button
  onClick={handleToggleRealTime}
  className={`flex items-center px-3 py-2 rounded-md border text-sm font-medium transition-colors ${
    realTimeEnabled
      ? 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200'
      : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200'
  }`}
  title={realTimeEnabled ? '點擊關閉即時更新' : '點擊開啟即時更新'}
>
  <Activity className={`w-4 h-4 mr-2 ${realTimeEnabled ? 'animate-pulse' : ''}`} />
  即時更新 {realTimeEnabled ? '開啟' : '關閉'}
</button>
```

### 狀態管理

- 使用React的`useState`管理即時更新開關狀態
- 默認開啟即時更新功能
- 狀態變化會觸發WebSocket訂閱的重新評估

## 優勢

1. **用戶控制**: 用戶可以根據需要選擇是否啟用即時更新
2. **性能優化**: 關閉即時更新可以減少不必要的網絡請求
3. **電池節省**: 在移動設備上可以節省電池使用
4. **網絡友好**: 在網絡條件不佳時可以關閉即時更新
5. **簡潔設計**: 界面簡潔，功能明確

## 兼容性

- 與現有的刷圖計畫功能完全兼容
- 不影響手動刷新功能
- 保持與其他頁面的一致性設計風格
