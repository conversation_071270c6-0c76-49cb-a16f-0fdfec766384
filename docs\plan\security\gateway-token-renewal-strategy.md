# Gateway Token 續約策略分析

## 📋 問題分析

### 當前 Gateway Token 機制
- **過期時間**: 30天 (建議改為 24小時)
- **存儲位置**: 資料庫中明文存儲
- **使用方式**: WebSocket 連接時作為 URL 參數傳遞
- **更新方式**: 目前需要手動重新生成

### 問題場景
當 Gateway Token 過期時，會發生以下情況：
1. Gateway 嘗試連接 WebSocket 時認證失敗
2. 連接被拒絕，Gateway 無法正常工作
3. 需要人工介入重新生成 Token

---

## 🎯 解決方案比較

### 方案一: Web 手動重新生成 (當前方式)
```mermaid
graph TD
    A[Gateway Token 過期] --> B[WebSocket 連接失敗]
    B --> C[管理員發現問題]
    C --> D[登入 Web 管理介面]
    D --> E[重新生成 Gateway Token]
    E --> F[複製新 Token 到 Gateway]
    F --> G[Gateway 重新連接]
```

**優點**:
- 實作簡單
- 安全性高 (需要人工確認)
- 完全可控

**缺點**:
- 需要人工介入
- 可能導致服務中斷
- 不適合大量 Gateway 部署

### 方案二: Gateway 自動續約 (推薦)
```mermaid
graph TD
    A[Gateway Token 即將過期] --> B[Gateway 發送續約請求]
    B --> C[服務器驗證 Gateway 身份]
    C --> D[生成新 Token]
    D --> E[返回新 Token 給 Gateway]
    E --> F[Gateway 更新本地 Token]
    F --> G[繼續正常服務]
```

**優點**:
- 無需人工介入
- 服務不中斷
- 適合大規模部署
- 提升用戶體驗

**缺點**:
- 實作較複雜
- 需要額外的安全機制

---

## 🚀 推薦實作: Gateway 自動續約機制

### 1. 後端 API 設計

#### 1.1 Gateway Token 續約端點
```javascript
// server/routes/gatewayAuthApi.js
const express = require('express');
const router = express.Router();
const { verifyToken } = require('../utils/jwtUtils');
const { generateGatewayToken } = require('../utils/jwtUtils');

/**
 * Gateway Token 自動續約
 * POST /api/gateway/auth/renew
 */
router.post('/gateway/auth/renew', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing or invalid authorization header' });
    }

    const currentToken = authHeader.substring(7);
    
    // 驗證當前 Token (即使已過期也要能解析)
    let decoded;
    try {
      decoded = verifyToken(currentToken);
    } catch (error) {
      // 如果 Token 過期，嘗試解析但不驗證簽名
      if (error.name === 'TokenExpiredError') {
        decoded = jwt.decode(currentToken);
        if (!decoded || decoded.type !== 'gateway') {
          return res.status(401).json({ error: 'Invalid token type' });
        }
      } else {
        return res.status(401).json({ error: 'Invalid token' });
      }
    }

    const { gatewayId, storeId, macAddress } = decoded;

    // 驗證 Gateway 是否存在且有效
    const { db } = await getDbConnection();
    const gateway = await db.collection('gateways').findOne({
      _id: new ObjectId(gatewayId),
      storeId: storeId,
      macAddress: macAddress,
      status: { $ne: 'disabled' }
    });

    if (!gateway) {
      return res.status(404).json({ error: 'Gateway not found or disabled' });
    }

    // 生成新 Token
    const newTokenData = generateGatewayToken({
      gatewayId,
      storeId, 
      macAddress
    });

    // 更新資料庫中的 Token (加密存儲)
    const encryptedToken = encrypt(newTokenData.token);
    await db.collection('gateways').updateOne(
      { _id: new ObjectId(gatewayId) },
      { 
        $set: { 
          'websocket.token': encryptedToken,
          'websocket.lastRenewed': new Date(),
          'websocket.expiresAt': new Date(Date.now() + (24 * 60 * 60 * 1000)) // 24小時後過期
        }
      }
    );

    // 記錄續約事件
    console.log(`Gateway ${gatewayId} token renewed successfully`);
    
    res.json({
      token: newTokenData.token,
      expiresIn: 24 * 60 * 60, // 24小時 (秒)
      expiresAt: Math.floor(Date.now() / 1000) + (24 * 60 * 60),
      renewedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Gateway token renewal failed:', error);
    res.status(500).json({ error: 'Token renewal failed' });
  }
});

module.exports = router;
```

#### 1.2 Token 過期檢查中間件
```javascript
// server/middleware/gatewayAuth.js
const { verifyToken, isTokenExpiringSoon } = require('../utils/jwtUtils');

const checkGatewayTokenExpiry = (req, res, next) => {
  const token = req.headers.authorization?.substring(7);
  
  if (token && isTokenExpiringSoon(token, 60 * 60)) { // 1小時內過期
    res.setHeader('X-Token-Expiring', 'true');
    res.setHeader('X-Token-Expires-In', getTokenRemainingTime(token));
  }
  
  next();
};

module.exports = { checkGatewayTokenExpiry };
```

### 2. Gateway 端實作 (Python 範例)

#### 2.1 Token 管理類
```python
import asyncio
import aiohttp
import jwt
import time
import logging
from datetime import datetime, timedelta

class GatewayTokenManager:
    def __init__(self, gateway_config):
        self.gateway_id = gateway_config['gateway_id']
        self.store_id = gateway_config['store_id']
        self.mac_address = gateway_config['mac_address']
        self.server_url = gateway_config['server_url']
        self.current_token = gateway_config.get('initial_token')
        self.token_expires_at = None
        self.renewal_task = None
        
    async def start_token_manager(self):
        """啟動 Token 管理器"""
        if self.current_token:
            # 解析當前 Token 的過期時間
            try:
                decoded = jwt.decode(self.current_token, options={"verify_signature": False})
                self.token_expires_at = decoded.get('exp')
            except Exception as e:
                logging.warning(f"無法解析初始 Token: {e}")
        
        # 啟動自動續約任務
        self.renewal_task = asyncio.create_task(self._auto_renewal_loop())
        
    async def _auto_renewal_loop(self):
        """自動續約循環"""
        while True:
            try:
                if self._should_renew_token():
                    await self._renew_token()
                
                # 每 30 分鐘檢查一次
                await asyncio.sleep(30 * 60)
                
            except Exception as e:
                logging.error(f"Token 續約循環錯誤: {e}")
                await asyncio.sleep(60)  # 錯誤時 1 分鐘後重試
    
    def _should_renew_token(self):
        """檢查是否需要續約 Token"""
        if not self.token_expires_at:
            return True
            
        # 在過期前 2 小時續約
        renewal_time = self.token_expires_at - (2 * 60 * 60)
        return time.time() >= renewal_time
    
    async def _renew_token(self):
        """執行 Token 續約"""
        try:
            headers = {
                'Authorization': f'Bearer {self.current_token}',
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f'{self.server_url}/api/gateway/auth/renew',
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        # 更新 Token 資訊
                        self.current_token = data['token']
                        self.token_expires_at = data['expiresAt']
                        
                        # 保存到本地配置檔案
                        await self._save_token_to_config()
                        
                        logging.info(f"Token 續約成功，新過期時間: {datetime.fromtimestamp(self.token_expires_at)}")
                        
                    else:
                        error_data = await response.json()
                        logging.error(f"Token 續約失敗: {error_data.get('error', 'Unknown error')}")
                        
        except Exception as e:
            logging.error(f"Token 續約請求失敗: {e}")
    
    async def _save_token_to_config(self):
        """保存 Token 到本地配置檔案"""
        config = {
            'gateway_id': self.gateway_id,
            'store_id': self.store_id,
            'mac_address': self.mac_address,
            'server_url': self.server_url,
            'current_token': self.current_token,
            'token_expires_at': self.token_expires_at,
            'last_updated': datetime.now().isoformat()
        }
        
        # 寫入配置檔案 (加密存儲)
        with open('gateway_config.json', 'w') as f:
            json.dump(config, f, indent=2)
    
    def get_current_token(self):
        """獲取當前有效的 Token"""
        return self.current_token
    
    async def stop(self):
        """停止 Token 管理器"""
        if self.renewal_task:
            self.renewal_task.cancel()
```

#### 2.2 WebSocket 連接管理
```python
import websockets
import json

class GatewayWebSocketClient:
    def __init__(self, token_manager):
        self.token_manager = token_manager
        self.websocket = None
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        
    async def connect(self):
        """連接到 WebSocket 服務器"""
        while self.reconnect_attempts < self.max_reconnect_attempts:
            try:
                token = self.token_manager.get_current_token()
                ws_url = f"ws://server:3001/ws/store/{self.token_manager.store_id}/gateway/{self.token_manager.gateway_id}?token={token}"
                
                self.websocket = await websockets.connect(ws_url)
                self.reconnect_attempts = 0  # 重置重連次數
                
                logging.info("WebSocket 連接成功")
                
                # 啟動消息處理
                await self._handle_messages()
                
            except websockets.exceptions.ConnectionClosedError as e:
                if e.code == 1008:  # Token 相關錯誤
                    logging.warning("Token 可能已過期，嘗試續約...")
                    await self.token_manager._renew_token()
                
                self.reconnect_attempts += 1
                wait_time = min(2 ** self.reconnect_attempts, 60)  # 指數退避
                logging.info(f"WebSocket 連接失敗，{wait_time}秒後重試...")
                await asyncio.sleep(wait_time)
                
            except Exception as e:
                logging.error(f"WebSocket 連接錯誤: {e}")
                self.reconnect_attempts += 1
                await asyncio.sleep(5)
    
    async def _handle_messages(self):
        """處理 WebSocket 消息"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                
                # 檢查服務器是否提示 Token 即將過期
                if data.get('type') == 'token_expiring_warning':
                    logging.info("收到 Token 過期警告，立即續約...")
                    await self.token_manager._renew_token()
                    
                # 處理其他消息...
                await self._process_message(data)
                
        except websockets.exceptions.ConnectionClosed:
            logging.info("WebSocket 連接已關閉")
        except Exception as e:
            logging.error(f"消息處理錯誤: {e}")
```

### 3. 系統配置增強

#### 3.1 Token 過期時間配置
```javascript
// 在系統配置中添加 Gateway Token 相關設定
const gatewayTokenConfig = {
  expiry: '24h',              // Token 有效期
  renewalThreshold: '2h',     // 提前續約時間
  maxRenewalAttempts: 3,      // 最大續約嘗試次數
  autoRenewalEnabled: true,   // 是否啟用自動續約
  gracePeriod: '1h'          // 過期後的寬限期
};
```

#### 3.2 監控與告警
```javascript
// 監控 Gateway Token 狀態
const monitorGatewayTokens = async () => {
  const { db } = await getDbConnection();
  const expiringSoon = await db.collection('gateways').find({
    'websocket.expiresAt': {
      $lt: new Date(Date.now() + (4 * 60 * 60 * 1000)) // 4小時內過期
    },
    status: 'online'
  }).toArray();
  
  if (expiringSoon.length > 0) {
    console.warn(`發現 ${expiringSoon.length} 個 Gateway Token 即將過期`);
    // 發送告警通知
  }
};
```

---

## 📊 方案建議

### 🎯 **推薦方案: 混合模式**

1. **主要機制**: Gateway 自動續約
   - 提供無縫的服務體驗
   - 減少人工維護成本

2. **備用機制**: Web 手動重新生成
   - 當自動續約失敗時的後備方案
   - 提供管理員完全控制權

3. **監控告警**: 主動監控 Token 狀態
   - 提前發現潛在問題
   - 確保服務穩定性

### 🔧 **實作優先級**

1. **第一階段** (2週): 實作 Gateway 自動續約 API
2. **第二階段** (1週): 開發 Gateway 端續約邏輯  
3. **第三階段** (1週): 添加監控告警機制
4. **第四階段** (1週): Web 管理介面增強

這樣的設計既保證了系統的自動化程度，又保留了必要的人工控制能力，是最佳的平衡方案。
