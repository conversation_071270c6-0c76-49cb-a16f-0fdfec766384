// tests/dataFieldApi.test.js
const request = require('supertest');
const express = require('express');
const { createMockCollection } = require('./helpers/mockDb');
const createMockDataFieldApi = require('./helpers/mockDataFieldApi');

// 防止實際路由檔案被引入
jest.mock('../routes/dataFieldApi', () => {
  return {};
});

describe('資料欄位 API 測試', () => {
  let app;
  let mockCollection;
  let router;
  beforeEach(() => {
    // 創建 Express 應用
    app = express();

    // 初始化模擬資料
    const initialData = [
      { _id: '1', name: '欄位1', type: 'text', section: 'header' },
      { _id: '2', name: '欄位2', type: 'number', section: 'body' },
    ];

    // 創建模擬集合
    mockCollection = createMockCollection(initialData);

    // 創建模擬的資料欄位 API 路由
    router = createMockDataFieldApi(mockCollection);

    // 使用模擬路由
    app.use(express.json());
    app.use('/api', router);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/dataFields', () => {
    test('應該返回所有資料欄位', async () => {
      const response = await request(app).get('/api/dataFields');

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
      expect(mockCollection.find).toHaveBeenCalled();
    });

    test('應該處理獲取資料欄位時的錯誤', async () => {
      // 模擬錯誤
      const mockToArray = jest.fn().mockRejectedValueOnce(new Error('資料庫錯誤'));
      mockCollection.find.mockReturnValueOnce({
        toArray: mockToArray
      });

      const response = await request(app).get('/api/dataFields');

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('GET /api/dataFields/section/:section', () => {
    test('應該返回特定區塊的資料欄位', async () => {
      // 模擬特定區塊的資料
      mockCollection.find.mockImplementationOnce((query) => ({
        toArray: jest.fn().mockResolvedValue([
          { _id: '1', name: '欄位1', type: 'text', section: query.section }
        ])
      }));

      const response = await request(app).get('/api/dataFields/section/header');

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(1);
      expect(response.body[0].section).toBe('header');
      expect(mockCollection.find).toHaveBeenCalledWith({ section: 'header' });
    });

    test('應該處理獲取特定區塊資料欄位時的錯誤', async () => {
      // 模擬錯誤
      mockCollection.find.mockImplementationOnce(() => {
        throw new Error('資料庫錯誤');
      });

      const response = await request(app).get('/api/dataFields/section/header');

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error');
    });
  });

  // 更多測試可以添加：
  // 1. 創建資料欄位的測試
  // 2. 更新資料欄位的測試
  // 3. 刪除資料欄位的測試
});
