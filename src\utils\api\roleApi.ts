import { buildEndpointUrl } from './apiConfig';

/**
 * 獲取所有角色
 * @param type 可選的角色類型過濾
 * @returns 角色列表
 */
export async function getAllRoles(type?: string): Promise<any[]> {
  try {
    // 構建查詢參數
    const queryParams = new URLSearchParams();
    if (type) {
      queryParams.append('type', type);
    }
    
    // 構建URL
    const url = type 
      ? `${buildEndpointUrl('roles')}?${queryParams}`
      : buildEndpointUrl('roles');
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });
    
    if (!response.ok) {
      throw new Error(`獲取角色列表失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('獲取角色列表錯誤:', error);
    throw error;
  }
}

/**
 * 獲取單個角色
 * @param id 角色ID
 * @returns 角色詳情
 */
export async function getRole(id: string): Promise<any> {
  try {
    const response = await fetch(buildEndpointUrl('roles', id), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });
    
    if (!response.ok) {
      throw new Error(`獲取角色詳情失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`獲取角色詳情錯誤 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 創建角色
 * @param roleData 角色數據
 * @returns 創建的角色
 */
export async function createRole(roleData: any): Promise<any> {
  try {
    const response = await fetch(buildEndpointUrl('roles'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(roleData),
      credentials: 'include'
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `創建角色失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('創建角色錯誤:', error);
    throw error;
  }
}

/**
 * 更新角色
 * @param id 角色ID
 * @param roleData 更新的角色數據
 * @returns 更新後的角色
 */
export async function updateRole(id: string, roleData: any): Promise<any> {
  try {
    const response = await fetch(buildEndpointUrl('roles', id), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(roleData),
      credentials: 'include'
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `更新角色失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`更新角色錯誤 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 刪除角色
 * @param id 角色ID
 */
export async function deleteRole(id: string): Promise<void> {
  try {
    const response = await fetch(buildEndpointUrl('roles', id), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `刪除角色失敗: ${response.statusText}`);
    }
  } catch (error) {
    console.error(`刪除角色錯誤 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 獲取可用權限列表
 * @returns 可用權限列表
 */
export async function getAvailablePermissions(): Promise<any[]> {
  try {
    const response = await fetch(buildEndpointUrl('permissions', 'available'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });
    
    if (!response.ok) {
      throw new Error(`獲取可用權限列表失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('獲取可用權限列表錯誤:', error);
    throw error;
  }
}
