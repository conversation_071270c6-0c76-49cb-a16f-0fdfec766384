// tests/helpers/mockTemplateValidator.js
/**
 * 模板驗證 API 的模擬路由
 */
const express = require('express');

// 創建模擬路由
function createMockTemplateValidatorApi() {
  const router = express.Router();
  
  // 模擬驗證器
  let validator = {
    validateTemplate: jest.fn().mockImplementation(template => {
      // 簡單的驗證邏輯
      const errors = [];
      
      if (!template.id) {
        errors.push('模板缺少 ID');
      }
      
      if (!template.name) {
        errors.push('模板缺少名稱');
      }
      
      return { 
        valid: errors.length === 0, 
        errors 
      };
    }),
    calculateChecksum: jest.fn().mockImplementation(template => {
      // 簡單模擬檢查碼生成
      return `checksum-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
    }),
    validateChecksum: jest.fn().mockImplementation((template, checksum) => {
      // 簡單模擬檢查碼驗證
      const newChecksum = validator.calculateChecksum(template);
      return checksum === newChecksum;
    })
  };
  
  // 設置驗證器
  router.setValidator = jest.fn().mockImplementation(v => {
    validator = v;
    return router;
  });

  // 驗證模板
  router.post('/validate-template', (req, res) => {
    try {
      const template = req.body;
      const result = validator.validateTemplate(template);
      
      if (!result.valid) {
        return res.status(400).json(result);
      }
      
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: '模板驗證失敗' });
    }
  });
  
  // 計算檢查碼
  router.post('/calculate-checksum', (req, res) => {
    try {
      const template = req.body;
      const checksum = validator.calculateChecksum(template);
      res.json({ checksum });
    } catch (error) {
      res.status(500).json({ error: '計算檢查碼失敗' });
    }
  });
  
  // 驗證檢查碼
  router.post('/validate-checksum', (req, res) => {
    try {
      const { template, checksum } = req.body;
      
      if (!template || !checksum) {
        return res.status(400).json({ error: '缺少必要參數' });
      }
      
      const valid = validator.validateChecksum(template, checksum);
      res.json({ valid });
    } catch (error) {
      res.status(500).json({ error: '檢查碼驗證失敗' });
    }
  });
  
  return router;
}

module.exports = createMockTemplateValidatorApi;
