<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>顏色轉換模組測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .canvas-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 15px;
        }
        .canvas-item {
            text-align: center;
        }
        .canvas-item canvas {
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .canvas-item p {
            margin: 10px 0 0 0;
            font-size: 14px;
            color: #666;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .controls label {
            display: inline-block;
            margin-right: 15px;
            font-weight: bold;
        }
        .controls select, .controls input {
            margin-right: 15px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .controls button {
            padding: 8px 16px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .controls button:hover {
            background: #005a87;
        }
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>顏色轉換模組測試</h1>
        <p>這個頁面用於測試新的統一顏色轉換模組，驗證不同顏色類型的轉換效果。</p>

        <div class="controls">
            <label for="colorType">顏色類型:</label>
            <select id="colorType">
                <option value="Gray16">Gray16 (黑白)</option>
                <option value="Black & White & Red">Black & White & Red (黑白紅)</option>
                <option value="Black & White & Red & Yellow">Black & White & Red & Yellow (黑白紅黃)</option>
                <option value="All colors">All colors (全彩)</option>
            </select>

            <label for="threshold">閾值:</label>
            <input type="range" id="threshold" min="0" max="255" value="128">
            <span id="thresholdValue">128</span>

            <button onclick="runTest()">執行測試</button>
            <button onclick="clearLog()">清除日誌</button>
        </div>

        <div class="test-section">
            <h3>原始圖像</h3>
            <div class="canvas-container">
                <div class="canvas-item">
                    <canvas id="originalCanvas" width="200" height="150"></canvas>
                    <p>原始測試圖像</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>轉換結果</h3>
            <div class="canvas-container" id="resultsContainer">
                <!-- 轉換結果將在這裡顯示 -->
            </div>
        </div>

        <div class="test-section">
            <h3>測試日誌</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script type="module">
        // 模擬 DisplayColorType 枚舉
        const DisplayColorType = {
            BW: "Gray16",
            BWR: "Black & White & Red",
            BWRY: "Black & White & Red & Yellow",
            ALL: "All colors"
        };

        // 簡化版的顏色轉換函數（用於測試）
        function getConversionStrategy(colorType) {
            const normalizedColorType = typeof colorType === 'string'
                ? colorType.toUpperCase()
                : colorType;

            switch (normalizedColorType) {
                case 'GRAY16':
                    return { algorithm: 'dithering', name: '抖動算法' };
                case 'BLACK & WHITE & RED':
                    return { algorithm: 'colorQuantization', name: '三色量化' };
                case 'BLACK & WHITE & RED & YELLOW':
                    return { algorithm: 'colorQuantization', name: '四色量化' };
                case 'ALL COLORS':
                    return { algorithm: 'original', name: '保持原色' };
                default:
                    return { algorithm: 'blackAndWhite', name: '黑白二值化' };
            }
        }

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function createTestImage() {
            const canvas = document.getElementById('originalCanvas');
            const ctx = canvas.getContext('2d');

            // 創建一個彩色測試圖像
            // 背景漸變
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
            gradient.addColorStop(0, '#ff0000');
            gradient.addColorStop(0.25, '#ffff00');
            gradient.addColorStop(0.5, '#00ff00');
            gradient.addColorStop(0.75, '#00ffff');
            gradient.addColorStop(1, '#0000ff');

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 添加一些形狀
            ctx.fillStyle = '#000000';
            ctx.fillRect(20, 20, 40, 40);

            ctx.fillStyle = '#ffffff';
            ctx.fillRect(80, 20, 40, 40);

            ctx.fillStyle = '#ff0000';
            ctx.beginPath();
            ctx.arc(50, 100, 20, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = '#ffff00';
            ctx.beginPath();
            ctx.arc(150, 100, 20, 0, 2 * Math.PI);
            ctx.fill();

            // 添加文字
            ctx.fillStyle = '#000000';
            ctx.font = '16px Arial';
            ctx.fillText('測試文字', 10, 140);

            log('創建測試圖像完成');
        }

        function simulateColorConversion(sourceCanvas, colorType, threshold) {
            const newCanvas = document.createElement('canvas');
            newCanvas.width = sourceCanvas.width;
            newCanvas.height = sourceCanvas.height;
            const ctx = newCanvas.getContext('2d');

            // 複製原始圖像
            ctx.drawImage(sourceCanvas, 0, 0);

            const imageData = ctx.getImageData(0, 0, newCanvas.width, newCanvas.height);
            const data = imageData.data;

            const strategy = getConversionStrategy(colorType);

            switch (strategy.algorithm) {
                case 'original':
                    // 保持原色，不做任何處理
                    break;

                case 'blackAndWhite':
                    // 黑白二值化
                    for (let i = 0; i < data.length; i += 4) {
                        const grayscale = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                        const color = grayscale < threshold ? 0 : 255;
                        data[i] = data[i + 1] = data[i + 2] = color;
                    }
                    break;

                case 'dithering':
                    // 簡化的抖動效果
                    for (let i = 0; i < data.length; i += 4) {
                        const grayscale = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                        const color = (grayscale + Math.random() * 50 - 25) < threshold ? 0 : 255;
                        data[i] = data[i + 1] = data[i + 2] = color;
                    }
                    break;

                case 'colorQuantization':
                    // 顏色量化
                    for (let i = 0; i < data.length; i += 4) {
                        const r = data[i];
                        const g = data[i + 1];
                        const b = data[i + 2];

                        // 使用與後端相同的顏色量化邏輯
                        let closestColor;
                        if (colorType.includes('YELLOW')) {
                            // 四色量化：黑、白、紅、黃 (BWRY)
                            // 檢查是否為黃色（高紅色和綠色，低藍色）
                            if (r > 180 && g > 180 && b < 100) {
                                closestColor = { r: 255, g: 255, b: 0 }; // 黃色
                            }
                            // 檢查是否為紅色（高紅色，低綠色和藍色）
                            else if (r > 150 && g < 100 && b < 100) {
                                closestColor = { r: 255, g: 0, b: 0 }; // 紅色
                            }
                            // 檢查是否為白色（高亮度）
                            else {
                                const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
                                if (brightness > 128) {
                                    closestColor = { r: 255, g: 255, b: 255 }; // 白色
                                } else {
                                    closestColor = { r: 0, g: 0, b: 0 }; // 黑色
                                }
                            }
                        } else {
                            // 三色量化：黑、白、紅 (BWR)
                            // 檢查是否為紅色（高紅色，低綠色和藍色）
                            if (r > 150 && g < 100 && b < 100) {
                                closestColor = { r: 255, g: 0, b: 0 }; // 紅色
                            }
                            // 檢查是否為白色（高亮度）
                            else {
                                const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
                                if (brightness > 128) {
                                    closestColor = { r: 255, g: 255, b: 255 }; // 白色
                                } else {
                                    closestColor = { r: 0, g: 0, b: 0 }; // 黑色
                                }
                            }
                        }

                        data[i] = closestColor.r;
                        data[i + 1] = closestColor.g;
                        data[i + 2] = closestColor.b;
                    }
                    break;
            }

            ctx.putImageData(imageData, 0, 0);
            return { canvas: newCanvas, strategy };
        }

        function runTest() {
            const colorType = document.getElementById('colorType').value;
            const threshold = parseInt(document.getElementById('threshold').value);

            log(`開始測試 - 顏色類型: ${colorType}, 閾值: ${threshold}`);

            const originalCanvas = document.getElementById('originalCanvas');
            const result = simulateColorConversion(originalCanvas, colorType, threshold);

            // 清除之前的結果
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = '';

            // 顯示轉換結果
            const canvasItem = document.createElement('div');
            canvasItem.className = 'canvas-item';

            const canvas = result.canvas;
            canvas.style.border = '1px solid #ccc';
            canvas.style.borderRadius = '4px';

            const description = document.createElement('p');
            description.textContent = `${colorType} (${result.strategy.name})`;

            canvasItem.appendChild(canvas);
            canvasItem.appendChild(description);
            resultsContainer.appendChild(canvasItem);

            log(`轉換完成 - 使用算法: ${result.strategy.name}`);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createTestImage();

            // 閾值滑塊事件
            const thresholdSlider = document.getElementById('threshold');
            const thresholdValue = document.getElementById('thresholdValue');

            thresholdSlider.addEventListener('input', function() {
                thresholdValue.textContent = this.value;
            });

            log('顏色轉換模組測試頁面初始化完成');
        });

        // 全局函數
        window.runTest = runTest;
        window.clearLog = clearLog;
    </script>
</body>
</html>
