# 設備管理頁面閃爍問題修復

## 問題描述

當設備已經被註冊到某些門店，另外門店又回報這些MAC地址時，會變成以下狀態，然後設備管理頁面UI會一直閃爍，因為一直"重新獲取完整設備列表"。

### 問題日誌示例
```
收到設備狀態更新: 13 個設備, 更新類型: batch
DevicesPage.tsx:475 發現 8 個新設備，重新獲取完整設備列表
```

## 問題根源分析

1. **WebSocket事件處理邏輯問題**：
   - 當其他門店的gateway回報已存在的設備時，這些設備的ID不在當前門店的設備列表中
   - 前端錯誤地將這些設備判斷為"新設備"
   - 觸發了不必要的`fetchDevices()`調用，導致頁面閃爍

2. **判斷邏輯缺陷**：
   - 原始邏輯只檢查設備ID是否在當前設備列表中
   - 沒有考慮跨門店設備可能有相同MAC地址但不同ID的情況

## 修復方案

### 1. 前端修復 (src/components/DevicesPage.tsx)

修改設備狀態更新處理邏輯，在判斷是否為"新設備"時：

1. **雙重檢查機制**：
   - 檢查設備ID是否不在當前設備列表中
   - 檢查設備MAC地址是否已存在於當前門店

2. **過濾跨門店重複設備**：
   - 如果設備ID不在當前列表中，但MAC地址已存在，則忽略該設備
   - 只有當設備ID和MAC地址都不存在時，才認為是真正的新設備

3. **增強日誌記錄**：
   - 添加詳細的調試日誌，幫助識別跨門店設備回報
   - 記錄被忽略的設備信息

### 2. 移除舊事件監聽器 (src/components/DevicesPage.tsx)

移除了舊的 `deviceUpdated` 事件監聽器：
- 這個監聽器會無條件觸發 `fetchDevices()`
- 在WebSocket實時更新功能實現後已不再需要
- 是導致頁面閃爍的主要原因之一

### 3. 移除舊事件觸發 (src/components/DeviceDetailPage.tsx)

移除了 `DeviceDetailPage` 中觸發 `deviceUpdated` 事件的代碼：
- 在發送預覽圖成功後不再觸發舊事件
- 現在依賴WebSocket實時更新機制

### 4. 修復WebSocket設備狀態廣播邏輯 (server/services/websocketService.js)

**這是最關鍵的修復**：修改了設備狀態廣播的門店分組邏輯：
- **問題根源**：原本使用網關的 `storeId` 來廣播所有該網關發現的設備
- **修復方案**：改為按每個設備實際所屬的 `storeId` 分組廣播
- **具體改動**：
  - 將 `processedDevices` 按 `device.storeId` 分組
  - 為每個門店分別調用 `deviceStatusBroadcaster.scheduleUpdate()`
  - 避免跨門店設備被錯誤廣播到當前門店

### 修復後的邏輯

#### 1. 新設備判斷邏輯
```typescript
// 檢查是否有真正的新設備需要添加
const newDevices = event.devices.filter(eventDevice => {
  // 檢查設備是否不在當前設備列表中（按ID比較）
  const isNotInCurrentList = !prevDevices.some(existingDevice => existingDevice._id === eventDevice._id);

  // 檢查設備是否已存在於當前門店（按MAC地址比較）
  const macExistsInCurrentStore = prevDevices.some(existingDevice =>
    existingDevice.macAddress === eventDevice.macAddress
  );

  // 如果設備ID不在當前列表中，但MAC地址已存在，說明這是其他門店回報的已存在設備
  if (isNotInCurrentList && macExistsInCurrentStore) {
    console.log(`忽略其他門店回報的已存在設備: MAC=${eventDevice.macAddress}, ID=${eventDevice._id}`);
    return false;
  }

  // 只有當設備ID不在當前列表中且MAC地址也不存在時，才認為是真正的新設備
  return isNotInCurrentList && !macExistsInCurrentStore;
});
```

#### 2. 移除舊事件監聽器
```typescript
// 修復前（會導致閃爍）
useEffect(() => {
  const handleDeviceUpdated = (event: Event) => {
    const customEvent = event as CustomEvent;
    if (customEvent.detail) {
      console.log('接收到設備更新事件，重新獲取設備列表');
      fetchDevices(); // 無條件觸發，導致閃爍
    }
  };
  window.addEventListener('deviceUpdated', handleDeviceUpdated);
  return () => window.removeEventListener('deviceUpdated', handleDeviceUpdated);
}, []);

// 修復後（已移除）
// 注意：舊的 deviceUpdated 事件監聽器已移除，現在使用WebSocket實時更新
```

#### 3. 移除舊事件觸發
```typescript
// 修復前（會觸發不必要的更新）
window.dispatchEvent(new CustomEvent('deviceUpdated', { detail: updatedDevice }));

// 修復後（已移除）
// 注意：移除了舊的 deviceUpdated 事件觸發，現在使用WebSocket實時更新
```

## 測試驗證

### 測試腳本
運行 `scripts/test-device-flicker-fix.js` 來驗證修復效果：

```bash
node scripts/test-device-flicker-fix.js
```

### 測試場景

1. **正常新設備**：
   - 新的MAC地址設備被gateway發現
   - ✅ 應該觸發設備列表重新獲取

2. **跨門店重複設備**：
   - 已存在的MAC地址被其他門店gateway回報
   - ✅ 不應該觸發設備列表重新獲取
   - ✅ 應該在控制台看到忽略日誌

3. **設備狀態更新**：
   - 現有設備的狀態、電池、信號強度更新
   - ✅ 應該正常更新，不觸發重新獲取

### 測試結果

```
✅ 修復成功！純跨門店設備回報不再觸發不必要的設備列表重新獲取
✅ 真正的新設備仍然會正確觸發設備列表更新
```

### 預期效果

1. **消除頁面閃爍**：
   - ✅ 跨門店設備回報不再觸發不必要的設備列表重新獲取
   - ✅ 頁面保持穩定，不會出現閃爍現象

2. **保持功能完整性**：
   - ✅ 真正的新設備仍然會觸發設備列表更新
   - ✅ 現有設備的狀態更新正常工作
   - ✅ 選取狀態和分頁狀態得到保持

3. **改善調試體驗**：
   - ✅ 詳細的日誌幫助識別問題
   - ✅ 清楚區分新設備和跨門店重複設備

## 相關文件

### 修改的文件
- `src/components/DevicesPage.tsx` - 主要修復文件
  - 改進新設備判斷邏輯
  - 移除舊的 deviceUpdated 事件監聽器
- `src/components/DeviceDetailPage.tsx` - 移除舊事件觸發
  - 移除 deviceUpdated 事件觸發

### 未修改的文件
- `server/services/websocketService.js` - WebSocket服務邏輯（無需修改）
- `server/routes/deviceApi.js` - 設備API邏輯（無需修改）

### 新增的文件
- `scripts/test-device-flicker-fix.js` - 測試驗證腳本
- `docs/fix-device-ui-flicker.md` - 修復文檔

## 使用說明

### 如何驗證修復效果

1. **開啟瀏覽器開發者工具**：
   - 按 F12 打開開發者工具
   - 切換到 Console 標籤

2. **觀察日誌輸出**：
   - 修復前：會看到頻繁的 "發現 X 個新設備，重新獲取完整設備列表"
   - 修復後：會看到 "忽略其他門店回報的已存在設備: MAC=XX:XX:XX:XX:XX:XX, ID=XXXXX"

3. **檢查頁面穩定性**：
   - 修復前：設備列表會頻繁閃爍刷新
   - 修復後：設備列表保持穩定，只有真正的新設備才會觸發刷新

### 監控關鍵日誌

在瀏覽器控制台中關注以下日誌：

```javascript
// 正常的設備狀態更新（不會觸發重新獲取）
"收到設備狀態更新: 13 個設備, 更新類型: batch"

// 跨門店重複設備被正確忽略
"忽略其他門店回報的已存在設備: MAC=00:11:22:33:44:55, ID=device1_store2"

// 只有真正的新設備才會觸發重新獲取
"發現 1 個新設備，重新獲取完整設備列表"
```

## 注意事項

1. **MAC地址唯一性**：
   - 系統中MAC地址在全局範圍內是唯一的
   - 但不同門店可能會有不同的設備ID對應相同MAC地址的情況

2. **WebSocket事件過濾**：
   - 修復只影響前端的事件處理邏輯
   - 不影響後端的設備創建和更新邏輯

3. **向後兼容性**：
   - 修復保持了原有的功能邏輯
   - 只是增加了更精確的新設備判斷機制

4. **性能改善**：
   - 減少了不必要的API調用
   - 提升了用戶體驗和系統響應速度
