# 預覽圖生成服務實作步驟（不使用Docker）

本文檔提供了預覽圖生成服務的實作步驟，不依賴Docker容器化。

## 目錄

1. [環境準備](#環境準備)
2. [共享渲染邏輯模組](#共享渲染邏輯模組)
3. [預覽服務開發](#預覽服務開發)
4. [主服務器整合](#主服務器整合)
5. [部署與配置](#部署與配置)
6. [測試與驗證](#測試與驗證)

## 環境準備

### 開發環境需求

1. Node.js v18+ 
2. npm 或 yarn
3. 支援 ES Modules 的環境

### 項目結構

```
epd-manager-lite/
├── shared-preview-renderer/     # 共享渲染邏輯模組
├── preview-service/             # 預覽服務
├── server/                      # 現有的主服務器
└── src/                         # 現有的前端代碼
```

### 初始設置

1. 創建共享模組目錄：

```bash
mkdir -p shared-preview-renderer/src
cd shared-preview-renderer
npm init -y
```

2. 創建預覽服務目錄：

```bash
mkdir -p preview-service
cd preview-service
npm init -y
```

## 共享渲染邏輯模組

### 1. 安裝依賴

在 `shared-preview-renderer` 目錄中：

```bash
npm install --save html2canvas
npm install --save-dev typescript rollup rollup-plugin-typescript2 @rollup/plugin-node-resolve @rollup/plugin-commonjs
```

### 2. 配置 TypeScript

創建 `tsconfig.json`：

```json
{
  "compilerOptions": {
    "target": "es2020",
    "module": "esnext",
    "moduleResolution": "node",
    "declaration": true,
    "outDir": "./dist",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

### 3. 配置 Rollup

創建 `rollup.config.js`：

```javascript
import typescript from 'rollup-plugin-typescript2';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';

export default {
  input: 'src/index.ts',
  output: [
    {
      file: 'dist/index.js',
      format: 'cjs',
      sourcemap: true
    },
    {
      file: 'dist/index.esm.js',
      format: 'esm',
      sourcemap: true
    },
    {
      file: 'dist/shared-preview-renderer.js',
      format: 'umd',
      name: 'SharedPreviewRenderer',
      sourcemap: true
    }
  ],
  plugins: [
    resolve(),
    commonjs(),
    typescript({
      tsconfig: './tsconfig.json'
    })
  ],
  external: ['html2canvas']
};
```

### 4. 更新 package.json

在 `package.json` 中添加：

```json
{
  "scripts": {
    "build": "rollup -c",
    "dev": "rollup -c -w"
  },
  "main": "dist/index.js",
  "module": "dist/index.esm.js",
  "types": "dist/index.d.ts"
}
```

### 5. 實現共享模組

從現有代碼中抽取渲染邏輯，創建以下文件：

- `src/adapters/index.ts` - 環境適配器接口
- `src/adapters/browser-adapter.ts` - 瀏覽器環境適配器
- `src/adapters/puppeteer-adapter.ts` - Puppeteer環境適配器
- `src/utils/canvas-utils.ts` - 畫布工具函數
- `src/utils/image-effects.ts` - 圖像效果處理
- `src/components/preview-renderer.ts` - 核心渲染組件
- `src/index.ts` - 導出所有模組

## 預覽服務開發

### 1. 安裝依賴

在 `preview-service` 目錄中：

```bash
npm install express cors body-parser puppeteer
npm install --save-dev typescript ts-node nodemon @types/express @types/cors
```

### 2. 配置 TypeScript

創建 `tsconfig.json`：

```json
{
  "compilerOptions": {
    "target": "es2020",
    "module": "commonjs",
    "outDir": "./dist",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

### 3. 更新 package.json

在 `package.json` 中添加：

```json
{
  "scripts": {
    "build": "tsc",
    "start": "node dist/server.js",
    "dev": "nodemon --exec ts-node src/server.ts"
  }
}
```

### 4. 實現預覽服務

創建 `src/server.ts`：

```typescript
import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import puppeteer from 'puppeteer';
import path from 'path';
import fs from 'fs';

// 創建 Express 應用
const app = express();
const PORT = process.env.PREVIEW_SERVICE_PORT || 3002;

// 中間件
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));

// 瀏覽器實例池
let browserInstance: puppeteer.Browser | null = null;

// 啟動瀏覽器
async function getBrowser() {
  if (!browserInstance) {
    browserInstance = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
  }
  return browserInstance;
}

// 預覽圖生成API
app.post('/api/preview/generate', async (req, res) => {
  try {
    const { template, bindingData, storeData, effectType, threshold } = req.body;
    
    // 獲取瀏覽器實例
    const browser = await getBrowser();
    const page = await browser.newPage();
    
    // 加載共享渲染模組
    const sharedModulePath = path.resolve(__dirname, '../../shared-preview-renderer/dist/shared-preview-renderer.js');
    const sharedModuleContent = fs.readFileSync(sharedModulePath, 'utf-8');
    await page.addScriptTag({ content: sharedModuleContent });
    
    // 加載 html2canvas
    await page.addScriptTag({ url: 'https://html2canvas.hertzen.com/dist/html2canvas.min.js' });
    
    // 注入數據
    await page.evaluate((template, bindingData, storeData, effectType, threshold) => {
      window.templateData = template;
      window.bindingData = bindingData;
      window.storeData = storeData;
      window.effectType = effectType || 'blackAndWhite';
      window.threshold = threshold || 128;
    }, template, bindingData, storeData, effectType, threshold);
    
    // 執行渲染
    const previewData = await page.evaluate(async () => {
      // 創建 Puppeteer 適配器
      const adapter = window.SharedPreviewRenderer.createAdapter('puppeteer');
      
      // 創建預覽渲染器
      const renderer = new window.SharedPreviewRenderer.PreviewRenderer(adapter);
      
      // 生成預覽圖
      return await renderer.generatePreview(
        window.templateData,
        window.bindingData,
        window.storeData,
        { effectType: window.effectType, threshold: window.threshold }
      );
    });
    
    await page.close();
    
    // 返回預覽圖數據
    res.json({
      success: true,
      previewData
    });
  } catch (error) {
    console.error('預覽圖生成失敗:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 批量生成預覽圖API
app.post('/api/preview/batch-generate', async (req, res) => {
  try {
    const { items, effectType, threshold } = req.body;
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        error: '無效的請求數據'
      });
    }
    
    const results = [];
    const browser = await getBrowser();
    
    for (const item of items) {
      try {
        const page = await browser.newPage();
        
        // 加載共享渲染模組
        const sharedModulePath = path.resolve(__dirname, '../../shared-preview-renderer/dist/shared-preview-renderer.js');
        const sharedModuleContent = fs.readFileSync(sharedModulePath, 'utf-8');
        await page.addScriptTag({ content: sharedModuleContent });
        
        // 加載 html2canvas
        await page.addScriptTag({ url: 'https://html2canvas.hertzen.com/dist/html2canvas.min.js' });
        
        // 注入數據
        await page.evaluate((template, bindingData, storeData, effectType, threshold) => {
          window.templateData = template;
          window.bindingData = bindingData;
          window.storeData = storeData;
          window.effectType = effectType || 'blackAndWhite';
          window.threshold = threshold || 128;
        }, item.template, item.bindingData, item.storeData, effectType, threshold);
        
        // 執行渲染
        const previewData = await page.evaluate(async () => {
          const adapter = window.SharedPreviewRenderer.createAdapter('puppeteer');
          const renderer = new window.SharedPreviewRenderer.PreviewRenderer(adapter);
          return await renderer.generatePreview(
            window.templateData,
            window.bindingData,
            window.storeData,
            { effectType: window.effectType, threshold: window.threshold }
          );
        });
        
        await page.close();
        
        results.push({
          id: item.id,
          success: true,
          previewData
        });
      } catch (error) {
        console.error(`項目 ${item.id} 預覽圖生成失敗:`, error);
        results.push({
          id: item.id,
          success: false,
          error: error.message
        });
      }
    }
    
    res.json({
      success: true,
      results
    });
  } catch (error) {
    console.error('批量預覽圖生成失敗:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 健康檢查API
app.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

// 啟動服務
app.listen(PORT, () => {
  console.log(`預覽服務運行在 http://localhost:${PORT}`);
});

// 優雅關閉
process.on('SIGINT', async () => {
  if (browserInstance) {
    await browserInstance.close();
  }
  process.exit(0);
});
```

## 主服務器整合

### 1. 安裝依賴

在 `server` 目錄中：

```bash
npm install node-fetch@2
```

### 2. 創建預覽服務客戶端

在 `server/services` 目錄中創建 `previewService.js`：

```javascript
const fetch = require('node-fetch');

// 預覽服務配置
const PREVIEW_SERVICE_URL = process.env.PREVIEW_SERVICE_URL || 'http://localhost:3002';

/**
 * 從預覽服務生成預覽圖
 */
async function generatePreviewFromService(template, bindingData, storeData, options = {}) {
  try {
    const response = await fetch(`${PREVIEW_SERVICE_URL}/api/preview/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        template,
        bindingData,
        storeData,
        effectType: options.effectType || 'blackAndWhite',
        threshold: options.threshold || 128
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`預覽服務錯誤: ${response.status} - ${errorText}`);
    }
    
    const result = await response.json();
    return result.previewData;
  } catch (error) {
    console.error('調用預覽服務失敗:', error);
    throw error;
  }
}

/**
 * 批量生成預覽圖
 */
async function batchGeneratePreviewsFromService(items, options = {}) {
  try {
    const response = await fetch(`${PREVIEW_SERVICE_URL}/api/preview/batch-generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        items,
        effectType: options.effectType || 'blackAndWhite',
        threshold: options.threshold || 128
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`預覽服務錯誤: ${response.status} - ${errorText}`);
    }
    
    const result = await response.json();
    return result.results;
  } catch (error) {
    console.error('調用批量預覽服務失敗:', error);
    throw error;
  }
}

module.exports = {
  generatePreviewFromService,
  batchGeneratePreviewsFromService
};
```

### 3. 更新發送預覽圖到網關服務

修改 `server/services/sendPreviewToGateway.js`：

```javascript
// 引入預覽服務
const previewService = require('./previewService');

// 在現有的 sendDevicePreviewToGateway 函數中添加
async function sendDevicePreviewToGateway(deviceId, options = {}) {
  try {
    // 獲取設備信息
    const device = await getDeviceById(deviceId);
    
    // 如果設備沒有預覽圖，嘗試生成一個
    if (!device.previewImage) {
      console.log(`設備 ${deviceId} 沒有預覽圖，嘗試生成...`);
      
      // 檢查設備是否有模板和綁定數據
      if (device.templateId && device.dataBindings) {
        try {
          // 獲取模板
          const template = await getTemplateById(device.templateId);
          
          // 獲取門店數據
          const storeData = await getStoreData(device.storeId);
          
          // 使用預覽服務生成預覽圖
          const previewData = await previewService.generatePreviewFromService(
            template,
            device.dataBindings,
            storeData,
            { effectType: 'blackAndWhite', threshold: 128 }
          );
          
          if (previewData) {
            // 更新設備的預覽圖
            await updateDevicePreviewImage(deviceId, previewData);
            device.previewImage = previewData;
            console.log(`已成功為設備 ${deviceId} 生成預覽圖`);
          }
        } catch (error) {
          console.error(`無法為設備 ${deviceId} 生成預覽圖:`, error);
          throw new Error(`無法生成預覽圖: ${error.message}`);
        }
      } else {
        throw new Error(`設備 ${deviceId} 缺少模板或綁定數據，無法生成預覽圖`);
      }
    }
    
    // 繼續現有的發送邏輯...
  }
}
```

## 部署與配置

### 1. 啟動腳本

在項目根目錄創建 `start-preview-service.js`：

```javascript
const { spawn } = require('child_process');
const path = require('path');

// 啟動預覽服務
const previewService = spawn('node', ['dist/server.js'], {
  cwd: path.join(__dirname, 'preview-service'),
  stdio: 'inherit',
  env: {
    ...process.env,
    PREVIEW_SERVICE_PORT: '3002'
  }
});

previewService.on('close', (code) => {
  console.log(`預覽服務已關閉，退出碼: ${code}`);
});

// 處理進程終止
process.on('SIGINT', () => {
  previewService.kill('SIGINT');
  process.exit(0);
});
```

### 2. 更新 package.json

在項目根目錄的 `package.json` 中添加：

```json
{
  "scripts": {
    "start:preview-service": "node start-preview-service.js",
    "dev": "concurrently \"npm run client\" \"npm run server\" \"npm run start:preview-service\""
  }
}
```

## 測試與驗證

### 1. 單元測試

為共享模組添加單元測試，確保渲染邏輯正確。

### 2. 集成測試

測試預覽服務API，確保能夠正確生成預覽圖。

### 3. 比較測試

比較前端和服務端生成的預覽圖，確保效果一致。

### 4. 性能測試

測試預覽服務的響應時間和資源佔用。
