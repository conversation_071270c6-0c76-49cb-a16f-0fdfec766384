/**
 * 統一的顏色轉換模組
 * 前後端共用的顏色轉換邏輯，根據 colorType 自動選擇合適的轉換算法
 */

import { DisplayColorType } from '../../types';

// 顏色轉換選項介面
export interface ColorConversionOptions {
  threshold?: number;           // 二值化閾值 (0-255)，默認 128
  ditherStrength?: number;      // 抖動強度 (0-1)，默認 1.0
  preserveAlpha?: boolean;      // 是否保留透明度，默認 true
  levels?: number;              // 灰度級別數量，默認 16（用於 Gray16）
}

// 顏色轉換結果介面
export interface ColorConversionResult {
  success: boolean;
  canvas?: HTMLCanvasElement;
  error?: string;
}

// 環境適配器介面，用於支援不同環境（瀏覽器/Node.js）
export interface EnvironmentAdapter {
  createCanvas: (width: number, height: number) => HTMLCanvasElement;
  getContext2D: (canvas: HTMLCanvasElement) => CanvasRenderingContext2D | null;
}

// 瀏覽器環境適配器
export const BrowserAdapter: EnvironmentAdapter = {
  createCanvas: (width: number, height: number) => {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    return canvas;
  },
  getContext2D: (canvas: HTMLCanvasElement) => canvas.getContext('2d')
};

/**
 * 根據 colorType 獲取推薦的轉換策略
 */
export function getConversionStrategy(colorType: string | DisplayColorType): {
  algorithm: 'blackAndWhite' | 'grayscale' | 'gray16' | 'dithering' | 'colorQuantization' | 'original';
  options: ColorConversionOptions;
} {
  // 標準化 colorType
  const normalizedColorType = typeof colorType === 'string'
    ? colorType.toUpperCase()
    : colorType;

  switch (normalizedColorType) {
    case DisplayColorType.BW:
    case 'GRAY16':
    case 'BW':
      // 16階灰度屏幕：使用16級灰度量化
      return {
        algorithm: 'gray16',
        options: { levels: 16, preserveAlpha: true }
      };

    case DisplayColorType.BWR:
    case 'BLACK & WHITE & RED':
    case 'BWR':
      // 黑白紅屏幕：使用三色量化
      return {
        algorithm: 'colorQuantization',
        options: { threshold: 128, preserveAlpha: true }
      };

    case DisplayColorType.BWRY:
    case 'BLACK & WHITE & RED & YELLOW':
    case 'BWRY':
      // 黑白紅黃屏幕：使用四色量化
      return {
        algorithm: 'colorQuantization',
        options: { threshold: 128, preserveAlpha: true }
      };

    case 'ALL COLORS':
    case 'ALL':
      // 全彩屏幕：保持原始顏色
      return {
        algorithm: 'original',
        options: { preserveAlpha: true }
      };

    default:
      // 未知類型：使用黑白二值化作為安全選項
      console.warn(`未知的顏色類型: ${colorType}，使用黑白二值化作為默認轉換`);
      return {
        algorithm: 'blackAndWhite',
        options: { threshold: 128, preserveAlpha: true }
      };
  }
}

/**
 * 主要的顏色轉換函數
 * @param sourceCanvas 源畫布
 * @param colorType 目標顏色類型
 * @param adapter 環境適配器
 * @param customOptions 自定義選項（會覆蓋默認選項）
 * @returns 轉換結果
 */
export function convertImageForColorType(
  sourceCanvas: HTMLCanvasElement,
  colorType: string | DisplayColorType,
  adapter: EnvironmentAdapter = BrowserAdapter,
  customOptions?: Partial<ColorConversionOptions>
): ColorConversionResult {
  try {
    // 獲取轉換策略
    const strategy = getConversionStrategy(colorType);
    const options = { ...strategy.options, ...customOptions };

    // 根據算法執行轉換
    let resultCanvas: HTMLCanvasElement;

    switch (strategy.algorithm) {
      case 'original':
        resultCanvas = cloneCanvas(sourceCanvas, adapter);
        break;
      case 'blackAndWhite':
        resultCanvas = convertToBlackAndWhite(sourceCanvas, adapter, options);
        break;
      case 'grayscale':
        resultCanvas = convertToGrayscale(sourceCanvas, adapter, options);
        break;
      case 'gray16':
        resultCanvas = convertToGray16(sourceCanvas, adapter, options);
        break;
      case 'dithering':
        resultCanvas = convertToDithering(sourceCanvas, adapter, options);
        break;
      case 'colorQuantization':
        resultCanvas = convertToColorQuantization(sourceCanvas, colorType, adapter, options);
        break;
      default:
        throw new Error(`不支援的轉換算法: ${strategy.algorithm}`);
    }

    return {
      success: true,
      canvas: resultCanvas
    };
  } catch (error) {
    console.error('顏色轉換失敗:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    };
  }
}

/**
 * 複製畫布
 */
function cloneCanvas(sourceCanvas: HTMLCanvasElement, adapter: EnvironmentAdapter): HTMLCanvasElement {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);

  if (!ctx) {
    throw new Error('無法獲取畫布上下文');
  }

  ctx.drawImage(sourceCanvas, 0, 0);
  return newCanvas;
}

/**
 * 轉換為黑白二值化圖像
 */
function convertToBlackAndWhite(
  sourceCanvas: HTMLCanvasElement,
  adapter: EnvironmentAdapter,
  options: ColorConversionOptions
): HTMLCanvasElement {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);
  const sourceCtx = adapter.getContext2D(sourceCanvas);

  if (!ctx || !sourceCtx) {
    throw new Error('無法獲取畫布上下文');
  }

  const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
  const data = imageData.data;
  const threshold = options.threshold || 128;

  for (let i = 0; i < data.length; i += 4) {
    // 使用標準灰度轉換公式
    const grayscale = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
    const color = grayscale < threshold ? 0 : 255;

    data[i] = data[i + 1] = data[i + 2] = color;

    // 根據選項決定是否保留透明度
    if (!options.preserveAlpha) {
      data[i + 3] = 255;
    }
  }

  ctx.putImageData(imageData, 0, 0);
  return newCanvas;
}

/**
 * 轉換為灰階圖像
 */
function convertToGrayscale(
  sourceCanvas: HTMLCanvasElement,
  adapter: EnvironmentAdapter,
  options: ColorConversionOptions
): HTMLCanvasElement {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);
  const sourceCtx = adapter.getContext2D(sourceCanvas);

  if (!ctx || !sourceCtx) {
    throw new Error('無法獲取畫布上下文');
  }

  const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
  const data = imageData.data;

  for (let i = 0; i < data.length; i += 4) {
    const grayscale = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
    data[i] = data[i + 1] = data[i + 2] = grayscale;

    if (!options.preserveAlpha) {
      data[i + 3] = 255;
    }
  }

  ctx.putImageData(imageData, 0, 0);
  return newCanvas;
}

/**
 * 轉換為16階灰度圖像
 */
function convertToGray16(
  sourceCanvas: HTMLCanvasElement,
  adapter: EnvironmentAdapter,
  options: ColorConversionOptions
): HTMLCanvasElement {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);
  const sourceCtx = adapter.getContext2D(sourceCanvas);

  if (!ctx || !sourceCtx) {
    throw new Error('無法獲取畫布上下文');
  }

  const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
  const data = imageData.data;
  const levels = options.levels || 16;

  // 計算每個灰度級別的步長
  const step = 255 / (levels - 1);

  for (let i = 0; i < data.length; i += 4) {
    // 使用標準灰度轉換公式
    const grayscale = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];

    // 量化到指定的灰度級別
    const level = Math.round(grayscale / step);
    const quantizedGray = Math.min(255, level * step);

    data[i] = data[i + 1] = data[i + 2] = quantizedGray;

    if (!options.preserveAlpha) {
      data[i + 3] = 255;
    }
  }

  ctx.putImageData(imageData, 0, 0);
  return newCanvas;
}

/**
 * 轉換為抖動效果（Floyd-Steinberg 抖動算法）
 */
function convertToDithering(
  sourceCanvas: HTMLCanvasElement,
  adapter: EnvironmentAdapter,
  options: ColorConversionOptions
): HTMLCanvasElement {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);
  const sourceCtx = adapter.getContext2D(sourceCanvas);

  if (!ctx || !sourceCtx) {
    throw new Error('無法獲取畫布上下文');
  }

  const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
  const data = new Uint8ClampedArray(imageData.data);
  const width = sourceCanvas.width;
  const height = sourceCanvas.height;

  // 轉換為灰階並保存在新數組
  const grayscale = new Uint8ClampedArray(width * height);
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = (y * width + x) * 4;
      grayscale[y * width + x] = Math.round(0.299 * data[idx] + 0.587 * data[idx + 1] + 0.114 * data[idx + 2]);
    }
  }

  // 應用 Floyd-Steinberg 抖動算法
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = y * width + x;
      const oldPixel = grayscale[idx];
      const newPixel = oldPixel < (options.threshold || 128) ? 0 : 255;
      grayscale[idx] = newPixel;

      const error = oldPixel - newPixel;
      const strength = options.ditherStrength || 1.0;

      // 將誤差分散到鄰近像素
      if (x + 1 < width) {
        grayscale[idx + 1] += error * strength * 7 / 16;
      }
      if (y + 1 < height) {
        if (x - 1 >= 0) {
          grayscale[(y + 1) * width + x - 1] += error * strength * 3 / 16;
        }
        grayscale[(y + 1) * width + x] += error * strength * 5 / 16;
        if (x + 1 < width) {
          grayscale[(y + 1) * width + x + 1] += error * strength * 1 / 16;
        }
      }
    }
  }

  // 將結果寫回圖像數據
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = (y * width + x) * 4;
      const grayIdx = y * width + x;
      const color = Math.max(0, Math.min(255, grayscale[grayIdx]));

      data[idx] = data[idx + 1] = data[idx + 2] = color;

      if (!options.preserveAlpha) {
        data[idx + 3] = 255;
      }
    }
  }

  ctx.putImageData(new ImageData(data, width, height), 0, 0);
  return newCanvas;
}

/**
 * 轉換為顏色量化（用於多色電子紙屏幕）
 */
function convertToColorQuantization(
  sourceCanvas: HTMLCanvasElement,
  colorType: string | DisplayColorType,
  adapter: EnvironmentAdapter,
  options: ColorConversionOptions
): HTMLCanvasElement {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);
  const sourceCtx = adapter.getContext2D(sourceCanvas);

  if (!ctx || !sourceCtx) {
    throw new Error('無法獲取畫布上下文');
  }

  const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
  const data = imageData.data;

  // 根據顏色類型定義調色板
  const palette = getColorPalette(colorType);

  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];

    // 找到最接近的調色板顏色
    const closestColor = findClosestColor(r, g, b, palette);

    data[i] = closestColor.r;
    data[i + 1] = closestColor.g;
    data[i + 2] = closestColor.b;

    if (!options.preserveAlpha) {
      data[i + 3] = 255;
    }
  }

  ctx.putImageData(imageData, 0, 0);
  return newCanvas;
}

/**
 * 根據顏色類型獲取調色板
 */
function getColorPalette(colorType: string | DisplayColorType): Array<{r: number, g: number, b: number}> {
  const normalizedColorType = typeof colorType === 'string'
    ? colorType.toUpperCase()
    : colorType;

  switch (normalizedColorType) {
    case DisplayColorType.BWR:
    case 'BLACK & WHITE & RED':
    case 'BWR':
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 }      // 紅色
      ];

    case DisplayColorType.BWRY:
    case 'BLACK & WHITE & RED & YELLOW':
    case 'BWRY':
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 },     // 紅色
        { r: 255, g: 255, b: 0 }    // 黃色
      ];

    default:
      // 默認黑白調色板
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }  // 白色
      ];
  }
}

/**
 * 根據顏色類型獲取可用的 hex 顏色列表（用於 UI 顏色選擇器）
 */
export function getAvailableColorsForColorType(colorType: string | DisplayColorType): string[] {
  // 標準化顏色類型字符串
  const normalizedColorType = typeof colorType === 'string'
    ? colorType.toUpperCase()
    : colorType;

  // 特殊處理 Gray16，提供 16 個灰度級別
  if (normalizedColorType === 'GRAY16' ||
      normalizedColorType === 'BW' ||
      normalizedColorType === 'BLACK & WHITE') {
    const grayColors: string[] = [];
    for (let i = 0; i < 16; i++) {
      const grayValue = Math.round((255 / 15) * i);
      const hex = `#${grayValue.toString(16).padStart(2, '0').repeat(3)}`;
      grayColors.push(hex.toUpperCase());
    }
    return grayColors;
  }

  // 獲取調色板並轉換為 hex 格式
  const palette = getColorPalette(colorType);
  return palette.map(color => {
    const r = color.r.toString(16).padStart(2, '0');
    const g = color.g.toString(16).padStart(2, '0');
    const b = color.b.toString(16).padStart(2, '0');
    return `#${r}${g}${b}`.toUpperCase();
  });
}

/**
 * 檢查顏色是否在指定 colortype 的可用顏色範圍內
 */
export function isColorValidForColorType(color: string, colorType: string | DisplayColorType): boolean {
  const availableColors = getAvailableColorsForColorType(colorType);
  return availableColors.includes(color.toUpperCase());
}

/**
 * 將任意顏色映射到最接近的可用顏色
 */
export function mapColorToAvailableColor(color: string, colorType: string | DisplayColorType): string {
  const availableColors = getAvailableColorsForColorType(colorType);

  // 如果顏色已經在可用範圍內，直接返回
  if (availableColors.includes(color.toUpperCase())) {
    return color.toUpperCase();
  }

  // 將 hex 轉換為 RGB
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // 獲取調色板並找到最接近的顏色
  const palette = getColorPalette(colorType);
  const closestColor = findClosestColor(r, g, b, palette);

  // 轉換回 hex 格式
  const rHex = closestColor.r.toString(16).padStart(2, '0');
  const gHex = closestColor.g.toString(16).padStart(2, '0');
  const bHex = closestColor.b.toString(16).padStart(2, '0');
  return `#${rHex}${gHex}${bHex}`.toUpperCase();
}

/**
 * 找到最接近的調色板顏色
 * 改進的算法，對電子紙顏色進行特殊處理
 */
function findClosestColor(
  r: number,
  g: number,
  b: number,
  palette: Array<{r: number, g: number, b: number}>
): {r: number, g: number, b: number} {
  // 對於四色電子紙（BWRY），使用特殊的顏色匹配邏輯
  if (palette.length === 4) {
    // 檢查是否為黃色（高紅色和綠色，低藍色）
    if (r > 180 && g > 180 && b < 100) {
      return { r: 255, g: 255, b: 0 }; // 黃色
    }
    // 檢查是否為紅色（高紅色，低綠色和藍色）
    if (r > 150 && g < 100 && b < 100) {
      return { r: 255, g: 0, b: 0 }; // 紅色
    }
    // 檢查是否為白色（高亮度）
    const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
    if (brightness > 128) {
      return { r: 255, g: 255, b: 255 }; // 白色
    } else {
      return { r: 0, g: 0, b: 0 }; // 黑色
    }
  }

  // 對於三色電子紙（BWR），使用特殊的顏色匹配邏輯
  if (palette.length === 3) {
    // 檢查是否為紅色（高紅色，低綠色和藍色）
    if (r > 150 && g < 100 && b < 100) {
      return { r: 255, g: 0, b: 0 }; // 紅色
    }
    // 檢查是否為白色（高亮度）
    const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
    if (brightness > 128) {
      return { r: 255, g: 255, b: 255 }; // 白色
    } else {
      return { r: 0, g: 0, b: 0 }; // 黑色
    }
  }

  // 對於其他情況，使用標準的歐幾里得距離
  let minDistance = Infinity;
  let closestColor = palette[0];

  for (const color of palette) {
    // 使用加權歐幾里得距離，對人眼敏感的綠色給予更高權重
    const distance = Math.sqrt(
      0.3 * Math.pow(r - color.r, 2) +
      0.59 * Math.pow(g - color.g, 2) +
      0.11 * Math.pow(b - color.b, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      closestColor = color;
    }
  }

  return closestColor;
}

/**
 * 便利函數：直接根據 colorType 轉換圖像
 * 這是最常用的 API，隱藏了複雜的配置細節
 */
export function convertImageByColorType(
  sourceCanvas: HTMLCanvasElement,
  colorType: string | DisplayColorType,
  customOptions?: Partial<ColorConversionOptions>
): HTMLCanvasElement | null {
  const result = convertImageForColorType(sourceCanvas, colorType, BrowserAdapter, customOptions);

  if (result.success && result.canvas) {
    return result.canvas;
  } else {
    console.error('圖像轉換失敗:', result.error);
    return null;
  }
}

/**
 * 向後兼容的函數：模擬舊的 effectType 行為
 * 用於逐步遷移現有代碼
 */
export function convertImageByEffectType(
  sourceCanvas: HTMLCanvasElement,
  effectType: 'original' | 'blackAndWhite' | 'grayscale' | 'gray16' | 'inverted' | 'dithering' = 'blackAndWhite',
  threshold: number = 128
): HTMLCanvasElement {
  const options: ColorConversionOptions = { threshold, preserveAlpha: true, levels: 16 };

  switch (effectType) {
    case 'original':
      return cloneCanvas(sourceCanvas, BrowserAdapter);
    case 'blackAndWhite':
      return convertToBlackAndWhite(sourceCanvas, BrowserAdapter, options);
    case 'grayscale':
      return convertToGrayscale(sourceCanvas, BrowserAdapter, options);
    case 'gray16':
      return convertToGray16(sourceCanvas, BrowserAdapter, options);
    case 'dithering':
      return convertToDithering(sourceCanvas, BrowserAdapter, options);
    case 'inverted':
      // 反轉效果：先轉灰階再反轉
      const grayCanvas = convertToGrayscale(sourceCanvas, BrowserAdapter, options);
      return invertCanvas(grayCanvas, BrowserAdapter, options);
    default:
      return convertToBlackAndWhite(sourceCanvas, BrowserAdapter, options);
  }
}

/**
 * 反轉畫布顏色
 */
function invertCanvas(
  sourceCanvas: HTMLCanvasElement,
  adapter: EnvironmentAdapter,
  options: ColorConversionOptions
): HTMLCanvasElement {
  const newCanvas = adapter.createCanvas(sourceCanvas.width, sourceCanvas.height);
  const ctx = adapter.getContext2D(newCanvas);
  const sourceCtx = adapter.getContext2D(sourceCanvas);

  if (!ctx || !sourceCtx) {
    throw new Error('無法獲取畫布上下文');
  }

  const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
  const data = imageData.data;

  for (let i = 0; i < data.length; i += 4) {
    data[i] = 255 - data[i];         // R
    data[i + 1] = 255 - data[i + 1]; // G
    data[i + 2] = 255 - data[i + 2]; // B

    if (!options.preserveAlpha) {
      data[i + 3] = 255;
    }
  }

  ctx.putImageData(imageData, 0, 0);
  return newCanvas;
}