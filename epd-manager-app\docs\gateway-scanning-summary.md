# 網關掃描功能總結

## 1. 功能概述

網關掃描功能是 EPD Manager App 的核心功能之一，它允許用戶在以下情況下發現並添加本地網絡中的網關設備：

1. **初次使用**：當用戶帳號內沒有任何網關時，需要掃描並添加網關
2. **添加新網關**：當用戶需要添加新的網關設備到已有的帳號時

該功能通過 UDP 廣播技術實現，能夠自動發現同一局域網內的所有網關設備，並提供友好的用戶界面讓用戶選擇要添加的網關。

## 2. 技術架構

### 2.1 整體架構

```mermaid
graph TD
    subgraph "移動應用"
        UI[用戶界面]
        Scanner[網關掃描器]
        API[API 客戶端]
    end
    
    subgraph "本地網絡"
        UDP[UDP 廣播]
    end
    
    subgraph "網關設備"
        Listener[UDP 監聽器]
        Handler[請求處理器]
    end
    
    subgraph "後端服務"
        Server[API 服務]
        DB[數據庫]
    end
    
    UI --> Scanner
    Scanner --> UDP
    UDP --> Listener
    Listener --> Handler
    Handler --> UDP
    UDP --> Scanner
    Scanner --> UI
    UI --> API
    API --> Server
    Server --> DB
```

### 2.2 關鍵技術

1. **UDP 廣播**：使用 UDP 協議在本地網絡中廣播發現請求
2. **React Native UDP**：使用 `react-native-udp` 庫實現 UDP 通信
3. **網絡信息獲取**：使用 `react-native-network-info` 獲取設備 IP 和子網信息
4. **JSON 消息格式**：使用 JSON 格式進行數據交換
5. **Redux 狀態管理**：使用 Redux 管理掃描結果和網關狀態

## 3. 實現細節

### 3.1 移動應用端

#### 3.1.1 網關掃描器服務

網關掃描器服務負責發送 UDP 廣播消息並處理回應：

- **技術選擇**：`react-native-udp` + `react-native-network-info`
- **主要功能**：
  - 獲取本地網絡信息
  - 發送 UDP 廣播消息
  - 接收和解析網關回應
  - 通知 UI 更新掃描結果

#### 3.1.2 用戶界面

掃描功能包含兩個主要頁面：

1. **網關掃描頁面**：
   - 顯示掃描進度和結果
   - 列出發現的網關設備
   - 提供添加網關的按鈕

2. **網關添加頁面**：
   - 顯示網關詳細信息
   - 允許用戶輸入網關名稱
   - 選擇要添加到的門店
   - 提供確認添加的按鈕

### 3.2 網關設備端

網關設備需要實現 UDP 監聽和回應機制：

- **監聽端口**：UDP 5000
- **協議名稱**：EPD-GATEWAY-DISCOVERY
- **協議版本**：1.0
- **回應格式**：JSON 格式，包含網關的基本信息

### 3.3 後端服務端

後端服務需要提供網關註冊 API：

- **API 端點**：`POST /api/gateways`
- **請求格式**：JSON 格式，包含網關信息和門店 ID
- **回應格式**：JSON 格式，包含創建的網關信息和 WebSocket 連接信息

## 4. 通信協議

### 4.1 UDP 發現協議

#### 4.1.1 發現請求

```json
{
  "type": "discovery",
  "protocol": "EPD-GATEWAY-DISCOVERY",
  "version": "1.0",
  "timestamp": 1683270664000
}
```

#### 4.1.2 發現回應

```json
{
  "type": "discovery-response",
  "protocol": "EPD-GATEWAY-DISCOVERY",
  "version": "1.0",
  "timestamp": 1683270664500,
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "model": "GW-2000",
  "name": "Gateway-1234",
  "firmwareVersion": "1.0.0",
  "capabilities": ["wifi", "bluetooth"],
  "status": "ready"
}
```

### 4.2 網關註冊 API

#### 4.2.1 請求

```json
{
  "name": "Gateway-1234",
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "model": "GW-2000",
  "ipAddress": "*************",
  "storeId": "store123",
  "status": "online",
  "notes": "辦公室網關",
  "firmwareVersion": "1.0.0"
}
```

#### 4.2.2 回應

```json
{
  "_id": "gateway123",
  "name": "Gateway-1234",
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "model": "GW-2000",
  "ipAddress": "*************",
  "storeId": "store123",
  "status": "online",
  "notes": "辦公室網關",
  "firmwareVersion": "1.0.0",
  "createdAt": "2023-05-05T08:15:30Z",
  "updatedAt": "2023-05-05T08:15:30Z",
  "websocket": {
    "url": "ws://localhost:3001/ws/store/store123/gateway/gateway123",
    "path": "/ws/store/store123/gateway/gateway123",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "protocol": "json"
  }
}
```

## 5. 用戶流程

### 5.1 初次使用流程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant Gateway as 網關設備
    participant Server as 後端服務器
    
    User->>App: 登入系統
    App->>Server: 獲取網關列表
    Server-->>App: 返回空列表
    App->>App: 顯示提示：未找到網關
    App->>App: 自動進入網關掃描頁面
    
    App->>Gateway: 發送 UDP 廣播掃描消息
    Gateway-->>App: 回應掃描消息
    App->>App: 顯示發現的網關
    
    User->>App: 選擇要添加的網關
    App->>App: 顯示網關添加頁面
    User->>App: 填寫網關信息並確認
    
    App->>Server: 發送添加網關請求
    Server->>Server: 創建網關記錄
    Server-->>App: 返回添加結果
    
    App-->>User: 顯示添加成功消息
    App->>App: 返回網關列表頁面
```

### 5.2 添加新網關流程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant Gateway as 網關設備
    participant Server as 後端服務器
    
    User->>App: 登入系統
    App->>Server: 獲取網關列表
    Server-->>App: 返回現有網關列表
    App->>App: 顯示網關列表
    
    User->>App: 點擊"掃描本地網關"按鈕
    App->>App: 進入網關掃描頁面
    
    App->>Gateway: 發送 UDP 廣播掃描消息
    Gateway-->>App: 回應掃描消息
    App->>App: 顯示發現的網關
    
    User->>App: 選擇要添加的新網關
    App->>App: 顯示網關添加頁面
    User->>App: 填寫網關信息並確認
    
    App->>Server: 發送添加網關請求
    Server->>Server: 創建網關記錄
    Server-->>App: 返回添加結果
    
    App-->>User: 顯示添加成功消息
    App->>App: 返回網關列表頁面
```

## 6. 安全考慮

### 6.1 移動應用端

1. **權限控制**：確保只有授權用戶才能添加網關
2. **數據驗證**：驗證從網關接收的數據格式和內容
3. **安全存儲**：安全存儲網關憑證和連接信息

### 6.2 網關設備端

1. **消息驗證**：驗證接收到的消息格式和協議
2. **回應限制**：限制回應頻率，避免被用於 DoS 攻擊
3. **IP 過濾**：只回應來自特定 IP 範圍的請求

### 6.3 後端服務端

1. **認證與授權**：使用 JWT 進行 API 認證
2. **數據驗證**：驗證網關註冊請求的數據
3. **重複檢查**：檢查 MAC 地址是否已被註冊

## 7. 性能優化

### 7.1 移動應用端

1. **掃描超時**：設置合理的掃描超時時間
2. **結果緩存**：緩存掃描結果，避免頻繁掃描
3. **批量操作**：支持批量添加網關

### 7.2 網關設備端

1. **隨機延遲**：添加隨機回應延遲，避免網絡擁塞
2. **消息壓縮**：對大型回應消息進行壓縮
3. **資源控制**：控制 UDP 監聽器的資源使用

### 7.3 後端服務端

1. **數據庫索引**：為網關 MAC 地址創建索引
2. **緩存機制**：緩存常用的網關數據
3. **批量處理**：支持批量網關註冊

## 8. 測試策略

### 8.1 單元測試

1. **掃描器測試**：測試網關掃描器的各個功能
2. **UI 組件測試**：測試掃描頁面和添加頁面的 UI 組件
3. **API 客戶端測試**：測試網關註冊 API 的調用

### 8.2 集成測試

1. **端到端測試**：測試完整的掃描和添加流程
2. **多設備測試**：測試同時發現多個網關設備
3. **錯誤處理測試**：測試各種錯誤情況的處理

### 8.3 性能測試

1. **掃描性能**：測試大型網絡中的掃描性能
2. **並發測試**：測試多用戶同時掃描和添加網關
3. **資源使用**：監控掃描過程中的資源使用情況

## 9. 未來擴展

### 9.1 功能擴展

1. **自動配置**：掃描後自動配置網關
2. **批量操作**：支持批量添加和配置網關
3. **高級過濾**：根據型號、功能等過濾網關

### 9.2 技術擴展

1. **安全增強**：添加設備認證和消息加密
2. **協議升級**：擴展協議支持更多功能
3. **多協議支持**：支持 mDNS、UPnP 等其他發現協議
