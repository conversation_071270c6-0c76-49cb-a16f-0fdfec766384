# EPD Manager Lite 技術路線圖

## 優先順序分析

### 緊急修復項目（P0）
這些問題直接影響系統功能，應優先處理：

1. **網關連線狀態誤判問題**
   - 網關當機後重新連線時間過短造成的誤判
   - 即使裝置已回應仍顯示未連線狀態
   - 直接影響用戶對系統可靠性的判斷

2. **低效能網關影像傳輸問題**
   - sendImageToGateway 在處理低效能網關時的問題
   - 導致部分設備無法正常顯示圖像

3. **後端影像渲染修復**
   - fix/backend-image-rendering 分支的問題
   - 確保圖像正確渲染到所有支援的設備

### 重要優化項目（P1）
這些項目對系統穩定性和擴展性有較大影響：

1. **列表自動更新優化**
   - 網關、設備和模板列表自動更新機制
   - 提高用戶操作體驗和數據一致性

2. **前後端圖片渲染強化**
   - 開發獨立且可轉移的渲染服務
   - 提高渲染質量和效率

3. **傳輸數據壓縮格式支援**
   - 改善數據傳輸效率
   - 減少網絡負載

### 功能擴展項目（P2）
這些項目為系統增加新功能：

1. **門店管理功能**
   - 門店設置
   - 門店數據匯入
   - 門店模板功能

2. **動態元件連接設計**
   - 提高系統靈活性
   - 支援自定義元件擴展

3. **權限管理細部調整**
   - 更精細的權限控制
   - 提高系統安全性

### 長期規劃項目（P3）
這些項目涉及系統架構調整，需要較長時間規劃：

1. **資料庫移轉**
   - 需要完整的遷移計劃
   - 確保數據完整性和系統穩定性

2. **Docker 容器化**
   - 完整封裝系統到 Docker
   - 提高部署和擴展性

3. **韌體格式支援**
   - 加入 fw 格式支援
   - 擴展系統功能

## 技術依賴關係

```
【網關連線問題修復】─────┐
                        ↓
【低效能網關傳輸問題】────┐
                        ↓
【後端渲染修復】─────────┐
                        ↓
        ┌───────────────┴────────────────────┐
        ↓                                    ↓
【列表自動更新優化】                  【前後端渲染強化】
        │                                    │
        ↓                                    ↓
【傳輸數據壓縮】───────────┐     【獨立渲染服務】───────┐
        │                  │               │           │
        ↓                  ↓               ↓           │
【動態元件連接設計】       │      【權限管理調整】      │
        │                  │               │           │
        ├──────────────────┼───────────────┘           │
        ↓                  ↓                           │
【門店管理功能】───────────┼───────────────────────────┤
        │                  │                           │
        ↓                  ↓                           ↓
【資料庫移轉】─────────────┼───────────────┐   【韌體格式支援】
        │                  │               │           │
        └──────────────────┼───────────────┼───────────┘
                           ↓               ↓
                      【Docker 容器化】   【系統整合測試】
```

## 技術資源分配建議

### 前端開發團隊
- 列表自動更新優化
- 門店管理介面
- 動態元件連接前端實現

### 後端開發團隊
- 網關連接問題修復
- 後端渲染強化
- 資料庫移轉
- 權限管理調整

### DevOps 團隊
- Docker 容器化
- 獨立渲染服務架構
- 系統部署優化

### 嵌入式開發團隊
- 低效能網關傳輸問題
- 傳輸數據壓縮實現
- 韌體格式支援

## 里程碑計劃

### 第一階段：系統穩定性提升（1-2 個月）
- 修復所有 P0 級別問題
- 完成列表自動更新優化
- 制定資料庫移轉和 Docker 容器化計劃

### 第二階段：核心功能擴展（2-4 個月）
- 實現傳輸數據壓縮
- 完成獨立渲染服務開發
- 實作動態元件連接設計
- 開始門店管理功能開發

### 第三階段：架構優化（4-6 個月）
- 完成資料庫移轉
- 實現 Docker 容器化
- 完善門店管理功能
- 加入韌體格式支援

### 第四階段：系統整合與優化（6-8 個月）
- 系統全面測試
- 性能優化
- 文檔完善
- 用戶培訓

## 技術債務管理

在此計劃執行過程中，需密切關注以下潛在技術債務：

1. **舊版 API 相容性**
   - 確保系統變更不影響現有整合
   - 制定 API 版本管理策略

2. **代碼重構需求**
   - 識別需要重構的代碼區域
   - 在相關功能開發時進行漸進式重構

3. **測試覆蓋率**
   - 為新功能和修復建立完整測試
   - 增加自動化測試比例

4. **文檔更新**
   - 確保所有變更都有相應的文檔更新
   - 為開發人員和最終用戶提供清晰的指南
