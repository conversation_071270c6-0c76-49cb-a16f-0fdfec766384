const QRCode = require('qrcode');
const { createCanvas, loadImage } = require('canvas');

/**
 * 將白色背景的圖片 Buffer 轉換為透明背景
 * @param {Buffer} buffer 原始圖片 Buffer
 * @param {number} width 圖片寬度
 * @param {number} height 圖片高度
 * @returns {Promise<Buffer>} 透明背景的圖片 Buffer
 */
async function convertWhiteBackgroundToTransparentBuffer(buffer, width, height) {
  try {
    // 載入圖片到 Canvas
    const img = await loadImage(buffer);
    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext('2d');

    // 繪製圖片到 Canvas
    ctx.drawImage(img, 0, 0, width, height);

    // 獲取圖片數據
    const imageData = ctx.getImageData(0, 0, width, height);
    const data = imageData.data;

    // 將白色像素轉換為透明
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];

      // 檢查是否為白色或接近白色的像素（容忍度為 10）
      if (r > 245 && g > 245 && b > 245) {
        data[i + 3] = 0; // 設置 alpha 為 0（透明）
      }
    }

    // 將修改後的數據放回 Canvas
    ctx.putImageData(imageData, 0, 0);

    // 轉換為 Buffer
    return canvas.toBuffer('image/png');
  } catch (error) {
    console.error('轉換透明背景失敗:', error);
    throw error;
  }
}

/**
 * QR Code 渲染器
 * 支援多種 QR Code 類型和完整的參數控制
 */

/**
 * 處理 QR Code 的資料綁定
 * @param {Object} element - 元素配置
 * @param {Object} sampleDataByIndex - 按索引組織的範例數據
 * @param {Array} dataFields - 資料欄位定義
 * @returns {string} 處理後的內容
 */
function processDataBinding(element, sampleDataByIndex, dataFields) {
  // 1. 檢查是否有資料綁定
  if (!element.dataBinding || !element.dataBinding.fieldId) {
    return element.codeContent || 'QR Code Sample'; // 返回靜態內容
  }

  // 2. 獲取綁定的資料欄位
  const field = dataFields.find(f => f.id === element.dataBinding.fieldId);
  if (!field) {
    console.warn(`找不到欄位 ${element.dataBinding.fieldId}`);
    return element.codeContent || 'ERROR: Field not found';
  }

  // 3. 獲取數據值
  const dataIndex = element.dataBinding.dataIndex || 0;
  const sampleData = sampleDataByIndex[dataIndex];
  if (!sampleData || !sampleData[field.id]) {
    console.warn(`找不到數據 ${field.id} at index ${dataIndex}`);
    return field.defaultValue || element.codeContent || 'No Data';
  }

  // 4. 格式驗證和清理
  let content = sampleData[field.id];

  // 根據 QR Code 類型進行驗證
  const validation = validateQRCodeContent(content, element.qrCodeType);
  if (!validation.isValid) {
    console.error(`QR Code 內容驗證失敗: ${validation.error}`);
    return `ERROR: ${validation.error}`;
  }
  content = validation.sanitizedContent || content;

  // 5. 添加前綴（如果需要）
  if (element.dataBinding.displayOptions?.showPrefix && field.prefix) {
    content = field.prefix + content;
  }

  return content;
}

/**
 * 驗證 QR Code 內容格式
 * @param {string} content - 要驗證的內容
 * @param {string} qrCodeType - QR Code 類型
 * @returns {Object} 驗證結果
 */
function validateQRCodeContent(content, qrCodeType = 'qrcode') {
  if (!content || typeof content !== 'string') {
    return {
      isValid: false,
      error: '內容不能為空'
    };
  }

  // QR Code 驗證規則
  const QR_CODE_LIMITS = {
    qrcode: { maxLength: 4296, charset: 'all' },
    datamatrix: { maxLength: 3116, charset: 'ascii' },
    pdf417: { maxLength: 2710, charset: 'all' }
  };

  const limits = QR_CODE_LIMITS[qrCodeType] || QR_CODE_LIMITS.qrcode;

  // 長度檢查
  if (content.length > limits.maxLength) {
    return {
      isValid: false,
      error: `內容長度超過限制 (${limits.maxLength} 字符)`
    };
  }

  // 字符集檢查
  if (limits.charset === 'ascii') {
    // ASCII 字符檢查
    if (!/^[\x00-\x7F]*$/.test(content)) {
      return {
        isValid: false,
        error: '僅支援 ASCII 字符',
        sanitizedContent: content.replace(/[^\x00-\x7F]/g, '?')
      };
    }
  }

  return {
    isValid: true,
    sanitizedContent: content
  };
}

/**
 * 渲染 QR Code 到 DOM 元素
 * @param {HTMLElement} elementDiv - 目標 DOM 元素
 * @param {Object} element - 元素配置
 * @param {Object} sampleDataByIndex - 範例數據
 * @param {Array} dataFields - 資料欄位定義
 */
async function renderQRCode(elementDiv, element, sampleDataByIndex, dataFields) {
  try {
    // 處理資料綁定
    const content = processDataBinding(element, sampleDataByIndex, dataFields);

    // 獲取 QR Code 配置
    const qrCodeType = element.qrCodeType || 'qrcode';
    const errorCorrectionLevel = element.errorCorrectionLevel || 'M';
    const quietZone = element.quietZone || 4;
    const moduleSize = element.moduleSize || 3;
    const foregroundColor = '#000000'; // QR Code 條碼顏色固定為黑色
    const backgroundColor = element.fillColor || '#FFFFFF';

    // 處理透明背景：QR Code 套件不支援透明背景，將透明轉換為白色
    const processedBackgroundColor = backgroundColor === 'transparent' ? '#FFFFFF' : backgroundColor;

    // 創建 Canvas
    const canvas = createCanvas(element.width, element.height);
    const ctx = canvas.getContext('2d');

    // 如果原始背景是透明，需要特殊處理
    if (backgroundColor === 'transparent') {
      // 先生成白色背景的 QR Code
      const qrOptions = {
        errorCorrectionLevel: errorCorrectionLevel,
        type: 'image/png',
        quality: 1,
        margin: quietZone,
        color: {
          dark: foregroundColor,
          light: '#FFFFFF'
        },
        width: element.width,
        height: element.height
      };

      // 目前 qrcode 套件主要支援標準 QR Code
      if (qrCodeType === 'datamatrix' || qrCodeType === 'pdf417') {
        console.warn(`${qrCodeType} 類型暫時使用 QR Code 代替`);
      }

      // 生成 QR Code Buffer
      const buffer = await QRCode.toBuffer(content, qrOptions);

      // 轉換為透明背景
      const transparentBuffer = await convertWhiteBackgroundToTransparentBuffer(buffer, element.width, element.height);

      // 載入透明背景的圖片並繪製到 Canvas
      const img = await loadImage(transparentBuffer);
      ctx.drawImage(img, 0, 0, element.width, element.height);
    } else {
      // 正常生成有色背景的 QR Code
      // 設置背景色
      ctx.fillStyle = processedBackgroundColor;
      ctx.fillRect(0, 0, element.width, element.height);

      const qrOptions = {
        errorCorrectionLevel: errorCorrectionLevel,
        type: 'image/png',
        quality: 1,
        margin: quietZone,
        color: {
          dark: foregroundColor,
          light: processedBackgroundColor
        },
        width: element.width,
        height: element.height
      };

      // 目前 qrcode 套件主要支援標準 QR Code
      if (qrCodeType === 'datamatrix' || qrCodeType === 'pdf417') {
        console.warn(`${qrCodeType} 類型暫時使用 QR Code 代替`);
      }

      // 生成 QR Code Buffer
      const buffer = await QRCode.toBuffer(content, qrOptions);

      // 載入圖片並繪製到 Canvas
      const img = await loadImage(buffer);
      ctx.drawImage(img, 0, 0, element.width, element.height);
    }

    // 將 Canvas 內容設置到 DOM 元素
    const dataURL = canvas.toDataURL();
    elementDiv.style.backgroundImage = `url(${dataURL})`;
    elementDiv.style.backgroundSize = 'contain';
    elementDiv.style.backgroundRepeat = 'no-repeat';
    elementDiv.style.backgroundPosition = 'center';

    // 設置元素樣式 - 與前端 QRCodeRenderer.tsx 保持一致
    elementDiv.style.width = `${element.width}px`;
    elementDiv.style.height = `${element.height}px`;
    elementDiv.style.position = 'absolute';
    elementDiv.style.left = `${element.x}px`;
    elementDiv.style.top = `${element.y}px`;

    // 設置容器背景色 - 與前端保持一致
    elementDiv.style.backgroundColor = backgroundColor;

    // 處理邊框 - 與前端 QRCodeRenderer.tsx 保持一致
    if (element.showBorder !== false) {
      const borderWidth = element.lineWidth || 2;
      const borderColor = element.borderColor || '#000000';
      elementDiv.style.border = `${borderWidth}px solid ${borderColor}`;
      elementDiv.style.borderRadius = '4px';
    } else {
      elementDiv.style.border = 'none';
    }

    // 設置容器樣式 - 與前端保持一致
    elementDiv.style.display = 'flex';
    elementDiv.style.flexDirection = 'column';
    elementDiv.style.alignItems = 'center';
    elementDiv.style.justifyContent = 'center';
    elementDiv.style.padding = '8px';
    elementDiv.style.boxSizing = 'border-box';

    if (element.rotation) {
      elementDiv.style.transform = `rotate(${element.rotation}deg)`;
      elementDiv.style.transformOrigin = 'center center';
    }

    console.log(`QR Code 渲染完成: ${qrCodeType}, 內容: ${content.substring(0, 50)}...`);

  } catch (error) {
    console.error('QR Code 渲染失敗:', error);

    // 錯誤時顯示佔位符 - 與前端保持一致的樣式
    elementDiv.style.width = `${element.width}px`;
    elementDiv.style.height = `${element.height}px`;
    elementDiv.style.position = 'absolute';
    elementDiv.style.left = `${element.x}px`;
    elementDiv.style.top = `${element.y}px`;

    // 設置錯誤時的背景色和邊框
    elementDiv.style.backgroundColor = '#ffe6e6'; // 淺紅色背景表示錯誤
    elementDiv.style.border = '1px solid #ff0000'; // 紅色邊框表示錯誤
    elementDiv.style.borderRadius = '4px';

    // 設置容器樣式
    elementDiv.style.display = 'flex';
    elementDiv.style.flexDirection = 'column';
    elementDiv.style.alignItems = 'center';
    elementDiv.style.justifyContent = 'center';
    elementDiv.style.padding = '8px';
    elementDiv.style.boxSizing = 'border-box';
    elementDiv.style.fontSize = '12px';
    elementDiv.style.color = '#ff0000'; // 紅色文字表示錯誤

    if (element.rotation) {
      elementDiv.style.transform = `rotate(${element.rotation}deg)`;
      elementDiv.style.transformOrigin = 'center center';
    }

    elementDiv.textContent = 'QR Code 錯誤';
  }
}

/**
 * 生成 QR Code 預覽圖片（用於 API）
 * @param {Object} options - QR Code 選項
 * @returns {Promise<Buffer>} 圖片 Buffer
 */
async function generateQRCodePreview(options) {
  const {
    content,
    qrCodeType = 'qrcode',
    errorCorrectionLevel = 'M',
    quietZone = 4,
    moduleSize = 3,
    backgroundColor = '#FFFFFF',
    width = 200,
    height = 200
  } = options;

  // QR Code 條碼顏色固定為黑色
  const foregroundColor = '#000000';

  // 處理透明背景：QR Code 套件不支援透明背景，將透明轉換為白色
  const processedBackgroundColor = backgroundColor === 'transparent' ? '#FFFFFF' : backgroundColor;

  try {
    // 驗證內容
    const validation = validateQRCodeContent(content, qrCodeType);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // 如果原始背景是透明，需要特殊處理
    if (backgroundColor === 'transparent') {
      // 先生成白色背景的 QR Code
      const qrOptions = {
        errorCorrectionLevel: errorCorrectionLevel,
        type: 'image/png',
        quality: 1,
        margin: quietZone,
        color: {
          dark: foregroundColor,
          light: '#FFFFFF'
        },
        width: width,
        height: height
      };

      // 生成 QR Code Buffer
      const buffer = await QRCode.toBuffer(validation.sanitizedContent || content, qrOptions);

      // 使用 Canvas 將白色背景轉換為透明
      return await convertWhiteBackgroundToTransparentBuffer(buffer, width, height);
    } else {
      // 正常生成有色背景的 QR Code
      const qrOptions = {
        errorCorrectionLevel: errorCorrectionLevel,
        type: 'image/png',
        quality: 1,
        margin: quietZone,
        color: {
          dark: foregroundColor,
          light: processedBackgroundColor
        },
        width: width,
        height: height
      };

      // 生成 QR Code Buffer
      const buffer = await QRCode.toBuffer(validation.sanitizedContent || content, qrOptions);
      return buffer;
    }

  } catch (error) {
    console.error('QR Code 預覽生成失敗:', error);
    throw error;
  }
}

module.exports = {
  renderQRCode,
  generateQRCodePreview,
  validateQRCodeContent,
  processDataBinding
};
