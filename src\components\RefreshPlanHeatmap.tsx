import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Flame, Clock, Activity, AlertCircle, CheckCircle, Pause } from 'lucide-react';
import { RefreshPlan } from '../types/refreshPlan';
import { refreshPlanApi, formatExecuteTime } from '../services/refreshPlanApi';
import { subscribeToRefreshPlanUpdate, RefreshPlanUpdateEvent } from '../utils/websocketClient';
import { Store } from '../types/store';

interface RefreshPlanHeatmapProps {
  store: Store;
}

interface HeatmapDay {
  date: Date;
  plans: RefreshPlan[];
  intensity: number; // 0-4 強度等級
  isToday: boolean;
}

export const RefreshPlanHeatmap: React.FC<RefreshPlanHeatmapProps> = ({ store }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [plans, setPlans] = useState<RefreshPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hoveredDay, setHoveredDay] = useState<HeatmapDay | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // 載入刷圖計畫
  const loadPlans = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await refreshPlanApi.getPlans(store.id, {
        page: 1,
        limit: 1000,
      });

      if (response.success) {
        setPlans(response.data.plans);
      }
    } catch (err) {
      setError('載入刷圖計畫失敗');
      console.error('載入刷圖計畫失敗:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (store?.id) {
      loadPlans();
    }
  }, [store?.id]);

  // WebSocket 即時更新
  useEffect(() => {
    if (!store?.id) return;

    const handleRefreshPlanUpdate = (event: RefreshPlanUpdateEvent) => {
      if (event.storeId === store.id) {
        console.log('熱力圖收到刷圖計畫更新事件:', event);
        
        if (event.updateType === 'delete') {
          setPlans(prevPlans => prevPlans.filter(plan => plan._id !== event.planId));
        } else if (event.updateType === 'create') {
          loadPlans();
        } else if (event.updateType === 'status_change' || event.updateType === 'update') {
          setPlans(prevPlans =>
            prevPlans.map(plan => {
              if (plan._id !== event.planId) {
                return plan;
              }
              
              return {
                ...plan,
                ...event.planData,
                _id: event.planId
              };
            })
          );
        }
      }
    };

    const unsubscribe = subscribeToRefreshPlanUpdate(
      store.id,
      handleRefreshPlanUpdate,
      {}
    );

    return () => {
      unsubscribe();
    };
  }, [store?.id]);

  // 生成熱力圖數據（顯示過去12週）
  const generateHeatmapData = (): HeatmapDay[][] => {
    const weeks: HeatmapDay[][] = [];
    const today = new Date();
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - (12 * 7) + 1); // 12週前
    
    // 調整到週一開始
    const dayOfWeek = startDate.getDay();
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    startDate.setDate(startDate.getDate() - daysToMonday);

    for (let week = 0; week < 12; week++) {
      const weekDays: HeatmapDay[] = [];
      
      for (let day = 0; day < 7; day++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + (week * 7) + day);
        
        const dayPlans = getPlansForDate(date);
        const intensity = calculateIntensity(dayPlans);
        const isToday = date.toDateString() === today.toDateString();
        
        weekDays.push({
          date,
          plans: dayPlans,
          intensity,
          isToday,
        });
      }
      
      weeks.push(weekDays);
    }
    
    return weeks;
  };

  // 獲取指定日期的計畫
  const getPlansForDate = (date: Date): RefreshPlan[] => {
    return plans.filter(plan => {
      if (!plan.enabled) return false;
      
      const { trigger } = plan;
      
      switch (trigger.type) {
        case 'once':
          if (trigger.executeDate) {
            const executeDate = new Date(trigger.executeDate);
            return executeDate.toDateString() === date.toDateString();
          }
          return false;
          
        case 'daily':
          return true;
          
        case 'weekly':
          if (trigger.weekDays && trigger.weekDays.length > 0) {
            return trigger.weekDays.includes(date.getDay());
          }
          return false;
          
        default:
          return false;
      }
    });
  };

  // 計算強度等級 (0-4)
  const calculateIntensity = (dayPlans: RefreshPlan[]): number => {
    if (dayPlans.length === 0) return 0;
    if (dayPlans.length === 1) return 1;
    if (dayPlans.length === 2) return 2;
    if (dayPlans.length <= 4) return 3;
    return 4;
  };

  // 獲取強度對應的顏色
  const getIntensityColor = (intensity: number, isToday: boolean): string => {
    if (isToday) {
      return 'bg-indigo-500 ring-2 ring-indigo-300';
    }
    
    switch (intensity) {
      case 0:
        return 'bg-gray-100';
      case 1:
        return 'bg-green-200';
      case 2:
        return 'bg-green-400';
      case 3:
        return 'bg-green-600';
      case 4:
        return 'bg-green-800';
      default:
        return 'bg-gray-100';
    }
  };

  // 月份導航
  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const heatmapData = generateHeatmapData();
  const weekDays = ['一', '二', '三', '四', '五', '六', '日'];
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

  if (loading) {
    return (
      <div className="relative mb-6">
        <div className="absolute inset-0 bg-gradient-to-br from-green-100/80 via-emerald-50/60 to-teal-100/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>
        <div className="relative p-6">
          <div className="flex items-center justify-center h-32">
            <div className="text-gray-500">載入熱力圖中...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="relative mb-6">
        <div className="absolute inset-0 bg-gradient-to-br from-red-100/80 via-pink-50/60 to-red-100/80 backdrop-blur-sm rounded-2xl border border-red-200/50 shadow-xl"></div>
        <div className="relative p-6">
          <div className="flex items-center justify-center h-32">
            <div className="text-red-600">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative mb-6">
      {/* 玻璃效果背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-100/80 via-emerald-50/60 to-teal-100/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>
      
      {/* 動態光效背景 */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-2xl animate-pulse opacity-50"></div>
      
      {/* 內容區域 */}
      <div className="relative p-6">
        {/* 標題 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-lg flex items-center justify-center shadow-lg">
              <Flame className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-lg font-bold text-gray-800">刷圖計畫熱力圖</h3>
            <span className="text-sm text-gray-500">過去12週活動概覽</span>
          </div>
        </div>

        {/* 熱力圖 */}
        <div className="bg-white/30 rounded-xl p-4 mb-4">
          {/* 月份標籤 */}
          <div className="flex text-xs text-gray-500 mb-2 relative h-4">
            {heatmapData.map((week, weekIndex) => {
              // 檢查這一週是否包含該月的第一天
              const weekFirstDay = week[0].date;
              const month = weekFirstDay.getMonth();

              // 檢查前一週是否是不同的月份
              const prevWeek = weekIndex > 0 ? heatmapData[weekIndex - 1] : null;
              const prevMonth = prevWeek ? prevWeek[0].date.getMonth() : -1;
              const isDifferentMonth = month !== prevMonth;

              // 只在月份變化時顯示月份標籤
              if (isDifferentMonth) {
                // 計算標籤位置：週標籤寬度(24px) + 週索引 * (格子寬度12px + 間距4px)
                const leftPosition = 24 + weekIndex * 16;
                return (
                  <span
                    key={weekIndex}
                    className="font-medium absolute"
                    style={{ left: `${leftPosition}px` }}
                  >
                    {months[month]}
                  </span>
                );
              }
              return null;
            })}
          </div>

          {/* 週標籤和熱力圖格子 */}
          <div className="flex gap-1">
            {/* 週標籤 */}
            <div className="flex flex-col gap-1 mr-2">
              <div className="h-4"></div> {/* 空白對齊月份標籤，增加高度 */}
              {weekDays.map((day, index) => (
                <div key={day} className="h-3 flex items-center">
                  {index % 2 === 1 && (
                    <span className="text-xs text-gray-500 w-4">{day}</span>
                  )}
                </div>
              ))}
            </div>

            {/* 熱力圖格子 */}
            <div className="flex gap-1">
              {heatmapData.map((week, weekIndex) => (
                <div key={weekIndex} className="flex flex-col gap-1 w-3">
                  <div className="h-4"></div> {/* 為月份標籤預留空間 */}
                  {week.map((day, dayIndex) => (
                    <div
                      key={`${weekIndex}-${dayIndex}`}
                      className={`
                        w-3 h-3 rounded-sm cursor-pointer transition-all duration-200 hover:scale-125
                        ${getIntensityColor(day.intensity, day.isToday)}
                      `}
                      onMouseEnter={(e) => {
                        setHoveredDay(day);
                        setMousePosition({ x: e.clientX, y: e.clientY });
                      }}
                      onMouseMove={(e) => {
                        setMousePosition({ x: e.clientX, y: e.clientY });
                      }}
                      onMouseLeave={() => setHoveredDay(null)}
                      title={`${day.date.toLocaleDateString('zh-TW')} - ${day.plans.length} 個計畫`}
                    ></div>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 強度圖例 */}
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">較少</span>
          <div className="flex items-center gap-1">
            {[0, 1, 2, 3, 4].map((intensity) => (
              <div
                key={intensity}
                className={`w-3 h-3 rounded-sm ${getIntensityColor(intensity, false)}`}
              ></div>
            ))}
          </div>
          <span className="text-gray-600">較多</span>
        </div>

        {/* 統計資訊 */}
        <div className="mt-6 grid grid-cols-3 gap-4">
          <div className="bg-white/50 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-green-600">
              {plans.filter(p => p.enabled).length}
            </div>
            <div className="text-sm text-gray-600">啟用計畫</div>
          </div>
          <div className="bg-white/50 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {heatmapData.flat().filter(day => day.plans.length > 0).length}
            </div>
            <div className="text-sm text-gray-600">活躍天數</div>
          </div>
          <div className="bg-white/50 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {Math.round(heatmapData.flat().reduce((sum, day) => sum + day.plans.length, 0) / heatmapData.flat().length * 10) / 10}
            </div>
            <div className="text-sm text-gray-600">平均每日</div>
          </div>
        </div>

        {/* 懸停詳情 */}
        {hoveredDay && (
          <div 
            className="fixed z-50 p-4 bg-white rounded-lg shadow-xl border border-gray-200 max-w-sm pointer-events-none"
            style={{
              left: `${mousePosition.x + 10}px`,
              top: `${mousePosition.y - 10}px`,
              transform: mousePosition.x > window.innerWidth - 350 ? 'translateX(-100%)' : 'none'
            }}
          >
            <div className="font-medium text-gray-800 mb-2">
              {hoveredDay.date.toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
              })}
            </div>
            
            {hoveredDay.plans.length > 0 ? (
              <div className="space-y-2">
                <div className="text-sm text-gray-600 mb-2">
                  共 {hoveredDay.plans.length} 個計畫
                </div>
                {hoveredDay.plans.slice(0, 3).map((plan) => (
                  <div key={plan._id} className="flex items-center gap-2 text-sm">
                    <div className={`w-2 h-2 rounded-full ${
                      plan.status === 'active' ? 'bg-green-500' :
                      plan.status === 'running' ? 'bg-blue-500' :
                      plan.status === 'error' ? 'bg-red-500' : 'bg-gray-400'
                    }`}></div>
                    <span className="font-medium">{plan.name}</span>
                  </div>
                ))}
                {hoveredDay.plans.length > 3 && (
                  <div className="text-xs text-gray-500">
                    還有 {hoveredDay.plans.length - 3} 個計畫...
                  </div>
                )}
              </div>
            ) : (
              <div className="text-sm text-gray-500">
                此日期沒有計畫
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
