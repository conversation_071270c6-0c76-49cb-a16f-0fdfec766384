import { create } from 'zustand';

interface NavigationState {
  // 當前活動項目
  activeItem: string;
  // 是否顯示第二層選單
  showSecondLevel: boolean;
  // 選中的門店ID
  selectedStoreId: string | null;
  
  // 設置當前活動項目
  setActiveItem: (item: string) => void;
  // 設置是否顯示第二層選單
  setShowSecondLevel: (show: boolean) => void;
  // 設置選中的門店ID
  setSelectedStoreId: (id: string | null) => void;
  // 重置導航狀態
  resetNavigation: () => void;
}

// 創建導航狀態管理
export const useNavigationStore = create<NavigationState>()((set) => ({
  activeItem: 'store-management',
  showSecondLevel: false,
  selectedStoreId: null,
  
  // 設置當前活動項目
  setActiveItem: (item) => set({ activeItem: item }),
  
  // 設置是否顯示第二層選單
  setShowSecondLevel: (show) => set({ showSecondLevel: show }),
  
  // 設置選中的門店ID
  setSelectedStoreId: (id) => set({ selectedStoreId: id }),
  
  // 重置導航狀態
  resetNavigation: () => set({
    activeItem: 'store-management',
    showSecondLevel: false,
    selectedStoreId: null
  })
}));

export default useNavigationStore;
