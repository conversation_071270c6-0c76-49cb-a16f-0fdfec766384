@echo off
chcp 65001 >nul
echo ========================================
echo EPD Manager Deployment Tool (Windows)
echo ========================================
echo.

:: Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not running or not installed. Please install and start Docker Desktop first.
    echo Download: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

:: Check required files
if not exist "epd-manager-all.tar" (
    echo [ERROR] Cannot find epd-manager-all.tar image file
    pause
    exit /b 1
)

if not exist "docker-compose.yml" (
    echo [ERROR] Cannot find docker-compose.yml file
    pause
    exit /b 1
)

:: Check .env file
if not exist ".env" (
    echo [WARNING] Cannot find .env file
    if exist ".env.example" (
        echo [INFO] Creating .env file from .env.example...
        copy ".env.example" ".env"
        echo [IMPORTANT] Please edit .env file and set correct JWT_SECRET
        echo [IMPORTANT] Run this script again after completion
        pause
        exit /b 1
    ) else (
        echo [ERROR] Cannot find .env.example file
        pause
        exit /b 1
    )
)

echo [INFO] Loading Docker images...
echo Loading all images (EPD Manager + MongoDB)...
docker load -i epd-manager-all.tar
if %errorlevel% neq 0 (
    echo [ERROR] Image loading failed
    pause
    exit /b 1
)

echo.
echo [INFO] Stopping existing containers...
docker-compose down

echo [INFO] Starting EPD Manager...
docker-compose up -d

if %errorlevel% neq 0 (
    echo [ERROR] Startup failed
    pause
    exit /b 1
)

echo.
echo [SUCCESS] EPD Manager deployed successfully!
echo.
echo Service Status:
docker-compose ps

:: 獲取系統IP地址
setlocal enabledelayedexpansion
set IP_ADDRESS=localhost

:: 使用 ipconfig 命令獲取 IP 地址
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /R "IPv4.*[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*"') do (
    set IP_LINE=%%a
    set IP_LINE=!IP_LINE:~1!
    if not "!IP_LINE:127.0.0.1=!" == "!IP_LINE!" (
        rem 忽略 localhost
    ) else if not "!IP_LINE:169.254=!" == "!IP_LINE!" (
        rem 忽略自動私有 IP
    ) else (
        set IP_ADDRESS=!IP_LINE!
        goto :got_ip
    )
)

:got_ip
if "%IP_ADDRESS%"=="localhost" (
    echo [WARNING] Unable to get machine IP address, using default 'localhost'
)

echo.
echo Access URLs:
echo   Frontend: http://%IP_ADDRESS%:5173
echo   Backend: http://%IP_ADDRESS%:3001
echo.
echo [IMPORTANT] First-time setup:
echo   1. Open browser and visit http://%IP_ADDRESS%:5173
echo   2. System will automatically show initialization page
echo   3. Set administrator account and password
echo   4. After initialization, you can use the system normally
echo.
echo [TIP] Please set a strong password for security
echo.
echo Press any key to exit...
pause >nul