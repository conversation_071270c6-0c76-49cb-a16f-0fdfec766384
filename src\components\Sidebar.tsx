import React from 'react';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from './ui/LanguageSwitcher';
import { useAuthStore } from '../store/authStore';
import { useNavigationStore } from '../store/navigationStore';
import { PermissionMenuItem } from './permission/PermissionMenuItem';
import { DevModeWrapper } from './DevModeWrapper';
import {
  Menu,
  Building,
  LayoutTemplate,
  Settings,
  Upload,
  BarChart3,
  Users,
  HardDrive,
  Database,
  Store,
  ChevronLeft,
  FileText,
  Shield,
  ClipboardList,
  LogOut,
  Key,
  TestTube,
  Bug
} from 'lucide-react';

type SidebarItem = {
  id: string;
  translationKey: string;
  icon: React.ReactNode;
  permission?: string;
  anyPermissions?: string[];
  allPermissions?: string[];
};

// 第一層選單項目 - 門店管理和系統相關項目
const firstLevelItems: SidebarItem[] = [
  // 門店管理選項 - 允許系統角色的 store:view 權限或任何門店角色權限
  { id: 'store-management', translationKey: 'sidebar.storeManagement', icon: <Building size={20} />, anyPermissions: [
    'store:view',
    'store-data:view', 'store-data:create', 'store-data:update', 'store-data:delete',
    'store-template:view', 'store-template:create', 'store-template:update', 'store-template:delete',
    'gateway:view', 'gateway:create', 'gateway:update', 'gateway:delete',
    'device:view', 'device:create', 'device:update', 'device:delete',
    'store-settings:view', 'store-settings:update',
    'analytics:view', 'analytics:export'
  ] },
  { id: 'system-data', translationKey: 'sidebar.systemData', icon: <Database size={20} />, anyPermissions: ['system:view', 'system-data:view'] },
  { id: 'system-templates', translationKey: 'sidebar.systemTemplates', icon: <LayoutTemplate size={20} />, permission: 'template:view' },
  { id: 'permission-management', translationKey: 'sidebar.permissionManagement', icon: <Shield size={20} />, anyPermissions: ['role:view', 'user:view', 'permission:view'] },
  { id: 'system-logs', translationKey: 'sidebar.systemLogs', icon: <ClipboardList size={20} />, permission: 'system:view' },
  { id: 'settings', translationKey: 'sidebar.systemConfig', icon: <Settings size={20} />, permission: 'system:view' },
  { id: 'change-password', translationKey: 'auth.changePassword', icon: <Key size={20} /> },
  { id: 'color-test', translationKey: 'test.colorRestriction', icon: <TestTube size={20} /> },
  { id: 'bug-management', translationKey: 'sidebar.bugManagement', icon: <Bug size={20} /> },
];

// 第二層選單項目 - 門店管理下的子選單
const secondLevelItems: SidebarItem[] = [
  { id: 'store-overview', translationKey: 'sidebar.storeOverview', icon: <Store size={20} />, anyPermissions: ['store:view', 'store-data:view'] },
  { id: 'database', translationKey: 'sidebar.database', icon: <Database size={20} />, anyPermissions: ['store:view', 'store-data:view'] },
  { id: 'templates', translationKey: 'sidebar.templates', icon: <LayoutTemplate size={20} />, anyPermissions: ['template:view', 'store-template:view'] },
  { id: 'deploy', translationKey: 'sidebar.deploy', icon: <Upload size={20} />, anyPermissions: ['gateway:view'] },
  { id: 'devices', translationKey: 'sidebar.devices', icon: <HardDrive size={20} />, anyPermissions: ['device:view'] },
  { id: 'users', translationKey: 'sidebar.users', icon: <Users size={20} />, anyPermissions: ['store:update', 'store-settings:view', 'store-settings:update'] },
  { id: 'analytics', translationKey: 'sidebar.analytics', icon: <BarChart3 size={20} />, anyPermissions: ['system:view', 'analytics:view'] },
];

interface SidebarProps {
  activeItem: string;
  setActiveItem: (id: string) => void;
  isSidebarCollapsed: boolean;
  toggleSidebar: () => void;
  showSecondLevel: boolean;
  setShowSecondLevel: (show: boolean) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  activeItem,
  setActiveItem,
  isSidebarCollapsed,
  toggleSidebar,
  showSecondLevel,
  setShowSecondLevel
}) => {
  const { t } = useTranslation();
  const { logout, user } = useAuthStore();
  // 使用 navigationStore 來同步導航狀態
  const { setActiveItem: setGlobalActiveItem, setShowSecondLevel: setGlobalShowSecondLevel } = useNavigationStore();

  // 根據層級選擇不同的選單項目
  const currentItems = showSecondLevel ? secondLevelItems : firstLevelItems;

  return (    <div className={`bg-gradient-to-b from-slate-800 to-slate-700 text-white h-screen flex flex-col ${isSidebarCollapsed ? 'w-16' : 'w-58'} transition-all duration-300 shadow-xl`}>
      <div className={`flex items-center p-4 border-b border-slate-600/50 ${!isSidebarCollapsed && 'bg-gradient-to-r from-slate-800/90 to-slate-700/80 backdrop-blur-sm'}`}>        <button
          onClick={toggleSidebar}
          className="p-1.5 rounded-full bg-slate-700/80 hover:bg-blue-600/80 transition-all duration-200 shadow-lg hover:shadow-blue-500/20"
        >
          <Menu size={18} className="text-blue-100" />
        </button>
        {!isSidebarCollapsed && (
          <div className="ml-3 flex flex-col relative">
            <div className="absolute -left-1 -top-1 w-6 h-6 rounded-full bg-blue-500/20 blur-md"></div>
            <div className="absolute -right-1 -bottom-1 w-4 h-4 rounded-full bg-cyan-400/30 blur-md"></div>
            <h1 className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-300 via-cyan-200 to-blue-400 tracking-tight drop-shadow-lg">
              EPD Manager
              <span className="absolute top-0 left-0 right-0 bottom-0 bg-gradient-to-r from-blue-400/10 to-cyan-300/20 blur-sm opacity-70"></span>
            </h1>
            <div className="text-[10px] font-medium -mt-1 text-blue-200/70 tracking-wider pl-0.5">
              電子紙管理系統
            </div>
          </div>
        )}
      </div>      {/* 返回按鈕，只在第二層選單時顯示 */}
      {showSecondLevel && (
        <div className="p-2 border-b border-slate-600/50 bg-gradient-to-r from-slate-800/60 to-slate-700/60">
          <button
            onClick={() => {
              setShowSecondLevel(false);
              setGlobalShowSecondLevel(false); // 同步到全局狀態
              setActiveItem('store-management');
              setGlobalActiveItem('store-management'); // 同步到全局狀態
            }}
            className="flex items-center w-full p-2 rounded-md bg-slate-700/50 hover:bg-slate-600/70 hover:shadow-md hover:shadow-blue-800/10 transition-all duration-300"
          >
            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-600/20">
              <ChevronLeft size={16} className="text-blue-300" />
            </div>
            {!isSidebarCollapsed && (
              <span className="ml-3 text-sm font-medium text-blue-100">{t('common.back')}</span>
            )}
          </button>
        </div>
      )}<div className="flex-1 overflow-y-auto py-4 px-1">
        {/* 背景裝飾元素 - 科技感圓點和線條 */}
        <div className="absolute inset-0 overflow-hidden opacity-5 pointer-events-none">
          <div className="absolute top-1/4 right-2 w-40 h-40 rounded-full border border-blue-300/30 animate-pulse"></div>
          <div className="absolute top-1/3 left-4 w-16 h-16 rounded-full border border-cyan-400/20"></div>
          <div className="absolute bottom-1/4 right-6 w-24 h-24 rounded-full border border-blue-500/20 animate-pulse"></div>
        </div>
        
        <ul className="space-y-1.5 relative">
          {currentItems.map((item: SidebarItem) => {
            const menuItem = (
              <PermissionMenuItem
                key={item.id}
                permission={item.permission}
                anyPermissions={item.anyPermissions}
                allPermissions={item.allPermissions}
              >
                <li>
                  <button
                    onClick={() => {
                      setActiveItem(item.id);
                      setGlobalActiveItem(item.id); // 同步到全局狀態
                    }}                    className={`flex items-center ${isSidebarCollapsed ? 'justify-center' : 'justify-start'} w-full p-2 rounded-md transition-all duration-300 ${
                      activeItem === item.id
                        ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-md shadow-blue-500/30'
                        : 'hover:bg-slate-600/50 hover:shadow-sm hover:shadow-blue-400/10'
                    }`}
                  >
                    <span className="flex-shrink-0">{item.icon}</span>
                    {!isSidebarCollapsed && (
                      <span className="ml-3">{t(item.translationKey)}</span>
                    )}
                  </button>
                </li>
              </PermissionMenuItem>
            );

            // 如果是 bug-management 項目，需要用 DevModeWrapper 包裝
            if (item.id === 'bug-management') {
              return (
                <DevModeWrapper key={item.id}>
                  {menuItem}
                </DevModeWrapper>
              );
            }

            return menuItem;
          })}
        </ul>
      </div>      {!isSidebarCollapsed ? (
        <div className="p-4 border-t border-slate-600/50 bg-gradient-to-b from-slate-700/60 to-slate-800/80 space-y-2 shadow-inner">
          <LanguageSwitcher className="w-full transition-all hover:shadow-md hover:shadow-blue-900/20 rounded-md" />
        </div>
      ) : (
        <div className="p-2 border-t border-slate-600/50 bg-gradient-to-b from-slate-700/60 to-slate-800/80 shadow-inner">
          <LanguageSwitcher className="w-full transition-all hover:shadow-md hover:shadow-blue-900/20 rounded-md" />
        </div>
      )}
    </div>
  );
};