/**
 * Star和Heart圖標調試測試 - 專門分析這兩個圖標的渲染問題
 */

const fs = require('fs');
const path = require('path');

/**
 * 測試Star和Heart圖標的詳細渲染
 */
async function testStarHeartDebug() {
  try {
    console.log('=== 開始Star和Heart圖標調試測試 ===\n');

    // 引入預覽服務
    const { regeneratePreviewBeforeSend } = require('../services/previewService');

    // 測試案例：只渲染star和heart，使用更大的尺寸
    const testCases = [
      {
        name: 'star-debug',
        iconType: 'star',
        size: { width: 120, height: 120 },
        position: { x: 50, y: 50 },
        color: '#000000'
      },
      {
        name: 'heart-debug',
        iconType: 'heart',
        size: { width: 120, height: 120 },
        position: { x: 200, y: 50 },
        color: '#ff0000'
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n--- 調試測試: ${testCase.name} ---`);
      
      // 創建測試模板
      const testTemplate = {
        id: `star-heart-debug-${testCase.name}`,
        name: `Star Heart 調試測試 - ${testCase.name}`,
        width: 400,
        height: 250,
        screenSize: '400x250',
        elements: [
          {
            id: 'debug-icon',
            type: 'icon',
            x: testCase.position.x,
            y: testCase.position.y,
            width: testCase.size.width,
            height: testCase.size.height,
            iconType: testCase.iconType,
            lineColor: testCase.color,
            lineWidth: 3 // 使用更粗的線條便於觀察
          }
        ]
      };

      // 創建測試設備數據
      const testDevice = {
        id: 'test-device',
        dataBindings: {}
      };

      // 創建測試門店數據
      const testStoreData = [
        {
          id: 'test-store',
          name: '測試門店',
          storeSpecificData: []
        }
      ];

      console.log(`生成調試預覽圖: ${testCase.iconType} (${testCase.size.width}x${testCase.size.height})`);
      
      // 計算預期的實際icon尺寸和位置
      const containerWidth = testCase.size.width;
      const containerHeight = testCase.size.height;
      const expectedIconSize = Math.min(containerWidth, containerHeight) * 0.8;
      const expectedOffsetX = (containerWidth - expectedIconSize) / 2;
      const expectedOffsetY = (containerHeight - expectedIconSize) / 2;
      const expectedActualX = testCase.position.x + expectedOffsetX;
      const expectedActualY = testCase.position.y + expectedOffsetY;
      
      console.log(`預期位置調整:`);
      console.log(`  容器: (${testCase.position.x}, ${testCase.position.y}, ${containerWidth}x${containerHeight})`);
      console.log(`  實際: (${expectedActualX}, ${expectedActualY}, ${expectedIconSize}x${expectedIconSize})`);

      const result = await regeneratePreviewBeforeSend(testDevice, testStoreData, testTemplate, []);

      if (result) {
        // 將 base64 圖片保存到檔案
        const base64Data = result.replace(/^data:image\/png;base64,/, '');
        const outputPath = path.join(__dirname, `star-heart-debug-${testCase.name}-output.png`);
        fs.writeFileSync(outputPath, base64Data, 'base64');
        
        console.log(`✓ 調試預覽圖已保存到: ${outputPath}`);
        console.log(`  圖片大小: ${Math.round(base64Data.length * 0.75 / 1024)} KB`);
      } else {
        console.log(`✗ ${testCase.name} 調試預覽圖生成失敗`);
      }
    }

    // 創建對比測試：同時渲染star和heart
    console.log(`\n--- 對比測試: star vs heart ---`);
    
    const compareTemplate = {
      id: 'star-heart-compare',
      name: 'Star Heart 對比測試',
      width: 400,
      height: 250,
      screenSize: '400x250',
      elements: [
        {
          id: 'star-compare',
          type: 'icon',
          x: 50,
          y: 75,
          width: 100,
          height: 100,
          iconType: 'star',
          lineColor: '#000000',
          lineWidth: 2
        },
        {
          id: 'heart-compare',
          type: 'icon',
          x: 250,
          y: 75,
          width: 100,
          height: 100,
          iconType: 'heart',
          lineColor: '#ff0000',
          lineWidth: 2
        }
      ]
    };

    const testDevice = {
      id: 'test-device',
      dataBindings: {}
    };

    const testStoreData = [
      {
        id: 'test-store',
        name: '測試門店',
        storeSpecificData: []
      }
    ];

    console.log('生成對比預覽圖...');
    const compareResult = await regeneratePreviewBeforeSend(testDevice, testStoreData, compareTemplate, []);

    if (compareResult) {
      const base64Data = compareResult.replace(/^data:image\/png;base64,/, '');
      const outputPath = path.join(__dirname, 'star-heart-compare-output.png');
      fs.writeFileSync(outputPath, base64Data, 'base64');
      
      console.log(`✓ 對比預覽圖已保存到: ${outputPath}`);
      console.log(`  圖片大小: ${Math.round(base64Data.length * 0.75 / 1024)} KB`);
    } else {
      console.log('✗ 對比預覽圖生成失敗');
    }

    console.log('\n=== Star和Heart圖標調試測試完成 ===');

  } catch (error) {
    console.error('測試過程中發生錯誤:', error);
    console.error('錯誤堆疊:', error.stack);
  }
}

// 執行測試
testStarHeartDebug();
