# 數據綁定功能實作規劃

本文件描述了模板元素數據綁定功能的設計和實作規劃。這個功能將允許用戶將模板中的動態元件（如文字元件）綁定到數據欄位，使其在預覽和最終輸出時能夠動態顯示資料。

## 核心需求

1. 僅**動態文字**元件支援綁定變數，靜態文字不支援
2. 動態文字僅能綁定 `pure text` 和 `number` 類型的資料欄位
3. 資料綁定需要兩個步驟：
   - 先選擇要綁定的是第幾筆資料（數據索引）
   - 再選擇要綁定的欄位
4. 動態文字需提供「顯示前綴」選項（Display Prefix）
5. 解除綁定時需清除對應的連動元素提示內容
6. 資料綁定架構需獨立化，方便未來擴展到其他元件

## 系統配置

- 在系統配置中增加「最大資料綁定數量」設定（預設值：8）
- 用戶可以自訂此數值以動態調整可引用的資料數量

## 資料綁定架構圖

```mermaid
graph TD
    A[系統配置模組<br>SystemConfig] -->|提供配置參數| B[資料綁定核心模組<br>DataBindingCore]
    B -->|提供通用介面| C[元件綁定管理器<br>ComponentBindingManager]
    C -->|提供文字特定綁定| D[文字元件綁定<br>TextBinding]
    C -->|未來擴展| E[其他元件綁定<br>待開發]
    D -->|產生預覽| F[預覽與渲染模組<br>PreviewRenderer]
    F <-->|提供裝置數據映射| G[裝置綁定管理模組<br>DeviceBindingManager]

    style A fill:#d0e0ff,stroke:#333,stroke-width:1px
    style B fill:#b8e0d2,stroke:#333,stroke-width:1px
    style C fill:#b8e0d2,stroke:#333,stroke-width:1px
    style D fill:#d5e8d4,stroke:#333,stroke-width:1px
    style E fill:#e0e0e0,stroke:#333,stroke-width:1px
    style F fill:#ffe6cc,stroke:#333,stroke-width:1px
    style G fill:#ffe6cc,stroke:#333,stroke-width:1px
```

## 綁定資料流程

```mermaid
sequenceDiagram
    participant User as 使用者
    participant ConfigUI as 系統配置介面
    participant TemplateEditor as 模板編輯器
    participant ElementProperties as 元件屬性面板
    participant BindingCore as 資料綁定核心
    participant PreviewModal as 預覽模式
    
    User->>ConfigUI: 設定最大資料綁定數量
    ConfigUI->>BindingCore: 更新配置
    
    User->>TemplateEditor: 建立/選擇動態文字元件
    TemplateEditor->>ElementProperties: 顯示文字屬性面板
    
    User->>ElementProperties: 1. 選擇要綁定的數據索引
    User->>ElementProperties: 2. 選擇要綁定的資料欄位
    User->>ElementProperties: 3. 設定是否顯示前綴
    
    ElementProperties->>BindingCore: 更新元件綁定資訊
    BindingCore->>TemplateEditor: 更新元件顯示（顯示佔位符）
    
    User->>TemplateEditor: 點擊預覽按鈕
    TemplateEditor->>PreviewModal: 開啟預覽
    PreviewModal->>BindingCore: 獲取處理後的元素資料
    BindingCore->>PreviewModal: 返回替換變數後的元素
    PreviewModal->>User: 顯示預覽（含實際/模擬數據）
```

## 預期的綁定架構圖
```
┌─────────────────────────────────┐
│       資料綁定核心模組          │
│    (DataBindingCore)            │
├─────────────────────────────────┤
│ - 註冊支援綁定的元件類型        │
│ - 管理可綁定的資料欄位          │
│ - 過濾資料欄位類型              │
│ - 提供綁定/解綁操作             │
└───────────────┬─────────────────┘
                │
                ▼
┌─────────────────────────────────┐
│       元件綁定管理器            │
│   (ComponentBindingManager)     │
├─────────────────────────────────┤
│ - 處理特定元件的綁定邏輯        │
│ - 提供元件特定的綁定選項        │
│ - 管理綁定元件的視覺呈現        │
└─────┬─────────────────┬─────────┘
      │                 │
      ▼                 ▼
┌───────────────┐ ┌───────────────┐
│  文字元件綁定  │ │  其他元件綁定  │
│  (TextBinding) │ │ (待開發)      │
├───────────────┤ ├───────────────┤
│ - prefix 顯示  │ │ - 特定元件的  │
│ - 文字特有配置 │ │   綁定配置    │
└───────────────┘ └───────────────┘
      │
      ▼
┌───────────────────────┐
│    預覽與渲染模組     │
│ (PreviewRenderer)     │
├───────────────────────┤
│ - 在預覽時替換變數    │
│ - 渲染綁定元件        │
└───────────────────────┘
```
## 資料結構設計

### 系統配置
```typescript
interface SystemConfig {
  maxBindingDataCount: number;  // 模板可綁定的最大數據數量，預設值為8
  // 其他系統配置
}
```

### 綁定資訊
```typescript
interface BindingInfo {
  dataIndex: number;           // 綁定的數據索引 (0 到 maxBindingDataCount-1)
  fieldId: string | null;      // 綁定的資料欄位ID
  displayOptions?: {           // 元件特定的顯示選項
    showPrefix?: boolean;      // 是否顯示前綴
    // 其他可能的選項
  }
}
```

### 修改後的元素介面
```typescript
interface TemplateElement {
  // 現有屬性
  id: string;
  type: string;
  content?: string;
  // ... 其他現有屬性
  
  // 綁定相關屬性
  dataBinding?: BindingInfo;
}
```

### 裝置綁定關係
```typescript
interface DeviceBinding {
  deviceId: string;           // 裝置ID
  templateId: string;         // 模板ID
  dataIds: Record<number, string>;  // 數據索引 -> 實際數據ID的映射
}
```

## 實作 Action Items

1. **修改資料模型 (`src/types.ts`)**:
   - [ ] 更新 `TemplateElement` 介面，添加 `dataBinding` 屬性
   - [ ] 定義 `BindingInfo` 介面

2. **系統配置模組**:
   - [ ] 添加「最大資料綁定數量」設定到系統配置介面 (`src/components/system-config/ParamSettingTab.tsx`)
   - [ ] 更新系統配置 API (`server/routes/sysConfigApi.js`)
   - [ ] 在前端添加相關 API 方法 (`src/utils/api/sysConfigApi.ts`)

3. **資料綁定核心模組**:
   - [ ] 創建獨立的綁定核心模組 (`src/utils/dataBinding/bindingCore.ts`)
   - [ ] 實作過濾資料欄位類型的方法
   - [ ] 實作處理數據索引和欄位綁定的功能
   - [ ] 提供統一的綁定/解綁操作介面

4. **文字元件綁定**:
   - [ ] 修改文字元件屬性面板 (`src/components/editor/properties/TextProperties.tsx`):
     - [ ] 添加數據索引選擇器
     - [ ] 添加欄位選擇器（僅顯示文字和數字類型）
     - [ ] 添加「顯示前綴」選項
     - [ ] 處理綁定和解除綁定的邏輯（解除綁定時清除提示內容）
   - [ ] 修改文字元件渲染 (`src/components/editor/elements/TextElement.tsx`):
     - [ ] 為綁定元素添加視覺指示
     - [ ] 阻止綁定元素的直接編輯

5. **預覽與渲染模組**:
   - [ ] 更新 `previewUtils.ts` 中的資料處理邏輯:
     - [ ] 支持根據數據索引和欄位綁定處理多筆數據
     - [ ] 根據「顯示前綴」選項決定輸出格式
   - [ ] 修改 `PreviewModal.tsx` 使其能渲染綁定後的元素

6. **集成和測試**:
   - [ ] 確保所有模組間正確互動
   - [ ] 測試數據綁定的各種場景
   - [ ] 測試系統配置變更的影響
   - [ ] 測試前綴顯示選項

## 擴展性考慮

此架構設計考慮了未來的擴展需求:
1. 能輕鬆擴展到其他元件類型
2. 裝置綁定管理模組預留了裝置+模板+數據ID的綁定功能
3. 系統配置可動態調整，使用者可依需求設定最大資料綁定數量

## 注意事項

- 綁定到模板的只是資料欄位定義，不是實際資料
- 綁定到哪一筆實際資料的映射將在裝置綁定中處理
- 「數據索引」是指模板內的資料序號(1-N)，不是對應到實際資料的ID
- 在動態文字元件中，只有將類型為 `pure text` 和 `number` 的欄位列為可選項
