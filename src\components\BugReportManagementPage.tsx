import React, { useState, useEffect } from 'react';
import { Bug, Download, Eye, Edit, Trash2, Filter, RefreshCw, X } from 'lucide-react';
import { BugReport, BugReportFilters } from '../types/bugReport';
import { getAllBugReports, deleteBugReport, updateBugReport, exportBugReports } from '../utils/api/bugReportApi';
import { buildEndpointUrl } from '../utils/api/apiConfig';


export const BugReportManagementPage: React.FC = () => {
  const [bugReports, setBugReports] = useState<BugReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<BugReportFilters>({
    page: 1,
    limit: 10,
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });
  const [selectedReport, setSelectedReport] = useState<BugReport | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  // 載入bug回報列表
  const loadBugReports = async () => {
    try {
      setLoading(true);
      const response = await getAllBugReports(filters);
      setBugReports(response.data);
      setPagination(response.pagination);
      setError(null);
    } catch (err) {
      console.error('載入bug回報失敗:', err);
      setError(err instanceof Error ? err.message : '載入失敗');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBugReports();
  }, [filters]);

  // 處理篩選變更
  const handleFilterChange = (key: keyof BugReportFilters, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : (typeof value === 'number' ? value : 1), // 非頁碼變更時重置到第一頁
    }));
  };

  // 處理狀態更新
  const handleStatusUpdate = async (id: string, status: string) => {
    try {
      await updateBugReport(id, { status: status as any });
      await loadBugReports(); // 重新載入列表
    } catch (err) {
      console.error('更新狀態失敗:', err);
      setError(err instanceof Error ? err.message : '更新失敗');
    }
  };

  // 處理刪除
  const handleDelete = async (id: string) => {
    if (!confirm('確定要刪除這個bug回報嗎？')) {
      return;
    }

    try {
      await deleteBugReport(id);
      await loadBugReports(); // 重新載入列表
    } catch (err) {
      console.error('刪除失敗:', err);
      setError(err instanceof Error ? err.message : '刪除失敗');
    }
  };

  // 處理導出
  const handleExport = async () => {
    try {
      const blob = await exportBugReports();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `bug-reports-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('導出失敗:', err);
      setError(err instanceof Error ? err.message : '導出失敗');
    }
  };

  // 獲取優先級顏色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 獲取狀態顏色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'text-blue-600 bg-blue-100';
      case 'in-progress': return 'text-yellow-600 bg-yellow-100';
      case 'resolved': return 'text-green-600 bg-green-100';
      case 'closed': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 獲取圖片URL
  const getImageUrl = (imageId: string) => {
    return buildEndpointUrl('files', imageId);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* 標題 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <Bug className="text-red-500" size={24} />
            <h1 className="text-2xl font-bold text-gray-900">Bug回報管理</h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={loadBugReports}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50"
            >
              <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
              <span>重新載入</span>
            </button>
            <button
              onClick={handleExport}
              className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
            >
              <Download size={16} />
              <span>導出CSV</span>
            </button>
          </div>
        </div>

        {/* 篩選器 */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex items-center space-x-4">
            <Filter size={16} className="text-gray-500" />
            <select
              value={filters.status || ''}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有狀態</option>
              <option value="open">開放</option>
              <option value="in-progress">處理中</option>
              <option value="resolved">已解決</option>
              <option value="closed">已關閉</option>
            </select>
            <select
              value={filters.priority || ''}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有優先級</option>
              <option value="critical">緊急</option>
              <option value="high">高</option>
              <option value="medium">中</option>
              <option value="low">低</option>
            </select>
          </div>
        </div>

        {/* 錯誤信息 */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* 載入中 */}
        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p className="mt-2 text-gray-600">載入中...</p>
          </div>
        )}

        {/* Bug回報列表 */}
        {!loading && (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      標題
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      截圖
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      優先級
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      狀態
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      創建者
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      創建時間
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {bugReports.map((report) => (
                    <tr key={report._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {report.title}
                          </div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {report.content}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {report.imageId ? (
                          <div className="flex items-center">
                            <img
                              src={getImageUrl(report.imageId)}
                              alt="Bug截圖縮圖"
                              className="w-12 h-12 object-cover rounded border cursor-pointer hover:opacity-80"
                              onClick={() => {
                                setSelectedReport(report);
                                setShowDetailModal(true);
                              }}
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const parent = target.parentElement;
                                if (parent && !parent.querySelector('.error-placeholder')) {
                                  const errorDiv = document.createElement('div');
                                  errorDiv.className = 'error-placeholder w-12 h-12 bg-red-100 border border-red-300 rounded flex items-center justify-center text-red-500 text-xs';
                                  errorDiv.textContent = '載入失敗';
                                  parent.appendChild(errorDiv);
                                }
                              }}
                              title="點擊查看大圖"
                            />
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">無圖片</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(report.priority)}`}>
                          {report.priority}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={report.status}
                          onChange={(e) => handleStatusUpdate(report._id, e.target.value)}
                          className={`text-xs font-semibold rounded-full px-2 py-1 border-0 ${getStatusColor(report.status)}`}
                        >
                          <option value="open">開放</option>
                          <option value="in-progress">處理中</option>
                          <option value="resolved">已解決</option>
                          <option value="closed">已關閉</option>
                        </select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {report.createdBy}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(report.createdAt).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setSelectedReport(report);
                              setShowDetailModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900"
                            title="查看詳情"
                          >
                            <Eye size={16} />
                          </button>
                          <button
                            onClick={() => handleDelete(report._id)}
                            className="text-red-600 hover:text-red-900"
                            title="刪除"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 分頁 */}
            {pagination.pages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => handleFilterChange('page', Math.max(1, pagination.page - 1))}
                    disabled={pagination.page <= 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                  >
                    上一頁
                  </button>
                  <button
                    onClick={() => handleFilterChange('page', Math.min(pagination.pages, pagination.page + 1))}
                    disabled={pagination.page >= pagination.pages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                  >
                    下一頁
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      顯示第 <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> 到{' '}
                      <span className="font-medium">
                        {Math.min(pagination.page * pagination.limit, pagination.total)}
                      </span>{' '}
                      項，共 <span className="font-medium">{pagination.total}</span> 項
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                        <button
                          key={page}
                          onClick={() => handleFilterChange('page', page)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            page === pagination.page
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      ))}
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 詳情彈窗 */}
        {showDetailModal && selectedReport && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-4 border-b">
                <h3 className="text-lg font-semibold">Bug回報詳情</h3>
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="p-1 hover:bg-gray-100 rounded-full"
                >
                  <X size={20} />
                </button>
              </div>
              <div className="p-4 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">標題</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedReport.title}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">內容</label>
                  <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{selectedReport.content}</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">優先級</label>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(selectedReport.priority)}`}>
                      {selectedReport.priority}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">狀態</label>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedReport.status)}`}>
                      {selectedReport.status}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">當前頁面</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedReport.currentPage}</p>
                </div>
                {selectedReport.imageId && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">截圖</label>
                    <div className="mt-1">
                      <img
                        src={getImageUrl(selectedReport.imageId)}
                        alt="Bug截圖"
                        className="max-w-full h-auto rounded-lg border shadow-sm"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const errorDiv = document.createElement('div');
                          errorDiv.className = 'text-red-500 text-sm p-2 border border-red-200 rounded bg-red-50';
                          errorDiv.textContent = '圖片載入失敗';
                          target.parentNode?.appendChild(errorDiv);
                        }}
                      />
                    </div>
                  </div>
                )}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">創建者</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedReport.createdBy}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">創建時間</label>
                    <p className="mt-1 text-sm text-gray-900">{new Date(selectedReport.createdAt).toLocaleString()}</p>
                  </div>
                </div>
                {selectedReport.notes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">備註</label>
                    <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{selectedReport.notes}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
