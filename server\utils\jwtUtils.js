const jwt = require('jsonwebtoken');

// JWT 密鑰配置 - 與 server/index.js 保持一致
const getJwtSecret = () => {
  return process.env.JWT_SECRET || 'your_jwt_secret_here';
};

/**
 * 生成用戶登入token
 * @param {Object} payload - token載荷
 * @param {string} expiresIn - 過期時間，默認24小時
 * @returns {string} JWT token
 */
const generateUserToken = (payload, expiresIn = '24h') => {
  const jwtSecret = getJwtSecret();
  return jwt.sign(payload, jwtSecret, { expiresIn });
};

/**
 * 生成WebSocket連接token
 * @param {Object} userInfo - 用戶信息
 * @param {string} userInfo.userId - 用戶ID
 * @param {string} userInfo.username - 用戶名
 * @param {number} expiresInSeconds - 過期時間（秒），默認24小時
 * @returns {string} WebSocket JWT token
 */
const generateWebSocketToken = (userInfo, expiresInSeconds = 24 * 60 * 60) => {
  const jwtSecret = getJwtSecret();
  const payload = {
    userId: userInfo.userId,
    username: userInfo.username,
    type: 'websocket',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + expiresInSeconds
  };
  
  return jwt.sign(payload, jwtSecret);
};

/**
 * 生成Gateway連接token
 * @param {Object} gatewayInfo - Gateway信息
 * @param {string} gatewayInfo.gatewayId - Gateway ID
 * @param {string} gatewayInfo.storeId - 門店ID
 * @param {string} gatewayInfo.macAddress - MAC地址
 * @param {number} expiresInSeconds - 過期時間（秒），默認30天
 * @returns {string} Gateway JWT token
 */
const generateGatewayToken = (gatewayInfo, expiresInSeconds = 30 * 24 * 60 * 60) => {
  const jwtSecret = getJwtSecret();
  const payload = {
    gatewayId: gatewayInfo.gatewayId,
    storeId: gatewayInfo.storeId,
    macAddress: gatewayInfo.macAddress,
    type: 'gateway',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + expiresInSeconds
  };
  
  return jwt.sign(payload, jwtSecret);
};

/**
 * 驗證JWT token
 * @param {string} token - JWT token
 * @returns {Object} 解碼後的payload
 * @throws {Error} token無效時拋出錯誤
 */
const verifyToken = (token) => {
  const jwtSecret = getJwtSecret();
  return jwt.verify(token, jwtSecret);
};

/**
 * 解碼JWT token（不驗證簽名）
 * @param {string} token - JWT token
 * @returns {Object} 解碼後的payload
 */
const decodeToken = (token) => {
  return jwt.decode(token);
};

/**
 * 檢查token是否即將過期（1小時內）
 * @param {string} token - JWT token
 * @returns {boolean} 是否即將過期
 */
const isTokenExpiringSoon = (token) => {
  try {
    const decoded = decodeToken(token);
    if (!decoded || !decoded.exp) {
      return true; // 無過期時間視為即將過期
    }
    
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = decoded.exp - currentTime;
    
    // 如果剩餘時間少於1小時（3600秒），視為即將過期
    return timeUntilExpiry < 3600;
  } catch (error) {
    return true; // 解碼失敗視為即將過期
  }
};

/**
 * 獲取token剩餘有效時間（秒）
 * @param {string} token - JWT token
 * @returns {number} 剩餘秒數，-1表示已過期或無效
 */
const getTokenRemainingTime = (token) => {
  try {
    const decoded = decodeToken(token);
    if (!decoded || !decoded.exp) {
      return -1;
    }
    
    const currentTime = Math.floor(Date.now() / 1000);
    const remainingTime = decoded.exp - currentTime;
    
    return Math.max(0, remainingTime);
  } catch (error) {
    return -1;
  }
};

module.exports = {
  getJwtSecret,
  generateUserToken,
  generateWebSocketToken,
  generateGatewayToken,
  verifyToken,
  decodeToken,
  isTokenExpiringSoon,
  getTokenRemainingTime
};
