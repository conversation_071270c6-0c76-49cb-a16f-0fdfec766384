import React, { useState, useEffect } from 'react';
import { Building, Search, Plus, ChevronRight, Edit, Trash2, AlertCircle, LogIn } from 'lucide-react';
import { Store } from '../types/store';
import { getAllStores, deleteStore, updateStoreOrder } from '../utils/api/storeApi';
import { AddStoreModal } from './AddStoreModal';
import { EditStoreModal } from './EditStoreModal';
import { useAuthStore } from '../store/authStore';
import { Icon, IconType } from './editor/elements/IconComponent';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { createPortal } from 'react-dom';

// 可拖拽的門店卡片組件
interface SortableStoreCardProps {
  store: Store;
  onClick: () => void;
  onEdit: (e: React.MouseEvent) => void;
  onDelete: (e: React.MouseEvent) => void;
}

const SortableStoreCard: React.FC<SortableStoreCardProps> = ({ store, onClick, onEdit, onDelete }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    isOver
  } = useSortable({
    id: store._id || store.id,
    transition: {
      duration: 150,
      easing: 'cubic-bezier(0.18, 0.67, 0.6, 1.22)',
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging ? 'none' : transition || 'transform 150ms cubic-bezier(0.18, 0.67, 0.6, 1.22)',
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 1000 : isOver ? 100 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group relative bg-gradient-to-br from-white via-gray-50/50 to-white
        backdrop-blur-sm border border-gray-200/60 rounded-2xl shadow-lg hover:shadow-xl
        transition-all duration-150 ease-out overflow-hidden flex
        ${isDragging ? 'shadow-2xl scale-[1.05] rotate-2 ring-2 ring-blue-400/50' : 'hover:scale-[1.01]'}
        ${isOver && !isDragging ? 'scale-[0.98] shadow-inner bg-blue-50/30' : ''}
        ${isDragging ? 'cursor-grabbing' : ''}
      `}
    >
      {/* 玻璃效果背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/80 via-gray-50/40 to-white/60 backdrop-blur-sm"></div>

      {/* 主要內容區域 - 7/8 寬度 */}
      <div
        className="relative flex-1 p-6 select-none"
        style={{
          width: '87.5%',
          userSelect: 'none',
          WebkitUserSelect: 'none',
          MozUserSelect: 'none',
          msUserSelect: 'none'
        }}
      >
        {/* 頂部區域 */}
        <div className="flex items-center mb-4">
          {/* 拖拽手柄 + 門店圖標和名稱 */}
          <div
            {...attributes}
            {...listeners}
            className="flex items-center min-w-0 flex-1 cursor-grab active:cursor-grabbing hover:bg-gradient-to-br hover:from-gray-50/30 hover:to-blue-50/20 transition-all duration-200 rounded-xl p-2 -m-2"
          >
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg mr-4 flex-shrink-0">
              {store.icon ? (
                <Icon iconType={store.icon as IconType} size={24} color="white" />
              ) : (
                <Building className="text-white" size={24} />
              )}
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="text-lg font-bold text-gray-800 truncate group-hover:text-blue-700 transition-colors">
                {store.name || '未命名門店'}
              </h3>
              <p className="text-sm text-gray-500 font-medium">ID: {store.id || '無ID'}</p>
            </div>
          </div>

          {/* 操作按鈕 - 獨立於拖拽區域 */}
          <div className="flex items-center space-x-1 ml-4 flex-shrink-0 relative z-20">
            <button
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                console.log('Edit button clicked for store:', store.name);
                onEdit(e);
              }}
              onMouseDown={(e) => {
                e.stopPropagation();
              }}
              onPointerDown={(e) => {
                e.stopPropagation();
              }}
              className="p-2 rounded-xl hover:bg-blue-100/80 transition-all duration-200 group/edit cursor-pointer select-none"
              title="編輯門店"
              style={{
                pointerEvents: 'auto',
                touchAction: 'none',
                userSelect: 'none'
              }}
            >
              <Edit size={16} className="text-blue-500 group-hover/edit:text-blue-700 transition-colors pointer-events-none" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                console.log('Delete button clicked for store:', store.name);
                onDelete(e);
              }}
              onMouseDown={(e) => {
                e.stopPropagation();
              }}
              onPointerDown={(e) => {
                e.stopPropagation();
              }}
              className="p-2 rounded-xl hover:bg-red-100/80 transition-all duration-200 group/delete cursor-pointer select-none"
              title="刪除門店"
              style={{
                pointerEvents: 'auto',
                touchAction: 'none',
                userSelect: 'none'
              }}
            >
              <Trash2 size={16} className="text-red-500 group-hover/delete:text-red-700 transition-colors pointer-events-none" />
            </button>
          </div>
        </div>

        {/* 門店信息 - 也可以拖拽 */}
        <div
          {...attributes}
          {...listeners}
          className="space-y-2 cursor-grab active:cursor-grabbing hover:bg-gradient-to-br hover:from-gray-50/30 hover:to-blue-50/20 transition-all duration-200 rounded-xl p-2 -m-2"
        >
          <div className="flex items-center text-sm text-gray-600">
            <span className="font-medium text-gray-700 mr-2">地址:</span>
            <span className="truncate">{store.address || '無地址'}</span>
          </div>
          {store.phone && (
            <div className="flex items-center text-sm text-gray-600">
              <span className="font-medium text-gray-700 mr-2">電話:</span>
              <span>{store.phone}</span>
            </div>
          )}
          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm">
              <span className="font-medium text-gray-700 mr-2">狀態:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                store.status === 'active'
                  ? 'bg-green-100 text-green-700'
                  : 'bg-gray-100 text-gray-700'
              }`}>
                {store.status === 'active' ? '營業中' : '暫停營業'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 右側進入門店區域 - 1/8 寬度 */}
      <div
        className="relative flex items-center justify-center bg-gradient-to-br from-gray-50/20 via-white/10 to-gray-100/20
          hover:from-blue-50/30 hover:via-indigo-50/20 hover:to-blue-100/30
          border-l border-gray-200/30 cursor-pointer transition-all duration-300 group/enter overflow-hidden"
        style={{ width: '12.5%' }}
        onClick={(e) => {
          e.stopPropagation();
          onClick();
        }}
      >
        {/* 浮雕背景紋理 */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-transparent to-gray-100/20"></div>

        {/* 內嵌陰影效果 */}
        <div className="absolute inset-0 shadow-inner bg-gradient-to-br from-transparent via-white/10 to-transparent
          group-hover/enter:from-blue-50/20 group-hover/enter:to-indigo-50/20 transition-all duration-300"></div>

        {/* 箭頭 - 直接融合在區塊中 */}
        <ChevronRight size={20} className="relative z-10 text-gray-400 group-hover/enter:text-blue-600
          group-hover/enter:scale-110 transition-all duration-300 drop-shadow-sm" />

        {/* 右側高亮邊框 */}
        <div className="absolute right-0 top-0 bottom-0 w-1 bg-gradient-to-b from-transparent via-blue-400/60 to-transparent
          opacity-0 group-hover/enter:opacity-100 transition-opacity duration-300"></div>

        {/* 頂部和底部的細微高亮 */}
        <div className="absolute top-0 left-2 right-2 h-px bg-gradient-to-r from-transparent via-white/40 to-transparent
          opacity-0 group-hover/enter:opacity-100 transition-opacity duration-300"></div>
        <div className="absolute bottom-0 left-2 right-2 h-px bg-gradient-to-r from-transparent via-white/40 to-transparent
          opacity-0 group-hover/enter:opacity-100 transition-opacity duration-300"></div>
      </div>

      {/* 底部漸變邊框 */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500
        opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    </div>
  );
};

interface StoreManagementPageProps {
  onSelectStore: (store: Store) => void;
}

export const StoreManagementPage: React.FC<StoreManagementPageProps> = ({ onSelectStore }) => {
  const { isAuthenticated } = useAuthStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [stores, setStores] = useState<Store[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [storeToDelete, setStoreToDelete] = useState<Store | null>(null);
  const [isDragInProgress, setIsDragInProgress] = useState(false);
  const [activeStore, setActiveStore] = useState<Store | null>(null);

  // 拖拽傳感器設置 - 優化流暢性並避免與按鈕衝突
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3, // 很小的激活距離，快速響應
        tolerance: 5,
        delay: 50, // 很短的延遲，平衡響應性和按鈕點擊
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 獲取門店數據
  const fetchStores = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 只有在已登入狀態下才獲取門店數據
      if (isAuthenticated) {
        const data = await getAllStores();
        setStores(data);
      } else {
        // 未登入時清空門店列表
        setStores([]);
        setError('請先登入以查看門店數據');
      }
    } catch (err: any) {
      console.error('獲取門店數據失敗:', err);
      if (err.message === '未登入或登入已過期') {
        setError('請先登入以查看門店數據');
      } else {
        setError('獲取門店數據失敗，請重試');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加載和認證狀態變更時重新獲取數據
  useEffect(() => {
    fetchStores();
  }, [isAuthenticated]);

  // 處理搜索
  const filteredStores = stores.filter(store =>
    (store.name && store.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (store.id && store.id.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (store.address && store.address.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // 處理刪除門店
  const handleDeleteStore = async () => {
    if (!storeToDelete) return;

    try {
      await deleteStore(storeToDelete._id || storeToDelete.id);
      // 重新獲取門店列表
      fetchStores();
      // 關閉確認對話框
      setShowDeleteConfirm(false);
      setStoreToDelete(null);
    } catch (err) {
      console.error('刪除門店失敗:', err);
      setError('刪除門店失敗，請重試');
    }
  };

  // 處理編輯門店
  const handleEditStore = (store: Store) => {
    setSelectedStore(store);
    setShowEditModal(true);
  };

  // 處理拖拽結束
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setIsDragInProgress(false);
    setActiveStore(null);

    if (!over || active.id === over.id) {
      return;
    }

    const oldIndex = filteredStores.findIndex(store => (store._id || store.id) === active.id);
    const newIndex = filteredStores.findIndex(store => (store._id || store.id) === over.id);

    if (oldIndex === -1 || newIndex === -1) {
      return;
    }

    // 重新排列門店
    const newStores = arrayMove(filteredStores, oldIndex, newIndex);

    // 立即更新本地狀態，提供即時反饋
    setStores(newStores);

    // 異步更新後端，不阻塞UI
    setTimeout(async () => {
      try {
        // 準備排序數據
        const storeOrders = newStores.map((store, index) => ({
          _id: store._id || store.id,
          sortOrder: index * 10 // 使用間隔為10的排序值
        }));

        // 發送到後端更新排序
        await updateStoreOrder(storeOrders);
      } catch (error) {
        console.error('更新門店排序失敗:', error);
        setError('更新門店排序失敗，請重試');
        // 如果更新失敗，重新獲取數據
        fetchStores();
      }
    }, 0);
  };

  // 處理拖拽開始
  const handleDragStart = (event: DragStartEvent) => {
    setIsDragInProgress(true);
    const activeStoreData = filteredStores.find(store => (store._id || store.id) === event.active.id);
    setActiveStore(activeStoreData || null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/50 overflow-x-hidden">
      <div className="container mx-auto px-4 py-8 max-w-full">

        {/* 錯誤提示 */}
        {error && (
          <div className="mb-6 p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-xl shadow-sm">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* 未登入提示 */}
        {!isAuthenticated && (
          <div className="mb-8 p-8 bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-100 border border-blue-200 rounded-2xl shadow-lg text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
              <LogIn className="text-white" size={32} />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">請先登入</h3>
            <p className="text-gray-600 max-w-md mx-auto">您需要登入後才能查看和管理門店信息</p>
          </div>
        )}

        {/* 搜索欄和操作區域 */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row gap-4 items-stretch sm:items-center">
            {/* 搜索欄 */}
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="搜索門店名稱、ID或地址..."
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl
                  bg-white/80 backdrop-blur-sm shadow-sm placeholder-gray-400
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                  transition-all duration-200 hover:shadow-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={!isAuthenticated}
              />
            </div>

            {/* 新增門店按鈕 */}
            <button
              className={`flex items-center justify-center px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                isAuthenticated
                  ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
              onClick={() => isAuthenticated && setShowAddModal(true)}
              disabled={!isAuthenticated}
            >
              <Plus size={20} className="mr-2" />
              新增門店
            </button>
          </div>
        </div>

        {/* 門店卡片 */}
        {isLoading ? (
          <div className="text-center py-12">
            <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-blue-500 bg-blue-100 transition ease-in-out duration-150">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              載入中...
            </div>
          </div>
        ) : isAuthenticated && filteredStores.length > 0 ? (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            autoScroll={false}
          >
            <SortableContext
              items={filteredStores.map(store => store._id || store.id)}
              strategy={verticalListSortingStrategy}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 overflow-hidden">
                {filteredStores.map(store => (
                  <SortableStoreCard
                    key={store._id || store.id}
                    store={store}
                    onClick={() => !isDragInProgress && onSelectStore(store)}
                    onEdit={(e) => {
                      e.stopPropagation();
                      handleEditStore(store);
                    }}
                    onDelete={(e) => {
                      e.stopPropagation();
                      setStoreToDelete(store);
                      setShowDeleteConfirm(true);
                    }}
                  />
                ))}
              </div>
            </SortableContext>

            {/* 拖拽覆蓋層 */}
            <DragOverlay>
              {activeStore ? (
                <div className="bg-gradient-to-br from-white via-gray-50/50 to-white
                  backdrop-blur-sm border border-blue-400/60 rounded-2xl shadow-2xl
                  overflow-hidden flex opacity-95 scale-105 rotate-2 ring-2 ring-blue-400/50">

                  {/* 拖拽中的卡片內容 */}
                  <div className="relative flex-1 p-6" style={{ width: '87.5%' }}>
                    <div className="flex items-center mb-4">
                      <div className="flex items-center min-w-0 flex-1">
                        <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg mr-4 flex-shrink-0">
                          {activeStore.icon ? (
                            <Icon iconType={activeStore.icon as IconType} size={24} color="white" />
                          ) : (
                            <Building className="text-white" size={24} />
                          )}
                        </div>
                        <div className="min-w-0 flex-1">
                          <h3 className="text-lg font-bold text-gray-800 truncate">
                            {activeStore.name || '未命名門店'}
                          </h3>
                          <p className="text-sm text-gray-500 font-medium">ID: {activeStore.id || '無ID'}</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-gray-600">
                        <span className="font-medium text-gray-700 mr-2">地址:</span>
                        <span className="truncate">{activeStore.address || '無地址'}</span>
                      </div>
                      {activeStore.phone && (
                        <div className="flex items-center text-sm text-gray-600">
                          <span className="font-medium text-gray-700 mr-2">電話:</span>
                          <span>{activeStore.phone}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 右側進入區域 */}
                  <div className="relative flex items-center justify-center bg-gradient-to-br from-blue-50/40 via-indigo-50/30 to-blue-100/40
                    border-l border-blue-200/40" style={{ width: '12.5%' }}>
                    <ChevronRight size={20} className="text-blue-600" />
                  </div>
                </div>
              ) : null}
            </DragOverlay>
          </DndContext>
        ) : (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <Building className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {!isAuthenticated
                  ? '請先登入'
                  : searchTerm
                    ? '沒有找到門店'
                    : '尚未添加門店'
                }
              </h3>
              <p className="text-gray-500">
                {!isAuthenticated
                  ? '您需要登入後才能查看和管理門店'
                  : searchTerm
                    ? '沒有符合搜索條件的門店，請嘗試其他關鍵字'
                    : '點擊上方的「新增門店」按鈕來添加您的第一個門店'
                }
              </p>
            </div>
          </div>
        )}
      </div>

      {/* 新增門店模態窗口 */}
      <AddStoreModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          fetchStores();
          setShowAddModal(false);
        }}
      />

      {/* 編輯門店模態窗口 */}
      {selectedStore && (
        <EditStoreModal
          isOpen={showEditModal}
          store={selectedStore}
          onClose={() => {
            setShowEditModal(false);
            setSelectedStore(null);
          }}
          onSuccess={() => {
            fetchStores();
            setShowEditModal(false);
            setSelectedStore(null);
          }}
        />
      )}

      {/* 刪除確認對話框 */}
      {showDeleteConfirm && storeToDelete && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full overflow-hidden">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                  <Trash2 className="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900">確認刪除</h3>
                  <p className="text-sm text-gray-500">此操作無法撤銷</p>
                </div>
              </div>
              <p className="text-gray-700 mb-6">
                您確定要刪除門店 <span className="font-semibold text-gray-900">"{storeToDelete.name || '未命名門店'}"</span> 嗎？
                所有相關的數據都將被永久刪除。
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  className="px-6 py-2.5 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors font-medium"
                  onClick={() => {
                    setShowDeleteConfirm(false);
                    setStoreToDelete(null);
                  }}
                >
                  取消
                </button>
                <button
                  className="px-6 py-2.5 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
                  onClick={handleDeleteStore}
                >
                  確認刪除
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
