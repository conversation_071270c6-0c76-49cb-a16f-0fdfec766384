# 深色模式切換功能實現文檔

## 概述

本文檔記錄了在EPD Manager應用中實現深色/淺色模式切換功能的完整過程。

## 實現的功能

### 1. 主題管理系統
- **主題狀態管理**: 使用Zustand創建了`themeStore`來管理主題狀態
- **持久化存儲**: 主題偏好會自動保存到localStorage
- **系統主題檢測**: 自動檢測用戶系統的深色/淺色模式偏好
- **主題應用**: 自動將主題類名應用到DOM根元素

### 2. 主題切換組件
- **ThemeToggle組件**: 可配置的主題切換按鈕
- **多種尺寸**: 支持sm、md、lg三種尺寸
- **圖標切換**: 根據當前主題顯示太陽或月亮圖標
- **可選標籤**: 支持顯示文字標籤
- **無障礙支持**: 包含適當的aria-label和title屬性

### 3. 用戶界面集成
- **頂部導航**: 主題切換開關位於登出按鈕旁邊
- **響應式設計**: 在不同屏幕尺寸下都能正常工作
- **一致的樣式**: 使用Tailwind CSS的設計系統變量

### 4. 深色模式樣式支持
- **CSS變量系統**: 使用HSL顏色空間定義深色和淺色主題
- **組件更新**: 更新了狀態徽章和其他組件以支持深色模式
- **邊框和背景**: 所有UI元素都支持主題切換

## 文件結構

```
src/
├── store/
│   └── themeStore.ts              # 主題狀態管理
├── components/
│   ├── ui/
│   │   ├── ThemeToggle.tsx        # 主題切換組件
│   │   ├── DeviceStatusBadge.tsx  # 更新支持深色模式
│   │   ├── GatewayStatusBadge.tsx # 更新支持深色模式
│   │   └── ImageUpdateStatusBadge.tsx # 更新支持深色模式
├── i18n/
│   └── locales/
│       ├── zh-TW.json            # 中文翻譯
│       ├── en.json               # 英文翻譯
│       └── ja.json               # 日文翻譯
└── App.tsx                       # 主應用集成
```

## 技術實現細節

### 1. 主題狀態管理 (themeStore.ts)

```typescript
// 主題類型定義
export type Theme = 'light' | 'dark';

// 狀態接口
interface ThemeState {
  theme: Theme;
  isDark: boolean;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  applyTheme: (theme: Theme) => void;
}
```

### 2. CSS變量系統 (index.css)

```css
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  /* 其他淺色模式變量 */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  /* 其他深色模式變量 */
}
```

### 3. Tailwind配置 (tailwind.config.js)

```javascript
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class', // 啟用class模式的深色模式
  theme: {
    extend: {
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        // 其他設計系統顏色
      }
    }
  }
}
```

## 使用方法

### 1. 基本使用
```tsx
import ThemeToggle from './components/ui/ThemeToggle';

// 基本使用
<ThemeToggle />

// 帶標籤的大尺寸
<ThemeToggle size="lg" showLabel />
```

### 2. 在組件中使用主題狀態
```tsx
import { useThemeStore } from './store/themeStore';

const MyComponent = () => {
  const { theme, isDark, toggleTheme } = useThemeStore();
  
  return (
    <div className={`bg-background text-foreground ${isDark ? 'dark-specific-class' : 'light-specific-class'}`}>
      當前主題: {theme}
    </div>
  );
};
```

### 3. 支持深色模式的樣式
```tsx
// 使用Tailwind的dark:前綴
<div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
  自動適應主題的內容
</div>

// 使用設計系統變量（推薦）
<div className="bg-background text-foreground border border-border">
  使用設計系統的內容
</div>
```

## 特性

### 1. 自動初始化
- 應用啟動時自動檢測系統主題偏好
- 如果用戶之前設置過主題，則使用保存的偏好
- 監聽系統主題變化（僅在用戶未手動設置時）

### 2. 持久化
- 主題偏好自動保存到localStorage
- 頁面刷新後保持用戶選擇的主題
- 跨瀏覽器會話保持一致

### 3. 無障礙支持
- 適當的ARIA標籤
- 鍵盤導航支持
- 高對比度支持

### 4. 性能優化
- 使用CSS變量避免重新計算樣式
- 最小化DOM操作
- 高效的狀態管理

## 測試

### 測試檢查清單
- [ ] 主題切換按鈕正常工作
- [ ] 主題偏好正確保存和恢復
- [ ] 所有UI組件在深色模式下可讀
- [ ] 狀態徽章顏色正確適配
- [ ] 邊框和背景正確切換
- [ ] 系統主題檢測正常工作

## 未來改進

1. **更多主題選項**: 可以添加更多預設主題（如高對比度主題）
2. **動畫效果**: 添加主題切換的過渡動畫
3. **自定義主題**: 允許用戶自定義顏色方案
4. **時間自動切換**: 根據時間自動切換主題

## 總結

深色模式切換功能已成功實現並集成到EPD Manager應用中。該功能提供了完整的主題管理系統，支持用戶偏好保存、系統主題檢測，並確保所有UI組件在不同主題下都能正常工作。
