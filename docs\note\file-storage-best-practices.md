# 檔案存儲最佳實踐指南

## 📋 概述

本文檔提供 EPD Manager 系統中檔案存儲的最佳實踐指南，包括 GridFS 使用規範、效能優化建議、安全性考量等。

## 🎯 檔案存儲策略

### 何時使用 GridFS

✅ **適合使用 GridFS 的情況**:
- 檔案大小 > 16MB
- 需要串流處理的檔案
- 需要豐富元數據的檔案
- 需要版本控制的檔案
- 二進制檔案 (圖片、韌體、文檔)

❌ **不適合使用 GridFS 的情況**:
- 小於 1KB 的檔案
- 純文字配置檔案
- 頻繁修改的檔案
- 需要複雜查詢的結構化數據

### 檔案分類策略

```javascript
// 推薦的檔案類型分類
const FILE_CATEGORIES = {
  // 圖片類
  IMAGES: {
    USER_AVATAR: 'user-avatar',
    TEMPLATE_IMAGE: 'template-image', 
    BUG_REPORT_IMAGE: 'bug-report-image',
    PREVIEW_IMAGE: 'preview-image'
  },
  
  // 韌體類
  FIRMWARE: {
    ORIGINAL: 'original_firmware',
    PURE: 'pure_firmware',
    PATCH: 'firmware_patch'
  },
  
  // 文檔類
  DOCUMENTS: {
    MANUAL: 'user-manual',
    REPORT: 'system-report',
    LOG: 'system-log'
  }
};
```

## 🔧 實現最佳實踐

### 1. 統一的檔案上傳處理

```javascript
// 建議的通用檔案上傳函數
async function uploadFileToGridFS(gridFSBucket, fileBuffer, filename, metadata = {}) {
  // 驗證檔案
  validateFile(fileBuffer, filename, metadata);
  
  // 生成唯一檔案名 (避免衝突)
  const uniqueFilename = generateUniqueFilename(filename);
  
  // 準備完整元數據
  const fullMetadata = {
    ...metadata,
    originalFilename: filename,
    uploadDate: new Date(),
    fileSize: fileBuffer.length,
    checksum: calculateChecksum(fileBuffer)
  };
  
  return new Promise((resolve, reject) => {
    const uploadStream = gridFSBucket.openUploadStream(uniqueFilename, {
      metadata: fullMetadata
    });
    
    uploadStream.write(fileBuffer);
    uploadStream.end();
    
    uploadStream.on('finish', () => {
      resolve({
        fileId: uploadStream.id,
        filename: uniqueFilename,
        size: fileBuffer.length
      });
    });
    
    uploadStream.on('error', reject);
  });
}

// 檔案驗證函數
function validateFile(buffer, filename, metadata) {
  // 檔案大小檢查
  const maxSize = getMaxFileSize(metadata.type);
  if (buffer.length > maxSize) {
    throw new Error(`檔案大小超過限制: ${maxSize} bytes`);
  }
  
  // 檔案類型檢查
  const allowedTypes = getAllowedFileTypes(metadata.type);
  const fileExtension = path.extname(filename).toLowerCase();
  if (!allowedTypes.includes(fileExtension)) {
    throw new Error(`不支援的檔案類型: ${fileExtension}`);
  }
  
  // 檔案內容檢查 (可選)
  if (metadata.type === 'original_firmware') {
    validateBinFileFormat(buffer);
  }
}
```

### 2. 元數據標準化

```javascript
// 標準化的元數據結構
const STANDARD_METADATA = {
  // 必需欄位
  type: 'string',           // 檔案類型分類
  originalFilename: 'string', // 原始檔案名
  uploadDate: 'Date',       // 上傳時間
  uploadedBy: 'ObjectId',   // 上傳者ID
  
  // 可選欄位
  mimetype: 'string',       // MIME 類型
  fileSize: 'number',       // 檔案大小
  checksum: 'string',       // 校驗和
  description: 'string',    // 檔案描述
  tags: 'Array<string>',    // 標籤
  
  // 業務特定欄位
  deviceType: 'string',     // 設備類型 (韌體專用)
  functionType: 'string',   // 功能類型 (韌體專用)
  version: 'string',        // 版本號 (韌體專用)
  
  // 系統欄位
  isActive: 'boolean',      // 是否啟用
  accessLevel: 'string',    // 存取級別
  expiryDate: 'Date'        // 過期時間
};

// 元數據建構函數
function buildMetadata(type, baseData, customData = {}) {
  const metadata = {
    type,
    uploadDate: new Date(),
    isActive: true,
    ...baseData,
    ...customData
  };
  
  // 根據類型添加特定欄位
  switch (type) {
    case 'original_firmware':
    case 'pure_firmware':
      if (!metadata.deviceType || !metadata.functionType) {
        throw new Error('韌體檔案必須包含設備類型和功能類型');
      }
      break;
      
    case 'bug-report-image':
      metadata.accessLevel = 'internal';
      break;
      
    default:
      metadata.accessLevel = 'public';
  }
  
  return metadata;
}
```

### 3. 檔案下載優化

```javascript
// 優化的檔案下載處理
async function downloadFileFromGridFS(gridFSBucket, fileId, res, options = {}) {
  try {
    // 獲取檔案資訊
    const fileInfo = await gridFSBucket.find({ _id: fileId }).toArray();
    if (fileInfo.length === 0) {
      return res.status(404).json({ error: '檔案不存在' });
    }
    
    const file = fileInfo[0];
    
    // 設置回應標頭
    const headers = {
      'Content-Type': file.metadata?.mimetype || 'application/octet-stream',
      'Content-Length': file.length,
      'Cache-Control': options.cache || 'public, max-age=3600',
      'ETag': `"${file._id}"`
    };
    
    // 支援斷點續傳
    if (options.supportRange) {
      headers['Accept-Ranges'] = 'bytes';
      
      const range = req.headers.range;
      if (range) {
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : file.length - 1;
        
        headers['Content-Range'] = `bytes ${start}-${end}/${file.length}`;
        headers['Content-Length'] = (end - start) + 1;
        
        res.status(206); // Partial Content
      }
    }
    
    // 設置檔案下載名稱
    if (options.download) {
      const filename = file.metadata?.originalFilename || file.filename;
      headers['Content-Disposition'] = `attachment; filename="${encodeURIComponent(filename)}"`;
    }
    
    res.set(headers);
    
    // 創建下載流
    const downloadStream = gridFSBucket.openDownloadStream(fileId);
    
    downloadStream.on('error', (error) => {
      console.error('檔案下載錯誤:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: '檔案下載失敗' });
      }
    });
    
    downloadStream.pipe(res);
    
  } catch (error) {
    console.error('下載檔案時發生錯誤:', error);
    res.status(500).json({ error: '內部伺服器錯誤' });
  }
}
```

## 🚀 效能優化

### 1. 索引策略

```javascript
// 推薦的 GridFS 索引
async function createGridFSIndexes(db) {
  const filesCollection = db.collection('fs.files');
  const chunksCollection = db.collection('fs.chunks');
  
  // fs.files 索引
  await filesCollection.createIndex({ filename: 1 });
  await filesCollection.createIndex({ uploadDate: -1 });
  await filesCollection.createIndex({ 'metadata.type': 1 });
  await filesCollection.createIndex({ 'metadata.uploadedBy': 1 });
  await filesCollection.createIndex({ 'metadata.isActive': 1 });
  
  // 複合索引
  await filesCollection.createIndex({ 
    'metadata.type': 1, 
    'metadata.isActive': 1, 
    uploadDate: -1 
  });
  
  // fs.chunks 索引 (GridFS 自動創建，但可以優化)
  await chunksCollection.createIndex({ files_id: 1, n: 1 });
}
```

### 2. 快取策略

```javascript
// 檔案元數據快取
const fileMetadataCache = new Map();

async function getFileMetadata(fileId, useCache = true) {
  if (useCache && fileMetadataCache.has(fileId)) {
    return fileMetadataCache.get(fileId);
  }
  
  const metadata = await db.collection('fs.files').findOne({ _id: fileId });
  
  if (metadata && useCache) {
    // 快取 1 小時
    setTimeout(() => fileMetadataCache.delete(fileId), 3600000);
    fileMetadataCache.set(fileId, metadata);
  }
  
  return metadata;
}

// 圖片縮圖快取
const thumbnailCache = new Map();

async function generateThumbnail(imageId, size = '150x150') {
  const cacheKey = `${imageId}_${size}`;
  
  if (thumbnailCache.has(cacheKey)) {
    return thumbnailCache.get(cacheKey);
  }
  
  // 生成縮圖邏輯...
  const thumbnail = await createImageThumbnail(imageId, size);
  
  thumbnailCache.set(cacheKey, thumbnail);
  return thumbnail;
}
```

### 3. 批次操作

```javascript
// 批次檔案上傳
async function uploadMultipleFiles(gridFSBucket, files, commonMetadata = {}) {
  const uploadPromises = files.map(file => {
    const metadata = buildMetadata(file.type, {
      ...commonMetadata,
      originalFilename: file.name,
      mimetype: file.mimetype
    });
    
    return uploadFileToGridFS(gridFSBucket, file.buffer, file.name, metadata);
  });
  
  return Promise.all(uploadPromises);
}

// 批次檔案刪除
async function deleteMultipleFiles(gridFSBucket, fileIds) {
  const deletePromises = fileIds.map(fileId => {
    return gridFSBucket.delete(fileId).catch(error => {
      console.error(`刪除檔案 ${fileId} 失敗:`, error);
      return { fileId, error: error.message };
    });
  });
  
  return Promise.allSettled(deletePromises);
}
```

## 🔒 安全性最佳實踐

### 1. 檔案驗證

```javascript
// 檔案安全掃描
async function scanFileForSecurity(buffer, filename) {
  const checks = [];
  
  // 檔案大小檢查
  if (buffer.length > MAX_FILE_SIZE) {
    checks.push({ type: 'size', status: 'fail', message: '檔案過大' });
  }
  
  // 檔案類型檢查
  const detectedType = await detectFileType(buffer);
  const expectedType = getExpectedTypeFromFilename(filename);
  if (detectedType !== expectedType) {
    checks.push({ type: 'type', status: 'fail', message: '檔案類型不匹配' });
  }
  
  // 惡意內容檢查
  const malwareCheck = await scanForMalware(buffer);
  if (!malwareCheck.clean) {
    checks.push({ type: 'malware', status: 'fail', message: '檢測到惡意內容' });
  }
  
  return {
    safe: checks.every(check => check.status !== 'fail'),
    checks
  };
}

// 檔案存取權限檢查
function checkFileAccess(user, fileMetadata, operation) {
  // 檢查檔案存取級別
  if (fileMetadata.accessLevel === 'private' && 
      fileMetadata.uploadedBy.toString() !== user._id.toString()) {
    return false;
  }
  
  // 檢查操作權限
  const requiredPermission = `file:${operation}`;
  return user.permissions.includes(requiredPermission);
}
```

### 2. 檔案加密 (可選)

```javascript
// 敏感檔案加密存儲
const crypto = require('crypto');

function encryptFile(buffer, key) {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher('aes-256-cbc', key, iv);
  
  const encrypted = Buffer.concat([
    iv,
    cipher.update(buffer),
    cipher.final()
  ]);
  
  return encrypted;
}

function decryptFile(encryptedBuffer, key) {
  const iv = encryptedBuffer.slice(0, 16);
  const encrypted = encryptedBuffer.slice(16);
  
  const decipher = crypto.createDecipher('aes-256-cbc', key, iv);
  
  const decrypted = Buffer.concat([
    decipher.update(encrypted),
    decipher.final()
  ]);
  
  return decrypted;
}
```

## 🧹 維護與清理

### 1. 定期清理策略

```javascript
// 清理過期檔案
async function cleanupExpiredFiles(db) {
  const expiredFiles = await db.collection('fs.files').find({
    'metadata.expiryDate': { $lt: new Date() }
  }).toArray();
  
  for (const file of expiredFiles) {
    await gridFSBucket.delete(file._id);
    console.log(`已刪除過期檔案: ${file.filename}`);
  }
  
  return expiredFiles.length;
}

// 清理孤立檔案
async function cleanupOrphanedFiles(db) {
  // 獲取所有 GridFS 檔案 ID
  const allFileIds = await db.collection('fs.files').distinct('_id');
  
  // 獲取業務集合中引用的檔案 ID
  const referencedIds = new Set([
    ...await db.collection('software').distinct('binFileId'),
    ...await db.collection('software').distinct('extractedBinId'),
    ...await db.collection('bugReports').distinct('imageId'),
    // 添加其他業務集合的檔案引用...
  ]);
  
  // 找出孤立檔案
  const orphanedIds = allFileIds.filter(id => !referencedIds.has(id));
  
  // 刪除孤立檔案
  for (const fileId of orphanedIds) {
    await gridFSBucket.delete(fileId);
  }
  
  return orphanedIds.length;
}

// 定期維護任務
async function performMaintenance(db) {
  console.log('開始檔案系統維護...');
  
  const expiredCount = await cleanupExpiredFiles(db);
  const orphanedCount = await cleanupOrphanedFiles(db);
  
  console.log(`維護完成: 清理 ${expiredCount} 個過期檔案, ${orphanedCount} 個孤立檔案`);
  
  // 更新統計資訊
  await updateStorageStatistics(db);
}

// 每日執行維護任務
setInterval(() => {
  performMaintenance(db).catch(console.error);
}, 24 * 60 * 60 * 1000); // 24 小時
```

### 2. 監控與統計

```javascript
// 存儲統計
async function getStorageStatistics(db) {
  const stats = await db.collection('fs.files').aggregate([
    {
      $group: {
        _id: '$metadata.type',
        count: { $sum: 1 },
        totalSize: { $sum: '$length' },
        avgSize: { $avg: '$length' }
      }
    },
    {
      $sort: { totalSize: -1 }
    }
  ]).toArray();
  
  const totalStats = await db.collection('fs.files').aggregate([
    {
      $group: {
        _id: null,
        totalFiles: { $sum: 1 },
        totalSize: { $sum: '$length' }
      }
    }
  ]).toArray();
  
  return {
    byType: stats,
    total: totalStats[0] || { totalFiles: 0, totalSize: 0 }
  };
}

// 效能監控
function monitorGridFSPerformance() {
  const metrics = {
    uploadCount: 0,
    downloadCount: 0,
    uploadTime: [],
    downloadTime: [],
    errors: []
  };
  
  // 記錄上傳效能
  function recordUpload(duration, success) {
    metrics.uploadCount++;
    if (success) {
      metrics.uploadTime.push(duration);
    } else {
      metrics.errors.push({ type: 'upload', time: new Date() });
    }
  }
  
  // 記錄下載效能
  function recordDownload(duration, success) {
    metrics.downloadCount++;
    if (success) {
      metrics.downloadTime.push(duration);
    } else {
      metrics.errors.push({ type: 'download', time: new Date() });
    }
  }
  
  return { recordUpload, recordDownload, getMetrics: () => metrics };
}
```

## 📊 總結

### 關鍵要點

1. **統一架構**: 所有檔案使用 GridFS 統一存儲
2. **元數據標準化**: 使用一致的元數據結構
3. **安全第一**: 檔案驗證、權限控制、加密存儲
4. **效能優化**: 索引、快取、批次操作
5. **定期維護**: 清理過期檔案、監控效能

### 開發建議

- 📝 **文檔化**: 記錄所有檔案類型和元數據結構
- 🧪 **測試**: 編寫檔案操作的單元測試和整合測試
- 📊 **監控**: 實施檔案存儲的效能監控
- 🔄 **備份**: 定期備份重要檔案
- 🚀 **優化**: 根據使用模式持續優化存儲策略

---

**文檔版本**: 1.0.0  
**最後更新**: 2024-12-20  
**適用範圍**: EPD Manager 檔案存儲系統
