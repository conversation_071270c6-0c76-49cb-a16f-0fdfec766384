import { EPDConversionOptions, EPDConversionResult, ImageInfo, PixelData } from '../types';
import { rotateImageData } from '../utils/rotationUtils';

/**
 * 基礎轉換器抽象類
 */
export abstract class BaseConverter {
  protected width: number;
  protected height: number;
  protected paddedWidth: number;
  protected buffer: Uint8Array;
  protected options: EPDConversionOptions;

  constructor(options: EPDConversionOptions) {
    this.options = options;
    // 注意：寬度和高度將在 convert 方法中根據旋轉後的圖像重新設置
    this.width = options.width;
    this.height = options.height;
    this.paddedWidth = 0; // 將在 initializeForRotatedImage 中設置
    this.buffer = new Uint8Array(0); // 將在 initializeForRotatedImage 中重新分配
  }

  // 抽象方法，由子類實作
  abstract calculatePaddedWidth(): number;
  abstract calculateBufferSize(): number;
  abstract processPixel(x: number, y: number, pixel: PixelData): void;
  abstract getPixelData(): Uint8Array;
  protected abstract getBytesPerPixel(): number;

  /**
   * 創建 ImageInfo 結構
   * 使用旋轉為0度後的長寬資訊
   */
  protected createImageInfo(): ImageInfo {
    const imageInfo = {
      imagecode: this.options.imagecode,
      x: this.options.x || 0,
      y: this.options.y || 0,
      width: this.paddedWidth,  // 使用旋轉為0度後的對齊寬度
      height: this.height       // 使用旋轉為0度後的高度
    };

    console.log(`EPD轉換: 創建ImageInfo - 寬度: ${imageInfo.width}, 高度: ${imageInfo.height} (旋轉為0度後的尺寸)`);

    return imageInfo;
  }

  /**
   * 將 ImageInfo 序列化為字節數組 (Little Endian)
   */
  protected serializeImageInfo(imageInfo: ImageInfo): Uint8Array {
    const buffer = new ArrayBuffer(12); // uint32 + 4 * uint16 = 12 bytes
    const view = new DataView(buffer);

    view.setUint32(0, imageInfo.imagecode, true);  // Little Endian
    view.setUint16(4, imageInfo.x, true);
    view.setUint16(6, imageInfo.y, true);
    view.setUint16(8, imageInfo.width, true);
    view.setUint16(10, imageInfo.height, true);

    return new Uint8Array(buffer);
  }

  /**
   * 組合 ImageInfo + 像素數據
   */
  protected combineRawData(imageInfo: ImageInfo, pixelData: Uint8Array): Uint8Array {
    const imageInfoBytes = this.serializeImageInfo(imageInfo);
    const rawdata = new Uint8Array(imageInfoBytes.length + pixelData.length);

    rawdata.set(imageInfoBytes, 0);
    rawdata.set(pixelData, imageInfoBytes.length);

    return rawdata;
  }

  /**
   * 根據旋轉後的圖像初始化轉換器尺寸和緩衝區
   */
  protected initializeForRotatedImage(rotatedImageData: ImageData): void {
    // 使用旋轉後的實際尺寸
    this.width = rotatedImageData.width;
    this.height = rotatedImageData.height;

    // 重新計算對齊後的寬度
    this.paddedWidth = this.calculatePaddedWidth();

    // 重新分配緩衝區
    this.buffer = new Uint8Array(this.calculateBufferSize());

    console.log(`EPD轉換: 使用旋轉後尺寸 ${this.width}x${this.height}，對齊寬度: ${this.paddedWidth}`);
  }

  /**
   * 檢查是否需要旋轉以匹配設備尺寸
   */
  protected needsRotationForDeviceMatch(imageData: ImageData): boolean {
    const canvasWidth = imageData.width;
    const canvasHeight = imageData.height;
    const deviceWidth = this.options.width;
    const deviceHeight = this.options.height;

    // 檢查 canvasSize 跟 deviceSize 是否長寬相反
    const canvasIsLandscape = canvasWidth > canvasHeight;
    const deviceIsLandscape = deviceWidth > deviceHeight;

    return canvasIsLandscape !== deviceIsLandscape;
  }

  /**
   * 應用旋轉以匹配設備尺寸
   */
  protected applyRotationForDeviceMatch(imageData: ImageData): ImageData {
    if (!this.needsRotationForDeviceMatch(imageData)) {
      console.log('EPD轉換: canvasSize與deviceSize方向一致，無需旋轉');
      return imageData;
    }

    console.log(`EPD轉換: canvasSize(${imageData.width}x${imageData.height})與deviceSize(${this.options.width}x${this.options.height})長寬相反，將預覽圖轉90度`);

    // 將預覽圖轉90度以匹配設備尺寸
    return rotateImageData(imageData, 90);
  }

  /**
   * 處理圖像數據
   */
  protected processImageData(imageData: ImageData): void {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < this.paddedWidth; x++) {
        if (x < width) {
          // 處理實際像素
          const index = (y * width + x) * 4;
          const pixel: PixelData = {
            r: data[index],
            g: data[index + 1],
            b: data[index + 2],
            a: data[index + 3]
          };
          this.processPixel(x, y, pixel);
        } else {
          // 填充像素（白色）
          const paddingPixel: PixelData = { r: 255, g: 255, b: 255, a: 255 };
          this.processPixel(x, y, paddingPixel);
        }
      }
    }
  }

  /**
   * 通用的轉換流程
   */
  convert(imageData: ImageData): EPDConversionResult {
    const startTime = performance.now();

    try {
      // 記錄原始尺寸
      const originalWidth = imageData.width;
      const originalHeight = imageData.height;

      // 應用旋轉以匹配設備尺寸
      const rotatedImageData = this.applyRotationForDeviceMatch(imageData);

      // 根據旋轉後的圖像重新初始化轉換器尺寸和緩衝區
      this.initializeForRotatedImage(rotatedImageData);

      // 處理每個像素
      this.processImageData(rotatedImageData);

      // 獲取像素數據
      const pixelData = this.getPixelData();

      // 創建 ImageInfo
      const imageInfo = this.createImageInfo();

      // 組合最終的 rawdata
      const rawdata = this.combineRawData(imageInfo, pixelData);

      const endTime = performance.now();

      return {
        success: true,
        rawdata,
        imageInfo,
        pixelData,
        metadata: {
          originalSize: { width: originalWidth, height: originalHeight },
          finalSize: { width: this.paddedWidth, height: this.height },
          bytesPerPixel: this.getBytesPerPixel(),
          totalBytes: rawdata.length,
          imageInfoBytes: 12, // ImageInfo 固定 12 字節
          pixelDataBytes: pixelData.length,
          processingTime: endTime - startTime,
          colorType: this.options.colorType
        }
      };
    } catch (error) {
      const endTime = performance.now();

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          originalSize: { width: imageData.width, height: imageData.height },
          finalSize: { width: 0, height: 0 },
          bytesPerPixel: 0,
          totalBytes: 0,
          imageInfoBytes: 0,
          pixelDataBytes: 0,
          processingTime: endTime - startTime,
          colorType: this.options.colorType
        }
      };
    }
  }
}
