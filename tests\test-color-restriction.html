<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>顏色限制功能驗證</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .color-test {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 3px;
        }
        .color-test .result {
            margin-left: 20px;
            padding: 5px 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
            background: #f9f9f9;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .color-box {
            width: 30px;
            height: 30px;
            border: 1px solid #333;
            display: inline-block;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>顏色限制功能驗證</h1>
        <p>驗證 template editor 中根據 colortype 限制元素顏色的功能</p>

        <div class="test-section">
            <h3>Gray16 顏色類型驗證</h3>
            <div id="gray16-test"></div>
        </div>

        <div class="test-section">
            <h3>BWR (黑白紅) 顏色類型驗證</h3>
            <div id="bwr-test"></div>
        </div>

        <div class="test-section">
            <h3>BWRY (黑白紅黃) 顏色類型驗證</h3>
            <div id="bwry-test"></div>
        </div>

        <div class="test-section">
            <h3>顏色映射驗證</h3>
            <div id="mapping-test"></div>
        </div>
    </div>

    <script type="module">
        // 模擬 DisplayColorType 枚舉
        const DisplayColorType = {
            BW: "Gray16",
            BWR: "Black & White & Red",
            BWRY: "Black & White & Red & Yellow"
        };

        // 模擬 getAvailableColorsForColorType 函數
        function getAvailableColorsForColorType(colorType) {
            const normalizedColorType = typeof colorType === 'string'
                ? colorType.toUpperCase()
                : colorType;

            // 特殊處理 Gray16，提供 16 個灰度級別
            if (colorType === DisplayColorType.BW || colorType === 'Gray16' || colorType === 'BW') {
                const grayColors = [];
                for (let i = 0; i < 16; i++) {
                    const grayValue = Math.round((255 / 15) * i);
                    const hex = `#${grayValue.toString(16).padStart(2, '0').repeat(3)}`;
                    grayColors.push(hex.toUpperCase());
                }
                return grayColors;
            }

            // 根據顏色類型定義調色板
            switch (normalizedColorType) {
                case DisplayColorType.BWR:
                case 'BLACK & WHITE & RED':
                case 'BWR':
                    return ['#000000', '#FFFFFF', '#FF0000'];

                case DisplayColorType.BWRY:
                case 'BLACK & WHITE & RED & YELLOW':
                case 'BWRY':
                    return ['#000000', '#FFFFFF', '#FF0000', '#FFFF00'];

                default:
                    // 默認黑白調色板
                    return ['#000000', '#FFFFFF'];
            }
        }

        // 驗證函數
        function testColorType(colorType, containerId) {
            const container = document.getElementById(containerId);
            const colors = getAvailableColorsForColorType(colorType);

            container.innerHTML = `
                <p><strong>顏色類型:</strong> ${colorType}</p>
                <p><strong>可用顏色數量:</strong> ${colors.length}</p>
                <div class="color-test">
                    <strong>可用顏色:</strong>
                    ${colors.map(color => `
                        <div class="color-box" style="background-color: ${color}" title="${color}"></div>
                    `).join('')}
                </div>
                <p><strong>顏色列表:</strong> ${colors.join(', ')}</p>
            `;
        }

        // 測試顏色映射
        function testColorMapping() {
            const container = document.getElementById('mapping-test');
            const testCases = [
                { input: '#FF5555', colorType: 'BWR', expected: '#FF0000' },
                { input: '#FFFF80', colorType: 'BWRY', expected: '#FFFF00' },
                { input: '#808080', colorType: 'Gray16', expected: '#888888' },
                { input: '#123456', colorType: 'BWR', expected: '#000000' }
            ];

            let html = '<p>驗證任意顏色映射到可用顏色:</p>';

            testCases.forEach(testCase => {
                // 簡化的映射邏輯（實際實現會更複雜）
                const availableColors = getAvailableColorsForColorType(testCase.colorType);
                let mappedColor = availableColors[0]; // 默認映射到第一個顏色

                // 簡單的映射邏輯
                if (testCase.input.toUpperCase() === '#FF0000' || testCase.input.includes('FF') && testCase.input.includes('00')) {
                    mappedColor = availableColors.find(c => c === '#FF0000') || mappedColor;
                } else if (testCase.input.toUpperCase() === '#FFFF00' || (testCase.input.includes('FF') && testCase.input.endsWith('00'))) {
                    mappedColor = availableColors.find(c => c === '#FFFF00') || mappedColor;
                } else if (testCase.input.includes('80') || testCase.input.includes('88')) {
                    mappedColor = availableColors.find(c => c.includes('88')) || mappedColor;
                }

                html += `
                    <div class="color-test">
                        <div class="color-box" style="background-color: ${testCase.input}"></div>
                        <span>${testCase.input} (${testCase.colorType}) → </span>
                        <div class="color-box" style="background-color: ${mappedColor}"></div>
                        <span>${mappedColor}</span>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 執行驗證
        testColorType('Gray16', 'gray16-test');
        testColorType('Black & White & Red', 'bwr-test');
        testColorType('Black & White & Red & Yellow', 'bwry-test');
        testColorMapping();

        console.log('顏色限制功能驗證完成');
        console.log('Gray16 顏色:', getAvailableColorsForColorType('Gray16'));
        console.log('BWR 顏色:', getAvailableColorsForColorType('Black & White & Red'));
        console.log('BWRY 顏色:', getAvailableColorsForColorType('Black & White & Red & Yellow'));
    </script>
</body>
</html>
