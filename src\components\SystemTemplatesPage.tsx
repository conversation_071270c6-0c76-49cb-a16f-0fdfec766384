import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TemplateList } from './TemplateList';
import { AddTemplateModal } from './AddTemplateModal';

export const SystemTemplatesPage: React.FC = () => {
  const { t } = useTranslation();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  return (
    <>
      <main className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-4 py-2">
        <TemplateList
          onAddTemplate={() => setIsAddModalOpen(true)}
          store={null}
          defaultTemplateTypeFilter="system"
          systemTemplatesOnly={true}
        />
      </main>

      {/* 添加模板模態窗口 */}
      <AddTemplateModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        store={null}
        systemTemplatesOnly={true}
      />
    </>
  );
};
