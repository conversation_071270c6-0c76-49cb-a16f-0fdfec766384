# 後端Icon元件渲染實現計畫

## 問題分析

### 當前狀況
1. **前端渲染機制**：
   - 使用 `lucide-react` 圖標庫
   - 通過 React 的 `createElement` 和 `createRoot` 動態渲染SVG圖標
   - 支援完整的圖標屬性：`iconType`, `size`, `color`, `strokeWidth`
   - 渲染效果完美，與設計一致

2. **後端渲染機制**：
   - 目前只渲染為佔位符文字 "Icon"
   - 無法生成實際的圖標SVG內容
   - 導致後端生成的預覽圖與前端效果不一致

### 核心問題
後端缺乏將 `lucide-react` 圖標轉換為實際SVG內容的能力，需要建立一套與前端完全一致的圖標渲染機制。

## 解決方案架構

### 方案一：使用 `lucide-static` NPM 套件 (強烈推薦)
使用 `lucide-static` 套件直接在 Node.js 後端獲取 SVG 字符串數據。

**優點**：
- 與前端使用完全相同的圖標庫
- 自動同步圖標更新，無需手動維護
- 官方支援，穩定可靠
- 直接提供 SVG 字符串，易於集成
- 支援所有圖標類型和屬性

**缺點**：
- 需要額外的 NPM 依賴

### 方案二：SVG模板庫 (備選方案)
建立一個包含所有支援圖標的SVG模板庫，後端直接使用SVG字符串渲染。

**優點**：
- 渲染效果與前端完全一致
- 性能優異，無需額外依賴
- 易於維護和擴展

**缺點**：
- 需要手動維護SVG模板
- 新增圖標時需要同步更新
- 維護成本較高

## 實施計畫

### 階段一：安裝和配置 `lucide-static` 套件

#### 1.1 安裝依賴
```bash
cd server
npm install lucide-static
```

#### 1.2 創建圖標渲染服務
```
server/
├── utils/
│   └── iconRenderer.js        # 圖標渲染服務
└── services/
    └── previewService.js      # 修改現有預覽服務
```

#### 1.3 支援的圖標類型
從 `src/components/editor/elements/IconComponent.tsx` 提取所有支援的圖標類型：
- star, heart, square, circle, triangle
- alert-circle, check-circle, info, x-circle
- arrow-up, arrow-down, arrow-left, arrow-right
- shopping-cart, truck, package, home, user
- mail, phone, calendar, clock, settings
- bookmark, bell, camera

#### 1.4 圖標名稱映射
建立前端圖標名稱到 `lucide-static` 圖標名稱的映射關係：
- 前端使用 kebab-case：`alert-circle`
- `lucide-static` 使用 camelCase：`alertCircle`

### 階段二：實現後端圖標渲染服務

#### 2.1 創建圖標渲染函數
```javascript
// server/utils/iconRenderer.js
const lucideIcons = require('lucide-static/lib');

function convertIconName(iconType) {
  // 將 kebab-case 轉換為 camelCase
  return iconType.split('-')
    .map((part, index) => index === 0 ? part : part.charAt(0).toUpperCase() + part.slice(1))
    .join('');
}

function renderIconSvg(iconType, options = {}) {
  const { size = 24, color = '#000', strokeWidth = 2 } = options;

  // 轉換圖標名稱
  const camelCaseIconName = convertIconName(iconType);

  // 獲取 SVG 字符串
  const iconSvg = lucideIcons[camelCaseIconName] || lucideIcons.circle;

  // 自定義 SVG 屬性
  return iconSvg
    .replace(/width="24"/, `width="${size}"`)
    .replace(/height="24"/, `height="${size}"`)
    .replace(/stroke="currentColor"/, `stroke="${color}"`)
    .replace(/stroke-width="2"/, `stroke-width="${strokeWidth}"`);
}

module.exports = { renderIconSvg };
```

#### 2.2 集成到預覽服務
修改 `server/services/previewService.js` 中的 `renderTemplateElements` 函數：
- 引入圖標渲染服務
- 檢測到 `element.type === 'icon'` 時調用渲染服務
- 將生成的 SVG 內容插入到 DOM 元素中

#### 2.3 DOM集成方式
```javascript
// 在 server/services/previewService.js 中
const { renderIconSvg } = require('../utils/iconRenderer');

// 在 renderTemplateElements 函數中
if (element.type === 'icon') {
  const iconSvg = renderIconSvg(element.iconType || 'circle', {
    size: Math.min(element.width, element.height) * 0.8,
    color: element.lineColor || '#000',
    strokeWidth: element.lineWidth || 2
  });

  elementDiv.innerHTML = iconSvg;
  elementDiv.style.display = 'flex';
  elementDiv.style.justifyContent = 'center';
  elementDiv.style.alignItems = 'center';
  elementDiv.style.backgroundColor = 'transparent';
  elementDiv.style.border = 'none';
}
```

### 階段三：確保渲染一致性

#### 3.1 參數映射一致性
確保後端圖標渲染使用與前端完全相同的參數計算邏輯：
- 圖標尺寸：`Math.min(element.width, element.height) * 0.8`
- 圖標顏色：`element.lineColor || '#000'`
- 線條寬度：`element.lineWidth || 2`

#### 3.2 圖標名稱轉換
實現與前端相同的圖標名稱轉換邏輯：
- 支援 kebab-case 到 PascalCase 的轉換
- 例如：'alert-circle' → 'AlertCircle'

#### 3.3 容器樣式一致性
確保後端生成的圖標容器樣式與前端一致：
- `display: flex`
- `justifyContent: center`
- `alignItems: center`
- `backgroundColor: transparent`
- `border: none`

### 階段四：測試與驗證

#### 4.1 單元測試
- 測試所有支援的圖標類型
- 驗證不同參數組合的渲染結果
- 確保SVG輸出格式正確

#### 4.2 集成測試
- 創建包含圖標元素的測試模板
- 比較前端和後端生成的預覽圖
- 驗證圖標在不同顏色類型下的渲染效果

#### 4.3 視覺回歸測試
- 建立圖標渲染的基準圖像
- 自動檢測渲染結果的視覺差異
- 確保更新不會破壞現有功能

## 技術實現細節

### 使用 `lucide-static` 的優勢
```javascript
// 直接從官方套件獲取 SVG 字符串
const lucideIcons = require('lucide-static/lib');

// 例如獲取 star 圖標
const starIcon = lucideIcons.star;
// 返回完整的 SVG 字符串：
// '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/></svg>'
```

### 圖標名稱轉換函數
```javascript
function convertIconName(iconType) {
  // 將 kebab-case 轉換為 camelCase
  return iconType.split('-')
    .map((part, index) => index === 0 ? part : part.charAt(0).toUpperCase() + part.slice(1))
    .join('');
}

// 例子：
// 'alert-circle' → 'alertCircle'
// 'shopping-cart' → 'shoppingCart'
// 'arrow-up' → 'arrowUp'
```

### SVG 屬性自定義
```javascript
function customizeSvgAttributes(svgString, options) {
  const { size, color, strokeWidth } = options;

  return svgString
    .replace(/width="24"/, `width="${size}"`)
    .replace(/height="24"/, `height="${size}"`)
    .replace(/stroke="currentColor"/, `stroke="${color}"`)
    .replace(/stroke-width="2"/, `stroke-width="${strokeWidth}"`);
}
```

### 錯誤處理機制
- 不支援的圖標類型時使用預設圖標（circle）
- 圖標名稱轉換失敗時記錄錯誤並使用備用圖標
- SVG 屬性替換失敗時使用原始 SVG
- 記錄詳細的錯誤日誌便於調試

## 預期效果

### 實施完成後
1. **渲染一致性**：後端生成的預覽圖中的圖標與前端完全一致
2. **功能完整性**：支援所有前端支援的圖標類型和屬性
3. **性能優化**：圖標渲染不會顯著影響預覽圖生成速度
4. **維護性**：新增圖標時可以輕鬆擴展

### 成功指標
- [ ] 所有支援的圖標類型都能正確渲染
- [ ] 圖標顏色、尺寸、線條寬度參數生效
- [ ] 前後端預覽圖視覺效果一致
- [ ] 預覽圖生成性能無明顯下降
- [ ] 通過所有自動化測試

## 風險評估與緩解

### 潛在風險
1. **套件版本兼容性**：`lucide-static` 版本更新可能影響圖標可用性
2. **性能影響**：大量圖標渲染可能影響預覽生成速度
3. **圖標名稱映射**：前端和後端圖標名稱格式不一致

### 緩解措施
1. 鎖定 `lucide-static` 版本，定期測試更新
2. 實施圖標渲染快取機制
3. 建立完整的圖標名稱映射表和測試
4. 實施錯誤處理和備用圖標機制

## 後續優化

### 長期改進方向
1. **圖標快取機制**：實施智能快取減少重複渲染和字符串處理
2. **圖標預處理**：啟動時預處理所有圖標，提高運行時性能
3. **版本同步機制**：建立前後端圖標庫版本同步檢查
4. **擴展圖標庫**：支援更多圖標庫（如Font Awesome等）

## 實施優勢

### 使用 `lucide-static` 的關鍵優勢
1. **官方支援**：使用 Lucide 官方提供的 Node.js 套件
2. **版本一致性**：確保前後端使用相同版本的圖標
3. **自動更新**：新圖標自動可用，無需手動維護
4. **完整功能**：支援所有 Lucide 圖標和屬性
5. **穩定可靠**：經過廣泛測試和社群驗證

這個計畫將確保後端預覽圖生成能夠完美渲染icon元件，達到與前端完全一致的視覺效果，同時利用官方套件的穩定性和可維護性。
