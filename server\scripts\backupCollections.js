const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');

// MongoDB 連接信息
const uri = 'mongodb://127.0.0.1:27017';
const dbName = 'resourceManagement';

// 備份目錄
const backupDir = path.join(__dirname, '../backups');

async function backupCollections() {
  let client;
  
  try {
    console.log('開始備份集合...');
    
    // 確保備份目錄存在
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
      console.log(`創建備份目錄: ${backupDir}`);
    }
    
    // 連接數據庫
    client = new MongoClient(uri);
    await client.connect();
    console.log('MongoDB 連接成功');
    
    const db = client.db(dbName);
    
    // 備份 stores 集合
    const stores = await db.collection('stores').find().toArray();
    const storesBackupPath = path.join(backupDir, `stores_backup_${new Date().toISOString().replace(/:/g, '-')}.json`);
    fs.writeFileSync(storesBackupPath, JSON.stringify(stores, null, 2));
    console.log(`stores 集合備份完成: ${storesBackupPath}`);
    
    // 備份 storeData 集合
    const storeData = await db.collection('storeData').find().toArray();
    const storeDataBackupPath = path.join(backupDir, `storeData_backup_${new Date().toISOString().replace(/:/g, '-')}.json`);
    fs.writeFileSync(storeDataBackupPath, JSON.stringify(storeData, null, 2));
    console.log(`storeData 集合備份完成: ${storeDataBackupPath}`);
    
    console.log('備份完成');
    
  } catch (error) {
    console.error('備份失敗:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('MongoDB 連接已關閉');
    }
  }
}

// 執行備份
backupCollections();
