# BWRY 顏色映射修正說明

## 問題描述

在 EPD 格式轉換中，BWRY (Black & White & Red & Yellow) 顏色類型的紅色和黃色在實際設備上顯示相反了。

## 原始映射 (錯誤)

```
0 = 黑色 (RGB: 0,0,0)
1 = 白色 (RGB: 255,255,255)  
2 = 紅色 (RGB: 255,0,0)     ❌ 在設備上顯示為黃色
3 = 黃色 (RGB: 255,255,0)   ❌ 在設備上顯示為紅色
```

## 修正後映射 (正確)

```
0 = 黑色 (RGB: 0,0,0)
1 = 白色 (RGB: 255,255,255)
2 = 黃色 (RGB: 255,255,0)   ✅ 修正：原本是3，現在改為2
3 = 紅色 (RGB: 255,0,0)     ✅ 修正：原本是2，現在改為3
```

## 修正的文件

### 前端文件
- `src/utils/epdConversion/converters/BWRYConverter.ts`
  - 修正 `analyzeColorBWRY()` 方法中的顏色數值映射

### 後端文件  
- `server/utils/epdConversion.js`
  - 修正 `BWRYConverter.analyzeColorBWRY()` 方法中的顏色數值映射

### 文檔文件
- `plan/epd-rawdata-conversion-plan.md`
- `plan/epd-conversion-technical-spec.md`

## 修正內容

在所有相關文件中，將 BWRY 顏色映射從：
```javascript
// 舊的錯誤映射
if (matchedColor.r === 255 && matchedColor.g === 0 && matchedColor.b === 0) {
  return 2; // 紅色 - 錯誤：在設備上顯示為黃色
} else if (matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 0) {
  return 3; // 黃色 - 錯誤：在設備上顯示為紅色
}
```

修正為：
```javascript
// 新的正確映射
if (matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 0) {
  return 2; // 黃色 - 正確：在設備上顯示為黃色
} else if (matchedColor.r === 255 && matchedColor.g === 0 && matchedColor.b === 0) {
  return 3; // 紅色 - 正確：在設備上顯示為紅色
}
```

## 影響範圍

- ✅ 前端 EPD 轉換模組
- ✅ 後端 EPD 轉換模組  
- ✅ 相關技術文檔
- ⚠️ 需要重新測試 BWRY 設備的圖像顯示效果

## 測試建議

1. 使用 BWRY 顏色類型的模板創建包含紅色和黃色元素的預覽圖
2. 將預覽圖發送到 3.7" BWRY 設備進行測試
3. 確認設備上顯示的紅色和黃色與預期一致

## 注意事項

- 此修正只影響 BWRY 顏色類型，不影響 BWR 或其他顏色類型
- 修正後的映射應該與實際 EPD 硬體的顏色顯示一致
- 如果仍有顏色顯示問題，可能需要進一步檢查硬體規格或驅動程式

## 修正日期

2024年12月 - 根據實際設備測試反饋進行修正
