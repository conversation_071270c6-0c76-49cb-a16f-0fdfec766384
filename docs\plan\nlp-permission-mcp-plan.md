# 自然語言權限管理 MCP 伺服器實施計劃

## 1. 概述

本計劃旨在設計和實施一個基於 MCP (Model Context Protocol) 的伺服器，該伺服器將整合 Google 大型語言模型 (LLM)，允許使用者透過自然語言介面（聊天機器人）來管理系統中的角色、人員和權限設置。

## 2. 目標

*   建立一個 MCP 伺服器，提供透過自然語言管理權限的工具。
*   整合 Google LLM 以解析自然語言指令。
*   將 MCP 伺服器與現有後端 API 對接，執行實際的權限管理操作。
*   在前端應用程式中整合一個聊天機器人介面，使用者可透過此介面與 MCP 伺服器互動。
*   確保整個流程的安全性和可靠性。

## 3. 架構設計

```mermaid
graph LR
    A[使用者] -- 自然語言輸入 --> B(前端聊天機器人);
    B -- 呼叫 MCP 工具 --> C{MCP 伺服器};
    C -- 傳送文字到 LLM --> D[Google LLM];
    D -- 解析意圖/實體 --> C;
    C -- 呼叫後端 API --> E(後端 API - User/Role/Permission);
    E -- 操作 --> F[(資料庫)];
    E -- 回應 --> C;
    C -- 回應 --> B;
    B -- 顯示結果 --> A;
```

## 4. MCP 伺服器設計

### 4.1. 工具 (Tools)

*   **`manage_permission(natural_language_query: string)`**:
    *   **描述**: 接收使用者的自然語言查詢，解析意圖並執行相應的權限管理操作（新增/修改/刪除使用者、角色、權限指派）。
    *   **輸入**:
        *   `natural_language_query`: 使用者輸入的自然語言字串 (例如："幫我新增一個叫做 '張三' 的使用者，並給他 '編輯者' 的角色")。
    *   **處理流程**:
        1.  接收 `natural_language_query`。
        2.  將查詢傳送給 Google LLM 進行意圖識別和實體提取 (使用者名稱、角色名稱、權限名稱、操作類型等)。
        3.  根據 LLM 的解析結果，判斷需要執行的操作 (例如：`createUser`, `assignRole`, `grantPermission`)。
        4.  呼叫相應的後端 API 端點，並傳遞必要的參數。
        5.  處理後端 API 的回應。
        6.  產生一個對使用者友善的回應訊息。
    *   **輸出**: 操作結果的文字描述 (例如："已成功新增使用者 '張三' 並指派 '編輯者' 角色。" 或 "操作失敗：找不到角色 '未知角色'")。
    *   **依賴**: Google LLM API, 後端權限管理 API。

### 4.2. 資源 (Resources)

*   可能不需要特定的資源，主要依賴工具與外部服務（LLM, 後端 API）的互動。

### 4.3. MCP 伺服器實作技術

*   建議使用 Node.js 和 `@modelcontextprotocol/server-nodejs` 套件來建立 MCP 伺服器。

## 5. 前端整合

*   **聊天機器人按鈕**: 在權限管理相關頁面（例如 `src/components/permission/PermissionManagementPage.tsx` 或 `src/components/permission/UserManagement.tsx`）的右下角或合適位置添加一個浮動按鈕或圖示，點擊後展開聊天視窗。
*   **聊天介面**:
    *   使用現有的 UI 元件庫（例如 Shadcn UI，如果專案有使用的話）或專門的聊天 UI 庫來建立介面。
    *   包含一個輸入框供使用者輸入自然語言，以及一個顯示對話歷史的區域。
*   **MCP 呼叫**:
    *   當使用者送出訊息時，前端 JavaScript/TypeScript 程式碼將呼叫 `use_mcp_tool`。
    *   `server_name` 將是新建立的 MCP 伺服器的名稱。
    *   `tool_name` 將是 `manage_permission`。
    *   `arguments` 將包含使用者輸入的 `natural_language_query`。
*   **結果顯示**: 將 MCP 工具的回應顯示在聊天介面中。

## 6. Google LLM 整合

*   **模型選擇**: 根據需求選擇合適的 Google LLM 模型（例如 Gemini 系列），考慮其理解能力、回應速度和成本。
*   **API 金鑰管理**: 安全地儲存和管理 Google Cloud API 金鑰。建議將金鑰儲存在環境變數或安全的配置管理系統中，而不是直接寫在程式碼裡。
*   **Prompt 設計**:
    *   設計清晰、結構化的 Prompt，引導 LLM 準確地從使用者輸入中提取意圖和實體。
    *   範例 Prompt 結構：
        ```
        你是一個權限管理助手。請分析以下使用者請求，並提取操作類型、使用者名稱、角色名稱和權限名稱。

        使用者請求："{natural_language_query}"

        請以 JSON 格式回傳分析結果，包含以下欄位：
        - "action": 操作類型 (例如 "createUser", "assignRole", "revokePermission", "getInfo")
        - "userName": 使用者名稱 (如果適用)
        - "roleName": 角色名稱 (如果適用)
        - "permissionName": 權限名稱 (如果適用)

        如果無法確定某些資訊，請將對應欄位設為 null。
        ```
*   **輸出處理**:
    *   處理 LLM 可能回傳的 JSON 格式錯誤或不完整的情況。
    *   建立驗證邏輯，確保從 LLM 提取的資訊是有效的（例如，檢查角色名稱是否存在於系統中）。
    *   如果 LLM 無法確定意圖或缺少必要資訊，MCP 工具應回傳一個要求使用者提供更多資訊的回應。

## 7. 後端 API 互動

*   **檢視現有 API**: 檢查 `server/routes/userApi.js`, `server/routes/roleApi.js`, `server/routes/permissionApi.js` 中的端點是否滿足 MCP 工具的需求。
    *   `POST /api/users`: 新增使用者
    *   `PUT /api/users/:userId/roles`: 指派角色給使用者 (可能需要調整或新增)
    *   `DELETE /api/users/:userId/roles/:roleId`: 移除使用者的角色 (可能需要調整或新增)
    *   `GET /api/roles`: 取得角色列表 (用於驗證 LLM 提取的角色名稱)
    *   `GET /api/permissions`: 取得權限列表 (用於驗證 LLM 提取的權限名稱)
    *   其他可能需要的 API...
*   **API 修改/新增**: 如果現有 API 不足，需要規劃和實作新的端點或修改現有端點以支援 MCP 工具所需的操作。確保 API 有適當的錯誤處理和回應格式。
*   **身份驗證**: MCP 伺服器呼叫後端 API 時，需要傳遞有效的身份驗證憑證（例如 JWT Token），確保操作的合法性。這需要在 MCP 工具的實作中處理。

## 8. 安全性考量

*   **MCP 伺服器安全**:
    *   限制對 MCP 伺服器的存取，例如透過 IP 白名單或 API 金鑰。
    *   對來自前端的請求進行驗證。
*   **輸入清理**: 對使用者輸入的自然語言進行清理，防止 Prompt Injection 攻擊。
*   **LLM 使用安全**:
    *   監控 LLM API 的使用情況，防止濫用。
    *   避免將敏感資訊直接傳遞給 LLM。
*   **後端 API 安全**: 確保後端 API 有健全的身份驗證、授權和輸入驗證機制。
*   **操作確認**: 對於敏感操作（例如刪除使用者、授予管理員權限），考慮在執行前要求使用者再次確認。

## 9. 實施步驟

1.  **環境設定 (1-2 天)**:
    *   設定 Google Cloud 專案並啟用所需的 LLM API。
    *   取得 API 金鑰。
    *   建立新的 Node.js 專案用於 MCP 伺服器，或在現有後端專案中整合。
2.  **MCP 伺服器基礎建設 (2-3 天)**:
    *   使用 `@modelcontextprotocol/server-nodejs` 建立 MCP 伺服器骨架。
    *   定義 `manage_permission` 工具的輸入/輸出結構。
3.  **LLM 整合與 Prompt 工程 (3-5 天)**:
    *   實作呼叫 Google LLM API 的邏輯。
    *   設計和測試 Prompt，反覆運算以提高解析準確性。
    *   實作 LLM 回應的解析和驗證邏輯。
4.  **後端 API 整合 (2-4 天)**:
    *   檢視並根據需要修改/新增後端 API 端點。
    *   在 MCP 工具中實作呼叫後端 API 的邏輯，包含身份驗證處理。
5.  **前端聊天機器人介面開發 (3-5 天)**:
    *   設計和實作聊天機器人 UI。
    *   實作呼叫 MCP 工具的前端邏輯。
    *   處理並顯示 MCP 工具的回應。
6.  **端到端測試與除錯 (3-5 天)**:
    *   測試整個流程，從前端輸入到後端資料變更。
    *   測試各種自然語言輸入的場景和邊界條件。
    *   修復發現的錯誤。
7.  **安全性加固與部署 (2-3 天)**:
    *   實施安全性措施。
    *   部署 MCP 伺服器。
    *   部署前端和後端更新。

**(總計約 16-27 工作天)**

## 10. 測試計劃

*   **單元測試**:
    *   測試 MCP 工具中解析 LLM 回應的邏輯。
    *   測試呼叫後端 API 的邏輯。
*   **整合測試**:
    *   測試 MCP 工具與 Google LLM 的整合。
    *   測試 MCP 工具與後端 API 的整合。
*   **前端測試**:
    *   測試聊天機器人 UI 的互動。
    *   測試前端呼叫 MCP 工具的邏輯。
*   **端到端測試**:
    *   模擬使用者透過聊天機器人輸入各種指令，驗證整個流程是否按預期工作，以及資料庫中的資料是否正確更新。
    *   測試場景範例：
        *   新增使用者並指派角色。
        *   變更現有使用者的角色。
        *   查詢某個使用者的權限。
        *   輸入模糊或不完整的指令，驗證系統的回應。
        *   輸入包含潛在惡意內容的指令。
*   **效能測試**: 評估 LLM API 和後端 API 的回應時間，確保使用者體驗流暢。
*   **安全性測試**: 進行 Prompt Injection 測試和其他相關的安全測試。

## 11. 未來擴展

*   支援更複雜的權限查詢。
*   支援批次操作。
*   整合其他自然語言處理功能，例如對話管理。
*   支援多語言。
