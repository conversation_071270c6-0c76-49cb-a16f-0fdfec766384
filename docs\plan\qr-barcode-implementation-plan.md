# QR Code 和條碼元件實作計劃

## 專案概述

實作 Template Editor 中的 QR Code 和條碼元件，使其與現有元件控制方式保持一致，支援顏色變更、不同模式選擇，並確保預覽圖輸出時轉換不失真。

## 現狀分析

### 目前實作狀況
1. **基礎架構已存在**：
   - `TemplateElement` 類型已包含 `qr-code` 和 `barcode`
   - ToolsPanel 已有對應的工具按鈕
   - 基本的佔位符渲染已實作（前端和後端）

2. **缺少的功能**：
   - QR Code 和條碼的屬性面板
   - 真實的 QR Code 和條碼生成
   - 模式選擇（不同條碼類型）
   - 顏色控制整合
   - 資料綁定支援

## 實作計劃

### 階段一：擴展資料結構

#### 1.1 更新 TemplateElement 介面
**檔案**: `src/types.ts`

新增屬性：
```typescript
export interface TemplateElement {
  // ... 現有屬性
  
  // QR Code 和條碼專用屬性
  qrCodeType?: 'qrcode' | 'datamatrix' | 'pdf417';  // QR Code 類型
  barcodeType?: 'code128' | 'ean13' | 'upc-a' | 'code39' | 'code93';  // 條碼類型
  codeContent?: string;  // 編碼內容（靜態內容或預設值）
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';  // QR Code 錯誤修正等級
  quietZone?: number;  // 靜區大小
  moduleSize?: number;  // 模組大小（影響密度）
}
```

#### 1.2 更新資料欄位類型
**檔案**: `src/types.ts`

確保 `DataFieldType` 包含所有支援的條碼類型：
```typescript
export enum DataFieldType {
  // ... 現有類型
  BARCODE_CODE39 = "barcode-code39",
  BARCODE_CODE93 = "barcode-code93", 
  QR_CODE_DATAMATRIX = "QR code-datamatrix",
  QR_CODE_PDF417 = "QR code-pdf417"
}
```

### 階段二：屬性面板實作

#### 2.1 建立 QR Code 屬性面板
**檔案**: `src/components/editor/properties/QRCodeProperties.tsx`

功能需求：
- QR Code 類型選擇（QR Code、Data Matrix、PDF417）
- 錯誤修正等級選擇
- 靜區大小調整
- 模組大小調整
- 內容設定（靜態內容輸入）
- 顏色控制（前景色、背景色）
- 資料綁定選項

#### 2.2 建立條碼屬性面板
**檔案**: `src/components/editor/properties/BarcodeProperties.tsx`

功能需求：
- 條碼類型選擇（Code128、EAN13、UPC-A、Code39、Code93）
- 高度調整
- 寬度比例調整
- 顯示文字選項
- 靜區大小調整
- 內容設定（靜態內容輸入）
- 顏色控制（前景色、背景色）
- 資料綁定選項

#### 2.3 整合到元素屬性面板
**檔案**: `src/components/editor/ElementPropertiesPanel.tsx`

新增條件渲染：
```typescript
{selectedElement?.type === 'qr-code' && (
  <>
    <PropertySeparator />
    <QRCodeProperties
      element={selectedElement}
      updateElement={updateSelectedElement}
      colorType={colorType}
    />
  </>
)}

{selectedElement?.type === 'barcode' && (
  <>
    <PropertySeparator />
    <BarcodeProperties
      element={selectedElement}
      updateElement={updateSelectedElement}
      colorType={colorType}
    />
  </>
)}
```

### 階段三：前端顯示實作（佔位符模式）

#### 3.1 建立 QR Code 顯示元件
**檔案**: `src/components/editor/elements/QRCodeRenderer.tsx`

功能：
- 顯示帶有 QR Code 圖示的佔位符
- 顯示當前設定的類型和內容預覽
- 支援顏色預覽（邊框和背景色）
- 響應式尺寸調整
- 顯示設定摘要（類型、錯誤修正等級等）

#### 3.2 建立條碼顯示元件
**檔案**: `src/components/editor/elements/BarcodeRenderer.tsx`

功能：
- 顯示帶有條碼圖示的佔位符
- 顯示當前設定的條碼類型
- 支援顏色預覽（邊框和背景色）
- 顯示內容預覽
- 顯示設定摘要（類型、高度等）

#### 3.3 更新 ElementRenderer
**檔案**: `src/components/editor/elements/ElementRenderer.tsx`

新增 QR Code 和條碼的顯示邏輯：
```typescript
{element.type === 'qr-code' && (
  <QRCodeRenderer
    element={element}
    zoom={zoom}
  />
)}

{element.type === 'barcode' && (
  <BarcodeRenderer
    element={element}
    zoom={zoom}
  />
)}
```

### 階段四：後端統一渲染實作

#### 4.1 安裝後端套件
```bash
cd server
npm install qrcode jsbarcode canvas
```

#### 4.2 建立後端 QR Code 渲染器
**檔案**: `server/utils/qrCodeRenderer.js`

功能：
- 使用 Node.js 版本的 qrcode 套件
- 支援所有 QR Code 類型（QR Code、Data Matrix、PDF417）
- 支援 Canvas 渲染到 DOM 元素
- 完整的參數控制（顏色、錯誤修正、尺寸等）
- 資料綁定內容處理

#### 4.3 建立後端條碼渲染器
**檔案**: `server/utils/barcodeRenderer.js`

功能：
- 使用 Node.js 版本的 jsbarcode 套件
- 支援所有條碼類型（Code128、EAN13、UPC-A、Code39、Code93）
- 支援 Canvas 渲染到 DOM 元素
- 完整的參數控制（顏色、高度、寬度等）
- 資料綁定內容處理

### 階段五：預覽系統整合

#### 5.1 更新預覽服務
**檔案**: `server/services/previewService.js`

替換佔位符邏輯：
```javascript
} else if (element.type === 'qr-code') {
  const { renderQRCode } = require('../utils/qrCodeRenderer');
  await renderQRCode(elementDiv, element, sampleDataByIndex, dataFields);
} else if (element.type === 'barcode') {
  const { renderBarcode } = require('../utils/barcodeRenderer');
  await renderBarcode(elementDiv, element, sampleDataByIndex, dataFields);
}
```

#### 5.2 建立前端預覽 API 服務
**檔案**: `src/utils/api/codePreviewApi.ts`

功能：
- 呼叫後端 API 生成 QR Code/條碼預覽
- 快取機制避免重複請求
- 錯誤處理和重試機制
- 支援即時預覽更新

#### 5.3 更新 PreviewComponent
**檔案**: `src/components/PreviewComponent.tsx`

修改策略：
- QR Code 和條碼元素改為呼叫後端 API
- 其他元素維持前端渲染
- 統一的錯誤處理
- 載入狀態顯示

### 階段六：資料綁定系統整合

#### 6.1 擴展資料綁定核心支援
**檔案**: `src/utils/dataBinding/bindingCore.ts`

更新支援的元件類型：
```typescript
private supportedElementTypes: string[] = [
  'text',
  'multiline-text',
  'qr-code',      // 新增
  'barcode'       // 新增
];
```

更新支援的欄位類型：
```typescript
private supportedFieldTypes: string[] = [
  DataFieldType.TEXT,
  DataFieldType.NUMBER,
  DataFieldType.UNIQUE_IDENTIFIER,
  // QR Code 類型
  DataFieldType.QR_CODE,
  DataFieldType.QR_CODE_DATAMATRIX,    // 新增
  DataFieldType.QR_CODE_PDF417,        // 新增
  // 條碼類型
  DataFieldType.BARCODE_CODE128,
  DataFieldType.BARCODE_EAN13,
  DataFieldType.BARCODE_UPC_A,
  DataFieldType.BARCODE_CODE39,        // 新增
  DataFieldType.BARCODE_CODE93         // 新增
];
```

#### 6.2 建立 QR Code 綁定處理器
**檔案**: `src/utils/dataBinding/qrCodeBinding.ts`

功能：
```typescript
export class QRCodeBinding {
  /**
   * 獲取 QR Code 元件可綁定的資料欄位
   * 根據 QR Code 類型過濾適用的欄位
   */
  public static getBindableFields(fields: DataField[], qrCodeType?: string): DataField[] {
    const baseTypes = [
      DataFieldType.TEXT,
      DataFieldType.UNIQUE_IDENTIFIER,
      DataFieldType.QR_CODE
    ];

    // 根據 QR Code 類型添加特定支援
    const supportedTypes = [...baseTypes];
    if (qrCodeType === 'datamatrix') {
      supportedTypes.push(DataFieldType.QR_CODE_DATAMATRIX);
    } else if (qrCodeType === 'pdf417') {
      supportedTypes.push(DataFieldType.QR_CODE_PDF417);
    }

    return fields.filter(field => supportedTypes.includes(field.type as DataFieldType));
  }

  /**
   * 處理 QR Code 元件的綁定和內容驗證
   */
  public static processBinding(
    element: TemplateElement,
    fields: DataField[],
    sampleData?: Record<string, any>
  ): TemplateElement {
    // 綁定處理邏輯
    // 內容格式驗證
    // 錯誤處理
  }

  /**
   * 驗證 QR Code 內容格式
   */
  public static validateContent(content: string, qrCodeType: string): {
    isValid: boolean;
    error?: string;
    sanitizedContent?: string;
  } {
    // 根據 QR Code 類型進行格式驗證
    // 長度限制檢查
    // 字符集驗證
  }
}
```

#### 6.3 建立條碼綁定處理器
**檔案**: `src/utils/dataBinding/barcodeBinding.ts`

功能：
```typescript
export class BarcodeBinding {
  /**
   * 獲取條碼元件可綁定的資料欄位
   * 根據條碼類型過濾適用的欄位
   */
  public static getBindableFields(fields: DataField[], barcodeType?: string): DataField[] {
    const baseTypes = [
      DataFieldType.TEXT,
      DataFieldType.NUMBER,
      DataFieldType.UNIQUE_IDENTIFIER
    ];

    // 根據條碼類型添加特定支援
    const supportedTypes = [...baseTypes];
    switch (barcodeType) {
      case 'code128':
        supportedTypes.push(DataFieldType.BARCODE_CODE128);
        break;
      case 'ean13':
        supportedTypes.push(DataFieldType.BARCODE_EAN13);
        break;
      case 'upc-a':
        supportedTypes.push(DataFieldType.BARCODE_UPC_A);
        break;
      case 'code39':
        supportedTypes.push(DataFieldType.BARCODE_CODE39);
        break;
      case 'code93':
        supportedTypes.push(DataFieldType.BARCODE_CODE93);
        break;
    }

    return fields.filter(field => supportedTypes.includes(field.type as DataFieldType));
  }

  /**
   * 驗證條碼內容格式
   */
  public static validateContent(content: string, barcodeType: string): {
    isValid: boolean;
    error?: string;
    sanitizedContent?: string;
  } {
    // 根據條碼類型進行格式驗證
    // 長度限制檢查
    // 字符集驗證（如 Code39 只支援特定字符）
    // 校驗碼計算
  }
}
```

#### 6.4 更新元件綁定管理器
**檔案**: `src/utils/dataBinding/componentBindingManager.ts`

註冊新的綁定處理器：
```typescript
// 在建構函數中註冊
this.bindingHandlers['qr-code'] = QRCodeBinding;
this.bindingHandlers['barcode'] = BarcodeBinding;
```

#### 6.5 資料欄位類型擴展
**檔案**: `src/types.ts`

新增缺少的資料欄位類型：
```typescript
export enum DataFieldType {
  // ... 現有類型
  BARCODE_CODE39 = "barcode-code39",
  BARCODE_CODE93 = "barcode-code93",
  QR_CODE_DATAMATRIX = "QR code-datamatrix",
  QR_CODE_PDF417 = "QR code-pdf417"
}
```

#### 6.6 屬性面板綁定 UI 整合
**檔案**: `src/components/editor/properties/QRCodeProperties.tsx` 和 `BarcodeProperties.tsx`

功能需求：
- **智能欄位過濾**: 根據選擇的 QR Code/條碼類型自動過濾可用欄位
- **綁定模式切換**: 靜態內容 vs 動態綁定
- **即時驗證**: 輸入內容格式驗證和錯誤提示
- **預覽更新**: 綁定變更時即時更新預覽
- **相容性檢查**: 欄位類型與 QR Code/條碼類型的相容性檢查

#### 6.7 後端資料綁定處理
**檔案**: `server/utils/qrCodeRenderer.js` 和 `server/utils/barcodeRenderer.js`

資料綁定處理流程：
```javascript
/**
 * 處理 QR Code/條碼的資料綁定
 * @param {Object} element - 元素配置
 * @param {Object} sampleDataByIndex - 按索引組織的範例數據
 * @param {Array} dataFields - 資料欄位定義
 * @returns {string} 處理後的內容
 */
function processDataBinding(element, sampleDataByIndex, dataFields) {
  // 1. 檢查是否有資料綁定
  if (!element.dataBinding || !element.dataBinding.fieldId) {
    return element.codeContent || ''; // 返回靜態內容
  }

  // 2. 獲取綁定的資料欄位
  const field = dataFields.find(f => f.id === element.dataBinding.fieldId);
  if (!field) {
    console.warn(`找不到欄位 ${element.dataBinding.fieldId}`);
    return element.codeContent || 'ERROR: Field not found';
  }

  // 3. 獲取數據值
  const dataIndex = element.dataBinding.dataIndex;
  const sampleData = sampleDataByIndex[dataIndex];
  if (!sampleData || !sampleData[field.id]) {
    console.warn(`找不到數據 ${field.id} at index ${dataIndex}`);
    return field.defaultValue || element.codeContent || 'No Data';
  }

  // 4. 格式驗證和清理
  let content = sampleData[field.id];

  // 根據元素類型進行驗證
  if (element.type === 'qr-code') {
    const validation = validateQRCodeContent(content, element.qrCodeType);
    if (!validation.isValid) {
      console.error(`QR Code 內容驗證失敗: ${validation.error}`);
      return `ERROR: ${validation.error}`;
    }
    content = validation.sanitizedContent || content;
  } else if (element.type === 'barcode') {
    const validation = validateBarcodeContent(content, element.barcodeType);
    if (!validation.isValid) {
      console.error(`條碼內容驗證失敗: ${validation.error}`);
      return `ERROR: ${validation.error}`;
    }
    content = validation.sanitizedContent || content;
  }

  // 5. 添加前綴（如果需要）
  if (element.dataBinding.displayOptions?.showPrefix && field.prefix) {
    content = field.prefix + content;
  }

  return content;
}
```

#### 6.8 內容驗證規則
**檔案**: `server/utils/codeValidation.js`

建立統一的驗證規則：
```javascript
// QR Code 驗證規則
const QR_CODE_LIMITS = {
  qrcode: { maxLength: 4296, charset: 'all' },
  datamatrix: { maxLength: 3116, charset: 'ascii' },
  pdf417: { maxLength: 2710, charset: 'all' }
};

// 條碼驗證規則
const BARCODE_LIMITS = {
  code128: { maxLength: 80, charset: 'ascii' },
  ean13: { length: 13, charset: 'numeric' },
  'upc-a': { length: 12, charset: 'numeric' },
  code39: { maxLength: 43, charset: 'code39' },
  code93: { maxLength: 47, charset: 'ascii' }
};
```

### 階段七：顏色限制整合

#### 7.1 整合 RestrictedColorInput
確保 QR Code 和條碼屬性面板使用 `RestrictedColorInput` 元件，支援：
- Gray16 (16 灰階)
- BWR (黑白紅)
- BWRY (黑白紅黃)

#### 7.2 後端顏色轉換優化
**檔案**: `server/utils/qrCodeRenderer.js` 和 `server/utils/barcodeRenderer.js`

確保生成的 QR Code 和條碼在顏色轉換時不失真：
- 直接使用模板的 colorType 限制色彩
- 使用高對比度設定
- 避免中間色調
- 確保可讀性
- 與現有顏色轉換系統整合

### 階段八：API 和快取優化

#### 8.1 建立專用 API 端點
**檔案**: `server/routes/codePreview.js`

功能：
- `/api/code-preview/qr` - QR Code 預覽生成
- `/api/code-preview/barcode` - 條碼預覽生成
- 參數驗證和錯誤處理
- 回應快取標頭

#### 8.2 前端快取機制
**檔案**: `src/utils/api/codePreviewApi.ts`

功能：
- 基於參數的快取 key
- 記憶體快取避免重複請求
- 快取失效機制
- 載入狀態管理

### 階段九：測試與優化

#### 9.1 功能測試
- 各種 QR Code 類型生成測試
- 各種條碼類型生成測試
- 顏色限制測試
- 資料綁定測試
- 預覽圖生成測試
- API 回應時間測試

#### 9.2 效能優化
- 後端 Canvas 渲染效能
- 大尺寸 QR Code/條碼處理
- API 快取效果驗證
- 記憶體使用優化

#### 9.3 錯誤處理
- 無效內容處理
- 尺寸限制處理
- 格式錯誤處理
- 網路錯誤處理
- 後端服務不可用處理

## 技術考量

### 1. 統一後端渲染的優勢
- **完全一致性**: 前端預覽和最終輸出完全相同
- **顏色轉換精確**: 直接在生成時應用顏色限制
- **效能集中**: 避免前端重複計算
- **維護簡化**: 只需維護一套渲染邏輯

### 2. 套件選擇
- **qrcode**: 成熟的 QR Code 生成套件，Node.js 原生支援
- **jsbarcode**: 功能完整的條碼生成套件，支援 Canvas 輸出
- **node-canvas**: 後端 Canvas API，與瀏覽器 API 相容

### 3. API 設計考量
- **專用端點**: 獨立的 QR Code/條碼生成 API
- **參數驗證**: 完整的輸入驗證和錯誤回應
- **快取策略**: 基於參數的智能快取
- **回應格式**: 統一的 JSON 回應格式

### 4. 前端整合策略
- **混合渲染**: QR Code/條碼使用 API，其他元素前端渲染
- **載入狀態**: 優雅的載入和錯誤狀態處理
- **快取機制**: 前端快取避免重複 API 呼叫
- **降級處理**: API 失敗時顯示佔位符

## 預期成果

1. **完整的 QR Code 元件**：
   - 支援多種 QR Code 類型
   - 完整的屬性控制
   - 顏色限制整合
   - 資料綁定支援

2. **完整的條碼元件**：
   - 支援多種條碼格式
   - 完整的屬性控制
   - 顏色限制整合
   - 資料綁定支援

3. **無失真預覽**：
   - 高品質的預覽圖生成
   - 正確的顏色轉換
   - 前後端一致性

4. **良好的使用者體驗**：
   - 直觀的屬性面板
   - 即時預覽更新
   - 錯誤提示和驗證

## 主要檔案結構

### 前端檔案
1. **屬性面板**：
   - `src/components/editor/properties/QRCodeProperties.tsx`
   - `src/components/editor/properties/BarcodeProperties.tsx`

2. **顯示元件**：
   - `src/components/editor/elements/QRCodeRenderer.tsx` - 佔位符顯示
   - `src/components/editor/elements/BarcodeRenderer.tsx` - 佔位符顯示

3. **資料綁定處理器**：
   - `src/utils/dataBinding/qrCodeBinding.ts` - QR Code 綁定邏輯
   - `src/utils/dataBinding/barcodeBinding.ts` - 條碼綁定邏輯

4. **API 服務**：
   - `src/utils/api/codePreviewApi.ts` - 前端 API 服務

### 後端檔案
1. **渲染器**：
   - `server/utils/qrCodeRenderer.js` - 真實 QR Code 生成
   - `server/utils/barcodeRenderer.js` - 真實條碼生成

2. **驗證工具**：
   - `server/utils/codeValidation.js` - 內容格式驗證

3. **API 端點**：
   - `server/routes/codePreview.js` - 專用 API 端點

## 實作順序建議

1. 階段一：資料結構擴展
2. 階段二：屬性面板實作
3. 階段三：前端顯示實作（佔位符模式）
4. 階段四：後端統一渲染實作
5. 階段五：預覽系統整合
6. 階段六：資料綁定整合
7. 階段七：顏色限制整合
8. 階段八：API 和快取優化
9. 階段九：測試與優化

每個階段完成後進行測試，確保功能正常再進入下一階段。

## 資料綁定詳細規劃

### 🔗 綁定流程設計

#### 1. 欄位類型自動過濾
```typescript
// 根據 QR Code/條碼類型智能過濾可用欄位
const getAvailableFields = (elementType: string, codeType?: string) => {
  if (elementType === 'qr-code') {
    return QRCodeBinding.getBindableFields(allFields, codeType);
  } else if (elementType === 'barcode') {
    return BarcodeBinding.getBindableFields(allFields, codeType);
  }
  return [];
};
```

#### 2. 內容驗證機制
- **即時驗證**: 用戶輸入時即時檢查格式
- **類型相容性**: 確保欄位類型與 QR Code/條碼類型相容
- **長度限制**: 根據不同類型設定最大長度
- **字符集檢查**: 驗證字符是否符合規範

#### 3. 錯誤處理策略
- **降級顯示**: 無效內容時顯示錯誤訊息
- **預設值**: 使用欄位預設值或元素靜態內容
- **用戶提示**: 清楚的錯誤訊息和修正建議

### 📊 支援的資料欄位類型對應

| 元素類型 | 支援的欄位類型 | 說明 |
|---------|---------------|------|
| QR Code | TEXT, UNIQUE_IDENTIFIER, QR_CODE | 基本文字內容 |
| QR Code (DataMatrix) | QR_CODE_DATAMATRIX | 專用 DataMatrix 格式 |
| QR Code (PDF417) | QR_CODE_PDF417 | 專用 PDF417 格式 |
| 條碼 (Code128) | TEXT, NUMBER, BARCODE_CODE128 | 支援文字和數字 |
| 條碼 (EAN13) | NUMBER, BARCODE_EAN13 | 僅數字，13位 |
| 條碼 (UPC-A) | NUMBER, BARCODE_UPC_A | 僅數字，12位 |
| 條碼 (Code39) | TEXT, BARCODE_CODE39 | 特定字符集 |
| 條碼 (Code93) | TEXT, BARCODE_CODE93 | ASCII 字符 |

### 🛡️ 驗證規則詳細

#### QR Code 驗證
- **QR Code**: 最大 4296 字符，支援所有字符
- **DataMatrix**: 最大 3116 字符，ASCII 字符集
- **PDF417**: 最大 2710 字符，支援所有字符

#### 條碼驗證
- **Code128**: 最大 80 字符，ASCII 字符集
- **EAN13**: 固定 13 位數字
- **UPC-A**: 固定 12 位數字
- **Code39**: 最大 43 字符，特定字符集 (A-Z, 0-9, 空格, -, ., $, /, +, %, *)
- **Code93**: 最大 47 字符，ASCII 字符集

## 核心優勢

✅ **完全一致性** - 所有 QR Code/條碼都由後端統一生成，確保前端預覽和最終輸出完全相同
✅ **顏色精確性** - 直接在生成時應用顏色限制，避免轉換失真
✅ **智能綁定** - 根據類型自動過濾可用欄位，避免不相容的綁定
✅ **格式驗證** - 完整的內容驗證機制，確保生成的 QR Code/條碼可讀
✅ **效能優化** - 前端快取 + 後端專業處理，最佳化使用者體驗
✅ **維護簡化** - 單一渲染邏輯，降低維護複雜度
