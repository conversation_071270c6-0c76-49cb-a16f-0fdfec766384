/**
 * 前端 QR Code 生成器
 * 使用 qrcode 套件直接在前端生成 QR Code
 */

import QRCode from 'qrcode';

/**
 * 將白色背景的圖片轉換為透明背景
 * @param dataUrl base64 圖片數據
 * @returns Promise<string> 透明背景的 base64 圖片數據
 */
async function convertWhiteBackgroundToTransparent(dataUrl: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('無法創建 Canvas 上下文'));
        return;
      }

      canvas.width = img.width;
      canvas.height = img.height;

      // 繪製圖片到 Canvas
      ctx.drawImage(img, 0, 0);

      // 獲取圖片數據
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // 將白色像素轉換為透明
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];

        // 檢查是否為白色或接近白色的像素（容忍度為 10）
        if (r > 245 && g > 245 && b > 245) {
          data[i + 3] = 0; // 設置 alpha 為 0（透明）
        }
      }

      // 將修改後的數據放回 Canvas
      ctx.putImageData(imageData, 0, 0);

      // 轉換為 data URL
      const transparentDataUrl = canvas.toDataURL('image/png');
      resolve(transparentDataUrl);
    };

    img.onerror = () => {
      reject(new Error('圖片載入失敗'));
    };

    img.src = dataUrl;
  });
}

export interface QRCodeOptions {
  content: string;
  qrCodeType?: 'qrcode' | 'datamatrix' | 'pdf417';
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
  quietZone?: number;
  moduleSize?: number;
  foregroundColor?: string;
  backgroundColor?: string;
  width?: number;
  height?: number;
}

/**
 * 驗證 QR Code 內容
 */
export function validateQRCodeContent(content: string, qrCodeType: string = 'qrcode') {
  if (!content || content.length === 0) {
    return { isValid: false, error: '內容不能為空' };
  }

  // 根據類型設定最大長度限制
  const maxLengths = {
    qrcode: 4296,
    datamatrix: 3116,
    pdf417: 2710
  };

  const maxLength = maxLengths[qrCodeType as keyof typeof maxLengths] || 4296;
  
  if (content.length > maxLength) {
    return { 
      isValid: false, 
      error: `內容長度超過限制 (${content.length}/${maxLength})` 
    };
  }

  return { isValid: true, sanitizedContent: content };
}

/**
 * 生成 QR Code 圖片
 * @param options QR Code 選項
 * @returns Promise<string> base64 圖片數據
 */
export async function generateQRCode(options: QRCodeOptions): Promise<string> {
  const {
    content,
    qrCodeType = 'qrcode',
    errorCorrectionLevel = 'M',
    quietZone = 4,
    backgroundColor = '#FFFFFF',
    width = 200,
    height = 200
  } = options;

  // QR Code 條碼顏色固定為黑色
  const foregroundColor = '#000000';

  // 處理透明背景：QR Code 套件不支援透明背景，將透明轉換為白色
  const processedBackgroundColor = backgroundColor === 'transparent' ? '#FFFFFF' : backgroundColor;

  // 驗證內容
  const validation = validateQRCodeContent(content, qrCodeType);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  // 目前只支援標準 QR Code，其他類型暫時使用相同邏輯
  // 未來可以擴展支援 Data Matrix 和 PDF417
  if (qrCodeType !== 'qrcode') {
    console.warn(`QR Code 類型 ${qrCodeType} 暫時使用標準 QR Code 生成`);
  }

  try {
    // 如果原始背景是透明，需要特殊處理
    if (backgroundColor === 'transparent') {
      // 先生成白色背景的 QR Code
      const qrOptions = {
        errorCorrectionLevel: errorCorrectionLevel,
        type: 'image/png' as const,
        quality: 1,
        margin: quietZone,
        color: {
          dark: foregroundColor,
          light: '#FFFFFF'
        },
        width: Math.max(width, 50),
        height: Math.max(height, 50)
      };

      // 生成 QR Code 並轉換為透明背景
      const dataUrl = await QRCode.toDataURL(validation.sanitizedContent || content, qrOptions);

      // 使用 Canvas 將白色背景轉換為透明
      return await convertWhiteBackgroundToTransparent(dataUrl);
    } else {
      // 正常生成有色背景的 QR Code
      const qrOptions = {
        errorCorrectionLevel: errorCorrectionLevel,
        type: 'image/png' as const,
        quality: 1,
        margin: quietZone,
        color: {
          dark: foregroundColor,
          light: processedBackgroundColor
        },
        width: Math.max(width, 50), // 確保最小尺寸
        height: Math.max(height, 50) // 確保最小尺寸
      };

      // 生成 QR Code 並返回 data URL
      const dataUrl = await QRCode.toDataURL(validation.sanitizedContent || content, qrOptions);
      return dataUrl;
    }

  } catch (error) {
    console.error('QR Code 生成失敗:', error);
    throw new Error(`QR Code 生成失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
}

/**
 * 為編輯器生成 QR Code 預覽圖片
 * 這個函數專門為編輯器優化，處理特殊情況
 */
export async function generateQRCodeForEditor(element: any): Promise<string> {
  // 獲取內容
  let content = element.codeContent;
  if (!content) {
    if (element.dataBinding?.fieldId) {
      content = 'QR Code 範例內容';
    } else {
      content = 'QR Code 內容';
    }
  }

  // 確保尺寸合理
  const width = Math.max(Math.round(element.width || 100), 50);
  const height = Math.max(Math.round(element.height || 100), 50);

  return generateQRCode({
    content,
    qrCodeType: element.qrCodeType || 'qrcode',
    errorCorrectionLevel: element.errorCorrectionLevel || 'M',
    quietZone: element.quietZone || 4,
    // foregroundColor 在 generateQRCode 函數中已固定為黑色，不需要傳遞
    backgroundColor: element.fillColor || '#FFFFFF',
    width,
    height
  });
}

/**
 * 獲取 QR Code 類型的顯示名稱
 */
export function getQRCodeTypeDisplay(qrCodeType?: string): string {
  const typeMap = {
    qrcode: 'QR Code',
    datamatrix: 'Data Matrix',
    pdf417: 'PDF417'
  };
  
  return typeMap[qrCodeType as keyof typeof typeMap] || 'QR Code';
}

/**
 * 獲取支援的 QR Code 類型列表
 */
export function getSupportedQRCodeTypes() {
  return [
    { value: 'qrcode', label: 'QR Code', maxLength: 4296 },
    { value: 'datamatrix', label: 'Data Matrix', maxLength: 3116 },
    { value: 'pdf417', label: 'PDF417', maxLength: 2710 }
  ];
}

/**
 * 獲取支援的錯誤修正等級
 */
export function getSupportedErrorCorrectionLevels() {
  return [
    { value: 'L', label: 'L (低)' },
    { value: 'M', label: 'M (中)' },
    { value: 'Q', label: 'Q (四分位)' },
    { value: 'H', label: 'H (高)' }
  ];
}
