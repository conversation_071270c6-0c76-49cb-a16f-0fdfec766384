/**
 * ImageData 處理工具函數
 */

/**
 * 從 Canvas 創建 ImageData
 */
export function createImageDataFromCanvas(
  canvas: HTMLCanvasElement,
  width?: number,
  height?: number
): ImageData {
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('無法獲取 Canvas 上下文');
  }

  const w = width || canvas.width;
  const h = height || canvas.height;

  return ctx.getImageData(0, 0, w, h);
}

/**
 * 從 base64 字符串創建 Canvas
 */
export function createCanvasFromImageData(imageDataStr: string): Promise<HTMLCanvasElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('無法獲取 Canvas 上下文'));
        return;
      }

      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      resolve(canvas);
    };

    img.onerror = () => {
      reject(new Error('無法載入圖片數據'));
    };

    img.src = imageDataStr;
  });
}

/**
 * 複製 ImageData
 */
export function cloneImageData(imageData: ImageData): ImageData {
  const newImageData = new ImageData(imageData.width, imageData.height);
  newImageData.data.set(imageData.data);
  return newImageData;
}

/**
 * 創建空白的 ImageData
 */
export function createBlankImageData(
  width: number,
  height: number,
  fillColor: { r: number; g: number; b: number; a: number } = { r: 255, g: 255, b: 255, a: 255 }
): ImageData {
  const imageData = new ImageData(width, height);
  const data = imageData.data;

  for (let i = 0; i < data.length; i += 4) {
    data[i] = fillColor.r;     // R
    data[i + 1] = fillColor.g; // G
    data[i + 2] = fillColor.b; // B
    data[i + 3] = fillColor.a; // A
  }

  return imageData;
}

/**
 * 調試用：將 ImageData 轉換為 Canvas 並返回 base64
 */
export function imageDataToBase64(imageData: ImageData): string {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('無法獲取 Canvas 上下文');
  }

  canvas.width = imageData.width;
  canvas.height = imageData.height;
  ctx.putImageData(imageData, 0, 0);

  return canvas.toDataURL();
}
