/**
 * Rawdata 壓縮模組類型定義
 */

// 支援的 rawdata 格式
const RAWDATA_FORMATS = {
  RAWDATA: 'rawdata',           // 未壓縮原始數據
  RUNLENDATA: 'runlendata',     // Run-Length Encoding 壓縮
  // 預留未來格式
  LZ4DATA: 'lz4data',           // LZ4 壓縮 (未實作)
  GZIPDATA: 'gzipdata'          // GZIP 壓縮 (未實作)
};

/**
 * 壓縮結果結構
 * @typedef {Object} CompressionResult
 * @property {boolean} success - 壓縮是否成功
 * @property {string} format - 使用的格式
 * @property {number} originalSize - 原始數據大小
 * @property {number} compressedSize - 壓縮後大小
 * @property {number} compressionRatio - 壓縮比 (compressedSize / originalSize)
 * @property {number} processingTime - 處理時間 (ms)
 * @property {Uint8Array|null} data - 壓縮後的數據
 * @property {string|null} error - 錯誤信息
 */
const CompressionResult = {
  success: false,
  format: '',
  originalSize: 0,
  compressedSize: 0,
  compressionRatio: 1.0,
  processingTime: 0,
  data: null,
  error: null
};

/**
 * 格式處理結果
 * @typedef {Object} FormatProcessingResult
 * @property {boolean} success - 處理是否成功
 * @property {string} format - 使用的格式
 * @property {number} originalSize - 原始數據大小
 * @property {number} processedSize - 處理後大小
 * @property {number} processingRatio - 處理比 (processedSize / originalSize)
 * @property {number} processingTime - 處理時間 (ms)
 * @property {Uint8Array} data - 處理後的數據
 * @property {string} reason - 處理原因或說明
 * @property {string|null} error - 錯誤信息
 */
const FormatProcessingResult = {
  success: false,
  format: '',
  originalSize: 0,
  processedSize: 0,
  processingRatio: 1.0,
  processingTime: 0,
  data: null,
  reason: '',
  error: null
};

/**
 * 驗證格式名稱是否有效
 * @param {string} format - 格式名稱
 * @returns {boolean} 是否有效
 */
function isValidFormat(format) {
  return Object.values(RAWDATA_FORMATS).includes(format);
}

/**
 * 獲取所有支援的格式
 * @returns {string[]} 格式名稱列表
 */
function getAllSupportedFormats() {
  return Object.values(RAWDATA_FORMATS);
}

/**
 * 獲取已實作的格式（排除預留格式）
 * @returns {string[]} 已實作的格式名稱列表
 */
function getImplementedFormats() {
  return [
    RAWDATA_FORMATS.RAWDATA,
    RAWDATA_FORMATS.RUNLENDATA
  ];
}

/**
 * 創建壓縮結果對象
 * @param {boolean} success - 是否成功
 * @param {string} format - 格式名稱
 * @param {Uint8Array} originalData - 原始數據
 * @param {Uint8Array|null} compressedData - 壓縮數據
 * @param {number} processingTime - 處理時間
 * @param {string|null} error - 錯誤信息
 * @returns {CompressionResult} 壓縮結果
 */
function createCompressionResult(success, format, originalData, compressedData = null, processingTime = 0, error = null) {
  return {
    success,
    format,
    originalSize: originalData ? originalData.length : 0,
    compressedSize: compressedData ? compressedData.length : 0,
    compressionRatio: compressedData && originalData ? 
      compressedData.length / originalData.length : 1.0,
    processingTime,
    data: compressedData,
    error
  };
}

/**
 * 創建格式處理結果對象
 * @param {boolean} success - 是否成功
 * @param {string} format - 格式名稱
 * @param {Uint8Array} originalData - 原始數據
 * @param {Uint8Array} processedData - 處理後數據
 * @param {number} processingTime - 處理時間
 * @param {string} reason - 處理原因
 * @param {string|null} error - 錯誤信息
 * @returns {FormatProcessingResult} 處理結果
 */
function createFormatProcessingResult(success, format, originalData, processedData, processingTime = 0, reason = '', error = null) {
  return {
    success,
    format,
    originalSize: originalData ? originalData.length : 0,
    processedSize: processedData ? processedData.length : 0,
    processingRatio: processedData && originalData ? 
      processedData.length / originalData.length : 1.0,
    processingTime,
    data: processedData,
    reason,
    error
  };
}

module.exports = {
  RAWDATA_FORMATS,
  CompressionResult,
  FormatProcessingResult,
  isValidFormat,
  getAllSupportedFormats,
  getImplementedFormats,
  createCompressionResult,
  createFormatProcessingResult
};
