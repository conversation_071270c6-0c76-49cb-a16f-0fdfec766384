// tests/helpers/mockFileApi.js
/**
 * 檔案 API 的模擬路由
 */
const express = require('express');

// 創建模擬路由
function createMockFileApi() {
  const router = express.Router();

  // 初始化資料庫連接函數
  router.initDB = jest.fn();

  // 模擬檔案存儲
  const mockFiles = [
    { name: 'image1.png', path: '/uploads/image1.png' },
    { name: 'image2.jpg', path: '/uploads/image2.jpg' }
  ];

  // 模擬檔案上傳
  router.post('/upload', (req, res) => {
    try {
      // 模擬 multer 的文件上傳結果
      const mockFile = {
        filename: 'mock-file.png',
        path: '/uploads/mock-file.png'
      };

      mockFiles.push({ name: mockFile.filename, path: mockFile.path });

      res.json({
        filename: mockFile.filename,
        path: mockFile.path
      });
    } catch (error) {
      res.status(500).json({ error: '檔案上傳失敗' });
    }
  });

  // 獲取圖片列表
  router.get('/images', (req, res) => {
    try {
      // 模擬 fs 的 readdir 函數
      const fs = require('fs');
      if (fs.readdir && typeof fs.readdir === 'function' && fs.readdir.mock) {
        // 如果 fs.readdir 被模擬了，則使用模擬的函數
        fs.readdir('/mock/path', (err, files) => {
          if (err) {
            return res.status(500).json({ error: '獲取圖片列表失敗' });
          }
          res.json(files);
        });
      } else {
        // 否則使用預設的模擬數據
        res.json(mockFiles.map(file => file.name));
      }
    } catch (error) {
      res.status(500).json({ error: '獲取圖片列表失敗' });
    }
  });

  // 模擬中間件處理檔案名稱亂碼
  router.fixFileNameEncoding = (req, res, next) => {
    if (req.file) {
      req.file.originalname = req.file.originalname || 'mock-file.png';
    }
    next();
  };

  return router;
}

module.exports = createMockFileApi;
