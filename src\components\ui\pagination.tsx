import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from './button';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  maxVisiblePages?: number;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  showFirstLast = true,
  maxVisiblePages = 5
}) => {
  const { t } = useTranslation();

  // 如果總頁數小於等於 1，不顯示分頁
  if (totalPages <= 1) {
    return null;
  }

  // 計算要顯示的頁碼範圍
  const getPageRange = () => {
    // 如果總頁數小於等於最大可見頁數，顯示所有頁碼
    if (totalPages <= maxVisiblePages) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // 計算起始和結束頁碼
    let start = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let end = start + maxVisiblePages - 1;

    // 調整起始和結束頁碼
    if (end > totalPages) {
      end = totalPages;
      start = Math.max(1, end - maxVisiblePages + 1);
    }

    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  };

  const pageRange = getPageRange();

  return (
    <div className="flex items-center space-x-2">
      {/* 首頁按鈕 */}
      {showFirstLast && (
        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1}
          title={t('common.firstPage')}
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>
      )}

      {/* 上一頁按鈕 */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        title={t('common.previousPage')}
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>

      {/* 頁碼按鈕 */}
      {pageRange.map(page => (
        <Button
          key={page}
          variant={page === currentPage ? 'default' : 'outline'}
          size="sm"
          onClick={() => onPageChange(page)}
          className={page === currentPage ? 'pointer-events-none' : ''}
        >
          {page}
        </Button>
      ))}

      {/* 下一頁按鈕 */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        title={t('common.nextPage')}
      >
        <ChevronRight className="h-4 w-4" />
      </Button>

      {/* 末頁按鈕 */}
      {showFirstLast && (
        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages}
          title={t('common.lastPage')}
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
};

export default Pagination;
