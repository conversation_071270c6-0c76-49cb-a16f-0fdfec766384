import { EPDConversionOptions, EPDConversionResult } from './types';
import { DisplayColorType } from '../../types';
import { BaseConverter } from './converters/BaseConverter';
import { BWConverter } from './converters/BWConverter';
import { BWRConverter } from './converters/BWRConverter';
import { BWRYConverter } from './converters/BWRYConverter';
import { createImageDataFromCanvas } from './utils/imageDataUtils';

/**
 * EPD 主轉換器類
 */
export class EpdConverter {
  /**
   * 將 Canvas 轉換為 EPD 原始數據
   */
  static convert(
    canvas: HTMLCanvasElement,
    options: EPDConversionOptions
  ): EPDConversionResult {
    try {
      // 使用 Canvas 的實際尺寸，而不是設備配置的尺寸
      const actualWidth = canvas.width;
      const actualHeight = canvas.height;

      // 驗證圖片尺寸
      if (!actualWidth || !actualHeight || actualWidth <= 0 || actualHeight <= 0) {
        throw new Error(`無法獲取有效的圖片尺寸: ${actualWidth}x${actualHeight}`);
      }

      console.log('EPD轉換開始:', {
        colorType: options.colorType,
        canvasSize: `${actualWidth}x${actualHeight}`,
        deviceSize: `${options.width}x${options.height}`,
        imagecode: options.imagecode.toString(16),
        templateRotation: options.templateRotation || 0
      });

      // 獲取圖像數據，使用 Canvas 的實際尺寸
      const imageData = createImageDataFromCanvas(canvas, actualWidth, actualHeight);

      // 創建轉換器，保持原始的設備尺寸選項
      // 轉換器內部會根據 canvasSize 和 deviceSize 的比較決定是否旋轉
      const converter = this.createConverter(options);

      // 執行轉換
      const result = converter.convert(imageData);

      if (result.success) {
        console.log('EPD轉換成功:', {
          totalBytes: result.metadata.totalBytes,
          imageInfoBytes: result.metadata.imageInfoBytes,
          pixelDataBytes: result.metadata.pixelDataBytes,
          processingTime: `${result.metadata.processingTime.toFixed(2)}ms`
        });
      } else {
        console.error('EPD轉換失敗:', result.error);
      }

      return result;

    } catch (error) {
      console.error('EPD轉換發生錯誤:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          originalSize: { width: canvas.width || 0, height: canvas.height || 0 },
          finalSize: { width: 0, height: 0 },
          bytesPerPixel: 0,
          totalBytes: 0,
          imageInfoBytes: 0,
          pixelDataBytes: 0,
          processingTime: 0,
          colorType: options.colorType
        }
      };
    }
  }

  /**
   * 創建適當的轉換器
   */
  private static createConverter(options: EPDConversionOptions): BaseConverter {
    switch (options.colorType) {
      case DisplayColorType.BW:  // "Gray16"
        return new BWConverter(options);
      case DisplayColorType.BWR: // "Black & White & Red"
        return new BWRConverter(options);
      case DisplayColorType.BWRY: // "Black & White & Red & Yellow"
        return new BWRYConverter(options);
      default:
        throw new Error(`不支援的顏色類型: ${options.colorType}`);
    }
  }

  /**
   * 獲取支援的顏色類型列表
   */
  static getSupportedColorTypes(): DisplayColorType[] {
    return [
      DisplayColorType.BW,
      DisplayColorType.BWR,
      DisplayColorType.BWRY
    ];
  }

  /**
   * 檢查顏色類型是否支援
   */
  static isColorTypeSupported(colorType: DisplayColorType): boolean {
    return this.getSupportedColorTypes().includes(colorType);
  }
}
