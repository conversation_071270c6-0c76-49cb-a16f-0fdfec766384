# Rawdata 格式增強計畫

## 概述

實現裝置回報 rawdata 格式功能，並添加 runlendata 壓縮格式，提供可擴展的壓縮架構。

## 需求分析

### 核心需求
1. **裝置格式回報**：Gateway 回報支援的 rawdata 格式
2. **runlendata 壓縮**：實現 Run-Length Encoding 壓縮
3. **格式選擇**：Server 根據 Gateway 回報的格式進行發送
4. **架構擴展性**：為未來更多壓縮格式預留空間
5. **測試工具更新**：ws-client-from-copied-info.js 支援格式選擇

### 技術規格

#### 支援的 Rawdata 格式
1. **rawdata** (預設)：未壓縮的原始數據
2. **runlendata**：Run-Length Encoding 壓縮數據
3. **預留擴展**：為未來格式如 lz4data、gzipdata 等預留

#### Gateway 格式回報結構
```javascript
{
  type: 'gatewayInfo',
  info: {
    macAddress: 'AA:BB:CC:DD:EE:FF',
    // ... 現有欄位
    chunkingSupport: {
      enabled: true,
      maxChunkSize: 200,
      maxSingleMessageSize: 2048,
      embeddedIndex: true,
      // 新增：偏好的 rawdata 格式
      supportedFormat: 'runlendata'  // 可選值: 'rawdata', 'runlendata'
    }
  }
}
```

#### Server 格式選擇邏輯
```javascript
// 根據 Gateway 指定的格式進行傳輸
function selectRawdataFormat(gatewayMac) {
  const capability = gatewayCapabilities.get(gatewayMac);
  const preferredFormat = capability?.supportedFormat || 'rawdata';

  // 驗證格式是否有效
  const validFormats = ['rawdata', 'runlendata'];
  if (!validFormats.includes(preferredFormat)) {
    console.warn(`Gateway ${gatewayMac} 指定了無效格式 ${preferredFormat}，使用預設 rawdata`);
    return 'rawdata';
  }

  console.log(`Gateway ${gatewayMac} 偏好格式: ${preferredFormat}`);
  return preferredFormat;
}
```

## 系統架構設計

### 1. 模組結構
```
server/utils/rawdataCompression/
├── index.js                    # 主要導出接口
├── types.js                    # 類型定義
├── compressors/
│   ├── runLengthCompressor.js  # Run-Length 壓縮器
│   ├── baseCompressor.js       # 基礎壓縮器類
│   └── compressionRegistry.js  # 壓縮器註冊表
├── utils/
│   ├── formatSelector.js       # 格式選擇工具
│   └── compressionAnalyzer.js  # 壓縮分析工具
└── __tests__/
    ├── runLengthCompressor.test.js
    └── formatSelector.test.js
```

### 2. 核心接口設計

```javascript
// 壓縮器基礎接口
class BaseCompressor {
  constructor(name) {
    this.name = name;
  }
  
  // 壓縮數據（不包含 ImageInfo）
  compress(pixelData) {
    throw new Error('Must implement compress method');
  }
  
  // 解壓數據
  decompress(compressedData) {
    throw new Error('Must implement decompress method');
  }
  
  // 估算壓縮比
  estimateCompressionRatio(pixelData) {
    throw new Error('Must implement estimateCompressionRatio method');
  }
}

// Run-Length 壓縮器
class RunLengthCompressor extends BaseCompressor {
  constructor() {
    super('runlendata');
  }
  
  compress(pixelData) {
    // 實現 Run-Length Encoding
    const compressed = [];
    let i = 0;
    
    while (i < pixelData.length) {
      const currentByte = pixelData[i];
      let count = 1;
      
      // 計算連續相同字節的數量
      while (i + count < pixelData.length && 
             pixelData[i + count] === currentByte && 
             count < 255) {
        count++;
      }
      
      // 根據重複次數決定編碼方式
      if (count >= 3) {
        // 重複模式：[0xFF, count, value]
        compressed.push(0xFF, count, currentByte);
      } else {
        // 直接模式：直接存儲字節
        for (let j = 0; j < count; j++) {
          compressed.push(currentByte);
        }
      }
      
      i += count;
    }
    
    return new Uint8Array(compressed);
  }
  
  decompress(compressedData) {
    const decompressed = [];
    let i = 0;
    
    while (i < compressedData.length) {
      if (compressedData[i] === 0xFF && i + 2 < compressedData.length) {
        // 重複模式
        const count = compressedData[i + 1];
        const value = compressedData[i + 2];
        
        for (let j = 0; j < count; j++) {
          decompressed.push(value);
        }
        
        i += 3;
      } else {
        // 直接模式
        decompressed.push(compressedData[i]);
        i++;
      }
    }
    
    return new Uint8Array(decompressed);
  }
  
  estimateCompressionRatio(pixelData) {
    // 快速估算壓縮比，不實際壓縮
    let estimatedSize = 0;
    let i = 0;
    
    while (i < pixelData.length) {
      const currentByte = pixelData[i];
      let count = 1;
      
      while (i + count < pixelData.length && 
             pixelData[i + count] === currentByte && 
             count < 255) {
        count++;
      }
      
      if (count >= 3) {
        estimatedSize += 3; // [0xFF, count, value]
      } else {
        estimatedSize += count; // 直接存儲
      }
      
      i += count;
    }
    
    return estimatedSize / pixelData.length;
  }
}
```

### 3. 格式選擇邏輯

```javascript
// 格式選擇器
class FormatSelector {
  constructor() {
    this.compressors = new Map();
    this.registerCompressor(new RunLengthCompressor());
  }
  
  registerCompressor(compressor) {
    this.compressors.set(compressor.name, compressor);
  }
  
  selectBestFormat(gatewayMac, pixelData) {
    const capability = gatewayCapabilities.get(gatewayMac);
    const supportedFormats = capability?.supportedFormats || ['rawdata'];
    
    let bestFormat = 'rawdata';
    let bestRatio = 1.0;
    
    // 測試支援的壓縮格式
    for (const format of supportedFormats) {
      if (format === 'rawdata') continue;
      
      const compressor = this.compressors.get(format);
      if (compressor) {
        const ratio = compressor.estimateCompressionRatio(pixelData);
        if (ratio < bestRatio) {
          bestFormat = format;
          bestRatio = ratio;
        }
      }
    }
    
    return {
      format: bestFormat,
      estimatedRatio: bestRatio,
      estimatedSize: Math.ceil(pixelData.length * bestRatio)
    };
  }
  
  compressData(format, pixelData) {
    if (format === 'rawdata') {
      return pixelData;
    }
    
    const compressor = this.compressors.get(format);
    if (!compressor) {
      throw new Error(`Unsupported format: ${format}`);
    }
    
    return compressor.compress(pixelData);
  }
}
```

## 實作計劃

### 階段一：基礎架構建立 (1-2天)
1. **創建壓縮模組目錄結構**
2. **實作基礎壓縮器類和註冊表**
3. **更新 Gateway 能力回報結構**
4. **修改 websocketService.js 支援格式選擇**

### 階段二：Run-Length 壓縮器實作 (2-3天)
1. **實作 RunLengthCompressor 類**
2. **優化壓縮算法效率**
3. **添加壓縮比估算功能**
4. **編寫單元測試**

### 階段三：Server 端整合 (1-2天)
1. **修改 sendPreviewToGateway.js**
2. **整合格式選擇邏輯**
3. **更新 WebSocket 消息格式**
4. **添加格式標識到傳輸消息**

### 階段四：測試工具更新 (1天)
1. **更新 ws-client-from-copied-info.js**
2. **添加格式選擇互動選項**
3. **支援 runlendata 解壓縮**
4. **添加壓縮效果展示**

### 階段五：測試和驗證 (1-2天)
1. **端到端功能測試**
2. **壓縮效果驗證**
3. **性能測試**
4. **向後兼容性測試**

## 風險評估與緩解

### 技術風險
1. **壓縮效果不佳**：某些數據可能不適合 Run-Length 壓縮
   - 緩解：實作壓縮比估算，只在有效時使用壓縮
2. **性能影響**：壓縮可能增加 CPU 負載
   - 緩解：優化算法，添加性能監控
3. **兼容性問題**：舊版 Gateway 可能不支援新格式
   - 緩解：保持向後兼容，預設使用 rawdata

### 實作風險
1. **複雜度增加**：多格式支援增加系統複雜度
   - 緩解：模組化設計，清晰的接口定義
2. **測試覆蓋**：需要測試多種格式組合
   - 緩解：完整的單元測試和整合測試

## 成功指標

1. **功能完整性**：所有格式正確實作並可正常使用
2. **壓縮效果**：Run-Length 壓縮在適合的數據上達到 30% 以上壓縮比
3. **性能表現**：壓縮處理時間不超過原始傳輸時間的 10%
4. **兼容性**：與現有系統完全兼容，無破壞性變更
5. **可擴展性**：新格式可以輕鬆添加到系統中

## 詳細實作規範

### WebSocket 消息格式更新

#### 1. Gateway 能力回報格式
```javascript
{
  type: 'gatewayInfo',
  info: {
    macAddress: 'AA:BB:CC:DD:EE:FF',
    model: 'Gateway Model 003',
    // ... 其他現有欄位
    chunkingSupport: {
      enabled: true,
      maxChunkSize: 200,
      maxSingleMessageSize: 2048,
      embeddedIndex: true,
      // 新增：偏好的 rawdata 格式
      supportedFormat: 'runlendata'  // 可選值: 'rawdata', 'runlendata'
    }
  }
}
```

#### 2. 圖像傳輸消息格式更新
```javascript
// 直接傳輸消息
{
  type: 'update_preview',
  deviceMac: 'AA:BB:CC:DD:EE:FF',
  imageCode: '12345678',
  rawdata: [255, 255, 0, 128, ...],  // 原始或壓縮後的數據
  rawdataFormat: 'runlendata',       // 新增：數據格式標識
  timestamp: '2021-12-31T16:00:00.000Z'
}

// 分片傳輸開始消息
{
  type: 'image_chunk_start',
  chunkId: 'chunk_12345',
  deviceMac: 'AA:BB:CC:DD:EE:FF',
  imageCode: '12345678',
  totalChunks: 10,
  totalSize: 2048,
  chunkSize: 200,
  indexSize: 4,
  dataType: 'rawdata',
  rawdataFormat: 'runlendata',       // 新增：數據格式標識
  mode: 'embedded_index',
  timestamp: '2021-12-31T16:00:00.000Z'
}
```

### Run-Length Encoding 優化算法

#### 標準 RLE 算法（與 Go 版本相同）
```javascript
class RunLengthCompressor extends BaseCompressor {
  constructor() {
    super('runlendata');
    this.MIN_RUN_LENGTH = 2;      // 最小重複長度
    this.MAX_RUN_LENGTH = 0x7F;   // 最大重複長度 (127)
    this.MAX_LITERAL_LENGTH = 0x7F; // 最大非重複長度 (127)
    this.LITERAL_FLAG = 0x80;     // 非重複序列標記 (bit7 = 1)
  }

  compress(pixelData) {
    const result = [];
    const n = pixelData.length;
    let inx = 0;

    while (inx < n) {
      // 檢查重複序列
      let runLength = 1;
      while (inx + runLength < n &&
             pixelData[inx + runLength] === pixelData[inx] &&
             runLength < this.MAX_RUN_LENGTH) {
        runLength++;
      }

      if (runLength >= this.MIN_RUN_LENGTH) {
        // 編碼重複序列：[runLength, value] (bit7 = 0)
        result.push(runLength);
        result.push(pixelData[inx]);
        inx += runLength;
      } else {
        // 非重複序列
        const start = inx;
        while (inx < n &&
               (inx + 1 >= n || pixelData[inx] !== pixelData[inx + 1]) &&
               (inx - start) < this.MAX_LITERAL_LENGTH) {
          inx++;
        }
        const length = inx - start;
        result.push(this.LITERAL_FLAG | length); // bit7 = 1
        for (let i = start; i < inx; i++) {
          result.push(pixelData[i]);
        }
      }
    }

    return new Uint8Array(result);
  }

  decompress(compressedData) {
    const decompressed = [];
    let i = 0;

    while (i < compressedData.length) {
      const header = compressedData[i];
      i++;

      if ((header & this.LITERAL_FLAG) === 0) {
        // 重複序列：bit7 = 0
        const runLength = header;
        if (i >= compressedData.length) {
          throw new Error('Incomplete RLE data: missing value byte');
        }
        const value = compressedData[i];
        i++;

        for (let j = 0; j < runLength; j++) {
          decompressed.push(value);
        }
      } else {
        // 非重複序列：bit7 = 1
        const length = header & this.MAX_LITERAL_LENGTH;
        if (i + length > compressedData.length) {
          throw new Error('Incomplete RLE data: insufficient data bytes');
        }

        for (let j = 0; j < length; j++) {
          decompressed.push(compressedData[i + j]);
        }
        i += length;
      }
    }

    return new Uint8Array(decompressed);
  }

  // 快速壓縮比估算（與 Go 版本邏輯一致）
  estimateCompressionRatio(pixelData) {
    let estimatedSize = 0;
    const n = pixelData.length;
    let inx = 0;

    while (inx < n) {
      // 檢查重複序列
      let runLength = 1;
      while (inx + runLength < n &&
             pixelData[inx + runLength] === pixelData[inx] &&
             runLength < this.MAX_RUN_LENGTH) {
        runLength++;
      }

      if (runLength >= this.MIN_RUN_LENGTH) {
        // 重複序列：2 bytes (runLength + value)
        estimatedSize += 2;
        inx += runLength;
      } else {
        // 非重複序列
        const start = inx;
        while (inx < n &&
               (inx + 1 >= n || pixelData[inx] !== pixelData[inx + 1]) &&
               (inx - start) < this.MAX_LITERAL_LENGTH) {
          inx++;
        }
        const length = inx - start;
        // 非重複序列：1 + length bytes (header + data)
        estimatedSize += 1 + length;
      }
    }

    return estimatedSize / pixelData.length;
  }
}
```

### 測試工具更新規範

#### ws-client-from-copied-info.js 更新
```javascript
// 在 main() 函數中添加格式選擇
async function main() {
  // ... 現有代碼

  // 新增：詢問支援的 rawdata 格式
  console.log('\n===== Rawdata 格式支援設定 =====');
  console.log('請選擇要模擬支援的 rawdata 格式：');
  console.log('1. rawdata only (預設，僅支援未壓縮格式)');
  console.log('2. rawdata + runlendata (支援 Run-Length 壓縮)');
  console.log('3. 自定義格式組合');

  const formatChoice = await prompt('請選擇格式支援選項 (1-3，預設為2): ') || '2';
  let supportedFormats = ['rawdata'];

  switch (formatChoice) {
    case '1':
      supportedFormats = ['rawdata'];
      break;
    case '2':
      supportedFormats = ['runlendata', 'rawdata'];
      break;
    case '3':
      const customFormats = await prompt('請輸入支援的格式 (用逗號分隔，如: runlendata,rawdata): ');
      supportedFormats = customFormats.split(',').map(f => f.trim()).filter(f => f);
      break;
    default:
      supportedFormats = ['runlendata', 'rawdata'];
  }

  console.log(`已設定支援的格式: ${supportedFormats.join(', ')}`);
  console.log('===============================\n');

  // 將格式信息傳遞給連接函數
  wsInfo.supportedFormats = supportedFormats;

  // ... 其餘現有代碼
}

// 更新 connectWebSocket 函數中的 gatewayInfo 消息
function connectWebSocket(wsInfo, maxChunkSize = 20) {
  // ... 現有代碼

  // 在發送 gatewayInfo 時包含格式支援信息
  const gatewayInfoMessage = {
    type: 'gatewayInfo',
    info: {
      macAddress: wsInfo.macAddress || 'AA:BB:CC:DD:EE:FF',
      model: wsInfo.model || 'Gateway Model 003',
      wifiFirmwareVersion: wsInfo.wifiFirmwareVersion || '1.0.0',
      btFirmwareVersion: wsInfo.btFirmwareVersion || '2.0.0',
      ipAddress: wsInfo.ipAddress || '*************',
      chunkingSupport: {
        enabled: true,
        maxChunkSize: maxChunkSize,
        maxSingleMessageSize: 2048,
        embeddedIndex: true,
        jsonHeader: true,
        // 新增：支援的格式
        supportedFormats: wsInfo.supportedFormats || ['rawdata']
      }
    }
  };

  // ... 其餘代碼
}
```

### 數據處理和保存更新
```javascript
// 更新 saveRawData 函數支援格式識別
function saveRawData(rawdata, deviceMac = 'unknown', imageCode = null, format = 'rawdata') {
  try {
    console.log(`準備保存 ${format} 格式的數據為 bin 檔案，裝置 MAC: ${deviceMac}`);

    // ... 現有的數據處理邏輯

    // 根據格式調整檔案名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `${format}_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
    const filePath = path.join(saveDir, fileName);

    // 寫入文件
    fs.writeFileSync(filePath, finalBuffer);

    console.log(`已成功將 ${format} 數據保存到: ${filePath}`);
    console.log(`數據大小: ${finalBuffer.length} 字節`);

    // 如果是壓縮格式，嘗試解壓縮驗證
    if (format === 'runlendata') {
      try {
        const decompressed = decompressRunLength(finalBuffer);
        console.log(`解壓縮後大小: ${decompressed.length} 字節`);
        console.log(`壓縮比: ${(finalBuffer.length / decompressed.length * 100).toFixed(1)}%`);

        // 保存解壓縮後的數據
        const decompressedFileName = `rawdata_decompressed_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
        const decompressedFilePath = path.join(saveDir, decompressedFileName);
        fs.writeFileSync(decompressedFilePath, Buffer.from(decompressed));
        console.log(`解壓縮數據已保存到: ${decompressedFilePath}`);
      } catch (decompressError) {
        console.error('解壓縮驗證失敗:', decompressError.message);
      }
    }

    // ... 其餘現有代碼
  } catch (err) {
    console.error('保存數據時出錯:', err.message);
  }
}

// 添加 Run-Length 解壓縮函數
function decompressRunLength(compressedData) {
  const ESCAPE_BYTE = 0xFF;
  const decompressed = [];
  let i = 0;

  while (i < compressedData.length) {
    if (compressedData[i] === ESCAPE_BYTE && i + 2 < compressedData.length) {
      const runLength = compressedData[i + 1];
      const value = compressedData[i + 2];

      if (runLength === 0) {
        // 轉義的 0xFF 字節
        decompressed.push(value);
      } else {
        // RLE 解碼
        for (let j = 0; j < runLength; j++) {
          decompressed.push(value);
        }
      }
      i += 3;
    } else {
      // 直接字節
      decompressed.push(compressedData[i]);
      i++;
    }
  }

  return new Uint8Array(decompressed);
}
```
