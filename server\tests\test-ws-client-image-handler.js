// WebSocket 測試客戶端 - 圖片處理器
// 此版本專注於正確解析並保存 WebSocket 中的圖像數據

const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 配置
const WS_SERVER = 'ws://localhost:3001/ws';
const jwtSecret = 'your_jwt_secret_here'; // 與伺服器端設置保持一致

// 建立命令行界面
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 提示用戶輸入
const prompt = (query) => new Promise((resolve) => rl.question(query, resolve));

// 生成 JWT token
const generateToken = (gatewayId, storeId) => {
  const payload = {
    gatewayId: gatewayId,
    storeId: storeId,
    type: 'gateway'
  };
  return jwt.sign(payload, jwtSecret, { expiresIn: '30d' });
};

// 檢查 base64 字符串是否有效
function isValidBase64(base64Str) {
  // base64 字符串應該符合特定的格式 (只包含 A-Z, a-z, 0-9, +, /, =)
  // 並且長度應該是 4 的倍數 (可能帶有填充字符 =)
  if (!base64Str || typeof base64Str !== 'string') {
    return false;
  }
  
  // 去除可能存在的空格和換行符
  const cleanStr = base64Str.replace(/\s/g, '');
  
  // 檢查是否符合 base64 格式
  const base64Regex = /^[A-Za-z0-9+/]+={0,2}$/;
  return base64Regex.test(cleanStr) && cleanStr.length % 4 === 0;
}

// 檢查圖片數據是否有效
function isValidImageBuffer(buffer) {
  // 檢查 buffer 是否為有效 Buffer 並且長度大於最小有效圖片大小
  if (!Buffer.isBuffer(buffer) || buffer.length < 100) {
    return false;
  }
  
  // 檢查常見圖片格式的文件頭標識 (Magic Numbers)
  const isPNG = buffer.length >= 8 && 
                buffer[0] === 0x89 && 
                buffer[1] === 0x50 && // P
                buffer[2] === 0x4E && // N
                buffer[3] === 0x47 && // G
                buffer[4] === 0x0D && 
                buffer[5] === 0x0A && 
                buffer[6] === 0x1A && 
                buffer[7] === 0x0A;
  
  const isJPEG = buffer.length >= 3 && 
                 buffer[0] === 0xFF && 
                 buffer[1] === 0xD8 && 
                 buffer[2] === 0xFF;
  
  const isGIF = buffer.length >= 6 && 
                buffer[0] === 0x47 && // G
                buffer[1] === 0x49 && // I
                buffer[2] === 0x46 && // F
                buffer[3] === 0x38 && // 8
                (buffer[4] === 0x39 || buffer[4] === 0x37) && // 9 or 7
                buffer[5] === 0x61;   // a
  
  const isBMP = buffer.length >= 2 && 
                buffer[0] === 0x42 && // B
                buffer[1] === 0x4D;   // M
  
  // 如果是常見圖片格式之一，則認為有效
  return isPNG || isJPEG || isGIF || isBMP;
}

// 保存圖像文件
function saveImageFile(imageBuffer, deviceMac = 'unknown', extension = 'png') {
  try {
    console.log(`準備保存圖片，裝置 MAC: ${deviceMac}，格式: ${extension}`);

    // 檢查 buffer 是否有效
    if (!Buffer.isBuffer(imageBuffer) || imageBuffer.length === 0) {
      throw new Error('無效的圖像數據 buffer');
    }
    
    // 驗證這是一個有效的圖片
    if (!isValidImageBuffer(imageBuffer)) {
      console.warn(`收到無效的圖像數據，長度: ${imageBuffer.length} 字節，不進行保存`);
      return;
    }
    
    // 建立保存圖像的目錄
    const saveDir = path.join(__dirname, 'saved_images');
    if (!fs.existsSync(saveDir)) {
      fs.mkdirSync(saveDir, { recursive: true });
    }
    
    // 創建文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `preview_${deviceMac.replace(/:/g, '')}_${timestamp}.${extension}`;
    const filePath = path.join(saveDir, fileName);
    
    // 寫入文件
    fs.writeFileSync(filePath, imageBuffer);
    
    console.log(`已成功保存圖片到: ${filePath}`);
  } catch (err) {
    console.error('保存圖片時出錯:', err.message);
  }
}

// 處理 base64 編碼的圖像
function processBase64Image(base64Data, deviceMac) {
  try {
    console.log(`處理 base64 圖像數據，裝置 MAC: ${deviceMac}`);
    
    // 從 Data URL 中提取 base64 數據部分
    const matches = base64Data.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
    
    if (!matches || matches.length !== 3) {
      console.log('未匹配到標準 data URL 格式，嘗試作為純 base64 處理...');
      
      // 檢查是否是有效的 base64 字符串
      if (!isValidBase64(base64Data)) {
        console.warn('無效的 base64 字符串，跳過保存');
        return;
      }
      
      // 嘗試直接作為 base64 字符串處理
      const imageBuffer = Buffer.from(base64Data, 'base64');
      
      // 檢查解碼後的數據是否是有效的圖片
      if (!isValidImageBuffer(imageBuffer)) {
        console.warn('base64 解碼後不是有效的圖片數據，跳過保存');
        return;
      }
      
      saveImageFile(imageBuffer, deviceMac, 'png');
      return;
    }
    
    // 提取 MIME 類型和 base64 數據
    const mimeType = matches[1];
    const base64Buffer = matches[2];
    const extension = mimeType.split('/')[1] || 'png';
    
    // 檢查提取出的 base64 字符串是否有效
    if (!isValidBase64(base64Buffer)) {
      console.warn('從 Data URL 提取的 base64 字符串無效，跳過保存');
      return;
    }
    
    // 創建圖像數據的 buffer
    const imageBuffer = Buffer.from(base64Buffer, 'base64');
    
    // 檢查解碼後的數據是否有效
    if (!isValidImageBuffer(imageBuffer)) {
      console.warn('從 Data URL 解碼的數據不是有效的圖片，跳過保存');
      return;
    }
    
    // 保存圖像文件
    saveImageFile(imageBuffer, deviceMac, extension);
  } catch (err) {
    console.error('處理 base64 圖像時出錯:', err.message);
  }
}

// 處理 WebSocket 接收的消息
function handleWebSocketMessage(data) {
  try {
    // 檢查是否為二進制數據
    if (data instanceof Buffer) {
      console.log('收到二進制數據，檢查是否為有效圖片...');
      
      // 檢查這個 Buffer 是否包含有效的圖片數據
      if (!isValidImageBuffer(data)) {
        console.warn('收到的二進制數據不是有效的圖片格式，跳過保存');
        console.log(`數據長度: ${data.length} 字節，前幾個字節: ${data.slice(0, 16).toString('hex')}`);
        return;
      }
      
      // 二進制數據是有效的圖片，保存它
      saveImageFile(data, 'binary-data', detectImageFormat(data));
      return;
    }
    
    // 嘗試解析 JSON 數據
    const message = JSON.parse(data);
    console.log('收到消息類型:', message.type);
    
    // 最關鍵的部分：檢查消息是否包含 imageData 字段
    if (message.hasOwnProperty('imageData')) {
      console.log('檢測到消息中包含 imageData 字段，準備處理...');
      console.log(`imageData 類型: ${typeof message.imageData}`);
      
      if (typeof message.imageData === 'string') {
        // 從消息中獲取裝置 MAC 地址
        const deviceMac = message.deviceMac || 'unknown';
        
        // 處理 imageData 字段的內容
        processBase64Image(message.imageData, deviceMac);
      } else {
        console.warn('imageData 字段不是字符串類型，無法處理');
      }
    }
  } catch (error) {
    console.error('處理 WebSocket 消息時出錯:', error.message);
  }
}

// 根據數據內容檢測圖片格式
function detectImageFormat(buffer) {
  if (buffer.length >= 8 && buffer[0] === 0x89 && buffer[1] === 0x50) {
    return 'png';
  } else if (buffer.length >= 3 && buffer[0] === 0xFF && buffer[1] === 0xD8) {
    return 'jpg';
  } else if (buffer.length >= 6 && buffer[0] === 0x47 && buffer[1] === 0x49 && buffer[2] === 0x46) {
    return 'gif';
  } else if (buffer.length >= 2 && buffer[0] === 0x42 && buffer[1] === 0x4D) {
    return 'bmp';
  }
  return 'bin'; // 未知格式
}

// 連接到 WebSocket 服務器
async function connectToWebSocket() {
  try {
    // 獲取連接參數
    const storeId = await prompt('請輸入門店 ID: ');
    const gatewayId = await prompt('請輸入網關 ID: ');
    
    // 生成 token
    const token = generateToken(gatewayId, storeId);
    console.log(`已生成 token: ${token}`);
    
    // 構建 WebSocket URL
    const url = `${WS_SERVER}/store/${storeId}/gateway/${gatewayId}?token=${token}`;
    console.log(`嘗試連接到 WebSocket: ${url}`);
    
    const ws = new WebSocket(url);
    
    ws.on('open', () => {
      console.log('WebSocket 連接已建立！');
      console.log('等待接收包含 imageData 的消息...');
      
      // 發送一個簡單的 ping 消息
      const pingMessage = {
        type: 'ping',
        timestamp: Date.now()
      };
      ws.send(JSON.stringify(pingMessage));
    });
    
    ws.on('message', (data) => {
      // 所有接收的消息都通過統一的處理函數處理
      handleWebSocketMessage(data);
    });
    
    ws.on('error', (error) => {
      console.error('WebSocket 錯誤:', error.message);
    });
    
    ws.on('close', () => {
      console.log('WebSocket 連接已關閉');
      rl.close();
    });
    
    // 添加用戶命令支持
    console.log('\n可用命令:');
    console.log('  q - 退出程序');
    console.log('  request <MAC> - 請求特定設備的圖像');
    
    rl.on('line', (input) => {
      const command = input.trim();
      
      if (command === 'q') {
        console.log('正在關閉連接並退出...');
        ws.close();
        setTimeout(() => process.exit(0), 500);
      } else if (command.startsWith('request ')) {
        const mac = command.split(' ')[1];
        if (mac && mac.match(/^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/)) {
          console.log(`請求設備 ${mac} 的預覽圖像...`);
          const requestMessage = {
            type: 'requestPreviewImage',
            macAddress: mac,
            timestamp: Date.now()
          };
          ws.send(JSON.stringify(requestMessage));
        } else {
          console.log('無效的 MAC 地址格式，請使用 AA:BB:CC:DD:EE:FF 格式');
        }
      } else if (command) {
        console.log('未知命令。使用 q 退出，或 request <MAC> 請求圖像');
      }
    });
    
    // 處理程序退出
    process.on('SIGINT', () => {
      console.log('\n正在關閉連接...');
      ws.close();
      rl.close();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('連接到 WebSocket 服務器時出錯:', error.message);
    rl.close();
    process.exit(1);
  }
}

console.log('=== WebSocket 圖片處理器 ===');
console.log('此工具專門用於接收並保存 WebSocket 消息中的圖片數據');
console.log('只有確認包含 imageData 字段的消息才會被處理為圖片\n');

// 啟動連接
connectToWebSocket().catch(error => {
  console.error('程序執行時發生錯誤:', error);
  process.exit(1);
});
