import { buildEndpointUrl } from './apiConfig';
import { useAuthStore } from '../../store/authStore';

/**
 * EPD Agent 配置接口
 */
export interface AIConfig {
  geminiApiKey: string;
  enabled: boolean;
  model: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
}

/**
 * AI對話消息接口
 */
export interface AIMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  executionResult?: any;
}

/**
 * AI對話請求接口
 */
export interface AIChatRequest {
  message: string;
  context?: any;
}

/**
 * AI對話回應接口
 */
export interface AIChatResponse {
  intent: string;
  confidence: number;
  extractedData: any;
  missingFields: string[];
  response: string;
  executionResult?: any;
}

/**
 * AI狀態接口
 */
export interface AIStatus {
  configured: boolean;
  enabled: boolean;
  model: string;
  initialized: boolean;
  timestamp: number;
}

/**
 * 獲取EPD Agent配置
 * @returns EPD Agent配置
 */
export async function getAIConfig(): Promise<AIConfig> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('configs', 'ai-config'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok) {
      if (response.status === 404) {
        // 如果配置不存在，返回默認配置
        return {
          geminiApiKey: '',
          enabled: false,
          model: 'gemini-2.0-flash',
          maxTokens: 2048,
          temperature: 0.7,
          timeout: 30000
        };
      }
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`獲取EPD Agent配置失敗: ${response.statusText}`);
    }
    
    return await response.json() as AIConfig;
  } catch (error) {
    console.error('獲取AI配置失敗:', error);
    throw error;
  }
}

/**
 * 保存AI配置
 * @param config AI配置
 */
export async function saveAIConfig(config: AIConfig): Promise<void> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('configs', 'ai-config'), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(config),
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`保存AI配置失敗: ${response.statusText}`);
    }
  } catch (error) {
    console.error('保存AI配置失敗:', error);
    throw error;
  }
}

/**
 * 測試AI連接
 * @returns 測試結果
 */
export async function testAIConnection(): Promise<{ success: boolean; message: string; response?: string }> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('ai-assistant', 'test'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }

      try {
        const errorData = await response.json();
        throw new Error(errorData.message || `測試AI連接失敗: ${response.statusText}`);
      } catch (parseError) {
        const errorText = await response.text();
        throw new Error(`測試AI連接失敗: ${response.statusText} - ${errorText}`);
      }
    }
    
    return await response.json();
  } catch (error) {
    console.error('測試AI連接失敗:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : '測試連接失敗'
    };
  }
}

/**
 * 發送EPD Agent對話消息
 * @param request 對話請求
 * @returns EPD Agent回應
 */
export async function sendAIMessage(request: AIChatRequest): Promise<AIChatResponse> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('ai-assistant', 'chat'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(request),
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const errorText = await response.text();
      throw new Error(`EPD Agent對話失敗: ${response.statusText} - ${errorText}`);
    }
    
    return await response.json() as AIChatResponse;
  } catch (error) {
    console.error('AI對話失敗:', error);
    throw error;
  }
}

/**
 * 獲取AI狀態
 * @returns AI狀態
 */
export async function getAIStatus(): Promise<AIStatus> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('ai-assistant', 'status'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`獲取AI狀態失敗: ${response.statusText}`);
    }
    
    return await response.json() as AIStatus;
  } catch (error) {
    console.error('獲取AI狀態失敗:', error);
    throw error;
  }
}

/**
 * 重新初始化AI服務
 * @returns 初始化結果
 */
export async function reinitializeAI(): Promise<{ success: boolean; message: string }> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('ai-assistant', 'reinitialize'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const errorText = await response.text();
      throw new Error(`重新初始化AI失敗: ${response.statusText} - ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('重新初始化AI失敗:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : '重新初始化失敗'
    };
  }
}

/**
 * 獲取AI對話歷史
 * @param page 頁碼
 * @param limit 每頁數量
 * @returns 對話歷史
 */
export async function getAIHistory(page: number = 1, limit: number = 20): Promise<{
  conversations: AIMessage[];
  total: number;
  page: number;
  limit: number;
}> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('ai-assistant', `history?page=${page}&limit=${limit}`), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`獲取AI對話歷史失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('獲取AI對話歷史失敗:', error);
    throw error;
  }
}

/**
 * 清除AI對話歷史
 * @returns 清除結果
 */
export async function clearAIHistory(): Promise<{ success: boolean; message: string }> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('ai-assistant', 'history'), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`清除AI對話歷史失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('清除AI對話歷史失敗:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : '清除歷史失敗'
    };
  }
}
