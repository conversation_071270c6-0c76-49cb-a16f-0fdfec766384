/**
 * 測試 Gateway 狀態重置功能
 * 
 * 這個測試驗證當 server 啟動時，所有 Gateway 狀態會被重置為 offline
 */

const { MongoClient, ObjectId } = require('mongodb');
const { resetAllGatewayStatus } = require('../server/services/websocketService');

// MongoDB 連接配置
const MONGODB_URI = 'mongodb://localhost:27017';
const DATABASE_NAME = 'epd_manager_lite';

async function testGatewayStatusReset() {
  let client;
  
  try {
    console.log('=== Gateway 狀態重置測試 ===\n');
    
    // 連接到 MongoDB
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    console.log('✓ 已連接到 MongoDB');
    
    const db = client.db(DATABASE_NAME);
    const gatewayCollection = db.collection('gateways');
    
    // 1. 創建測試數據 - 設置一些 Gateway 為 online 狀態
    console.log('\n1. 準備測試數據...');
    
    const testGateways = [
      {
        _id: new ObjectId(),
        name: 'Test Gateway 1',
        macAddress: 'AA:BB:CC:DD:EE:01',
        status: 'online',
        storeId: 'test-store-1',
        model: 'Test Model',
        wifiFirmwareVersion: '1.0.0',
        btFirmwareVersion: '1.0.0',
        ipAddress: '*************',
        lastSeen: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        _id: new ObjectId(),
        name: 'Test Gateway 2',
        macAddress: 'AA:BB:CC:DD:EE:02',
        status: 'online',
        storeId: 'test-store-2',
        model: 'Test Model',
        wifiFirmwareVersion: '1.0.0',
        btFirmwareVersion: '1.0.0',
        ipAddress: '*************',
        lastSeen: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        _id: new ObjectId(),
        name: 'Test Gateway 3',
        macAddress: 'AA:BB:CC:DD:EE:03',
        status: 'offline',
        storeId: 'test-store-3',
        model: 'Test Model',
        wifiFirmwareVersion: '1.0.0',
        btFirmwareVersion: '1.0.0',
        ipAddress: '*************',
        lastSeen: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    // 清理舊的測試數據
    await gatewayCollection.deleteMany({ name: { $regex: /^Test Gateway/ } });
    
    // 插入測試數據
    await gatewayCollection.insertMany(testGateways);
    console.log(`✓ 已插入 ${testGateways.length} 個測試 Gateway`);
    
    // 2. 檢查初始狀態
    console.log('\n2. 檢查初始狀態...');
    const onlineCount = await gatewayCollection.countDocuments({ 
      name: { $regex: /^Test Gateway/ },
      status: 'online' 
    });
    const offlineCount = await gatewayCollection.countDocuments({ 
      name: { $regex: /^Test Gateway/ },
      status: 'offline' 
    });
    
    console.log(`   - Online Gateway 數量: ${onlineCount}`);
    console.log(`   - Offline Gateway 數量: ${offlineCount}`);
    
    // 3. 初始化 WebSocket 服務的數據庫連接
    console.log('\n3. 初始化 WebSocket 服務...');
    const { initDB } = require('../server/services/websocketService');
    
    // 創建數據庫連接函數
    const getDbConnection = () => {
      return Promise.resolve({ client, db });
    };
    
    // 初始化數據庫連接
    initDB(getDbConnection);
    console.log('✓ WebSocket 服務數據庫連接已初始化');
    
    // 4. 執行狀態重置
    console.log('\n4. 執行 Gateway 狀態重置...');
    await resetAllGatewayStatus();
    
    // 5. 檢查重置後的狀態
    console.log('\n5. 檢查重置後的狀態...');
    const onlineCountAfter = await gatewayCollection.countDocuments({ 
      name: { $regex: /^Test Gateway/ },
      status: 'online' 
    });
    const offlineCountAfter = await gatewayCollection.countDocuments({ 
      name: { $regex: /^Test Gateway/ },
      status: 'offline' 
    });
    
    console.log(`   - Online Gateway 數量: ${onlineCountAfter}`);
    console.log(`   - Offline Gateway 數量: ${offlineCountAfter}`);
    
    // 6. 驗證結果
    console.log('\n6. 驗證結果...');
    if (onlineCountAfter === 0 && offlineCountAfter === testGateways.length) {
      console.log('✅ 測試通過！所有 Gateway 狀態已正確重置為 offline');
    } else {
      console.log('❌ 測試失敗！Gateway 狀態重置不正確');
      console.log(`   期望: 0 個 online, ${testGateways.length} 個 offline`);
      console.log(`   實際: ${onlineCountAfter} 個 online, ${offlineCountAfter} 個 offline`);
    }
    
    // 7. 清理測試數據
    console.log('\n7. 清理測試數據...');
    await gatewayCollection.deleteMany({ name: { $regex: /^Test Gateway/ } });
    console.log('✓ 測試數據已清理');
    
  } catch (error) {
    console.error('❌ 測試過程中發生錯誤:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('\n✓ MongoDB 連接已關閉');
    }
  }
}

// 執行測試
if (require.main === module) {
  testGatewayStatusReset()
    .then(() => {
      console.log('\n=== 測試完成 ===');
      process.exit(0);
    })
    .catch((error) => {
      console.error('測試執行失敗:', error);
      process.exit(1);
    });
}

module.exports = { testGatewayStatusReset };
