# 網關配置協議規範

## 1. 概述

本文檔定義了 EPD Manager App 與網關設備之間的配置協議。該協議允許移動應用程序將從服務器獲取的 WebSocket 連接信息發送給網關設備，使網關能夠與服務器建立 WebSocket 連接。

## 2. 配置方法

App 可以通過以下兩種方式將 WebSocket 連接信息發送給網關設備：

### 2.1 HTTP 配置方法

網關設備需要提供一個 HTTP API 端點，用於接收 WebSocket 連接信息。

#### 2.1.1 請求格式

```
POST http://{gateway_ip}:{gateway_port}/api/config/websocket
Content-Type: application/json

{
  "url": "ws://server-address:port/ws/store/{storeId}/gateway/{gatewayId}",
  "token": "jwt_token_here",
  "protocol": "json",
  "storeId": "store_id_here",
  "gatewayId": "gateway_id_here"
}
```

| 參數 | 類型 | 必填 | 描述 |
|------|------|------|------|
| url | string | 是 | WebSocket 服務器 URL |
| token | string | 是 | 用於 WebSocket 認證的 JWT Token |
| protocol | string | 是 | WebSocket 協議類型，通常為 "json" |
| storeId | string | 是 | 門店 ID |
| gatewayId | string | 是 | 網關 ID |

#### 2.1.2 回應格式

```
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "WebSocket configuration received"
}
```

### 2.2 TCP 配置方法

如果網關設備不支持 HTTP API，可以使用 TCP 連接發送配置信息。

#### 2.2.1 連接格式

```
TCP {gateway_ip}:{gateway_tcp_port}
```

#### 2.2.2 消息格式

```json
{
  "type": "websocket_config",
  "url": "ws://server-address:port/ws/store/{storeId}/gateway/{gatewayId}",
  "token": "jwt_token_here",
  "protocol": "json",
  "storeId": "store_id_here",
  "gatewayId": "gateway_id_here"
}
```

#### 2.2.3 回應格式

```json
{
  "type": "websocket_config_response",
  "success": true,
  "message": "WebSocket configuration received"
}
```

## 3. 網關設備實現要點

### 3.1 HTTP 配置方法實現

網關設備需要實現一個 HTTP 服務器，監聽指定端口（默認為 80），並提供 `/api/config/websocket` 端點。

```javascript
// 網關設備 HTTP 服務器示例 (Node.js)
const express = require('express');
const app = express();
const port = 80;

// 解析 JSON 請求體
app.use(express.json());

// WebSocket 配置端點
app.post('/api/config/websocket', (req, res) => {
  const config = req.body;
  
  // 驗證配置信息
  if (!config.url || !config.token) {
    return res.status(400).json({
      success: false,
      message: 'Missing required parameters'
    });
  }
  
  // 保存配置信息
  saveWebSocketConfig(config);
  
  // 嘗試連接到 WebSocket 服務器
  connectToWebSocketServer(config);
  
  // 返回成功響應
  res.json({
    success: true,
    message: 'WebSocket configuration received'
  });
});

// 啟動 HTTP 服務器
app.listen(port, () => {
  console.log(`Gateway HTTP server listening on port ${port}`);
});

// 保存 WebSocket 配置
function saveWebSocketConfig(config) {
  // 實現配置保存邏輯
  console.log('Saving WebSocket configuration:', config);
}

// 連接到 WebSocket 服務器
function connectToWebSocketServer(config) {
  // 實現 WebSocket 連接邏輯
  console.log('Connecting to WebSocket server:', config.url);
}
```

### 3.2 TCP 配置方法實現

網關設備需要實現一個 TCP 服務器，監聽指定端口（如 8080），並處理接收到的配置消息。

```javascript
// 網關設備 TCP 服務器示例 (Node.js)
const net = require('net');
const port = 8080;

// 創建 TCP 服務器
const server = net.createServer((socket) => {
  console.log('Client connected');
  
  // 處理接收到的數據
  socket.on('data', (data) => {
    try {
      // 解析 JSON 數據
      const config = JSON.parse(data.toString());
      
      // 驗證消息類型
      if (config.type !== 'websocket_config') {
        throw new Error('Invalid message type');
      }
      
      // 驗證配置信息
      if (!config.url || !config.token) {
        throw new Error('Missing required parameters');
      }
      
      // 保存配置信息
      saveWebSocketConfig(config);
      
      // 嘗試連接到 WebSocket 服務器
      connectToWebSocketServer(config);
      
      // 發送成功響應
      socket.write(JSON.stringify({
        type: 'websocket_config_response',
        success: true,
        message: 'WebSocket configuration received'
      }));
    } catch (error) {
      // 發送錯誤響應
      socket.write(JSON.stringify({
        type: 'websocket_config_response',
        success: false,
        message: error.message
      }));
    }
  });
  
  // 處理連接關閉
  socket.on('end', () => {
    console.log('Client disconnected');
  });
  
  // 處理錯誤
  socket.on('error', (error) => {
    console.error('Socket error:', error);
  });
});

// 啟動 TCP 服務器
server.listen(port, () => {
  console.log(`Gateway TCP server listening on port ${port}`);
});

// 保存 WebSocket 配置
function saveWebSocketConfig(config) {
  // 實現配置保存邏輯
  console.log('Saving WebSocket configuration:', config);
}

// 連接到 WebSocket 服務器
function connectToWebSocketServer(config) {
  // 實現 WebSocket 連接邏輯
  console.log('Connecting to WebSocket server:', config.url);
}
```

## 4. 移動應用實現要點

### 4.1 HTTP 配置方法實現

```javascript
// 移動應用 HTTP 配置方法示例 (React Native)
import axios from 'axios';

// 發送 WebSocket 配置到網關
async function sendWebSocketConfigToGateway(gatewayIp, config) {
  try {
    const response = await axios.post(`http://${gatewayIp}/api/config/websocket`, config, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 5000 // 5 秒超時
    });
    
    return response.data;
  } catch (error) {
    console.error('Failed to send WebSocket config to gateway:', error);
    throw error;
  }
}
```

### 4.2 TCP 配置方法實現

```javascript
// 移動應用 TCP 配置方法示例 (React Native)
import TcpSocket from 'react-native-tcp-socket';

// 發送 WebSocket 配置到網關
function sendWebSocketConfigToGateway(gatewayIp, gatewayPort, config) {
  return new Promise((resolve, reject) => {
    // 創建 TCP 客戶端
    const client = TcpSocket.createConnection({
      host: gatewayIp,
      port: gatewayPort,
      timeout: 5000 // 5 秒超時
    });
    
    // 處理連接成功
    client.on('connect', () => {
      console.log('Connected to gateway');
      
      // 創建配置消息
      const message = {
        type: 'websocket_config',
        ...config
      };
      
      // 發送配置消息
      client.write(JSON.stringify(message));
    });
    
    // 處理接收到的數據
    client.on('data', (data) => {
      try {
        // 解析 JSON 數據
        const response = JSON.parse(data.toString());
        
        // 關閉連接
        client.end();
        
        // 檢查響應
        if (response.success) {
          resolve(response);
        } else {
          reject(new Error(response.message || 'Failed to configure gateway'));
        }
      } catch (error) {
        reject(error);
      }
    });
    
    // 處理錯誤
    client.on('error', (error) => {
      console.error('TCP connection error:', error);
      reject(error);
    });
    
    // 處理連接關閉
    client.on('close', () => {
      console.log('Connection closed');
    });
  });
}
```

## 5. 安全考慮

### 5.1 通信安全

- 考慮使用 HTTPS 代替 HTTP 進行配置
- 對於 TCP 連接，考慮實現 TLS 加密
- 限制配置 API 只接受來自本地網絡的請求

### 5.2 認證機制

- 考慮在配置 API 中添加認證機制，如 API 密鑰或密碼
- 驗證網關的 MAC 地址，確保只有合法的網關能夠接收配置

### 5.3 數據驗證

- 驗證所有接收到的配置數據
- 檢查 URL 格式、Token 有效性等
- 防止 SQL 注入和 XSS 攻擊

## 6. 錯誤處理

### 6.1 連接錯誤

- 網關設備應該能夠處理連接超時和失敗
- 移動應用應該提供重試機制和用戶友好的錯誤消息

### 6.2 配置錯誤

- 網關設備應該驗證配置參數，並返回明確的錯誤消息
- 移動應用應該處理配置錯誤，並提供故障排除建議

### 6.3 WebSocket 連接錯誤

- 網關設備應該能夠處理 WebSocket 連接失敗
- 實現重連機制，在連接斷開時自動嘗試重新連接
