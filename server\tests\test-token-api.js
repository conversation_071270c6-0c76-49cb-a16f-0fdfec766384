#!/usr/bin/env node

/**
 * 測試統一的token生成API
 * 用法: node test-token-api.js [username] [password]
 */

const http = require('http');
const querystring = require('querystring');

// 配置
const BASE_URL = 'http://localhost:3001';
const DEFAULT_USERNAME = 'root';
const DEFAULT_PASSWORD = '12345689';

// 從命令行參數獲取用戶名和密碼
const username = process.argv[2] || DEFAULT_USERNAME;
const password = process.argv[3] || DEFAULT_PASSWORD;

console.log('🔧 測試統一token生成API');
console.log(`📝 使用用戶: ${username}`);
console.log(`🌐 服務器地址: ${BASE_URL}`);
console.log('');

async function testTokenAPI() {
  try {
    // 1. 測試登入並獲取tokens
    console.log('1️⃣ 測試登入API...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      username,
      password,
      rememberMe: false
    });

    if (loginResponse.status === 200) {
      console.log('✅ 登入成功');
      console.log(`📋 API Token: ${loginResponse.data.token.substring(0, 20)}...`);
      console.log(`🔗 WebSocket Token: ${loginResponse.data.wsToken.substring(0, 20)}...`);
      console.log(`⏰ WebSocket Token過期時間: ${loginResponse.data.wsTokenExpiresIn}秒`);
      
      // 保存cookie和token
      const cookies = loginResponse.headers['set-cookie'];
      const apiToken = loginResponse.data.token;
      const wsToken = loginResponse.data.wsToken;
      
      console.log('');
      
      // 2. 測試獲取WebSocket token API
      console.log('2️⃣ 測試獲取WebSocket token API...');
      const wsTokenResponse = await axios.get(`${BASE_URL}/api/auth/websocket-token`, {
        headers: {
          'Cookie': cookies ? cookies.join('; ') : '',
          'Authorization': `Bearer ${apiToken}`
        }
      });
      
      if (wsTokenResponse.status === 200) {
        console.log('✅ 獲取WebSocket token成功');
        console.log(`🔗 新WebSocket Token: ${wsTokenResponse.data.wsToken.substring(0, 20)}...`);
        console.log(`⏰ 過期時間: ${wsTokenResponse.data.expiresIn}秒`);
        console.log(`👤 用戶信息: ${wsTokenResponse.data.user.username}`);
      }
      
      console.log('');
      
      // 3. 測試生成Gateway token API
      console.log('3️⃣ 測試生成Gateway token API...');
      const gatewayTokenResponse = await axios.post(`${BASE_URL}/api/auth/gateway-token`, {
        gatewayId: 'test-gateway-001',
        storeId: 'test-store-001',
        macAddress: 'AA:BB:CC:DD:EE:FF'
      }, {
        headers: {
          'Cookie': cookies ? cookies.join('; ') : '',
          'Authorization': `Bearer ${apiToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (gatewayTokenResponse.status === 200) {
        console.log('✅ 生成Gateway token成功');
        console.log(`🏭 Gateway Token: ${gatewayTokenResponse.data.token.substring(0, 20)}...`);
        console.log(`⏰ 過期時間: ${gatewayTokenResponse.data.expiresIn}秒`);
        console.log(`🔗 WebSocket URL: ${gatewayTokenResponse.data.wsUrl}`);
        console.log(`🏪 Gateway信息:`, gatewayTokenResponse.data.gateway);
      }
      
      console.log('');
      console.log('🎉 所有token API測試完成！');
      
      // 4. 驗證token格式
      console.log('');
      console.log('4️⃣ 驗證token格式...');
      
      const { verifyToken, decodeToken } = require('../utils/jwtUtils');
      
      try {
        // 驗證WebSocket token
        const wsDecoded = verifyToken(wsToken);
        console.log('✅ WebSocket token驗證成功');
        console.log(`📋 Token內容:`, {
          userId: wsDecoded.userId,
          username: wsDecoded.username,
          type: wsDecoded.type,
          exp: new Date(wsDecoded.exp * 1000).toLocaleString()
        });
        
        // 驗證Gateway token
        const gatewayDecoded = verifyToken(gatewayTokenResponse.data.token);
        console.log('✅ Gateway token驗證成功');
        console.log(`📋 Token內容:`, {
          gatewayId: gatewayDecoded.gatewayId,
          storeId: gatewayDecoded.storeId,
          macAddress: gatewayDecoded.macAddress,
          type: gatewayDecoded.type,
          exp: new Date(gatewayDecoded.exp * 1000).toLocaleString()
        });
        
      } catch (error) {
        console.error('❌ Token驗證失敗:', error.message);
      }
      
    } else {
      console.error('❌ 登入失敗:', loginResponse.status);
    }
    
  } catch (error) {
    console.error('❌ 測試失敗:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('💡 提示: 請確認用戶名和密碼是否正確');
      console.log('💡 默認管理員帳號: root / 12345689');
    }
  }
}

// 執行測試
testTokenAPI();
