@echo off
chcp 65001 >nul
echo ========================================
echo EPD Manager Stop Tool (Windows)
echo ========================================
echo.

:: Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not running or not installed
    pause
    exit /b 1
)

:: Check if docker-compose.yml exists
if not exist "docker-compose.yml" (
    echo [ERROR] Cannot find docker-compose.yml file
    echo Please run this script in directory containing docker-compose.yml
    pause
    exit /b 1
)

echo [INFO] Stopping EPD Manager services...
docker-compose down

if %errorlevel% neq 0 (
    echo [ERROR] Failed to stop services
    pause
    exit /b 1
)

echo.
echo [SUCCESS] EPD Manager services stopped
echo.

:: Ask if cleanup data
set /p cleanup="Do you want to clean all data (including database)? This will delete all data! (y/N): "
if /i "%cleanup%"=="y" (
    echo [WARNING] Cleaning all data...
    docker-compose down -v
    echo [DONE] All data cleaned
) else (
    echo [INFO] Data preserved, will be restored on next startup
)

echo.
echo Management commands:
echo   Restart: docker-compose up -d
echo   Check status: docker-compose ps
echo   View logs: docker-compose logs -f
echo.
echo Press any key to exit...
pause >nul
