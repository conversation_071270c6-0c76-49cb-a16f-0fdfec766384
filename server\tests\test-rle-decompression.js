// 測試 RLE 解壓縮修正
const fs = require('fs');
const path = require('path');

// RLE 解壓縮函數（從 ws-client-from-copied-info.js 複製）
function decompressRunLength(compressedData) {
  const decompressed = [];
  let i = 0;

  while (i < compressedData.length) {
    const header = compressedData[i];
    i++;

    if ((header & 0x80) === 0) {
      // 重複序列：bit7 = 0
      const runLength = header;
      if (i >= compressedData.length) {
        throw new Error('Incomplete RLE data: missing value byte');
      }
      const value = compressedData[i];
      i++;

      for (let j = 0; j < runLength; j++) {
        decompressed.push(value);
      }
    } else {
      // 非重複序列：bit7 = 1
      const length = header & 0x7F;
      if (i + length > compressedData.length) {
        throw new Error('Incomplete RLE data: insufficient data bytes');
      }

      for (let j = 0; j < length; j++) {
        decompressed.push(compressedData[i + j]);
      }
      i += length;
    }
  }

  return new Uint8Array(decompressed);
}

// 修正後的解壓縮函數
function decompressRawdata(rawdata, format) {
  if (format === 'rawdata') {
    return rawdata; // 無需解壓縮
  } else if (format === 'runlendata') {
    // RLE 壓縮數據，需要正確處理 ImageInfo 和像素數據
    const rawdataBytes = rawdata instanceof Uint8Array ? rawdata : new Uint8Array(rawdata);
    
    if (rawdataBytes.length < 12) {
      throw new Error('Rawdata too small to contain ImageInfo (12 bytes)');
    }
    
    // 分離 ImageInfo (前 12 bytes) 和壓縮的像素數據
    const imageInfo = rawdataBytes.slice(0, 12);
    const compressedPixels = rawdataBytes.slice(12);
    
    console.log(`ImageInfo 大小: ${imageInfo.length} bytes`);
    console.log(`壓縮像素數據大小: ${compressedPixels.length} bytes`);
    
    // 解壓縮像素數據
    const decompressedPixels = decompressRunLength(compressedPixels);
    console.log(`解壓縮像素數據大小: ${decompressedPixels.length} bytes`);
    
    // 重新組合完整數據
    const completeData = new Uint8Array(12 + decompressedPixels.length);
    completeData.set(imageInfo, 0);
    completeData.set(decompressedPixels, 12);
    
    return completeData;
  } else {
    console.warn(`未知的 rawdata 格式: ${format}，當作未壓縮處理`);
    return rawdata;
  }
}

// 測試函數
function testDecompression() {
  console.log('=== RLE 解壓縮測試 ===');
  
  // 創建測試數據：12 bytes ImageInfo + 壓縮的像素數據
  const imageInfo = new Uint8Array([
    0x00, 0x00, 0x00, 0x00,  // X, Y (uint16 each)
    0x80, 0x01, 0x28, 0x01,  // Width=384, Height=296 (uint16 each, little-endian)
    0x12, 0x34, 0x56, 0x78   // imageCode (uint32, little-endian)
  ]);
  
  // 模擬全白圖像的 RLE 壓縮數據
  // 對於 2.9" (384x296) BW 圖像，像素數據大小應該是 384*296/8 = 14208 bytes
  // 但是根據您的描述是 9472 bytes，可能是不同的尺寸或格式
  
  // 創建一個簡單的 RLE 壓縮數據：127個0xFF + 20個0x00
  const compressedPixels = new Uint8Array([
    127, 0xFF,  // 重複序列：127個0xFF
    20, 0x00    // 重複序列：20個0x00
  ]);
  
  // 組合完整的測試數據
  const testData = new Uint8Array(12 + compressedPixels.length);
  testData.set(imageInfo, 0);
  testData.set(compressedPixels, 12);
  
  console.log(`測試數據總大小: ${testData.length} bytes`);
  console.log(`ImageInfo: ${imageInfo.length} bytes`);
  console.log(`壓縮像素數據: ${compressedPixels.length} bytes`);
  
  try {
    // 測試解壓縮
    const decompressed = decompressRawdata(testData, 'runlendata');
    
    console.log(`解壓縮後總大小: ${decompressed.length} bytes`);
    console.log(`預期像素數據大小: ${127 + 20} = 147 bytes`);
    console.log(`實際解壓縮後大小: ${decompressed.length - 12} bytes (像素數據)`);
    
    // 驗證 ImageInfo 是否保持不變
    const decompressedImageInfo = decompressed.slice(0, 12);
    const imageInfoMatch = Array.from(imageInfo).every((byte, index) => byte === decompressedImageInfo[index]);
    console.log(`ImageInfo 保持不變: ${imageInfoMatch}`);
    
    // 驗證像素數據
    const decompressedPixels = decompressed.slice(12);
    console.log(`前10個像素值: [${Array.from(decompressedPixels.slice(0, 10)).join(', ')}]`);
    console.log(`後10個像素值: [${Array.from(decompressedPixels.slice(-10)).join(', ')}]`);
    
    console.log('✅ 解壓縮測試成功！');
    
  } catch (error) {
    console.error('❌ 解壓縮測試失敗:', error.message);
  }
}

// 如果有實際的測試文件，也可以測試
function testWithRealFile() {
  const testDir = path.join(__dirname, 'saved_images');
  if (!fs.existsSync(testDir)) {
    console.log('沒有找到 saved_images 目錄，跳過實際文件測試');
    return;
  }
  
  // 查找 runlendata 文件
  const files = fs.readdirSync(testDir);
  const runlendataFiles = files.filter(file => file.includes('runlendata') && file.endsWith('.bin'));
  
  if (runlendataFiles.length === 0) {
    console.log('沒有找到 runlendata 文件，跳過實際文件測試');
    return;
  }
  
  console.log('\n=== 實際文件測試 ===');
  const testFile = runlendataFiles[0];
  console.log(`測試文件: ${testFile}`);
  
  try {
    const filePath = path.join(testDir, testFile);
    const fileData = fs.readFileSync(filePath);
    
    console.log(`文件大小: ${fileData.length} bytes`);
    
    const decompressed = decompressRawdata(fileData, 'runlendata');
    console.log(`解壓縮後大小: ${decompressed.length} bytes`);
    
    const compressionRatio = (fileData.length / decompressed.length * 100).toFixed(1);
    console.log(`壓縮比: ${compressionRatio}%`);
    
    console.log('✅ 實際文件測試成功！');
    
  } catch (error) {
    console.error('❌ 實際文件測試失敗:', error.message);
  }
}

// 執行測試
testDecompression();
testWithRealFile();
