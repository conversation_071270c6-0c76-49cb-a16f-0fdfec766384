/**
 * EPD 圖片轉換模組 (JavaScript 版本)
 *
 * 將圖片轉換為 EPD 可用的原始數據格式
 * 支援 BW/GRAY16、BWR、BWRY 等格式
 */

const { createCanvas, loadImage } = require('canvas');

// DisplayColorType 枚舉 (對應前端的 DisplayColorType)
const DisplayColorType = {
  BW: 'Gray16',
  BWR: 'Black & White & Red',
  BWRY: 'Black & White & Red & Yellow'
};

/**
 * 獲取預設調色板（與預覽圖生成時使用的相同）
 */
function getColorPalette(colorType) {
  switch (colorType) {
    case DisplayColorType.BWR: // "Black & White & Red"
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 }      // 紅色
      ];

    case DisplayColorType.BWRY: // "Black & White & Red & Yellow"
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 },     // 紅色
        { r: 255, g: 255, b: 0 }    // 黃色
      ];

    case DisplayColorType.BW: // "Gray16"
    default:
      // 16級灰度調色板
      const grayPalette = [];
      for (let i = 0; i < 16; i++) {
        const grayValue = Math.round((255 / 15) * i);
        grayPalette.push({ r: grayValue, g: grayValue, b: grayValue });
      }
      return grayPalette;
  }
}

/**
 * 精確顏色比對（與預覽圖生成時使用的相同邏輯）
 */
function findExactColorMatch(r, g, b, palette) {
  // 直接比對 RGB 值，找到完全匹配的顏色
  for (const color of palette) {
    if (color.r === r && color.g === g && color.b === b) {
      return color;
    }
  }

  // 如果沒有完全匹配，記錄警告並使用備用邏輯
  console.warn(`EPD轉換: 未找到精確匹配的顏色: RGB(${r}, ${g}, ${b})`);
  return findClosestColor(r, g, b, palette);
}

/**
 * 備用的最接近顏色查找
 */
function findClosestColor(r, g, b, palette) {
  let minDistance = Infinity;
  let closestColor = palette[0];

  for (const color of palette) {
    const distance = Math.sqrt(
      Math.pow(r - color.r, 2) +
      Math.pow(g - color.g, 2) +
      Math.pow(b - color.b, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      closestColor = color;
    }
  }

  console.warn(`EPD轉換: 使用最接近的顏色: RGB(${closestColor.r}, ${closestColor.g}, ${closestColor.b})`);
  return closestColor;
}

/**
 * 計算對齊後的寬度
 */
function calculatePaddedWidth(width, colorType) {
  switch (colorType) {
    case DisplayColorType.BW:  // "Gray16"
      return Math.ceil(width / 2) * 2;  // 2的倍數
    case DisplayColorType.BWR: // "Black & White & Red"
      return Math.ceil(width / 8) * 8;  // 8的倍數
    case DisplayColorType.BWRY: // "Black & White & Red & Yellow"
      return Math.ceil(width / 4) * 4;  // 4的倍數
    default:
      throw new Error(`不支援的顏色類型: ${colorType}`);
  }
}

/**
 * 獲取模板旋轉角度
 */
function getTemplateRotation(template) {
  // 從模板的 orientation 或其他屬性獲取旋轉角度
  if (template && template.orientation) {
    // 解析 orientation 字符串，例如 "90°", "180°", "270°"
    const match = template.orientation.match(/(\d+)°/);
    if (match) {
      return parseInt(match[1], 10);
    }
  }
  return 0; // 默認無旋轉
}

/**
 * 獲取模板旋轉角度的反向旋轉
 */
function getTemplateReverseRotation(templateRotation) {
  if (templateRotation === 0) return 0;
  return (360 - templateRotation) % 360;
}

/**
 * 檢查是否需要旋轉以匹配設備尺寸
 */
function needsRotationForDeviceMatch(imageData, options) {
  const canvasWidth = imageData.width;
  const canvasHeight = imageData.height;
  const deviceWidth = options.width;
  const deviceHeight = options.height;

  // 檢查 canvasSize 跟 deviceSize 是否長寬相反
  const canvasIsLandscape = canvasWidth > canvasHeight;
  const deviceIsLandscape = deviceWidth > deviceHeight;

  return canvasIsLandscape !== deviceIsLandscape;
}

/**
 * 圖片旋轉處理
 */
function rotateImageData(imageData, rotation) {
  switch (rotation) {
    case 90:
      return rotateImageData90(imageData);
    case 180:
      return rotateImageData180(imageData);
    case 270:
      return rotateImageData270(imageData);
    default:
      return imageData;
  }
}

/**
 * 90度順時針旋轉
 */
function rotateImageData90(imageData) {
  const { width, height, data } = imageData;
  const canvas = createCanvas(height, width);
  const ctx = canvas.getContext('2d');

  // 創建新的 ImageData
  const newImageData = ctx.createImageData(height, width);
  const newData = newImageData.data;

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const oldIndex = (y * width + x) * 4;
      const newX = height - 1 - y;
      const newY = x;
      const newIndex = (newY * height + newX) * 4;

      newData[newIndex] = data[oldIndex];         // R
      newData[newIndex + 1] = data[oldIndex + 1]; // G
      newData[newIndex + 2] = data[oldIndex + 2]; // B
      newData[newIndex + 3] = data[oldIndex + 3]; // A
    }
  }

  return newImageData;
}

/**
 * 180度旋轉
 */
function rotateImageData180(imageData) {
  const { width, height, data } = imageData;
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  const newImageData = ctx.createImageData(width, height);
  const newData = newImageData.data;

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const oldIndex = (y * width + x) * 4;
      const newX = width - 1 - x;
      const newY = height - 1 - y;
      const newIndex = (newY * width + newX) * 4;

      newData[newIndex] = data[oldIndex];         // R
      newData[newIndex + 1] = data[oldIndex + 1]; // G
      newData[newIndex + 2] = data[oldIndex + 2]; // B
      newData[newIndex + 3] = data[oldIndex + 3]; // A
    }
  }

  return newImageData;
}

/**
 * 270度順時針旋轉
 */
function rotateImageData270(imageData) {
  const { width, height, data } = imageData;
  const canvas = createCanvas(height, width);
  const ctx = canvas.getContext('2d');

  const newImageData = ctx.createImageData(height, width);
  const newData = newImageData.data;

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const oldIndex = (y * width + x) * 4;
      const newX = y;
      const newY = width - 1 - x;
      const newIndex = (newY * height + newX) * 4;

      newData[newIndex] = data[oldIndex];         // R
      newData[newIndex + 1] = data[oldIndex + 1]; // G
      newData[newIndex + 2] = data[oldIndex + 2]; // B
      newData[newIndex + 3] = data[oldIndex + 3]; // A
    }
  }

  return newImageData;
}

/**
 * 從 base64 字符串創建 Canvas
 */
async function createCanvasFromImageData(imageDataStr) {
  try {
    // 移除 data URL 前綴
    const base64Data = imageDataStr.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');

    const img = await loadImage(buffer);
    const canvas = createCanvas(img.width, img.height);
    const ctx = canvas.getContext('2d');

    ctx.drawImage(img, 0, 0);
    return canvas;
  } catch (error) {
    throw new Error(`無法創建 Canvas: ${error.message}`);
  }
}

/**
 * 將 ImageInfo 序列化為字節數組 (Little Endian)
 */
function serializeImageInfo(imageInfo) {
  const buffer = new ArrayBuffer(12); // uint32 + 4 * uint16 = 12 bytes
  const view = new DataView(buffer);

  view.setUint32(0, imageInfo.imagecode, true);  // Little Endian
  view.setUint16(4, imageInfo.x, true);
  view.setUint16(6, imageInfo.y, true);
  view.setUint16(8, imageInfo.width, true);
  view.setUint16(10, imageInfo.height, true);

  return new Uint8Array(buffer);
}

/**
 * 組合 ImageInfo + 像素數據
 */
function combineRawData(imageInfo, pixelData) {
  const imageInfoBytes = serializeImageInfo(imageInfo);
  const rawdata = new Uint8Array(imageInfoBytes.length + pixelData.length);

  rawdata.set(imageInfoBytes, 0);
  rawdata.set(pixelData, imageInfoBytes.length);

  return rawdata;
}

/**
 * BW/GRAY16 轉換器
 */
class BWConverter {
  constructor(options) {
    this.options = options;
    this.width = options.width;
    this.height = options.height;
    this.paddedWidth = calculatePaddedWidth(this.width, options.colorType);
    this.bufferSize = Math.ceil((this.paddedWidth * this.height) / 2);
    this.buffer = new Uint8Array(this.bufferSize);
    this.index = 0;
  }

  processPixel(x, y, pixel) {
    const grayValue = this.toGray16(pixel);

    if ((this.index & 1) === 0) {
      // 偶數索引：存儲在高4位
      this.buffer[this.index >> 1] = (grayValue & 0xF0);
    } else {
      // 奇數索引：存儲在低4位
      this.buffer[this.index >> 1] |= (grayValue >> 4);
    }
    this.index++;
  }

  toGray16(pixel) {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BW);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 將匹配的灰度值轉換為 4bit 格式 (0-15)
    const grayLevel = Math.round(matchedColor.r / 17); // 255/15 ≈ 17
    return Math.min(15, Math.max(0, grayLevel)) << 4;
  }

  getPixelData() {
    return this.buffer;
  }
}

/**
 * BWR 轉換器
 */
class BWRConverter {
  constructor(options) {
    this.options = options;
    this.width = options.width;
    this.height = options.height;
    this.paddedWidth = calculatePaddedWidth(this.width, options.colorType);
    this.bufferSize = Math.ceil((this.paddedWidth * this.height) / 8);
    this.buffer = new Uint8Array(this.bufferSize);   // 黑白數據
    this.buffer2 = new Uint8Array(this.bufferSize);  // 紅色數據
    this.index = 0;
  }

  processPixel(x, y, pixel) {
    const { isRed, isWhite } = this.analyzeColor(pixel);
    const bitMask = 0x80 >> (x & 7);

    if (isRed) {
      // 紅色：兩個buffer都設為0 (已經初始化為0)
    } else {
      this.buffer2[this.index] |= bitMask;
      if (isWhite) {
        this.buffer[this.index] |= bitMask;
      }
    }

    // 當處理完一個字節或到達行尾時，移動到下一個字節
    if ((bitMask === 1) || ((x + 1) === this.paddedWidth)) {
      this.index++;
    }
  }

  analyzeColor(pixel) {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BWR);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 根據匹配的顏色判斷類型
    const isRed = matchedColor.r === 255 && matchedColor.g === 0 && matchedColor.b === 0;
    const isWhite = matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 255;

    return { isRed, isWhite };
  }

  getPixelData() {
    // 合併兩個表格
    const result = new Uint8Array(this.buffer.length * 2);
    result.set(this.buffer, 0);
    result.set(this.buffer2, this.buffer.length);
    return result;
  }
}

/**
 * BWRY 轉換器
 */
class BWRYConverter {
  constructor(options) {
    this.options = options;
    this.width = options.width;
    this.height = options.height;
    this.paddedWidth = calculatePaddedWidth(this.width, options.colorType);
    this.bufferSize = Math.ceil((this.paddedWidth * this.height) / 4);
    this.buffer = new Uint8Array(this.bufferSize);
    this.index = 0;
  }

  processPixel(x, y, pixel) {
    const colorValue = this.analyzeColorBWRY(pixel);
    const pixelInByte = x & 3; // 每字節4個像素
    const shift = (3 - pixelInByte) * 2; // 2bit per pixel

    this.buffer[this.index] |= (colorValue << shift);

    // 當處理完一個字節或到達行尾時，移動到下一個字節
    if (pixelInByte === 3 || (x + 1) === this.paddedWidth) {
      this.index++;
    }
  }

  analyzeColorBWRY(pixel) {
    // 使用預設調色板進行精確匹配
    const palette = getColorPalette(DisplayColorType.BWRY);
    const matchedColor = findExactColorMatch(pixel.r, pixel.g, pixel.b, palette);

    // 根據匹配的顏色返回對應的數值：0=黑, 1=白, 2=黃, 3=紅 (修正紅黃對應關係)
    if (matchedColor.r === 0 && matchedColor.g === 0 && matchedColor.b === 0) {
      return 0; // 黑色
    } else if (matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 255) {
      return 1; // 白色
    } else if (matchedColor.r === 255 && matchedColor.g === 255 && matchedColor.b === 0) {
      return 2; // 黃色 (修正：原本是3，現在改為2)
    } else if (matchedColor.r === 255 && matchedColor.g === 0 && matchedColor.b === 0) {
      return 3; // 紅色 (修正：原本是2，現在改為3)
    }

    // 備用邏輯（不應該到達這裡）
    console.warn(`BWRY: 未預期的顏色匹配結果: RGB(${matchedColor.r}, ${matchedColor.g}, ${matchedColor.b})`);
    return 0; // 默認黑色
  }

  getPixelData() {
    return this.buffer;
  }
}

/**
 * 主要的 EPD 轉換函數
 */
async function convertImageToEPDRawData(canvas, options) {
  const startTime = Date.now();

  try {
    // 使用 Canvas 的實際尺寸，而不是設備配置的尺寸
    const actualWidth = canvas.width;
    const actualHeight = canvas.height;

    // 驗證圖片尺寸
    if (!actualWidth || !actualHeight || actualWidth <= 0 || actualHeight <= 0) {
      throw new Error(`無法獲取有效的圖片尺寸: ${actualWidth}x${actualHeight}`);
    }

    console.log('EPD轉換開始:', {
      colorType: options.colorType,
      canvasSize: `${actualWidth}x${actualHeight}`,
      deviceSize: `${options.width}x${options.height}`,
      imagecode: options.imagecode.toString(16),
      templateRotation: options.templateRotation || 0
    });

    // 獲取圖像數據，使用 Canvas 的實際尺寸
    const ctx = canvas.getContext('2d');
    const imageData = ctx.getImageData(0, 0, actualWidth, actualHeight);

    // 記錄原始尺寸
    const originalWidth = actualWidth;
    const originalHeight = actualHeight;

    // 檢查是否需要旋轉以匹配設備尺寸
    const needsRotation = needsRotationForDeviceMatch(imageData, options);
    let rotatedImageData = imageData;

    if (needsRotation) {
      console.log(`EPD轉換: canvasSize(${actualWidth}x${actualHeight})與deviceSize(${options.width}x${options.height})長寬相反，將預覽圖轉90度`);
      rotatedImageData = rotateImageData(imageData, 90);
    } else {
      console.log('EPD轉換: canvasSize與deviceSize方向一致，無需旋轉');
    }

    // 使用旋轉後的實際尺寸來創建轉換器
    const rotatedWidth = rotatedImageData.width;
    const rotatedHeight = rotatedImageData.height;

    console.log(`EPD轉換: 使用旋轉後尺寸 ${rotatedWidth}x${rotatedHeight}，原始尺寸: ${originalWidth}x${originalHeight}`);

    // 選擇適當的轉換器，使用旋轉後的圖片尺寸
    const actualOptions = {
      ...options,
      width: rotatedWidth,
      height: rotatedHeight
    };

    let converter;
    switch (options.colorType) {
      case DisplayColorType.BW:  // "Gray16"
        converter = new BWConverter(actualOptions);
        break;
      case DisplayColorType.BWR: // "Black & White & Red"
        converter = new BWRConverter(actualOptions);
        break;
      case DisplayColorType.BWRY: // "Black & White & Red & Yellow"
        converter = new BWRYConverter(actualOptions);
        break;
      default:
        throw new Error(`不支援的顏色類型: ${options.colorType}`);
    }

    // 處理每個像素
    const data = rotatedImageData.data;
    const width = rotatedImageData.width;
    const height = rotatedImageData.height;

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < converter.paddedWidth; x++) {
        if (x < width) {
          // 處理實際像素
          const index = (y * width + x) * 4;
          const pixel = {
            r: data[index],
            g: data[index + 1],
            b: data[index + 2],
            a: data[index + 3]
          };
          converter.processPixel(x, y, pixel);
        } else {
          // 填充像素（白色）
          const paddingPixel = { r: 255, g: 255, b: 255, a: 255 };
          converter.processPixel(x, y, paddingPixel);
        }
      }
    }

    // 獲取像素數據
    const pixelData = converter.getPixelData();

    // 創建 ImageInfo (使用旋轉為0度後的長寬資訊)
    const imageInfo = {
      imagecode: options.imagecode,
      x: options.x || 0,
      y: options.y || 0,
      width: converter.paddedWidth,  // 使用旋轉為0度後的對齊寬度
      height: converter.height       // 使用旋轉為0度後的高度
    };

    console.log(`EPD轉換: 創建ImageInfo - 寬度: ${imageInfo.width}, 高度: ${imageInfo.height} (旋轉為0度後的尺寸)`);

    // 組合最終的 rawdata
    const rawdata = combineRawData(imageInfo, pixelData);

    const endTime = Date.now();
    const processingTime = endTime - startTime;

    console.log('EPD轉換成功:', {
      totalBytes: rawdata.length,
      imageInfoBytes: 12,
      pixelDataBytes: pixelData.length,
      processingTime: `${processingTime}ms`
    });

    return {
      success: true,
      rawdata,
      imageInfo,
      pixelData,
      metadata: {
        originalSize: { width: originalWidth, height: originalHeight },
        finalSize: { width: converter.paddedWidth, height: converter.height },
        totalBytes: rawdata.length,
        imageInfoBytes: 12,
        pixelDataBytes: pixelData.length,
        processingTime,
        colorType: options.colorType
      }
    };

  } catch (error) {
    const endTime = Date.now();
    const processingTime = endTime - startTime;

    console.error('EPD轉換失敗:', error);

    return {
      success: false,
      error: error.message,
      metadata: {
        originalSize: { width: canvas.width || 0, height: canvas.height || 0 },
        finalSize: { width: 0, height: 0 },
        totalBytes: 0,
        imageInfoBytes: 0,
        pixelDataBytes: 0,
        processingTime,
        colorType: options.colorType
      }
    };
  }
}

module.exports = {
  DisplayColorType,
  getColorPalette,
  findExactColorMatch,
  calculatePaddedWidth,
  getTemplateRotation,
  getTemplateReverseRotation,
  needsRotationForDeviceMatch,
  rotateImageData,
  createCanvasFromImageData,
  serializeImageInfo,
  combineRawData,
  BWConverter,
  BWRConverter,
  BWRYConverter,
  convertImageToEPDRawData
};
