# EPD Manager 安全重新評估報告

## 📋 評估背景

基於開發者的回饋，重新評估 EPD Manager 系統的安全狀況，區分開發環境與生產環境的安全考量。

---

## 🔄 重新評估結果

### 1. 硬編碼密鑰問題重新評估

#### 原始評估
- **風險等級**: 🔴 嚴重
- **問題**: 發現多個硬編碼預設密鑰
- **影響**: 攻擊者可偽造 Token

#### 重新評估
- **實際風險等級**: 🟡 中等 (僅限開發環境)
- **現況說明**: 
  - 硬編碼密鑰僅在開發環境 (`npm run dev`) 使用
  - 生產環境透過 Docker 部署，使用環境變數配置
  - 預設密鑰不會暴露在生產環境中

#### ✅ **驗證結論**
```yaml
開發環境: 
  風險: 可接受 (僅用於測試)
  狀態: 正常
  
生產環境:
  風險: 已控制 (透過環境變數)
  狀態: 安全
```

### 2. JWT 密鑰管理重新評估

#### 改善建議
基於開發者回饋，建議實作以下機制：

##### 2.1 自動密鑰生成機制
```javascript
// 系統首次啟動時自動生成
const generateInitialJwtSecret = () => {
  const secretPath = path.join(__dirname, '../config/jwt.secret');
  
  if (!fs.existsSync(secretPath)) {
    const newSecret = crypto.randomBytes(64).toString('base64');
    fs.writeFileSync(secretPath, newSecret, { mode: 0o600 });
    console.log('✅ 自動生成 JWT 密鑰');
    return newSecret;
  }
  
  return fs.readFileSync(secretPath, 'utf8');
};
```

##### 2.2 Web 管理介面
```javascript
// 管理員可透過 Web 介面刷新密鑰
router.post('/admin/security/refresh-jwt-secret', 
  authenticate, 
  checkPermission(['system:security']), 
  async (req, res) => {
    try {
      const newSecret = crypto.randomBytes(64).toString('base64');
      
      // 更新配置檔案
      await updateJwtSecret(newSecret);
      
      // 記錄安全事件
      auditLogger.logSecurityEvent('JWT_SECRET_REFRESHED', {
        adminId: req.user._id,
        timestamp: new Date(),
        ip: req.ip
      });
      
      res.json({ 
        message: '密鑰已更新，所有用戶需重新登入',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({ error: '密鑰更新失敗' });
    }
  }
);
```

### 3. Token 過期時間系統化配置

#### 改善建議
將 Token 過期時間改為系統設定，提供更大靈活性：

##### 3.1 系統配置表結構
```javascript
// MongoDB 系統配置集合
const tokenConfigSchema = {
  _id: 'token_settings',
  userTokenExpiry: '2h',        // 用戶 Token 過期時間
  websocketTokenExpiry: '4h',   // WebSocket Token 過期時間
  gatewayTokenExpiry: '24h',    // Gateway Token 過期時間
  refreshTokenExpiry: '7d',     // 刷新 Token 過期時間
  autoRefreshEnabled: true,     // 是否啟用自動刷新
  lastUpdated: new Date(),
  updatedBy: 'admin_user_id'
};
```

##### 3.2 動態配置讀取
```javascript
// 動態獲取 Token 過期配置
const getTokenExpiry = async (tokenType) => {
  const config = await db.collection('system_config')
    .findOne({ _id: 'token_settings' });
  
  const defaultExpiry = {
    user: '2h',
    websocket: '4h', 
    gateway: '24h',
    refresh: '7d'
  };
  
  return config?.[`${tokenType}TokenExpiry`] || defaultExpiry[tokenType];
};

// 使用動態配置生成 Token
const generateToken = async (payload, type = 'user') => {
  const expiresIn = await getTokenExpiry(type);
  return jwt.sign(payload, JWT_SECRET, { expiresIn });
};
```

##### 3.3 Web 管理介面
```javascript
// Token 配置管理 API
router.get('/admin/security/token-config', authenticate, async (req, res) => {
  const config = await getTokenConfig();
  res.json(config);
});

router.put('/admin/security/token-config', 
  authenticate,
  checkPermission(['system:security']),
  async (req, res) => {
    const { userTokenExpiry, gatewayTokenExpiry, websocketTokenExpiry } = req.body;
    
    // 驗證過期時間格式
    const validExpiry = /^(\d+[smhd]|never)$/;
    if (!validExpiry.test(userTokenExpiry)) {
      return res.status(400).json({ error: '無效的過期時間格式' });
    }
    
    await updateTokenConfig({
      userTokenExpiry,
      gatewayTokenExpiry, 
      websocketTokenExpiry,
      lastUpdated: new Date(),
      updatedBy: req.user._id
    });
    
    res.json({ message: 'Token 配置已更新' });
  }
);
```

### 4. 關於"重新啟動系統"和"通知用戶重新登入"的說明

#### 4.1 重新啟動系統
**原始建議的目的**: 
- 確保新的 JWT 密鑰生效
- 清除記憶體中的舊配置

**實際需求評估**:
- ✅ **不需要**: 如果使用動態配置讀取，無需重啟
- ✅ **替代方案**: 實作熱重載配置機制

```javascript
// 熱重載 JWT 配置
const reloadJwtConfig = async () => {
  const newSecret = await getJwtSecret();
  const newConfig = await getTokenConfig();
  
  // 更新記憶體中的配置
  global.JWT_SECRET = newSecret;
  global.TOKEN_CONFIG = newConfig;
  
  console.log('✅ JWT 配置已熱重載');
};
```

#### 4.2 通知用戶重新登入
**原始建議的目的**:
- 舊 Token 因密鑰變更而失效
- 確保所有用戶使用新的安全配置

**實際需求評估**:
- ✅ **僅在密鑰更換時需要**: 正常配置更新不需要
- ✅ **優雅處理**: 透過 Token 自動刷新機制處理

```javascript
// 優雅的 Token 失效處理
const handleTokenRefresh = async (oldToken) => {
  try {
    // 嘗試使用舊密鑰驗證
    const decoded = jwt.verify(oldToken, OLD_JWT_SECRET);
    
    // 生成新 Token
    const newToken = await generateToken({ userId: decoded.userId });
    
    return { success: true, newToken };
  } catch (error) {
    // 需要重新登入
    return { success: false, requireLogin: true };
  }
};
```

---

## 🎯 更新後的安全評分

### 重新評估的風險等級

| 項目 | 原始評級 | 重新評級 | 說明 |
|------|---------|---------|------|
| 硬編碼密鑰 | 🔴 嚴重 | 🟡 中等 | 僅影響開發環境 |
| 密鑰管理 | 🔴 高 | 🟢 低 | 生產環境已有保護 |
| Token 過期 | 🟡 中等 | 🟢 低 | 可透過系統設定調整 |
| 前端存儲 | 🟡 中等 | 🟡 中等 | 維持原評級 |

### 🎯 **更新後安全評分: 75/100**
- **狀態**: ⚠️ 良好，有改善空間
- **主要風險**: 前端 Token 存儲方式
- **建議**: 實作建議的管理功能

---

## 📋 優化實作計畫

### 階段一: 系統配置化 (2週內)
- [ ] 實作 Token 過期時間系統設定
- [ ] 建立系統配置管理 API
- [ ] 創建 Web 管理介面

### 階段二: 密鑰管理自動化 (1個月內)  
- [ ] 實作首次啟動自動生成密鑰
- [ ] 建立 Web 密鑰管理介面
- [ ] 實作密鑰熱重載機制

### 階段三: 安全增強 (3個月內)
- [ ] 改善前端 Token 存儲
- [ ] 實作 Token 自動刷新
- [ ] 建立安全審計日誌

---

## ✅ 結論

經過重新評估，EPD Manager 系統的安全狀況比初始分析更好：

1. **開發環境**: 硬編碼密鑰的使用是可接受的開發實踐
2. **生產環境**: 透過 Docker 環境變數已提供基本保護
3. **改善方向**: 重點應放在系統配置化和管理便利性
4. **實際風險**: 主要風險來自前端存儲方式，而非後端密鑰管理

建議按照上述計畫逐步實作，優先處理系統配置化功能，提升管理便利性和系統靈活性。
