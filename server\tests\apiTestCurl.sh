#!/bin/bash

# 測試 API 端點的 curl 命令
# 注意：這些命令需要在服務器運行的情況下執行

# 獲取 JWT token（需要先登入）
# curl -X POST http://localhost:3001/api/auth/login -H "Content-Type: application/json" -d '{"username":"root","password":"123456789"}' -c cookies.txt

# 使用 token 測試 API

echo "測試創建門店 API"
curl -X POST http://localhost:3001/api/stores \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{"id":"TEST002","name":"API測試門店","address":"API測試地址","status":"active"}'

echo -e "\n\n測試獲取門店 API"
curl -X GET http://localhost:3001/api/stores/TEST002 \
  -H "Content-Type: application/json" \
  -b cookies.txt

echo -e "\n\n測試創建門店專屬數據 API"
curl -X POST http://localhost:3001/api/storeData \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{"storeId":"TEST002","id":"API001","name":"API測試商品","price":"300","quantity":"5"}'

echo -e "\n\n測試獲取門店專屬數據 API"
curl -X GET http://localhost:3001/api/storeData?storeId=TEST002 \
  -H "Content-Type: application/json" \
  -b cookies.txt

echo -e "\n\n測試更新門店專屬數據 API"
# 注意：需要替換 {sn} 為實際的序號
# curl -X PUT http://localhost:3001/api/storeData/{sn}?storeId=TEST002 \
#   -H "Content-Type: application/json" \
#   -b cookies.txt \
#   -d '{"price":"400"}'

echo -e "\n\n測試刪除門店專屬數據 API"
# 注意：需要替換 {sn} 為實際的序號
# curl -X DELETE http://localhost:3001/api/storeData/{sn}?storeId=TEST002 \
#   -H "Content-Type: application/json" \
#   -b cookies.txt

echo -e "\n\n測試刪除門店 API"
curl -X DELETE http://localhost:3001/api/stores/TEST002 \
  -H "Content-Type: application/json" \
  -b cookies.txt

echo -e "\n\n測試完成"
