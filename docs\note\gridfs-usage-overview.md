# GridFS 使用情況總覽

## 📋 文檔目的

本文檔提供 EPD Manager 系統中 GridFS 使用情況的快速總覽，方便開發人員和系統管理員了解檔案存儲的整體架構。

## 🎯 GridFS 在系統中的角色

GridFS 是 EPD Manager 系統的**統一檔案存儲解決方案**，負責處理所有大於 16MB 或需要特殊處理的檔案。

### 為什麼選擇 GridFS？

| 需求 | 傳統方案 | GridFS 方案 |
|------|----------|-------------|
| **大檔案存儲** | 檔案系統 + 路徑引用 | 直接存儲在 MongoDB |
| **檔案完整性** | 手動校驗 | 自動分塊校驗 |
| **元數據管理** | 額外數據表 | 內建元數據支援 |
| **串流處理** | 複雜實現 | 原生串流 API |
| **備份一致性** | 檔案與數據分離 | 統一備份策略 |

## 📊 系統中的 GridFS 使用統計

### 檔案類型分布

```
📁 EPD Manager GridFS 存儲
├── 🖼️ 圖片檔案 (60%)
│   ├── 模板編輯器圖片
│   ├── Bug 報告附件
│   └── 用戶上傳圖片
├── 💾 韌體檔案 (35%)
│   ├── Gateway 韌體 (WiFi/BLE)
│   ├── EPD 韌體 (BLE)
│   └── 純韌體提取檔案
└── 📄 其他檔案 (5%)
    ├── 系統日誌
    ├── 報告文檔
    └── 配置備份
```

### 存儲容量分析

| 檔案類型 | 平均大小 | 數量估計 | 總容量估計 |
|----------|----------|----------|------------|
| 模板圖片 | 500KB | 1,000+ | ~500MB |
| Bug報告圖片 | 2MB | 100+ | ~200MB |
| Gateway韌體 | 5MB | 50+ | ~250MB |
| EPD韌體 | 3MB | 30+ | ~90MB |
| **總計** | - | **1,180+** | **~1.04GB** |

## 🏗️ 架構概覽

### 數據庫集合結構

```
MongoDB Database: resourceManagement
│
├── 📋 業務數據集合
│   ├── software (軟體元數據)
│   ├── bugReports (Bug報告)
│   ├── templates (模板數據)
│   └── users (用戶數據)
│
└── 📁 GridFS 集合
    ├── fs.files (檔案元數據)
    │   ├── filename, length, uploadDate
    │   └── metadata: { type, mimetype, uploadedBy, ... }
    └── fs.chunks (檔案內容分塊)
        ├── files_id (關聯到 fs.files)
        ├── n (分塊序號)
        └── data (實際檔案內容)
```

### 檔案關聯關係

```mermaid
graph TD
    A[業務集合] --> B[GridFS 檔案ID]
    B --> C[fs.files]
    C --> D[fs.chunks]
    
    E[software] --> F[binFileId]
    E --> G[extractedBinId]
    F --> C
    G --> C
    
    H[bugReports] --> I[imageId]
    I --> C
```

## 🔧 API 端點總覽

### 檔案上傳端點

| 端點 | 用途 | 檔案類型 | 大小限制 |
|------|------|----------|----------|
| `POST /api/files/upload` | 一般檔案上傳 | 圖片、文檔 | 預設 |
| `POST /api/files/upload-multiple` | 批次檔案上傳 | 圖片、文檔 | 預設 |
| `POST /api/software/upload` | 軟體韌體上傳 | .bin 檔案 | 100MB |
| `POST /api/bug-reports` | Bug報告附件 | 圖片 | 10MB |

### 檔案下載端點

| 端點 | 用途 | 回應類型 |
|------|------|----------|
| `GET /api/files/:id` | 一般檔案下載 | 檔案串流 |
| `GET /api/software/:id/download` | 軟體檔案下載 | 檔案串流 |
| `GET /api/software/:id/download?type=pure` | 純韌體下載 | 檔案串流 |

## 📱 前端整合

### 檔案顯示

```typescript
// 圖片顯示 - 統一的 URL 格式
const imageUrl = buildEndpointUrl('files', fileId);

// 使用範例
<img src={imageUrl} alt="圖片" />
```

### 檔案上傳

```typescript
// FormData 上傳
const formData = new FormData();
formData.append('file', file);

const response = await fetch('/api/files/upload', {
  method: 'POST',
  body: formData
});
```

### 檔案下載

```typescript
// 程式化下載
const response = await fetch(`/api/software/${id}/download`);
const blob = await response.blob();

// 觸發瀏覽器下載
const url = URL.createObjectURL(blob);
const a = document.createElement('a');
a.href = url;
a.download = filename;
a.click();
```

## 🔒 安全性實現

### 檔案驗證層級

```
📋 多層檔案驗證
├── 1️⃣ 檔案類型驗證 (副檔名 + MIME)
├── 2️⃣ 檔案大小限制
├── 3️⃣ 內容格式驗證 (如 bin 檔案格式)
├── 4️⃣ CRC 校驗和驗證
└── 5️⃣ 權限存取控制
```

### 權限控制

| 操作 | 所需權限 | 檢查點 |
|------|----------|--------|
| 檔案上傳 | `file:create` | API 中間件 |
| 檔案下載 | `file:read` | API 中間件 |
| 檔案刪除 | `file:delete` | API 中間件 |
| 軟體管理 | `software:*` | 專用權限 |

## 📈 效能特性

### 索引策略

```javascript
// 自動創建的索引
db.fs.files.getIndexes()
[
  { "filename": 1 },
  { "uploadDate": -1 },
  { "metadata.type": 1 },
  { "metadata.uploadedBy": 1 }
]

db.fs.chunks.getIndexes()
[
  { "files_id": 1, "n": 1 }  // GridFS 核心索引
]
```

### 效能優化措施

- ✅ **串流處理**: 大檔案不載入記憶體
- ✅ **分塊存儲**: 支援斷點續傳
- ✅ **索引優化**: 快速檔案查詢
- ✅ **快取策略**: 元數據快取
- ✅ **批次操作**: 減少網路往返

## 🛠️ 維護工具

### 管理命令

```javascript
// 檢查 GridFS 狀態
db.fs.files.stats()

// 查看檔案類型分布
db.fs.files.aggregate([
  { $group: { _id: "$metadata.type", count: { $sum: 1 } } }
])

// 查找大檔案
db.fs.files.find({ length: { $gt: 10 * 1024 * 1024 } })

// 清理孤立檔案
// (需要自定義腳本)
```

### 監控指標

- 📊 **存儲使用量**: 總大小、檔案數量
- ⏱️ **操作效能**: 上傳/下載速度
- 🚨 **錯誤率**: 失敗操作統計
- 🔄 **清理效果**: 孤立檔案清理

## 🔮 未來規劃

### 短期改進 (1-3個月)

- [ ] 檔案縮圖生成
- [ ] 檔案預覽功能
- [ ] 批次檔案操作 UI
- [ ] 檔案使用統計

### 中期規劃 (3-6個月)

- [ ] 檔案版本控制
- [ ] 自動檔案壓縮
- [ ] CDN 整合
- [ ] 檔案同步機制

### 長期願景 (6個月+)

- [ ] 分散式檔案存儲
- [ ] 檔案加密存儲
- [ ] 智能檔案分類
- [ ] 檔案生命週期管理

## 📞 常見問題

### Q: 為什麼不使用檔案系統存儲？

**A**: GridFS 提供更好的一致性、備份便利性和 MongoDB 生態整合。

### Q: GridFS 的效能如何？

**A**: 對於大檔案和串流操作，GridFS 效能優異。小檔案建議直接存儲在文檔中。

### Q: 如何備份 GridFS 檔案？

**A**: 使用 MongoDB 標準備份工具 (mongodump/mongorestore) 即可包含 GridFS 檔案。

### Q: 檔案刪除後如何恢復？

**A**: 需要從備份恢復。建議實施軟刪除策略用於重要檔案。

## 📚 相關文檔

- [GridFS 架構詳細文檔](./gridfs-architecture.md)
- [檔案存儲最佳實踐](./file-storage-best-practices.md)
- [軟體管理系統設計](../plan/software-management/system-design.md)

---

**文檔版本**: 1.0.0  
**最後更新**: 2024-12-20  
**維護者**: EPD Manager 開發團隊

**快速連結**:
- 🔧 [API 文檔](../api/)
- 🏗️ [系統架構](../architecture/)
- 🚀 [部署指南](../deployment/)
