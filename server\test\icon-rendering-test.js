const { renderIconSvg, getSupportedIconTypes, isIconTypeSupported, convertIconName } = require('../utils/iconRenderer');
const fs = require('fs');
const path = require('path');

console.log('=== 開始測試圖標渲染功能 ===\n');

// 測試所有支援的圖標類型
const supportedIcons = getSupportedIconTypes();
console.log(`支援的圖標類型數量: ${supportedIcons.length}`);
console.log(`支援的圖標類型: ${supportedIcons.join(', ')}\n`);

// 測試圖標名稱轉換
console.log('=== 測試圖標名稱轉換 ===');
const testMappings = [
  'alert-circle',
  'check-circle', 
  'x-circle',
  'star',
  'heart',
  'arrow-up'
];

testMappings.forEach(iconType => {
  const converted = convertIconName(iconType);
  console.log(`${iconType} -> ${converted}`);
});
console.log('');

// 測試圖標渲染
console.log('=== 測試圖標渲染 ===');
let successCount = 0;
let failCount = 0;

supportedIcons.forEach(iconType => {
  try {
    const svg = renderIconSvg(iconType, {
      size: 24,
      color: '#000',
      strokeWidth: 2
    });
    
    if (svg && svg.includes('<svg') && svg.includes('</svg>')) {
      console.log(`✓ ${iconType}: 渲染成功`);
      successCount++;
    } else {
      console.log(`✗ ${iconType}: 渲染失敗 - 無效的 SVG`);
      failCount++;
    }
  } catch (error) {
    console.log(`✗ ${iconType}: 發生錯誤 - ${error.message}`);
    failCount++;
  }
});

console.log(`\n=== 測試結果 ===`);
console.log(`成功: ${successCount}`);
console.log(`失敗: ${failCount}`);
console.log(`總計: ${successCount + failCount}`);

// 測試不同參數組合
console.log('\n=== 測試不同參數組合 ===');
const testParams = [
  { size: 16, color: '#ff0000', strokeWidth: 1 },
  { size: 32, color: '#00ff00', strokeWidth: 3 },
  { size: 48, color: '#0000ff', strokeWidth: 4 }
];

testParams.forEach((params, index) => {
  try {
    const svg = renderIconSvg('star', params);
    const hasCorrectSize = svg.includes(`width="${params.size}"`) && svg.includes(`height="${params.size}"`);
    const hasCorrectColor = svg.includes(`stroke="${params.color}"`);
    const hasCorrectStrokeWidth = svg.includes(`stroke-width="${params.strokeWidth}"`);
    
    if (hasCorrectSize && hasCorrectColor && hasCorrectStrokeWidth) {
      console.log(`✓ 參數組合 ${index + 1}: 所有屬性正確應用`);
    } else {
      console.log(`✗ 參數組合 ${index + 1}: 部分屬性未正確應用`);
      console.log(`  尺寸: ${hasCorrectSize}, 顏色: ${hasCorrectColor}, 線條寬度: ${hasCorrectStrokeWidth}`);
    }
  } catch (error) {
    console.log(`✗ 參數組合 ${index + 1}: 發生錯誤 - ${error.message}`);
  }
});

// 測試錯誤處理
console.log('\n=== 測試錯誤處理 ===');
const invalidIcons = ['invalid-icon', 'non-existent', ''];

invalidIcons.forEach(iconType => {
  try {
    const svg = renderIconSvg(iconType);
    if (svg && svg.includes('<svg')) {
      console.log(`✓ ${iconType || '(空字符串)'}: 錯誤處理正常，返回備用圖標`);
    } else {
      console.log(`✗ ${iconType || '(空字符串)'}: 錯誤處理失敗`);
    }
  } catch (error) {
    console.log(`✗ ${iconType || '(空字符串)'}: 發生未處理的錯誤 - ${error.message}`);
  }
});

// 測試支援檢查功能
console.log('\n=== 測試支援檢查功能 ===');
const checkIcons = ['star', 'invalid-icon', 'heart', 'non-existent'];

checkIcons.forEach(iconType => {
  const isSupported = isIconTypeSupported(iconType);
  console.log(`${iconType}: ${isSupported ? '支援' : '不支援'}`);
});

// 輸出一個範例 SVG 用於檢查
console.log('\n=== 範例 SVG 輸出 ===');
try {
  const exampleSvg = renderIconSvg('star', { size: 24, color: '#333', strokeWidth: 2 });
  console.log('Star 圖標 SVG:');
  console.log(exampleSvg);
} catch (error) {
  console.log(`無法生成範例 SVG: ${error.message}`);
}

console.log('\n=== 測試完成 ===');
