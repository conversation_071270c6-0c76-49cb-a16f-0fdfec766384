# Template Editor 顏色限制功能實現

## 功能概述

實現了 template editor 中所有元素根據 template 的 colortype 限制可選顏色的功能。用戶現在只能選擇與模板顏色類型兼容的顏色，確保設計的模板能在目標設備上正確顯示。

## 實現的功能

### 1. 顏色限制工具函數 (`src/utils/colorConversion/index.ts`)

- **`getAvailableColorsForColorType(colorType)`**: 根據顏色類型獲取可用的 hex 顏色列表
- **`isColorValidForColorType(color, colorType)`**: 檢查顏色是否在指定 colortype 的可用範圍內
- **`mapColorToAvailableColor(color, colorType)`**: 將任意顏色映射到最接近的可用顏色

#### 支援的顏色類型：
- **Gray16/BW**: 16 個灰度級別 (從 #000000 到 #FFFFFF)
- **BWR (Black & White & Red)**: 黑色、白色、紅色
- **BWRY (Black & White & Red & Yellow)**: 黑色、白色、紅色、黃色
- **其他類型**: 根據調色板定義的顏色

### 2. 受限制的顏色選擇器組件 (`src/components/editor/properties/RestrictedColorInput.tsx`)

- 替代原有的 `ColorInput` 組件
- 顯示顏色按鈕網格而不是顏色選擇器
- 支援透明選項
- 顯示顏色名稱和 hex 值
- 提供視覺化的顏色選擇界面
- 當前選中顏色的高亮顯示

#### 特色功能：
- 透明顏色的棋盤格背景顯示
- 灰度顏色的級別標示
- 顏色類型信息顯示
- 響應式網格佈局

### 3. 更新的屬性面板組件

所有包含顏色設定的屬性面板都已更新：

#### 文字屬性 (`TextProperties.tsx`)
- 文字顏色限制

#### 線條屬性 (`LineProperties.tsx`)
- 線條顏色限制

#### 圖標屬性 (`IconProperties.tsx`)
- 圖標顏色限制

#### 矩形屬性 (`RectangleProperties.tsx`) - 新增
- 線條顏色和填充顏色限制

#### 圓形屬性 (`CircleProperties.tsx`) - 新增
- 線條顏色和填充顏色限制

#### 預設屬性 (`DefaultProperties.tsx`)
- 預設線條顏色限制

### 4. 元素屬性面板整合 (`ElementPropertiesPanel.tsx`)

- 添加 `colorType` 參數支援
- 將模板的顏色類型傳遞給所有屬性組件
- 支援矩形和圓形元素的專門屬性面板

### 5. 模板編輯器整合 (`TemplateEditor.tsx`)

- 將 `selectedTemplate.color` 傳遞給 `ElementPropertiesPanel`
- 確保所有元素屬性都能獲得顏色類型信息

## 使用方式

### 1. 在模板編輯器中
1. 選擇任意元素
2. 在屬性面板中點擊顏色設定
3. 只能選擇與模板 colortype 兼容的顏色
4. 系統會自動顯示可用顏色的網格

### 2. 顏色自動映射
- 如果元素當前顏色不在可用範圍內，系統會自動映射到最接近的可用顏色
- 用戶可以看到映射後的結果並進行調整

### 3. 透明支援
- 所有顏色選擇器都支援透明選項
- 透明顏色以棋盤格背景顯示

## 技術實現細節

### 顏色轉換邏輯
```typescript
// 獲取可用顏色
const availableColors = getAvailableColorsForColorType('BWR');
// 結果: ['#000000', '#FFFFFF', '#FF0000']

// 顏色映射
const mappedColor = mapColorToAvailableColor('#FF5555', 'BWR');
// 結果: '#FF0000' (最接近的紅色)
```

### 組件使用
```tsx
<RestrictedColorInput
  value={element.lineColor || '#000000'}
  onChange={(value) => updateElement({ lineColor: value })}
  colorType={template.color}
/>
```

## 驗證

創建了驗證文件 `test-color-restriction.html` 來驗證：
- 不同顏色類型的可用顏色列表
- 顏色映射功能
- 視覺化顏色顯示

## 向後兼容性

- 如果沒有提供 `colorType`，組件會回退到原有的顏色選擇器
- 現有的模板和元素不會受到影響
- 漸進式增強，不破壞現有功能

## 未來擴展

1. **更多顏色類型支援**: 可以輕鬆添加新的顏色類型
2. **顏色預覽**: 可以添加在設備上的顏色預覽效果
3. **顏色建議**: 可以提供顏色搭配建議
4. **批量顏色更新**: 可以實現一鍵更新所有元素顏色

## 色彩工具功能

為了方便驗證和檢查功能，我們添加了：

### 1. 獨立色彩工具頁面
- **色彩工具組件**: `src/components/test/ColorTool.tsx`
- **訪問方式**: 在應用中點擊側邊欄的「色彩工具」選項
- **功能**:
  - 實時切換不同顏色類型
  - 查看每種顏色類型的可用顏色
  - 驗證顏色選擇器的交互
  - 對比不同顏色類型的差異

### 2. 靜態驗證頁面
- **文件**: `test-color-restriction.html`
- **訪問方式**: 直接在瀏覽器中打開
- **功能**: 驗證顏色轉換邏輯和顏色映射

## 實際使用流程

1. **創建模板**: 在模板創建時選擇適當的顏色類型
2. **編輯元素**: 在模板編輯器中添加元素
3. **設定顏色**: 點擊元素的顏色設定，只能選擇兼容的顏色
4. **即時反饋**: 系統會自動將不兼容的顏色映射到最接近的可用顏色

## 相關文件

- `src/utils/colorConversion/index.ts` - 顏色轉換工具函數
- `src/components/editor/properties/RestrictedColorInput.tsx` - 受限制顏色選擇器
- `src/components/editor/properties/*.tsx` - 各種屬性面板組件
- `src/components/editor/ElementPropertiesPanel.tsx` - 元素屬性面板
- `src/components/TemplateEditor.tsx` - 模板編輯器
- `src/components/test/ColorTool.tsx` - 色彩工具組件
- `test-color-restriction.html` - 功能驗證文件
- `src/components/Sidebar.tsx` - 側邊欄（添加色彩工具選項）
- `src/App.tsx` - 主應用（添加色彩工具路由）
- `src/i18n/locales/*.json` - 翻譯文件（添加色彩工具選項翻譯）
