import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, AlertCircle, Bluetooth } from 'lucide-react';
import { upgradeBtFirmware } from '../utils/api/gatewayApi';

interface UpgradeBtFirmwareModalProps {
  isOpen: boolean;
  selectedGateways: string[];
  storeId?: string; // 當前選中的門店ID
  onClose: () => void;
  onSuccess: () => void;
}

export const UpgradeBtFirmwareModal: React.FC<UpgradeBtFirmwareModalProps> = ({
  isOpen,
  selectedGateways,
  storeId,
  onClose,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const [version, setVersion] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 固件版本列表（實際應用中可能需要從 API 獲取）
  const firmwareVersions = [
    { value: '1.0.0', label: 'v1.0.0 - 基礎版本' },
    { value: '1.1.0', label: 'v1.1.0 - 穩定性改進' },
    { value: '1.2.0', label: 'v1.2.0 - 功能增強' },
    { value: '2.0.0', label: 'v2.0.0 - 最新版本' },
  ];

  // 處理表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 驗證表單
    if (!version) {
      setError(t('gateways.versionRequired'));
      return;
    }

    if (selectedGateways.length === 0) {
      setError(t('gateways.noGatewaySelected'));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // 升級固件，傳遞門店ID
      await upgradeBtFirmware(selectedGateways, version, storeId);

      // 通知成功
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('升級藍芽固件失敗:', err);
      setError(err.message || t('gateways.upgradeFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 如果模態窗口未開啟，不渲染任何內容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold flex items-center">
            <Bluetooth className="mr-2" size={20} />
            {t('gateways.upgradeBtFirmware')}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          {/* 錯誤提示 */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700 flex items-center">
              <AlertCircle className="mr-2" size={20} />
              <span>{error}</span>
            </div>
          )}

          {/* 選中的網關數量 */}
          <div className="mb-4 p-3 bg-blue-50 border-l-4 border-blue-500 text-blue-700">
            {selectedGateways.length > 0 ? (
              <p>
                {t('gateways.selectedGatewaysCount', { count: selectedGateways.length })}
              </p>
            ) : (
              <p>{t('gateways.noGatewaySelected')}</p>
            )}
          </div>

          {/* 固件版本選擇 */}
          <div className="mb-4">
            <label htmlFor="version" className="block text-sm font-medium text-gray-700 mb-1">
              {t('gateways.selectFirmwareVersion')} <span className="text-red-500">*</span>
            </label>
            <select
              id="version"
              value={version}
              onChange={(e) => setVersion(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="">{t('gateways.selectVersion')}</option>
              {firmwareVersions.map((v) => (
                <option key={v.value} value={v.value}>
                  {v.label}
                </option>
              ))}
            </select>
          </div>

          {/* 警告提示 */}
          <div className="mb-4 p-3 bg-yellow-50 border-l-4 border-yellow-500 text-yellow-700">
            <p className="font-medium">{t('gateways.upgradeWarning')}</p>
            <ul className="mt-2 list-disc list-inside text-sm">
              <li>{t('gateways.upgradeWarning1')}</li>
              <li>{t('gateways.upgradeWarning2')}</li>
              <li>{t('gateways.upgradeWarning3')}</li>
            </ul>
          </div>

          {/* 按鈕 */}
          <div className="flex justify-end gap-2 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={loading}
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-75 disabled:cursor-not-allowed"
              disabled={loading || selectedGateways.length === 0 || !version}
            >
              {loading ? t('common.processing') : t('common.confirm')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
