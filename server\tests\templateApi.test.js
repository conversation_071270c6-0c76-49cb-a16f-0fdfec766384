// tests/templateApi.test.js
const request = require('supertest');
const express = require('express');
const { createMockCollection } = require('./helpers/mockDb');
const createMockTemplateApi = require('./helpers/mockTemplateApi');

// 防止實際路由檔案被引入
jest.mock('../routes/templateApi', () => {
  return {};
});

describe('模板 API 測試', () => {
  let app;
  let mockCollection;
  let router;
  beforeEach(() => {
    // 創建 Express 應用
    app = express();

    // 初始化模擬資料
    const initialData = [
      { _id: '1', name: '模板1', content: { elements: [] } },
      { _id: '2', name: '模板2', content: { elements: [] } },
    ];

    // 創建模擬集合
    mockCollection = createMockCollection(initialData);

    // 創建模擬的模板 API 路由
    router = createMockTemplateApi(mockCollection);

    // 使用模擬路由
    app.use(express.json());
    app.use('/api', router);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/templates', () => {
    test('應該返回所有模板', async () => {
      const response = await request(app).get('/api/templates');

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
      expect(mockCollection.find).toHaveBeenCalled();
    });

    test('應該處理獲取模板時的錯誤', async () => {
      // 模擬錯誤
      const mockToArray = jest.fn().mockRejectedValueOnce(new Error('資料庫錯誤'));
      mockCollection.find.mockReturnValueOnce({
        toArray: mockToArray
      });

      const response = await request(app).get('/api/templates');

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('GET /api/templates/:id', () => {
    test('應該返回指定 ID 的模板', async () => {
      const response = await request(app).get('/api/templates/1');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('name', '模板1');
      expect(mockCollection.findOne).toHaveBeenCalledWith({ _id: '1' });
    });

    test('當模板不存在時應該返回 404', async () => {
      mockCollection.findOne.mockResolvedValueOnce(null);

      const response = await request(app).get('/api/templates/999');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', '找不到模板');
    });
  });

  describe('POST /api/templates', () => {
    test('應該創建新模板', async () => {
      const newTemplate = {
        name: '新模板',
        content: { elements: [] }
      };

      // 模擬驗證函數
      router.setValidator({
        validateTemplate: jest.fn().mockReturnValue({ valid: true, errors: [] }),
        calculateChecksum: jest.fn().mockReturnValue('mock-checksum')
      });

      const response = await request(app)
        .post('/api/templates')
        .send(newTemplate);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(mockCollection.insertOne).toHaveBeenCalledWith(
        expect.objectContaining({
          name: '新模板',
          checksum: 'mock-checksum'
        })
      );
    });

    test('如果模板無效應該返回 400', async () => {
      const invalidTemplate = {
        // 缺少必要欄位
      };

      // 模擬驗證失敗
      router.setValidator({
        validateTemplate: jest.fn().mockReturnValue({
          valid: false,
          errors: ['模板缺少名稱']
        })
      });

      const response = await request(app)
        .post('/api/templates')
        .send(invalidTemplate);

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('errors');
      expect(response.body.errors).toContain('模板缺少名稱');
    });
  });

  describe('PUT /api/templates/:id', () => {
    test('應該更新現有模板', async () => {
      const updatedTemplate = {
        name: '更新的模板',
        content: { elements: [] }
      };

      // 模擬驗證函數
      router.setValidator({
        validateTemplate: jest.fn().mockReturnValue({ valid: true, errors: [] }),
        calculateChecksum: jest.fn().mockReturnValue('new-checksum')
      });

      const response = await request(app)
        .put('/api/templates/1')
        .send(updatedTemplate);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', '模板更新成功');
      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { _id: '1' },
        expect.objectContaining({
          $set: expect.objectContaining({
            name: '更新的模板',
            checksum: 'new-checksum'
          })
        })
      );
    });
  });

  describe('DELETE /api/templates/:id', () => {
    test('應該刪除模板', async () => {
      const response = await request(app).delete('/api/templates/1');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', '模板刪除成功');
      expect(mockCollection.deleteOne).toHaveBeenCalledWith({ _id: '1' });
    });

    test('當模板無法刪除時應該返回錯誤', async () => {
      mockCollection.deleteOne.mockResolvedValueOnce({ deletedCount: 0 });

      const response = await request(app).delete('/api/templates/999');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', '找不到模板或刪除失敗');
    });
  });
});
