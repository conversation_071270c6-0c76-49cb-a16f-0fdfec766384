# Server端Bin檔案解析開發指南

## 概述

本指南提供server端開發人員快速實現格式化bin檔案解析功能的方法和範例代碼。

## 檔案格式快速參考

```
Offset | Size | Description        | Format
-------|------|--------------------|--------------
0x00   | 2    | Device Type        | Little Endian
0x02   | 2    | Function Type      | Little Endian  
0x04   | 4    | Version (x.x.x.x)  | 4 bytes
0x08   | N    | Original Bin Data  | Raw bytes
N+8    | 4    | CRC32 Checksum     | Little Endian
```

## 常數定義

### JavaScript/Node.js

```javascript
// 設備類型
const DEVICE_TYPES = {
    GATEWAY: 0,
    EPD: 1
};

// 功能類型
const FUNCTION_TYPES = {
    WIFI: 0,
    BLE: 1
};

// 反向映射
const DEVICE_TYPE_NAMES = {
    0: 'gateway',
    1: 'epd'
};

const FUNCTION_TYPE_NAMES = {
    0: 'wifi',
    1: 'ble'
};
```

### Python

```python
# 設備類型
DEVICE_TYPES = {
    'GATEWAY': 0,
    'EPD': 1
}

# 功能類型
FUNCTION_TYPES = {
    'WIFI': 0,
    'BLE': 1
}

# 反向映射
DEVICE_TYPE_NAMES = {0: 'gateway', 1: 'epd'}
FUNCTION_TYPE_NAMES = {0: 'wifi', 1: 'ble'}
```

## 解析實現

### JavaScript/Node.js 實現

```javascript
const fs = require('fs');
const zlib = require('zlib');

class BinFileParser {
    constructor() {
        this.DEVICE_TYPE_NAMES = {0: 'gateway', 1: 'epd'};
        this.FUNCTION_TYPE_NAMES = {0: 'wifi', 1: 'ble'};
    }
    
    parseBinFile(filePath) {
        const buffer = fs.readFileSync(filePath);
        
        if (buffer.length < 12) {
            throw new Error('檔案太小，不是有效的格式化bin檔案');
        }
        
        // 解析標頭
        const deviceType = buffer.readUInt16LE(0);
        const functionType = buffer.readUInt16LE(2);
        const version = [
            buffer.readUInt8(4),
            buffer.readUInt8(5), 
            buffer.readUInt8(6),
            buffer.readUInt8(7)
        ].join('.');
        
        // 提取bin數據和校驗和
        const binData = buffer.slice(8, -4);
        const checksum = buffer.readUInt32LE(buffer.length - 4);
        
        // 驗證校驗和
        const calculatedChecksum = zlib.crc32(binData) >>> 0;
        const isValid = checksum === calculatedChecksum;
        
        return {
            deviceType: this.DEVICE_TYPE_NAMES[deviceType] || 'unknown',
            functionType: this.FUNCTION_TYPE_NAMES[functionType] || 'unknown',
            version: version,
            binData: binData,
            checksum: checksum,
            calculatedChecksum: calculatedChecksum,
            isValid: isValid,
            binSize: binData.length,
            totalSize: buffer.length
        };
    }
    
    // 驗證檔案是否為格式化bin檔案
    isFormattedBinFile(filePath) {
        try {
            const result = this.parseBinFile(filePath);
            return result.isValid;
        } catch (error) {
            return false;
        }
    }
    
    // 提取原始bin數據
    extractOriginalBin(filePath, outputPath) {
        const result = this.parseBinFile(filePath);
        if (!result.isValid) {
            throw new Error('校驗和驗證失敗');
        }
        fs.writeFileSync(outputPath, result.binData);
        return result;
    }
}

// 使用範例
const parser = new BinFileParser();

try {
    const result = parser.parseBinFile('gateway_wifi_*******_20241220_143022.bin');
    console.log('解析結果:', result);
    
    if (result.isValid) {
        console.log(`設備: ${result.deviceType}`);
        console.log(`功能: ${result.functionType}`);
        console.log(`版本: ${result.version}`);
        console.log(`韌體大小: ${result.binSize} bytes`);
    } else {
        console.log('校驗和驗證失敗');
    }
} catch (error) {
    console.error('解析失敗:', error.message);
}
```

### Python 實現

```python
import struct
import zlib

class BinFileParser:
    def __init__(self):
        self.device_type_names = {0: 'gateway', 1: 'epd'}
        self.function_type_names = {0: 'wifi', 1: 'ble'}
    
    def parse_bin_file(self, file_path):
        """解析格式化bin檔案"""
        with open(file_path, 'rb') as f:
            data = f.read()
        
        if len(data) < 12:
            raise ValueError('檔案太小，不是有效的格式化bin檔案')
        
        # 解析標頭
        device_type = struct.unpack('<H', data[0:2])[0]
        function_type = struct.unpack('<H', data[2:4])[0]
        version = '.'.join(str(b) for b in data[4:8])
        
        # 提取bin數據和校驗和
        bin_data = data[8:-4]
        checksum = struct.unpack('<I', data[-4:])[0]
        
        # 驗證校驗和
        calculated_checksum = zlib.crc32(bin_data) & 0xffffffff
        is_valid = checksum == calculated_checksum
        
        return {
            'device_type': self.device_type_names.get(device_type, 'unknown'),
            'function_type': self.function_type_names.get(function_type, 'unknown'),
            'version': version,
            'bin_data': bin_data,
            'checksum': checksum,
            'calculated_checksum': calculated_checksum,
            'is_valid': is_valid,
            'bin_size': len(bin_data),
            'total_size': len(data)
        }
    
    def is_formatted_bin_file(self, file_path):
        """檢查是否為格式化bin檔案"""
        try:
            result = self.parse_bin_file(file_path)
            return result['is_valid']
        except:
            return False
    
    def extract_original_bin(self, file_path, output_path):
        """提取原始bin數據"""
        result = self.parse_bin_file(file_path)
        if not result['is_valid']:
            raise ValueError('校驗和驗證失敗')
        
        with open(output_path, 'wb') as f:
            f.write(result['bin_data'])
        
        return result

# 使用範例
parser = BinFileParser()

try:
    result = parser.parse_bin_file('gateway_wifi_*******_20241220_143022.bin')
    print('解析結果:', result)
    
    if result['is_valid']:
        print(f"設備: {result['device_type']}")
        print(f"功能: {result['function_type']}")
        print(f"版本: {result['version']}")
        print(f"韌體大小: {result['bin_size']} bytes")
    else:
        print('校驗和驗證失敗')
        
except Exception as e:
    print(f'解析失敗: {e}')
```

## API設計建議

### RESTful API端點

```javascript
// Express.js 範例
const express = require('express');
const multer = require('multer');
const app = express();
const upload = multer({ dest: 'uploads/' });

// 上傳並解析bin檔案
app.post('/api/firmware/upload', upload.single('binFile'), (req, res) => {
    try {
        const parser = new BinFileParser();
        const result = parser.parseBinFile(req.file.path);
        
        if (!result.isValid) {
            return res.status(400).json({
                error: '校驗和驗證失敗',
                details: result
            });
        }
        
        res.json({
            success: true,
            firmware: {
                deviceType: result.deviceType,
                functionType: result.functionType,
                version: result.version,
                size: result.binSize
            }
        });
    } catch (error) {
        res.status(400).json({
            error: '檔案解析失敗',
            message: error.message
        });
    }
});

// 獲取韌體信息
app.get('/api/firmware/:id/info', (req, res) => {
    // 從資料庫或檔案系統獲取韌體檔案
    // 解析並返回信息
});
```

## 資料庫儲存建議

### MongoDB Schema

```javascript
const firmwareSchema = {
    filename: String,
    deviceType: String,      // 'gateway', 'epd'
    functionType: String,    // 'wifi', 'ble'
    version: String,         // '*******'
    binSize: Number,
    checksum: String,        // hex格式
    uploadDate: Date,
    isValid: Boolean,
    filePath: String
};
```

### MySQL Schema

```sql
CREATE TABLE firmware (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    device_type ENUM('gateway', 'epd') NOT NULL,
    function_type ENUM('wifi', 'ble') NOT NULL,
    version VARCHAR(15) NOT NULL,
    bin_size INT NOT NULL,
    checksum VARCHAR(8) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_valid BOOLEAN NOT NULL,
    file_path VARCHAR(500) NOT NULL
);
```

## 錯誤處理

### 常見錯誤類型

1. **檔案格式錯誤**: 檔案太小或格式不正確
2. **校驗和失敗**: 檔案可能已損壞
3. **不支援的設備/功能類型**: 未知的類型代碼
4. **版本格式錯誤**: 版本信息無法解析

### 錯誤回應格式

```javascript
{
    "error": "檔案解析失敗",
    "code": "PARSE_ERROR",
    "details": {
        "reason": "校驗和驗證失敗",
        "expected": "0x12345678",
        "actual": "0x87654321"
    }
}
```

## 效能考量

1. **大檔案處理**: 使用串流處理避免記憶體溢出
2. **快取**: 快取解析結果避免重複計算
3. **非同步處理**: 使用非同步I/O處理檔案操作
4. **批次處理**: 支援批次上傳和解析

## 安全建議

1. **檔案大小限制**: 設定合理的檔案大小上限
2. **檔案類型驗證**: 檢查檔案副檔名和MIME類型
3. **路徑安全**: 防止路徑遍歷攻擊
4. **權限控制**: 實施適當的存取權限控制
