/**
 * 端到端測試：測試完整的 rawdata 格式處理流程
 */

const compression = require('./index.js');

async function runE2ETest() {
  console.log('🚀 開始端到端測試...\n');
  
  try {
    // 測試數據：模擬 EPD 圖像數據
    const testCases = [
      {
        name: '大面積單色數據',
        data: new Uint8Array(1000).fill(0xFF),
        expectedGoodCompression: true
      },
      {
        name: '混合重複數據',
        data: new Uint8Array([
          ...new Array(100).fill(0x00),
          ...new Array(50).fill(0xFF),
          0x01, 0x02, 0x03, 0x04, 0x05,
          ...new Array(200).fill(0x80),
          0x10, 0x20, 0x30
        ]),
        expectedGoodCompression: true
      },
      {
        name: '隨機數據',
        data: new Uint8Array(Array.from({length: 500}, (_, i) => i % 256)),
        expectedGoodCompression: false
      },
      {
        name: '小數據',
        data: new Uint8Array([0xFF, 0xFF, 0x00, 0x01]),
        expectedGoodCompression: false
      }
    ];
    
    console.log('📊 測試案例概覽:');
    testCases.forEach((testCase, index) => {
      console.log(`  ${index + 1}. ${testCase.name} (${testCase.data.length} bytes)`);
    });
    console.log('');
    
    // 測試每個案例
    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`🧪 測試案例 ${i + 1}: ${testCase.name}`);
      console.log(`   數據大小: ${testCase.data.length} bytes`);
      
      // 測試 rawdata 格式
      console.log('   📄 測試 rawdata 格式...');
      const rawResult = compression.processPixelData('rawdata', testCase.data);
      console.log(`      ✅ 成功: ${rawResult.success}`);
      console.log(`      📏 大小: ${rawResult.originalSize} → ${rawResult.processedSize} bytes`);
      console.log(`      📊 比率: ${(rawResult.processingRatio * 100).toFixed(1)}%`);
      
      // 測試 runlendata 格式
      console.log('   🗜️  測試 runlendata 格式...');
      const compressedResult = compression.processPixelData('runlendata', testCase.data);
      console.log(`      ✅ 成功: ${compressedResult.success}`);
      console.log(`      📏 大小: ${compressedResult.originalSize} → ${compressedResult.processedSize} bytes`);
      console.log(`      📊 比率: ${(compressedResult.processingRatio * 100).toFixed(1)}%`);
      
      if (compressedResult.success) {
        // 測試解壓縮
        console.log('   🔄 測試解壓縮...');
        try {
          const decompressed = compression.decompressData('runlendata', compressedResult.data);
          const isMatch = testCase.data.length === decompressed.length &&
            testCase.data.every((value, index) => value === decompressed[index]);
          
          console.log(`      ✅ 解壓縮成功: ${isMatch}`);
          
          if (!isMatch) {
            console.log(`      ❌ 數據不匹配！原始: ${testCase.data.length}, 解壓縮: ${decompressed.length}`);
          }
        } catch (error) {
          console.log(`      ❌ 解壓縮失敗: ${error.message}`);
        }
        
        // 檢查壓縮效果
        const compressionRatio = compressedResult.processingRatio;
        const hasGoodCompression = compressionRatio < 0.8;
        
        if (testCase.expectedGoodCompression && hasGoodCompression) {
          console.log(`      🎯 壓縮效果符合預期 (${(compressionRatio * 100).toFixed(1)}%)`);
        } else if (!testCase.expectedGoodCompression && !hasGoodCompression) {
          console.log(`      🎯 壓縮效果符合預期 (${(compressionRatio * 100).toFixed(1)}%)`);
        } else {
          console.log(`      ⚠️  壓縮效果與預期不符 (${(compressionRatio * 100).toFixed(1)}%)`);
        }
      }
      
      console.log('');
    }
    
    // 測試格式適用性分析
    console.log('🔍 測試格式適用性分析...');
    testCases.forEach((testCase, index) => {
      console.log(`   案例 ${index + 1}: ${testCase.name}`);
      
      const rawAnalysis = compression.analyzeFormatSuitability('rawdata', testCase.data);
      console.log(`      rawdata: ${rawAnalysis.suitable ? '✅' : '❌'} - ${rawAnalysis.reason}`);
      
      const compressedAnalysis = compression.analyzeFormatSuitability('runlendata', testCase.data);
      console.log(`      runlendata: ${compressedAnalysis.suitable ? '✅' : '❌'} - ${compressedAnalysis.reason}`);
    });
    console.log('');
    
    // 測試一致性
    console.log('🎯 測試壓縮一致性...');
    const consistencyResults = [];
    
    for (const testCase of testCases) {
      const result = compression.testCompressionConsistency('runlendata', testCase.data);
      consistencyResults.push(result);
      
      console.log(`   ${testCase.name}: ${result.consistent ? '✅' : '❌'} - ${result.message}`);
    }
    
    const allConsistent = consistencyResults.every(r => r.consistent);
    console.log(`   總體一致性: ${allConsistent ? '✅ 通過' : '❌ 失敗'}`);
    console.log('');
    
    // 顯示模組統計
    console.log('📈 模組統計信息:');
    const stats = compression.getModuleStats();
    console.log(`   初始化狀態: ${stats.initialized ? '✅' : '❌'}`);
    console.log(`   註冊的壓縮器: ${stats.registry.totalCompressors}`);
    console.log(`   支援的格式: ${stats.registry.supportedFormats.join(', ')}`);
    console.log(`   處理次數: ${stats.processor.totalProcessed}`);
    console.log(`   成功率: ${stats.processor.successRate}%`);
    console.log('');
    
    // 測試錯誤處理
    console.log('🚨 測試錯誤處理...');
    
    try {
      compression.processPixelData('invalid_format', testCases[0].data);
      console.log('   ❌ 應該拋出錯誤但沒有');
    } catch (error) {
      console.log('   ✅ 正確處理無效格式錯誤');
    }
    
    try {
      compression.decompressData('runlendata', new Uint8Array([0x04])); // 不完整數據
      console.log('   ❌ 應該拋出錯誤但沒有');
    } catch (error) {
      console.log('   ✅ 正確處理不完整壓縮數據錯誤');
    }
    
    console.log('\n🎉 端到端測試完成！');
    
    // 最終總結
    const totalTests = testCases.length * 2; // 每個案例測試兩種格式
    const successfulTests = consistencyResults.filter(r => r.consistent).length;
    
    console.log('\n📋 測試總結:');
    console.log(`   測試案例: ${testCases.length}`);
    console.log(`   一致性測試: ${successfulTests}/${consistencyResults.length} 通過`);
    console.log(`   支援格式: ${compression.getSupportedFormats().length}`);
    console.log(`   整體狀態: ${allConsistent ? '✅ 成功' : '❌ 有問題'}`);
    
    if (allConsistent) {
      console.log('\n🚀 系統準備就緒，可以開始使用！');
    } else {
      console.log('\n⚠️  發現問題，請檢查實作！');
    }
    
  } catch (error) {
    console.error('❌ 端到端測試失敗:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 運行測試
if (require.main === module) {
  runE2ETest();
}

module.exports = { runE2ETest };
