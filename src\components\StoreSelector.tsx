import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, ChevronUp, Check } from 'lucide-react';
import { Store } from '../types/store';
import { getAllStores } from '../utils/api/storeApi';
import { useTranslation } from 'react-i18next';

interface StoreSelectorProps {
  currentStore: Store;
  onStoreChange: (store: Store) => void;
  pageName: string;
}

const StoreSelector: React.FC<StoreSelectorProps> = ({ currentStore, onStoreChange, pageName }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [stores, setStores] = useState<Store[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();

  // 獲取所有門店
  useEffect(() => {
    const fetchStores = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const storeList = await getAllStores();
        setStores(storeList);
      } catch (err) {
        console.error('獲取門店列表失敗:', err);
        setError('無法載入門店列表');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStores();
  }, []);

  // 點擊外部關閉下拉選單
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 切換下拉選單
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // 選擇門店
  const handleSelectStore = (store: Store) => {
    onStoreChange(store);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className="flex items-center text-xl font-bold text-gray-800 hover:text-blue-600 transition-colors"
      >
        <span>{currentStore.name || '未命名門店'}</span>
        <span className="mx-2">-</span>
        <span>{pageName}</span>
        {isOpen ? (
          <ChevronUp className="ml-2 h-5 w-5" />
        ) : (
          <ChevronDown className="ml-2 h-5 w-5" />
        )}
      </button>

      {isOpen && (
        <div className="absolute z-50 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 max-h-96 overflow-y-auto">
          <div className="py-1">
            {isLoading ? (
              <div className="px-4 py-2 text-sm text-gray-500">載入中...</div>
            ) : error ? (
              <div className="px-4 py-2 text-sm text-red-500">{error}</div>
            ) : stores.length === 0 ? (
              <div className="px-4 py-2 text-sm text-gray-500">{t('storeManagement.noStores')}</div>
            ) : (
              stores.map((store) => (
                <button
                  key={store._id || store.id}
                  className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center justify-between ${
                    currentStore.id === store.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                  }`}
                  onClick={() => handleSelectStore(store)}
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{store.name || '未命名門店'}</span>
                    <span className="text-xs text-gray-500">{store.id}</span>
                  </div>
                  {currentStore.id === store.id && <Check className="h-4 w-4 text-blue-600" />}
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default StoreSelector;
