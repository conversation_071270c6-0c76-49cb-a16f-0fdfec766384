# EPD Manager App UI 設計

## 1. 設計原則

EPD Manager App 的 UI 設計遵循以下原則：

1. **簡潔明了**: 界面簡潔，重點突出，減少視覺干擾
2. **一致性**: 保持視覺元素、交互模式和功能的一致性
3. **易用性**: 操作直觀，減少學習成本
4. **響應式**: 適應不同尺寸的設備和屏幕方向
5. **專業性**: 體現專業工具的特性，注重功能性和效率

## 2. 色彩方案

### 2.1 主要色彩

- **主色**: `#1976D2` (藍色) - 代表專業、穩定和可靠
- **輔助色**: `#388E3C` (綠色) - 用於成功狀態和確認操作
- **警告色**: `#F57C00` (橙色) - 用於警告和需要注意的信息
- **錯誤色**: `#D32F2F` (紅色) - 用於錯誤和危險操作
- **中性色**: `#757575` (灰色) - 用於次要信息和禁用狀態

### 2.2 背景色

- **主背景**: `#FFFFFF` (白色) - 主要內容區域
- **次背景**: `#F5F5F5` (淺灰色) - 次要內容區域和卡片
- **深色背景**: `#212121` (深灰色) - 用於頂部導航欄和底部標籤欄

### 2.3 文字色

- **主要文字**: `#212121` (深灰色) - 主要內容文字
- **次要文字**: `#757575` (灰色) - 次要內容和說明文字
- **反色文字**: `#FFFFFF` (白色) - 深色背景上的文字

## 3. 排版

### 3.1 字體

- **主要字體**: 系統默認字體
  - iOS: San Francisco
  - Android: Roboto

### 3.2 字體大小

- **大標題**: 24sp
- **標題**: 20sp
- **副標題**: 16sp
- **正文**: 14sp
- **小字**: 12sp
- **極小字**: 10sp

### 3.3 字體粗細

- **粗體**: 用於標題和重要信息
- **常規**: 用於正文和一般信息
- **細體**: 用於次要信息和說明文字

## 4. 組件設計

### 4.1 按鈕

#### 4.1.1 主要按鈕

- 背景色: 主色 `#1976D2`
- 文字色: 白色 `#FFFFFF`
- 圓角: 4dp
- 高度: 48dp
- 內邊距: 水平 16dp，垂直 8dp

#### 4.1.2 次要按鈕

- 背景色: 透明
- 邊框: 1dp 主色 `#1976D2`
- 文字色: 主色 `#1976D2`
- 圓角: 4dp
- 高度: 48dp
- 內邊距: 水平 16dp，垂直 8dp

#### 4.1.3 文本按鈕

- 背景色: 透明
- 文字色: 主色 `#1976D2`
- 高度: 36dp
- 內邊距: 水平 8dp，垂直 4dp

#### 4.1.4 浮動操作按鈕 (FAB)

- 背景色: 主色 `#1976D2`
- 圖標色: 白色 `#FFFFFF`
- 尺寸: 56dp x 56dp
- 陰影: 2dp

### 4.2 輸入框

- 背景色: 白色 `#FFFFFF`
- 邊框: 1dp 灰色 `#BDBDBD`
- 文字色: 深灰色 `#212121`
- 提示文字色: 灰色 `#757575`
- 圓角: 4dp
- 高度: 56dp
- 內邊距: 水平 12dp，垂直 16dp

### 4.3 卡片

- 背景色: 白色 `#FFFFFF`
- 圓角: 8dp
- 陰影: 1dp
- 內邊距: 16dp
- 外邊距: 8dp

### 4.4 列表項

- 背景色: 白色 `#FFFFFF`
- 高度: 72dp (單行), 88dp (雙行)
- 內邊距: 水平 16dp，垂直 8dp
- 分隔線: 1dp 淺灰色 `#EEEEEE`

### 4.5 對話框

- 背景色: 白色 `#FFFFFF`
- 圓角: 8dp
- 陰影: 24dp
- 內邊距: 24dp
- 最大寬度: 280dp

### 4.6 標籤欄

- 背景色: 深灰色 `#212121`
- 選中項文字/圖標色: 主色 `#1976D2`
- 未選中項文字/圖標色: 灰色 `#BDBDBD`
- 高度: 56dp

## 5. 頁面設計

### 5.1 登入頁面

![登入頁面示意圖]

- **布局**: 垂直居中，上方 Logo，中間表單，底部按鈕
- **元素**:
  - Logo
  - 用戶名輸入框
  - 密碼輸入框
  - 記住我選項
  - 登入按鈕
  - 伺服器設置按鈕

### 5.2 門店選擇頁面

![門店選擇頁面示意圖]

- **布局**: 頂部標題，中間列表，底部按鈕
- **元素**:
  - 頁面標題
  - 搜索框
  - 門店列表
  - 刷新按鈕

### 5.3 網關管理頁面

![網關管理頁面示意圖]

- **布局**: 頂部標題，中間列表，右下角浮動按鈕
- **元素**:
  - 頁面標題
  - 網關列表
  - 添加網關按鈕 (FAB)
  - 刷新按鈕

### 5.4 網關詳情頁面

![網關詳情頁面示意圖]

- **布局**: 頂部標題，中間信息卡片，底部操作按鈕
- **元素**:
  - 頁面標題
  - 網關基本信息卡片
  - 網關狀態卡片
  - 網關設備列表卡片
  - WebSocket 連接按鈕
  - 編輯按鈕
  - 刪除按鈕

### 5.5 設備管理頁面

![設備管理頁面示意圖]

- **布局**: 頂部標題，中間列表，右下角浮動按鈕
- **元素**:
  - 頁面標題
  - 設備列表
  - 添加設備按鈕 (FAB)
  - 刷新按鈕

### 5.6 設備詳情頁面

![設備詳情頁面示意圖]

- **布局**: 頂部標題，中間信息卡片，底部操作按鈕
- **元素**:
  - 頁面標題
  - 設備基本信息卡片
  - 設備狀態卡片
  - 設備預覽圖像
  - 請求預覽按鈕
  - 編輯按鈕
  - 刪除按鈕

### 5.7 WebSocket 控制台頁面

![WebSocket 控制台頁面示意圖]

- **布局**: 頂部標題，中間消息列表，底部輸入區域
- **元素**:
  - 頁面標題
  - 連接狀態指示器
  - 消息列表
  - 命令輸入框
  - 發送按鈕
  - 清除按鈕

### 5.8 設置頁面

![設置頁面示意圖]

- **布局**: 頂部標題，中間設置列表
- **元素**:
  - 頁面標題
  - 用戶信息卡片
  - 伺服器設置
  - 通知設置
  - 緩存管理
  - 關於信息
  - 登出按鈕

## 6. 交互設計

### 6.1 導航

- **底部標籤導航**: 用於主要功能模塊之間的切換
- **堆棧導航**: 用於同一模塊內的頁面導航
- **抽屜導航**: 用於輔助功能和設置

### 6.2 手勢

- **點擊**: 選擇項目，觸發操作
- **長按**: 顯示上下文菜單或額外操作
- **滑動**: 滾動列表，切換頁面
- **下拉**: 刷新列表數據

### 6.3 反饋

- **視覺反饋**: 按鈕狀態變化，高亮選中項
- **觸覺反饋**: 按鈕點擊時的輕微震動
- **動畫**: 頁面轉場，加載指示器，狀態變化

### 6.4 加載狀態

- **全屏加載**: 首次加載數據時顯示
- **局部加載**: 刷新或加載更多數據時顯示
- **按鈕加載**: 操作進行中時按鈕顯示加載狀態

### 6.5 錯誤處理

- **錯誤提示**: Toast 或 Snackbar 顯示簡短錯誤信息
- **錯誤頁面**: 嚴重錯誤時顯示專門的錯誤頁面
- **重試機制**: 提供重試按鈕或自動重試

## 7. 響應式設計

### 7.1 不同屏幕尺寸

- **手機**: 單列布局，優化垂直空間使用
- **平板**: 雙列布局，利用更大的屏幕空間
- **橫屏模式**: 調整布局以適應寬屏顯示

### 7.2 適配策略

- **彈性布局**: 使用 Flex 布局適應不同屏幕尺寸
- **相對尺寸**: 使用相對單位而非絕對單位
- **斷點**: 在不同屏幕寬度下使用不同布局

## 8. 無障礙設計

- **足夠的對比度**: 確保文字和背景有足夠的對比度
- **適當的字體大小**: 提供調整字體大小的選項
- **語音讀屏**: 為所有元素提供適當的描述
- **鍵盤操作**: 支持使用鍵盤或外部設備操作
