import { useState, useEffect, useCallback } from 'react';
import { useAuthStore } from '../store/authStore';
import { getCurrentUserPermissions } from '../utils/api/userApi';

/**
 * 權限檢查Hook
 * 用於檢查當前用戶是否擁有特定權限
 */
export const usePermission = () => {
  const { isAuthenticated } = useAuthStore();
  const [permissions, setPermissions] = useState<string[]>([]);
  const [roles, setRoles] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // 獲取用戶權限
  useEffect(() => {
    const fetchUserPermissions = async () => {
      if (!isAuthenticated) {
        setPermissions([]);
        setRoles([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const data = await getCurrentUserPermissions();

        setPermissions(data.permissions);
        setRoles(data.roles);
      } catch (error) {
        console.error('獲取用戶權限錯誤:', error);
        setPermissions([]);
        setRoles([]);
      } finally {
        setLoading(false);
      }
    };

    fetchUserPermissions();
  }, [isAuthenticated]);

  /**
   * 檢查用戶是否有特定權限
   * @param permission 權限標識符
   * @returns 是否有權限
   */
  const hasPermission = useCallback((permission: string): boolean => {
    if (!isAuthenticated || loading) return false;

    // 如果有 'all' 權限，則擁有所有權限
    if (permissions.includes('all')) return true;

    // 檢查是否有特定權限
    return permissions.includes(permission);
  }, [isAuthenticated, loading, permissions]);

  /**
   * 檢查用戶是否有多個權限中的任意一個
   * @param permissionList 權限標識符列表
   * @returns 是否有權限
   */
  const hasAnyPermission = useCallback((permissionList: string[]): boolean => {
    if (!isAuthenticated || loading) return false;

    // 如果有 'all' 權限，則擁有所有權限
    if (permissions.includes('all')) return true;

    // 檢查是否有任意一個權限
    return permissionList.some(permission => permissions.includes(permission));
  }, [isAuthenticated, loading, permissions]);

  /**
   * 檢查用戶是否有所有指定的權限
   * @param permissionList 權限標識符列表
   * @returns 是否有所有權限
   */
  const hasAllPermissions = useCallback((permissionList: string[]): boolean => {
    if (!isAuthenticated || loading) return false;

    // 如果有 'all' 權限，則擁有所有權限
    if (permissions.includes('all')) return true;

    // 檢查是否有所有權限
    return permissionList.every(permission => permissions.includes(permission));
  }, [isAuthenticated, loading, permissions]);

  return {
    loading,
    permissions,
    roles,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions
  };
};

export default usePermission;
