/**
 * 圖片旋轉處理工具
 */

/**
 * 主要的圖片旋轉函數
 */
export function rotateImageData(
  imageData: ImageData,
  rotation: number
): ImageData {
  switch (rotation) {
    case 90:
      return rotateImageData90(imageData);
    case 180:
      return rotateImageData180(imageData);
    case 270:
      return rotateImageData270(imageData);
    default:
      return imageData;
  }
}

/**
 * 獲取模板旋轉角度的反向旋轉
 */
export function getTemplateReverseRotation(templateRotation: number): number {
  if (templateRotation === 0) return 0;
  return (360 - templateRotation) % 360;
}

/**
 * 90度順時針旋轉
 */
function rotateImageData90(imageData: ImageData): ImageData {
  const { width, height, data } = imageData;
  const newImageData = new ImageData(height, width);
  const newData = newImageData.data;
  
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const oldIndex = (y * width + x) * 4;
      const newX = height - 1 - y;
      const newY = x;
      const newIndex = (newY * height + newX) * 4;
      
      newData[newIndex] = data[oldIndex];         // R
      newData[newIndex + 1] = data[oldIndex + 1]; // G
      newData[newIndex + 2] = data[oldIndex + 2]; // B
      newData[newIndex + 3] = data[oldIndex + 3]; // A
    }
  }
  
  return newImageData;
}

/**
 * 180度旋轉
 */
function rotateImageData180(imageData: ImageData): ImageData {
  const { width, height, data } = imageData;
  const newImageData = new ImageData(width, height);
  const newData = newImageData.data;
  
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const oldIndex = (y * width + x) * 4;
      const newX = width - 1 - x;
      const newY = height - 1 - y;
      const newIndex = (newY * width + newX) * 4;
      
      newData[newIndex] = data[oldIndex];         // R
      newData[newIndex + 1] = data[oldIndex + 1]; // G
      newData[newIndex + 2] = data[oldIndex + 2]; // B
      newData[newIndex + 3] = data[oldIndex + 3]; // A
    }
  }
  
  return newImageData;
}

/**
 * 270度順時針旋轉（等於90度逆時針）
 */
function rotateImageData270(imageData: ImageData): ImageData {
  const { width, height, data } = imageData;
  const newImageData = new ImageData(height, width);
  const newData = newImageData.data;
  
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const oldIndex = (y * width + x) * 4;
      const newX = y;
      const newY = width - 1 - x;
      const newIndex = (newY * height + newX) * 4;
      
      newData[newIndex] = data[oldIndex];         // R
      newData[newIndex + 1] = data[oldIndex + 1]; // G
      newData[newIndex + 2] = data[oldIndex + 2]; // B
      newData[newIndex + 3] = data[oldIndex + 3]; // A
    }
  }
  
  return newImageData;
}
