import React, { useEffect, useRef, useState } from 'react';
import { createRoot } from 'react-dom/client';
import * as lucideIcons from 'lucide-react';
import { renderCanvasToImage } from '../components/editor/canvasUtils';
import { processTextBindings, restoreTextBindings } from '../utils/previewUtils';
import { applyImageEffect, EffectType } from '../utils/previewUtils';
import { convertImageByColorType, convertImageByEffectType } from '../utils/colorConversion';
import { processImageUrl } from '../utils/imageUrlUtils';
import { generateQRCodeForEditor } from '../utils/codeGenerators/qrCodeGenerator';
import { generateBarcodeForEditor } from '../utils/codeGenerators/barcodeGenerator';

interface PreviewComponentProps {
  template: any;
  bindingData: Record<string, string>;
  storeData: any[];
  effectType?: EffectType;
  threshold?: number;
  onPreviewGenerated?: (previewData: string | null) => void;
  deviceId?: string; // 可選的設備ID，不再用於自動保存預覽圖，僅保留向後兼容性
  storeId?: string; // 可選的門店ID，不再用於自動保存預覽圖，僅保留向後兼容性
}



/**
 * 處理 QR Code 的資料綁定並渲染
 * @param elementDiv DOM 元素
 * @param element 元素配置
 * @param sampleDataByIndex 範例數據
 * @param dataFields 資料欄位定義
 */
const renderQRCodeElement = async (
  elementDiv: HTMLElement,
  element: any,
  sampleDataByIndex: Record<number, Record<string, any>>,
  dataFields: any[] = []
) => {
  try {
    // 處理資料綁定
    let content = element.codeContent || 'QR Code 內容';

    if (element.dataBinding && element.dataBinding.fieldId) {
      const dataIndex = element.dataBinding.dataIndex || 0;
      const fieldId = element.dataBinding.fieldId;

      // 獲取綁定的數據
      if (sampleDataByIndex[dataIndex] && sampleDataByIndex[dataIndex][fieldId] !== undefined) {
        content = String(sampleDataByIndex[dataIndex][fieldId]);
        console.log(`QR Code 使用綁定數據: ${content} (從 dataIndex=${dataIndex}, fieldId=${fieldId})`);
      } else {
        console.log(`QR Code 未找到綁定數據，使用默認內容: ${content}`);
      }
    }

    // 創建一個臨時的元素配置用於生成 QR Code
    const tempElement = {
      ...element,
      codeContent: content
    };

    // 生成 QR Code 圖片
    const qrCodeDataUrl = await generateQRCodeForEditor(tempElement);

    // 設置元素的背景色和邊框，與編輯器保持一致
    const backgroundColor = element.fillColor || '#FFFFFF';
    elementDiv.style.backgroundColor = backgroundColor;

    // 處理邊框 - 與 QRCodeRenderer.tsx 保持一致
    if (element.showBorder !== false) {
      const borderWidth = element.lineWidth || 2;
      const borderColor = element.borderColor || '#000000';
      elementDiv.style.border = `${borderWidth}px solid ${borderColor}`;
      elementDiv.style.borderRadius = '4px';
    } else {
      elementDiv.style.border = 'none';
    }

    // 設置容器樣式
    elementDiv.style.display = 'flex';
    elementDiv.style.alignItems = 'center';
    elementDiv.style.justifyContent = 'center';
    elementDiv.style.padding = '8px';
    elementDiv.style.boxSizing = 'border-box';

    // 創建圖片元素
    const imgElement = document.createElement('img');
    imgElement.src = qrCodeDataUrl;
    imgElement.style.width = '100%';
    imgElement.style.height = '100%';
    imgElement.style.objectFit = 'contain';

    elementDiv.appendChild(imgElement);

  } catch (error) {
    console.error('QR Code 渲染失敗:', error);
    // 渲染錯誤佔位符
    elementDiv.style.border = '1px solid #ff0000';
    elementDiv.style.backgroundColor = '#ffe6e6';
    elementDiv.style.display = 'flex';
    elementDiv.style.justifyContent = 'center';
    elementDiv.style.alignItems = 'center';
    elementDiv.style.fontSize = '12px';
    elementDiv.style.color = '#ff0000';
    elementDiv.textContent = 'QR Code 錯誤';
  }
};

/**
 * 處理條碼的資料綁定並渲染
 * @param elementDiv DOM 元素
 * @param element 元素配置
 * @param sampleDataByIndex 範例數據
 * @param dataFields 資料欄位定義
 */
const renderBarcodeElement = async (
  elementDiv: HTMLElement,
  element: any,
  sampleDataByIndex: Record<number, Record<string, any>>,
  dataFields: any[] = []
) => {
  try {
    // 處理資料綁定
    let content = element.codeContent || '123456789';

    if (element.dataBinding && element.dataBinding.fieldId) {
      const dataIndex = element.dataBinding.dataIndex || 0;
      const fieldId = element.dataBinding.fieldId;

      // 獲取綁定的數據
      if (sampleDataByIndex[dataIndex] && sampleDataByIndex[dataIndex][fieldId] !== undefined) {
        content = String(sampleDataByIndex[dataIndex][fieldId]);
        console.log(`Barcode 使用綁定數據: ${content} (從 dataIndex=${dataIndex}, fieldId=${fieldId})`);
      } else {
        console.log(`Barcode 未找到綁定數據，使用默認內容: ${content}`);
      }
    }

    // 創建一個臨時的元素配置用於生成條碼
    const tempElement = {
      ...element,
      codeContent: content
    };

    // 生成條碼圖片
    const barcodeDataUrl = await generateBarcodeForEditor(tempElement);

    // 設置元素的背景色和邊框，與編輯器保持一致
    const backgroundColor = element.fillColor || '#FFFFFF';
    elementDiv.style.backgroundColor = backgroundColor;

    // 處理邊框 - 與 BarcodeRenderer.tsx 保持一致
    if (element.showBorder !== false) {
      const borderWidth = element.lineWidth || 2;
      const borderColor = element.borderColor || '#000000';
      elementDiv.style.border = `${borderWidth}px solid ${borderColor}`;
      elementDiv.style.borderRadius = '4px';
    } else {
      elementDiv.style.border = 'none';
    }

    // 設置容器樣式
    elementDiv.style.display = 'flex';
    elementDiv.style.alignItems = 'center';
    elementDiv.style.justifyContent = 'center';
    elementDiv.style.padding = '8px';
    elementDiv.style.boxSizing = 'border-box';

    // 創建圖片元素
    const imgElement = document.createElement('img');
    imgElement.src = barcodeDataUrl;
    imgElement.style.width = '100%';
    imgElement.style.height = '100%';
    imgElement.style.objectFit = 'contain';

    elementDiv.appendChild(imgElement);

  } catch (error) {
    console.error('Barcode 渲染失敗:', error);
    // 渲染錯誤佔位符
    elementDiv.style.border = '1px solid #ff0000';
    elementDiv.style.backgroundColor = '#ffe6e6';
    elementDiv.style.display = 'flex';
    elementDiv.style.justifyContent = 'center';
    elementDiv.style.alignItems = 'center';
    elementDiv.style.fontSize = '12px';
    elementDiv.style.color = '#ff0000';
    elementDiv.textContent = 'Barcode 錯誤';
  }
};

const PreviewComponent: React.FC<PreviewComponentProps> = ({
  template,
  bindingData,
  storeData,
  effectType = 'blackAndWhite',
  threshold = 128,
  onPreviewGenerated,
  // 保留參數但標記為未使用，以保持向後兼容性
  deviceId: _deviceId,
  storeId: _storeId
}): JSX.Element => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  // 分析模板中的數據綁定
  const analyzeTemplateBindings = (elements: any[]) => {
    // 收集所有帶有資料綁定的元素
    const elementsWithDataBinding: any[] = [];
    const dataIndicesByIndex: any[] = [];
    const fieldMappings = new Map<number, string[]>();

    // 調試信息：輸出模板元素總數
    console.log(`分析模板中的數據綁定，元素總數: ${elements.length}`);

    for (const element of elements) {
      // 檢查是否有數據綁定
      if (element.dataBinding && element.dataBinding.dataIndex !== undefined && element.dataBinding.fieldId) {
        const dataIndex = element.dataBinding.dataIndex;
        const fieldId = element.dataBinding.fieldId;

        console.log(`找到帶有數據綁定的元素: type=${element.type}, dataIndex=${dataIndex}, fieldId=${fieldId}`);

        elementsWithDataBinding.push({
          element,
          dataIndex,
          fieldId
        });

        // 記錄每個 dataIndex 對應的欄位
        if (!fieldMappings.has(dataIndex)) {
          fieldMappings.set(dataIndex, [fieldId]);
          dataIndicesByIndex.push({
            dataIndex,
            displayName: `資料${dataIndex + 1}`,
            fieldId: `dataIndex${dataIndex}`
          });
          console.log(`為 dataIndex=${dataIndex} 創建新的映射，欄位: ${fieldId}`);
        } else {
          const fields = fieldMappings.get(dataIndex);
          if (fields && !fields.includes(fieldId)) {
            fields.push(fieldId);
            console.log(`為 dataIndex=${dataIndex} 添加欄位: ${fieldId}`);
          }
        }
      } else if (element.dataFieldId) {
        // 處理舊版數據綁定格式
        const match = element.dataFieldId.match(/(\d+)/);
        if (match) {
          const dataIndex = parseInt(match[1]) - 1; // 將 "data1" 映射到索引 0
          const fieldId = element.dataFieldId;

          console.log(`找到帶有舊版數據綁定的元素: type=${element.type}, dataIndex=${dataIndex}, fieldId=${fieldId}`);

          elementsWithDataBinding.push({
            element,
            dataIndex,
            fieldId
          });

          // 記錄每個 dataIndex 對應的欄位
          if (!fieldMappings.has(dataIndex)) {
            fieldMappings.set(dataIndex, [fieldId]);
            dataIndicesByIndex.push({
              dataIndex,
              displayName: `資料${dataIndex + 1}`,
              fieldId: `dataIndex${dataIndex}`
            });
            console.log(`為舊版 dataIndex=${dataIndex} 創建新的映射，欄位: ${fieldId}`);
          } else {
            const fields = fieldMappings.get(dataIndex);
            if (fields && !fields.includes(fieldId)) {
              fields.push(fieldId);
              console.log(`為舊版 dataIndex=${dataIndex} 添加欄位: ${fieldId}`);
            }
          }
        }
      }
    }

    // 調試信息：輸出分析結果
    console.log(`找到 ${elementsWithDataBinding.length} 個帶有數據綁定的元素`);
    console.log(`數據索引總數: ${dataIndicesByIndex.length}`);
    console.log(`欄位映射: `, Object.fromEntries(fieldMappings));

    return { dataIndicesByIndex, fieldMappings };
  };

  // 生成預覽圖
  const generatePreview = async () => {
    if (!template || !template.elements || !Array.isArray(template.elements) || !containerRef.current) {
      return null;
    }

    try {
      // 清空容器
      containerRef.current.innerHTML = '';

      // 獲取模板尺寸
      let canvasWidth = 250;  // 默認值
      let canvasHeight = 122; // 默認值

      if (template && template.screenSize) {
        // 解析模板的螢幕尺寸
        const sizeMatch = template.screenSize.match(/(\d+)x(\d+)/);
        if (sizeMatch && sizeMatch[1] && sizeMatch[2]) {
          canvasWidth = parseInt(sizeMatch[1], 10);
          canvasHeight = parseInt(sizeMatch[2], 10);
        }
      }

      // 檢測模板方向（縱向或橫向）
      const isPortrait = canvasHeight > canvasWidth;
      console.log(`模板方向: ${isPortrait ? '縱向' : '橫向'}, 尺寸: ${canvasWidth}x${canvasHeight}`);

      // 設置容器尺寸
      containerRef.current.style.width = `${canvasWidth}px`;
      containerRef.current.style.height = `${canvasHeight}px`;
      containerRef.current.setAttribute('data-canvas-width', String(canvasWidth));
      containerRef.current.setAttribute('data-canvas-height', String(canvasHeight));
      containerRef.current.setAttribute('data-orientation', isPortrait ? 'portrait' : 'landscape');

      // 確保所有模板都使用 overflow: hidden，防止內容溢出
      containerRef.current.style.overflow = 'hidden';

      // 設置容器的初始樣式，確保在第一次渲染時正確顯示
      containerRef.current.style.display = 'block';
      containerRef.current.style.visibility = 'visible';
      containerRef.current.style.opacity = '1';

      // 創建元素容器
      const elementsContainer = document.createElement('div');
      elementsContainer.className = 'elements-container';
      elementsContainer.style.position = 'relative';
      elementsContainer.style.width = '100%';
      elementsContainer.style.height = '100%';
      containerRef.current.appendChild(elementsContainer);

      // 分析模板中的數據綁定
      analyzeTemplateBindings(template.elements);

      // 構造數據樣本 - 從 bindingData 和 storeData 中提取實際數據
      const sampleDataByIndex: Record<number, Record<string, any>> = {};
      const storeItemsByDataIndex: Record<number, any> = {};

      console.log('開始處理綁定數據:', bindingData);
      console.log('門店數據數量:', storeData.length);

      // 對於每個綁定的數據索引，查找對應的門店數據
      console.log('處理數據綁定，bindingData:', bindingData ? Object.keys(bindingData).length : 'null');

      // 即使沒有綁定數據，也繼續處理模板渲染
      // 如果有綁定數據，則處理綁定數據
      if (bindingData && Object.keys(bindingData).length > 0) {
        Object.entries(bindingData).forEach(([dataIndexKey, dataId]) => {
        console.log(`處理綁定數據: ${dataIndexKey} -> ${dataId}`);

        // 從 dataIndexKey (如 "dataIndex0") 提取數據索引
        const match = dataIndexKey.match(/dataIndex(\d+)/);
        if (match) {
          const dataIndex = parseInt(match[1]);
          console.log(`從 ${dataIndexKey} 提取數據索引: ${dataIndex}`);

          if (dataId) {
            // 查找對應的門店數據
            // 使用 uid 作為主要匹配條件，因為它是唯一的
            const storeItem = storeData.find(item =>
              item.uid === dataId ||
              item.id === dataId ||
              (item._id && item._id.toString() === dataId)
            );

            console.log(`查找數據ID為 ${dataId} 的門店數據項:`, storeItem ? {
              id: storeItem.id,
              uid: storeItem.uid,
              _id: storeItem._id,
              storeId: storeItem.storeId,
              _storeName: storeItem._storeName
            } : '未找到');

            if (storeItem) {
              console.log(`找到對應的門店數據項: id=${storeItem.id}, uid=${storeItem.uid}`);

              // 保存整個門店項目，以便後續使用
              storeItemsByDataIndex[dataIndex] = storeItem;

              // 為這個數據索引初始化一個對象
              if (!sampleDataByIndex[dataIndex]) {
                sampleDataByIndex[dataIndex] = {};
              }

              // 將門店數據中的所有欄位加入到對應的數據索引中
              for (const [key, value] of Object.entries(storeItem)) {
                if (key !== 'id' && key !== '_id') { // 排除 id 欄位
                  sampleDataByIndex[dataIndex][key] = value;
                }
              }

              // 添加調試信息
              console.log(`為數據索引 ${dataIndex} 添加了 ${Object.keys(storeItem).length} 個欄位`);
              console.log(`數據索引 ${dataIndex} 的欄位:`, Object.keys(sampleDataByIndex[dataIndex]));
            } else {
              console.log(`未找到 ID 為 ${dataId} 的門店數據項`);

              // 如果找不到完全匹配的項目，嘗試部分匹配
              // 例如，dataId 可能是 uid 的一部分
              const partialMatch = storeData.find(item =>
                (item.id && item.id.includes(dataId)) ||
                (item.uid && item.uid.includes(dataId)) ||
                (item._id && item._id.toString().includes(dataId))
              );

              if (partialMatch) {
                console.log(`找到部分匹配的門店數據項: id=${partialMatch.id}, uid=${partialMatch.uid}`);

                // 保存整個門店項目，以便後續使用
                storeItemsByDataIndex[dataIndex] = partialMatch;

                // 為這個數據索引初始化一個對象
                if (!sampleDataByIndex[dataIndex]) {
                  sampleDataByIndex[dataIndex] = {};
                }

                // 將門店數據中的所有欄位加入到對應的數據索引中
                for (const [key, value] of Object.entries(partialMatch)) {
                  if (key !== 'id' && key !== '_id') { // 排除 id 欄位
                    sampleDataByIndex[dataIndex][key] = value;
                  }
                }

                console.log(`為數據索引 ${dataIndex} 添加了 ${Object.keys(partialMatch).length} 個欄位 (部分匹配)`);
              } else if (storeData.length > 0) {
                // 如果找不到匹配的項目，但有門店數據，使用第一個項目
                console.log(`未找到匹配的門店數據項，使用第一個項目`);

                const firstItem = storeData[0];
                storeItemsByDataIndex[dataIndex] = firstItem;

                // 為這個數據索引初始化一個對象
                if (!sampleDataByIndex[dataIndex]) {
                  sampleDataByIndex[dataIndex] = {};
                }

                // 將門店數據中的所有欄位加入到對應的數據索引中
                for (const [key, value] of Object.entries(firstItem)) {
                  if (key !== 'id' && key !== '_id') { // 排除 id 欄位
                    sampleDataByIndex[dataIndex][key] = value;
                  }
                }

                console.log(`為數據索引 ${dataIndex} 添加了 ${Object.keys(firstItem).length} 個欄位 (使用第一個項目)`);
              }
            }
          } else {
            console.log(`數據索引 ${dataIndex} 沒有對應的數據 ID`);
          }
        } else {
          // 如果 dataIndexKey 不是標準格式，嘗試直接使用它作為欄位 ID
          console.log(`非標準數據索引鍵: ${dataIndexKey}`);

          // 查找對應的門店數據
          if (dataId) {
            // 使用 uid 作為主要匹配條件，因為它是唯一的
            const storeItem = storeData.find(item =>
              item.uid === dataId ||
              item.id === dataId ||
              (item._id && item._id.toString() === dataId)
            );

            console.log(`查找數據ID為 ${dataId} 的門店數據項:`, storeItem ? {
              id: storeItem.id,
              uid: storeItem.uid,
              _id: storeItem._id,
              storeId: storeItem.storeId,
              _storeName: storeItem._storeName
            } : '未找到');

            if (storeItem) {
              console.log(`找到對應的門店數據項: id=${storeItem.id}, uid=${storeItem.uid}`);

              // 使用 dataIndexKey 作為欄位 ID
              // 為了簡化處理，我們將其放在數據索引 0 下
              if (!sampleDataByIndex[0]) {
                sampleDataByIndex[0] = {};
              }

              // 將門店數據中的所有欄位加入到對應的數據索引中
              for (const [key, value] of Object.entries(storeItem)) {
                if (key !== 'id' && key !== '_id') { // 排除 id 欄位
                  sampleDataByIndex[0][key] = value;
                }
              }

              // 特別處理：將 dataIndexKey 作為欄位 ID 添加到數據索引 0 中
              sampleDataByIndex[0][dataIndexKey] = dataId;

              console.log(`為數據索引 0 添加了 ${Object.keys(storeItem).length} 個欄位 (非標準索引)`);
            }
          }
        }
      });

      // 輸出最終的數據樣本
      console.log('最終的數據樣本:', sampleDataByIndex);
      console.log('門店項目映射:', storeItemsByDataIndex);
      } else {
        console.log('沒有綁定數據，將直接渲染靜態模板');
      }

      // 為每個元素創建 DOM 元素，並添加到臨時畫布
      for (let index = 0; index < template.elements.length; index++) {
        const element = template.elements[index];
        const elementDiv = document.createElement('div');
        elementDiv.setAttribute('data-element-id', String(index));
        elementDiv.setAttribute('data-element-type', element.type);
        elementDiv.style.position = 'absolute';

        // 對於線條元素，需要特別處理其位置和尺寸
        if (element.type === 'line') {
          // 計算虛擬選取框的實際位置和尺寸
          let boxLeft = element.x;
          let boxTop = element.y;
          let boxWidth = Math.abs(element.width);
          let boxHeight = Math.abs(element.height);

          // 根據線段的方向調整選取框位置
          if (element.width < 0) {
            boxLeft = element.x + element.width;
          }
          if (element.height < 0) {
            boxTop = element.y + element.height;
          }

          elementDiv.style.left = `${boxLeft}px`;
          elementDiv.style.top = `${boxTop}px`;
          elementDiv.style.width = `${boxWidth + 1}px`; // 確保有足夠的空間顯示線條
          elementDiv.style.height = `${boxHeight + 1}px`;
        } else {
          // 其他元素使用原始值
          elementDiv.style.left = `${element.x}px`;
          elementDiv.style.top = `${element.y}px`;
          elementDiv.style.width = `${element.width}px`;
          elementDiv.style.height = `${element.height}px`;
        }

        // 處理元素旋轉
        if (element.rotation) {
          elementDiv.style.transform = `rotate(${element.rotation}deg)`;
          elementDiv.style.transformOrigin = 'center center';
        }

        // 檢查是否為文字元素且有數據綁定
        if ((element.type === 'text' || element.type === 'multiline-text')) {
          // 創建文字容器
          const textContainer = document.createElement('div');
          textContainer.className = 'text-element-content';
          textContainer.style.width = '100%';
          textContainer.style.height = '100%';
          textContainer.style.fontSize = `${element.fontSize || 12}px`;
          textContainer.style.fontFamily = element.fontFamily || 'Arial';
          textContainer.style.color = element.lineColor || '#000';

          // 檢查是否有數據綁定
          if (element.dataBinding && element.dataBinding.fieldId) {
            const dataIndex = element.dataBinding.dataIndex;
            const fieldId = element.dataBinding.fieldId;
            // 根據 template editor 的邏輯，只有當 displayOptions.showPrefix 為 true 時才顯示前綴
            const showPrefix = element.dataBinding.displayOptions?.showPrefix === true;

            // 打印調試信息，查看 showPrefix 的實際值
            console.log(`元素 ${index} 的 showPrefix 值為: ${showPrefix}`);
            console.log(`元素 ${index} 的 dataBinding:`, element.dataBinding);

            // 設置綁定數據屬性
            elementDiv.setAttribute('data-has-binding', 'true');
            elementDiv.setAttribute('data-field-id', fieldId);
            elementDiv.setAttribute('data-index', String(dataIndex));
            elementDiv.setAttribute('data-show-prefix', String(showPrefix));
            elementDiv.setAttribute('data-original-content', element.content || 'Text');

            // 如果有對應的門店數據，設置門店 ID 和 item-uid
            if (storeItemsByDataIndex[dataIndex]) {
              // 設置門店 ID - 這應該是門店的 ID，而不是商品的 ID
              // 首先檢查是否有 storeId 屬性
              if (storeItemsByDataIndex[dataIndex].storeId) {
                elementDiv.setAttribute('data-store-id', storeItemsByDataIndex[dataIndex].storeId);
                console.log(`設置元素 ${index} 的門店ID為: ${storeItemsByDataIndex[dataIndex].storeId}`);
              }
              // 如果沒有 storeId 屬性，則使用 id 屬性
              else if (storeItemsByDataIndex[dataIndex].id) {
                elementDiv.setAttribute('data-store-id', storeItemsByDataIndex[dataIndex].id);
                console.log(`設置元素 ${index} 的門店ID為: ${storeItemsByDataIndex[dataIndex].id} (從id屬性)`);
              }

              // 如果門店數據有 uid 欄位，設置 item-uid
              if (storeItemsByDataIndex[dataIndex].uid) {
                elementDiv.setAttribute('data-item-uid', storeItemsByDataIndex[dataIndex].uid);
                console.log(`設置元素 ${index} 的商品UID為: ${storeItemsByDataIndex[dataIndex].uid}`);
              }
            }

            // 設置文字內容
            // 首先檢查是否有對應的數據索引
            if (sampleDataByIndex[dataIndex]) {
              // 檢查該數據索引下是否有對應的欄位數據
              if (sampleDataByIndex[dataIndex][fieldId] !== undefined) {
                // 如果有實際數據，使用實際數據
                let displayValue = String(sampleDataByIndex[dataIndex][fieldId]);

                // 如果需要顯示前綴，添加前綴
                if (showPrefix) {
                  // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
                  displayValue = element.dataBinding.prefix ? `${element.dataBinding.prefix}: ${displayValue}` : `: ${displayValue}`;
                }

                textContainer.textContent = displayValue;
                console.log(`設置元素 ${index} 的文字內容為: ${displayValue} (從 dataIndex=${dataIndex}, fieldId=${fieldId})`);
              } else {
                // 如果該數據索引下沒有對應的欄位數據，嘗試直接使用欄位ID作為鍵
                // 這是為了處理欄位ID可能直接對應數據項的情況
                const allKeys = Object.keys(sampleDataByIndex[dataIndex]);
                console.log(`數據索引 ${dataIndex} 下的所有鍵:`, allKeys);

                // 嘗試查找匹配的鍵
                let matchedKey = null;
                for (const key of allKeys) {
                  if (key === fieldId || key.includes(fieldId)) {
                    matchedKey = key;
                    break;
                  }
                }

                if (matchedKey) {
                  // 如果找到匹配的鍵，使用該鍵對應的值
                  let displayValue = String(sampleDataByIndex[dataIndex][matchedKey]);

                  // 如果需要顯示前綴，添加前綴
                  if (showPrefix) {
                    displayValue = element.dataBinding.prefix ? `${element.dataBinding.prefix}: ${displayValue}` : `: ${displayValue}`;
                  }

                  textContainer.textContent = displayValue;
                  console.log(`設置元素 ${index} 的文字內容為: ${displayValue} (從匹配的鍵 ${matchedKey})`);
                } else {
                  // 如果沒有找到匹配的鍵，顯示默認文本 'TEXT'
                  let displayValue = 'TEXT';

                  // 即使沒有實際數據，也要檢查是否需要顯示前綴
                  if (showPrefix) {
                    displayValue = element.dataBinding.prefix ? `${element.dataBinding.prefix}: ${displayValue}` : `: ${displayValue}`;
                  }

                  textContainer.textContent = displayValue;
                  console.log(`設置元素 ${index} 的文字內容為默認值: ${displayValue} (未找到匹配的鍵)`);
                }
              }
            } else {
              // 如果沒有對應的數據索引，顯示默認文本 'TEXT'
              let displayValue = 'TEXT';

              // 即使沒有實際數據，也要檢查是否需要顯示前綴
              if (showPrefix) {
                displayValue = element.dataBinding.prefix ? `${element.dataBinding.prefix}: ${displayValue}` : `: ${displayValue}`;
              }

              textContainer.textContent = displayValue;
              console.log(`設置元素 ${index} 的文字內容為默認值: ${displayValue} (未找到數據索引 ${dataIndex})`);
            }
          } else if (element.dataFieldId) {
            // 處理舊版數據綁定格式
            elementDiv.setAttribute('data-has-binding', 'true');
            elementDiv.setAttribute('data-field-id', element.dataFieldId);
            elementDiv.setAttribute('data-original-content', element.content || 'Text');

            // 從 dataFieldId 中提取 dataIndex (如 "data1" -> 0)
            const match = element.dataFieldId.match(/(\d+)/);
            if (match) {
              const dataIndex = parseInt(match[1]) - 1; // 將 "data1" 映射到索引 0
              elementDiv.setAttribute('data-index', String(dataIndex));
            } else {
              elementDiv.setAttribute('data-index', '0');
            }

            // 顯示默認文本 'TEXT'
            textContainer.textContent = 'TEXT';
          } else {
            // 沒有綁定，使用元素的原始內容
            elementDiv.setAttribute('data-has-binding', 'false');
            elementDiv.setAttribute('data-original-content', element.content || 'Text');
            textContainer.textContent = element.content || '';
          }

          elementDiv.appendChild(textContainer);
        } else if (element.type === 'rectangle' || element.type === 'square') {
          // 矩形/正方形元素渲染
          elementDiv.style.border = `${element.lineWidth || 1}px solid ${element.lineColor || '#000'}`;
          elementDiv.style.backgroundColor = element.fillColor || 'transparent';
        } else if (element.type === 'circle' || element.type === 'ellipse') {
          // 圓形/橢圓形元素渲染
          elementDiv.style.border = `${element.lineWidth || 1}px solid ${element.lineColor || '#000'}`;
          elementDiv.style.backgroundColor = element.fillColor || 'transparent';
          elementDiv.style.borderRadius = '50%';
        } else if (element.type === 'line') {
          // 線條元素渲染 - 使用與 template editor 相同的方式
          const lineWidth = element.lineWidth || 1;
          const lineColor = element.lineColor || '#000';

          // 清除默認的邊框和背景
          elementDiv.style.border = 'none';
          elementDiv.style.backgroundColor = 'transparent';
          elementDiv.style.overflow = 'visible'; // 確保線條不會被裁剪

          // 計算線條的尺寸和方向
          // 在 template editor 中，線條的寬度和高度可能為負數，表示方向
          const boxWidth = Math.abs(element.width);
          const boxHeight = Math.abs(element.height);

          // 計算線條的起點和終點
          // 根據寬度和高度的正負確定線條的方向
          const x1 = element.width < 0 ? boxWidth : 0;
          const y1 = element.height < 0 ? boxHeight : 0;
          const x2 = element.width < 0 ? 0 : boxWidth;
          const y2 = element.height < 0 ? 0 : boxHeight;

          // 創建 SVG 元素來繪製線條
          const svgElement = document.createElementNS("http://www.w3.org/2000/svg", "svg");
          svgElement.setAttribute('width', '100%');
          svgElement.setAttribute('height', '100%');
          svgElement.style.display = 'block';
          svgElement.style.overflow = 'visible';
          svgElement.style.position = 'absolute'; // 確保 SVG 元素的定位正確
          svgElement.style.top = '0';
          svgElement.style.left = '0';
          svgElement.style.width = '100%';
          svgElement.style.height = '100%';

          // 創建線條元素
          const lineElement = document.createElementNS("http://www.w3.org/2000/svg", "line");
          lineElement.setAttribute('x1', String(x1));
          lineElement.setAttribute('y1', String(y1));
          lineElement.setAttribute('x2', String(x2));
          lineElement.setAttribute('y2', String(y2));
          lineElement.setAttribute('stroke', lineColor);
          lineElement.setAttribute('stroke-width', String(lineWidth));
          lineElement.setAttribute('stroke-linecap', 'round'); // 增加圓角端點

          // 添加一個額外的線條元素，作為備份
          const backupLineElement = document.createElementNS("http://www.w3.org/2000/svg", "line");
          backupLineElement.setAttribute('x1', String(x1));
          backupLineElement.setAttribute('y1', String(y1));
          backupLineElement.setAttribute('x2', String(x2));
          backupLineElement.setAttribute('y2', String(y2));
          backupLineElement.setAttribute('stroke', lineColor);
          backupLineElement.setAttribute('stroke-width', String(lineWidth + 0.5)); // 稍微增加線寬，確保可見
          backupLineElement.setAttribute('stroke-linecap', 'round');
          backupLineElement.setAttribute('stroke-opacity', '0.5'); // 半透明

          svgElement.appendChild(backupLineElement);
          svgElement.appendChild(lineElement);
          elementDiv.appendChild(svgElement);
        } else if (element.type === 'image') {
          // 圖像元素渲染
          // 確保圖片元素容器是透明的
          elementDiv.style.backgroundColor = 'transparent';
          elementDiv.style.border = 'none';

          if (element.imageUrl) {
            const imgElement = document.createElement('img');
            // 使用 processImageUrl 處理圖片 URL，確保遠端連線時能正確載入
            const processedUrl = processImageUrl(element.imageUrl);
            imgElement.src = processedUrl;
            imgElement.style.width = '100%';
            imgElement.style.height = '100%';
            imgElement.style.objectFit = 'contain';
            imgElement.style.backgroundColor = 'transparent';

            // 添加載入成功和錯誤處理
            imgElement.onload = () => {
              console.log('圖片載入成功:', element.imageUrl, '處理後的URL:', processedUrl);
            };

            imgElement.onerror = () => {
              console.error('圖片載入失敗:', element.imageUrl, '處理後的URL:', processedUrl);
              console.error('請檢查圖片URL是否正確，以及服務器是否可訪問');
            };

            elementDiv.appendChild(imgElement);
          }
        } else if (element.type === 'qr-code') {
          // QR Code 元素渲染 - 處理數據綁定並生成實際的 QR Code
          await renderQRCodeElement(elementDiv, element, sampleDataByIndex, []);
        } else if (element.type === 'barcode') {
          // 條碼元素渲染 - 處理數據綁定並生成實際的條碼
          await renderBarcodeElement(elementDiv, element, sampleDataByIndex, []);
        } else if (element.type === 'icon') {
          // 圖標元素渲染 - 使用 lucide-react
          elementDiv.style.border = 'none';
          elementDiv.style.backgroundColor = 'transparent';
          elementDiv.style.display = 'flex';
          elementDiv.style.justifyContent = 'center';
          elementDiv.style.alignItems = 'center';
          elementDiv.style.overflow = 'visible';

          // 獲取圖標類型，確保不為null或undefined
          const iconType = element.iconType || 'circle';
          const iconColor = element.lineColor || '#000';
          const iconSize = Math.min(element.width, element.height) * 0.8;
          const strokeWidth = element.lineWidth || 2;

          // 將圖標類型轉換為駝峰式命名，例如 'alert-circle' 轉換為 'AlertCircle'
          const iconName = iconType.split('-').map((part: string) => part.charAt(0).toUpperCase() + part.slice(1)).join('');

          // 創建一個 React 元素的容器
          const iconContainer = document.createElement('div');
          iconContainer.id = `icon-container-${index}`;
          iconContainer.style.width = '100%';
          iconContainer.style.height = '100%';
          iconContainer.style.display = 'flex';
          iconContainer.style.justifyContent = 'center';
          iconContainer.style.alignItems = 'center';

          // 添加到元素中
          elementDiv.appendChild(iconContainer);

          // 使用 React 渲染圖標
          const IconComponent = (lucideIcons as any)[iconName] || (lucideIcons as any).Circle;
          if (IconComponent) {
            // 在下一個微任務中渲染圖標，確保 DOM 已經準備好
            setTimeout(() => {
              try {
                const iconElement = React.createElement(IconComponent, {
                  color: iconColor,
                  size: iconSize,
                  strokeWidth: strokeWidth
                });

                // 使用 createRoot 渲染圖標
                const container = document.getElementById(`icon-container-${index}`);
                if (container) {
                  const root = createRoot(container);
                  root.render(iconElement);
                }
              } catch (error) {
                console.error('渲染圖標時發生錯誤:', error);
              }
            }, 0);
          }
        } else {
          // 未知元素類型，使用通用樣式
          elementDiv.style.border = '1px dashed #999';
          elementDiv.style.display = 'flex';
          elementDiv.style.justifyContent = 'center';
          elementDiv.style.alignItems = 'center';
          elementDiv.textContent = element.type || '未知元素';
        }

        // 將元素添加到畫布
        elementsContainer.appendChild(elementDiv);
      }

      // 等待所有圖片載入完成
      console.log('等待所有圖片載入完成...');
      const imageElements = elementsContainer.querySelectorAll('img');
      const imageLoadPromises = Array.from(imageElements).map(img => {
        return new Promise<void>((resolve, reject) => {
          if (img.complete) {
            // 圖片已經載入完成
            resolve();
          } else {
            // 等待圖片載入
            const onLoad = () => {
              img.removeEventListener('load', onLoad);
              img.removeEventListener('error', onError);
              resolve();
            };
            const onError = () => {
              img.removeEventListener('load', onLoad);
              img.removeEventListener('error', onError);
              console.warn('圖片載入失敗，但繼續生成預覽:', img.src);
              resolve(); // 即使載入失敗也繼續
            };
            img.addEventListener('load', onLoad);
            img.addEventListener('error', onError);
          }
        });
      });

      // 等待所有圖片載入完成（最多等待5秒）
      await Promise.race([
        Promise.all(imageLoadPromises),
        new Promise(resolve => setTimeout(resolve, 5000)) // 5秒超時
      ]);
      console.log('圖片載入完成，共', imageElements.length, '張圖片');

      // 處理綁定元素，使其顯示實際數據
      console.log('開始處理綁定文字元素...');
      const originalStyles = await processTextBindings();
      console.log('綁定文字元素處理完成，共處理了', originalStyles.length, '個元素');

      // 創建一個符合 HTMLDivElement 的對象
      const canvasRef = {
        current: containerRef.current as unknown as HTMLDivElement
      };

      // 渲染畫布為圖像
      const renderedCanvas = await renderCanvasToImage(
        canvasRef,
        canvasWidth,
        canvasHeight
      );

      // 恢復綁定元素的原始樣式和內容
      restoreTextBindings(originalStyles);

      if (!renderedCanvas) {
        return null;
      }

      // 應用選定的效果
      // 優先使用模板的 colorType，如果沒有則使用傳統的 effectType
      let previewCanvas: HTMLCanvasElement;

      if (template.color) {
        // 使用新的顏色轉換模組，根據模板的 colorType 自動選擇最佳轉換方式
        console.log(`使用模板顏色類型進行轉換: ${template.color}`);
        const convertedCanvas = convertImageByColorType(renderedCanvas, template.color, { threshold });
        previewCanvas = convertedCanvas || renderedCanvas;
      } else {
        // 向後兼容：使用傳統的 effectType 方式
        console.log(`使用傳統效果類型進行轉換: ${effectType}`);
        previewCanvas = convertImageByEffectType(renderedCanvas, effectType, threshold);
      }

      // 將預覽圖轉換為 base64 數據
      const previewData = previewCanvas.toDataURL('image/png');

      // 設置預覽圖
      setPreviewImage(previewData);

      // 不再自動保存預覽圖到設備記錄
      // 預覽圖只在用戶按下確定按鈕時才會保存到數據庫

      // 如果有回調函數，調用它
      if (onPreviewGenerated) {
        onPreviewGenerated(previewData);
      }

      return previewData;
    } catch (error) {
      console.error('生成預覽圖時發生錯誤:', error);

      // 如果有回調函數，調用它
      if (onPreviewGenerated) {
        onPreviewGenerated(null);
      }

      return null;
    }
  };

  // 當模板或綁定數據變更時生成預覽圖
  useEffect(() => {
    if (template) {
      // 確保容器在渲染前是可見的
      if (containerRef.current) {
        containerRef.current.style.display = 'block';
        containerRef.current.style.visibility = 'visible';
        containerRef.current.style.opacity = '1';

        // 檢測是否為縱向模板，並設置適當的樣式
        if (template.screenSize) {
          const sizeMatch = template.screenSize.match(/(\d+)x(\d+)/);
          if (sizeMatch && sizeMatch[1] && sizeMatch[2]) {
            const width = parseInt(sizeMatch[1], 10);
            const height = parseInt(sizeMatch[2], 10);
            const isPortrait = height > width;

            if (isPortrait) {
              console.log('useEffect 檢測到縱向模板，設置適當的樣式');
              containerRef.current.style.overflow = 'hidden';
            }
          }
        }
      }

      // 使用 requestAnimationFrame 確保在下一幀渲染
      requestAnimationFrame(() => {
        // 即使沒有綁定數據也生成預覽圖
        generatePreview();
      });
    }
  }, [template, bindingData, storeData, effectType, threshold]);

  return (
    <div className="preview-component">
      {/* 隱藏的渲染容器 - 但確保它可以正確渲染 */}
      <div
        ref={containerRef}
        style={{
          position: 'absolute',
          left: '-9999px',
          top: '0',
          visibility: 'hidden',
          display: 'block', // 確保容器是可見的，即使它被隱藏在視口外
          opacity: '1' // 確保容器是可見的，即使它被隱藏在視口外
        }}
      />

      {/* 顯示預覽圖 */}
      <div className="preview-display" style={{ width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        {previewImage ? (
          <div style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            overflow: 'hidden' // 防止內容溢出
          }}>
            <img
              src={previewImage}
              alt="預覽"
              style={{
                maxWidth: '95%',
                maxHeight: '95%',
                objectFit: 'contain'
              }}
              onLoad={(e) => {
                // 獲取圖片的原始尺寸
                const img = e.target as HTMLImageElement;
                const isPortrait = img.naturalHeight > img.naturalWidth;
                console.log(`預覽圖方向: ${isPortrait ? '縱向' : '橫向'}, 尺寸: ${img.naturalWidth}x${img.naturalHeight}`);

                // 獲取父容器尺寸
                const container = img.parentElement;
                if (!container) return;

                const containerWidth = container.clientWidth;
                const containerHeight = container.clientHeight;
                console.log(`容器尺寸: ${containerWidth}x${containerHeight}`);

                // 根據圖片方向調整樣式
                if (isPortrait) {
                  // 縱向圖片優先保持高度，確保完整顯示
                  img.style.maxHeight = '95%';
                  img.style.maxWidth = '70%'; // 縱向圖片寬度縮小，確保完整顯示
                } else {
                  // 橫向圖片優先保持寬度
                  img.style.maxWidth = '95%';
                  img.style.maxHeight = '95%';
                }

                console.log(`應用自適應樣式: ${isPortrait ? '縱向模式' : '橫向模式'}`);
              }}
            />
          </div>
        ) : (
          <p className="text-gray-500">生成預覽中...</p>
        )}
      </div>
    </div>
  );
};

export default PreviewComponent;
