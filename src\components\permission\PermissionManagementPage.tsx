import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RoleManagement } from './RoleManagement';
import { UserManagement } from './UserManagement';
import { PermissionAssignment } from './PermissionAssignment';

type TabType = 'roles' | 'users' | 'permissions';

export const PermissionManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<TabType>('roles');

  const tabs = [
    { id: 'roles' as TabType, label: t('permission.roleManagement') },
    { id: 'users' as TabType, label: t('permission.userManagement') },
    { id: 'permissions' as TabType, label: t('permission.permissionAssignment') },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'roles':
        return <RoleManagement />;
      case 'users':
        return <UserManagement />;
      case 'permissions':
        return <PermissionAssignment />;
      default:
        return null;
    }
  };

  return (
    <div className="p-8 lg:px-4 py-2">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="border-b">
            <nav className="flex">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-6 py-3 text-sm font-medium transition-colors
                    ${activeTab === tab.id
                      ? 'bg-orange-500 text-white'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                    }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
          <div className="min-h-[600px]">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PermissionManagementPage;
