/**
 * 格式處理器
 * 根據 Gateway 指定的格式進行數據處理
 */

const { RAWDATA_FORMATS, createFormatProcessingResult } = require('../types');
const { globalRegistry } = require('../compressors/compressionRegistry');

/**
 * 格式處理器類
 */
class FormatProcessor {
  constructor(registry = globalRegistry) {
    this.registry = registry;
    this.processingStats = {
      totalProcessed: 0,
      successCount: 0,
      failureCount: 0,
      formatUsage: new Map(),
      totalProcessingTime: 0
    };
  }
  
  /**
   * 根據指定格式處理數據
   * @param {string} format - 目標格式
   * @param {Uint8Array} pixelData - 像素數據
   * @returns {Object} 處理結果
   */
  processFormat(format, pixelData) {
    const startTime = this.getTimestamp();
    
    try {
      // 驗證輸入
      this.validateInput(format, pixelData);
      
      // 更新統計
      this.processingStats.totalProcessed++;
      this.updateFormatUsage(format);
      
      if (format === RAWDATA_FORMATS.RAWDATA) {
        // 不處理，直接返回
        const processingTime = this.getTimestamp() - startTime;
        this.processingStats.successCount++;
        this.processingStats.totalProcessingTime += processingTime;
        
        return createFormatProcessingResult(
          true,
          format,
          pixelData,
          pixelData,
          processingTime,
          'No processing required for rawdata format'
        );
      }
      
      const processor = this.registry.getCompressor(format);
      if (!processor) {
        throw new Error(`Unsupported format: ${format}`);
      }
      
      const result = processor.compress(pixelData);
      const processingTime = this.getTimestamp() - startTime;
      
      if (result.success) {
        this.processingStats.successCount++;
        this.processingStats.totalProcessingTime += processingTime;
        
        return createFormatProcessingResult(
          true,
          format,
          pixelData,
          result.data,
          result.processingTime,
          `Processed with ${format} compression`
        );
      } else {
        throw new Error(result.error || 'Processing failed');
      }
      
    } catch (error) {
      const processingTime = this.getTimestamp() - startTime;
      this.processingStats.failureCount++;
      this.processingStats.totalProcessingTime += processingTime;
      
      console.error(`Format processing failed for ${format}:`, error.message);
      
      return createFormatProcessingResult(
        false,
        format,
        pixelData,
        pixelData, // 返回原始數據作為備用
        processingTime,
        'Processing failed, using original data',
        error.message
      );
    }
  }
  
  /**
   * 檢查格式是否支援
   * @param {string} format - 格式名稱
   * @returns {boolean} 是否支援
   */
  isFormatSupported(format) {
    if (!format || typeof format !== 'string') {
      return false;
    }
    
    return format === RAWDATA_FORMATS.RAWDATA || this.registry.isFormatSupported(format);
  }
  
  /**
   * 獲取所有支援的格式
   * @returns {string[]} 支援的格式列表
   */
  getSupportedFormats() {
    const registryFormats = this.registry.getSupportedFormats();
    
    // 確保 rawdata 總是在列表中
    if (!registryFormats.includes(RAWDATA_FORMATS.RAWDATA)) {
      return [RAWDATA_FORMATS.RAWDATA, ...registryFormats];
    }
    
    return registryFormats;
  }
  
  /**
   * 分析格式對數據的適用性
   * @param {string} format - 格式名稱
   * @param {Uint8Array} pixelData - 像素數據
   * @returns {Object} 分析結果
   */
  analyzeFormatSuitability(format, pixelData) {
    try {
      this.validateInput(format, pixelData);
      
      if (format === RAWDATA_FORMATS.RAWDATA) {
        return {
          suitable: true,
          estimatedRatio: 1.0,
          estimatedSize: pixelData.length,
          reason: 'Raw data format always suitable',
          details: {
            processingRequired: false,
            compressionPotential: 0
          }
        };
      }
      
      const processor = this.registry.getCompressor(format);
      if (!processor) {
        return {
          suitable: false,
          estimatedRatio: 1.0,
          estimatedSize: pixelData.length,
          reason: `Format ${format} not supported`,
          details: {
            processingRequired: false,
            compressionPotential: 0
          }
        };
      }
      
      const estimatedRatio = processor.estimateCompressionRatio(pixelData);
      const suitable = processor.isSuitableFor(pixelData);
      
      return {
        suitable,
        estimatedRatio,
        estimatedSize: Math.ceil(pixelData.length * estimatedRatio),
        reason: suitable ? 
          `Good compression potential with ${format}` : 
          `Limited compression benefit with ${format}`,
        details: {
          processingRequired: true,
          compressionPotential: Math.round((1 - estimatedRatio) * 100)
        }
      };
      
    } catch (error) {
      return {
        suitable: false,
        estimatedRatio: 1.0,
        estimatedSize: pixelData.length,
        reason: `Analysis failed: ${error.message}`,
        details: {
          processingRequired: false,
          compressionPotential: 0
        }
      };
    }
  }
  
  /**
   * 獲取處理統計信息
   * @returns {Object} 統計信息
   */
  getStats() {
    const avgProcessingTime = this.processingStats.totalProcessed > 0 ? 
      this.processingStats.totalProcessingTime / this.processingStats.totalProcessed : 0;
    
    const successRate = this.processingStats.totalProcessed > 0 ? 
      (this.processingStats.successCount / this.processingStats.totalProcessed) * 100 : 0;
    
    return {
      totalProcessed: this.processingStats.totalProcessed,
      successCount: this.processingStats.successCount,
      failureCount: this.processingStats.failureCount,
      successRate: Math.round(successRate * 100) / 100,
      averageProcessingTime: Math.round(avgProcessingTime * 100) / 100,
      totalProcessingTime: Math.round(this.processingStats.totalProcessingTime * 100) / 100,
      formatUsage: Object.fromEntries(this.processingStats.formatUsage)
    };
  }
  
  /**
   * 重置統計信息
   */
  resetStats() {
    this.processingStats = {
      totalProcessed: 0,
      successCount: 0,
      failureCount: 0,
      formatUsage: new Map(),
      totalProcessingTime: 0
    };
    
    console.log('📊 Format processor stats reset');
  }
  
  /**
   * 驗證輸入參數
   * @param {string} format - 格式名稱
   * @param {Uint8Array} pixelData - 像素數據
   * @private
   */
  validateInput(format, pixelData) {
    if (!format || typeof format !== 'string') {
      throw new Error('Format must be a non-empty string');
    }
    
    if (!pixelData) {
      throw new Error('Pixel data is required');
    }
    
    if (!(pixelData instanceof Uint8Array)) {
      throw new Error('Pixel data must be Uint8Array');
    }
    
    if (pixelData.length === 0) {
      throw new Error('Pixel data cannot be empty');
    }
  }
  
  /**
   * 更新格式使用統計
   * @param {string} format - 格式名稱
   * @private
   */
  updateFormatUsage(format) {
    const currentCount = this.processingStats.formatUsage.get(format) || 0;
    this.processingStats.formatUsage.set(format, currentCount + 1);
  }
  
  /**
   * 獲取時間戳
   * @returns {number} 時間戳
   * @private
   */
  getTimestamp() {
    if (typeof performance !== 'undefined' && performance.now) {
      return performance.now();
    } else {
      return Date.now();
    }
  }
  
  /**
   * 列印處理器狀態
   */
  printStatus() {
    const stats = this.getStats();
    const supportedFormats = this.getSupportedFormats();
    
    console.log('\n🔧 Format Processor Status:');
    console.log(`  Supported Formats: ${supportedFormats.join(', ')}`);
    console.log(`  Total Processed: ${stats.totalProcessed}`);
    console.log(`  Success Rate: ${stats.successRate}%`);
    console.log(`  Average Processing Time: ${stats.averageProcessingTime}ms`);
    
    if (stats.formatUsage && Object.keys(stats.formatUsage).length > 0) {
      console.log('  Format Usage:');
      Object.entries(stats.formatUsage).forEach(([format, count]) => {
        console.log(`    - ${format}: ${count} times`);
      });
    }
    
    console.log('');
  }
}

module.exports = FormatProcessor;
