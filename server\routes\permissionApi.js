const express = require('express');
const router = express.Router();
const Permission = require('../models/Permission');
const { authenticate, checkPermission } = require('../middleware/auth');
const { ObjectId } = require('mongodb');

// 數據庫連接函數引用
let connectDBFunction;

// 初始化數據庫連接
function initDB(connectDB) {
  connectDBFunction = connectDB;
  return connectDBFunction;
}

/**
 * 獲取所有權限分配
 * GET /api/permissions
 */
router.get('/permissions', authenticate, async (req, res) => {
  try {
    // 獲取查詢參數
    const { userId, roleId, scopeType, scope, page = 1, limit = 10 } = req.query;
    
    // 構建過濾條件
    const filter = {};
    
    if (userId) {
      filter.userId = userId;
    }
    
    if (roleId) {
      filter.roleId = roleId;
    }
    
    if (scopeType) {
      filter.scopeType = scopeType;
    }
    
    if (scope) {
      filter.scope = scope;
    }
    
    // 獲取數據庫連接
    const { db } = await connectDBFunction();
    
    // 獲取權限分配列表（帶有用戶和角色信息）
    const permissions = await Permission.findAllWithDetails(db, filter);
    
    // 獲取總數
    const total = await db.collection('permissions').countDocuments(filter);
    
    res.json({
      permissions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total
      }
    });
  } catch (error) {
    console.error('獲取權限分配列表錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 獲取單個權限分配
 * GET /api/permissions/:id
 */
router.get('/permissions/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    
    // 獲取數據庫連接
    const { db } = await connectDBFunction();
    
    // 查找權限分配
    const permission = await Permission.findById(db, id);
    
    if (!permission) {
      return res.status(404).json({ error: '權限分配不存在' });
    }
    
    // 獲取用戶和角色信息
    const User = require('../models/User');
    const Role = require('../models/Role');
    
    const user = await User.findById(db, permission.userId.toString());
    const role = await Role.findById(db, permission.roleId.toString());
    
    // 移除用戶密碼
    if (user) {
      const { password, ...userWithoutPassword } = user;
      permission.user = userWithoutPassword;
    }
    
    permission.role = role;
    
    res.json(permission);
  } catch (error) {
    console.error('獲取權限分配錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 創建權限分配
 * POST /api/permissions
 */
router.post('/permissions', authenticate, checkPermission('permission:create'), async (req, res) => {
  try {
    const { userId, roleId, scope, scopeType } = req.body;
    
    if (!userId || !roleId || !scope || !scopeType) {
      return res.status(400).json({ error: '所有字段都是必填的' });
    }
    
    // 獲取數據庫連接
    const { db } = await connectDBFunction();
    
    // 檢查用戶是否存在
    const User = require('../models/User');
    const user = await User.findById(db, userId);
    
    if (!user) {
      return res.status(404).json({ error: '用戶不存在' });
    }
    
    // 檢查角色是否存在
    const Role = require('../models/Role');
    const role = await Role.findById(db, roleId);
    
    if (!role) {
      return res.status(404).json({ error: '角色不存在' });
    }
    
    // 創建權限分配
    const permission = await Permission.createPermission(db, {
      userId,
      roleId,
      scope,
      scopeType
    });
    
    res.status(201).json(permission);
  } catch (error) {
    console.error('創建權限分配錯誤:', error);
    
    if (error.message === '用戶已有相同範圍的權限分配') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 更新權限分配
 * PUT /api/permissions/:id
 */
router.put('/permissions/:id', authenticate, checkPermission('permission:update'), async (req, res) => {
  try {
    const { id } = req.params;
    const { roleId, scope, scopeType } = req.body;
    
    // 獲取數據庫連接
    const { db } = await connectDBFunction();
    
    // 檢查權限分配是否存在
    const existingPermission = await Permission.findById(db, id);
    
    if (!existingPermission) {
      return res.status(404).json({ error: '權限分配不存在' });
    }
    
    // 如果提供了角色 ID，檢查角色是否存在
    if (roleId) {
      const Role = require('../models/Role');
      const role = await Role.findById(db, roleId);
      
      if (!role) {
        return res.status(404).json({ error: '角色不存在' });
      }
    }
    
    // 更新權限分配
    await Permission.updatePermission(db, id, {
      roleId,
      scope,
      scopeType
    });
    
    // 獲取更新後的權限分配
    const updatedPermission = await Permission.findById(db, id);
    
    res.json(updatedPermission);
  } catch (error) {
    console.error('更新權限分配錯誤:', error);
    
    if (error.message === '用戶已有相同範圍的權限分配') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 刪除權限分配
 * DELETE /api/permissions/:id
 */
router.delete('/permissions/:id', authenticate, checkPermission('permission:delete'), async (req, res) => {
  try {
    const { id } = req.params;
    
    // 獲取數據庫連接
    const { db } = await connectDBFunction();
    
    // 檢查權限分配是否存在
    const existingPermission = await Permission.findById(db, id);
    
    if (!existingPermission) {
      return res.status(404).json({ error: '權限分配不存在' });
    }
    
    // 刪除權限分配
    await Permission.deletePermission(db, id);
    
    res.json({ message: '權限分配刪除成功' });
  } catch (error) {
    console.error('刪除權限分配錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 批量刪除權限分配
 * DELETE /api/permissions
 */
router.delete('/permissions', authenticate, checkPermission('permission:delete'), async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ error: '請提供要刪除的權限分配 ID 列表' });
    }
    
    // 獲取數據庫連接
    const { db } = await connectDBFunction();
    
    // 批量刪除權限分配
    await Permission.batchDeletePermissions(db, ids);
    
    res.json({ message: '權限分配批量刪除成功' });
  } catch (error) {
    console.error('批量刪除權限分配錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 批量創建權限分配
 * POST /api/permissions/batch
 */
router.post('/permissions/batch', authenticate, checkPermission('permission:create'), async (req, res) => {
  try {
    const { userIds, roleId, scope, scopeType } = req.body;
    
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0 || !roleId || !scope || !scopeType) {
      return res.status(400).json({ error: '所有字段都是必填的' });
    }
    
    // 獲取數據庫連接
    const { db } = await connectDBFunction();
    
    // 檢查角色是否存在
    const Role = require('../models/Role');
    const role = await Role.findById(db, roleId);
    
    if (!role) {
      return res.status(404).json({ error: '角色不存在' });
    }
    
    // 批量創建權限分配
    const results = [];
    const errors = [];
    
    for (const userId of userIds) {
      try {
        // 檢查用戶是否存在
        const User = require('../models/User');
        const user = await User.findById(db, userId);
        
        if (!user) {
          errors.push({ userId, error: '用戶不存在' });
          continue;
        }
        
        // 檢查用戶是否已有相同範圍的權限分配
        const existingPermission = await db.collection('permissions').findOne({
          userId: new ObjectId(userId),
          scope
        });
        
        if (existingPermission) {
          errors.push({ userId, error: '用戶已有相同範圍的權限分配' });
          continue;
        }
        
        // 創建權限分配
        const permission = await Permission.createPermission(db, {
          userId,
          roleId,
          scope,
          scopeType
        });
        
        results.push(permission);
      } catch (error) {
        errors.push({ userId, error: error.message });
      }
    }
    
    res.status(201).json({
      message: `成功創建 ${results.length} 個權限分配，失敗 ${errors.length} 個`,
      results,
      errors
    });
  } catch (error) {
    console.error('批量創建權限分配錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

module.exports = { router, initDB };
