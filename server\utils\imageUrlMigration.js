/**
 * 圖片URL遷移工具
 * 將資料庫中的完整圖片URL轉換為文件ID格式
 */

const { MongoClient } = require('mongodb');

/**
 * 從完整URL中提取文件ID
 * @param {string} url 完整的圖片URL
 * @returns {string} 文件ID或原URL
 */
function extractFileIdFromUrl(url) {
  if (!url || typeof url !== 'string') return url;

  // 如果已經是文件ID格式，直接返回
  if (/^[a-f0-9]{24}$/i.test(url)) {
    return url;
  }

  // 從相對路徑提取文件ID
  if (url.startsWith('/api/files/')) {
    return url.replace('/api/files/', '');
  }

  // 從完整URL提取文件ID
  const fileIdMatch = url.match(/\/api\/files\/([a-f0-9]{24})/i);
  if (fileIdMatch) {
    return fileIdMatch[1];
  }

  // 無法提取，返回原URL
  return url;
}

/**
 * 遷移模板集合中的圖片URL
 * @param {Object} db 資料庫連接
 * @returns {Promise<Object>} 遷移結果
 */
async function migrateTemplateImages(db) {
  const collection = db.collection('templates');
  const templates = await collection.find({}).toArray();
  
  let updatedCount = 0;
  let processedCount = 0;

  for (const template of templates) {
    let hasChanges = false;
    const updates = {};

    // 處理模板預覽圖
    if (template.previewImage && typeof template.previewImage === 'string') {
      const newPreviewImage = extractFileIdFromUrl(template.previewImage);
      if (newPreviewImage !== template.previewImage) {
        updates.previewImage = newPreviewImage;
        hasChanges = true;
      }
    }

    // 處理模板元素中的圖片URL
    if (template.elements && Array.isArray(template.elements)) {
      const newElements = template.elements.map(element => {
        if (element.imageUrl && typeof element.imageUrl === 'string') {
          const newImageUrl = extractFileIdFromUrl(element.imageUrl);
          if (newImageUrl !== element.imageUrl) {
            hasChanges = true;
            return { ...element, imageUrl: newImageUrl };
          }
        }
        return element;
      });

      if (hasChanges) {
        updates.elements = newElements;
      }
    }

    // 如果有變更，更新資料庫
    if (hasChanges) {
      await collection.updateOne(
        { _id: template._id },
        { $set: updates }
      );
      updatedCount++;
      console.log(`已更新模板: ${template.name} (ID: ${template._id})`);
    }

    processedCount++;
  }

  return {
    collection: 'templates',
    processed: processedCount,
    updated: updatedCount
  };
}

/**
 * 遷移設備集合中的預覽圖URL
 * @param {Object} db 資料庫連接
 * @returns {Promise<Object>} 遷移結果
 */
async function migrateDeviceImages(db) {
  const collection = db.collection('devices');
  const devices = await collection.find({}).toArray();
  
  let updatedCount = 0;
  let processedCount = 0;

  for (const device of devices) {
    let hasChanges = false;
    const updates = {};

    // 處理設備預覽圖
    if (device.previewImage && typeof device.previewImage === 'string') {
      const newPreviewImage = extractFileIdFromUrl(device.previewImage);
      if (newPreviewImage !== device.previewImage) {
        updates.previewImage = newPreviewImage;
        hasChanges = true;
      }
    }

    // 如果有變更，更新資料庫
    if (hasChanges) {
      await collection.updateOne(
        { _id: device._id },
        { $set: updates }
      );
      updatedCount++;
      console.log(`已更新設備: ${device.name || device._id} (ID: ${device._id})`);
    }

    processedCount++;
  }

  return {
    collection: 'devices',
    processed: processedCount,
    updated: updatedCount
  };
}

/**
 * 遷移門店資料集合中的圖片URL
 * @param {Object} db 資料庫連接
 * @returns {Promise<Object>} 遷移結果
 */
async function migrateStoreDataImages(db) {
  const collection = db.collection('storeData');
  const storeDataList = await collection.find({}).toArray();
  
  let updatedCount = 0;
  let processedCount = 0;

  for (const storeData of storeDataList) {
    let hasChanges = false;
    const updates = {};

    // 處理門店資料中的圖片欄位
    if (storeData.data && typeof storeData.data === 'object') {
      const newData = { ...storeData.data };
      
      for (const [key, value] of Object.entries(newData)) {
        if (typeof value === 'string' && value.includes('/api/files/')) {
          const newValue = extractFileIdFromUrl(value);
          if (newValue !== value) {
            newData[key] = newValue;
            hasChanges = true;
          }
        }
      }

      if (hasChanges) {
        updates.data = newData;
      }
    }

    // 如果有變更，更新資料庫
    if (hasChanges) {
      await collection.updateOne(
        { _id: storeData._id },
        { $set: updates }
      );
      updatedCount++;
      console.log(`已更新門店資料: ${storeData._id}`);
    }

    processedCount++;
  }

  return {
    collection: 'storeData',
    processed: processedCount,
    updated: updatedCount
  };
}

/**
 * 執行完整的圖片URL遷移
 * @param {string} mongoUrl MongoDB連接字符串
 * @param {string} dbName 資料庫名稱
 * @returns {Promise<Array>} 遷移結果
 */
async function migrateImageUrls(mongoUrl, dbName) {
  const client = new MongoClient(mongoUrl);
  
  try {
    await client.connect();
    console.log('已連接到MongoDB');
    
    const db = client.db(dbName);
    const results = [];

    // 遷移模板圖片
    console.log('\n開始遷移模板圖片...');
    const templateResult = await migrateTemplateImages(db);
    results.push(templateResult);

    // 遷移設備圖片
    console.log('\n開始遷移設備圖片...');
    const deviceResult = await migrateDeviceImages(db);
    results.push(deviceResult);

    // 遷移門店資料圖片
    console.log('\n開始遷移門店資料圖片...');
    const storeDataResult = await migrateStoreDataImages(db);
    results.push(storeDataResult);

    return results;
  } finally {
    await client.close();
    console.log('已關閉MongoDB連接');
  }
}

module.exports = {
  extractFileIdFromUrl,
  migrateTemplateImages,
  migrateDeviceImages,
  migrateStoreDataImages,
  migrateImageUrls
};

// 如果直接執行此腳本
if (require.main === module) {
  const mongoUrl = process.env.MONGODB_URI || 'mongodb://localhost:27017';
  const dbName = process.env.DB_NAME || 'epd_manager';

  console.log('開始圖片URL遷移...');
  console.log(`MongoDB URL: ${mongoUrl}`);
  console.log(`資料庫名稱: ${dbName}`);

  migrateImageUrls(mongoUrl, dbName)
    .then(results => {
      console.log('\n=== 遷移完成 ===');
      results.forEach(result => {
        console.log(`${result.collection}: 處理 ${result.processed} 筆，更新 ${result.updated} 筆`);
      });
      process.exit(0);
    })
    .catch(error => {
      console.error('遷移失敗:', error);
      process.exit(1);
    });
}
