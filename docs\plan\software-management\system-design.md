# 軟體管理系統設計規劃

## 1. 需求分析

### 1.1 功能需求

#### 核心功能
- **軟體上傳**: 支援bin檔案上傳，自動驗證CRC校驗和
- **格式解析**: 解析bin檔案格式，提取設備類型、功能類型、版本等資訊
- **分類管理**: 設備(EPD)與網關分區顯示，網關按功能分類
- **版本控制**: 支援多版本軟體管理，版本比較和升級路徑
- **狀態管理**: 軟體啟用/禁用控制，影響其他頁面的選單顯示
- **權限控制**: 基於角色的軟體管理權限

#### 業務規則
- bin檔案必須通過CRC驗證才能上傳
- 上傳後提取並儲存元數據，原始bin內容單獨存放
- 軟體資訊包含：檔案大小、上傳日期、版本、設備類型、功能類型
- 禁用的軟體不會出現在其他頁面的選單中
- 支援軟體版本回滾和升級

### 1.2 非功能需求

#### 效能要求
- 支援大檔案上傳（最大100MB）
- 檔案上傳進度顯示
- 快速的軟體列表查詢和過濾
- 高效的CRC驗證處理

#### 安全要求
- 檔案類型驗證（僅允許.bin檔案）
- 檔案內容安全掃描
- 上傳權限控制
- 操作日誌記錄

#### 可用性要求
- 直觀的用戶介面設計
- 清晰的錯誤提示
- 響應式設計支援
- 國際化支援

## 2. 系統架構

### 2.1 整體架構

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端UI層      │    │   API服務層     │    │   數據存儲層    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ 軟體管理頁面    │◄──►│ 軟體管理API     │◄──►│ MongoDB         │
│ 上傳介面        │    │ 檔案上傳API     │    │ GridFS          │
│ 軟體列表        │    │ 驗證服務        │    │ 軟體資訊集合    │
│ 詳細資訊        │    │ 權限控制        │    │ 版本記錄        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 模組劃分

#### 前端模組
- **SoftwareManagementTab**: 主要管理頁面
- **SoftwareUploadModal**: 軟體上傳對話框
- **SoftwareList**: 軟體列表組件
- **SoftwareDetail**: 軟體詳細資訊
- **SoftwareFilter**: 過濾和搜尋組件

#### 後端模組
- **softwareApi**: 軟體管理API路由
- **SoftwareModel**: 軟體資料模型
- **BinFileValidator**: bin檔案驗證器
- **SoftwareService**: 軟體業務邏輯服務

## 3. 資料庫設計

### 3.1 軟體資訊集合 (software)

```javascript
{
  _id: ObjectId,
  
  // 基本資訊
  name: String,                    // 軟體名稱
  description: String,             // 軟體描述
  version: String,                 // 版本號 (x.x.x.x)
  
  // 設備資訊 (從bin檔案解析)
  deviceType: String,              // 設備類型: 'gateway', 'epd'
  functionType: String,            // 功能類型: 'wifi', 'ble'
  
  // 檔案資訊
  originalFilename: String,        // 原始檔案名
  fileSize: Number,               // 檔案大小 (bytes)
  checksum: String,               // CRC32校驗和 (hex)
  
  // 儲存資訊
  binFileId: ObjectId,            // GridFS中原始bin檔案ID
  extractedBinId: ObjectId,       // GridFS中提取的純bin內容ID
  
  // 狀態管理
  status: String,                 // 'active', 'disabled', 'deprecated'
  isEnabled: Boolean,             // 是否啟用 (影響選單顯示)
  

  
  // 相容性資訊
  compatibility: {
    minHardwareVersion: String,
    maxHardwareVersion: String,
    supportedSizes: [String],      // 支援的螢幕尺寸
    requirements: [String]         // 其他需求
  },
  
  // 元數據
  uploadedBy: ObjectId,           // 上傳者ID
  uploadDate: Date,               // 上傳日期
  lastModified: Date,             // 最後修改日期
  modifiedBy: ObjectId,           // 最後修改者ID
  

  
  // 標籤和分類
  tags: [String],                 // 標籤
  category: String,               // 分類
  
  createdAt: Date,
  updatedAt: Date
}
```

### 3.2 軟體部署記錄集合 (software_deployments)

```javascript
{
  _id: ObjectId,
  softwareId: ObjectId,           // 軟體ID
  deviceId: ObjectId,             // 設備ID
  gatewayId: ObjectId,            // 網關ID
  storeId: ObjectId,              // 門店ID
  
  deploymentStatus: String,       // 'pending', 'in_progress', 'completed', 'failed'
  deploymentDate: Date,           // 部署日期
  completedDate: Date,            // 完成日期
  
  deployedBy: ObjectId,           // 部署者ID
  deploymentNotes: String,        // 部署備註
  
  // 部署結果
  result: {
    success: Boolean,
    errorMessage: String,
    deploymentTime: Number,       // 部署耗時 (秒)
    previousVersion: String       // 之前的版本
  },
  
  createdAt: Date,
  updatedAt: Date
}
```

### 3.3 索引設計

```javascript
// 軟體資訊集合索引
db.software.createIndex({ "deviceType": 1, "functionType": 1 });
db.software.createIndex({ "status": 1, "isEnabled": 1 });
db.software.createIndex({ "version": 1 });
db.software.createIndex({ "uploadDate": -1 });
db.software.createIndex({ "checksum": 1 }, { unique: true });

// 部署記錄集合索引
db.software_deployments.createIndex({ "softwareId": 1, "deploymentDate": -1 });
db.software_deployments.createIndex({ "deviceId": 1, "deploymentStatus": 1 });
db.software_deployments.createIndex({ "storeId": 1, "deploymentDate": -1 });
```

## 4. API設計

### 4.1 軟體管理API端點

#### 4.1.1 軟體列表查詢
```
GET /api/software
Query Parameters:
- deviceType: string (optional) - 設備類型過濾
- functionType: string (optional) - 功能類型過濾
- status: string (optional) - 狀態過濾
- page: number (optional) - 頁碼
- limit: number (optional) - 每頁數量
- search: string (optional) - 搜尋關鍵字

Response:
{
  "success": true,
  "data": {
    "software": [...],
    "total": number,
    "page": number,
    "limit": number
  }
}
```

#### 4.1.2 軟體上傳
```
POST /api/software/upload
Content-Type: multipart/form-data
Body:
- file: File (bin檔案)
- name: string - 軟體名稱
- description: string - 軟體描述
- category: string - 分類

Response:
{
  "success": true,
  "data": {
    "id": "ObjectId",
    "message": "軟體上傳成功"
  }
}
```

#### 4.1.3 軟體詳細資訊
```
GET /api/software/:id

Response:
{
  "success": true,
  "data": {
    // 軟體完整資訊
  }
}
```

#### 4.1.4 軟體狀態更新
```
PUT /api/software/:id/status
Body:
{
  "isEnabled": boolean,
  "status": string
}

Response:
{
  "success": true,
  "message": "狀態更新成功"
}
```

#### 4.1.5 軟體下載
```
GET /api/software/:id/download
Response: Binary file stream
```

### 4.2 錯誤處理

```javascript
// 標準錯誤回應格式
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "錯誤描述",
    "details": {} // 詳細錯誤資訊
  }
}

// 常見錯誤代碼
- INVALID_FILE_FORMAT: 無效的檔案格式
- CRC_VALIDATION_FAILED: CRC驗證失敗
- DUPLICATE_SOFTWARE: 重複的軟體
- INSUFFICIENT_PERMISSIONS: 權限不足
- FILE_TOO_LARGE: 檔案過大
- UNSUPPORTED_DEVICE_TYPE: 不支援的設備類型
```

## 5. UI設計規範

### 5.1 頁面佈局

#### 5.1.1 主要佈局結構
```
┌─────────────────────────────────────────────────────────────┐
│ 軟體管理                                                    │
├─────────────────────────────────────────────────────────────┤
│ [上傳軟體] [重新整理]                    [搜尋框] [過濾器] │
├─────────────────────────────────────────────────────────────┤
│ 設備分類標籤: [全部] [Gateway] [EPD]                       │
├─────────────────────────────────────────────────────────────┤
│ Gateway軟體                                                 │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│ │ WiFi軟體    │ │ BLE軟體     │ │ 其他功能    │           │
│ │ ├─軟體A v1.0│ │ ├─軟體C v2.1│ │             │           │
│ │ ├─軟體B v1.5│ │ ├─軟體D v1.0│ │             │           │
│ └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│ EPD軟體                                                     │
│ ┌─────────────┐                                             │
│ │ BLE軟體     │                                             │
│ │ ├─軟體E v3.0│                                             │
│ │ ├─軟體F v2.5│                                             │
│ └─────────────┘                                             │
└─────────────────────────────────────────────────────────────┘
```

#### 5.1.2 軟體卡片設計
```
┌─────────────────────────────────────────────────────────┐
│ 軟體名稱 v1.0.0.0                            [啟用/禁用] │
├─────────────────────────────────────────────────────────┤
│ 📱 Gateway WiFi                                         │
│ 📅 2024-12-20 10:30                                    │
│ 💾 2.5 MB                                              │
│ ✅ CRC: A1B2C3D4                                       │
├─────────────────────────────────────────────────────────┤
│ [詳細資訊] [下載] [部署] [刪除]                         │
└─────────────────────────────────────────────────────────┘
```

### 5.2 色彩設計

#### 5.2.1 狀態色彩
- **啟用狀態**: 綠色 (#10B981)
- **禁用狀態**: 灰色 (#6B7280)
- **錯誤狀態**: 紅色 (#EF4444)
- **警告狀態**: 橙色 (#F59E0B)
- **資訊狀態**: 藍色 (#3B82F6)

#### 5.2.2 設備類型色彩
- **Gateway**: 藍色系 (#1E40AF)
- **EPD**: 綠色系 (#059669)
- **通用**: 灰色系 (#374151)

### 5.3 互動設計

#### 5.3.1 上傳流程
1. 點擊"上傳軟體"按鈕
2. 開啟檔案選擇對話框
3. 選擇bin檔案後顯示上傳進度
4. 自動進行CRC驗證
5. 驗證成功後顯示軟體資訊表單
6. 填寫軟體名稱、描述等資訊
7. 確認上傳並顯示成功訊息

#### 5.3.2 狀態切換
- 啟用/禁用開關使用Toggle組件
- 狀態變更需要確認對話框
- 即時更新UI狀態
- 顯示操作結果提示

## 6. 技術實現

### 6.1 前端技術棧
- **React 18**: 主要框架
- **TypeScript**: 類型安全
- **Tailwind CSS**: 樣式框架
- **Lucide React**: 圖標庫
- **React Hook Form**: 表單處理
- **React Query**: 數據獲取和快取

### 6.2 後端技術棧
- **Node.js**: 運行環境
- **Express.js**: Web框架
- **MongoDB**: 數據庫
- **GridFS**: 檔案存儲
- **Multer**: 檔案上傳處理
- **Node.js crypto**: CRC計算

### 6.3 檔案處理流程

#### 6.3.1 上傳處理
```javascript
// 1. 接收檔案
const file = req.file;

// 2. 驗證檔案類型
if (!file.originalname.endsWith('.bin')) {
  throw new Error('只允許.bin檔案');
}

// 3. 解析bin檔案格式
const binInfo = parseBinFile(file.buffer);

// 4. 驗證CRC
if (!validateCRC(binInfo)) {
  throw new Error('CRC驗證失敗');
}

// 5. 提取純bin內容
const pureBinData = extractPureBin(file.buffer);

// 6. 儲存到GridFS
const originalFileId = await saveToGridFS(file.buffer, file.originalname);
const pureBinId = await saveToGridFS(pureBinData, `${binInfo.name}_pure.bin`);

// 7. 儲存軟體資訊到MongoDB
const software = await saveSoftwareInfo({
  ...binInfo,
  binFileId: originalFileId,
  extractedBinId: pureBinId
});
```

#### 6.3.2 bin檔案解析
```javascript
function parseBinFile(buffer) {
  // 根據bin-formatter工具的格式解析
  const deviceType = buffer.readUInt16LE(0);
  const functionType = buffer.readUInt16LE(2);
  const version = [
    buffer.readUInt8(4),
    buffer.readUInt8(5),
    buffer.readUInt8(6),
    buffer.readUInt8(7)
  ].join('.');

  const binData = buffer.slice(8, -4);
  const checksum = buffer.readUInt32LE(buffer.length - 4);

  return {
    deviceType: getDeviceTypeName(deviceType),
    functionType: getFunctionTypeName(functionType),
    version,
    binData,
    checksum: checksum.toString(16).toUpperCase(),
    fileSize: buffer.length
  };
}
```

## 7. 安全考量

### 7.1 檔案安全
- 檔案類型白名單驗證
- 檔案大小限制
- 病毒掃描整合
- 檔案內容驗證

### 7.2 權限控制
- 基於角色的存取控制
- 操作權限細分
- 審計日誌記錄
- 敏感操作二次確認

### 7.3 數據安全
- 檔案加密存儲
- 傳輸加密
- 備份和恢復
- 數據完整性檢查

## 8. 效能優化

### 8.1 檔案處理優化
- 分片上傳支援
- 斷點續傳
- 並行處理
- 快取機制

### 8.2 查詢優化
- 數據庫索引優化
- 分頁查詢
- 結果快取
- 懶加載

### 8.3 前端優化
- 虛擬滾動
- 圖片懶加載
- 組件懶加載
- 狀態管理優化
