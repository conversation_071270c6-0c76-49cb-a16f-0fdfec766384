import { DisplayColorType } from '../../types';

/**
 * ImageInfo 結構 - 對應 Go 的 ImageInfo struct
 */
export interface ImageInfo {
  imagecode: number; // uint32 - 圖片編碼 (與 WebSocket 發送的 imageCode 相同)
  x: number;         // uint16 - X 座標
  y: number;         // uint16 - Y 座標  
  width: number;     // uint16 - 寬度
  height: number;    // uint16 - 高度
}

/**
 * EPD 轉換選項
 */
export interface EPDConversionOptions {
  colorType: DisplayColorType;     // 使用現有的 DisplayColorType
  width: number;
  height: number;
  imagecode: number;               // 圖片編碼 (與 WebSocket 發送的 imageCode 相同)
  x?: number;                      // 圖片在設備上的 X 座標，默認 0
  y?: number;                      // 圖片在設備上的 Y 座標，默認 0
  templateRotation?: number;       // 模板旋轉角度 (0, 90, 180, 270)，需要反向旋轉回 0 度
  paddingColor?: string;           // 填充顏色，默認白色
  qualityLevel?: number;           // 品質等級 1-10，默認 5
}

/**
 * EPD 轉換結果
 */
export interface EPDConversionResult {
  success: boolean;
  rawdata?: Uint8Array;            // 包含 ImageInfo + 圖片數據
  imageInfo?: ImageInfo;           // ImageInfo 結構
  pixelData?: Uint8Array;          // 純圖片像素數據
  error?: string;
  metadata: {
    originalSize: { width: number; height: number };
    finalSize: { width: number; height: number };
    bytesPerPixel: number;
    totalBytes: number;
    imageInfoBytes: number;        // ImageInfo 佔用的字節數 (固定12字節)
    pixelDataBytes: number;        // 像素數據佔用的字節數
    processingTime: number;
    colorType: DisplayColorType;
  };
}

/**
 * 像素數據結構
 */
export interface PixelData {
  r: number;
  g: number;
  b: number;
  a: number;
}
