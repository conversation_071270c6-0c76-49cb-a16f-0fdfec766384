const schedule = require('node-schedule');

class TaskScheduler {
  constructor() {
    this.activeTasks = new Map();
    this.executionEngine = null;
  }

  // 初始化執行引擎
  setExecutionEngine(executionEngine) {
    this.executionEngine = executionEngine;
  }

  // 註冊計畫到調度器
  async registerPlan(plan) {
    try {
      console.log(`註冊計畫到調度器: ${plan.name}`, {
        planId: plan._id.toString(),
        trigger: plan.trigger,
        enabled: plan.enabled
      });

      // 如果已存在，先取消
      if (this.activeTasks.has(plan._id.toString())) {
        await this.unregisterPlan(plan._id.toString());
      }

      const triggerConfig = plan.trigger;
      let scheduledTask;

      switch (triggerConfig.type) {
        case 'once':
          // 單次執行：在指定日期的指定時間執行
          if (!triggerConfig.executeDate || !triggerConfig.executeTime) {
            console.error(`計畫 ${plan.name} 的單次執行配置不完整:`, triggerConfig);
            return;
          }

          const executeDateTime = new Date(`${triggerConfig.executeDate} ${triggerConfig.executeTime}`);

          // 檢查執行時間是否已過
          if (executeDateTime <= new Date()) {
            console.log(`計畫 ${plan.name} 的執行時間已過，跳過註冊`);
            return;
          }

          scheduledTask = schedule.scheduleJob(
            executeDateTime,
            () => this.executePlan(plan)
          );
          break;

        case 'daily':
          // 每日執行：每天在指定時間執行
          if (!triggerConfig.executeTime) {
            console.error(`計畫 ${plan.name} 的每日執行配置不完整:`, triggerConfig);
            return;
          }

          const [hour, minute] = triggerConfig.executeTime.split(':');
          const dailyRule = new schedule.RecurrenceRule();
          dailyRule.hour = parseInt(hour);
          dailyRule.minute = parseInt(minute);
          scheduledTask = schedule.scheduleJob(dailyRule, () => this.executePlan(plan));
          break;

        case 'weekly':
          // 每週執行：在指定星期的指定時間執行
          if (!triggerConfig.executeTime || !triggerConfig.weekDays || triggerConfig.weekDays.length === 0) {
            console.error(`計畫 ${plan.name} 的每週執行配置不完整:`, triggerConfig);
            return;
          }

          const [weekHour, weekMinute] = triggerConfig.executeTime.split(':');
          const weeklyRule = new schedule.RecurrenceRule();
          weeklyRule.dayOfWeek = triggerConfig.weekDays;
          weeklyRule.hour = parseInt(weekHour);
          weeklyRule.minute = parseInt(weekMinute);
          scheduledTask = schedule.scheduleJob(weeklyRule, () => this.executePlan(plan));
          break;

        default:
          throw new Error(`不支援的觸發類型: ${triggerConfig.type}`);
      }

      if (scheduledTask) {
        this.activeTasks.set(plan._id.toString(), {
          plan,
          scheduledTask,
          registeredAt: new Date(),
          storeId: plan.storeId
        });

        // 更新數據庫中的 nextRun 時間
        const nextRunTime = scheduledTask.nextInvocation();
        console.log(`計畫 ${plan.name} 計算的下次執行時間: ${nextRunTime}`);

        if (nextRunTime) {
          // 直接更新數據庫，不通過 updatePlanNextRun 方法（避免重複廣播）
          await this.directUpdatePlanNextRun(plan._id.toString(), nextRunTime, plan.storeId, plan.name);
        } else {
          console.warn(`計畫 ${plan.name} 的下次執行時間為空，跳過更新`);
        }

        console.log(`計畫 ${plan.name} 已註冊到調度器，下次執行時間: ${nextRunTime}`);
      }
    } catch (error) {
      console.error(`註冊計畫失敗: ${error.message}`);
      throw new Error(`註冊計畫失敗: ${error.message}`);
    }
  }

  // 取消計畫調度
  async unregisterPlan(planId) {
    try {
      const taskInfo = this.activeTasks.get(planId);
      if (taskInfo) {
        taskInfo.scheduledTask.cancel();
        this.activeTasks.delete(planId);
        console.log(`計畫 ${planId} 已從調度器移除`);
      }
    } catch (error) {
      console.error(`取消計畫調度失敗: ${error.message}`);
    }
  }

  // 執行計畫
  async executePlan(plan) {
    try {
      console.log(`開始執行計畫: ${plan.name}`);
      
      if (!this.executionEngine) {
        throw new Error('執行引擎未初始化');
      }

      // 創建執行記錄
      const executionId = await this.executionEngine.createExecutionRecord(plan);
      
      // 調用執行引擎
      await this.executionEngine.execute(plan, executionId);
      
    } catch (error) {
      console.error(`計畫執行失敗: ${error.message}`);
    }
  }

  // 獲取所有活動任務
  getActiveTasks() {
    return Array.from(this.activeTasks.values()).map(taskInfo => ({
      planId: taskInfo.plan._id.toString(),
      planName: taskInfo.plan.name,
      storeId: taskInfo.plan.storeId,
      registeredAt: taskInfo.registeredAt,
      nextRun: taskInfo.scheduledTask.nextInvocation()
    }));
  }

  // 重新載入所有啟用的計畫
  async reloadAllPlans() {
    try {
      console.log('重新載入所有啟用的計畫...');

      // 清除所有現有任務
      for (const [planId] of this.activeTasks) {
        await this.unregisterPlan(planId);
      }

      // 從數據庫重新載入所有啟用的計畫
      if (!this.executionEngine || !this.executionEngine.getDbConnection) {
        console.error('執行引擎或數據庫連接未初始化');
        return;
      }

      const { client, db } = await this.executionEngine.getDbConnection();
      const collection = db.collection('refreshPlans');
      
      const enabledPlans = await collection.find({ 
        enabled: true,
        status: { $in: ['active', 'inactive'] }
      }).toArray();

      console.log(`找到 ${enabledPlans.length} 個啟用的計畫`);

      // 重新註冊所有啟用的計畫
      for (const plan of enabledPlans) {
        try {
          await this.registerPlan(plan);
        } catch (error) {
          console.error(`重新註冊計畫 ${plan.name} 失敗:`, error.message);
        }
      }

      console.log(`成功重新載入 ${this.activeTasks.size} 個計畫`);
    } catch (error) {
      console.error('重新載入計畫失敗:', error);
    }
  }

  // 獲取計畫的下次執行時間
  getNextRunTime(planId) {
    const taskInfo = this.activeTasks.get(planId);
    if (taskInfo && taskInfo.scheduledTask) {
      return taskInfo.scheduledTask.nextInvocation();
    }
    return null;
  }

  // 檢查計畫是否已註冊
  isPlanRegistered(planId) {
    return this.activeTasks.has(planId);
  }

  // 直接更新計畫的下次執行時間到數據庫（用於registerPlan，包含廣播）
  async directUpdatePlanNextRun(planId, nextRunTime, storeId, planName) {
    try {
      if (!this.executionEngine || !this.executionEngine.getDbConnection) {
        console.warn('執行引擎或數據庫連接未初始化，無法更新 nextRun');
        return;
      }

      const { db } = await this.executionEngine.getDbConnection();
      const collection = db.collection('refreshPlans');

      const { ObjectId } = require('mongodb');
      const result = await collection.findOneAndUpdate(
        { _id: new ObjectId(planId) },
        { $set: { nextRun: nextRunTime, updatedAt: new Date() } },
        { returnDocument: 'after' }
      );

      console.log(`已更新計畫 ${planId} 的下次執行時間: ${nextRunTime}`);

      // 廣播計畫更新事件
      const updatedPlan = result.value || result;
      if (updatedPlan) {
        try {
          const websocketService = require('./websocketService');

          // 確保日期格式正確 - 參考新增計畫的做法
          const planDataToSend = {
            ...updatedPlan,
            _id: planId.toString(),
            // 直接使用 nextRunTime（JavaScript Date對象），轉換為ISO字符串
            nextRun: nextRunTime ? nextRunTime.toISOString() : null,
            updatedAt: new Date().toISOString(),
            updatedFields: ['nextRun', 'updatedAt']
          };

          websocketService.broadcastRefreshPlanUpdate(storeId, {
            planId: planId.toString(),
            planData: planDataToSend
          }, 'update');

          console.log(`計畫 ${planId} nextRun 更新廣播已發送`);
        } catch (error) {
          console.error('廣播計畫更新事件失敗:', error);
        }
      } else {
        console.error(`更新計畫 ${planId} nextRun 後找不到結果`);
      }
    } catch (error) {
      console.error(`更新計畫 nextRun 失敗: ${error.message}`);
    }
  }

  // 更新計畫的下次執行時間到數據庫
  async updatePlanNextRun(planId, nextRunTime) {
    try {
      if (!this.executionEngine || !this.executionEngine.getDbConnection) {
        console.warn('執行引擎或數據庫連接未初始化，無法更新 nextRun');
        return;
      }

      const { db } = await this.executionEngine.getDbConnection();
      const collection = db.collection('refreshPlans');

      const { ObjectId } = require('mongodb');
      const result = await collection.findOneAndUpdate(
        { _id: new ObjectId(planId) },
        { $set: { nextRun: nextRunTime, updatedAt: new Date() } },
        { returnDocument: 'after' }
      );

      console.log(`已更新計畫 ${planId} 的下次執行時間: ${nextRunTime}`);

      // 廣播計畫更新事件
      // 檢查新版本MongoDB驅動的返回格式：result 或 result.value
      const updatedPlan = result.value || result;
      if (updatedPlan) {
        try {
          console.log(`廣播計畫 ${planId} 的 nextRun 更新 (原始數據):`, {
            planId: planId.toString(),
            nextRun: updatedPlan.nextRun,
            updatedAt: updatedPlan.updatedAt,
            planName: updatedPlan.name
          });

          const websocketService = require('./websocketService');

          // 確保 nextRun 是正確的日期格式
          const convertToISOString = (dateValue) => {
            if (!dateValue) return null;

            // 處理 MongoDB 日期對象 { _date: ... }
            if (dateValue._date) {
              return new Date(dateValue._date).toISOString();
            }

            // 處理標準 Date 對象
            if (dateValue instanceof Date) {
              return dateValue.toISOString();
            }

            // 處理字符串格式的日期
            if (typeof dateValue === 'string') {
              return new Date(dateValue).toISOString();
            }

            // 嘗試直接轉換
            try {
              return new Date(dateValue).toISOString();
            } catch (error) {
              console.error('日期轉換失敗:', dateValue, error);
              return null;
            }
          };

          const planDataToSend = {
            ...updatedPlan,
            _id: planId.toString(),
            // 確保 nextRun 是 ISO 字符串格式
            nextRun: convertToISOString(updatedPlan.nextRun),
            updatedAt: convertToISOString(updatedPlan.updatedAt),
            createdAt: convertToISOString(updatedPlan.createdAt),
            lastRun: convertToISOString(updatedPlan.lastRun),
            updatedFields: ['nextRun', 'updatedAt']
          };

          websocketService.broadcastRefreshPlanUpdate(updatedPlan.storeId, {
            planId: planId.toString(),
            planData: planDataToSend
          }, 'update');

          console.log(`計畫 ${planId} nextRun 更新廣播已發送`);
        } catch (error) {
          console.error('廣播計畫更新事件失敗:', error);
        }
      } else {
        console.error(`更新計畫 ${planId} nextRun 後找不到結果`);
      }
    } catch (error) {
      console.error(`更新計畫 nextRun 失敗: ${error.message}`);
    }
  }

  // 獲取調度器狀態
  getSchedulerStatus() {
    const activeTasks = this.getActiveTasks();
    return {
      totalActiveTasks: activeTasks.length,
      activeTasks: activeTasks,
      schedulerStartTime: this.schedulerStartTime || new Date(),
      isHealthy: true
    };
  }

  // 啟動調度器
  async start() {
    try {
      this.schedulerStartTime = new Date();
      console.log('任務調度器啟動中...');
      
      // 載入所有啟用的計畫
      await this.reloadAllPlans();
      
      console.log('任務調度器啟動完成');
    } catch (error) {
      console.error('任務調度器啟動失敗:', error);
    }
  }

  // 停止調度器
  async stop() {
    try {
      console.log('任務調度器停止中...');
      
      // 取消所有活動任務
      for (const [planId] of this.activeTasks) {
        await this.unregisterPlan(planId);
      }
      
      console.log('任務調度器已停止');
    } catch (error) {
      console.error('任務調度器停止失敗:', error);
    }
  }
}

// 創建單例實例
const taskScheduler = new TaskScheduler();

module.exports = taskScheduler;
