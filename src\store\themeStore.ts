import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 主題類型
export type Theme = 'light' | 'dark';

// 主題狀態接口
interface ThemeState {
  theme: Theme;
  isDark: boolean;
  
  // 切換主題
  toggleTheme: () => void;
  // 設置主題
  setTheme: (theme: Theme) => void;
  // 應用主題到DOM
  applyTheme: (theme: Theme) => void;
}

// 應用主題到DOM的函數
const applyThemeToDOM = (theme: Theme) => {
  const root = document.documentElement;
  
  if (theme === 'dark') {
    root.classList.add('dark');
  } else {
    root.classList.remove('dark');
  }
  
  // 更新meta標籤的theme-color
  const metaThemeColor = document.querySelector('meta[name="theme-color"]');
  if (metaThemeColor) {
    metaThemeColor.setAttribute('content', theme === 'dark' ? '#1e293b' : '#ffffff');
  }
};

// 創建主題狀態管理
export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      theme: 'light',
      isDark: false,
      
      toggleTheme: () => {
        const currentTheme = get().theme;
        const newTheme: Theme = currentTheme === 'light' ? 'dark' : 'light';
        
        set({ 
          theme: newTheme, 
          isDark: newTheme === 'dark' 
        });
        
        applyThemeToDOM(newTheme);
      },
      
      setTheme: (theme: Theme) => {
        set({ 
          theme, 
          isDark: theme === 'dark' 
        });
        
        applyThemeToDOM(theme);
      },
      
      applyTheme: (theme: Theme) => {
        applyThemeToDOM(theme);
      }
    }),
    {
      name: 'theme-storage',
      onRehydrateStorage: () => (state) => {
        // 當從localStorage恢復狀態時，應用主題到DOM
        if (state) {
          applyThemeToDOM(state.theme);
        }
      }
    }
  )
);

// 初始化主題 - 檢測系統偏好
export const initializeTheme = () => {
  const store = useThemeStore.getState();
  
  // 如果沒有保存的主題偏好，檢測系統偏好
  const savedTheme = localStorage.getItem('theme-storage');
  if (!savedTheme) {
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const systemTheme: Theme = prefersDark ? 'dark' : 'light';
    store.setTheme(systemTheme);
  } else {
    // 應用已保存的主題
    store.applyTheme(store.theme);
  }
  
  // 監聽系統主題變化
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
    // 只有在用戶沒有手動設置主題時才跟隨系統
    const currentStorage = localStorage.getItem('theme-storage');
    if (!currentStorage) {
      const systemTheme: Theme = e.matches ? 'dark' : 'light';
      store.setTheme(systemTheme);
    }
  });
};

export default useThemeStore;
