// tests/sysConfigApi.test.js
const request = require('supertest');
const express = require('express');
const { createMockCollection } = require('./helpers/mockDb');
const createMockSysConfigApi = require('./helpers/mockSysConfigApi');

// 防止實際路由檔案被引入
jest.mock('../routes/sysConfigApi', () => {
  return {};
});

describe('系統配置 API 測試', () => {
  let app;
  let mockCollection;
  let router;
  beforeEach(() => {
    // 創建 Express 應用
    app = express();

    // 初始化模擬資料
    const initialData = [
      { _id: '1', type: 'gateway', config: { url: 'http://example.com' } },
      { _id: '2', type: 'userInfo', config: { name: 'Admin' } },
    ];

    // 創建模擬集合
    mockCollection = createMockCollection(initialData);

    // 創建模擬的系統配置 API 路由
    router = createMockSysConfigApi(mockCollection);

    // 使用模擬路由
    app.use(express.json());
    app.use('/api', router);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/sysConfig', () => {
    test('應該返回所有系統配置', async () => {
      const response = await request(app).get('/api/sysConfig');

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
      expect(mockCollection.find).toHaveBeenCalled();
    });

    test('應該處理獲取系統配置時的錯誤', async () => {
      // 模擬錯誤
      const mockToArray = jest.fn().mockRejectedValueOnce(new Error('資料庫錯誤'));
      mockCollection.find.mockReturnValueOnce({
        toArray: mockToArray
      });

      const response = await request(app).get('/api/sysConfig');

      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('GET /api/sysConfig/:type', () => {
    test('應該返回指定類型的系統配置', async () => {
      const response = await request(app).get('/api/sysConfig/gateway');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('config');
      expect(response.body.config).toHaveProperty('url', 'http://example.com');
      expect(mockCollection.findOne).toHaveBeenCalledWith({ type: 'gateway' });
    });

    test('當系統配置不存在時應該返回空對象', async () => {
      mockCollection.findOne.mockResolvedValueOnce(null);

      const response = await request(app).get('/api/sysConfig/nonexistent');

      expect(response.status).toBe(200);
      expect(response.body).toEqual({});
    });
  });

  describe('POST /api/sysConfig', () => {
    test('當配置不存在時應該創建新配置', async () => {
      mockCollection.findOne.mockResolvedValueOnce(null);

      const newConfig = {
        type: 'newConfig',
        config: { param: 'value' }
      };

      const response = await request(app)
        .post('/api/sysConfig')
        .send(newConfig);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(mockCollection.insertOne).toHaveBeenCalledWith(newConfig);
    });

    test('當配置已存在時應該更新配置', async () => {
      mockCollection.findOne.mockResolvedValueOnce({
        _id: '1',
        type: 'gateway',
        config: { url: 'http://old.com' }
      });

      const updatedConfig = {
        type: 'gateway',
        config: { url: 'http://new.com' }
      };

      const response = await request(app)
        .post('/api/sysConfig')
        .send(updatedConfig);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', '系統配置更新成功');
      expect(mockCollection.updateOne).toHaveBeenCalledWith(
        { type: 'gateway' },
        { $set: { config: updatedConfig.config } }
      );
    });
  });
});
