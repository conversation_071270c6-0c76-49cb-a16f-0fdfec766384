import React, { useState, useEffect, useRef } from 'react';
import { Line } from 'react-chartjs-2';
import { Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Store } from '../types/store';
import { StoreData, DataField } from '../types';
import { Gateway } from '../types/gateway';
import { Device } from '../types/device';
import { getAllStoreData } from '../utils/api/storeDataApi';
import { getAllDataFields } from '../utils/api/dataFieldApi';
import { getAllGateways } from '../utils/api/gatewayApi';
import { getAllDevices } from '../utils/api/deviceApi';
import { getUser } from '../utils/api/userApi';  // 添加 getUser 函數引入
import { AddStoreDataModal } from './AddStoreDataModal';
import { RefreshPlanCalendar } from './RefreshPlanCalendar';
import { RefreshPlanTimeline } from './RefreshPlanTimeline';
import { RefreshPlanHeatmap } from './RefreshPlanHeatmap';
import { Plus, AlertCircle, RefreshCw, Store as StoreIcon, MapPin, Phone, User, Wifi, Monitor, RotateCcw, Battery, Calendar, BarChart3, Flame } from 'lucide-react';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface StoreOverviewPageProps {
  store?: Store;
}

export function StoreOverviewPage({ store }: StoreOverviewPageProps) {
  const [storeData, setStoreData] = useState<StoreData[]>([]);
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [gateways, setGateways] = useState<Gateway[]>([]);
  const [devices, setDevices] = useState<Device[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFields, setIsLoadingFields] = useState(false);
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  // 添加管理員狀態
  const [manager, setManager] = useState<{ name: string; username: string } | null>(null);
  const [isLoadingManager, setIsLoadingManager] = useState(false);
  const [managerError, setManagerError] = useState<string | null>(null);
  // 刷圖計畫視圖類型
  const [planViewType, setPlanViewType] = useState<'calendar' | 'timeline' | 'heatmap'>('calendar');
  // 切換按鈕區域的引用
  const chartSwitchRef = useRef<HTMLDivElement>(null);

  // 處理視圖切換，讓圖表內容成為視覺焦點
  const handleViewTypeChange = (newViewType: 'calendar' | 'timeline' | 'heatmap') => {
    // 切換視圖
    setPlanViewType(newViewType);

    // 使用 setTimeout 確保 DOM 更新後再滾動到合適位置
    setTimeout(() => {
      const switchElement = chartSwitchRef.current;
      if (switchElement) {
        // 獲取切換按鈕的父容器（圖表區域）
        const chartContainer = switchElement.parentElement;
        if (chartContainer) {
          // 計算圖表區域的位置
          const rect = chartContainer.getBoundingClientRect();
          const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const containerAbsoluteTop = currentScrollTop + rect.top;

          // 將圖表區域的頂部定位在視窗頂部稍下方（留出一些空間）
          const targetScrollPosition = containerAbsoluteTop - 80; // 80px 的上邊距

          // 平滑滾動到目標位置
          window.scrollTo({
            top: Math.max(0, targetScrollPosition), // 確保不會滾動到負值
            behavior: 'smooth'
          });
        }
      }
    }, 150); // 延遲確保圖表完全渲染
  };

  // 獲取門店資料
  useEffect(() => {
    const fetchStoreData = async () => {
      if (!store) return;

      try {
        setIsLoading(true);
        setError(null);

        // 使用門店ID獲取對應的門店資料
        console.log(`正在獲取門店 ${store.id} 的資料...`);
        const data = await getAllStoreData(store.id);
        console.log(`獲取到門店 ${store.id} 的資料:`, data);

        if (data.length === 0) {
          console.log(`門店 ${store.id} 沒有對應的資料，可能需要創建`);
        } else {
          console.log(`門店 ${store.id} 的資料 storeId:`, data.map(item => item.storeId));
        }

        setStoreData(data);
      } catch (err) {
        console.error('獲取門店資料失敗:', err);
        setError('獲取門店資料失敗');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStoreData();
  }, [store]);

  // 獲取資料欄位定義
  useEffect(() => {
    const fetchDataFields = async () => {
      try {
        setIsLoadingFields(true);
        const fields = await getAllDataFields();
        setDataFields(fields);
      } catch (err) {
        console.error('獲取資料欄位失敗:', err);
      } finally {
        setIsLoadingFields(false);
      }
    };

    fetchDataFields();
  }, []);

  // 獲取管理員詳細信息
  useEffect(() => {
    const fetchManager = async () => {
      if (!store?.managerId) return;

      try {
        setIsLoadingManager(true);
        setManagerError(null);
        const userData = await getUser(store.managerId);
        setManager({
          name: userData.name || userData.username || '未知',
          username: userData.username || '未知'
        });
      } catch (err) {
        console.error('獲取管理員資訊失敗:', err);
        setManagerError('獲取管理員資訊失敗');
        setManager(null);
      } finally {
        setIsLoadingManager(false);
      }
    };

    fetchManager();
  }, [store?.managerId]);

  // 獲取Gateway和Device數據
  useEffect(() => {
    const fetchStatsData = async () => {
      if (!store?.id) return;

      try {
        setIsLoadingStats(true);

        // 並行獲取gateway和device數據
        const [gatewayData, deviceData] = await Promise.all([
          getAllGateways(store.id),
          getAllDevices(store.id)
        ]);

        console.log(`門店 ${store.id} - Gateway數量: ${gatewayData.length}, Device數量: ${deviceData.length}`);

        setGateways(gatewayData);
        setDevices(deviceData);
      } catch (err) {
        console.error('獲取統計數據失敗:', err);
      } finally {
        setIsLoadingStats(false);
      }
    };

    fetchStatsData();
  }, [store?.id]);

  // 計算統計數據
  const calculateStats = () => {
    // Gateway統計
    const gatewayOnline = gateways.filter(g => g.status === 'online').length;
    const gatewayOffline = gateways.filter(g => g.status === 'offline').length;

    // Device統計
    const deviceOnline = devices.filter(d => d.status === 'online').length;
    const deviceOffline = devices.filter(d => d.status === 'offline').length;

    // 刷新狀態統計（基於imageUpdateStatus）
    const refreshSucceeded = devices.filter(d => d.imageUpdateStatus === '已更新').length;
    const refreshFailed = devices.filter(d => d.imageUpdateStatus === '未更新').length;

    // 電池狀態統計（基於battery值，低於20%視為低電量）
    const batteryNormal = devices.filter(d => {
      const battery = d.data?.battery;
      return battery !== undefined && battery >= 20;
    }).length;
    const batteryLow = devices.filter(d => {
      const battery = d.data?.battery;
      return battery !== undefined && battery < 20;
    }).length;

    return {
      gateway: { online: gatewayOnline, offline: gatewayOffline },
      device: { online: deviceOnline, offline: deviceOffline },
      refresh: { succeeded: refreshSucceeded, failed: refreshFailed },
      battery: { normal: batteryNormal, low: batteryLow }
    };
  };

  const stats = calculateStats();

  const statusCards = [
    {
      title: 'Gateway',
      gradient: 'from-purple-400 to-blue-500',
      metrics: [
        { label: 'Online', value: stats.gateway.online },
        { label: 'Offline', value: stats.gateway.offline },
      ],
    },
    {
      title: 'ESL',
      gradient: 'from-blue-400 to-cyan-500',
      metrics: [
        { label: 'Online', value: stats.device.online },
        { label: 'Offline', value: stats.device.offline },
      ],
    },
    {
      title: 'Refresh',
      gradient: 'from-green-400 to-emerald-500',
      metrics: [
        { label: 'Succeeded', value: stats.refresh.succeeded },
        { label: 'Failed', value: stats.refresh.failed },
      ],
    },
    {
      title: 'Battery level',
      gradient: 'from-orange-400 to-amber-500',
      metrics: [
        { label: 'Normal', value: stats.battery.normal },
        { label: 'Low battery', value: stats.battery.low },
      ],
    },
  ];

  // 計算成功率
  const calculateSuccessRate = () => {
    const total = stats.refresh.succeeded + stats.refresh.failed;
    if (total === 0) return 0;
    return Math.round((stats.refresh.succeeded / total) * 100);
  };

  const successRate = calculateSuccessRate();

  const lineChartData = {
    labels: ['03-26', '03-27', '03-28', '03-29', '03-30', 'Yesterday', 'Today'],
    datasets: [
      {
        label: 'Succeeded/times',
        data: [0, 0, 0, 0, 0, 0, stats.refresh.succeeded],
        borderColor: 'rgb(99, 132, 255)',
        backgroundColor: 'rgba(99, 132, 255, 0.5)',
      },
      {
        label: 'Refresh/times',
        data: [0, 0, 0, 0, 0, 0, stats.refresh.succeeded + stats.refresh.failed],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
      },
    ],
  };

  const doughnutChartData = {
    labels: ['Succeeded', 'Failed'],
    datasets: [
      {
        data: [stats.refresh.succeeded, stats.refresh.failed],
        backgroundColor: [
          'rgba(99, 132, 255, 0.8)',
          'rgba(255, 99, 132, 0.8)',
        ],
        borderColor: [
          'rgba(99, 132, 255, 1)',
          'rgba(255, 99, 132, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // 刷新統計數據
  const handleRefreshStats = async () => {
    if (!store?.id) return;

    try {
      setIsLoadingStats(true);

      const [gatewayData, deviceData] = await Promise.all([
        getAllGateways(store.id),
        getAllDevices(store.id)
      ]);

      setGateways(gatewayData);
      setDevices(deviceData);
    } catch (err) {
      console.error('刷新統計數據失敗:', err);
    } finally {
      setIsLoadingStats(false);
    }
  };

  return (
    <>
      <div className="p-8 lg:px-4 py-2">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* 門店信息 */}
          {store && (
            <div className="relative mb-6">
              {/* 玻璃效果背景 */}
              <div className="absolute inset-0 bg-gradient-to-br from-sky-100/80 via-blue-50/60 to-cyan-100/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>

              {/* 動態光效背景 */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-2xl animate-pulse opacity-50"></div>

              {/* 內容區域 */}
              <div className="relative p-6">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-sky-400 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                      <StoreIcon className="w-7 h-7 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800">{store.name || '未命名門店'}</h2>
                      <p className="text-sm text-gray-600 bg-white/40 px-3 py-1 rounded-full border border-white/30 inline-block mt-1">
                        ID: {store.id}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* 地址信息 */}
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-green-500/20 rounded-xl border border-emerald-200/50 group-hover:shadow-lg transition-all duration-300"></div>
                    <div className="relative p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-green-500 rounded-lg flex items-center justify-center shadow-md">
                          <MapPin className="w-4 h-4 text-white" />
                        </div>
                        <span className="font-medium text-gray-700">地址</span>
                      </div>
                      <p className="text-gray-600 text-sm">{store.address || '無地址'}</p>
                    </div>
                  </div>

                  {/* 電話信息 */}
                  {store.phone && (
                    <div className="group relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-sky-400/20 to-blue-500/20 rounded-xl border border-sky-200/50 group-hover:shadow-lg transition-all duration-300"></div>
                      <div className="relative p-4">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="w-8 h-8 bg-gradient-to-br from-sky-400 to-blue-500 rounded-lg flex items-center justify-center shadow-md">
                            <Phone className="w-4 h-4 text-white" />
                          </div>
                          <span className="font-medium text-gray-700">電話</span>
                        </div>
                        <p className="text-gray-600 text-sm">{store.phone}</p>
                      </div>
                    </div>
                  )}

                  {/* 管理員信息 */}
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-violet-500/20 rounded-xl border border-purple-200/50 group-hover:shadow-lg transition-all duration-300"></div>
                    <div className="relative p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-violet-500 rounded-lg flex items-center justify-center shadow-md">
                          <User className="w-4 h-4 text-white" />
                        </div>
                        <span className="font-medium text-gray-700">管理員</span>
                      </div>
                      {isLoadingManager ? (
                        <p className="text-gray-600 text-sm">加載中...</p>
                      ) : manager ? (
                        <p className="text-gray-600 text-sm">{manager.name}</p>
                      ) : store.managerId ? (
                        <div>
                          <p className="text-gray-600 text-sm">ID: {store.managerId}</p>
                          {managerError && <p className="text-red-500 text-xs mt-1">({managerError})</p>}
                        </div>
                      ) : (
                        <p className="text-gray-600 text-sm">未設置</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 載入狀態 */}
          {isLoading && (
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-100/80 via-sky-50/60 to-blue-100/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-lg"></div>
              <div className="relative p-6 text-center">
                <div className="flex items-center justify-center gap-3">
                  <RefreshCw className="w-6 h-6 text-blue-600 animate-spin" />
                  <p className="text-blue-700 font-medium">載入門店資料中...</p>
                </div>
              </div>
            </div>
          )}

          {/* 錯誤信息 */}
          {error && (
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-br from-red-100/80 via-rose-50/60 to-red-100/80 backdrop-blur-sm rounded-2xl border border-red-200/50 shadow-lg"></div>
              <div className="relative p-6">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-red-400 to-red-600 rounded-lg flex items-center justify-center shadow-lg">
                    <AlertCircle className="w-5 h-5 text-white" />
                  </div>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </div>
            </div>
          )}

          {!isLoading && storeData.length === 0 && !error && (
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-br from-amber-100/80 via-yellow-50/60 to-amber-100/80 backdrop-blur-sm rounded-2xl border border-amber-200/50 shadow-lg"></div>
              <div className="relative p-6">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-amber-400 to-yellow-500 rounded-lg flex items-center justify-center shadow-lg">
                      <AlertCircle className="w-5 h-5 text-white" />
                    </div>
                    <p className="text-amber-700 font-medium">此門店尚未設置資料，請先添加門店資料。</p>
                  </div>
                  <button
                    onClick={() => setShowAddModal(true)}
                    className="group relative overflow-hidden bg-gradient-to-r from-blue-500 to-cyan-600 text-white px-4 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50"
                    disabled={isLoadingFields}
                    onMouseEnter={(e) => {
                      const lightEffect = e.currentTarget.querySelector('.add-button-light-sweep') as HTMLElement;
                      if (lightEffect && !isLoadingFields) {
                        // 重置動畫
                        lightEffect.style.transform = 'translateX(-100%)';
                        lightEffect.style.transition = 'none';

                        // 強制重繪
                        lightEffect.offsetHeight;

                        // 啟動動畫
                        lightEffect.style.transition = 'transform 700ms ease-out';
                        lightEffect.style.transform = 'translateX(100%)';
                      }
                    }}
                  >
                    <div className="add-button-light-sweep absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] pointer-events-none"></div>
                    <div className="relative flex items-center gap-2">
                      <Plus size={16} />
                      <span className="text-sm font-medium">添加門店資料</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 統計數據標題和刷新按鈕 */}
          <div className="relative mb-6">
            {/* 玻璃效果背景 */}
            <div className="absolute inset-0 bg-gradient-to-r from-slate-100/80 via-gray-50/60 to-slate-100/80 backdrop-blur-sm rounded-xl border border-white/30 shadow-lg"></div>

            {/* 內容區域 */}
            <div className="relative p-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-slate-400 to-gray-600 rounded-lg flex items-center justify-center shadow-lg">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">門店概況</h2>
                </div>

                <button
                  onClick={handleRefreshStats}
                  disabled={isLoadingStats}
                  className="group relative overflow-hidden bg-gradient-to-r from-sky-500 to-blue-600 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  onMouseEnter={(e) => {
                    const lightEffect = e.currentTarget.querySelector('.button-light-sweep') as HTMLElement;
                    if (lightEffect && !isLoadingStats) {
                      // 重置動畫
                      lightEffect.style.transform = 'translateX(-100%)';
                      lightEffect.style.transition = 'none';

                      // 強制重繪
                      lightEffect.offsetHeight;

                      // 啟動動畫
                      lightEffect.style.transition = 'transform 700ms ease-out';
                      lightEffect.style.transform = 'translateX(100%)';
                    }
                  }}
                >
                  <div className="button-light-sweep absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] pointer-events-none"></div>
                  <div className="relative flex items-center gap-2">
                    <RefreshCw size={18} className={`${isLoadingStats ? 'animate-spin' : ''}`} />
                    <span className="font-medium">{isLoadingStats ? '刷新中...' : '刷新統計'}</span>
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Status Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {statusCards.map((card, index) => {
              // 為每個卡片定義圖標
              const getCardIcon = (title: string) => {
                switch (title) {
                  case 'Gateway':
                    return <Wifi className="w-6 h-6 text-white" />;
                  case 'ESL':
                    return <Monitor className="w-6 h-6 text-white" />;
                  case 'Refresh':
                    return <RotateCcw className="w-6 h-6 text-white" />;
                  case 'Battery level':
                    return <Battery className="w-6 h-6 text-white" />;
                  default:
                    return <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>;
                }
              };

              // 處理光線掃過動畫
              const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
                const lightEffect = e.currentTarget.querySelector('.light-sweep') as HTMLElement;
                if (lightEffect) {
                  // 重置動畫
                  lightEffect.style.transform = 'translateX(-100%)';
                  lightEffect.style.transition = 'none';

                  // 強制重繪
                  lightEffect.offsetHeight;

                  // 啟動動畫
                  lightEffect.style.transition = 'transform 1000ms ease-out';
                  lightEffect.style.transform = 'translateX(100%)';
                }
              };

              return (
                <div
                  key={card.title}
                  className="group relative overflow-hidden"
                  onMouseEnter={handleMouseEnter}
                >
                  {/* 玻璃效果背景 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-white/10 to-white/5 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>

                  {/* 漸變背景 */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${card.gradient} rounded-2xl opacity-90 group-hover:opacity-100 transition-opacity duration-300`}></div>

                  {/* 動態光效 - 只在滑鼠進入時觸發一次 */}
                  <div className="light-sweep absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-2xl translate-x-[-100%] pointer-events-none"></div>

                  {/* 內容區域 */}
                  <div className="relative p-6 text-white">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-lg">
                          {getCardIcon(card.title)}
                        </div>
                        <h3 className="text-xl font-bold">{card.title}</h3>
                      </div>

                      {/* 總計數字 */}
                      <div className="text-right">
                        <div className="text-sm opacity-80">總計</div>
                        <div className="text-lg font-bold">
                          {card.metrics.reduce((sum, metric) => sum + metric.value, 0)}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      {card.metrics.map((metric, metricIndex) => (
                        <div key={metric.label} className="text-center">
                          <div className="relative">
                            <div className="w-16 h-16 mx-auto bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-lg mb-2 group-hover:scale-105 transition-transform duration-300">
                              <span className="text-2xl font-bold">{metric.value}</span>
                            </div>
                            {/* 狀態指示器 */}
                            <div className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                              (metric.label === 'Online' || metric.label === 'Succeeded' || metric.label === 'Normal')
                                ? 'bg-green-400'
                                : 'bg-red-400'
                            }`}></div>
                          </div>
                          <span className="block text-sm font-medium opacity-90">{metric.label}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Line Chart */}
            <div className="relative">
              {/* 玻璃效果背景 */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/80 via-slate-50/60 to-white/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>

              {/* 動態光效背景 */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-2xl animate-pulse opacity-30"></div>

              {/* 內容區域 */}
              <div className="relative p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-lg flex items-center justify-center shadow-lg">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-gray-800">
                    ESL status in the last week
                  </h3>
                </div>

                <div className="bg-white/50 rounded-xl p-4 border border-white/30">
                  <Line
                    data={lineChartData}
                    options={{
                      responsive: true,
                      scales: {
                        y: {
                          beginAtZero: true,
                          grid: {
                            color: 'rgba(148, 163, 184, 0.1)',
                          },
                          ticks: {
                            color: 'rgba(71, 85, 105, 0.8)',
                          }
                        },
                        x: {
                          grid: {
                            color: 'rgba(148, 163, 184, 0.1)',
                          },
                          ticks: {
                            color: 'rgba(71, 85, 105, 0.8)',
                          }
                        }
                      },
                      plugins: {
                        legend: {
                          labels: {
                            color: 'rgba(71, 85, 105, 0.8)',
                          }
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Doughnut Chart */}
            <div className="relative">
              {/* 玻璃效果背景 */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/80 via-slate-50/60 to-white/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>

              {/* 動態光效背景 */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-2xl animate-pulse opacity-30"></div>

              {/* 內容區域 */}
              <div className="relative p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-green-500 rounded-lg flex items-center justify-center shadow-lg">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-gray-800">
                    Success rate
                  </h3>
                </div>

                <div className="bg-white/50 rounded-xl p-4 border border-white/30">
                  <div className="relative" style={{ height: '250px' }}>
                    <Doughnut
                      data={doughnutChartData}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '60%',
                        plugins: {
                          legend: {
                            position: 'top' as const,
                            labels: {
                              padding: 15,
                              font: {
                                size: 12
                              },
                              color: 'rgba(71, 85, 105, 0.8)',
                            }
                          },
                        },
                      }}
                    />
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                      <div className="w-20 h-20 bg-white/80 rounded-full flex flex-col items-center justify-center backdrop-blur-sm border border-white/30 shadow-lg">
                        <div className="text-xl font-bold text-gray-800">{successRate}%</div>
                        <div className="text-lg">
                          {successRate >= 80 ? '😊' : successRate >= 50 ? '😐' : '😕'}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          
          {/* 刷圖計畫視圖 */}
          {store && (
            <div className="relative mb-6">
              {/* 根據選擇的視圖類型渲染對應組件 */}
              {planViewType === 'calendar' && <RefreshPlanCalendar store={store} />}
              {planViewType === 'timeline' && <RefreshPlanTimeline store={store} />}
              {planViewType === 'heatmap' && <RefreshPlanHeatmap store={store} />}

              {/* 視圖切換控制 - 移動到圖表下方 */}
              <div ref={chartSwitchRef} className="flex items-center justify-center mt-4">
                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-1 border border-white/30 shadow-lg">
                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => handleViewTypeChange('calendar')}
                      className={`
                        flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200
                        ${planViewType === 'calendar'
                          ? 'bg-purple-500 text-white shadow-md'
                          : 'text-gray-600 hover:bg-white/50'
                        }
                      `}
                    >
                      <Calendar size={16} />
                      <span className="text-sm font-medium">月曆視圖</span>
                    </button>

                    <button
                      onClick={() => handleViewTypeChange('timeline')}
                      className={`
                        flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200
                        ${planViewType === 'timeline'
                          ? 'bg-indigo-500 text-white shadow-md'
                          : 'text-gray-600 hover:bg-white/50'
                        }
                      `}
                    >
                      <BarChart3 size={16} />
                      <span className="text-sm font-medium">時間軸</span>
                    </button>

                    <button
                      onClick={() => handleViewTypeChange('heatmap')}
                      className={`
                        flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200
                        ${planViewType === 'heatmap'
                          ? 'bg-green-500 text-white shadow-md'
                          : 'text-gray-600 hover:bg-white/50'
                        }
                      `}
                    >
                      <Flame size={16} />
                      <span className="text-sm font-medium">熱力圖</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 添加門店資料模態窗口 */}
      {store && (
        <AddStoreDataModal
          isOpen={showAddModal}
          dataFields={dataFields}
          storeId={store.id}
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            // 重新獲取門店資料
            if (store) {
              getAllStoreData(store.id).then(data => {
                setStoreData(data);
                setShowAddModal(false);
              }).catch(err => {
                console.error('重新獲取門店資料失敗:', err);
              });
            }
          }}
        />
      )}
    </>
  );
}