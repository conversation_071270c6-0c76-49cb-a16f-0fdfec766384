// 刷圖計畫相關類型定義

export interface TriggerConfig {
  type: 'once' | 'daily' | 'weekly';
  executeTime: string;        // 執行時間，格式: "HH:mm"
  executeDate?: string;       // 單次執行日期，格式: "YYYY-MM-DD"
  weekDays?: number[];        // 週執行的星期，0=週日, 1=週一, ..., 6=週六
}

export interface TargetSelection {
  type: 'mac_addresses' | 'store_data';
  macAddresses?: string[];     // MAC 地址列表
  storeDataIds?: string[];     // 門店數據 ID 列表
}

export interface ExecutionConfig {
  useSystemSettings: boolean;      // 是否使用系統設定（強制為 true，不可關閉）
}

export interface PlanStatistics {
  totalRuns: number;         // 總執行次數
  successRuns: number;       // 成功次數
  failedRuns: number;        // 失敗次數
  lastRunResult?: any;       // 最後執行結果
}

export interface RefreshPlan {
  _id: string;
  storeId: string;             // 所屬門店ID
  name: string;                // 計畫名稱
  description?: string;        // 計畫描述
  enabled: boolean;            // 啟用狀態
  priority: 'high' | 'medium' | 'low';  // 優先級
  
  // 刷圖對象選擇配置
  targetSelection: TargetSelection;
  
  // 觸發配置
  trigger: TriggerConfig;
  
  // 執行策略
  execution: ExecutionConfig;
  
  // 狀態信息
  status: 'active' | 'inactive' | 'running' | 'error';
  lastRun?: string;             // 最後執行時間
  nextRun?: string;             // 下次執行時間
  
  // 統計信息
  statistics: PlanStatistics;
  
  createdAt: string;
  updatedAt: string;
  createdBy: string;          // 創建者ID
}

export interface ExecutionRecord {
  _id: string;
  planId: string;             // 計畫ID
  storeId: string;            // 門店ID
  startTime: string;          // 開始時間
  endTime?: string;           // 結束時間
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  
  // 執行結果
  result: {
    totalDevices: number;      // 總設備數
    successDevices: number;    // 成功設備數
    failedDevices: number;     // 失敗設備數
    skippedDevices: number;    // 跳過設備數
    processingTime: number;    // 處理時間（毫秒）
  };
  
  // 詳細結果
  deviceResults: {
    deviceId: string;
    status: 'success' | 'failed' | 'skipped';
    error?: string;
    processingTime?: number;
  }[];
  
  // 錯誤信息
  errors?: {
    type: string;
    message: string;
    timestamp: string;
  }[];
}

export interface PlanStatisticsData {
  period: 'day' | 'week' | 'month';
  totalExecutions: number;
  successExecutions: number;
  failedExecutions: number;
  successRate: string;
  avgProcessingTime: number;
  deviceStats: {
    totalDevices: number;
    successDevices: number;
    failedDevices: number;
  };
}

// API 請求和響應類型
export interface CreatePlanRequest {
  name: string;
  description?: string;
  enabled: boolean;
  priority: 'high' | 'medium' | 'low';
  targetSelection: TargetSelection;
  trigger: TriggerConfig;
  execution: ExecutionConfig;
}

export interface UpdatePlanRequest extends Partial<CreatePlanRequest> {}

export interface PlanListResponse {
  success: boolean;
  data: {
    plans: RefreshPlan[];
    total: number;
    page: number;
    limit: number;
  };
}

export interface PlanResponse {
  success: boolean;
  data: RefreshPlan;
}

export interface ExecutePlanResponse {
  success: boolean;
  data: {
    executionId: string;
    message: string;
  };
}

export interface ExecutionListResponse {
  success: boolean;
  data: {
    executions: ExecutionRecord[];
    total: number;
    page: number;
    limit: number;
  };
}

export interface StatisticsResponse {
  success: boolean;
  data: PlanStatisticsData;
}

// 表單驗證錯誤類型
export interface ValidationErrors {
  name?: string;
  targetSelection?: string;
  trigger?: string;
  execution?: string;
}

// 計畫配置向導步驟
export interface PlanConfigStep {
  id: number;
  title: string;
  component: React.ComponentType<any>;
  isValid?: boolean;
}

// 設備選擇相關類型
export interface DeviceOption {
  id: string;
  macAddress: string;
  name?: string;
  size?: string;
  colorType?: string;
  status?: string;
}

export interface StoreDataOption {
  id: string;
  name: string;
  description?: string;
  boundDeviceCount: number;
}

// 計畫卡片顯示狀態
export type PlanCardStatus = 'running' | 'active' | 'inactive' | 'error';

// 計畫操作類型
export type PlanAction = 'edit' | 'execute' | 'statistics' | 'delete' | 'toggle';

// 排序選項
export interface SortOption {
  field: 'name' | 'createdAt' | 'lastRun' | 'status';
  direction: 'asc' | 'desc';
}

// 篩選選項
export interface FilterOptions {
  status?: PlanCardStatus[];
  priority?: ('high' | 'medium' | 'low')[];
  targetType?: ('mac_addresses' | 'store_data')[];
}
