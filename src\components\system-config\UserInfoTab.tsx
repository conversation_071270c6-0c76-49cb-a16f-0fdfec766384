import React from 'react';
import { useTranslation } from 'react-i18next';

export function UserInfoTab() {
  const { t } = useTranslation();
  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">{t('systemConfig.userInfo')}</h2>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">{t('systemConfig.username')}</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">{t('systemConfig.email')}</label>
          <input
            type="email"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">{t('systemConfig.role')}</label>
          <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option>{t('systemConfig.admin')}</option>
            <option>{t('systemConfig.user')}</option>
            <option>{t('systemConfig.guest')}</option>
          </select>
        </div>
      </div>
    </div>
  );
}