# MCP/A2A架構設計文件

## 概述

本文檔描述了AI助手系統中的MCP（Model Context Protocol）和A2A（Agent-to-Agent）架構設計，用於實現智能化的任務執行和代理間通信。

## MCP (Model Context Protocol) 架構

### 1. MCP核心概念

MCP是一個標準化的協議，用於AI模型與外部工具和服務之間的通信。在我們的系統中，MCP負責：

- 標準化AI請求和響應格式
- 管理上下文信息傳遞
- 確保工具調用的安全性
- 提供可擴展的插件機制

### 2. MCP協議實現

#### 2.1 協議定義

```typescript
// src/types/mcp.ts
interface MCPRequest {
  id: string;
  method: string;
  params: any;
  context: MCPContext;
  metadata: MCPMetadata;
}

interface MCPResponse {
  id: string;
  result?: any;
  error?: MCPError;
  context: MCPContext;
}

interface MCPContext {
  sessionId: string;
  userId: string;
  permissions: string[];
  previousActions: MCPAction[];
  environment: 'development' | 'production';
}

interface MCPMetadata {
  timestamp: number;
  version: string;
  source: string;
  priority: 'low' | 'medium' | 'high';
}

interface MCPAction {
  type: string;
  parameters: any;
  result: any;
  timestamp: number;
}
```

#### 2.2 MCP服務器實現

```javascript
// server/services/mcpServer.js
class MCPServer {
  constructor() {
    this.tools = new Map();
    this.sessions = new Map();
    this.middleware = [];
  }

  // 註冊工具
  registerTool(name, tool) {
    this.tools.set(name, tool);
    console.log(`MCP工具已註冊: ${name}`);
  }

  // 處理MCP請求
  async handleRequest(request) {
    try {
      // 驗證請求格式
      this.validateRequest(request);
      
      // 檢查權限
      await this.checkPermissions(request);
      
      // 執行中間件
      await this.runMiddleware(request);
      
      // 查找並執行工具
      const tool = this.tools.get(request.method);
      if (!tool) {
        throw new MCPError('TOOL_NOT_FOUND', `工具 ${request.method} 不存在`);
      }
      
      const result = await tool.execute(request.params, request.context);
      
      // 更新上下文
      this.updateContext(request.context, {
        type: request.method,
        parameters: request.params,
        result: result,
        timestamp: Date.now()
      });
      
      return {
        id: request.id,
        result: result,
        context: request.context
      };
    } catch (error) {
      return {
        id: request.id,
        error: {
          code: error.code || 'INTERNAL_ERROR',
          message: error.message,
          data: error.data
        },
        context: request.context
      };
    }
  }

  // 註冊中間件
  use(middleware) {
    this.middleware.push(middleware);
  }

  async runMiddleware(request) {
    for (const middleware of this.middleware) {
      await middleware(request);
    }
  }
}
```

#### 2.3 MCP工具實現

```javascript
// server/tools/storeCreationTool.js
class StoreCreationTool {
  constructor() {
    this.name = 'create_store';
    this.description = '創建新門店';
    this.parameters = {
      type: 'object',
      properties: {
        name: { type: 'string', description: '門店名稱' },
        id: { type: 'string', description: '門店ID' },
        address: { type: 'string', description: '門店地址' },
        phone: { type: 'string', description: '聯繫電話' },
        managerId: { type: 'string', description: '管理員ID' }
      },
      required: ['name']
    };
  }

  async execute(params, context) {
    try {
      // 驗證參數
      this.validateParams(params);
      
      // 檢查權限
      if (!context.permissions.includes('store:create')) {
        throw new Error('沒有創建門店的權限');
      }
      
      // 生成門店ID（如果未提供）
      if (!params.id) {
        params.id = this.generateStoreId(params.name);
      }
      
      // 調用門店創建服務
      const storeService = require('../services/storeService');
      const result = await storeService.createStore(params, context.userId);
      
      return {
        success: true,
        data: result,
        message: `門店 "${params.name}" 創建成功`
      };
    } catch (error) {
      throw new MCPError('STORE_CREATION_FAILED', error.message, { params });
    }
  }

  validateParams(params) {
    if (!params.name || typeof params.name !== 'string') {
      throw new Error('門店名稱為必填項且必須為字符串');
    }
    
    if (params.phone && !/^[\d\-\+\(\)\s]+$/.test(params.phone)) {
      throw new Error('電話號碼格式不正確');
    }
  }

  generateStoreId(name) {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    return `store-${timestamp}-${randomSuffix}`;
  }
}

module.exports = StoreCreationTool;
```

## A2A (Agent-to-Agent) 架構

### 1. A2A核心概念

A2A架構實現了多個AI代理之間的協作，每個代理專門處理特定類型的任務：

- **主代理（Master Agent）**: 負責理解用戶意圖和任務分發
- **門店代理（Store Agent）**: 專門處理門店相關操作
- **模板代理（Template Agent）**: 專門處理模板相關操作
- **用戶代理（User Agent）**: 專門處理用戶管理操作

### 2. A2A架構實現

#### 2.1 代理基類

```javascript
// server/agents/baseAgent.js
class BaseAgent {
  constructor(name, capabilities) {
    this.name = name;
    this.capabilities = capabilities;
    this.status = 'idle';
    this.currentTask = null;
  }

  async processTask(task) {
    try {
      this.status = 'busy';
      this.currentTask = task;
      
      console.log(`代理 ${this.name} 開始處理任務: ${task.type}`);
      
      const result = await this.executeTask(task);
      
      this.status = 'idle';
      this.currentTask = null;
      
      return result;
    } catch (error) {
      this.status = 'error';
      console.error(`代理 ${this.name} 處理任務失敗:`, error);
      throw error;
    }
  }

  async executeTask(task) {
    throw new Error('子類必須實現 executeTask 方法');
  }

  canHandle(taskType) {
    return this.capabilities.includes(taskType);
  }

  getStatus() {
    return {
      name: this.name,
      status: this.status,
      capabilities: this.capabilities,
      currentTask: this.currentTask
    };
  }
}

module.exports = BaseAgent;
```

#### 2.2 專門代理實現

```javascript
// server/agents/storeAgent.js
const BaseAgent = require('./baseAgent');

class StoreAgent extends BaseAgent {
  constructor() {
    super('StoreAgent', ['create_store', 'update_store', 'delete_store', 'query_store']);
    this.storeService = require('../services/storeService');
  }

  async executeTask(task) {
    switch (task.type) {
      case 'create_store':
        return await this.createStore(task.data);
      case 'update_store':
        return await this.updateStore(task.data);
      case 'delete_store':
        return await this.deleteStore(task.data);
      case 'query_store':
        return await this.queryStore(task.data);
      default:
        throw new Error(`不支援的任務類型: ${task.type}`);
    }
  }

  async createStore(storeData) {
    try {
      // 驗證門店數據
      this.validateStoreData(storeData);
      
      // 檢查門店ID是否重複
      const existingStore = await this.storeService.findByStoreId(storeData.id);
      if (existingStore) {
        throw new Error(`門店ID "${storeData.id}" 已存在`);
      }
      
      // 創建門店
      const newStore = await this.storeService.createStore(storeData);
      
      return {
        success: true,
        data: newStore,
        message: `門店 "${storeData.name}" 創建成功`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '門店創建失敗'
      };
    }
  }

  validateStoreData(data) {
    if (!data.name) throw new Error('門店名稱為必填項');
    if (!data.id) throw new Error('門店ID為必填項');
    
    // 更多驗證邏輯...
  }
}

module.exports = StoreAgent;
```

#### 2.3 代理管理器

```javascript
// server/services/agentManager.js
class AgentManager {
  constructor() {
    this.agents = new Map();
    this.taskQueue = [];
    this.isProcessing = false;
  }

  registerAgent(agent) {
    this.agents.set(agent.name, agent);
    console.log(`代理已註冊: ${agent.name}`);
  }

  async delegateTask(task) {
    // 找到能處理此任務的代理
    const suitableAgent = this.findSuitableAgent(task.type);
    
    if (!suitableAgent) {
      throw new Error(`找不到能處理 "${task.type}" 任務的代理`);
    }
    
    // 如果代理忙碌，加入隊列
    if (suitableAgent.status === 'busy') {
      return await this.queueTask(task);
    }
    
    // 直接處理任務
    return await suitableAgent.processTask(task);
  }

  findSuitableAgent(taskType) {
    for (const agent of this.agents.values()) {
      if (agent.canHandle(taskType) && agent.status === 'idle') {
        return agent;
      }
    }
    
    // 如果沒有空閒代理，返回第一個能處理的代理
    for (const agent of this.agents.values()) {
      if (agent.canHandle(taskType)) {
        return agent;
      }
    }
    
    return null;
  }

  async queueTask(task) {
    return new Promise((resolve, reject) => {
      this.taskQueue.push({
        task,
        resolve,
        reject,
        timestamp: Date.now()
      });
      
      this.processQueue();
    });
  }

  async processQueue() {
    if (this.isProcessing || this.taskQueue.length === 0) {
      return;
    }
    
    this.isProcessing = true;
    
    while (this.taskQueue.length > 0) {
      const queueItem = this.taskQueue.shift();
      const agent = this.findSuitableAgent(queueItem.task.type);
      
      if (agent && agent.status === 'idle') {
        try {
          const result = await agent.processTask(queueItem.task);
          queueItem.resolve(result);
        } catch (error) {
          queueItem.reject(error);
        }
      } else {
        // 重新加入隊列
        this.taskQueue.unshift(queueItem);
        break;
      }
    }
    
    this.isProcessing = false;
  }

  getAgentStatus() {
    const status = {};
    for (const [name, agent] of this.agents) {
      status[name] = agent.getStatus();
    }
    return status;
  }
}

module.exports = new AgentManager();
```

### 3. 整合MCP和A2A

#### 3.1 統一協調器

```javascript
// server/services/aiCoordinator.js
const mcpServer = require('./mcpServer');
const agentManager = require('./agentManager');

class AICoordinator {
  constructor() {
    this.mcpServer = mcpServer;
    this.agentManager = agentManager;
    this.setupMCPTools();
    this.setupAgents();
  }

  setupMCPTools() {
    // 註冊MCP工具
    const StoreCreationTool = require('../tools/storeCreationTool');
    const TemplateCreationTool = require('../tools/templateCreationTool');
    const UserCreationTool = require('../tools/userCreationTool');
    
    this.mcpServer.registerTool('create_store', new StoreCreationTool());
    this.mcpServer.registerTool('create_template', new TemplateCreationTool());
    this.mcpServer.registerTool('create_user', new UserCreationTool());
  }

  setupAgents() {
    // 註冊A2A代理
    const StoreAgent = require('../agents/storeAgent');
    const TemplateAgent = require('../agents/templateAgent');
    const UserAgent = require('../agents/userAgent');
    
    this.agentManager.registerAgent(new StoreAgent());
    this.agentManager.registerAgent(new TemplateAgent());
    this.agentManager.registerAgent(new UserAgent());
  }

  async processRequest(request) {
    try {
      // 首先嘗試通過MCP處理
      if (this.mcpServer.tools.has(request.method)) {
        return await this.mcpServer.handleRequest(request);
      }
      
      // 如果MCP無法處理，委託給A2A代理
      const task = {
        type: request.method,
        data: request.params,
        context: request.context
      };
      
      const result = await this.agentManager.delegateTask(task);
      
      return {
        id: request.id,
        result: result,
        context: request.context
      };
    } catch (error) {
      return {
        id: request.id,
        error: {
          code: error.code || 'PROCESSING_FAILED',
          message: error.message
        },
        context: request.context
      };
    }
  }

  getSystemStatus() {
    return {
      mcp: {
        tools: Array.from(this.mcpServer.tools.keys()),
        sessions: this.mcpServer.sessions.size
      },
      agents: this.agentManager.getAgentStatus(),
      timestamp: Date.now()
    };
  }
}

module.exports = new AICoordinator();
```

## 4. 使用示例

### 4.1 創建門店流程

```javascript
// 用戶請求: "創建一個名為'台北101店'的門店"

// 1. AI解析請求
const aiResponse = await aiService.processUserRequest(userInput);
// 結果: { intent: 'create_store', extractedData: { name: '台北101店' } }

// 2. 構建MCP請求
const mcpRequest = {
  id: generateId(),
  method: 'create_store',
  params: aiResponse.extractedData,
  context: userContext
};

// 3. 處理請求
const result = await aiCoordinator.processRequest(mcpRequest);

// 4. 返回結果給用戶
```

這個MCP/A2A架構提供了：

1. **標準化協議** - MCP確保一致的通信格式
2. **專業化代理** - A2A實現任務專門化處理
3. **可擴展性** - 易於添加新工具和代理
4. **容錯機制** - 完善的錯誤處理和重試邏輯
5. **監控能力** - 實時狀態監控和日誌記錄
