// 測試刷圖計畫 API 的腳本
const { MongoClient, ObjectId } = require('mongodb');

const MONGODB_URI = 'mongodb://localhost:27017';
const DB_NAME = 'resourceManagement';

async function testRefreshPlanAPI() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('連接到 MongoDB');
    
    const db = client.db(DB_NAME);
    
    // 檢查是否有門店數據
    const storesCollection = db.collection('stores');
    const stores = await storesCollection.find().toArray();
    console.log(`找到 ${stores.length} 個門店`);
    
    if (stores.length === 0) {
      console.log('沒有門店數據，創建測試門店...');
      
      // 創建測試門店
      const testStore = {
        id: 'test-store-001',
        name: '測試門店',
        address: '測試地址',
        status: 'active',
        managerId: new ObjectId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        storeSpecificData: [],
        gatewayManagement: {},
        deviceManagement: {},
        storeSettings: {}
      };
      
      const result = await storesCollection.insertOne(testStore);
      console.log('創建測試門店成功:', result.insertedId);
      stores.push(testStore);
    }
    
    const testStoreId = stores[0].id;
    console.log(`使用門店 ID: ${testStoreId}`);
    
    // 檢查刷圖計畫集合
    const refreshPlansCollection = db.collection('refreshPlans');
    const existingPlans = await refreshPlansCollection.find({ storeId: testStoreId }).toArray();
    console.log(`門店 ${testStoreId} 現有 ${existingPlans.length} 個刷圖計畫`);
    
    // 創建測試刷圖計畫
    const testPlan = {
      storeId: testStoreId,
      name: '測試每日刷圖計畫',
      description: '這是一個測試用的每日刷圖計畫',
      enabled: true,
      priority: 'medium',
      targetSelection: {
        type: 'mac_addresses',
        macAddresses: ['AA:BB:CC:DD:EE:01', 'AA:BB:CC:DD:EE:02']
      },
      trigger: {
        type: 'daily',
        executeTime: '08:00'
      },
      execution: {
        useSystemSettings: true
      },
      status: 'active',
      lastRun: null,
      nextRun: null,
      statistics: {
        totalRuns: 0,
        successRuns: 0,
        failedRuns: 0,
        lastRunResult: null
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: new ObjectId().toString()
    };
    
    // 檢查是否已存在同名計畫
    const existingPlan = await refreshPlansCollection.findOne({ 
      storeId: testStoreId, 
      name: testPlan.name 
    });
    
    if (!existingPlan) {
      const planResult = await refreshPlansCollection.insertOne(testPlan);
      console.log('創建測試刷圖計畫成功:', planResult.insertedId);
    } else {
      console.log('測試刷圖計畫已存在:', existingPlan._id);
    }
    
    // 測試查詢計畫
    const allPlans = await refreshPlansCollection.find({ storeId: testStoreId }).toArray();
    console.log(`門店 ${testStoreId} 總共有 ${allPlans.length} 個刷圖計畫:`);
    
    allPlans.forEach((plan, index) => {
      console.log(`  ${index + 1}. ${plan.name} (${plan.status}) - ${plan.enabled ? '已啟用' : '已停用'}`);
    });
    
    // 創建測試設備數據
    const devicesCollection = db.collection('devices');
    const existingDevices = await devicesCollection.find({ storeId: testStoreId }).toArray();
    console.log(`門店 ${testStoreId} 現有 ${existingDevices.length} 個設備`);
    
    if (existingDevices.length === 0) {
      console.log('創建測試設備...');
      
      const testDevices = [
        {
          storeId: testStoreId,
          macAddress: 'AA:BB:CC:DD:EE:01',
          name: '測試設備1',
          size: '2.13',
          colorType: 'BW',
          status: 'online',
          templateId: null,
          dataBindings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          storeId: testStoreId,
          macAddress: 'AA:BB:CC:DD:EE:02',
          name: '測試設備2',
          size: '4.2',
          colorType: 'BWR',
          status: 'online',
          templateId: null,
          dataBindings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      
      const deviceResult = await devicesCollection.insertMany(testDevices);
      console.log(`創建 ${deviceResult.insertedCount} 個測試設備`);
    }
    
    console.log('\n測試數據準備完成！');
    console.log('現在可以在前端測試刷圖計畫功能了。');
    console.log(`門店 ID: ${testStoreId}`);
    console.log('前端地址: http://localhost:5173/');
    
  } catch (error) {
    console.error('測試失敗:', error);
  } finally {
    await client.close();
  }
}

// 執行測試
testRefreshPlanAPI();
