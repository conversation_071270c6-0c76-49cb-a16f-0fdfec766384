import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../../store/authStore';
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';

export const ChangePasswordPage: React.FC = () => {
  const { t } = useTranslation();
  const { changePassword, loading, error, clearError } = useAuthStore();
  
  // 表單狀態
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [success, setSuccess] = useState(false);
  
  // 處理修改密碼
  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 驗證表單
    if (!currentPassword || !newPassword || !confirmPassword) {
      return;
    }
    
    if (newPassword !== confirmPassword) {
      return;
    }
    
    try {
      await changePassword(currentPassword, newPassword);
      
      // 清空表單
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      
      // 顯示成功提示
      setSuccess(true);
      
      // 3 秒後隱藏成功提示
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('修改密碼錯誤:', error);
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>{t('auth.changePassword')}</CardTitle>
            <CardDescription>
              {t('auth.changePasswordDescription')}
            </CardDescription>
          </CardHeader>
          
          <form onSubmit={handleChangePassword}>
            <CardContent className="space-y-4">
              {/* 成功提示 */}
              {success && (
                <div className="bg-green-50 border border-green-200 rounded-md p-4 flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5" />
                  <p className="text-sm text-green-800">{t('auth.passwordChanged')}</p>
                </div>
              )}
              
              {/* 錯誤提示 */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4 flex items-start">
                  <AlertCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm text-red-800">{error}</p>
                    <button
                      onClick={clearError}
                      className="text-xs text-red-600 hover:text-red-800 mt-1"
                    >
                      {t('auth.dismiss')}
                    </button>
                  </div>
                </div>
              )}
              
              {/* 當前密碼 */}
              <div className="space-y-2">
                <Label htmlFor="current-password">
                  {t('auth.currentPassword')}
                </Label>
                <div className="relative">
                  <Input
                    id="current-password"
                    type={showCurrentPassword ? 'text' : 'password'}
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    placeholder={t('auth.currentPasswordPlaceholder')}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showCurrentPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              
              {/* 新密碼 */}
              <div className="space-y-2">
                <Label htmlFor="new-password">
                  {t('auth.newPassword')}
                </Label>
                <div className="relative">
                  <Input
                    id="new-password"
                    type={showNewPassword ? 'text' : 'password'}
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder={t('auth.newPasswordPlaceholder')}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showNewPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              
              {/* 確認新密碼 */}
              <div className="space-y-2">
                <Label htmlFor="confirm-password">
                  {t('auth.confirmNewPassword')}
                </Label>
                <div className="relative">
                  <Input
                    id="confirm-password"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder={t('auth.confirmNewPasswordPlaceholder')}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {newPassword && confirmPassword && newPassword !== confirmPassword && (
                  <p className="text-xs text-red-600 mt-1">
                    {t('auth.passwordMismatch')}
                  </p>
                )}
              </div>
            </CardContent>
            
            <CardFooter>
              <Button 
                type="submit" 
                disabled={
                  loading || 
                  !currentPassword || 
                  !newPassword || 
                  !confirmPassword || 
                  newPassword !== confirmPassword
                }
                className="w-full"
              >
                {loading ? t('common.saving') : t('auth.changePassword')}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
};
