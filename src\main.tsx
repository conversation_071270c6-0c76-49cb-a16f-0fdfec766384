import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import App from './App.tsx';
import './index.css';

// 導入 i18n 配置
import './i18n';

// 初始化 WebSocket 客戶端
import { getWebSocketClient } from './utils/websocketClient';

// 在應用啟動時就建立 WebSocket 連接
console.log('初始化 WebSocket 客戶端...');
getWebSocketClient();

// 添加 crypto.randomUUID polyfill 以解決兼容性問題
// 立即執行，確保在任何其他代碼運行之前就可用
(function() {
  // 檢查是否需要 polyfill
  if (!window.crypto) {
    (window as any).crypto = {};
  }

  if (!window.crypto.randomUUID) {
    // 創建一個簡單的 UUID v4 生成器作為 polyfill
    (window.crypto as any).randomUUID = function() {
      // 使用 crypto.getRandomValues 如果可用，否則使用 Math.random
      let randomValues: Uint8Array;

      if (window.crypto && window.crypto.getRandomValues) {
        randomValues = new Uint8Array(16);
        window.crypto.getRandomValues(randomValues);
      } else {
        // 回退到 Math.random（不夠安全，但可以工作）
        randomValues = new Uint8Array(16);
        for (let i = 0; i < 16; i++) {
          randomValues[i] = Math.floor(Math.random() * 256);
        }
      }

      // 設置版本位 (4) 和變體位
      randomValues[6] = (randomValues[6] & 0x0f) | 0x40; // 版本 4
      randomValues[8] = (randomValues[8] & 0x3f) | 0x80; // 變體位

      // 轉換為 UUID 字符串格式
      const hex = Array.from(randomValues, byte => byte.toString(16).padStart(2, '0')).join('');
      return [
        hex.slice(0, 8),
        hex.slice(8, 12),
        hex.slice(12, 16),
        hex.slice(16, 20),
        hex.slice(20, 32)
      ].join('-');
    };

    console.log('已添加 crypto.randomUUID polyfill (內建實現)');
  }

  // 額外載入 uuid 套件作為備用（異步）
  import('uuid').then(({ v4: uuidv4 }) => {
    // 如果 uuid 套件載入成功，使用更可靠的實現
    (window.crypto as any).randomUUID = uuidv4;
    console.log('已升級 crypto.randomUUID polyfill (使用 uuid 套件)');
  }).catch(error => {
    console.warn('無法載入 uuid 套件，使用內建 polyfill:', error);
  });
})();

// 添加全局錯誤處理器
window.addEventListener('error', (event) => {
  console.error('全局錯誤:', event.error);
});

// 添加未處理的 Promise 拒絕處理器
window.addEventListener('unhandledrejection', (event) => {
  console.error('未處理的 Promise 拒絕:', event.reason);
});

// 確保 DOM 已加載
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM 已加載，開始渲染 React 應用');

  const rootElement = document.getElementById('root');

  if (rootElement) {
    try {
      console.log('掛載 React 應用');
      const root = createRoot(rootElement);
      root.render(
        <StrictMode>
          <BrowserRouter>
            <App />
          </BrowserRouter>
        </StrictMode>
      );
      console.log('React 應用掛載成功');
    } catch (error) {
      console.error('React 渲染錯誤:', error);
      // 如果 React 無法渲染，顯示錯誤信息
      rootElement.innerHTML = `
        <div style="display:flex; justify-content:center; align-items:center; height:100vh; flex-direction:column; padding:20px;">
          <h2 style="color:red; margin-bottom:20px;">無法渲染應用</h2>
          <p>發生錯誤: ${String(error)}</p>
          <button onclick="location.reload()" style="margin-top:20px; padding:10px 20px; background:#3b82f6; color:white; border:none; border-radius:4px; cursor:pointer;">
            重新加載頁面
          </button>
        </div>
      `;
    }
  } else {
    console.error('找不到 root 元素');
  }
});
