# 性能優化策略

## 1. 防抖與合併機制

### 1.1 後端防抖策略

#### 1.1.1 設備狀態更新防抖
```javascript
// 防抖配置
const DEBOUNCE_CONFIG = {
  DEVICE_STATUS_DELAY: 500,      // 設備狀態更新防抖延遲
  MAX_PENDING_TIME: 2000,        // 最大待處理時間
  MAX_BATCH_SIZE: 50,            // 最大批量大小
  FORCE_FLUSH_INTERVAL: 5000     // 強制刷新間隔
};

class DeviceStatusDebouncer {
  constructor() {
    this.pendingUpdates = new Map(); // storeId -> Map<deviceId, deviceUpdate>
    this.timers = new Map();         // storeId -> timer
    this.lastFlushTime = new Map();  // storeId -> timestamp
  }

  // 添加設備更新
  addUpdate(storeId, deviceUpdate) {
    // 初始化門店的待處理更新
    if (!this.pendingUpdates.has(storeId)) {
      this.pendingUpdates.set(storeId, new Map());
      this.lastFlushTime.set(storeId, Date.now());
    }

    // 合併同一設備的多次更新
    const storeUpdates = this.pendingUpdates.get(storeId);
    const existingUpdate = storeUpdates.get(deviceUpdate._id);
    
    if (existingUpdate) {
      // 合併更新字段
      const mergedUpdate = {
        ...existingUpdate,
        ...deviceUpdate,
        updatedFields: [
          ...new Set([
            ...existingUpdate.updatedFields,
            ...deviceUpdate.updatedFields
          ])
        ]
      };
      storeUpdates.set(deviceUpdate._id, mergedUpdate);
    } else {
      storeUpdates.set(deviceUpdate._id, deviceUpdate);
    }

    // 檢查是否需要強制刷新
    const timeSinceLastFlush = Date.now() - this.lastFlushTime.get(storeId);
    const pendingCount = storeUpdates.size;

    if (timeSinceLastFlush > DEBOUNCE_CONFIG.MAX_PENDING_TIME || 
        pendingCount >= DEBOUNCE_CONFIG.MAX_BATCH_SIZE) {
      // 立即刷新
      this.flushStore(storeId);
      return;
    }

    // 設置或重置防抖定時器
    if (this.timers.has(storeId)) {
      clearTimeout(this.timers.get(storeId));
    }

    const timer = setTimeout(() => {
      this.flushStore(storeId);
    }, DEBOUNCE_CONFIG.DEVICE_STATUS_DELAY);

    this.timers.set(storeId, timer);
  }

  // 刷新指定門店的更新
  flushStore(storeId) {
    const storeUpdates = this.pendingUpdates.get(storeId);
    if (!storeUpdates || storeUpdates.size === 0) return;

    const updates = Array.from(storeUpdates.values());
    
    // 清理待處理更新
    this.pendingUpdates.delete(storeId);
    this.lastFlushTime.set(storeId, Date.now());
    
    if (this.timers.has(storeId)) {
      clearTimeout(this.timers.get(storeId));
      this.timers.delete(storeId);
    }

    // 執行廣播
    this.broadcastUpdates(storeId, updates);
  }

  // 廣播更新
  broadcastUpdates(storeId, updates) {
    const subscribers = getDeviceStatusSubscribers(storeId);
    
    if (subscribers.size === 0) return;

    const event = {
      type: 'device_status_update',
      storeId,
      devices: updates,
      timestamp: new Date().toISOString(),
      updateType: updates.length > 10 ? 'batch' : 'single',
      batchInfo: {
        count: updates.length,
        hasImageStatusUpdates: updates.some(u => u.updatedFields.includes('imageUpdateStatus')),
        hasBatteryUpdates: updates.some(u => u.data?.battery !== undefined)
      }
    };

    console.log(`廣播設備狀態: 門店=${storeId}, 設備數=${updates.length}, 訂閱者=${subscribers.size}`);

    subscribers.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify(event));
        } catch (error) {
          console.error('廣播設備狀態失敗:', error);
        }
      }
    });
  }

  // 強制刷新所有待處理更新
  flushAll() {
    for (const storeId of this.pendingUpdates.keys()) {
      this.flushStore(storeId);
    }
  }
}
```

### 1.2 前端防抖策略

#### 1.2.1 設備列表更新防抖
```typescript
// 前端更新防抖Hook
const useDeviceStatusUpdates = (storeId: string) => {
  const [devices, setDevices] = useState<Device[]>([]);
  const [pendingUpdates, setPendingUpdates] = useState<Map<string, any>>(new Map());
  const updateTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 防抖配置
  const FRONTEND_DEBOUNCE_DELAY = 200;
  const MAX_PENDING_UPDATES = 20;

  // 處理設備狀態更新
  const handleDeviceStatusUpdate = useCallback((event: DeviceStatusEvent) => {
    if (event.storeId !== storeId) return;

    // 合併待處理更新
    setPendingUpdates(prev => {
      const newPending = new Map(prev);
      
      event.devices.forEach(deviceUpdate => {
        const existing = newPending.get(deviceUpdate._id);
        if (existing) {
          // 合併更新
          newPending.set(deviceUpdate._id, {
            ...existing,
            ...deviceUpdate,
            updatedFields: [
              ...new Set([
                ...existing.updatedFields,
                ...deviceUpdate.updatedFields
              ])
            ]
          });
        } else {
          newPending.set(deviceUpdate._id, deviceUpdate);
        }
      });

      // 如果待處理更新過多，立即應用
      if (newPending.size >= MAX_PENDING_UPDATES) {
        applyPendingUpdates(newPending);
        return new Map();
      }

      return newPending;
    });

    // 設置防抖定時器
    if (updateTimerRef.current) {
      clearTimeout(updateTimerRef.current);
    }

    updateTimerRef.current = setTimeout(() => {
      setPendingUpdates(pending => {
        if (pending.size > 0) {
          applyPendingUpdates(pending);
        }
        return new Map();
      });
    }, FRONTEND_DEBOUNCE_DELAY);
  }, [storeId]);

  // 應用待處理更新
  const applyPendingUpdates = useCallback((updates: Map<string, any>) => {
    setDevices(prevDevices => {
      return prevDevices.map(device => {
        const update = updates.get(device._id || '');
        if (update) {
          return {
            ...device,
            status: update.status || device.status,
            lastSeen: update.lastSeen ? new Date(update.lastSeen) : device.lastSeen,
            imageUpdateStatus: update.imageUpdateStatus || device.imageUpdateStatus,
            data: {
              ...device.data,
              ...update.data
            }
          };
        }
        return device;
      });
    });
  }, []);

  return { devices, setDevices, handleDeviceStatusUpdate };
};
```

## 2. 流量控制策略

### 2.1 廣播頻率限制
```javascript
// 廣播頻率控制器
class BroadcastRateLimiter {
  constructor() {
    this.storeLastBroadcast = new Map(); // storeId -> timestamp
    this.globalLastBroadcast = 0;
    this.MIN_BROADCAST_INTERVAL = 100;   // 最小廣播間隔(ms)
    this.MIN_GLOBAL_INTERVAL = 50;       // 全局最小間隔(ms)
  }

  // 檢查是否可以廣播
  canBroadcast(storeId) {
    const now = Date.now();
    
    // 檢查全局頻率限制
    if (now - this.globalLastBroadcast < this.MIN_GLOBAL_INTERVAL) {
      return false;
    }

    // 檢查門店特定頻率限制
    const lastBroadcast = this.storeLastBroadcast.get(storeId) || 0;
    if (now - lastBroadcast < this.MIN_BROADCAST_INTERVAL) {
      return false;
    }

    return true;
  }

  // 記錄廣播時間
  recordBroadcast(storeId) {
    const now = Date.now();
    this.storeLastBroadcast.set(storeId, now);
    this.globalLastBroadcast = now;
  }

  // 獲取下次可廣播時間
  getNextBroadcastTime(storeId) {
    const now = Date.now();
    const lastBroadcast = this.storeLastBroadcast.get(storeId) || 0;
    const globalNext = this.globalLastBroadcast + this.MIN_GLOBAL_INTERVAL;
    const storeNext = lastBroadcast + this.MIN_BROADCAST_INTERVAL;
    
    return Math.max(globalNext, storeNext, now);
  }
}
```

### 2.2 連接數量管理
```javascript
// WebSocket連接管理器
class ConnectionManager {
  constructor() {
    this.maxConnectionsPerStore = 10;
    this.connectionsByStore = new Map(); // storeId -> Set<WebSocket>
    this.connectionMetrics = {
      totalConnections: 0,
      deviceStatusSubscriptions: 0,
      batchProgressSubscriptions: 0
    };
  }

  // 添加連接
  addConnection(ws, storeId) {
    if (!this.connectionsByStore.has(storeId)) {
      this.connectionsByStore.set(storeId, new Set());
    }

    const storeConnections = this.connectionsByStore.get(storeId);
    
    // 檢查連接數量限制
    if (storeConnections.size >= this.maxConnectionsPerStore) {
      console.warn(`門店 ${storeId} 連接數已達上限: ${this.maxConnectionsPerStore}`);
      return false;
    }

    storeConnections.add(ws);
    this.connectionMetrics.totalConnections++;
    
    return true;
  }

  // 移除連接
  removeConnection(ws, storeId) {
    if (this.connectionsByStore.has(storeId)) {
      const storeConnections = this.connectionsByStore.get(storeId);
      if (storeConnections.delete(ws)) {
        this.connectionMetrics.totalConnections--;
      }
    }
  }

  // 獲取連接統計
  getConnectionStats() {
    const storeStats = new Map();
    
    for (const [storeId, connections] of this.connectionsByStore) {
      storeStats.set(storeId, {
        totalConnections: connections.size,
        deviceStatusSubscribers: Array.from(connections).filter(
          ws => ws.subscribedStoreId === storeId
        ).length
      });
    }

    return {
      global: this.connectionMetrics,
      byStore: storeStats
    };
  }
}
```

## 3. 前端渲染優化

### 3.1 虛擬化列表
```typescript
// 使用 react-window 進行虛擬化渲染
import { FixedSizeList as List } from 'react-window';

const VirtualizedDeviceList: React.FC<{
  devices: Device[];
  onDeviceUpdate: (device: Device) => void;
}> = ({ devices, onDeviceUpdate }) => {
  const itemHeight = 60; // 每行高度
  const containerHeight = 400; // 容器高度

  const DeviceRow = ({ index, style }: { index: number; style: any }) => {
    const device = devices[index];
    
    return (
      <div style={style} className="device-row">
        <DeviceStatusBadge status={device.status} />
        <span>{device.macAddress}</span>
        {/* 其他設備信息 */}
      </div>
    );
  };

  return (
    <List
      height={containerHeight}
      itemCount={devices.length}
      itemSize={itemHeight}
      itemData={devices}
    >
      {DeviceRow}
    </List>
  );
};
```

### 3.2 選擇性重渲染
```typescript
// 使用 React.memo 和 useMemo 優化渲染
const DeviceListItem = React.memo<{
  device: Device;
  onUpdate: (device: Device) => void;
}>(({ device, onUpdate }) => {
  // 只有設備狀態相關字段變更時才重新渲染
  const statusInfo = useMemo(() => ({
    status: device.status,
    lastSeen: device.lastSeen,
    imageUpdateStatus: device.imageUpdateStatus,
    battery: device.data?.battery
  }), [
    device.status,
    device.lastSeen,
    device.imageUpdateStatus,
    device.data?.battery
  ]);

  return (
    <div className="device-item">
      <DeviceStatusBadge status={statusInfo.status} />
      <BatteryIndicator level={statusInfo.battery} />
      {/* 其他組件 */}
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定義比較函數，只比較關鍵字段
  const prev = prevProps.device;
  const next = nextProps.device;
  
  return (
    prev.status === next.status &&
    prev.lastSeen?.getTime() === next.lastSeen?.getTime() &&
    prev.imageUpdateStatus === next.imageUpdateStatus &&
    prev.data?.battery === next.data?.battery
  );
});
```
