const { ObjectId } = require('mongodb');

/**
 * 權限分配模型
 * 用於存儲用戶與角色的關聯
 */
class Permission {
  /**
   * 創建權限分配集合
   * @param {Object} db - MongoDB 數據庫實例
   * @returns {Object} - MongoDB 集合
   */
  static getCollection(db) {
    return db.collection('permissions');
  }

  /**
   * 創建新權限分配
   * @param {Object} db - MongoDB 數據庫實例
   * @param {Object} permissionData - 權限分配數據
   * @returns {Object} - 創建的權限分配
   */
  static async createPermission(db, permissionData) {
    const collection = this.getCollection(db);
    
    // 檢查用戶是否已經有相同範圍的權限分配
    const existingPermission = await collection.findOne({ 
      userId: new ObjectId(permissionData.userId),
      scope: permissionData.scope
    });
    
    if (existingPermission) {
      throw new Error('用戶已有相同範圍的權限分配');
    }

    // 創建權限分配
    const newPermission = {
      userId: new ObjectId(permissionData.userId),
      roleId: new ObjectId(permissionData.roleId),
      scope: permissionData.scope, // 'system' 或 特定門店ID
      scopeType: permissionData.scopeType || 'system', // 'system' 或 'store'
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await collection.insertOne(newPermission);
    return { ...newPermission, _id: result.insertedId };
  }

  /**
   * 根據 ID 查找權限分配
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 權限分配 ID
   * @returns {Object|null} - 找到的權限分配或 null
   */
  static async findById(db, id) {
    const collection = this.getCollection(db);
    return await collection.findOne({ _id: new ObjectId(id) });
  }

  /**
   * 根據用戶 ID 查找權限分配
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} userId - 用戶 ID
   * @returns {Array} - 權限分配列表
   */
  static async findByUserId(db, userId) {
    const collection = this.getCollection(db);
    return await collection.find({ userId: new ObjectId(userId) }).toArray();
  }

  /**
   * 獲取所有權限分配
   * @param {Object} db - MongoDB 數據庫實例
   * @param {Object} filter - 過濾條件
   * @param {Object} options - 查詢選項
   * @returns {Array} - 權限分配列表
   */
  static async findAll(db, filter = {}, options = {}) {
    const collection = this.getCollection(db);
    
    // 處理 filter 中的 ObjectId
    if (filter.userId && typeof filter.userId === 'string') {
      filter.userId = new ObjectId(filter.userId);
    }
    if (filter.roleId && typeof filter.roleId === 'string') {
      filter.roleId = new ObjectId(filter.roleId);
    }
    
    return await collection.find(filter, options).toArray();
  }

  /**
   * 獲取帶有用戶和角色信息的權限分配列表
   * @param {Object} db - MongoDB 數據庫實例
   * @param {Object} filter - 過濾條件
   * @returns {Array} - 權限分配列表
   */
  static async findAllWithDetails(db, filter = {}) {
    const collection = this.getCollection(db);
    
    // 處理 filter 中的 ObjectId
    if (filter.userId && typeof filter.userId === 'string') {
      filter.userId = new ObjectId(filter.userId);
    }
    if (filter.roleId && typeof filter.roleId === 'string') {
      filter.roleId = new ObjectId(filter.roleId);
    }
    
    // 使用聚合管道獲取詳細信息
    return await collection.aggregate([
      { $match: filter },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $lookup: {
          from: 'roles',
          localField: 'roleId',
          foreignField: '_id',
          as: 'role'
        }
      },
      {
        $unwind: {
          path: '$user',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $unwind: {
          path: '$role',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          _id: 1,
          userId: 1,
          roleId: 1,
          scope: 1,
          scopeType: 1,
          createdAt: 1,
          updatedAt: 1,
          'user.username': 1,
          'user.name': 1,
          'user.email': 1,
          'user.phone': 1,
          'user.status': 1,
          'role.name': 1,
          'role.description': 1,
          'role.type': 1
        }
      }
    ]).toArray();
  }

  /**
   * 更新權限分配
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 權限分配 ID
   * @param {Object} permissionData - 更新的權限分配數據
   * @returns {Object} - 更新結果
   */
  static async updatePermission(db, id, permissionData) {
    const collection = this.getCollection(db);
    
    // 如果要更新用戶或範圍，檢查是否已存在
    if (permissionData.userId && permissionData.scope) {
      const existingPermission = await collection.findOne({ 
        userId: new ObjectId(permissionData.userId),
        scope: permissionData.scope,
        _id: { $ne: new ObjectId(id) }
      });
      
      if (existingPermission) {
        throw new Error('用戶已有相同範圍的權限分配');
      }
    }

    const updateData = {
      ...permissionData,
      updatedAt: new Date()
    };

    // 處理 ObjectId
    if (updateData.userId && typeof updateData.userId === 'string') {
      updateData.userId = new ObjectId(updateData.userId);
    }
    if (updateData.roleId && typeof updateData.roleId === 'string') {
      updateData.roleId = new ObjectId(updateData.roleId);
    }

    // 移除不應該更新的字段
    delete updateData._id;
    
    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    return result;
  }

  /**
   * 刪除權限分配
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 權限分配 ID
   * @returns {Object} - 刪除結果
   */
  static async deletePermission(db, id) {
    const collection = this.getCollection(db);
    return await collection.deleteOne({ _id: new ObjectId(id) });
  }

  /**
   * 批量刪除權限分配
   * @param {Object} db - MongoDB 數據庫實例
   * @param {Array} ids - 權限分配 ID 數組
   * @returns {Object} - 刪除結果
   */
  static async batchDeletePermissions(db, ids) {
    const collection = this.getCollection(db);
    const objectIds = ids.map(id => new ObjectId(id));
    return await collection.deleteMany({ _id: { $in: objectIds } });
  }
}

module.exports = Permission;
