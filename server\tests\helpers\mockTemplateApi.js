// tests/helpers/mockTemplateApi.js
/**
 * 模板 API 的模擬路由
 */
const express = require('express');

// 創建模擬路由
function createMockTemplateApi(mockCollection) {
  const router = express.Router();
  
  // 初始化資料庫連接函數
  router.initDB = jest.fn();
  
  // 設置驗證器
  let validator = {
    validateTemplate: jest.fn().mockReturnValue({ valid: true, errors: [] }),
    calculateChecksum: jest.fn().mockReturnValue('mock-checksum')
  };
  
  router.setValidator = jest.fn().mockImplementation(v => {
    validator = v;
    return router;
  });

  // 獲取所有模板
  router.get('/templates', async (req, res) => {
    try {
      const templates = await mockCollection.find().toArray();
      res.json(templates);
    } catch (error) {
      res.status(500).json({ error: '獲取模板失敗' });
    }
  });
  
  // 獲取指定 ID 的模板
  router.get('/templates/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const template = await mockCollection.findOne({ _id: id });
      
      if (!template) {
        return res.status(404).json({ error: '找不到模板' });
      }
      
      res.json(template);
    } catch (error) {
      res.status(500).json({ error: '獲取模板失敗' });
    }
  });
  
  // 創建新模板
  router.post('/templates', async (req, res) => {
    try {
      // 驗證模板
      const validationResult = validator.validateTemplate(req.body);
      if (!validationResult.valid) {
        return res.status(400).json({ valid: false, errors: validationResult.errors });
      }
      
      // 計算檢查碼
      const checksum = validator.calculateChecksum(req.body);
      
      // 創建新模板
      const templateWithChecksum = {
        ...req.body,
        checksum
      };
      
      const result = await mockCollection.insertOne(templateWithChecksum);
      res.status(201).json({ id: result.insertedId });
    } catch (error) {
      res.status(500).json({ error: '創建模板失敗' });
    }
  });
  
  // 更新模板
  router.put('/templates/:id', async (req, res) => {
    try {
      const { id } = req.params;
      
      // 驗證模板
      const validationResult = validator.validateTemplate(req.body);
      if (!validationResult.valid) {
        return res.status(400).json({ valid: false, errors: validationResult.errors });
      }
      
      // 計算新檢查碼
      const checksum = validator.calculateChecksum(req.body);
      
      // 更新模板
      const templateWithChecksum = {
        ...req.body,
        checksum
      };
      
      const result = await mockCollection.updateOne({ _id: id }, { $set: templateWithChecksum });
      
      if (result.modifiedCount === 0) {
        return res.status(404).json({ error: '找不到模板' });
      }
      
      res.json({ message: '模板更新成功' });
    } catch (error) {
      res.status(500).json({ error: '更新模板失敗' });
    }
  });
  
  // 刪除模板
  router.delete('/templates/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const result = await mockCollection.deleteOne({ _id: id });
      
      if (result.deletedCount === 0) {
        return res.status(404).json({ error: '找不到模板或刪除失敗' });
      }
      
      res.json({ message: '模板刪除成功' });
    } catch (error) {
      res.status(500).json({ error: '刪除模板失敗' });
    }
  });
  
  return router;
}

module.exports = createMockTemplateApi;
