FROM node:18-alpine

# 安裝系統依賴 (Canvas 和 Puppeteer 需要)
RUN apk add --no-cache \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    giflib-dev \
    librsvg-dev \
    pixman-dev \
    pkgconfig \
    make \
    g++ \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    curl

# 設置 Puppeteer 環境變數
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

WORKDIR /app

# 複製根目錄的 package.json (前端)
COPY package*.json ./
RUN npm ci

# 複製後端的 package.json
COPY server/package*.json ./server/
# 在容器內重新編譯 Canvas 和其他原生模組
RUN cd server && npm ci --build-from-source

# 複製所有源碼
COPY . .

# 清除可能的架構不匹配的 node_modules 並重新安裝
RUN rm -rf server/node_modules && cd server && npm ci --build-from-source

# 創建必要目錄
RUN mkdir -p server/logs server/uploads

# 暴露端口 (固定端口，避免程式碼調用錯誤)
EXPOSE 5173 3001

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3001/api/health || exit 1

# 使用原始的開發命令
CMD ["npm", "run", "dev"]