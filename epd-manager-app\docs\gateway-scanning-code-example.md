# 網關掃描功能代碼示例

以下是網關掃描功能的關鍵組件代碼示例，包括掃描頁面和添加網關頁面。

## 1. 網關掃描頁面

```jsx
// screens/gateway/GatewayScanScreen.js
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { Button, Card, Icon } from 'react-native-elements';
import GatewayScanner from '../../services/gatewayScanner';
import { COLORS } from '../../theme';

const GatewayScanScreen = () => {
  const navigation = useNavigation();
  const { token } = useSelector(state => state.auth);
  
  const [isScanning, setIsScanning] = useState(false);
  const [discoveredGateways, setDiscoveredGateways] = useState([]);
  const [error, setError] = useState(null);
  
  // 開始掃描
  const startScan = useCallback(async () => {
    try {
      setError(null);
      setIsScanning(true);
      
      // 清空之前的結果
      setDiscoveredGateways([]);
      
      // 開始掃描
      await GatewayScanner.startScan();
    } catch (err) {
      setError(err.message);
      setIsScanning(false);
      Alert.alert('掃描錯誤', err.message);
    }
  }, []);
  
  // 停止掃描
  const stopScan = useCallback(() => {
    GatewayScanner.stopScan();
    setIsScanning(false);
  }, []);
  
  // 處理網關發現事件
  const handleGatewayDiscovered = useCallback((gateway) => {
    setDiscoveredGateways(prev => [...prev, gateway]);
  }, []);
  
  // 處理掃描完成事件
  const handleScanComplete = useCallback((gateways) => {
    setIsScanning(false);
    // 可以在這裡處理掃描完成後的邏輯
  }, []);
  
  // 添加網關
  const handleAddGateway = useCallback((gateway) => {
    navigation.navigate('AddGateway', { gateway });
  }, [navigation]);
  
  // 設置事件監聽器
  useEffect(() => {
    // 添加事件監聽器
    const removeGatewayDiscoveredListener = GatewayScanner.addListener({
      gatewayDiscovered: handleGatewayDiscovered,
      scanComplete: handleScanComplete
    });
    
    // 組件掛載時自動開始掃描
    startScan();
    
    // 組件卸載時清理
    return () => {
      stopScan();
      removeGatewayDiscoveredListener();
    };
  }, [startScan, stopScan, handleGatewayDiscovered, handleScanComplete]);
  
  // 渲染網關項
  const renderGatewayItem = ({ item }) => (
    <Card containerStyle={styles.gatewayCard}>
      <Card.Title>{item.name || `Gateway-${item.macAddress.substr(-4)}`}</Card.Title>
      <Card.Divider />
      <View style={styles.gatewayInfo}>
        <Text style={styles.infoLabel}>MAC:</Text>
        <Text style={styles.infoValue}>{item.macAddress}</Text>
      </View>
      <View style={styles.gatewayInfo}>
        <Text style={styles.infoLabel}>IP:</Text>
        <Text style={styles.infoValue}>{item.ipAddress}</Text>
      </View>
      <View style={styles.gatewayInfo}>
        <Text style={styles.infoLabel}>型號:</Text>
        <Text style={styles.infoValue}>{item.model}</Text>
      </View>
      {item.firmwareVersion && (
        <View style={styles.gatewayInfo}>
          <Text style={styles.infoLabel}>固件版本:</Text>
          <Text style={styles.infoValue}>{item.firmwareVersion}</Text>
        </View>
      )}
      <Button
        title="添加到服務器"
        icon={<Icon name="add" color="white" size={20} />}
        buttonStyle={styles.addButton}
        onPress={() => handleAddGateway(item)}
      />
    </Card>
  );
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>網關掃描</Text>
        {isScanning ? (
          <TouchableOpacity style={styles.scanButton} onPress={stopScan}>
            <Text style={styles.scanButtonText}>停止掃描</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity style={styles.scanButton} onPress={startScan}>
            <Text style={styles.scanButtonText}>重新掃描</Text>
          </TouchableOpacity>
        )}
      </View>
      
      {isScanning && (
        <View style={styles.scanningContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.scanningText}>正在掃描本地網絡中的網關...</Text>
        </View>
      )}
      
      {error && (
        <View style={styles.errorContainer}>
          <Icon name="error" color={COLORS.error} size={24} />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      <Text style={styles.sectionTitle}>
        已發現的網關: {discoveredGateways.length}
      </Text>
      
      <FlatList
        data={discoveredGateways}
        renderItem={renderGatewayItem}
        keyExtractor={item => item.macAddress}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={isScanning} onRefresh={startScan} />
        }
        ListEmptyComponent={
          !isScanning && (
            <View style={styles.emptyContainer}>
              <Icon name="devices" color={COLORS.grey} size={48} />
              <Text style={styles.emptyText}>
                {discoveredGateways.length === 0
                  ? '未發現網關設備'
                  : '沒有符合條件的網關'}
              </Text>
              <Button
                title="重新掃描"
                type="outline"
                onPress={startScan}
                containerStyle={styles.emptyButton}
              />
            </View>
          )
        }
      />
      
      <View style={styles.footer}>
        <Button
          title="完成"
          type="clear"
          onPress={() => navigation.goBack()}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  scanButton: {
    padding: 8,
    borderRadius: 4,
    backgroundColor: COLORS.primary,
  },
  scanButtonText: {
    color: COLORS.white,
    fontWeight: 'bold',
  },
  scanningContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.lightBackground,
    borderRadius: 8,
    margin: 16,
  },
  scanningText: {
    marginTop: 8,
    color: COLORS.text,
    textAlign: 'center',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: COLORS.errorBackground,
    borderRadius: 8,
    margin: 16,
  },
  errorText: {
    marginLeft: 8,
    color: COLORS.error,
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    padding: 16,
    paddingBottom: 8,
    color: COLORS.text,
  },
  listContainer: {
    padding: 8,
  },
  gatewayCard: {
    borderRadius: 8,
    marginBottom: 8,
  },
  gatewayInfo: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoLabel: {
    width: 80,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  infoValue: {
    flex: 1,
    color: COLORS.text,
  },
  addButton: {
    backgroundColor: COLORS.success,
    borderRadius: 4,
    marginTop: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    marginTop: 16,
    color: COLORS.grey,
    textAlign: 'center',
  },
  emptyButton: {
    marginTop: 16,
    width: 150,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
});

export default GatewayScanScreen;
```

## 2. 添加網關頁面

```jsx
// screens/gateway/AddGatewayScreen.js
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useSelector, useDispatch } from 'react-redux';
import { Button, Input, Card, Divider, Icon } from 'react-native-elements';
import { Picker } from '@react-native-picker/picker';
import { registerGateway } from '../../api/gateway';
import { fetchStores } from '../../store/actions/storeActions';
import { addGateway } from '../../store/actions/gatewayActions';
import { COLORS } from '../../theme';

const AddGatewayScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();
  
  // 從路由參數獲取網關信息
  const { gateway } = route.params || {};
  
  // 從 Redux 獲取狀態
  const { token } = useSelector(state => state.auth);
  const { stores, loading: storesLoading } = useSelector(state => state.store);
  
  // 本地狀態
  const [name, setName] = useState(gateway?.name || `Gateway-${gateway?.macAddress.substr(-4)}`);
  const [selectedStoreId, setSelectedStoreId] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // 加載門店列表
  useEffect(() => {
    dispatch(fetchStores());
  }, [dispatch]);
  
  // 如果只有一個門店，自動選擇
  useEffect(() => {
    if (stores.length === 1) {
      setSelectedStoreId(stores[0].id);
    }
  }, [stores]);
  
  // 處理添加網關
  const handleAddGateway = async () => {
    if (!selectedStoreId) {
      Alert.alert('錯誤', '請選擇門店');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // 準備網關數據
      const gatewayData = {
        name,
        macAddress: gateway.macAddress,
        model: gateway.model,
        ipAddress: gateway.ipAddress,
        storeId: selectedStoreId,
        status: 'online',
        notes,
        firmwareVersion: gateway.firmwareVersion || '1.0.0',
      };
      
      // 調用 API 註冊網關
      const result = await registerGateway(gatewayData, token);
      
      // 更新 Redux 狀態
      dispatch(addGateway(result));
      
      // 顯示成功消息
      Alert.alert(
        '成功',
        '網關已成功添加到服務器',
        [{ text: 'OK', onPress: () => navigation.navigate('GatewayList') }]
      );
    } catch (err) {
      setError(err.message || '添加網關失敗');
      Alert.alert('錯誤', err.message || '添加網關失敗');
    } finally {
      setLoading(false);
    }
  };
  
  // 如果沒有網關信息，顯示錯誤
  if (!gateway) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>無效的網關信息</Text>
        <Button
          title="返回"
          onPress={() => navigation.goBack()}
          containerStyle={styles.buttonContainer}
        />
      </View>
    );
  }
  
  return (
    <ScrollView style={styles.container}>
      <Card containerStyle={styles.card}>
        <Card.Title>網關信息</Card.Title>
        <Card.Divider />
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>MAC:</Text>
          <Text style={styles.infoValue}>{gateway.macAddress}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>IP:</Text>
          <Text style={styles.infoValue}>{gateway.ipAddress}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>型號:</Text>
          <Text style={styles.infoValue}>{gateway.model}</Text>
        </View>
        
        {gateway.firmwareVersion && (
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>固件版本:</Text>
            <Text style={styles.infoValue}>{gateway.firmwareVersion}</Text>
          </View>
        )}
      </Card>
      
      <Card containerStyle={styles.card}>
        <Card.Title>添加到服務器</Card.Title>
        <Card.Divider />
        
        <Input
          label="網關名稱"
          value={name}
          onChangeText={setName}
          placeholder="輸入網關名稱"
          leftIcon={<Icon name="router" size={24} color={COLORS.grey} />}
        />
        
        <Text style={styles.pickerLabel}>選擇門店:</Text>
        {storesLoading ? (
          <ActivityIndicator size="small" color={COLORS.primary} />
        ) : (
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={selectedStoreId}
              onValueChange={(itemValue) => setSelectedStoreId(itemValue)}
              style={styles.picker}
            >
              <Picker.Item label="-- 請選擇門店 --" value="" />
              {stores.map(store => (
                <Picker.Item
                  key={store.id}
                  label={store.name}
                  value={store.id}
                />
              ))}
            </Picker>
          </View>
        )}
        
        <Input
          label="備註"
          value={notes}
          onChangeText={setNotes}
          placeholder="輸入備註信息（可選）"
          multiline
          numberOfLines={3}
          leftIcon={<Icon name="note" size={24} color={COLORS.grey} />}
        />
        
        {error && (
          <View style={styles.errorContainer}>
            <Icon name="error" color={COLORS.error} size={24} />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}
        
        <View style={styles.buttonRow}>
          <Button
            title="取消"
            type="outline"
            onPress={() => navigation.goBack()}
            containerStyle={[styles.buttonContainer, styles.cancelButton]}
          />
          <Button
            title="確認添加"
            onPress={handleAddGateway}
            containerStyle={styles.buttonContainer}
            loading={loading}
            disabled={loading || !selectedStoreId}
          />
        </View>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  card: {
    borderRadius: 8,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoLabel: {
    width: 80,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  infoValue: {
    flex: 1,
    color: COLORS.text,
  },
  pickerLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.grey,
    marginLeft: 10,
    marginTop: 10,
    marginBottom: 5,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 4,
    marginBottom: 16,
    marginHorizontal: 10,
  },
  picker: {
    height: 50,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: COLORS.errorBackground,
    borderRadius: 8,
    marginVertical: 16,
  },
  errorText: {
    marginLeft: 8,
    color: COLORS.error,
    flex: 1,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  buttonContainer: {
    flex: 1,
    marginHorizontal: 8,
  },
  cancelButton: {
    borderColor: COLORS.grey,
  },
});

export default AddGatewayScreen;
```

## 3. 導航配置

將掃描頁面添加到導航中：

```jsx
// navigation/GatewayNavigator.js
import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import GatewayListScreen from '../screens/gateway/GatewayListScreen';
import GatewayDetailScreen from '../screens/gateway/GatewayDetailScreen';
import GatewayScanScreen from '../screens/gateway/GatewayScanScreen';
import AddGatewayScreen from '../screens/gateway/AddGatewayScreen';

const Stack = createNativeStackNavigator();

const GatewayNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="GatewayList" component={GatewayListScreen} />
      <Stack.Screen name="GatewayDetail" component={GatewayDetailScreen} />
      <Stack.Screen name="GatewayScan" component={GatewayScanScreen} />
      <Stack.Screen name="AddGateway" component={AddGatewayScreen} />
    </Stack.Navigator>
  );
};

export default GatewayNavigator;
```

## 4. 在網關列表頁面添加掃描按鈕

```jsx
// screens/gateway/GatewayListScreen.js (部分代碼)
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, Icon } from 'react-native-elements';
import { useNavigation } from '@react-navigation/native';

// ... 其他代碼 ...

const GatewayListScreen = () => {
  const navigation = useNavigation();
  
  // ... 其他代碼 ...
  
  return (
    <View style={styles.container}>
      {/* ... 其他 UI 元素 ... */}
      
      <View style={styles.buttonContainer}>
        <Button
          title="掃描本地網關"
          icon={<Icon name="wifi-tethering" color="white" size={24} />}
          buttonStyle={styles.scanButton}
          onPress={() => navigation.navigate('GatewayScan')}
        />
        <Button
          title="添加網關"
          icon={<Icon name="add" color="white" size={24} />}
          buttonStyle={styles.addButton}
          onPress={() => navigation.navigate('AddGateway', { isManualAdd: true })}
        />
      </View>
      
      {/* ... 其他 UI 元素 ... */}
    </View>
  );
};

const styles = StyleSheet.create({
  // ... 其他樣式 ...
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 16,
  },
  scanButton: {
    backgroundColor: COLORS.info,
    paddingHorizontal: 16,
  },
  addButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 16,
  },
});

export default GatewayListScreen;
```

## 5. Redux 操作

添加相關的 Redux 操作：

```jsx
// store/slices/gatewaySlice.js (部分代碼)
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  gateways: [],
  loading: false,
  error: null,
};

const gatewaySlice = createSlice({
  name: 'gateway',
  initialState,
  reducers: {
    // ... 其他 reducers ...
    
    addGatewayStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    addGatewaySuccess: (state, action) => {
      state.loading = false;
      state.gateways.push(action.payload);
    },
    addGatewayFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
  },
});

export const {
  // ... 其他 actions ...
  addGatewayStart,
  addGatewaySuccess,
  addGatewayFailure,
} = gatewaySlice.actions;

export default gatewaySlice.reducer;
```

```jsx
// store/actions/gatewayActions.js (部分代碼)
import { registerGateway } from '../../api/gateway';
import {
  // ... 其他 actions ...
  addGatewayStart,
  addGatewaySuccess,
  addGatewayFailure,
} from '../slices/gatewaySlice';

// ... 其他 action creators ...

export const addGateway = (gatewayData) => async (dispatch, getState) => {
  try {
    dispatch(addGatewayStart());
    
    const { token } = getState().auth;
    const result = await registerGateway(gatewayData, token);
    
    dispatch(addGatewaySuccess(result));
    return result;
  } catch (error) {
    dispatch(addGatewayFailure(error.message));
    throw error;
  }
};
```

這些代碼示例展示了如何實現網關掃描功能的用戶界面和業務邏輯。您可以根據實際需求進行調整和擴展。
