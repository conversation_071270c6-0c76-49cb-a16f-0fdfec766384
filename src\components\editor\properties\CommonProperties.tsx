import React from 'react';
import { TemplateElement } from '../../../types';
import { FormField, NumberInput, CompactFormField, InlineFormGroup } from './FormComponents';

interface CommonPropertiesProps {
  element: TemplateElement;
  updateElement: (updates: Partial<TemplateElement>) => void;
}

export const CommonProperties: React.FC<CommonPropertiesProps> = ({ element, updateElement }) => (
  <div className="mb-4 space-y-2">
    {/* 位置設定 - X 和 Y 在同一行 */}
    <div className="flex items-center justify-between">
      <span className="text-xs text-gray-400 mr-2 flex-shrink-0">位置</span>
      <div className="flex items-center gap-1">
        <NumberInput
          value={element.x}
          onChange={(value) => updateElement({ x: value })}
          step={1}
        />
        <span className="text-xs text-gray-500">×</span>
        <NumberInput
          value={element.y}
          onChange={(value) => updateElement({ y: value })}
          step={1}
        />
        <span className="text-xs text-gray-400 ml-1">px</span>
      </div>
    </div>

    {/* 大小設定 - 寬度和高度在同一行 */}
    <div className="flex items-center justify-between">
      <span className="text-xs text-gray-400 mr-2 flex-shrink-0">大小</span>
      <div className="flex items-center gap-1">
        <NumberInput
          value={element.width}
          onChange={(value) => updateElement({ width: value })}
          min={1}
          step={1}
        />
        <span className="text-xs text-gray-500">×</span>
        <NumberInput
          value={element.height}
          onChange={(value) => updateElement({ height: value })}
          min={1}
          step={1}
        />
        <span className="text-xs text-gray-400 ml-1">px</span>
      </div>
    </div>

    {/* 旋轉角度 - 獨立一行 */}
    {element.rotation !== undefined && (
      <FormField label="旋轉角度">
        <NumberInput
          value={element.rotation}
          onChange={(value) => updateElement({ rotation: value })}
          unit="°"
          step={1}
          min={-360}
          max={360}
        />
      </FormField>
    )}
  </div>
);