# 跨平台(RN + Chrome Extension)架構設計

## 1. 分層架構設計

```mermaid
flowchart TD
    A[UI層] --> B[業務邏輯層]
    B --> C[數據管理層]
    C --> D[存儲適配層]
    D --> E1[React Native 存儲]
    D --> E2[Chrome Storage]
```

## 2. 核心架構調整

### 2.1 存儲適配層設計
```typescript
// storage.adapter.ts
interface StorageAdapter {
  setItem(key: string, value: any): Promise<void>;
  getItem(key: string): Promise<any>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
}

// React Native 實現
class RNStorageAdapter implements StorageAdapter {
  async setItem(key: string, value: any) {
    await AsyncStorage.setItem(key, JSON.stringify(value));
  }
  
  async getItem(key: string) {
    const data = await AsyncStorage.getItem(key);
    return data ? JSON.parse(data) : null;
  }
  
  // ... 其他方法實現
}

// Chrome Extension 實現
class ChromeStorageAdapter implements StorageAdapter {
  async setItem(key: string, value: any) {
    await chrome.storage.local.set({ [key]: value });
  }
  
  async getItem(key: string) {
    const result = await chrome.storage.local.get(key);
    return result[key];
  }
  
  // ... 其他方法實現
}
```

### 2.2 統一狀態管理
```typescript
// store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import { getPlatformStorage } from './storage';

const storage = getPlatformStorage();

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'settings', 'offlineData']
};

export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware({
    serializableCheck: {
      ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
    },
  }),
});
```

### 2.3 平台特定功能封裝
```typescript
// platform/index.ts
interface PlatformFeatures {
  storage: StorageAdapter;
  network: NetworkAdapter;
  notification: NotificationAdapter;
}

// React Native 實現
class RNPlatform implements PlatformFeatures {
  storage = new RNStorageAdapter();
  network = new RNNetworkAdapter();
  notification = new RNNotificationAdapter();
}

// Chrome Extension 實現
class ChromePlatform implements PlatformFeatures {
  storage = new ChromeStorageAdapter();
  network = new ChromeNetworkAdapter();
  notification = new ChromeNotificationAdapter();
}
```

## 3. 同步策略調整

### 3.1 數據同步流程
```typescript
// sync/manager.ts
class SyncManager {
  constructor(private platform: PlatformFeatures) {}

  async sync() {
    const pendingOps = await this.platform.storage.getItem('pendingOperations');
    
    if (this.platform.network.isOnline()) {
      try {
        await this.syncWithServer(pendingOps);
        await this.platform.storage.removeItem('pendingOperations');
      } catch (error) {
        this.platform.notification.show('同步失敗');
      }
    }
  }
}
```

### 3.2 離線數據處理
```typescript
// offline/manager.ts
class OfflineManager {
  constructor(private platform: PlatformFeatures) {}

  async handleOfflineOperation(operation: Operation) {
    // 存儲操作
    const pendingOps = await this.platform.storage.getItem('pendingOperations') || [];
    pendingOps.push(operation);
    await this.platform.storage.setItem('pendingOperations', pendingOps);

    // 更新本地狀態
    store.dispatch(updateLocalState(operation));
  }
}
```

## 4. 專案結構調整

```
src/
├── core/                 # 核心邏輯（跨平台共用）
│   ├── api/
│   ├── store/
│   └── types/
├── platforms/           # 平台特定實現
│   ├── react-native/
│   └── chrome/
├── features/           # 業務功能模組
│   ├── auth/
│   ├── sync/
│   └── offline/
└── ui/                 # UI 組件
    ├── react-native/
    └── chrome/
```

## 5. 主要調整點

### 5.1 存儲機制
- React Native: AsyncStorage
- Chrome Extension: chrome.storage.local
- 統一通過 StorageAdapter 介面訪問

### 5.2 網絡處理
- React Native: NetInfo
- Chrome Extension: navigator.online
- 統一通過 NetworkAdapter 介面處理

### 5.3 通知機制
- React Native: 原生通知
- Chrome Extension: chrome.notifications
- 統一通過 NotificationAdapter 介面實現

### 5.4 UI 適配
- 使用平台特定的 UI 組件
- 共用業務邏輯和數據流

## 6. 開發注意事項

1. **代碼組織**
   - 清晰分離平台特定代碼
   - 最大化共用業務邏輯
   - 使用依賴注入管理平台差異

2. **數據存儲**
   - Chrome Extension 存儲限制為 5MB
   - 需要實現數據清理機制
   - 考慮使用 IndexedDB 存儲大量數據

3. **同步策略**
   - Chrome Extension 後台同步
   - React Native 前台同步
   - 需要處理不同平台的同步時機

4. **錯誤處理**
   - 平台特定錯誤碼轉換
   - 統一錯誤處理機制
   - 適配不同平台的錯誤展示

5. **測試策略**
   - 單元測試使用通用邏輯
   - 為平台特定代碼編寫專門測試
   - 端到端測試需要覆蓋兩個平台

## 7. 部署考慮

### 7.1 構建配置
```javascript
// webpack.config.js (Chrome Extension)
module.exports = {
  entry: {
    background: './src/platforms/chrome/background.ts',
    content: './src/platforms/chrome/content.ts',
    popup: './src/platforms/chrome/popup.tsx'
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].js'
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js'],
    alias: {
      '@core': path.resolve(__dirname, 'src/core'),
      '@platforms': path.resolve(__dirname, 'src/platforms')
    }
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/
      }
    ]
  },
  plugins: [
    new CopyWebpackPlugin({
      patterns: [
        { from: 'public', to: '.' },
        { from: 'manifest.json', to: '.' }
      ]
    })
  ]
};

// metro.config.js (React Native)
module.exports = {
  resolver: {
    platforms: ['ios', 'android']
  },
  // ...其他配置
};
```

### 7.2 環境變數
```typescript
// env.ts
export const ENV = {
  PLATFORM: process.env.PLATFORM,
  API_URL: process.env.API_URL,
  STORAGE_PREFIX: process.env.STORAGE_PREFIX
};

// Chrome Extension 特定配置
export const CHROME_ENV = {
  EXTENSION_ID: process.env.EXTENSION_ID,
  CONTENT_SECURITY_POLICY: process.env.CONTENT_SECURITY_POLICY,
  PERMISSIONS: [
    'storage',
    'tabs',
    'notifications',
    'http://*/*',
    'https://*/*'
  ]
};
```

### 7.3 打包與發布流程

#### Chrome Extension
1. **開發環境**
   ```bash
   # 開發構建
   npm run build:chrome:dev
   
   # 載入未打包的擴展
   - Chrome 開發者模式
   - 載入未打包的擴展
   - 選擇 dist 目錄
   ```

2. **生產環境**
   ```bash
   # 生產構建
   npm run build:chrome:prod
   
   # 打包擴展
   - 壓縮 dist 目錄
   - 生成 .zip 文件
   ```

3. **發布流程**
   - Chrome Web Store 開發者後台上傳
   - 版本號管理
   - 更新說明撰寫
   - 提交審核

#### React Native
1. **開發環境**
   ```bash
   # 開發運行
   expo start
   ```

2. **生產環境**
   ```bash
   # 使用 EAS Build
   eas build --platform all
   ```

### 7.4 版本控制策略

#### Chrome Extension
1. **版本號管理**
   ```json
   // manifest.json
   {
     "manifest_version": 3,
     "version": "1.0.0",
     "version_name": "1.0.0-beta"
   }
   ```

2. **更新機制**
   - 自動更新檢查
   - 強制更新策略
   - 增量更新支持

#### React Native
1. **版本號管理**
   ```json
   // app.json
   {
     "expo": {
       "version": "1.0.0",
       "android": {
         "versionCode": 1
       },
       "ios": {
         "buildNumber": "1"
       }
     }
   }
   ```

### 7.5 部署檢查清單

#### Chrome Extension
- [ ] manifest.json 配置檢查
- [ ] 權限聲明審查
- [ ] 內容安全策略設置
- [ ] 背景腳本優化
- [ ] 存儲使用限制檢查
- [ ] 跨域請求配置
- [ ] 性能指標檢測

#### React Native
- [ ] 原生模組配置
- [ ] 應用圖標資源
- [ ] 啟動屏幕配置
- [ ] 權限聲明檢查
- [ ] 版本號更新
- [ ] 打包配置驗證

## 8. 性能優化

1. **存儲優化**
   - 實現存儲大小監控
   - 定期清理過期數據
   - 使用壓縮算法減少存儲空間

2. **網絡優化**
   - 實現請求隊列
   - 批量同步策略
   - 網絡狀態智能檢測

3. **渲染優化**
   - 平台特定的性能優化
   - 共用組件的按需加載
   - 狀態管理的選擇性訂閱
