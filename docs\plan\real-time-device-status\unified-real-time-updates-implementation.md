# 統一即時更新功能實現總結

## 概述

本文檔記錄了將所有頁面的自動刷新功能統一改為 WebSocket 即時更新，並保持選取狀態的完整實現過程。

## 問題背景

原本系統中存在不一致的更新邏輯：
- **設備頁面** 和 **網關頁面**：使用 WebSocket 即時更新
- **門店資料頁面**、**系統資料頁面**、**模板列表頁面**：使用定時器自動刷新（30秒間隔）

當啟動自動刷新後，會導致使用者勾選欄位狀態全部被取消，影響用戶體驗。

## 解決方案

### 1. WebSocket 客戶端擴展

**檔案**：`src/utils/websocketClient.ts`

#### 新增事件類型

```typescript
// 門店資料更新事件
export interface StoreDataUpdateEvent {
  type: 'store_data_update';
  storeId: string;
  storeData: Array<{
    uid: string;
    data: Record<string, any>;
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'create' | 'update' | 'delete';
}

// 系統資料更新事件
export interface SystemDataUpdateEvent {
  type: 'system_data_update';
  systemData: Array<{
    uid: string;
    data: Record<string, any>;
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'create' | 'update' | 'delete';
}

// 模板更新事件
export interface TemplateUpdateEvent {
  type: 'template_update';
  storeId?: string;
  templates: Array<{
    id: string;
    name: string;
    isSystemTemplate: boolean;
    storeId?: string;
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'create' | 'update' | 'delete';
}
```

#### 新增訂閱方法

```typescript
// 門店資料更新訂閱
public subscribeStoreDataUpdate(storeId: string, options?: any)
public unsubscribeStoreDataUpdate(storeId: string)

// 系統資料更新訂閱
public subscribeSystemDataUpdate(options?: any)
public unsubscribeSystemDataUpdate()

// 模板更新訂閱
public subscribeTemplateUpdate(storeId?: string, options?: any)
public unsubscribeTemplateUpdate(storeId?: string)
```

#### 便捷訂閱函數

```typescript
export const subscribeToStoreDataUpdate = (storeId: string, handler: StoreDataUpdateEventHandler, options?: any): (() => void)
export const subscribeToSystemDataUpdate = (handler: SystemDataUpdateEventHandler, options?: any): (() => void)
export const subscribeToTemplateUpdate = (storeId: string | undefined, handler: TemplateUpdateEventHandler, options?: any): (() => void)
```

### 2. 頁面實現統一

#### DatabasePage.tsx - 門店資料頁面

**主要變更**：
- 移除定時器自動刷新邏輯
- 改用 `subscribeToStoreDataUpdate` WebSocket 即時更新
- 實現選取狀態保持功能
- 更新UI狀態指示器

**核心邏輯**：
```typescript
// 門店資料即時更新Hook
useEffect(() => {
  if (!store?.id || !isRealTimeEnabled) return;

  const handleStoreDataUpdate = (event: StoreDataUpdateEvent) => {
    if (event.storeId !== store.id) return;

    if (event.updateType === 'delete') {
      // 刪除操作：從本地狀態中移除
      const deletedUids = event.storeData.map(item => item.uid);
      setStoreData(prevData => prevData.filter(item => !deletedUids.includes(item.uid)));
      setSelectedItems(prevSelected => prevSelected.filter(uid => !deletedUids.includes(uid)));
    } else {
      // 創建或更新操作：重新獲取數據並保持選取狀態
      fetchDataWithSelectionPreservation();
    }
  };

  const unsubscribe = subscribeToStoreDataUpdate(store.id, handleStoreDataUpdate);
  return unsubscribe;
}, [store?.id, isRealTimeEnabled]);
```

#### SystemSpecificDataPage.tsx - 系統資料頁面

**主要變更**：
- 移除定時器自動刷新邏輯
- 改用 `subscribeToSystemDataUpdate` WebSocket 即時更新
- 實現選取狀態保持功能
- 更新UI狀態指示器

**核心邏輯**：
```typescript
// 系統資料即時更新Hook
useEffect(() => {
  if (!isRealTimeEnabled) return;

  const handleSystemDataUpdate = (event: SystemDataUpdateEvent) => {
    if (event.updateType === 'delete') {
      // 刪除操作：從本地狀態中移除並重新計算SN
      const deletedUids = event.systemData.map(item => item.uid);
      setSystemSpecificData(prevData => {
        const filteredData = prevData.filter(item => !deletedUids.includes(item.uid));
        return filteredData.map((item, index) => ({ ...item, sn: index + 1 }));
      });
      setSelectedItems(prevSelected => prevSelected.filter(uid => !deletedUids.includes(uid)));
    } else {
      fetchDataWithSelectionPreservation();
    }
  };

  const unsubscribe = subscribeToSystemDataUpdate(handleSystemDataUpdate);
  return unsubscribe;
}, [isRealTimeEnabled]);
```

#### TemplateList.tsx - 模板列表頁面

**主要變更**：
- 移除定時器自動刷新邏輯
- 改用 `subscribeToTemplateUpdate` WebSocket 即時更新
- 實現選取狀態保持功能（使用Zustand store）
- 更新UI狀態指示器

**核心邏輯**：
```typescript
// 模板即時更新Hook
useEffect(() => {
  if (!isRealTimeEnabled) return;

  const handleTemplateUpdate = (event: TemplateUpdateEvent) => {
    if (event.storeId && store?.id && event.storeId !== store.id) return;

    if (event.updateType === 'delete') {
      // 刪除操作：從本地狀態中移除
      const deletedIds = event.templates.map(template => template.id);
      deletedIds.forEach(id => deleteTemplate(id));
      
      // 清理選取狀態
      const currentSelectedIds = [...selectedTemplateIds];
      const validSelectedIds = currentSelectedIds.filter(id => !deletedIds.includes(id));
      if (validSelectedIds.length !== currentSelectedIds.length) {
        selectAllTemplates(false);
        if (validSelectedIds.length > 0) {
          selectAllTemplates(true, validSelectedIds);
        }
      }
    } else {
      loadTemplatesFromServerWithSelectionPreservation();
    }
  };

  const unsubscribe = subscribeToTemplateUpdate(store?.id, handleTemplateUpdate);
  return unsubscribe;
}, [isRealTimeEnabled, store?.id]);
```

### 3. 選取狀態保持機制

#### 核心原理

1. **純前端解決方案**：選取狀態只存在於前端記憶體中
2. **智能狀態檢查**：在數據更新時檢查已選取項目是否還存在
3. **自動清理**：移除已刪除項目的選取狀態
4. **全選狀態同步**：自動更新全選checkbox狀態

#### 實現範例

```typescript
// 檢查選取項目是否還存在，保持有效的選取狀態
if (selectedItems.length > 0) {
  const existingUids = updatedData.map(item => item.uid).filter(Boolean) as string[];
  const validSelectedItems = selectedItems.filter(uid => existingUids.includes(uid));
  
  if (validSelectedItems.length !== selectedItems.length) {
    console.log(`清理無效選取項目: ${selectedItems.length} -> ${validSelectedItems.length}`);
    setSelectedItems(validSelectedItems);
    
    // 更新全選狀態
    const allSelected = updatedData.length > 0 && 
      updatedData.every(item => item.uid && validSelectedItems.includes(item.uid));
    setSelectAll(allSelected);
  }
}
```

## 實現結果

### ✅ 統一的即時更新功能

1. **一致的更新邏輯**：所有頁面都使用 WebSocket 即時更新
2. **智能選取狀態保持**：自動檢查已選取項目是否還存在
3. **自動清理**：移除已刪除項目的選取狀態
4. **用戶控制**：每個頁面都有即時更新開關
5. **視覺指示器**：清楚顯示即時更新狀態

### ✅ 頁面覆蓋

- **DevicesPage.tsx** - 設備管理頁面 ✅
- **GatewaysPage.tsx** - 網關管理頁面 ✅
- **DatabasePage.tsx** - 門店資料頁面 ✅
- **SystemSpecificDataPage.tsx** - 系統資料頁面 ✅
- **TemplateList.tsx** - 模板列表頁面 ✅

### ✅ 技術特點

- **事件驅動**：所有頁面都監聽對應的 WebSocket 事件
- **選取狀態保持**：在數據更新時智能保持有效的選取項目
- **性能優化**：只在有變化時更新選取狀態
- **門店關聯**：支援按門店過濾更新事件
- **錯誤處理**：即時更新失敗時不干擾用戶操作

## 後端需求

為了完整實現此功能，後端需要實現以下 WebSocket 事件推送：

### 1. 門店資料更新事件

```javascript
// 當門店資料發生變更時推送
{
  type: 'store_data_update',
  storeId: 'store_id_here',
  storeData: [
    {
      uid: 'data_uid',
      data: { /* 更新的資料 */ },
      updatedFields: ['field1', 'field2']
    }
  ],
  timestamp: '2024-01-01T00:00:00.000Z',
  updateType: 'create' | 'update' | 'delete'
}
```

### 2. 系統資料更新事件

```javascript
// 當系統專屬資料發生變更時推送
{
  type: 'system_data_update',
  systemData: [
    {
      uid: 'data_uid',
      data: { /* 更新的資料 */ },
      updatedFields: ['field1', 'field2']
    }
  ],
  timestamp: '2024-01-01T00:00:00.000Z',
  updateType: 'create' | 'update' | 'delete'
}
```

### 3. 模板更新事件

```javascript
// 當模板發生變更時推送
{
  type: 'template_update',
  storeId: 'store_id_here', // 可選，系統模板時為空
  templates: [
    {
      id: 'template_id',
      name: 'template_name',
      isSystemTemplate: true,
      storeId: 'store_id_here',
      updatedFields: ['name', 'elements']
    }
  ],
  timestamp: '2024-01-01T00:00:00.000Z',
  updateType: 'create' | 'update' | 'delete'
}
```

## 總結

此次實現成功統一了所有頁面的即時更新邏輯，解決了自動刷新導致選取狀態丟失的問題。所有頁面現在都使用一致的 WebSocket 即時更新機制，並智能保持使用者的選取狀態，大幅提升了用戶體驗。

**實施日期**：2024年12月
**狀態**：前端實現完成，等待後端 WebSocket 事件推送實現
