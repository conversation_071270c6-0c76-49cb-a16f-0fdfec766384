import { DisplayColorType } from '../../../types';

export interface ColorRGB {
  r: number;
  g: number;
  b: number;
}

/**
 * 獲取預設調色板（與預覽圖生成時使用的相同）
 */
export function getColorPalette(colorType: DisplayColorType): ColorRGB[] {
  switch (colorType) {
    case DisplayColorType.BWR: // "Black & White & Red"
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 }      // 紅色
      ];

    case DisplayColorType.BWRY: // "Black & White & Red & Yellow"
      return [
        { r: 0, g: 0, b: 0 },       // 黑色
        { r: 255, g: 255, b: 255 }, // 白色
        { r: 255, g: 0, b: 0 },     // 紅色
        { r: 255, g: 255, b: 0 }    // 黃色
      ];

    case DisplayColorType.BW: // "Gray16"
    default:
      // 16級灰度調色板
      const grayPalette: ColorRGB[] = [];
      for (let i = 0; i < 16; i++) {
        const grayValue = Math.round((255 / 15) * i);
        grayPalette.push({ r: grayValue, g: grayValue, b: grayValue });
      }
      return grayPalette;
  }
}

/**
 * 精確顏色比對（與預覽圖生成時使用的相同邏輯）
 */
export function findExactColorMatch(r: number, g: number, b: number, palette: ColorRGB[]): ColorRGB {
  // 直接比對 RGB 值，找到完全匹配的顏色
  for (const color of palette) {
    if (color.r === r && color.g === g && color.b === b) {
      return color;
    }
  }
  
  // 如果沒有完全匹配，說明預覽圖生成有問題，記錄警告
  console.warn(`EPD轉換: 未找到精確匹配的顏色: RGB(${r}, ${g}, ${b})，調色板:`, palette);
  
  // 返回最接近的顏色作為備用
  return findClosestColor(r, g, b, palette);
}

/**
 * 備用的最接近顏色查找（僅在精確匹配失敗時使用）
 */
function findClosestColor(r: number, g: number, b: number, palette: ColorRGB[]): ColorRGB {
  let minDistance = Infinity;
  let closestColor = palette[0];

  for (const color of palette) {
    const distance = Math.sqrt(
      Math.pow(r - color.r, 2) +
      Math.pow(g - color.g, 2) +
      Math.pow(b - color.b, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      closestColor = color;
    }
  }

  console.warn(`EPD轉換: 使用最接近的顏色: RGB(${closestColor.r}, ${closestColor.g}, ${closestColor.b})`);
  return closestColor;
}

/**
 * 獲取顏色在調色板中的索引
 */
export function getColorIndex(color: ColorRGB, palette: ColorRGB[]): number {
  for (let i = 0; i < palette.length; i++) {
    if (palette[i].r === color.r && palette[i].g === color.g && palette[i].b === color.b) {
      return i;
    }
  }
  return 0; // 默認返回第一個顏色的索引
}
