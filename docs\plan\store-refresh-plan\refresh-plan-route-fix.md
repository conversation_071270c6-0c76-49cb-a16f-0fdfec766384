# 刷圖計畫路由衝突修復報告

## 問題描述

在刷圖計畫系統中發現了一個嚴重的路由衝突問題，導致調度器狀態API無法正常工作。

### 錯誤信息
```
BSONError: input must be a 24 character hex string, 12 byte Uint8Array, or an integer
    at new ObjectId (C:\Users\<USER>\Desktop\code\git\epd-manager-lite\server\node_modules\bson\lib\bson.cjs:2523:23)
    at C:\Users\<USER>\Desktop\code\git\epd-manager-lite\server\routes\refreshPlanApi.js:187:12
```

### 問題原因

Express.js 路由匹配是按照定義順序進行的，當前端請求 `/stores/KH001/refresh-plans/scheduler-status` 時：

1. **錯誤的匹配**：Express 首先匹配到了 `/stores/:storeId/refresh-plans/:planId` 路由
2. **參數錯誤解析**：將 `scheduler-status` 當作 `planId` 參數
3. **ObjectId 轉換失敗**：嘗試將 `scheduler-status` 轉換為 MongoDB ObjectId 時失敗

### 原始路由順序（有問題）
```javascript
// 這個通用路由會匹配所有 /stores/:storeId/refresh-plans/:planId 格式的請求
router.get('/stores/:storeId/refresh-plans/:planId', ...)

// 這些具體路由永遠不會被匹配到，因為上面的路由已經捕獲了請求
router.get('/stores/:storeId/refresh-plans/statistics', ...)
router.get('/stores/:storeId/refresh-plans/scheduler-status', ...)
```

## 修復方案

### 1. 調整路由順序

將具體的路由放在通用的參數化路由之前：

```javascript
// 具體路由必須在參數化路由之前定義
router.get('/stores/:storeId/refresh-plans/statistics', ...)
router.get('/stores/:storeId/refresh-plans/scheduler-status', ...)

// 通用的參數化路由放在最後
router.get('/stores/:storeId/refresh-plans/:planId', ...)
```

### 2. 添加 ObjectId 驗證

為所有使用 ObjectId 的路由添加參數驗證：

```javascript
router.get('/stores/:storeId/refresh-plans/:planId', async (req, res) => {
  try {
    const { storeId, planId } = req.params;
    
    // 驗證 planId 是否為有效的 ObjectId
    if (!ObjectId.isValid(planId)) {
      return res.status(400).json({ success: false, error: '無效的計畫ID格式' });
    }
    
    // ... 其餘邏輯
  } catch (error) {
    // ... 錯誤處理
  }
});
```

## 修復後的路由結構

### 正確的路由順序
```javascript
// 1. 具體的統計路由
router.get('/stores/:storeId/refresh-plans/statistics', ...)

// 2. 具體的調度器狀態路由  
router.get('/stores/:storeId/refresh-plans/scheduler-status', ...)

// 3. 通用的計畫ID路由（帶驗證）
router.get('/stores/:storeId/refresh-plans/:planId', ...)

// 4. 其他具體路由
router.get('/stores/:storeId/refresh-plans/:planId/executions', ...)
router.post('/stores/:storeId/refresh-plans/:planId/execute', ...)
```

### 添加的驗證

為以下路由添加了 ObjectId 驗證：
- `GET /stores/:storeId/refresh-plans/:planId`
- `PUT /stores/:storeId/refresh-plans/:planId`
- `DELETE /stores/:storeId/refresh-plans/:planId`
- `POST /stores/:storeId/refresh-plans/:planId/execute`
- `GET /stores/:storeId/refresh-plans/:planId/executions`
- `GET /stores/:storeId/refresh-plans/executions/:executionId`

## 測試驗證

### 修復前
```bash
# 這個請求會錯誤地匹配到 /:planId 路由
GET /api/stores/KH001/refresh-plans/scheduler-status
# 結果：BSONError - 無法將 "scheduler-status" 轉換為 ObjectId
```

### 修復後
```bash
# 這個請求會正確匹配到 /scheduler-status 路由
GET /api/stores/KH001/refresh-plans/scheduler-status
# 結果：正常返回調度器狀態數據

# 無效的計畫ID會返回友好的錯誤信息
GET /api/stores/KH001/refresh-plans/invalid-id
# 結果：400 Bad Request - "無效的計畫ID格式"
```

## 預防措施

### 1. 路由設計原則
- **具體路由優先**：將具體的路由放在參數化路由之前
- **參數驗證**：對所有參數進行格式驗證
- **錯誤處理**：提供友好的錯誤信息

### 2. 開發規範
- 新增路由時檢查是否與現有路由衝突
- 使用 ObjectId 的地方必須添加驗證
- 定期檢查路由順序的合理性

### 3. 測試建議
- 測試所有API端點的正常功能
- 測試無效參數的錯誤處理
- 測試路由匹配的正確性

## 影響範圍

### 修復的功能
- ✅ 調度器狀態API正常工作
- ✅ 統計資料API正常工作  
- ✅ 計畫詳情API有更好的錯誤處理
- ✅ 所有ObjectId相關API有參數驗證

### 不受影響的功能
- ✅ 計畫列表API
- ✅ 計畫創建API
- ✅ 其他非參數化路由

## 總結

這次修復解決了一個典型的Express.js路由順序問題，通過調整路由定義順序和添加參數驗證，確保了：

1. **路由正確匹配**：具體路由不會被通用路由誤捕獲
2. **錯誤處理改善**：無效參數會返回友好的錯誤信息
3. **系統穩定性提升**：避免了因參數格式錯誤導致的服務器錯誤

這個修復確保了刷圖計畫系統的所有API端點都能正常工作，特別是新增的調度器狀態監控功能。
