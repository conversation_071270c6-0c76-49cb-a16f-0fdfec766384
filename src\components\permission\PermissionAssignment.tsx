import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../../store/authStore';
import { PermissionList } from './PermissionList';
import { PermissionForm } from './PermissionForm';
import { PermissionBatchForm } from './PermissionBatchForm';
import { Button } from '../ui/button';
import { Plus } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { getAllPermissions, deletePermission, batchDeletePermissions } from '../../utils/api/permissionApi';

// 權限分配接口
export interface PermissionAssignment {
  _id: string;
  userId: string;
  roleId: string;
  scope: string;
  scopeType: 'system' | 'store';
  createdAt: string;
  updatedAt: string;
  user?: {
    username: string;
    name: string;
    email: string;
    phone: string;
    status: string;
  };
  role?: {
    name: string;
    description: string;
    type: string;
  };
}

export const PermissionAssignment: React.FC = () => {
  const { t } = useTranslation();
  const { token } = useAuthStore();

  // 狀態
  const [permissions, setPermissions] = useState<PermissionAssignment[]>([]);
  const [loading, setLoading] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isBatchDialogOpen, setIsBatchDialogOpen] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0
  });

  // 獲取權限分配列表
  const fetchPermissions = async () => {
    try {
      setLoading(true);

      const params = {
        page: pagination.page,
        limit: pagination.limit
      };

      const data = await getAllPermissions(params);
      setPermissions(data.permissions);
      setPagination(data.pagination);
    } catch (error) {
      console.error('獲取權限分配列表錯誤:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchPermissions();
  }, [token, pagination.page, pagination.limit]);

  // 處理分頁
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  // 處理刪除權限分配
  const handleDeletePermission = async (id: string) => {
    try {
      setLoading(true);

      await deletePermission(id);

      // 重新獲取權限分配列表
      await fetchPermissions();
    } catch (error) {
      console.error('刪除權限分配錯誤:', error);
    } finally {
      setLoading(false);
    }
  };

  // 處理批量刪除權限分配
  const handleBatchDelete = async (ids: string[]) => {
    try {
      setLoading(true);

      await batchDeletePermissions(ids);

      // 重新獲取權限分配列表
      await fetchPermissions();
    } catch (error) {
      console.error('批量刪除權限分配錯誤:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* 工具欄 */}
      <div className="flex justify-end items-center mb-4 space-x-2">
        <Button
          variant="outline"
          onClick={() => setIsBatchDialogOpen(true)}
        >
          {t('permission.batchAdd')}
        </Button>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {t('permission.addPermission')}
        </Button>
      </div>

      {/* 權限分配列表 */}
      <PermissionList
        permissions={permissions}
        loading={loading}
        pagination={pagination}
        onPageChange={handlePageChange}
        onDelete={handleDeletePermission}
        onBatchDelete={handleBatchDelete}
      />

      {/* 添加權限分配對話框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-2">
            <DialogTitle className="text-lg">新增</DialogTitle>
          </DialogHeader>
          <PermissionForm
            onClose={() => setIsAddDialogOpen(false)}
            onSuccess={() => {
              setIsAddDialogOpen(false);
              fetchPermissions();
            }}
          />
        </DialogContent>
      </Dialog>

      {/* 批量添加權限分配對話框 */}
      <Dialog open={isBatchDialogOpen} onOpenChange={setIsBatchDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{t('permission.batchAdd')}</DialogTitle>
          </DialogHeader>
          <PermissionBatchForm
            onClose={() => setIsBatchDialogOpen(false)}
            onSuccess={() => {
              setIsBatchDialogOpen(false);
              fetchPermissions();
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};
