# 門店刷圖計畫系統 - 用戶界面設計

## 整體設計原則

### 1. 設計理念
- **簡潔直觀**：界面簡潔明了，操作流程清晰
- **一致性**：與現有系統保持視覺和交互一致性
- **響應式**：支援不同螢幕尺寸的設備
- **可訪問性**：符合無障礙設計標準

### 2. 視覺風格
- 延續現有系統的設計語言
- 使用淺灰色背景和玻璃效果
- 支援深色/淺色主題切換
- 採用卡片式佈局和圓角設計

### 3. 交互設計
- 提供清晰的操作反饋
- 使用漸進式披露減少認知負擔
- 支援鍵盤導航和快捷鍵
- 實現平滑的動畫過渡效果

## 頁面結構設計

### 1. 門店設置頁面整合

```
門店設置
├── 標籤群組
├── 參數設定
├── 刷圖計畫 ← 新增標籤頁
├── 模板策略
└── 素材管理
```

#### 刷圖計畫標籤頁佈局
```
┌─────────────────────────────────────────────────────────────┐
│ 刷圖計畫                                    [+ 新增計畫]    │
├─────────────────────────────────────────────────────────────┤
│ 🔍 搜索計畫...        📊 統計  ⚙️ 設定  🔄 刷新           │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │   計畫卡片 1    │ │   計畫卡片 2    │ │   計畫卡片 3    │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │   計畫卡片 4    │ │   計畫卡片 5    │ │   計畫卡片 6    │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    [1] [2] [3] ... [10]                    │
└─────────────────────────────────────────────────────────────┘
```

### 2. 計畫卡片設計

```
┌─────────────────────────────────────────────────────────┐
│ 📅 每日早晨更新                           🟢 運行中      │
│ ─────────────────────────────────────────────────────── │
│ 📝 每天早上8點自動更新指定設備的預覽圖                  │
│                                                         │
│ 🎯 刷圖對象: 指定MAC (2台設備)                          │
│ ⏰ 下次執行: 明天 08:00                                 │
│ 📊 成功率: 95% (19/20)                                  │
│                                                         │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐        │
│ │ ▶️ 立即執行│ │ ⚙️ 編輯  │ │ 📊 統計  │ │ ⋯ 更多   │        │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘        │
└─────────────────────────────────────────────────────────┘
```

#### 門店數據對象的計畫卡片示例
```
┌─────────────────────────────────────────────────────────┐
│ 📅 促銷商品更新                           🔵 已啟用      │
│ ─────────────────────────────────────────────────────── │
│ 📝 每週更新促銷商品的價格和折扣信息                     │
│                                                         │
│ 🎯 刷圖對象: 門店數據 (3項數據，影響8台設備)            │
│ ⏰ 下次執行: 週一 09:00                                 │
│ 📊 成功率: 88% (7/8)                                    │
│                                                         │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐        │
│ │ ▶️ 立即執行│ │ ⚙️ 編輯  │ │ 📊 統計  │ │ ⋯ 更多   │        │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘        │
└─────────────────────────────────────────────────────────┘
```

#### 計畫卡片狀態指示
- **🟢 運行中**：計畫正在執行
- **🔵 已啟用**：計畫已啟用，等待下次執行
- **⚪ 已停用**：計畫已停用
- **🔴 錯誤**：計畫執行出現錯誤

### 3. 計畫配置向導

#### 步驟指示器
```
┌─────────────────────────────────────────────────────────┐
│ 1. 基礎設定 → 2. 設備選擇 → 3. 觸發條件 → 4. 執行策略 → 5. 確認 │
│    ●             ○             ○             ○         ○   │
└─────────────────────────────────────────────────────────┘
```

#### 步驟 1: 基礎設定
```
┌─────────────────────────────────────────────────────────┐
│ 基礎設定                                                │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 計畫名稱 *                                              │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 每日早晨更新                                        │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 計畫描述                                                │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 每天早上8點自動更新所有設備的預覽圖                 │ │
│ │                                                     │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 優先級                                                  │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐                   │
│ │   高    │ │ ● 中    │ │   低    │                   │
│ └─────────┘ └─────────┘ └─────────┘                   │
│                                                         │
│ ☑️ 立即啟用此計畫                                       │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

#### 步驟 2: 刷圖對象選擇
```
┌─────────────────────────────────────────────────────────┐
│ 刷圖對象選擇                                            │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 對象類型                                                │
│ ┌─────────────┐ ┌─────────────┐                       │
│ │ ● 指定MAC   │ │  門店數據   │                       │
│ └─────────────┘ └─────────────┘                       │
│                                                         │
│ MAC 地址選擇 (可多選)                                   │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ ☑️ AA:BB:CC:DD:EE:01 - EPD-001 (2.13吋)            │ │
│ │ ☑️ AA:BB:CC:DD:EE:02 - EPD-002 (4.2吋)             │ │
│ │ ☐ AA:BB:CC:DD:EE:03 - EPD-003 (7.5吋)              │ │
│ │ ☐ AA:BB:CC:DD:EE:04 - EPD-004 (2.13吋)             │ │
│ │ ... 顯示更多                                        │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 📊 已選擇 2 台設備                                      │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

#### 步驟 2: 門店數據選擇 (替代界面)
```
┌─────────────────────────────────────────────────────────┐
│ 刷圖對象選擇                                            │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 對象類型                                                │
│ ┌─────────────┐ ┌─────────────┐                       │
│ │  指定MAC    │ │ ● 門店數據  │                       │
│ └─────────────┘ └─────────────┘                       │
│                                                         │
│ 門店數據選擇 (可多選)                                   │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ ☑️ 商品A - 價格: $99 (綁定設備: 5台)                │ │
│ │ ☑️ 商品B - 價格: $199 (綁定設備: 3台)               │ │
│ │ ☐ 商品C - 價格: $299 (綁定設備: 2台)                │ │
│ │ ☐ 促銷活動 - 折扣: 20% (綁定設備: 8台)              │ │
│ │ ... 顯示更多                                        │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 📊 已選擇 2 項數據，將影響 8 台設備                     │
│ ⚠️ 只有同時綁定模板和選中數據的設備才會執行刷圖         │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

#### 步驟 3: 觸發條件
```
┌─────────────────────────────────────────────────────────┐
│ 觸發條件                                                │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 執行類型                                                │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐                   │
│ │  單次   │ │ ● 每日  │ │  每週   │                   │
│ └─────────┘ └─────────┘ └─────────┘                   │
│                                                         │
│ 執行時間                                                │
│ ┌─────┐ : ┌─────┐                                     │
│ │ 08  │   │ 00  │                                     │
│ └─────┘   └─────┘                                     │
│                                                         │
│ 📅 預覽: 每天 08:00 執行                                │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

#### 步驟 3: 單次執行設定 (替代界面)
```
┌─────────────────────────────────────────────────────────┐
│ 觸發條件                                                │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 執行類型                                                │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐                   │
│ │ ● 單次  │ │  每日   │ │  每週   │                   │
│ └─────────┘ └─────────┘ └─────────┘                   │
│                                                         │
│ 執行日期                                                │
│ ┌─────────────┐                                       │
│ │ 2024-01-15  │ 📅                                    │
│ └─────────────┘                                       │
│                                                         │
│ 執行時間                                                │
│ ┌─────┐ : ┌─────┐                                     │
│ │ 14  │   │ 30  │                                     │
│ └─────┘   └─────┘                                     │
│                                                         │
│ 📅 預覽: 2024-01-15 14:30 執行一次                     │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

#### 步驟 3: 每週執行設定 (替代界面)
```
┌─────────────────────────────────────────────────────────┐
│ 觸發條件                                                │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 執行類型                                                │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐                   │
│ │  單次   │ │  每日   │ │ ● 每週  │                   │
│ └─────────┘ └─────────┘ └─────────┘                   │
│                                                         │
│ 執行星期 (可多選)                                       │
│ ┌───┐ ┌───┐ ┌───┐ ┌───┐ ┌───┐ ┌───┐ ┌───┐         │
│ │ 日│ │●一│ │ 二│ │●三│ │ 四│ │●五│ │ 六│         │
│ └───┘ └───┘ └───┘ └───┘ └───┘ └───┘ └───┘         │
│                                                         │
│ 執行時間                                                │
│ ┌─────┐ : ┌─────┐                                     │
│ │ 08  │   │ 00  │                                     │
│ └─────┘   └─────┘                                     │
│                                                         │
│ 📅 預覽: 每週一、三、五 08:00 執行                      │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

#### 步驟 4: 執行策略
```
┌─────────────────────────────────────────────────────────┐
│ 執行策略                                                │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 傳送設定                                                │
│ ☑️ 使用系統設定的傳送參數                               │
│ ☑️ 啟用智能網關選擇                                     │
│ ☐ 發送到所有網關                                       │
│                                                         │
│ 📋 系統設定參數預覽                                     │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ • 網關並發數: 20                                    │ │
│ │ • 隊列重試次數: 3                                   │ │
│ │ • 重試間隔: 5 秒                                    │ │
│ │ • 超時時間: 30 秒                                   │ │
│ │                                                     │ │
│ │ ⚠️ 這些參數與設備管理的批量傳送使用相同設定          │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 🔗 如需修改這些參數，請前往 [系統設定] 頁面             │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

#### 步驟 5: 確認設定
```
┌─────────────────────────────────────────────────────────┐
│ 確認設定                                                │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 📋 計畫摘要                                             │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 計畫名稱: 每日早晨更新                              │ │
│ │ 刷圖對象: 指定MAC (2台設備)                         │ │
│ │ 執行時間: 每天 08:00                                │ │
│ │ 傳送設定: 使用系統設定，啟用智能網關                │ │
│ │ 狀態: 立即啟用                                      │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ ⚠️ 注意事項                                             │
│ • 計畫啟用後將按照設定的時間自動執行                    │
│ • 執行期間可能會影響網關性能                            │
│ • 建議在設備使用較少的時間執行                          │
│                                                         │
│ ┌─────────────┐ ┌─────────────┐                       │
│ │ 📄 保存草稿  │ │ ✅ 創建計畫  │                       │
│ └─────────────┘ └─────────────┘                       │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 4. 執行監控頁面

```
┌─────────────────────────────────────────────────────────┐
│ 執行監控 - 每日早晨更新                    🔄 自動刷新   │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 📊 執行概況                                             │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │
│ │ 總設備  │ │ 已完成  │ │ 處理中  │ │ 失敗    │       │
│ │   25    │ │   20    │ │    3    │ │    2    │       │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘       │
│                                                         │
│ 📈 執行進度                                             │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ ████████████████████████████████████████████░░░░░░ │ │
│ │ 80% (20/25)                                         │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 📋 設備處理狀態                                         │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ ✅ EPD-001  完成    ⏳ EPD-004  處理中               │ │
│ │ ✅ EPD-002  完成    ⏳ EPD-005  處理中               │ │
│ │ ✅ EPD-003  完成    ⏳ EPD-006  處理中               │ │
│ │ ❌ EPD-007  失敗    ❌ EPD-008  失敗                 │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │
│ │ 🛑 停止執行  │ │ 🔄 重試失敗  │ │ 📊 詳細報告  │       │
│ └─────────────┘ └─────────────┘ └─────────────┘       │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 5. 統計報表頁面

```
┌─────────────────────────────────────────────────────────┐
│ 統計報表                                                │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 📅 時間範圍: [最近7天 ▼]  📊 計畫: [全部 ▼]           │
│                                                         │
│ 📈 執行趨勢圖                                           │
│ ┌─────────────────────────────────────────────────────┐ │
│ │     成功率                                          │ │
│ │ 100%┤                                               │ │
│ │  90%┤     ●─────●─────●                             │ │
│ │  80%┤           ●─────●─────●                       │ │
│ │  70%┤                                               │ │
│ │     └─────┬─────┬─────┬─────┬─────┬─────┬           │ │
│ │          1/1   1/2   1/3   1/4   1/5   1/6         │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 📊 統計摘要                                             │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │
│ │ 總執行  │ │ 成功率  │ │ 平均時間│ │ 設備數  │       │
│ │   42    │ │  95%    │ │ 2.3分鐘 │ │  1,050  │       │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘       │
│                                                         │
│ 📋 計畫排行                                             │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 1. 每日早晨更新    95% (38/40)                      │ │
│ │ 2. 週末深度刷新    88% (22/25)                      │ │
│ │ 3. 促銷活動更新    92% (23/25)                      │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ ┌─────────────┐ ┌─────────────┐                       │
│ │ 📥 導出報告  │ │ 📧 郵件發送  │                       │
│ └─────────────┘ └─────────────┘                       │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## 響應式設計

### 1. 桌面版 (≥1024px)
- 使用卡片網格佈局，每行3-4個卡片
- 側邊欄導航固定顯示
- 支援多窗口操作

### 2. 平板版 (768px-1023px)
- 每行2個卡片
- 可收縮的側邊欄
- 觸控友好的按鈕尺寸

### 3. 手機版 (<768px)
- 單列卡片佈局
- 底部導航欄
- 手勢操作支援

## 交互細節

### 1. 動畫效果
- 卡片懸停時輕微上浮效果
- 狀態切換時的平滑過渡
- 加載時的骨架屏動畫
- 成功/失敗操作的反饋動畫

### 2. 快捷操作
- 雙擊卡片快速編輯
- 拖拽排序計畫優先級
- 鍵盤快捷鍵支援
- 批量操作選擇

### 3. 智能提示
- 表單驗證實時提示
- 操作確認對話框
- 上下文相關的幫助信息
- 錯誤處理友好提示

### 4. 個性化設定
- 卡片顯示密度調整
- 自定義儀表板佈局
- 主題色彩選擇
- 通知偏好設定

## 可訪問性設計

### 1. 鍵盤導航
- Tab 鍵順序邏輯清晰
- 焦點指示器明顯
- 快捷鍵提示
- 跳過連結支援

### 2. 螢幕閱讀器
- 語義化 HTML 結構
- ARIA 標籤完整
- 圖片替代文字
- 狀態變化通知

### 3. 視覺輔助
- 高對比度模式
- 字體大小調整
- 色彩無障礙設計
- 動畫減少選項

這個用戶界面設計確保了門店刷圖計畫系統的易用性和可訪問性，同時保持與現有系統的一致性和專業性。
