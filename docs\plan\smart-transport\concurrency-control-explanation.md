# 併發控制機制詳細說明

## 概述

本文檔詳細解釋智能網關選擇與任務隊列系統中的併發控制機制，包括單個網關的併發限制、系統總併發數量，以及相關參數的設置位置。

## 🔍 **併發數量的具體含義**

### 1. **系統級併發控制 (concurrency)**

**定義**: `concurrency` 參數控制的是**同時處理的任務數量**，而不是單個網關的任務數量。

```javascript
// 位置: server/services/sendPreviewToGateway.js 第1142-1146行
if (currentlyProcessing >= concurrency) {
  await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms後再檢查
  continue;
}
```

**實際意義**:
- `concurrency = 3` 表示系統同時處理 3 個設備的傳圖任務
- 這 3 個任務可能分配給不同的網關
- 每個任務會獨佔一個網關直到完成

### 2. **單個網關的併發限制**

**當前實現**: **每個網關同時只能處理 1 個傳圖任務**

```javascript
// 位置: server/services/websocketService.js 第1119-1145行
const isGatewayBusyWithChunk = (gatewayId) => {
  const gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
  if (!gatewayTransmissions || gatewayTransmissions.size === 0) {
    return false; // 網關空閒
  }
  
  // 檢查是否有任何活躍的傳輸
  let hasActiveTransmission = false;
  for (const [chunkId, transmission] of gatewayTransmissions) {
    if (transmission.status === 'active') {
      hasActiveTransmission = true; // 網關忙碌
    }
  }
  
  return hasActiveTransmission;
};
```

**判斷邏輯**:
- 只要網關有任何活躍的 chunk 傳輸，就被視為忙碌
- 忙碌的網關不會被分配新的任務
- 網關必須完成當前任務後才能接收新任務

## 📊 **併發數量計算公式**

### 1. **動態併發調整**

```javascript
// 位置: server/services/sendPreviewToGateway.js 第1030-1038行
const connectedGateways = websocketService.getConnectedGateways();
const maxConcurrency = Math.max(2, connectedGateways.size * 2);

const concurrency = Math.min(defaultConcurrency, maxConcurrency);
```

**計算邏輯**:
```
maxConcurrency = max(2, 網關數量 × 2)
actualConcurrency = min(用戶設定值, maxConcurrency)
```

**實例**:
- 1個網關: maxConcurrency = max(2, 1×2) = 2
- 2個網關: maxConcurrency = max(2, 2×2) = 4  
- 3個網關: maxConcurrency = max(2, 3×2) = 6
- 5個網關: maxConcurrency = max(2, 5×2) = 10

### 2. **實際併發分配**

| 網關數量 | 最大理論併發 | 建議設定值 | 實際效果 |
|----------|-------------|------------|----------|
| 1 | 2 | 2 | 1個網關最多1個任務，但系統可準備2個任務 |
| 2 | 4 | 3-4 | 2個網關各1個任務，系統可準備更多任務 |
| 3 | 6 | 4-5 | 3個網關各1個任務，其餘任務等待 |
| 4 | 8 | 5-6 | 4個網關各1個任務，其餘任務等待 |
| 5+ | 10+ | 6-8 | 所有網關可同時工作 |

## ⚙️ **參數設置位置**

### 1. **系統併發數量設置**

#### 前端調用
```javascript
// 位置: 前端調用 API 時
const result = await sendMultipleDevicePreviewsToGateways(deviceIds, {
  concurrency: 3,  // 設置系統併發數量
  enableSmartSelection: true
});
```

#### 後端API
```javascript
// 位置: server/routes/deviceApi.js
router.post('/devices/send-multiple-previews', authenticate, async (req, res) => {
  const {
    deviceIds,
    concurrency = 3,  // 默認併發數量
    enableSmartSelection = true
  } = req.body;
});
```

#### 配置文件
```javascript
// 位置: config/smart-transport.js
module.exports = {
  queue: {
    concurrency: 3,             // 默認併發數
    minConcurrency: 2,          // 最小併發數
    maxConcurrency: 8           // 最大併發數
  }
};
```

### 2. **單個網關併發限制**

**當前限制**: 硬編碼為 1，無法調整

```javascript
// 位置: server/services/websocketService.js
// 網關忙碌判斷：只要有任何活躍傳輸就視為忙碌
const isGatewayBusyWithChunk = (gatewayId) => {
  // ... 檢查邏輯
  return hasActiveTransmission; // true = 忙碌，false = 空閒
};
```

**如需修改**: 需要修改 `isGatewayBusyWithChunk` 函數的邏輯

## 🔧 **併發控制流程**

### 1. **任務分配流程**

```mermaid
graph TD
    A[新任務進入隊列] --> B{系統併發數是否達到上限?}
    B -->|是| C[等待500ms後重新檢查]
    B -->|否| D[檢查任務是否可處理]
    D --> E{設備的網關是否可用?}
    E -->|是| F[分配網關並開始處理]
    E -->|否| G[任務等待或重新排隊]
    F --> H[網關標記為忙碌]
    H --> I[執行傳圖任務]
    I --> J[任務完成，網關標記為空閒]
    C --> B
    G --> K[等待網關變為可用]
    K --> D
```

### 2. **網關狀態管理**

```javascript
// 開始任務時
websocketService.startChunkTransmission(gatewayId, chunkId, deviceMac);

// 任務完成時  
websocketService.endChunkTransmission(gatewayId, chunkId);
```

**狀態追蹤**:
```javascript
// 數據結構: Map<gatewayId, Map<chunkId, transmissionInfo>>
activeChunkTransmissions = {
  "gateway1": {
    "chunk_123": { chunkId, deviceMac, startTime, status: 'active' }
  },
  "gateway2": {
    "chunk_456": { chunkId, deviceMac, startTime, status: 'active' }
  }
}
```

## 📈 **性能影響分析**

### 1. **併發數量對性能的影響**

| 併發數 | 優點 | 缺點 | 適用場景 |
|--------|------|------|----------|
| 1-2 | 穩定性高，資源消耗低 | 處理速度慢 | 小批量，網關性能較弱 |
| 3-5 | 平衡性能與穩定性 | 需要足夠的網關支持 | 中批量，標準配置 |
| 6-8 | 處理速度快 | 資源消耗高，可能不穩定 | 大批量，高性能網關 |

### 2. **網關數量與併發的關係**

```javascript
// 理想情況下的併發效率
const efficiency = Math.min(concurrency, availableGateways);

// 實例
// 3個併發，2個網關 → 實際效率 = 2
// 5個併發，4個網關 → 實際效率 = 4  
// 2個併發，5個網關 → 實際效率 = 2
```

## 🛠️ **調優建議**

### 1. **根據網關數量調整併發**

```javascript
const calculateOptimalConcurrency = (gatewayCount) => {
  if (gatewayCount <= 2) return 2-3;
  if (gatewayCount <= 4) return 3-5;
  if (gatewayCount <= 6) return 5-7;
  return Math.min(gatewayCount + 2, 8);
};
```

### 2. **監控關鍵指標**

```javascript
// 監控指標
const metrics = {
  concurrencyUtilization: currentlyProcessing / concurrency,
  gatewayUtilization: busyGateways / totalGateways,
  queueWaitTime: avgWaitTime,
  taskSuccessRate: successCount / totalCount
};
```

### 3. **動態調整策略**

```javascript
// 根據性能動態調整
if (metrics.gatewayUtilization < 0.7 && metrics.queueWaitTime > 5000) {
  // 網關利用率低但等待時間長 → 增加併發數
  concurrency = Math.min(concurrency + 1, maxConcurrency);
} else if (metrics.taskSuccessRate < 0.9) {
  // 成功率低 → 降低併發數
  concurrency = Math.max(concurrency - 1, minConcurrency);
}
```

## 📝 **總結**

1. **系統併發數 (concurrency)**: 控制同時處理的任務數量，可配置
2. **單個網關併發**: 固定為1，每個網關同時只能處理1個任務
3. **最大可用網關數**: 等於連接的網關數量，無額外限制
4. **動態調整**: 系統會根據網關數量自動調整最大併發數
5. **性能平衡**: 需要根據實際環境調整併發數以獲得最佳性能

**關鍵理解**: 併發數不是指單個網關的併發能力，而是指系統同時處理多少個設備的傳圖任務。每個任務會獨佔一個網關直到完成。
