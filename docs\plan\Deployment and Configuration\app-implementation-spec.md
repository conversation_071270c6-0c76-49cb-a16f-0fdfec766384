# App端實現規格書

## 1. 概述

本文檔定義了網關與設備通信架構中App端的實現規格。App端是一個配網工具，負責登入遠端Server、掃描本地網絡中的網關、註冊網關到Server，並將WebSocket連接信息發送給網關。

## 2. 負責範圍

App端開發人員負責以下功能的實現：

1. 用戶登入與認證
2. 本地網絡掃描（ARP掃描）
3. 網關註冊流程
4. 與網關的HTTP通信
5. 用戶界面設計與實現

## 3. 技術選擇

- **框架**：React Native（跨平台移動應用）
- **網絡通信**：
  - HTTP/HTTPS：用於與Server的API通信
  - UDP廣播：用於網關掃描
- **狀態管理**：Redux或MobX
- **UI組件**：React Native Paper或NativeBase
- **開發語言**：JavaScript/TypeScript

## 4. 詳細設計

### 4.1 用戶登入

#### 4.1.1 登入流程

1. 用戶輸入用戶名和密碼
2. 發送登入請求到Server
3. 接收並存儲JWT Token
4. 使用Token進行後續API請求

```javascript
async function login(username, password) {
  try {
    const response = await fetch('http://server-address:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.status}`);
    }

    const data = await response.json();
    return data.token; // 保存token用於後續請求
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
}
```

### 4.2 網關掃描

#### 4.2.1 本地網絡掃描

使用UDP廣播方法掃描本地網絡中的網關：

```javascript
import { NetworkInfo } from 'react-native-network-info';
import UdpSocket from 'react-native-udp';

// 掃描本地網絡中的網關
export async function scanLocalGateways() {
  try {
    // 獲取本地網絡信息
    const ipAddress = await NetworkInfo.getIPAddress();
    const subnet = await NetworkInfo.getSubnet();
    
    // 創建UDP socket
    const socket = UdpSocket.createSocket({ type: 'udp4' });
    const port = 8899; // 網關監聽的UDP端口
    
    // 準備廣播消息
    const message = Buffer.from(JSON.stringify({
      type: 'discovery',
      clientIp: ipAddress
    }));
    
    // 發送廣播
    socket.send(message, 0, message.length, port, '***************');
    
    // 監聽回應
    const discoveredGateways = [];
    
    return new Promise((resolve, reject) => {
      socket.on('message', (msg, rinfo) => {
        try {
          const response = JSON.parse(msg.toString());
          if (response.type === 'gateway_info') {
            discoveredGateways.push({
              ip: rinfo.address,
              mac: response.macAddress,
              model: response.model,
              name: response.name || `Gateway-${response.macAddress}`
            });
          }
        } catch (e) {
          console.error('Error parsing gateway response:', e);
        }
      });
      
      // 5秒後結束掃描
      setTimeout(() => {
        socket.close();
        resolve(discoveredGateways);
      }, 5000);
    });
  } catch (error) {
    console.error('Scan error:', error);
    throw error;
  }
}
```

### 4.3 網關註冊

#### 4.3.1 註冊流程

1. 獲取已註冊的網關列表
2. 檢查網關是否已註冊
3. 註冊新網關
4. 獲取網關的WebSocket連接信息
5. 發送連接命令給網關

```javascript
// 完整的網關註冊和配置流程
async function registerAndConfigureGateway(token, gatewayData, gatewayIp) {
  try {
    // 1. 註冊新網關
    const newGateway = await registerGateway(token, gatewayData);

    // 2. 獲取包含WebSocket連接信息的網關詳情
    const gatewayWithWsInfo = await getGatewayWithWsInfo(token, newGateway._id);

    // 3. 發送WebSocket連接命令給網關
    const result = await sendConnectCommand(gatewayIp, gatewayWithWsInfo.websocket);

    console.log('Gateway registered and configured successfully:', result);
    return result;
  } catch (error) {
    console.error('Error in gateway registration and configuration process:', error);
    throw error;
  }
}
```

### 4.4 與網關通信

#### 4.4.1 發送連接命令

```javascript
// 發送連接命令給網關
async function sendConnectCommand(gatewayIp, wsInfo) {
  try {
    const response = await fetch(`http://${gatewayIp}/api/connect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        wsUrl: wsInfo.url,
        wsToken: wsInfo.token,
        wsProtocol: wsInfo.protocol
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to send connect command: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending connect command:', error);
    throw error;
  }
}
```

### 4.5 用戶界面

#### 4.5.1 主要界面

1. **登入界面**：用戶名和密碼輸入
2. **網關列表界面**：顯示已註冊的網關
3. **掃描界面**：顯示掃描到的本地網關
4. **註冊界面**：輸入網關信息並註冊
5. **配置界面**：顯示配置結果

## 5. 與其他組件的接口

### 5.1 與Server的接口

App端需要調用Server提供的以下API：

1. **登入API**：`POST /api/auth/login`
2. **網關列表API**：`GET /api/gateways?storeId={storeId}`
3. **網關註冊API**：`POST /api/gateways`
4. **網關詳情API**：`GET /api/gateways/{id}`

### 5.2 與Gateway的接口

App端通過HTTP與Gateway通信，調用以下API：

1. **連接命令API**：`POST http://{gateway_ip}/api/connect`
   - 請求體：
     ```json
     {
       "wsUrl": "ws://server-address:port/ws/store/store_id/gateway/gateway_id",
       "wsToken": "jwt_token_for_gateway",
       "wsProtocol": "json"
     }
     ```

2. **狀態查詢API**：`GET http://{gateway_ip}/api/status`
   - 響應：
     ```json
     {
       "mode": "scan",
       "macAddress": "AA:BB:CC:DD:EE:FF",
       "ipAddress": "*************",
       "model": "Gateway-Model",
       "firmwareVersion": "1.0.0",
       "connectedDevicesCount": 3
     }
     ```

## 6. 安全考慮

1. 安全存儲用戶憑證
2. 使用HTTPS進行API通信
3. 驗證服務器證書
4. 實施輸入驗證
5. 處理敏感數據時使用安全存儲

## 7. 測試計劃

1. 單元測試：測試各個功能模塊
2. 集成測試：測試與Server和Gateway的通信
3. UI測試：測試用戶界面
4. 用戶體驗測試：測試應用的易用性

## 8. 部署考慮

1. 支持Android和iOS平台
2. 版本更新機制
3. 錯誤報告機制
4. 應用性能監控

## 9. 開發時間表

1. 用戶界面設計：3天
2. 登入與認證：2天
3. 網關掃描：3天
4. 網關註冊：3天
5. 測試：3天
6. 打包與部署：1天

總計：15個工作日
