<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文字樣式修復測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .canvas-container {
            border: 1px solid #ccc;
            background: white;
            position: relative;
            width: 300px;
            height: 200px;
            margin: 20px 0;
        }
        .text-element-content {
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>文字樣式修復測試</h1>
    <p>這個測試用來驗證前端預覽圖生成時文字樣式（顏色和大小）是否正確保留。</p>

    <div class="test-container">
        <h2>測試場景</h2>
        <p>模擬一個包含不同樣式文字元素的模板，測試 processTextBindings 函數是否正確保存和恢復文字樣式。</p>
        
        <div class="canvas-container" id="testCanvas">
            <!-- 測試文字元素 1: 大字體紅色 -->
            <div data-element-type="text" data-has-binding="true" data-field-id="test-field-1" data-store-id="test-store" data-index="0" data-original-content="測試文字1">
                <div class="text-element-content" style="
                    left: 10px; 
                    top: 10px; 
                    width: 120px; 
                    height: 40px;
                    fontSize: 24px;
                    fontFamily: Arial;
                    color: #ff0000;
                ">測試文字1</div>
            </div>
            
            <!-- 測試文字元素 2: 小字體藍色 -->
            <div data-element-type="text" data-has-binding="true" data-field-id="test-field-2" data-store-id="test-store" data-index="0" data-original-content="測試文字2">
                <div class="text-element-content" style="
                    left: 10px; 
                    top: 60px; 
                    width: 100px; 
                    height: 30px;
                    fontSize: 14px;
                    fontFamily: Georgia;
                    color: #0000ff;
                ">測試文字2</div>
            </div>
            
            <!-- 測試文字元素 3: 中等字體綠色 -->
            <div data-element-type="text" data-has-binding="false" data-original-content="測試文字3">
                <div class="text-element-content" style="
                    left: 10px; 
                    top: 110px; 
                    width: 110px; 
                    height: 35px;
                    fontSize: 18px;
                    fontFamily: Times;
                    color: #00ff00;
                ">測試文字3</div>
            </div>
        </div>

        <button class="test-button" onclick="runTest()">執行測試</button>
        <button class="test-button" onclick="clearResults()">清除結果</button>
        
        <div id="testResults"></div>
    </div>

    <script>
        // 模擬 BindingStyleInfo 接口
        class BindingStyleInfo {
            constructor(element, textContainer) {
                this.element = element;
                this.textContainer = textContainer;
                this.backgroundColor = textContainer.style.backgroundColor;
                this.border = textContainer.style.border;
                this.originalContent = undefined;
                this.bindingIndicator = undefined;
                this.indicatorDisplay = '';
                // 新增文字樣式屬性
                this.fontSize = textContainer.style.fontSize;
                this.fontFamily = textContainer.style.fontFamily;
                this.color = textContainer.style.color;
            }
        }

        // 模擬修復後的 processTextBindings 函數
        function processTextBindings() {
            const originalStyles = [];
            
            try {
                // 找到所有文字元素
                const textElements = document.querySelectorAll('[data-element-type="text"], [data-element-type="multiline-text"]');
                console.log('找到文字元素數量:', textElements.length);

                textElements.forEach(element => {
                    const textContainer = element.querySelector('.text-element-content');
                    if (textContainer) {
                        const hasBoundDataField = element.getAttribute('data-has-binding') === 'true';
                        const originalContent = element.getAttribute('data-original-content') || 'TEXT';

                        // 存儲原始樣式，包括文字樣式
                        const styleInfo = new BindingStyleInfo(element, textContainer);

                        // 存儲原始文字內容
                        const textNodes = [];
                        for (let i = 0; i < textContainer.childNodes.length; i++) {
                            const node = textContainer.childNodes[i];
                            if (node.nodeType === Node.TEXT_NODE) {
                                textNodes.push(node);
                            }
                        }
                        styleInfo.originalContent = textNodes.map(node => node.textContent).join('') || undefined;

                        // 移除綁定標記樣式
                        textContainer.style.backgroundColor = 'transparent';
                        textContainer.style.border = 'none';

                        // 先清空元素內容
                        while (textContainer.firstChild) {
                            textContainer.removeChild(textContainer.firstChild);
                        }

                        // 重新應用文字樣式（這是修復的關鍵部分）
                        if (styleInfo.fontSize) textContainer.style.fontSize = styleInfo.fontSize;
                        if (styleInfo.fontFamily) textContainer.style.fontFamily = styleInfo.fontFamily;
                        if (styleInfo.color) textContainer.style.color = styleInfo.color;

                        // 添加新的文字內容
                        if (hasBoundDataField) {
                            // 模擬綁定數據處理
                            const textNode = document.createTextNode('綁定數據內容');
                            textContainer.appendChild(textNode);
                        } else {
                            const textNode = document.createTextNode(originalContent);
                            textContainer.appendChild(textNode);
                        }

                        originalStyles.push(styleInfo);
                    }
                });
            } catch (error) {
                console.error('處理綁定元素時出錯:', error);
            }

            return originalStyles;
        }

        // 模擬修復後的 restoreTextBindings 函數
        function restoreTextBindings(originalStyles) {
            console.log('恢復綁定數據的文字元素原始樣式:', originalStyles.length);
            originalStyles.forEach(styleInfo => {
                // 清空元素
                while (styleInfo.textContainer.firstChild) {
                    styleInfo.textContainer.removeChild(styleInfo.textContainer.firstChild);
                }

                // 添加文字內容
                if (styleInfo.originalContent) {
                    const textNode = document.createTextNode(styleInfo.originalContent);
                    styleInfo.textContainer.appendChild(textNode);
                }

                // 恢復樣式
                styleInfo.textContainer.style.backgroundColor = styleInfo.backgroundColor;
                styleInfo.textContainer.style.border = styleInfo.border;
                
                // 恢復文字樣式（這是修復的關鍵部分）
                if (styleInfo.fontSize) styleInfo.textContainer.style.fontSize = styleInfo.fontSize;
                if (styleInfo.fontFamily) styleInfo.textContainer.style.fontFamily = styleInfo.fontFamily;
                if (styleInfo.color) styleInfo.textContainer.style.color = styleInfo.color;
            });
        }

        function runTest() {
            const resultsDiv = document.getElementById('testResults');
            let results = '<h3>測試結果</h3>';

            try {
                // 記錄原始樣式
                const textElements = document.querySelectorAll('.text-element-content');
                const originalStyles = [];
                textElements.forEach(el => {
                    originalStyles.push({
                        element: el,
                        fontSize: el.style.fontSize,
                        fontFamily: el.style.fontFamily,
                        color: el.style.color,
                        textContent: el.textContent
                    });
                });

                results += '<div class="result">原始樣式記錄完成</div>';

                // 執行 processTextBindings
                const bindingStyles = processTextBindings();
                results += '<div class="result">processTextBindings 執行完成</div>';

                // 檢查樣式是否保留
                let stylesPreserved = true;
                textElements.forEach((el, index) => {
                    const original = originalStyles[index];
                    if (el.style.fontSize !== original.fontSize || 
                        el.style.fontFamily !== original.fontFamily || 
                        el.style.color !== original.color) {
                        stylesPreserved = false;
                        results += `<div class="result error">元素 ${index + 1} 樣式未正確保留</div>`;
                    }
                });

                if (stylesPreserved) {
                    results += '<div class="result success">✓ 所有文字樣式在 processTextBindings 後正確保留</div>';
                } else {
                    results += '<div class="result error">✗ 部分文字樣式在 processTextBindings 後丟失</div>';
                }

                // 執行 restoreTextBindings
                restoreTextBindings(bindingStyles);
                results += '<div class="result">restoreTextBindings 執行完成</div>';

                // 檢查恢復後的樣式
                let stylesRestored = true;
                textElements.forEach((el, index) => {
                    const original = originalStyles[index];
                    if (el.style.fontSize !== original.fontSize || 
                        el.style.fontFamily !== original.fontFamily || 
                        el.style.color !== original.color ||
                        el.textContent !== original.textContent) {
                        stylesRestored = false;
                        results += `<div class="result error">元素 ${index + 1} 樣式或內容未正確恢復</div>`;
                    }
                });

                if (stylesRestored) {
                    results += '<div class="result success">✓ 所有文字樣式和內容在 restoreTextBindings 後正確恢復</div>';
                } else {
                    results += '<div class="result error">✗ 部分文字樣式或內容在 restoreTextBindings 後未正確恢復</div>';
                }

                // 總結
                if (stylesPreserved && stylesRestored) {
                    results += '<div class="result success"><strong>✓ 測試通過：文字樣式修復成功！</strong></div>';
                } else {
                    results += '<div class="result error"><strong>✗ 測試失敗：文字樣式修復需要進一步調整</strong></div>';
                }

            } catch (error) {
                results += `<div class="result error">測試執行錯誤: ${error.message}</div>`;
            }

            resultsDiv.innerHTML = results;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }
    </script>
</body>
</html>
