const { MongoClient, ObjectId } = require('mongodb');

// MongoDB 連接信息
const uri = 'mongodb://127.0.0.1:27017';
const dbName = 'resourceManagement';

async function testStoreApi() {
  let client;

  try {
    console.log('開始測試 Store API 修改...');

    // 連接數據庫
    client = new MongoClient(uri);
    await client.connect();
    console.log('MongoDB 連接成功');

    const db = client.db(dbName);
    const storesCollection = db.collection('stores');

    // 測試 1: 創建一個新門店，確認 storeSpecificData 字段被正確初始化
    console.log('\n測試 1: 創建新門店');
    const testStore = {
      id: 'TEST001',
      name: '測試門店',
      address: '測試地址',
      status: 'active'
    };

    // 檢查是否已存在，如果存在則刪除
    const existingStore = await storesCollection.findOne({ id: testStore.id });
    if (existingStore) {
      await storesCollection.deleteOne({ id: testStore.id });
      console.log(`刪除已存在的測試門店: ${testStore.id}`);
    }

    // 創建新門店
    const now = new Date().toISOString();
    const newStore = {
      ...testStore,
      createdAt: now,
      updatedAt: now,
      storeSpecificData: [],
      gatewayManagement: {},
      deviceManagement: {},
      storeSettings: {}
    };

    const result = await storesCollection.insertOne(newStore);
    console.log(`創建測試門店成功，ID: ${result.insertedId}`);

    // 檢查門店是否正確創建
    const createdStore = await storesCollection.findOne({ id: testStore.id });
    if (!createdStore) {
      throw new Error('門店創建失敗');
    }

    // 檢查 storeSpecificData 字段是否正確初始化
    if (!Array.isArray(createdStore.storeSpecificData)) {
      throw new Error('storeSpecificData 字段未正確初始化為陣列');
    }

    console.log('測試 1 通過: 門店創建成功，storeSpecificData 字段正確初始化');

    // 測試 2: 添加門店專屬數據
    console.log('\n測試 2: 添加門店專屬數據');
    const testData = {
      sn: 1,
      id: 'ITEM001',
      name: '測試商品',
      description: '測試描述',
      price: '100',
      quantity: '10'
    };

    // 添加門店專屬數據
    await storesCollection.updateOne(
      { id: testStore.id },
      { $push: { storeSpecificData: { ...testData, _id: new ObjectId() } } }
    );

    // 檢查門店專屬數據是否正確添加
    const updatedStore = await storesCollection.findOne({ id: testStore.id });
    if (!updatedStore.storeSpecificData || updatedStore.storeSpecificData.length !== 1) {
      throw new Error('門店專屬數據添加失敗');
    }

    console.log('測試 2 通過: 門店專屬數據添加成功');

    // 測試 3: 更新門店專屬數據
    console.log('\n測試 3: 更新門店專屬數據');
    const updatedPrice = '200';

    // 更新門店專屬數據
    await storesCollection.updateOne(
      { id: testStore.id, 'storeSpecificData.sn': testData.sn },
      { $set: { 'storeSpecificData.$.price': updatedPrice } }
    );

    // 檢查門店專屬數據是否正確更新
    const storeAfterUpdate = await storesCollection.findOne({ id: testStore.id });
    const updatedItem = storeAfterUpdate.storeSpecificData.find(item => item.sn === testData.sn);

    if (!updatedItem || updatedItem.price !== updatedPrice) {
      throw new Error('門店專屬數據更新失敗');
    }

    console.log('測試 3 通過: 門店專屬數據更新成功');

    // 測試 4: 刪除門店專屬數據
    console.log('\n測試 4: 刪除門店專屬數據');

    // 刪除門店專屬數據
    await storesCollection.updateOne(
      { id: testStore.id },
      { $pull: { storeSpecificData: { sn: testData.sn } } }
    );

    // 檢查門店專屬數據是否正確刪除
    const storeAfterDelete = await storesCollection.findOne({ id: testStore.id });

    if (storeAfterDelete.storeSpecificData.some(item => item.sn === testData.sn)) {
      throw new Error('門店專屬數據刪除失敗');
    }

    console.log('測試 4 通過: 門店專屬數據刪除成功');

    // 清理測試數據
    await storesCollection.deleteOne({ id: testStore.id });
    console.log('\n清理測試數據成功');

    console.log('\n所有測試通過！');

  } catch (error) {
    console.error('測試失敗:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('MongoDB 連接已關閉');
    }
  }
}

// 執行測試
testStoreApi();
