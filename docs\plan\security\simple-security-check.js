// 簡化的安全檢查腳本
import fs from 'fs';
import path from 'path';

console.log('🚀 EPD Manager 安全快速檢查 (重新評估版)\n');

const issues = [];
const warnings = [];
const info = [];

// 檢查硬編碼密鑰
console.log('🔍 檢查硬編碼密鑰...');
const dangerousSecrets = [
  'epd-manager-jwt-secret-key',
  'your_jwt_secret_here',
  'your-super-secret-jwt-key-change-this-in-production'
];

const filesToCheck = [
  'server/utils/auth.js',
  'server/utils/jwtUtils.js', 
  'server/index.js',
  'server/routes/gatewayApi.js'
];

let foundSecrets = false;
filesToCheck.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      dangerousSecrets.forEach(secret => {
        if (content.includes(secret)) {
          info.push(`ℹ️  開發環境密鑰 "${secret}" 在 ${file} (僅開發環境使用)`);
          foundSecrets = true;
        }
      });
    }
  } catch (error) {
    console.log(`⚠️  無法讀取檔案: ${file}`);
  }
});

if (!foundSecrets) {
  console.log('✅ 未發現硬編碼密鑰');
} else {
  console.log('ℹ️  發現開發環境密鑰 (正常，僅用於開發測試)');
}

// 檢查長期有效的 Token
console.log('🔍 檢查 Token 過期配置...');
filesToCheck.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('30d') || content.includes('30 * 24 * 60 * 60')) {
        warnings.push(`🟡 發現長期有效 Token (30天) 在 ${file}`);
      }
    }
  } catch (error) {
    // 忽略錯誤
  }
});

// 檢查環境變數配置
console.log('🔍 檢查環境變數...');
if (fs.existsSync('.env')) {
  const envContent = fs.readFileSync('.env', 'utf8');
  if (envContent.includes('JWT_SECRET=')) {
    const match = envContent.match(/JWT_SECRET=(.+)/);
    if (match) {
      const secret = match[1].trim();
      if (secret.length < 32) {
        warnings.push(`🟡 .env 中 JWT_SECRET 長度不足: ${secret.length} < 32`);
      } else if (secret.includes('change-this') || secret.includes('your-')) {
        warnings.push(`🟡 .env 使用範例密鑰 (生產環境請使用強密鑰)`);
      } else {
        console.log('✅ .env JWT_SECRET 配置正確');
      }
    }
  } else {
    warnings.push(`🟡 .env 檔案缺少 JWT_SECRET 配置`);
  }
} else {
  warnings.push(`🟡 未找到 .env 檔案`);
}

// 檢查前端存儲
console.log('🔍 檢查前端 Token 存儲...');
if (fs.existsSync('src/store/authStore.ts')) {
  const content = fs.readFileSync('src/store/authStore.ts', 'utf8');
  if (content.includes('localStorage')) {
    warnings.push(`🟡 前端使用 localStorage 存儲 Token`);
  }
}

// 生成報告
console.log('\n' + '='.repeat(50));
console.log('📊 安全檢查結果');
console.log('='.repeat(50));

if (issues.length > 0) {
  console.log('\n🚨 嚴重問題 (需要立即修復):');
  issues.forEach(issue => console.log(issue));
}

if (warnings.length > 0) {
  console.log('\n⚠️  警告 (建議改善):');
  warnings.forEach(warning => console.log(warning));
}

if (info.length > 0) {
  console.log('\nℹ️  資訊 (開發環境正常):');
  info.forEach(item => console.log(item));
}

const score = Math.max(0, 100 - (issues.length * 30) - (warnings.length * 10));
console.log(`\n🎯 安全評分: ${score}/100`);

if (score >= 80) {
  console.log('✅ 安全狀態良好');
} else if (score >= 60) {
  console.log('⚠️  安全狀態一般，需要改善');
} else {
  console.log('❌ 安全狀態不佳，存在重大風險');
}

console.log('\n💡 優化建議:');
console.log('1. 實作系統配置化: Token 過期時間可調整');
console.log('2. 建立密鑰管理介面: Web 一鍵刷新密鑰');
console.log('3. 生產環境確保使用強密鑰');
console.log('4. 查看實作計畫: docs/plan/security/system-config-implementation.md');
console.log('5. 查看重新評估報告: docs/plan/security/security-reassessment.md');

console.log('\n' + '='.repeat(50));
