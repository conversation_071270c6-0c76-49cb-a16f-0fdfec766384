# Gateway 狀態重置實作說明

## 問題描述

當 server 被停止後，原本已經連線的 gateway 如果緊接著也被關機，下次 server 再開機時，只要 gateway 沒有開機回報狀態，這時候 server 還是會記錄該 gateway 為"連線"，這是不正確的行為。

## 問題分析

### 原始行為
1. Gateway 連線時，狀態設為 `online`
2. Gateway 斷線時，狀態設為 `offline`
3. Server 重啟時，不會重置 Gateway 狀態
4. 如果 Gateway 在 Server 停止期間關機，Server 重啟後仍顯示為 `online`

### 問題根因
Server 啟動時沒有重置所有 Gateway 狀態為 `offline`，導致狀態不一致。

## 解決方案

### 實作概述
在 WebSocket 服務初始化時，添加一個狀態重置步驟，將所有 Gateway 狀態重置為 `offline`。

### 核心修改

#### 1. 新增 `resetAllGatewayStatus` 函數

```javascript
// 重置所有網關狀態為離線
const resetAllGatewayStatus = async () => {
  try {
    const { collection } = await getGatewayCollection();
    
    // 將所有網關狀態設為 offline
    const result = await collection.updateMany(
      { status: 'online' }, // 只更新目前為 online 的網關
      {
        $set: {
          status: 'offline',
          updatedAt: new Date()
        }
      }
    );
    
    if (result.modifiedCount > 0) {
      console.log(`已重置 ${result.modifiedCount} 個網關狀態為離線`);
    } else {
      console.log('沒有需要重置狀態的網關');
    }
  } catch (error) {
    console.error('重置網關狀態失敗:', error);
  }
};
```

#### 2. 修改 `initWebSocketServer` 函數

```javascript
// 初始化 WebSocket 服務
const initWebSocketServer = async (server, jwtSecret) => {
  // 首先重置所有網關狀態為離線
  await resetAllGatewayStatus();
  
  // ... 其餘初始化邏輯
};
```

#### 3. 更新 server/index.js 中的調用

```javascript
// 初始化 WebSocket 服務（現在是 async 函數）
await initWebSocketServer(server, jwtSecret);
```

### 實作特點

1. **只重置 online 狀態的 Gateway**：避免不必要的數據庫操作
2. **記錄重置數量**：提供清晰的日誌信息
3. **錯誤處理**：確保重置失敗不會影響服務啟動
4. **非阻塞**：重置操作不會阻塞其他服務的啟動

## 測試驗證

### 測試場景
1. 創建多個測試 Gateway，部分設為 `online` 狀態
2. 執行狀態重置函數
3. 驗證所有 Gateway 狀態都變為 `offline`

### 測試結果
```
=== Gateway 狀態重置測試 ===

1. 準備測試數據...
✓ 已插入 3 個測試 Gateway

2. 檢查初始狀態...
   - Online Gateway 數量: 2
   - Offline Gateway 數量: 1

4. 執行 Gateway 狀態重置...
已重置 2 個網關狀態為離線

5. 檢查重置後的狀態...
   - Online Gateway 數量: 0
   - Offline Gateway 數量: 3

✅ 測試通過！所有 Gateway 狀態已正確重置為 offline
```

## 行為變更

### 修改前
- Server 啟動時保持 Gateway 原有狀態
- 可能出現狀態不一致的情況

### 修改後
- Server 啟動時重置所有 Gateway 狀態為 `offline`
- 只有當 Gateway 主動連線時才設為 `online`
- 確保狀態的一致性和準確性

## 影響評估

### 正面影響
1. **狀態一致性**：確保 Gateway 狀態反映真實連線情況
2. **可靠性提升**：避免顯示錯誤的連線狀態
3. **運維友好**：Server 重啟後狀態清晰明確

### 潜在影響
1. **短暫離線顯示**：Server 重啟時，所有 Gateway 會短暫顯示為離線
2. **重連時間**：Gateway 需要重新連線才能顯示為在線

## 相關文件

- `server/services/websocketService.js` - 主要實作
- `server/index.js` - 服務啟動邏輯
- `tests/test-gateway-status-reset.cjs` - 測試驗證

## 後續建議

1. **監控重置頻率**：記錄每次重置的 Gateway 數量，監控異常情況
2. **優化重連機制**：考慮實作 Gateway 自動重連機制
3. **狀態持久化**：考慮是否需要更精細的狀態管理策略
