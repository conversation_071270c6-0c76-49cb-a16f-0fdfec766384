# EPD Manager App 專案結構

## 1. 目錄結構

```
epd-manager-app/
├── assets/                  # 靜態資源文件
│   ├── images/              # 圖片資源
│   ├── fonts/               # 字體資源
│   └── animations/          # 動畫資源
│
├── src/                     # 源代碼
│   ├── api/                 # API 相關
│   │   ├── auth.js          # 認證 API
│   │   ├── store.js         # 門店 API
│   │   ├── gateway.js       # 網關 API
│   │   ├── device.js        # 設備 API
│   │   └── index.js         # API 導出
│   │
│   ├── components/          # 可重用組件
│   │   ├── common/          # 通用組件
│   │   ├── auth/            # 認證相關組件
│   │   ├── store/           # 門店相關組件
│   │   ├── gateway/         # 網關相關組件
│   │   ├── device/          # 設備相關組件
│   │   └── websocket/       # WebSocket 相關組件
│   │
│   ├── navigation/          # 導航相關
│   │   ├── AppNavigator.js  # 主導航
│   │   ├── AuthNavigator.js # 認證導航
│   │   └── TabNavigator.js  # 標籤導航
│   │
│   ├── screens/             # 頁面
│   │   ├── auth/            # 認證相關頁面
│   │   ├── store/           # 門店相關頁面
│   │   ├── gateway/         # 網關相關頁面
│   │   ├── device/          # 設備相關頁面
│   │   ├── console/         # WebSocket 控制台頁面
│   │   └── settings/        # 設置頁面
│   │
│   ├── services/            # 服務
│   │   ├── auth.js          # 認證服務
│   │   ├── storage.js       # 存儲服務
│   │   ├── websocket.js     # WebSocket 服務
│   │   └── image.js         # 圖像處理服務
│   │
│   ├── store/               # Redux 狀態管理
│   │   ├── slices/          # Redux 切片
│   │   ├── actions/         # Redux 動作
│   │   ├── selectors/       # Redux 選擇器
│   │   └── index.js         # Redux 存儲配置
│   │
│   ├── utils/               # 工具函數
│   │   ├── api.js           # API 工具
│   │   ├── validation.js    # 驗證工具
│   │   ├── format.js        # 格式化工具
│   │   └── logger.js        # 日誌工具
│   │
│   ├── constants/           # 常量
│   │   ├── api.js           # API 常量
│   │   ├── navigation.js    # 導航常量
│   │   ├── websocket.js     # WebSocket 常量
│   │   └── ui.js            # UI 常量
│   │
│   ├── hooks/               # 自定義 Hooks
│   │   ├── useAuth.js       # 認證 Hook
│   │   ├── useWebSocket.js  # WebSocket Hook
│   │   └── useStore.js      # 門店 Hook
│   │
│   ├── localization/        # 國際化
│   │   ├── translations/    # 翻譯文件
│   │   └── i18n.js          # i18n 配置
│   │
│   ├── theme/               # 主題
│   │   ├── colors.js        # 顏色
│   │   ├── typography.js    # 排版
│   │   └── index.js         # 主題導出
│   │
│   ├── App.js               # 應用入口
│   └── index.js             # 根組件
│
├── docs/                    # 文檔
│   ├── app-implementation-plan.md  # 實現計劃
│   ├── communication-flow.md       # 通信流程
│   └── project-structure.md        # 專案結構
│
├── .expo/                   # Expo 配置
├── node_modules/            # 依賴包
├── app.json                 # Expo 應用配置
├── babel.config.js          # Babel 配置
├── metro.config.js          # Metro 配置
├── package.json             # 包配置
└── README.md                # 項目說明
```

## 2. 主要組件說明

### 2.1 API 模塊

API 模塊負責與後端服務進行通信，包括：

- **auth.js**: 處理用戶認證相關 API
- **store.js**: 處理門店相關 API
- **gateway.js**: 處理網關相關 API
- **device.js**: 處理設備相關 API

每個 API 模塊都封裝了相關的 HTTP 請求，例如：

```javascript
// api/auth.js
import axios from 'axios';
import { API_BASE_URL } from '../constants/api';

export const login = async (username, password) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username,
      password
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const logout = async () => {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/logout`);
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

### 2.2 WebSocket 服務

WebSocket 服務負責與後端 WebSocket 服務器建立連接，處理實時通信：

```javascript
// services/websocket.js
import { WS_BASE_URL } from '../constants/api';

class WebSocketService {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectTimeout = null;
    this.messageHandlers = new Map();
  }

  connect(gateway, storeId, token) {
    const url = `${WS_BASE_URL}/store/${storeId}/gateway/${gateway._id}?token=${token}`;
    
    this.ws = new WebSocket(url);
    
    this.ws.onopen = this.handleOpen.bind(this);
    this.ws.onmessage = this.handleMessage.bind(this);
    this.ws.onerror = this.handleError.bind(this);
    this.ws.onclose = this.handleClose.bind(this);
    
    return this;
  }
  
  // 其他方法...
}

export default new WebSocketService();
```

### 2.3 Redux 狀態管理

使用 Redux Toolkit 管理應用狀態：

```javascript
// store/slices/authSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  loading: false,
  error: null
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    loginSuccess: (state, action) => {
      state.loading = false;
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
    },
    loginFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    logout: (state) => {
      return initialState;
    }
  }
});

export const { loginStart, loginSuccess, loginFailure, logout } = authSlice.actions;
export default authSlice.reducer;
```

### 2.4 自定義 Hooks

自定義 Hooks 用於封裝常用邏輯：

```javascript
// hooks/useWebSocket.js
import { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import WebSocketService from '../services/websocket';

export const useWebSocket = (gateway, storeId) => {
  const { token } = useSelector(state => state.auth);
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState([]);
  
  useEffect(() => {
    if (!gateway || !storeId || !token) return;
    
    const ws = WebSocketService.connect(gateway, storeId, token);
    
    ws.addMessageHandler('all', (message) => {
      setMessages(prev => [...prev, message]);
    });
    
    ws.onConnectionChange((connected) => {
      setIsConnected(connected);
    });
    
    return () => {
      WebSocketService.disconnect();
    };
  }, [gateway, storeId, token]);
  
  const sendMessage = useCallback((type, data) => {
    if (!isConnected) return false;
    WebSocketService.sendMessage(type, data);
    return true;
  }, [isConnected]);
  
  return {
    isConnected,
    messages,
    sendMessage
  };
};
```

### 2.5 頁面組件

頁面組件用於實現用戶界面：

```javascript
// screens/gateway/GatewayListScreen.js
import React, { useEffect, useState } from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { fetchGateways, createGateway } from '../../store/actions/gatewayActions';
import { GatewayItem, AddGatewayButton, LoadingIndicator, ErrorMessage } from '../../components/gateway';

const GatewayListScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const { gateways, loading, error } = useSelector(state => state.gateway);
  const { selectedStore } = useSelector(state => state.store);
  
  useEffect(() => {
    if (selectedStore) {
      dispatch(fetchGateways(selectedStore.id));
    }
  }, [dispatch, selectedStore]);
  
  const handleAddGateway = () => {
    navigation.navigate('CreateGateway');
  };
  
  if (loading) return <LoadingIndicator />;
  if (error) return <ErrorMessage message={error} />;
  
  return (
    <View style={styles.container}>
      <FlatList
        data={gateways}
        keyExtractor={item => item._id}
        renderItem={({ item }) => (
          <GatewayItem
            gateway={item}
            onPress={() => navigation.navigate('GatewayDetail', { gateway: item })}
          />
        )}
      />
      <AddGatewayButton onPress={handleAddGateway} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16
  }
});

export default GatewayListScreen;
```

## 3. 主要功能模塊

### 3.1 認證模塊

- 登入/登出
- Token 管理
- 用戶信息管理

### 3.2 門店模塊

- 門店列表
- 門店選擇
- 門店詳情

### 3.3 網關模塊

- 網關列表
- 網關創建
- 網關詳情
- 網關狀態管理

### 3.4 設備模塊

- 設備列表
- 設備添加/移除
- 設備狀態管理
- 設備預覽圖像

### 3.5 WebSocket 模塊

- 連接管理
- 消息發送/接收
- 心跳機制
- 重連機制

### 3.6 設置模塊

- 應用配置
- 用戶偏好設置
- 緩存管理
