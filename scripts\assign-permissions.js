import fetch from 'node-fetch';

// 配置
const API_BASE_URL = 'http://localhost:3001/api';
let TOKEN = '';

// 通用請求函數
async function makeRequest(endpoint, method = 'GET', data = null) {
  const url = `${API_BASE_URL}/${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': `token=${TOKEN}`
    },
    credentials: 'include'
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);

    // 檢查響應狀態
    if (!response.ok) {
      let errorMessage = `API error: ${response.status}`;

      try {
        const errorData = await response.json();
        errorMessage += ` - ${errorData.error || 'Unknown error'}`;
      } catch (jsonError) {
        errorMessage += ' - Could not parse error response';
      }

      throw new Error(errorMessage);
    }

    // 嘗試解析 JSON 響應
    try {
      const responseData = await response.json();
      return responseData;
    } catch (jsonError) {
      console.warn(`Warning: Could not parse JSON response from ${endpoint}`);
      return {}; // 返回空對象而不是拋出錯誤
    }
  } catch (error) {
    console.error(`Error making request to ${endpoint}:`, error.message);
    throw error;
  }
}

// 獲取所有角色
async function getAllRoles() {
  console.log('Getting all roles...');
  try {
    const roles = await makeRequest('roles');
    console.log(`Found ${roles.length} roles`);
    return roles;
  } catch (error) {
    console.error('Failed to get roles:', error.message);
    return [];
  }
}

// 獲取所有用戶
async function getAllUsers() {
  console.log('Getting all users...');
  try {
    const response = await makeRequest('users');
    console.log(`Found ${response.users.length} users`);
    return response.users;
  } catch (error) {
    console.error('Failed to get users:', error.message);
    return [];
  }
}

// 獲取所有門店
async function getAllStores() {
  console.log('Getting all stores...');
  try {
    const response = await makeRequest('stores');

    // 處理不同的可能響應結構
    let stores = [];

    if (Array.isArray(response)) {
      // 如果響應直接是數組
      stores = response;
    } else if (response && typeof response === 'object') {
      // 如果響應是對象，嘗試獲取 stores 屬性
      if (Array.isArray(response.stores)) {
        stores = response.stores;
      } else if (response.data && Array.isArray(response.data)) {
        stores = response.data;
      }
    }

    console.log(`Found ${stores.length} stores`);
    return stores;
  } catch (error) {
    console.error('Failed to get stores:', error.message);
    return [];
  }
}

// 獲取所有資料欄位
async function getAllDataFields() {
  console.log('Getting all data fields...');
  try {
    const dataFields = await makeRequest('dataFields');
    console.log(`Found ${dataFields.length} data fields`);
    return dataFields;
  } catch (error) {
    console.error('Failed to get data fields:', error.message);
    return [];
  }
}

// 獲取用戶權限
async function getUserPermissions(userId) {
  console.log(`Getting permissions for user ${userId}...`);
  try {
    const response = await makeRequest(`permissions?userId=${userId}`);
    console.log('Permissions response:', response);

    // 檢查響應格式，確保返回的是數組
    const permissions = Array.isArray(response) ? response :
                       (response.permissions ? response.permissions : []);

    console.log(`Found ${permissions.length} permissions for user`);
    return permissions;
  } catch (error) {
    console.error(`Failed to get user permissions:`, error.message);
    return [];
  }
}

// 分配權限
async function assignPermission(userId, roleId, scope, scopeType) {
  console.log(`Assigning permission: ${roleId} to user ${userId} for ${scopeType} ${scope}`);

  // 先檢查用戶是否已有該範圍的權限
  const userPermissions = await getUserPermissions(userId);
  const existingPermission = userPermissions.find(p => p.scope === scope);

  if (existingPermission) {
    console.log(`User already has permission for scope ${scope}, updating...`);
    try {
      // 如果角色不同，則更新權限
      if (existingPermission.roleId !== roleId) {
        const updatedPermission = await makeRequest(`permissions/${existingPermission._id}`, 'PUT', {
          roleId,
          scope,
          scopeType
        });
        console.log(`Permission updated: ${updatedPermission._id}`);
        return updatedPermission;
      } else {
        console.log(`User already has the same role for this scope, skipping...`);
        return existingPermission;
      }
    } catch (error) {
      console.error(`Failed to update permission:`, error.message);
      return null;
    }
  } else {
    // 如果用戶沒有該範圍的權限，則創建新權限
    try {
      const permission = await makeRequest('permissions', 'POST', {
        userId,
        roleId,
        scope,
        scopeType
      });
      console.log(`Permission assigned: ${permission._id}`);
      return permission;
    } catch (error) {
      console.error(`Failed to assign permission:`, error.message);
      return null;
    }
  }
}

// 創建資料欄位
async function createDataField(id, name, type, section, prefix = '') {
  console.log(`Creating data field: ${id} (${name})`);
  try {
    // 檢查資料欄位是否已存在
    try {
      const dataFields = await makeRequest('dataFields');
      const existingField = dataFields.find(f => f.id === id || f.name === name);
      if (existingField) {
        console.log(`Data field already exists: ${existingField.id} (${existingField.name})`);
        return existingField;
      }
    } catch (err) {
      console.log('Error checking existing data fields, will try to create new one');
    }

    const dataField = await makeRequest('dataFields', 'POST', {
      id,
      name,
      type,
      section,
      prefix
    });
    console.log(`Data field created: ${dataField.id} (${dataField.name})`);
    return dataField;
  } catch (error) {
    console.error(`Failed to create data field ${id}:`, error.message);
    return null;
  }
}

// 登入獲取 TOKEN
async function login(username, password) {
  console.log(`Logging in as ${username}...`);
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`Login failed: ${data.error || 'Unknown error'}`);
    }

    // 從 response headers 中獲取 token
    const setCookieHeader = response.headers.get('set-cookie');
    if (setCookieHeader) {
      const tokenMatch = setCookieHeader.match(/token=([^;]+)/);
      if (tokenMatch && tokenMatch[1]) {
        TOKEN = tokenMatch[1];
        console.log('Login successful, token obtained');
        return true;
      }
    }

    // 如果 headers 中沒有 token，嘗試從 response body 獲取
    if (data.token) {
      TOKEN = data.token;
      console.log('Login successful, token obtained from response body');
      return true;
    }

    throw new Error('Failed to obtain token from response');
  } catch (error) {
    console.error('Login error:', error.message);
    return false;
  }
}

// 主函數
async function assignPermissions() {
  try {
    console.log('Starting permission assignment...');

    // 先登入獲取 TOKEN
    const loginSuccess = await login('root', '123456789');
    if (!loginSuccess) {
      throw new Error('Failed to login, cannot proceed with permission assignment');
    }

    // 1. 檢查並創建資料欄位
    console.log('\n=== Checking Data Fields ===');

    // 首先確保 id 欄位存在（使用原子操作）
    console.log('Ensuring id field exists...');
    await makeRequest('dataFields/ensure-id-field', 'POST');

    const dataFields = await getAllDataFields();
    if (dataFields.length <= 1) { // 只有 id 欄位或沒有欄位
      console.log('Only id field found or no fields, creating default data fields...');

      // 一般資料欄位（不需要創建 id 欄位，因為已經確保存在了）
      await createDataField('name', '名稱', 'pure text', 'ordinary');
      await createDataField('description', '描述', 'pure text', 'ordinary');
      await createDataField('price', '價格', 'number', 'ordinary');
      await createDataField('quantity', '數量', 'number', 'ordinary');
      await createDataField('date', '日期', 'pure text', 'ordinary');

      // 圖標資料欄位
      await createDataField('status_icon', '狀態圖標', 'ICON', 'icon');
      await createDataField('category_icon', '分類圖標', 'ICON', 'icon');

      // 圖片資料欄位
      await createDataField('product_image', '產品圖片', 'IMAGE', 'image');
      await createDataField('store_image', '門店圖片', 'IMAGE', 'image');

      // 視頻資料欄位
      await createDataField('product_video', '產品視頻', 'VIDEO', 'video');
      await createDataField('store_video', '門店視頻', 'VIDEO', 'video');
    } else {
      console.log(`Found ${dataFields.length} data fields, skipping creation`);
    }

    // 2. 獲取所有角色
    console.log('\n=== Checking Roles ===');
    const roles = await getAllRoles();
    if (roles.length === 0) {
      throw new Error('No roles found');
    }

    // 3. 獲取所有用戶
    console.log('\n=== Checking Users ===');
    const users = await getAllUsers();
    if (users.length === 0) {
      throw new Error('No users found');
    }

    // 4. 獲取所有門店
    console.log('\n=== Checking Stores ===');
    const stores = await getAllStores();
    if (stores.length === 0) {
      throw new Error('No stores found');
    }

    // 找到特定角色
    const systemAdminRole = roles.find(r => r.name === '系統管理員');
    const operatorRole = roles.find(r => r.name === '操作員');
    const reviewerRole = roles.find(r => r.name === '審核員');
    const managerRole = roles.find(r => r.name === '店長');
    const staffRole = roles.find(r => r.name === '店員');

    // 找到特定用戶
    const operator1 = users.find(u => u.username === 'operator1');
    const operator2 = users.find(u => u.username === 'operator2');
    const manager1 = users.find(u => u.username === 'manager1');
    const manager2 = users.find(u => u.username === 'manager2');
    const staff1 = users.find(u => u.username === 'staff1');

    // 找到特定門店
    const store1 = stores.find(s => s.id === 'TP001') || stores.find(s => s.name === '台北總店'); // 台北總店
    const store2 = stores.find(s => s.id === 'TC001') || stores.find(s => s.name === '台中分店'); // 台中分店
    const store3 = stores.find(s => s.id === 'KH001') || stores.find(s => s.name === '高雄分店'); // 高雄分店

    // 如果找不到門店，顯示警告
    if (!store1) console.warn('Warning: 台北總店 not found');
    if (!store2) console.warn('Warning: 台中分店 not found');
    if (!store3) console.warn('Warning: 高雄分店 not found');

    console.log('Found roles:', {
      systemAdminRole: systemAdminRole?._id,
      operatorRole: operatorRole?._id,
      reviewerRole: reviewerRole?._id,
      managerRole: managerRole?._id,
      staffRole: staffRole?._id
    });

    console.log('Found users:', {
      operator1: operator1?._id,
      operator2: operator2?._id,
      manager1: manager1?._id,
      manager2: manager2?._id,
      staff1: staff1?._id
    });

    console.log('Found stores:', {
      store1: store1?._id || store1?.id,
      store2: store2?._id || store2?.id,
      store3: store3?._id || store3?.id
    });

    // 5. 分配權限
    console.log('\n=== Assigning Permissions ===');
    if (operator1 && operatorRole) {
      await assignPermission(operator1._id, operatorRole._id, 'system', 'system');
    }

    if (operator2 && reviewerRole) {
      await assignPermission(operator2._id, reviewerRole._id, 'system', 'system');
    }

    if (manager1 && managerRole && store1) {
      const scope = store1.id;
      console.log(`Assigning manager1 to store1 with scope: ${scope}`);
      await assignPermission(manager1._id, managerRole._id, scope, 'store');
    } else {
      console.warn('Cannot assign manager1 to store1: missing data');
      if (manager1 && managerRole) {
        console.log('manager1 and managerRole exist, but store1 is missing');
      }
    }

    if (manager2 && managerRole && store2) {
      const scope = store2.id;
      console.log(`Assigning manager2 to store2 with scope: ${scope}`);
      await assignPermission(manager2._id, managerRole._id, scope, 'store');
    } else {
      console.warn('Cannot assign manager2 to store2: missing data');
    }

    if (staff1 && staffRole && store3) {
      const scope = store3.id;
      console.log(`Assigning staff1 to store3 with scope: ${scope}`);
      await assignPermission(staff1._id, staffRole._id, scope, 'store');
    } else {
      console.warn('Cannot assign staff1 to store3: missing data');
    }

    console.log('\nPermission assignment completed!');
  } catch (error) {
    console.error('Error assigning permissions:', error);
  }
}

// 執行
console.log('Starting script...');
assignPermissions().catch(error => {
  console.error('Unhandled error in assignPermissions:', error);
});

export {};
