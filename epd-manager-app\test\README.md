# EPD Manager App 測試工具

本目錄包含用於測試 EPD Manager App 的各種工具和腳本，特別是網關掃描功能的測試工具。

## 網關掃描測試工具

### 1. 網關設備模擬器 (gateway-discovery-service.js)

這個腳本模擬網關設備，監聽 UDP 端口並回應掃描請求。

#### 使用方法

```bash
node gateway-discovery-service.js
```

#### 功能

- 監聽 UDP 端口 5000
- 接收並解析掃描請求
- 回應包含網關信息的消息
- 支持 EPD-GATEWAY-DISCOVERY 協議

### 2. 掃描測試客戶端 (test-gateway-discovery.js)

這個腳本模擬移動應用發送 UDP 廣播消息，並接收網關回應。

#### 使用方法

```bash
node test-gateway-discovery.js
```

#### 功能

- 發送 UDP 廣播消息
- 接收並解析網關回應
- 顯示發現的網關列表
- 支持 EPD-GATEWAY-DISCOVERY 協議

## 測試步驟

### 基本測試

1. 在一個終端中啟動網關模擬器：

```bash
node gateway-discovery-service.js
```

2. 在另一個終端中運行測試客戶端：

```bash
node test-gateway-discovery.js
```

3. 觀察兩個終端的輸出，確認掃描請求和回應正常工作

### 多網關測試

1. 複製 `gateway-discovery-service.js` 並修改網關信息
2. 在多個終端中啟動不同的網關模擬器
3. 運行測試客戶端，確認能夠發現所有網關

### 與應用集成測試

1. 啟動網關模擬器
2. 在模擬器或真機上運行 EPD Manager App
3. 進入網關掃描頁面
4. 確認應用能夠發現並顯示模擬的網關

## 協議規範

### 掃描請求

```json
{
  "type": "discovery",
  "protocol": "EPD-GATEWAY-DISCOVERY",
  "version": "1.0",
  "timestamp": 1683270664000
}
```

### 掃描回應

```json
{
  "type": "discovery-response",
  "protocol": "EPD-GATEWAY-DISCOVERY",
  "version": "1.0",
  "timestamp": 1683270664500,
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "model": "GW-2000",
  "name": "Gateway-1234",
  "firmwareVersion": "1.0.0",
  "capabilities": ["wifi", "bluetooth"],
  "status": "ready"
}
```

## 故障排除

### 無法發現網關

- 確保設備在同一局域網內
- 檢查防火牆設置，確保 UDP 端口 5000 未被阻止
- 嘗試修改廣播地址（默認為 ***************）

### 網關模擬器無法啟動

- 確保端口 5000 未被其他應用占用
- 檢查 Node.js 版本（建議 v12 或更高版本）

### 回應格式錯誤

- 確保客戶端和服務器使用相同的協議版本
- 檢查 JSON 格式是否正確
