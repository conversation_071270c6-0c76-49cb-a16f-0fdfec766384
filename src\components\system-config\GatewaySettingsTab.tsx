import React from 'react';
import { useTranslation } from 'react-i18next';

export function GatewaySettingsTab() {
  const { t } = useTranslation();
  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">{t('systemConfig.gatewaySettings')}</h2>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">{t('systemConfig.gatewayIp')}</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="***********"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">{t('systemConfig.port')}</label>
          <input
            type="number"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="8080"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">{t('systemConfig.protocol')}</label>
          <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option>HTTP</option>
            <option>HTTPS</option>
            <option>TCP</option>
            <option>UDP</option>
          </select>
        </div>
      </div>
    </div>
  );
}