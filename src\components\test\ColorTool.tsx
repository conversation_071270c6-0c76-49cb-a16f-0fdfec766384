import React, { useState } from 'react';
import { RestrictedColorInput } from '../editor/properties/RestrictedColorInput';
import { getAvailableColorsForColorType } from '../../utils/colorConversion';

/**
 * 色彩工具組件
 * 用於預覽和驗證 RestrictedColorInput 組件的功能
 */
export const ColorTool: React.FC = () => {
  const [selectedColor, setSelectedColor] = useState('#000000');
  const [currentColorType, setCurrentColorType] = useState('Black & White & Red');

  const colorTypes = [
    'Gray16',
    'Black & White & Red',
    'Black & White & Red & Yellow',
    'All colors'
  ];

  const handleColorTypeChange = (colorType: string) => {
    setCurrentColorType(colorType);
    // 重置顏色為該類型的第一個可用顏色
    const availableColors = getAvailableColorsForColorType(colorType);
    if (availableColors.length > 0) {
      setSelectedColor(availableColors[0]);
    }
  };

  return (
    <div className="p-6 bg-gray-900 text-white min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">色彩工具</h1>

        {/* 顏色類型選擇器 */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">選擇顏色類型:</label>
          <select
            value={currentColorType}
            onChange={(e) => handleColorTypeChange(e.target.value)}
            className="bg-gray-700 text-white px-3 py-2 rounded border border-gray-600"
          >
            {colorTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        {/* 當前選中的顏色顯示 */}
        <div className="mb-6 p-4 bg-gray-800 rounded">
          <h3 className="text-lg font-medium mb-2">當前選中的顏色:</h3>
          <div className="flex items-center gap-4">
            <div
              className="w-16 h-16 rounded border border-gray-600"
              style={{ backgroundColor: selectedColor }}
            />
            <div>
              <p className="text-sm">顏色值: {selectedColor}</p>
              <p className="text-sm">顏色類型: {currentColorType}</p>
            </div>
          </div>
        </div>

        {/* 受限制的顏色選擇器 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">顏色選擇器:</h3>
          <div className="bg-gray-800 p-4 rounded">
            <RestrictedColorInput
              value={selectedColor}
              onChange={setSelectedColor}
              colorType={currentColorType}
            />
          </div>
        </div>

        {/* 可用顏色列表 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">可用顏色列表:</h3>
          <div className="bg-gray-800 p-4 rounded">
            {(() => {
              const availableColors = getAvailableColorsForColorType(currentColorType);
              return (
                <div className="space-y-2">
                  <p className="text-sm text-gray-400">
                    總共 {availableColors.length} 種可用顏色
                  </p>
                  <div className="grid grid-cols-8 gap-2">
                    {availableColors.map((color, index) => (
                      <div key={color} className="text-center">
                        <div
                          className="w-8 h-8 rounded border border-gray-600 mx-auto mb-1"
                          style={{ backgroundColor: color }}
                        />
                        <p className="text-xs text-gray-400">{index}</p>
                        <p className="text-xs">{color}</p>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })()}
          </div>
        </div>

        {/* 不同顏色類型的對比 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">不同顏色類型對比:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {colorTypes.map(type => {
              const colors = getAvailableColorsForColorType(type);
              return (
                <div key={type} className="bg-gray-800 p-4 rounded">
                  <h4 className="font-medium mb-2">{type}</h4>
                  <p className="text-sm text-gray-400 mb-2">{colors.length} 種顏色</p>
                  <div className="flex flex-wrap gap-1">
                    {colors.slice(0, 12).map(color => (
                      <div
                        key={color}
                        className="w-6 h-6 rounded border border-gray-600"
                        style={{ backgroundColor: color }}
                        title={color}
                      />
                    ))}
                    {colors.length > 12 && (
                      <div className="w-6 h-6 rounded border border-gray-600 bg-gray-600 flex items-center justify-center">
                        <span className="text-xs">+{colors.length - 12}</span>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* 使用說明 */}
        <div className="bg-gray-800 p-4 rounded">
          <h3 className="text-lg font-medium mb-2">使用說明:</h3>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• 選擇不同的顏色類型來查看可用顏色</li>
            <li>• 點擊顏色選擇器中的顏色按鈕來選擇顏色</li>
            <li>• Gray16 提供 16 個灰度級別</li>
            <li>• BWR 提供黑、白、紅三種顏色</li>
            <li>• BWRY 提供黑、白、紅、黃四種顏色</li>
            <li>• 所有顏色選擇器都支援透明選項</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ColorTool;
