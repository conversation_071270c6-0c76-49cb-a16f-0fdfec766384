# EPD-Manager 多選功能調用流程

本文檔整理了EPD-Manager中多選功能的完整調用流程，用於開發參考和維護。

## 目錄

1. [狀態管理](#狀態管理)
2. [圈選操作流程](#圈選操作流程)
3. [單擊選擇流程](#單擊選擇流程)
4. [Shift+點擊多選流程](#shift點擊多選流程)
5. [多選移動流程](#多選移動流程)
6. [對齊與分佈操作流程](#對齊與分佈操作流程)
7. [多選狀態防護機制](#多選狀態防護機制)
8. [最佳實踐與注意事項](#最佳實踐與注意事項)

## 狀態管理

多選功能在 `useEditorState` 中管理以下相關狀態：

```typescript
// 多選功能 - 新增多選元素的狀態
const [selectedElementIds, setSelectedElementIds] = useState<string[]>([]);
const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);
const [selectionBox, setSelectionBox] = useState<{startX: number; startY: number; endX: number; endY: number} | null>(null);

// 新增多選移動狀態追蹤
const [isMultiMoving, setIsMultiMoving] = useState(false);
// 使用 ref 防止觸發誤選的時間窗口 (300ms)
const preventSelectTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
```

**關鍵狀態說明**：

- `selectedElementIds`: 存儲多個選中元素的ID數組，類型為 `string[]`
- `isMultiSelectMode`: 標記是否處於多選模式，類型為 `boolean`
- `selectionBox`: 存儲圈選框的起始座標和結束座標，類型為 `{startX: number; startY: number; endX: number; endY: number} | null`
- `isMultiMoving`: 標記是否正在進行多選元素的移動，類型為 `boolean`
- `preventSelectTimeoutRef`: 用於防止多選移動後立即觸發選擇的時間窗口參考，類型為 `ReturnType<typeof setTimeout> | null`

## 圈選操作流程

圈選是使用滑鼠拖曳選擇多個元素的操作。

### 1. 啟動圈選

在 `Canvas.tsx` 中的 `handleMultiSelectMouseDown` 函數：

```typescript
const handleMultiSelectMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
  // 如果點擊了現有元素但沒有按Shift，不進行圈選
  if (clickedOnElement && !e.shiftKey) return;
  // 如果選擇了工具，不執行圈選操作
  if (selectedTool) return;

  // 計算相對於畫布的坐標
  const rect = canvasRef.current.getBoundingClientRect();
  const x = (e.clientX - rect.left) / (zoom / 100);
  const y = (e.clientY - rect.top) / (zoom / 100);
  
  // 初始化選擇框
  setSelectionBox({ startX: x, startY: y, endX: x, endY: y });
  setIsMultiSelectMode(true);
  setIsDrawing(true);
  setStartPoint({ x, y });
}
```

### 2. 圈選過程

在 `Canvas.tsx` 中的 `handleMultiSelectMouseMove` 函數：

```typescript
const handleMultiSelectMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
  if (!isMultiSelectMode || !isDrawing || !selectionBox) return;

  // 計算相對於畫布的坐標
  const rect = canvasRef.current.getBoundingClientRect();
  const x = (e.clientX - rect.left) / (zoom / 100);
  const y = (e.clientY - rect.top) / (zoom / 100);

  // 更新選擇框
  setSelectionBox({ ...selectionBox, endX: x, endY: y });
}
```

### 3. 完成圈選

在 `Canvas.tsx` 中的 `handleMultiSelectMouseUp` 函數：

```typescript
const handleMultiSelectMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
  if (!isMultiSelectMode || !isDrawing || !selectionBox) return;
  
  setIsDrawing(false);

  // 處理選擇框內的元素
  if (selectionBox.startX !== selectionBox.endX || selectionBox.startY !== selectionBox.endY) {
    selectElementsByBox(selectionBox);
  }

  // 清除選擇框 - 僅清除視覺框，不影響選擇狀態
  setSelectionBox(null);
}
```

### 4. 選擇元素

在 `useEditorState.tsx` 中的 `selectElementsByBox` 函數：

```typescript
const selectElementsByBox = (box: {startX: number; startY: number; endX: number; endY: number}) => {
  const minX = Math.min(box.startX, box.endX);
  const maxX = Math.max(box.startX, box.endX);
  const minY = Math.min(box.startY, box.endY);
  const maxY = Math.max(box.startY, box.endY);

  // 使用重疊檢測算法找出選擇框內的元素
  const selectedIds = localElements.filter(element => {
    const elementMinX = element.x;
    const elementMaxX = element.x + element.width;
    const elementMinY = element.y;
    const elementMaxY = element.y + element.height;
    
    // 檢測矩形重疊
    const isOverlapping = !(
      elementMaxX < minX || // 元素在選擇框左側
      elementMinX > maxX || // 元素在選擇框右側
      elementMaxY < minY || // 元素在選擇框上方
      elementMinY > maxY    // 元素在選擇框下方
    );
    
    return isOverlapping;
  }).map(element => element.id);

  if (selectedIds.length === 0) return;
  
  // 根據選中元素的數量設置選中狀態
  if (selectedIds.length === 1) {
    setSelectedElementId(selectedIds[0]);
  } else {
    setSelectedElementId(null);
  }
  
  setSelectedElementIds(selectedIds);
  setIsMultiSelectMode(selectedIds.length > 1);
}
```

## 單擊選擇流程

單擊選擇是點擊單個元素進行選擇的操作。

### 1. 元素的點擊事件

各元素組件（如 `LineElement`, `RectangleElement`, `ElementRenderer`）中的 `onClick` 處理：

```typescript
onClick={(e) => {
  e.stopPropagation();
  
  // 如果正在多選移動保護時間內，忽略點擊
  if (isMultiMoving) {
    console.log('處於多選移動狀態，忽略選擇操作');
    return;
  }
  
  // 如果按下Shift鍵，則調用toggleElementSelection
  if (e.shiftKey && selectedElementIds.length > 0) {
    onSelect(element.id, e); // 會由父組件調用toggleElementSelection
  } else {
    // 普通點擊，選中單個元素
    onSelect(element.id, e);
  }
}}
```

### 2. 元素選擇處理

在 `Canvas.tsx` 中的元素選擇處理：

```typescript
onSelect={(id, e) => {
  // 修正多選邏輯 - 如果按下Shift且已有選中元素，則切換選擇狀態
  if (e && e.shiftKey && selectedElementIds.length > 0) {
    toggleElementSelection(id);
  } else {
    // 如果沒有按Shift，則單選該元素
    setSelectedElementId(id);
    setSelectedElementIds([id]);
  }
}}
```

### 3. 清除選擇

當點擊畫布空白處時，在 `Canvas.tsx` 的 `onClick` 事件中：

```typescript
<div
  ref={canvasRef}
  className="relative overflow-auto bg-white rounded-lg shadow-inner"
  onClick={(e) => {
    if (e.target === canvasRef.current) {
      clearSelection();
    }
  }}
  ...
>
```

在 `useEditorState.tsx` 中的 `clearSelection` 函數：

```typescript
const clearSelection = () => {
  // 如果處於多選移動保護時間窗口內，不處理清除選擇
  if (preventSelectTimeoutRef.current) {
    console.log('處於多選移動保護狀態，忽略清除選擇操作');
    return;
  }
  
  setSelectedElementId(null);
  setSelectedElementIds([]);
};
```

## Shift+點擊多選流程

Shift+點擊是在保持現有選擇的基礎上，添加或移除元素的操作。

### 1. 切換元素選擇狀態

在 `useEditorState.tsx` 中的 `toggleElementSelection` 函數：

```typescript
const toggleElementSelection = (id: string) => {
  // 如果處於多選移動保護時間窗口內，不處理單擊選擇
  if (preventSelectTimeoutRef.current) {
    console.log('處於多選移動保護狀態，忽略選擇操作');
    return;
  }

  setSelectedElementIds(prev => {
    if (prev.includes(id)) {
      // 如果元素已在選中列表中，則移除
      const newIds = prev.filter(eId => eId !== id);
      // 如果剩下一個元素，則設置為單選
      if (newIds.length === 1) {
        setSelectedElementId(newIds[0]);
      }
      return newIds;
    } else {
      // 否則添加到選擇列表
      return [...prev, id];
    }
  });
};
```

### 2. Shift+點擊處理

在 `Canvas.tsx` 中的 `handleMultiSelectMouseDown` 函數處理 Shift+點擊：

```typescript
if (e.shiftKey && clickedOnElement) {
  const elementId = clickedOnElement.getAttribute('data-element-id');
  if (elementId) {
    e.stopPropagation();
    toggleElementSelection(elementId);
    return;
  }
}
```

## 多選移動流程

多選移動是同時移動多個選中元素的操作。

### 1. 元素的拖曳處理

在各元素組件（如 `LineElement`, `RectangleElement`, `ElementRenderer`）中的拖曳處理：

```typescript
const handleMouseMove = (e: MouseEvent) => {
  if (isDragging) {
    // 元素拖曳移動
    const deltaX = e.clientX - startDragPosition.x;
    const deltaY = e.clientY - startDragPosition.y;
    
    // 根據縮放比例調整移動量
    const scaledDeltaX = deltaX / (zoom / 100);
    const scaledDeltaY = deltaY / (zoom / 100);
    
    // 多選狀態下移動所有選中元素
    if (isMultiSelected && moveSelectedElements) {
      moveSelectedElements(scaledDeltaX, scaledDeltaY);
    } else {
      // 單選狀態下只移動當前元素
      // ...單元素移動代碼...
    }
    
    setStartDragPosition({ x: e.clientX, y: e.clientY });
  }
};
```

### 2. 多選元素移動處理

在 `useEditorState.tsx` 中的 `moveSelectedElements` 函數：

```typescript
const moveSelectedElements = (dx: number, dy: number) => {
  if (selectedElementIds.length === 0) return;

  // 標記開始多選移動
  setIsMultiMoving(true);
  
  const updatedElements = localElements.map(element => {
    if (!selectedElementIds.includes(element.id)) return element;
    
    return {
      ...element,
      x: element.x + dx,
      y: element.y + dy
    };
  });

  setLocalElements(updatedElements);

  // 更新全局模板狀態
  if (selectedTemplate) {
    updateTemplate({
      ...selectedTemplate,
      elements: updatedElements
    });
  }
  
  // 清除先前的計時器（如果存在）
  if (preventSelectTimeoutRef.current) {
    clearTimeout(preventSelectTimeoutRef.current);
  }
  
  // 設置計時器，在多選移動結束後300毫秒內防止觸發單選
  preventSelectTimeoutRef.current = setTimeout(() => {
    setIsMultiMoving(false);
    preventSelectTimeoutRef.current = null;
    console.log('多選移動保護時間結束');
  }, 300);
};
```

## 對齊與分佈操作流程

多選元素可以進行對齊和分佈操作。

### 1. 對齊操作

在 `useEditorState.tsx` 中的 `alignSelectedElements` 函數：

```typescript
const alignSelectedElements = (alignType: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom') => {
  if (selectedElementIds.length <= 1) return;

  const selectedElements = localElements.filter(el => selectedElementIds.includes(el.id));
  
  // 計算邊界值
  let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
  let sumX = 0, sumY = 0;
  
  selectedElements.forEach(el => {
    minX = Math.min(minX, el.x);
    maxX = Math.max(maxX, el.x + el.width);
    minY = Math.min(minY, el.y);
    maxY = Math.max(maxY, el.y + el.height);
    sumX += el.x + el.width / 2;
    sumY += el.y + el.height / 2;
  });

  const centerX = sumX / selectedElements.length;
  const centerY = sumY / selectedElements.length;

  const updatedElements = localElements.map(element => {
    if (!selectedElementIds.includes(element.id)) return element;

    const updates: Partial<TemplateElement> = {};
    
    switch (alignType) {
      case 'left':
        updates.x = minX;
        break;
      case 'center':
        updates.x = minX + (maxX - minX) / 2 - element.width / 2;
        break;
      case 'right':
        updates.x = maxX - element.width;
        break;
      case 'top':
        updates.y = minY;
        break;
      case 'middle':
        updates.y = minY + (maxY - minY) / 2 - element.height / 2;
        break;
      case 'bottom':
        updates.y = maxY - element.height;
        break;
    }

    return { ...element, ...updates };
  });

  setLocalElements(updatedElements);
  updateTemplate({
    ...selectedTemplate,
    elements: updatedElements
  });
};
```

### 2. 分佈操作

在 `useEditorState.tsx` 中的 `distributeSelectedElements` 函數：

```typescript
const distributeSelectedElements = (distributeType: 'horizontal' | 'vertical') => {
  if (selectedElementIds.length <= 2) return;

  const selectedElements = localElements.filter(el => selectedElementIds.includes(el.id))
    .sort((a, b) => {
      if (distributeType === 'horizontal') {
        return a.x - b.x;
      } else {
        return a.y - b.y;
      }
    });

  // 計算總空間和間距
  let totalSpace, totalGap;
  if (distributeType === 'horizontal') {
    const firstElement = selectedElements[0];
    const lastElement = selectedElements[selectedElements.length - 1];
    totalSpace = (lastElement.x + lastElement.width) - firstElement.x;
    totalGap = totalSpace - selectedElements.reduce((sum, el) => sum + el.width, 0);
  } else {
    const firstElement = selectedElements[0];
    const lastElement = selectedElements[selectedElements.length - 1];
    totalSpace = (lastElement.y + lastElement.height) - firstElement.y;
    totalGap = totalSpace - selectedElements.reduce((sum, el) => sum + el.height, 0);
  }

  const gapSize = totalGap / (selectedElements.length - 1);
  
  const updatedElements = localElements.map(element => {
    const index = selectedElements.findIndex(sel => sel.id === element.id);
    if (index === -1 || index === 0 || index === selectedElements.length - 1) {
      return element;
    }

    const updates: Partial<TemplateElement> = {};
    
    if (distributeType === 'horizontal') {
      let expectedPosition = selectedElements[0].x;
      for (let i = 0; i < index; i++) {
        expectedPosition += selectedElements[i].width + gapSize;
      }
      updates.x = expectedPosition;
    } else {
      let expectedPosition = selectedElements[0].y;
      for (let i = 0; i < index; i++) {
        expectedPosition += selectedElements[i].height + gapSize;
      }
      updates.y = expectedPosition;
    }

    return { ...element, ...updates };
  });

  setLocalElements(updatedElements);
  updateTemplate({
    ...selectedTemplate,
    elements: updatedElements
  });
};
```

## 多選狀態防護機制

為了防止多選移動後立即誤觸發選擇操作，使用了時間窗口防護機制。

### 1. 設置防護時間窗口

在 `moveSelectedElements` 函數結尾處：

```typescript
// 設置計時器，在多選移動結束後300毫秒內防止觸發單選
preventSelectTimeoutRef.current = setTimeout(() => {
  setIsMultiMoving(false);
  preventSelectTimeoutRef.current = null;
  console.log('多選移動保護時間結束');
}, 300);
```

### 2. 在選擇操作中檢查防護狀態

在 `toggleElementSelection` 和 `clearSelection` 函數開頭：

```typescript
// 如果處於多選移動保護時間窗口內，不處理單擊選擇
if (preventSelectTimeoutRef.current) {
  console.log('處於多選移動保護狀態，忽略選擇操作');
  return;
}
```

### 3. 在元素點擊事件中檢查防護狀態

在元素的 `onClick` 處理函數中：

```typescript
// 如果正在進行多選移動，則不處理點擊選擇事件
if (isMultiMoving) {
  console.log('處於多選移動狀態，忽略選擇操作');
  return;
}
```

## 最佳實踐與注意事項

1. **狀態一致性** - 確保 `selectedElementIds` 與 `selectedElementId` 之間的狀態一致。當 `selectedElementIds.length === 1` 時，`selectedElementId` 應當設置為該元素ID。

2. **防護機制** - 多選移動後的 300ms 防護時間窗口是防止誤觸發選擇操作的關鍵機制，請勿移除。

3. **性能考量** - 當選中大量元素時，請注意更新操作的性能影響，可以考慮將 `selectedElementIds` 轉換為 Set 結構以優化查找效率。

4. **元素邊界計算** - 對齊和分佈操作需要準確計算元素的邊界，請確保元素的 x, y, width, height 屬性是準確的。

5. **圈選判定** - 改進矩形重疊檢測算法可以提高圈選的準確性，可考慮添加對部分重疊的支持。

這樣的防護機制確保了多選移動操作和選擇操作之間不會產生衝突，提高了用戶體驗。