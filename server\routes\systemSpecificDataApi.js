const express = require('express');
const router = express.Router();
const { ObjectId } = require('mongodb');
const { authenticate, checkPermission } = require('../middleware/auth');
const { broadcastSystemDataUpdate } = require('../services/websocketService');

// MongoDB 連接信息
const systemSpecificDataCollectionName = 'systemSpecificData';
const dataFieldsCollection = 'dataFields';

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取數據集合
const getCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();

  // 使用 systemSpecificData 集合
  const systemSpecificDataCollection = db.collection(systemSpecificDataCollectionName);
  const dataFieldsCol = db.collection(dataFieldsCollection);
  return { systemSpecificDataCollection, dataFieldsCol, client };
};

// 獲取所有系統專屬數據
router.get('/systemSpecificData', authenticate, checkPermission(['system:view', 'system-data:view']), async (req, res) => {
  try {
    const { systemSpecificDataCollection } = await getCollection();

    console.log('獲取系統專屬數據請求');

    // 獲取所有系統專屬數據
    const systemSpecificData = await systemSpecificDataCollection.find().toArray();
    
    console.log(`找到 ${systemSpecificData.length} 筆系統專屬數據`);
    res.json(systemSpecificData);
  } catch (error) {
    console.error('獲取系統專屬數據失敗:', error);
    res.status(500).json({ error: '獲取系統專屬數據失敗' });
  }
});

// 獲取單個系統專屬數據
router.get('/systemSpecificData/:uid', authenticate, checkPermission(['system:view', 'system-data:view']), async (req, res) => {
  try {
    const { uid } = req.params;
    const { systemSpecificDataCollection } = await getCollection();

    // 查找指定 uid 的系統專屬數據
    const systemSpecificDataItem = await systemSpecificDataCollection.findOne({ uid });

    if (!systemSpecificDataItem) {
      return res.status(404).json({ error: '系統專屬數據不存在' });
    }

    // 返回系統專屬數據項目
    res.json(systemSpecificDataItem);
  } catch (error) {
    console.error('獲取系統專屬數據失敗:', error);
    res.status(500).json({ error: '獲取系統專屬數據失敗' });
  }
});

// 創建系統專屬數據
router.post('/systemSpecificData', authenticate, checkPermission(['system:create', 'system-data:create']), async (req, res) => {
  try {
    // 從請求體中獲取數據
    const systemSpecificData = req.body;

    const { systemSpecificDataCollection, dataFieldsCol } = await getCollection();

    // 檢查 ID 是否已存在
    if (systemSpecificData.id) {
      const idExists = await systemSpecificDataCollection.findOne({ id: systemSpecificData.id });

      if (idExists) {
        return res.status(400).json({
          error: '系統專屬數據 ID 已存在',
          message: `ID "${systemSpecificData.id}" 已存在，請使用其他 ID`,
          code: 'DUPLICATE_SYSTEM_SPECIFIC_DATA_ID',
          field: 'id'
        });
      }
    }

    // 獲取一般資料欄位定義
    const dataFields = await dataFieldsCol.find({ section: "ordinary" }).toArray();

    // 生成唯一識別碼
    const uid = new ObjectId().toString();

    // 初始化系統專屬數據
    const newSystemSpecificData = {
      _id: new ObjectId(),
      uid: uid
    };

    // 根據資料欄位定義添加欄位
    dataFields.forEach(field => {
      newSystemSpecificData[field.id] = systemSpecificData[field.id] !== undefined ? systemSpecificData[field.id] : null;
    });

    // 插入到系統專屬數據集合
    await systemSpecificDataCollection.insertOne(newSystemSpecificData);

    // 推送WebSocket事件
    try {
      broadcastSystemDataUpdate([{
        uid: newSystemSpecificData.uid,
        data: newSystemSpecificData,
        updatedFields: Object.keys(newSystemSpecificData).filter(key => key !== '_id' && key !== 'uid')
      }], 'create');
    } catch (wsError) {
      console.error('推送系統資料創建事件失敗:', wsError);
      // 不影響API響應
    }

    // 返回新創建的系統專屬數據
    res.status(201).json(newSystemSpecificData);
  } catch (error) {
    console.error('創建系統專屬數據失敗:', error);
    res.status(500).json({ error: '創建系統專屬數據失敗' });
  }
});

// 更新系統專屬數據
router.put('/systemSpecificData/:uid', authenticate, checkPermission(['system:update', 'system-data:update']), async (req, res) => {
  try {
    const { uid } = req.params;
    const updateData = req.body;

    const { systemSpecificDataCollection } = await getCollection();

    // 檢查系統專屬數據是否存在
    const systemSpecificDataItem = await systemSpecificDataCollection.findOne({ uid });

    if (!systemSpecificDataItem) {
      return res.status(404).json({ error: '系統專屬數據不存在' });
    }

    // 如果更新包含 ID 字段，檢查 ID 是否已存在於其他數據項中
    if (updateData.id && updateData.id !== systemSpecificDataItem.id) {
      // 檢查是否有相同的 ID
      const idExists = await systemSpecificDataCollection.findOne({ 
        id: updateData.id,
        uid: { $ne: uid } // 排除當前項目
      });

      if (idExists) {
        return res.status(400).json({
          error: '系統專屬數據 ID 已存在',
          message: `ID "${updateData.id}" 已存在，請使用其他 ID`,
          code: 'DUPLICATE_SYSTEM_SPECIFIC_DATA_ID',
          field: 'id'
        });
      }
    }

    // 更新系統專屬數據
    const updatedItem = {
      ...systemSpecificDataItem,
      ...updateData
    };

    // 使用 $set 更新文檔
    await systemSpecificDataCollection.updateOne(
      { uid },
      { $set: updatedItem }
    );

    // 獲取更新後的系統專屬數據
    const updatedSystemSpecificData = await systemSpecificDataCollection.findOne({ uid });

    // 推送WebSocket事件
    try {
      broadcastSystemDataUpdate([{
        uid: updatedSystemSpecificData.uid,
        data: updatedSystemSpecificData,
        updatedFields: Object.keys(updateData)
      }], 'update');
    } catch (wsError) {
      console.error('推送系統資料更新事件失敗:', wsError);
      // 不影響API響應
    }

    // 返回更新後的系統專屬數據項目
    res.json(updatedSystemSpecificData);
  } catch (error) {
    console.error('更新系統專屬數據失敗:', error);
    res.status(500).json({ error: '更新系統專屬數據失敗' });
  }
});

// 刪除系統專屬數據
router.delete('/systemSpecificData/:uid', authenticate, checkPermission(['system:delete', 'system-data:delete']), async (req, res) => {
  try {
    const { uid } = req.params;

    const { systemSpecificDataCollection } = await getCollection();

    // 檢查系統專屬數據是否存在
    const systemSpecificDataItem = await systemSpecificDataCollection.findOne({ uid });

    if (!systemSpecificDataItem) {
      return res.status(404).json({ error: '系統專屬數據不存在' });
    }

    // 刪除系統專屬數據
    await systemSpecificDataCollection.deleteOne({ uid });

    // 推送WebSocket事件
    try {
      broadcastSystemDataUpdate([{
        uid: systemSpecificDataItem.uid,
        data: systemSpecificDataItem,
        updatedFields: []
      }], 'delete');
    } catch (wsError) {
      console.error('推送系統資料刪除事件失敗:', wsError);
      // 不影響API響應
    }

    res.status(204).send();
  } catch (error) {
    console.error('刪除系統專屬數據失敗:', error);
    res.status(500).json({ error: '刪除系統專屬數據失敗' });
  }
});

// 同步資料欄位到系統專屬數據結構
router.post('/systemSpecificData/sync-fields', authenticate, checkPermission(['system:update', 'system-data:update']), async (_, res) => {
  try {
    const { systemSpecificDataCollection, dataFieldsCol } = await getCollection();

    // 獲取一般資料欄位
    const dataFields = await dataFieldsCol.find({ section: "ordinary" }).toArray();

    // 獲取所有系統專屬數據
    const systemSpecificData = await systemSpecificDataCollection.find().toArray();
    let totalUpdatedItems = 0;

    // 遍歷每個系統專屬數據項目
    for (const item of systemSpecificData) {
      let updated = false;
      const updates = {};

      // 檢查每個資料欄位是否存在於系統專屬數據中
      for (const field of dataFields) {
        if (item[field.id] === undefined) {
          updates[field.id] = null;
          updated = true;
        }
      }

      // 如果有需要更新的欄位，執行更新
      if (updated) {
        await systemSpecificDataCollection.updateOne(
          { _id: item._id },
          { $set: updates }
        );
        totalUpdatedItems++;
      }
    }

    res.json({
      success: true,
      message: `已同步 ${totalUpdatedItems} 筆系統專屬數據與 ${dataFields.length} 個資料欄位`,
      lastSynced: new Date().toISOString()
    });
  } catch (error) {
    console.error('同步資料欄位失敗:', error);
    res.status(500).json({ error: '同步資料欄位失敗' });
  }
});

// 導出路由器和初始化函數
module.exports = { router, initDB };
