import React, { useEffect, useState } from 'react';

/**
 * 電路板背景組件
 * 創建一個具有電路板效果的SVG背景，從中間向外發散
 */
export const CircuitBackground: React.FC = () => {
  // 添加一些隨機閃爍的節點效果
  const [flashingNodes, setFlashingNodes] = useState<number[]>([]);
  // 添加脈衝效果
  const [pulseEffect, setPulseEffect] = useState(false);

  useEffect(() => {
    // 每隔一段時間隨機選擇一些節點進行閃爍
    const nodeInterval = setInterval(() => {
      const nodeCount = 36; // 總節點數 (增加了更多節點)
      const flashCount = Math.floor(Math.random() * 8) + 5; // 每次閃爍5-12個節點
      const newFlashingNodes: number[] = [];

      // 確保至少有一個主要路徑上的節點閃爍
      newFlashingNodes.push(Math.floor(Math.random() * 8)); // 主要8個方向的節點

      // 確保至少有一個次要路徑上的節點閃爍
      newFlashingNodes.push(8 + Math.floor(Math.random() * 8)); // 次要路徑的節點

      // 隨機選擇其他節點
      for (let i = 0; i < flashCount - 2; i++) {
        newFlashingNodes.push(Math.floor(Math.random() * nodeCount));
      }

      setFlashingNodes(newFlashingNodes);
    }, 600); // 縮短間隔，使閃爍更頻繁

    // 每隔一段時間觸發脈衝效果
    const pulseInterval = setInterval(() => {
      setPulseEffect(true);
      setTimeout(() => setPulseEffect(false), 800);
    }, 2500); // 縮短間隔，使脈衝更頻繁

    return () => {
      clearInterval(nodeInterval);
      clearInterval(pulseInterval);
    };
  }, []);

  return (
    <div className="circuit-background absolute inset-0 overflow-hidden" style={{ zIndex: 0, pointerEvents: 'none' }}>
      {/* 脈衝效果 - 從中心向外擴散的光環 */}
      {pulseEffect && (
        <>
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full bg-sky-400/5 animate-pulse-wave"></div>
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[400px] h-[400px] rounded-full bg-sky-400/10 animate-pulse-wave" style={{ animationDelay: '0.2s' }}></div>
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[200px] h-[200px] rounded-full bg-sky-400/15 animate-pulse-wave" style={{ animationDelay: '0.4s' }}></div>
        </>
      )}

      <svg
        className="absolute inset-0 w-full h-full opacity-50"
        xmlns="http://www.w3.org/2000/svg"
        width="100%"
        height="100%"
        viewBox="0 0 1000 1000"
        preserveAspectRatio="xMidYMid slice"
      >
        {/* 同心圓 - 代表電路板的基本結構 */}
        <circle cx="500" cy="500" r="450" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" fill="none" />
        <circle cx="500" cy="500" r="350" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" fill="none" />
        <circle cx="500" cy="500" r="250" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" fill="none" />
        <circle cx="500" cy="500" r="150" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" fill="none" />

        {/* 放射狀線條 - 從中心向外 */}
        <path d="M 500,500 L 500,0" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" />
        <path d="M 500,500 L 500,1000" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" />
        <path d="M 500,500 L 0,500" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" />
        <path d="M 500,500 L 1000,500" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" />
        <path d="M 500,500 L 0,0" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" />
        <path d="M 500,500 L 1000,1000" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" />
        <path d="M 500,500 L 0,1000" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" />
        <path d="M 500,500 L 1000,0" stroke="currentColor" strokeWidth="1" className="text-sky-700/30" />

        {/* 主要電路連接線 - 8個方向 */}
        {/* 上方 */}
        <path
          d="M 500,500 L 500,450 L 400,450 L 400,350 L 300,350 L 300,250 L 200,250 L 200,150 L 100,150 L 100,50 L 0,50"
          stroke="currentColor"
          strokeWidth="3"
          fill="none"
          className="text-sky-400 circuit-path"
        />

        {/* 右上方 */}
        <path
          d="M 500,500 L 550,450 L 600,450 L 600,350 L 700,350 L 700,250 L 800,250 L 800,150 L 900,150"
          stroke="currentColor"
          strokeWidth="3"
          fill="none"
          className="text-sky-400 circuit-path"
        />

        {/* 右方 */}
        <path
          d="M 500,500 L 550,500 L 650,500 L 750,500 L 850,500 L 950,500"
          stroke="currentColor"
          strokeWidth="3"
          fill="none"
          className="text-sky-400 circuit-path"
        />

        {/* 右下方 */}
        <path
          d="M 500,500 L 550,550 L 600,550 L 600,650 L 700,650 L 700,750 L 800,750 L 800,850 L 900,850"
          stroke="currentColor"
          strokeWidth="3"
          fill="none"
          className="text-sky-400 circuit-path"
        />

        {/* 下方 */}
        <path
          d="M 500,500 L 500,550 L 500,650 L 500,750 L 500,850 L 500,950"
          stroke="currentColor"
          strokeWidth="3"
          fill="none"
          className="text-sky-400 circuit-path"
        />

        {/* 左下方 */}
        <path
          d="M 500,500 L 450,550 L 400,550 L 400,650 L 300,650 L 300,750 L 200,750 L 200,850 L 100,850"
          stroke="currentColor"
          strokeWidth="3"
          fill="none"
          className="text-sky-400 circuit-path"
        />

        {/* 左方 */}
        <path
          d="M 500,500 L 450,500 L 350,500 L 250,500 L 150,500 L 50,500"
          stroke="currentColor"
          strokeWidth="3"
          fill="none"
          className="text-sky-400 circuit-path"
        />

        {/* 左上方 */}
        <path
          d="M 500,500 L 450,450 L 400,450 L 400,350 L 300,350 L 300,250 L 200,250 L 100,250 L 50,250"
          stroke="currentColor"
          strokeWidth="3"
          fill="none"
          className="text-sky-400 circuit-path"
        />

        {/* 次要電路連接線 - 斜向 */}
        <path
          d="M 500,500 L 530,470 L 560,440 L 590,410 L 620,380 L 650,350 L 680,320 L 710,290"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        <path
          d="M 500,500 L 470,470 L 440,440 L 410,410 L 380,380 L 350,350 L 320,320 L 290,290"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        <path
          d="M 500,500 L 530,530 L 560,560 L 590,590 L 620,620 L 650,650 L 680,680 L 710,710"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        <path
          d="M 500,500 L 470,530 L 440,560 L 410,590 L 380,620 L 350,650 L 320,680 L 290,710"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        {/* 額外的電路連接線 - 複雜路徑 */}
        <path
          d="M 500,500 L 450,480 L 400,480 L 400,400 L 350,400 L 350,350 L 300,350 L 300,300 L 250,300 L 200,300 L 150,300"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        <path
          d="M 500,500 L 550,480 L 600,480 L 600,400 L 650,400 L 650,350 L 700,350 L 700,300 L 750,300 L 800,300 L 850,300"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        <path
          d="M 500,500 L 450,520 L 400,520 L 400,600 L 350,600 L 350,650 L 300,650 L 300,700 L 250,700 L 200,700 L 150,700"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        <path
          d="M 500,500 L 550,520 L 600,520 L 600,600 L 650,600 L 650,650 L 700,650 L 700,700 L 750,700 L 800,700 L 850,700"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        {/* 不規則複雜的電路路徑 - 第一組 */}
        <path
          d="M 500,500 L 520,480 L 540,460 L 560,440 L 580,460 L 600,440 L 620,460 L 640,440 L 660,420"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-200 circuit-path"
        />

        <path
          d="M 500,500 L 480,480 L 460,460 L 440,440 L 420,460 L 400,440 L 380,460 L 360,440 L 340,420"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-200 circuit-path"
        />

        <path
          d="M 500,500 L 520,520 L 540,540 L 560,560 L 580,540 L 600,560 L 620,540 L 640,560 L 660,580"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-200 circuit-path"
        />

        <path
          d="M 500,500 L 480,520 L 460,540 L 440,560 L 420,540 L 400,560 L 380,540 L 360,560 L 340,580"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-200 circuit-path"
        />

        {/* 不規則複雜的電路路徑 - 第二組（螺旋形） */}
        <path
          d="M 500,500 L 520,490 L 530,470 L 520,450 L 500,440 L 480,450 L 470,470 L 480,490 L 500,500"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        <path
          d="M 500,500 L 510,520 L 530,530 L 550,520 L 560,500 L 550,480 L 530,470 L 510,480 L 500,500"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        {/* 不規則複雜的電路路徑 - 第三組（曲線） */}
        <path
          d="M 500,500 Q 550,450 600,500 Q 650,550 700,500 Q 750,450 800,500"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-200 circuit-path"
        />

        <path
          d="M 500,500 Q 450,450 400,500 Q 350,550 300,500 Q 250,450 200,500"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-200 circuit-path"
        />

        <path
          d="M 500,500 Q 550,550 500,600 Q 450,650 500,700 Q 550,750 500,800"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-200 circuit-path"
        />

        <path
          d="M 500,500 Q 450,550 500,600 Q 550,650 500,700 Q 450,750 500,800"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-200 circuit-path"
        />

        {/* 不規則複雜的電路路徑 - 第四組（隨機路徑） */}
        <path
          d="M 500,500 L 530,510 L 520,540 L 550,560 L 530,590 L 560,610 L 540,640 L 570,660 L 550,690"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        <path
          d="M 500,500 L 470,510 L 480,540 L 450,560 L 470,590 L 440,610 L 460,640 L 430,660 L 450,690"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        <path
          d="M 500,500 L 530,490 L 520,460 L 550,440 L 530,410 L 560,390 L 540,360 L 570,340 L 550,310"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        <path
          d="M 500,500 L 470,490 L 480,460 L 450,440 L 470,410 L 440,390 L 460,360 L 430,340 L 450,310"
          stroke="currentColor"
          strokeWidth="1.5"
          fill="none"
          className="text-sky-300 circuit-path"
        />

        {/* 中心節點 - CPU */}
        <circle cx="500" cy="500" r="10" className="fill-sky-400 stroke-sky-300" strokeWidth="2" />

        {/* 節點 - 小圓圈 - 位於電路路徑上 */}
        <circle cx="400" cy="450" r="6" className={`${flashingNodes.includes(0) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="300" cy="350" r="6" className={`${flashingNodes.includes(1) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="200" cy="250" r="6" className={`${flashingNodes.includes(2) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="100" cy="150" r="6" className={`${flashingNodes.includes(3) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />

        <circle cx="600" cy="450" r="6" className={`${flashingNodes.includes(4) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="700" cy="350" r="6" className={`${flashingNodes.includes(5) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="800" cy="250" r="6" className={`${flashingNodes.includes(6) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />

        <circle cx="600" cy="550" r="6" className={`${flashingNodes.includes(7) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="700" cy="650" r="6" className={`${flashingNodes.includes(8) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="800" cy="750" r="6" className={`${flashingNodes.includes(9) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />

        <circle cx="400" cy="550" r="6" className={`${flashingNodes.includes(10) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="300" cy="650" r="6" className={`${flashingNodes.includes(11) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="200" cy="750" r="6" className={`${flashingNodes.includes(12) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />

        <circle cx="400" cy="400" r="6" className={`${flashingNodes.includes(13) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="300" cy="300" r="6" className={`${flashingNodes.includes(14) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="600" cy="600" r="6" className={`${flashingNodes.includes(15) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="700" cy="700" r="6" className={`${flashingNodes.includes(16) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />

        {/* 同心圓上的節點 */}
        <circle cx="500" cy="50" r="5" className={`${flashingNodes.includes(17) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="950" cy="500" r="5" className={`${flashingNodes.includes(18) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="500" cy="950" r="5" className={`${flashingNodes.includes(19) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="50" cy="500" r="5" className={`${flashingNodes.includes(0) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />

        {/* 新增的節點 - 位於新添加的電路路徑上 */}
        <circle cx="650" cy="500" r="4" className={`${flashingNodes.includes(1) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="850" cy="500" r="4" className={`${flashingNodes.includes(2) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="350" cy="500" r="4" className={`${flashingNodes.includes(3) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="150" cy="500" r="4" className={`${flashingNodes.includes(4) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />

        <circle cx="500" cy="650" r="4" className={`${flashingNodes.includes(5) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="500" cy="850" r="4" className={`${flashingNodes.includes(6) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="500" cy="350" r="4" className={`${flashingNodes.includes(7) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="500" cy="150" r="4" className={`${flashingNodes.includes(8) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />

        {/* 斜向路徑上的節點 */}
        <circle cx="590" cy="410" r="3" className={`${flashingNodes.includes(9) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="410" cy="410" r="3" className={`${flashingNodes.includes(10) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="590" cy="590" r="3" className={`${flashingNodes.includes(11) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />
        <circle cx="410" cy="590" r="3" className={`${flashingNodes.includes(12) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="1" />

        {/* 微型連接線上的節點 */}
        <circle cx="540" cy="460" r="2" className={`${flashingNodes.includes(13) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="0.5" />
        <circle cx="460" cy="460" r="2" className={`${flashingNodes.includes(14) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="0.5" />
        <circle cx="540" cy="540" r="2" className={`${flashingNodes.includes(15) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="0.5" />
        <circle cx="460" cy="540" r="2" className={`${flashingNodes.includes(16) ? 'fill-sky-300 animate-ping' : 'fill-sky-400'} stroke-sky-300`} strokeWidth="0.5" />

        {/* 中心CPU芯片 - 代表登入表單 */}
        <rect x="460" y="460" width="80" height="80" rx="3" className="fill-sky-500/30 stroke-sky-400" strokeWidth="2" />

        {/* 其他IC芯片 - 分布在電路板上 */}
        <rect x="250" y="200" width="60" height="30" rx="2" className="fill-sky-500/30 stroke-sky-400" strokeWidth="2" />
        <rect x="700" y="300" width="60" height="30" rx="2" className="fill-sky-500/30 stroke-sky-400" strokeWidth="2" />
        <rect x="200" y="700" width="60" height="30" rx="2" className="fill-sky-500/30 stroke-sky-400" strokeWidth="2" />
        <rect x="750" y="650" width="60" height="30" rx="2" className="fill-sky-500/30 stroke-sky-400" strokeWidth="2" />

        {/* 電阻元件 */}
        <rect x="350" y="150" width="30" height="10" rx="1" className="fill-sky-400/30 stroke-sky-300" strokeWidth="1" />
        <rect x="650" y="150" width="30" height="10" rx="1" className="fill-sky-400/30 stroke-sky-300" strokeWidth="1" />
        <rect x="350" y="850" width="30" height="10" rx="1" className="fill-sky-400/30 stroke-sky-300" strokeWidth="1" />
        <rect x="650" y="850" width="30" height="10" rx="1" className="fill-sky-400/30 stroke-sky-300" strokeWidth="1" />
        <rect x="150" y="350" width="10" height="30" rx="1" className="fill-sky-400/30 stroke-sky-300" strokeWidth="1" />
        <rect x="850" y="350" width="10" height="30" rx="1" className="fill-sky-400/30 stroke-sky-300" strokeWidth="1" />
        <rect x="150" y="650" width="10" height="30" rx="1" className="fill-sky-400/30 stroke-sky-300" strokeWidth="1" />
        <rect x="850" y="650" width="10" height="30" rx="1" className="fill-sky-400/30 stroke-sky-300" strokeWidth="1" />

        {/* 電容元件 */}
        <path d="M 400,200 L 400,190 M 400,210 L 400,220 M 395,200 L 405,200 M 395,210 L 405,210" stroke="currentColor" strokeWidth="1.5" className="text-sky-300" />
        <path d="M 600,200 L 600,190 M 600,210 L 600,220 M 595,200 L 605,200 M 595,210 L 605,210" stroke="currentColor" strokeWidth="1.5" className="text-sky-300" />
        <path d="M 400,800 L 400,790 M 400,810 L 400,820 M 395,800 L 405,800 M 395,810 L 405,810" stroke="currentColor" strokeWidth="1.5" className="text-sky-300" />
        <path d="M 600,800 L 600,790 M 600,810 L 600,820 M 595,800 L 605,800 M 595,810 L 605,810" stroke="currentColor" strokeWidth="1.5" className="text-sky-300" />

        {/* 晶體管元件 */}
        <path d="M 200,400 L 220,400 M 220,390 L 220,410 M 220,400 L 240,410 L 240,390 L 220,400" stroke="currentColor" strokeWidth="1.5" className="text-sky-300" />
        <path d="M 800,400 L 780,400 M 780,390 L 780,410 M 780,400 L 760,410 L 760,390 L 780,400" stroke="currentColor" strokeWidth="1.5" className="text-sky-300" />
        <path d="M 200,600 L 220,600 M 220,590 L 220,610 M 220,600 L 240,610 L 240,590 L 220,600" stroke="currentColor" strokeWidth="1.5" className="text-sky-300" />
        <path d="M 800,600 L 780,600 M 780,590 L 780,610 M 780,600 L 760,610 L 760,590 L 780,600" stroke="currentColor" strokeWidth="1.5" className="text-sky-300" />

        {/* 數據流動點 - 使用固定位置的點 */}
        <circle cx="500" cy="400" r="2" className="fill-sky-200 animate-data-flow-1" />
        <circle cx="600" cy="500" r="2" className="fill-sky-200 animate-data-flow-2" />
        <circle cx="500" cy="600" r="2" className="fill-sky-200 animate-data-flow-3" />
        <circle cx="400" cy="500" r="2" className="fill-sky-200 animate-data-flow-4" />

        {/* 額外的數據流動點 */}
        <circle cx="550" cy="450" r="2" className="fill-sky-300 animate-data-flow-5" />
        <circle cx="550" cy="550" r="2" className="fill-sky-300 animate-data-flow-6" />
        <circle cx="450" cy="550" r="2" className="fill-sky-300 animate-data-flow-7" />
        <circle cx="450" cy="450" r="2" className="fill-sky-300 animate-data-flow-8" />

        {/* 微型處理器 */}
        <rect x="300" y="300" width="40" height="40" rx="2" className="fill-sky-500/20 stroke-sky-400" strokeWidth="1" />
        <rect x="700" y="700" width="40" height="40" rx="2" className="fill-sky-500/20 stroke-sky-400" strokeWidth="1" />
        <rect x="300" y="700" width="40" height="40" rx="2" className="fill-sky-500/20 stroke-sky-400" strokeWidth="1" />
        <rect x="700" y="300" width="40" height="40" rx="2" className="fill-sky-500/20 stroke-sky-400" strokeWidth="1" />

        {/* 微型處理器內部結構 */}
        <path d="M 305,305 H 335 M 305,315 H 335 M 305,325 H 335 M 305,335 H 335" stroke="currentColor" strokeWidth="0.5" className="text-sky-300" />
        <path d="M 705,705 H 735 M 705,715 H 735 M 705,725 H 735 M 705,735 H 735" stroke="currentColor" strokeWidth="0.5" className="text-sky-300" />
        <path d="M 305,705 H 335 M 305,715 H 335 M 305,725 H 335 M 305,735 H 335" stroke="currentColor" strokeWidth="0.5" className="text-sky-300" />
        <path d="M 705,305 H 735 M 705,315 H 735 M 705,325 H 735 M 705,335 H 735" stroke="currentColor" strokeWidth="0.5" className="text-sky-300" />

        {/* 中心CPU微型電路圖案 */}
        <path
          d="M 470,470 H 490 M 480,470 V 490 M 510,470 H 530 M 520,470 V 490"
          stroke="currentColor"
          strokeWidth="1.5"
          className="text-sky-200"
        />
        <path
          d="M 470,510 H 490 M 480,510 V 530 M 510,510 H 530 M 520,510 V 530"
          stroke="currentColor"
          strokeWidth="1.5"
          className="text-sky-200"
        />

        {/* 其他IC芯片的微型電路圖案 */}
        <path
          d="M 260,210 H 280 M 270,210 V 220 M 290,210 H 300 M 295,210 V 220"
          stroke="currentColor"
          strokeWidth="1.5"
          className="text-sky-200"
        />

        <path
          d="M 710,310 H 730 M 720,310 V 320 M 740,310 H 750 M 745,310 V 320"
          stroke="currentColor"
          strokeWidth="1.5"
          className="text-sky-200"
        />

        <path
          d="M 210,710 H 230 M 220,710 V 720 M 240,710 H 250 M 245,710 V 720"
          stroke="currentColor"
          strokeWidth="1.5"
          className="text-sky-200"
        />

        {/* 中心CPU數字化效果 */}
        <text x="480" y="485" className="text-sky-300 text-[10px] font-mono font-bold">01</text>
        <text x="510" y="485" className="text-sky-300 text-[10px] font-mono font-bold">10</text>
        <text x="480" y="525" className="text-sky-300 text-[10px] font-mono font-bold">11</text>
        <text x="510" y="525" className="text-sky-300 text-[10px] font-mono font-bold">00</text>

        {/* 其他IC芯片的數字化效果 */}
        <text x="265" y="215" className="text-sky-300 text-[8px] font-mono font-bold">01</text>
        <text x="715" y="315" className="text-sky-300 text-[8px] font-mono font-bold">10</text>
        <text x="215" y="715" className="text-sky-300 text-[8px] font-mono font-bold">11</text>
        <text x="765" y="665" className="text-sky-300 text-[8px] font-mono font-bold">00</text>

        {/* 電路路徑上的數字化效果 */}
        <text x="350" y="400" className="text-sky-300 text-[8px] font-mono font-bold">10110</text>
        <text x="650" y="400" className="text-sky-300 text-[8px] font-mono font-bold">01001</text>
        <text x="350" y="600" className="text-sky-300 text-[8px] font-mono font-bold">11010</text>
        <text x="650" y="600" className="text-sky-300 text-[8px] font-mono font-bold">00101</text>

        {/* 同心圓上的數字化效果 */}
        <text x="500" y="150" className="text-sky-300 text-[8px] font-mono font-bold">10001</text>
        <text x="850" y="500" className="text-sky-300 text-[8px] font-mono font-bold">01110</text>
        <text x="500" y="850" className="text-sky-300 text-[8px] font-mono font-bold">11100</text>
        <text x="150" y="500" className="text-sky-300 text-[8px] font-mono font-bold">00011</text>

        {/* 發光效果 - 中心CPU */}
        <circle cx="500" cy="500" r="100" className="fill-sky-400/5" />
        <circle cx="500" cy="500" r="50" className="fill-sky-400/10" />
      </svg>
    </div>
  );
};
