import { TemplateElement } from '../../types';
import { buildEndpointUrl } from './apiConfig';

/**
 * QR Code 和 Barcode 元件預覽圖片生成 API
 */

export interface CodeElementPreviewOptions {
  element: TemplateElement;
  width: number;
  height: number;
  content?: string; // 可選的內容覆蓋
}

export interface CodeElementPreviewResponse {
  success: boolean;
  data?: string; // base64 圖片數據
  error?: string;
}

/**
 * 生成 QR Code 元件預覽圖片
 * @param options 預覽選項
 * @returns Promise<string | null> base64 圖片數據或 null
 */
export async function generateQRCodeElementPreview(options: CodeElementPreviewOptions): Promise<string | null> {
  try {
    const { element, width, height, content } = options;
    
    // 構建請求參數
    const requestData = {
      qrCodeType: element.qrCodeType || 'qrcode',
      content: content || element.codeContent || 'QR Code 內容',
      errorCorrectionLevel: element.errorCorrectionLevel || 'M',
      quietZone: element.quietZone || 4,
      moduleSize: element.moduleSize || 3,
      // foregroundColor 在後端已固定為黑色，不需要傳遞
      backgroundColor: element.fillColor || '#FFFFFF',
      width,
      height
    };

    console.log('生成 QR Code 預覽圖片:', requestData);

    const response = await fetch(buildEndpointUrl('code-preview/qr'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('QR Code 預覽 API 錯誤:', response.status, errorText);
      return null;
    }

    const result: CodeElementPreviewResponse = await response.json();
    
    if (!result.success || !result.data) {
      console.error('QR Code 預覽生成失敗:', result.error);
      return null;
    }

    console.log('QR Code 預覽圖片生成成功');
    return result.data;
  } catch (error) {
    console.error('生成 QR Code 預覽圖片失敗:', error);
    return null;
  }
}

/**
 * 生成 Barcode 元件預覽圖片
 * @param options 預覽選項
 * @returns Promise<string | null> base64 圖片數據或 null
 */
export async function generateBarcodeElementPreview(options: CodeElementPreviewOptions): Promise<string | null> {
  try {
    const { element, width, height, content } = options;

    // 構建請求參數
    const requestData = {
      barcodeType: element.barcodeType || 'code128',
      content: content || element.codeContent || '條碼內容',
      quietZone: element.quietZone || 10,
      // 條碼顏色不支援透明，如果是透明則使用黑色
      foregroundColor: (element.lineColor === 'transparent' || !element.lineColor) ? '#000000' : element.lineColor,
      backgroundColor: element.fillColor || '#FFFFFF',
      width,
      height,
      showText: element.showText === true // 傳遞文字顯示設置，預設為 false
    };

    console.log('生成 Barcode 預覽圖片:', requestData);

    const response = await fetch(buildEndpointUrl('code-preview/barcode'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Barcode 預覽 API 錯誤:', response.status, errorText);
      return null;
    }

    const result: CodeElementPreviewResponse = await response.json();
    
    if (!result.success || !result.data) {
      console.error('Barcode 預覽生成失敗:', result.error);
      return null;
    }

    console.log('Barcode 預覽圖片生成成功');
    return result.data;
  } catch (error) {
    console.error('生成 Barcode 預覽圖片失敗:', error);
    return null;
  }
}

/**
 * 根據元件類型生成預覽圖片
 * @param element 元件
 * @param content 可選的內容覆蓋
 * @returns Promise<string | null> base64 圖片數據或 null
 */
export async function generateCodeElementPreview(
  element: TemplateElement, 
  content?: string
): Promise<string | null> {
  const options: CodeElementPreviewOptions = {
    element,
    width: element.width,
    height: element.height,
    content
  };

  if (element.type === 'qr-code') {
    return generateQRCodeElementPreview(options);
  } else if (element.type === 'barcode') {
    return generateBarcodeElementPreview(options);
  } else {
    console.error('不支援的元件類型:', element.type);
    return null;
  }
}

/**
 * 更新元件的預覽圖片
 * @param element 元件
 * @param updateElement 更新元件的回調函數
 * @param content 可選的內容覆蓋
 * @returns Promise<boolean> 是否成功更新
 */
export async function updateElementPreviewImage(
  element: TemplateElement,
  updateElement: (updates: Partial<TemplateElement>) => void,
  content?: string
): Promise<boolean> {
  try {
    const previewImageUrl = await generateCodeElementPreview(element, content);
    
    if (previewImageUrl) {
      updateElement({ previewImageUrl });
      console.log('元件預覽圖片已更新');
      return true;
    } else {
      console.error('無法生成預覽圖片');
      return false;
    }
  } catch (error) {
    console.error('更新元件預覽圖片失敗:', error);
    return false;
  }
}
