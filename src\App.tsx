import { useState, Component, ErrorInfo, ReactNode, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import i18next from 'i18next';
import { TemplateList } from './components/TemplateList';
import { TemplateEditor } from './components/TemplateEditor';
import { AddTemplateModal } from './components/AddTemplateModal';
import { Sidebar } from './components/Sidebar';
import { EmptyPage } from './components/EmptyPage';
import { DatabasePage } from './components/DatabasePage';
import { StoreOverviewPage } from './components/StoreOverviewPage';
import { DevicesPage } from './components/DevicesPage';
import { GatewaysPage } from './components/GatewaysPage';
import DeviceDetailPage from './components/DeviceDetailPage';
import { StoreManagementPage } from './components/StoreManagementPage';
import { StoreSettingsPage } from './components/StoreSettingsPage';
import { useTemplateStore } from './store';
import { ArrowLeft, LogOut } from 'lucide-react';
import { SystemConfigPage } from './components/system-config/SystemConfigPage';
import { SystemSpecificDataPage } from './components/SystemSpecificDataPage';
import { SystemTemplatesPage } from './components/SystemTemplatesPage';
import { PermissionManagementPage } from './components/permission/PermissionManagementPage';
import { SystemLogsPage } from './components/SystemLogsPage';
import { LoginPage } from './components/auth/LoginPage';
import { ChangePasswordPage } from './components/auth/ChangePasswordPage';
import { FloatingActionButton } from './components/FloatingActionButton';
import { BugReportManagementPage } from './components/BugReportManagementPage';
import { PasswordProtectedPage } from './components/PasswordProtectedPage';
import { DevModeWrapper } from './components/DevModeWrapper';
import StoreSelector from './components/StoreSelector';
import { AuthGuard } from './components/auth/AuthGuard';
import { useAuthStore } from './store/authStore';
import { usePermissionStore } from './store/permissionStore';
import { useNavigationStore } from './store/navigationStore';
import { useThemeStore, initializeTheme } from './store/themeStore';
import { Store } from './types/store';
import ColorTool from './components/test/ColorTool';
import ThemeToggle from './components/ui/ThemeToggle';


// 錯誤邊界組件
class ErrorBoundary extends Component<{children: ReactNode, fallback?: ReactNode}> {
  state = { hasError: false, error: null };

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("組件錯誤:", error, errorInfo);
  }

  render() {
    // 使用 i18next 的 t 函數來翻譯文本
    // 注意: 在類組件中，我們不能直接使用 useTranslation 鉤子
    const { t } = i18next;

    if (this.state.hasError) {
      // 自定義錯誤顯示
      return this.props.fallback || (
        <div className="flex items-center justify-center h-screen bg-gray-100">
          <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
            <h2 className="text-2xl font-bold text-red-600 mb-4">{t('errors.error')}</h2>
            <p className="text-gray-600 mb-4">{t('errors.renderError')}:</p>
            <div className="bg-gray-100 p-4 rounded overflow-auto text-sm mb-4">
              {String(this.state.error)}
            </div>
            <button
              onClick={() => window.location.reload()}
              className="w-full py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              {t('common.reload')}
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

function App() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const { selectedTemplate, setSelectedTemplate } = useTemplateStore();
  // 使用 navigationStore 來管理導航狀態
  const { activeItem, setActiveItem, showSecondLevel, setShowSecondLevel } = useNavigationStore();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<null | any>(null);
  // 存儲選中的門店信息
  const [selectedStore, setSelectedStore] = useState<null | Store>(null);
  const { t } = useTranslation(); // 使用 i18n 翻譯功能

  // 將選中的門店信息設置到 window 對象上，以便在整個應用中共享
  useEffect(() => {
    if (selectedStore) {
      (window as any).selectedStore = selectedStore;
      console.log('已將門店信息設置到全局 window 對象:', selectedStore.id);
    } else {
      (window as any).selectedStore = null;
    }
  }, [selectedStore]);

  // 認證狀態
  const { isAuthenticated, checkAuth, user, logout } = useAuthStore();
  // 權限狀態
  const { fetchPermissions } = usePermissionStore();
  // 主題狀態
  const { theme } = useThemeStore();

  // 初始化主題
  useEffect(() => {
    initializeTheme();
  }, []);

  // 檢查登入狀態和獲取權限
  useEffect(() => {
    const initAuth = async () => {
      const isLoggedIn = await checkAuth();

      // 如果已登入，則獲取權限
      if (isLoggedIn) {
        await fetchPermissions();
      }
    };

    initAuth();
  }, [checkAuth, fetchPermissions]);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const handleCancelEdit = () => {
    setSelectedTemplate(null);
  };

  // 獲取當前頁面標題
  const getPageTitle = () => {
    // 如果是第一層選單
    if (!showSecondLevel) {
      switch (activeItem) {
        case 'store-management': return t('sidebar.storeManagement');
        case 'system-data': return t('sidebar.systemData');
        case 'system-templates': return t('sidebar.systemTemplates');
        case 'permission-management': return t('sidebar.permissionManagement');
        case 'system-logs': return t('sidebar.systemLogs');
        case 'settings': return t('sidebar.systemConfig');
        case 'color-test': return '色彩工具';
        default: return "EPD Manager";
      }
    }

    // 如果是第二層選單，顯示對應的標題
    switch (activeItem) {
      case 'templates': return t('sidebar.templates');
      case 'devices': return t('sidebar.devices');
      case 'deploy': return t('sidebar.deploy');
      case 'analytics': return t('sidebar.analytics');
      case 'users': return t('sidebar.users');
      case 'database': return t('sidebar.database');
      case 'store-overview': return t('sidebar.storeOverview');
      default: return "EPD Manager";
    }
  };

  // 渲染頁面標題區塊
  const renderPageTitle = () => {
    // 登出按鈕（包含用戶名）和主題切換開關
    const userControls = (
      <div className="flex items-center gap-3">
        {/* 主題切換開關 */}
        <ThemeToggle size="sm" />

        {/* 登出按鈕 */}
        <button
          onClick={() => logout()}
          className="flex items-center px-3 py-1 text-sm bg-muted hover:bg-muted/80 text-foreground rounded-md transition-colors border border-border"
        >
          <LogOut size={16} className="mr-1" />
          {t('auth.logout')} {user?.username ? `(${user.username})` : ''}
        </button>
      </div>
    );

    if (selectedTemplate) {
      return (
        <div className="w-full bg-white py-5 px-4 shadow-sm">
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            <div className="flex items-center">
              <button
                onClick={handleCancelEdit}
                className="mr-4 p-2 rounded-full hover:bg-gray-100"
              >
                <ArrowLeft size={20} />
              </button>
              <h1 className="text-xl font-bold text-gray-800">
                {t('templates.templateEditor')}
              </h1>
            </div>
            {userControls}
          </div>
        </div>
      );
    }

    // 如果選中了門店，則顯示門店名稱和頁面標題
    if (selectedStore && showSecondLevel) {
      return (
        <div className="w-full bg-background py-5 px-4 shadow-sm border-b border-border">
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            <div className="flex items-center">
              {/* 使用 StoreSelector 組件替換靜態標題 */}
              <StoreSelector
                currentStore={selectedStore}
                onStoreChange={(store: Store) => {
                  // 更新本地狀態
                  setSelectedStore(store);
                  // 將選中的門店信息設置到 window 對象上
                  (window as any).selectedStore = store;
                  // 更新 navigationStore 中的 selectedStoreId
                  useNavigationStore.getState().setSelectedStoreId(store.id);
                  console.log('已切換到門店:', store.id);
                }}
                pageName={getPageTitle()}
              />
            </div>
            {userControls}
          </div>
        </div>
      );
    }

    // 第一層選單的標題
    return (
      <div className="w-full bg-background py-5 px-4 shadow-sm border-b border-border">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <h1 className="text-xl font-bold text-foreground">
            {getPageTitle()}
          </h1>
          {userControls}
        </div>
      </div>
    );
  };

  const renderContent = () => {
    // 如果有選中的模板，整頁顯示編輯器
    if (selectedTemplate) {
      return <TemplateEditor />;
    }

    // 如果有選中的設備，顯示設備詳情頁面
    if (selectedDevice) {
      return (
        <DeviceDetailPage
          device={selectedDevice}
          onBack={() => setSelectedDevice(null)}
        />
      );
    }

    // 如果選中了門店，則顯示對應的頁面
    if (selectedStore && showSecondLevel) {
      // 根據 activeItem 顯示不同的頁面
      switch (activeItem) {
        case 'store-overview':
          return <StoreOverviewPage store={selectedStore} />;
        case 'database':
          return <DatabasePage store={selectedStore} />;
        case 'templates':
          return (
            <main className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-4 py-2">
              <TemplateList
                onAddTemplate={() => setIsAddModalOpen(true)}
                store={selectedStore}
              />
            </main>
          );
        case 'devices':
          return <DevicesPage store={selectedStore} onViewDeviceDetail={(device) => setSelectedDevice(device)} />;
        case 'deploy':
          return <GatewaysPage store={selectedStore} />;
        case 'analytics':
          return <EmptyPage title="系統分析" />;
        case 'users':
          return <StoreSettingsPage store={selectedStore} />;
        case 'settings':
          return <SystemConfigPage />;
        default:
          return <EmptyPage title="未知頁面" />;
      }
    }

    // 如果沒有選中門店，則顯示第一層選單的頁面
    switch (activeItem) {
      case 'settings':
        return <SystemConfigPage />;
      case 'system-data':
        return <SystemSpecificDataPage />;
      case 'system-templates':
        return <SystemTemplatesPage />;
      case 'permission-management':
        return <PermissionManagementPage />;
      case 'system-logs':
        return <SystemLogsPage />;
      case 'change-password':
        return <ChangePasswordPage />;
      case 'color-test':
        return <ColorTool />;

      case 'bug-management':
        return (
          <DevModeWrapper>
            <PasswordProtectedPage
              title="Bug回報管理"
              description="此頁面用於管理系統bug回報，需要管理員權限"
              requiredPassword="rd123456"
            >
              <BugReportManagementPage />
            </PasswordProtectedPage>
          </DevModeWrapper>
        );
      default:
        return <StoreManagementPage onSelectStore={(store) => {
          setSelectedStore(store);
          setShowSecondLevel(true);
          setActiveItem('store-overview'); // 默認選中門店概況
        }} />;
    }
  };

  // 如果未登入，顯示登入頁面
  if (!isAuthenticated) {
    return <LoginPage />;
  }

  return (
    <AuthGuard>
      <div className="flex h-screen overflow-hidden bg-background">        {!selectedTemplate && (
          <Sidebar
            activeItem={activeItem}
            setActiveItem={(id) => {
              // 當切換側邊欄項目時，清除選中的設備
              setSelectedDevice(null);
              setActiveItem(id);
            }}
            isSidebarCollapsed={isSidebarCollapsed}
            toggleSidebar={toggleSidebar}
            showSecondLevel={showSecondLevel}
            setShowSecondLevel={(show) => {
              setShowSecondLevel(show);
              if (!show) {
                // 如果返回第一層選單，則清除選中的門店
                setSelectedStore(null);
              }
              // 切換層級時也清除選中的設備
              setSelectedDevice(null);
            }}
          />
        )}

        <div className={`flex-1 flex flex-col min-w-0 transition-all duration-300 ${isSidebarCollapsed && !selectedTemplate ? 'ml-0' : 'ml-0'}`}>
          {/* 標題區塊固定在頂部 */}
          {renderPageTitle()}

          {/* 內容區塊可滾動 */}
          <div className={`flex-1 overflow-auto ${selectedTemplate ? 'p-0' : ''}`}>
            {renderContent()}
          </div>
        </div>

        <AddTemplateModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          store={selectedStore}
        />

        {/* 懸浮球功能按鈕 */}
        <FloatingActionButton />
      </div>
    </AuthGuard>
  );
}

export default function WrappedApp() {
  return (
    <ErrorBoundary>
      <App />
    </ErrorBoundary>
  );
}