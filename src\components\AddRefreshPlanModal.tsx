import React, { useState, useEffect } from 'react';
import { X, Calendar, Clock, Target, Settings } from 'lucide-react';
import { Store } from '../types/store';
import { CreatePlanRequest, TriggerConfig, TargetSelection, ExecutionConfig } from '../types/refreshPlan';
import { refreshPlan<PERSON>pi, planConfigApi } from '../services/refreshPlanApi';

// 文字截斷工具函數
const truncateText = (text: string, maxLength: number = 10): string => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

interface AddRefreshPlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  store: Store;
  onSuccess: () => void;
}

export const AddRefreshPlanModal: React.FC<AddRefreshPlanModalProps> = ({
  isOpen,
  onClose,
  store,
  onSuccess
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [nameError, setNameError] = useState<string | null>(null);
  const [isCheckingName, setIsCheckingName] = useState(false);

  // 表單數據
  const [formData, setFormData] = useState<CreatePlanRequest>({
    name: '',
    description: '',
    enabled: true,
    priority: 'medium',
    targetSelection: {
      type: 'mac_addresses',
      macAddresses: [],
      storeDataIds: []
    },
    trigger: {
      type: 'daily',
      executeTime: '08:00',
      executeDate: '',
      weekDays: []
    },
    execution: {
      useSystemSettings: true
    }
  });

  // 設備和門店數據選項
  const [deviceOptions, setDeviceOptions] = useState<any[]>([]);
  const [storeDataOptions, setStoreDataOptions] = useState<any[]>([]);

  // 搜尋狀態
  const [deviceSearchTerm, setDeviceSearchTerm] = useState('');
  const [storeDataSearchTerm, setStoreDataSearchTerm] = useState('');

  // 載入設備和門店數據選項
  useEffect(() => {
    if (isOpen && store?.id) {
      loadOptions();
    }
  }, [isOpen, store?.id]);

  const loadOptions = async () => {
    try {
      console.log('開始載入選項，門店ID:', store.id);

      const [devicesRes, storeDataRes] = await Promise.all([
        planConfigApi.getStoreDevices(store.id),
        planConfigApi.getStoreData(store.id)
      ]);

      console.log('設備 API 響應:', devicesRes);
      console.log('門店數據 API 響應:', storeDataRes);

      if (devicesRes.success) {
        setDeviceOptions(devicesRes.data);
        console.log('設置設備選項:', devicesRes.data);
      } else {
        console.error('設備 API 失敗');
      }

      if (storeDataRes.success) {
        setStoreDataOptions(storeDataRes.data);
        console.log('設置門店數據選項:', storeDataRes.data);
      } else {
        console.error('門店數據 API 失敗');
      }
    } catch (err) {
      console.error('載入選項失敗:', err);
    }
  };

  // 檢查名稱是否重複
  const checkNameDuplicate = async (name: string) => {
    if (!name || name.trim() === '') {
      setNameError(null);
      return;
    }

    try {
      setIsCheckingName(true);
      const response = await refreshPlanApi.checkPlanName(store.id, name.trim());

      if (response.success && response.isDuplicate) {
        setNameError('計畫名稱已存在，請使用其他名稱');
      } else {
        setNameError(null);
      }
    } catch (err) {
      console.error('檢查名稱重複失敗:', err);
      setNameError('檢查名稱失敗，請稍後再試');
    } finally {
      setIsCheckingName(false);
    }
  };

  // 重置表單
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      enabled: true,
      priority: 'medium',
      targetSelection: {
        type: 'mac_addresses',
        macAddresses: [],
        storeDataIds: []
      },
      trigger: {
        type: 'daily',
        executeTime: '08:00',
        executeDate: '',
        weekDays: []
      },
      execution: {
        useSystemSettings: true
      }
    });
    setCurrentStep(1);
    setError(null);
    setNameError(null);
  };

  // 處理關閉
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 提交表單
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      // 驗證表單
      if (!formData.name.trim()) {
        throw new Error('請輸入計畫名稱');
      }

      // 檢查名稱是否重複
      if (nameError) {
        throw new Error(nameError);
      }

      // 再次檢查名稱重複（防止用戶在失去焦點後又修改了名稱）
      const nameCheckResponse = await refreshPlanApi.checkPlanName(store.id, formData.name.trim());
      if (nameCheckResponse.success && nameCheckResponse.isDuplicate) {
        throw new Error('計畫名稱已存在，請使用其他名稱');
      }

      if (formData.targetSelection.type === 'mac_addresses' &&
          (!formData.targetSelection.macAddresses || formData.targetSelection.macAddresses.length === 0)) {
        throw new Error('請選擇至少一個設備');
      }

      if (formData.targetSelection.type === 'store_data' &&
          (!formData.targetSelection.storeDataIds || formData.targetSelection.storeDataIds.length === 0)) {
        throw new Error('請選擇至少一個門店數據');
      }

      if (formData.trigger.type === 'once' && !formData.trigger.executeDate) {
        throw new Error('請選擇執行日期');
      }

      if (formData.trigger.type === 'weekly' &&
          (!formData.trigger.weekDays || formData.trigger.weekDays.length === 0)) {
        throw new Error('請選擇執行星期');
      }

      // 創建計畫
      const response = await refreshPlanApi.createPlan(store.id, formData);

      if (response.success) {
        onSuccess();
        handleClose();
      }
    } catch (err: any) {
      setError(err.message || '創建計畫失敗');
    } finally {
      setLoading(false);
    }
  };

  // 過濾設備選項
  const filteredDeviceOptions = deviceOptions.filter(device => {
    if (!deviceSearchTerm) return true;
    const searchLower = deviceSearchTerm.toLowerCase();
    return (
      device.macAddress.toLowerCase().includes(searchLower) ||
      (device.code && device.code.toLowerCase().includes(searchLower)) ||
      (device.note && device.note.toLowerCase().includes(searchLower))
    );
  });

  // 過濾門店數據選項
  const filteredStoreDataOptions = storeDataOptions.filter(storeData => {
    if (!storeDataSearchTerm) return true;
    const searchLower = storeDataSearchTerm.toLowerCase();
    return storeData.id.toLowerCase().includes(searchLower);
  });

  // 步驟標題
  const stepTitles = [
    '基礎設定',
    '刷圖對象',
    '觸發條件',
    '執行策略'
  ];

  // 驗證每個步驟的必要欄位
  const validateStep = (step: number): { isValid: boolean; errorMessage?: string } => {
    switch (step) {
      case 1: // 基礎設定
        if (!formData.name.trim()) {
          return { isValid: false, errorMessage: '請輸入計畫名稱' };
        }
        if (nameError) {
          return { isValid: false, errorMessage: nameError };
        }
        return { isValid: true };

      case 2: // 刷圖對象
        if (formData.targetSelection.type === 'mac_addresses') {
          if (!formData.targetSelection.macAddresses || formData.targetSelection.macAddresses.length === 0) {
            return { isValid: false, errorMessage: '請至少選擇一個設備' };
          }
        } else if (formData.targetSelection.type === 'store_data') {
          if (!formData.targetSelection.storeDataIds || formData.targetSelection.storeDataIds.length === 0) {
            return { isValid: false, errorMessage: '請至少選擇一個門店數據' };
          }
        }
        return { isValid: true };

      case 3: // 觸發條件
        if (!formData.trigger.executeTime) {
          return { isValid: false, errorMessage: '請設定執行時間' };
        }
        if (formData.trigger.type === 'once' && !formData.trigger.executeDate) {
          return { isValid: false, errorMessage: '請選擇執行日期' };
        }
        if (formData.trigger.type === 'weekly' && (!formData.trigger.weekDays || formData.trigger.weekDays.length === 0)) {
          return { isValid: false, errorMessage: '請至少選擇一個執行星期' };
        }
        return { isValid: true };

      case 4: // 執行策略
        // 執行策略步驟沒有必要驗證，因為都是預設值
        return { isValid: true };

      default:
        return { isValid: true };
    }
  };

  // 處理下一步
  const handleNextStep = () => {
    const validation = validateStep(currentStep);
    if (!validation.isValid) {
      setError(validation.errorMessage || '請完成必要欄位的填寫');
      return;
    }

    setError(null);
    setCurrentStep(currentStep + 1);
  };

  // 清除錯誤的輔助函數
  const clearErrorIfExists = () => {
    if (error) {
      setError(null);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* 標題欄 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">新增刷圖計畫</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 步驟指示器 */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {stepTitles.map((title, index) => {
              const stepNumber = index + 1;
              const isCurrentStep = stepNumber === currentStep;
              const isCompletedStep = stepNumber < currentStep;
              const isValidStep = isCompletedStep ? validateStep(stepNumber).isValid : true;

              return (
                <div key={index} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    isCurrentStep
                      ? 'bg-blue-500 text-white'
                      : isCompletedStep
                      ? isValidStep
                        ? 'bg-green-500 text-white'
                        : 'bg-red-500 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {isCompletedStep && isValidStep ? '✓' : stepNumber}
                  </div>
                  <span className={`ml-2 text-sm ${
                    isCurrentStep
                      ? 'text-blue-600 font-medium'
                      : isCompletedStep && !isValidStep
                      ? 'text-red-600'
                      : 'text-gray-600'
                  }`}>
                    {title}
                  </span>
                  {index < stepTitles.length - 1 && (
                    <div className={`w-8 h-0.5 mx-4 ${
                      isCompletedStep && isValidStep ? 'bg-green-500' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* 錯誤提示 */}
        {error && (
          <div className="mx-6 mt-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700">
            {error}
          </div>
        )}

        {/* 表單內容 */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {/* 步驟 1: 基礎設定 */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  計畫名稱 *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => {
                      setFormData({ ...formData, name: e.target.value });
                      clearErrorIfExists();
                      // 清除名稱錯誤，等待失去焦點時重新檢查
                      if (nameError) {
                        setNameError(null);
                      }
                    }}
                    onBlur={(e) => {
                      checkNameDuplicate(e.target.value);
                    }}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                      nameError
                        ? 'border-red-500 focus:ring-red-500'
                        : 'border-gray-300 focus:ring-blue-500'
                    }`}
                    placeholder="請輸入計畫名稱"
                    disabled={isCheckingName}
                  />
                  {isCheckingName && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    </div>
                  )}
                </div>
                {nameError && (
                  <p className="mt-1 text-sm text-red-600">{nameError}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  計畫描述
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="請輸入計畫描述（可選）"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  優先級
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => setFormData({ ...formData, priority: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="high">高</option>
                  <option value="medium">中</option>
                  <option value="low">低</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enabled"
                  checked={formData.enabled}
                  onChange={(e) => setFormData({ ...formData, enabled: e.target.checked })}
                  className="mr-2"
                />
                <label htmlFor="enabled" className="text-sm text-gray-700">
                  創建後立即啟用
                </label>
              </div>
            </div>
          )}

          {/* 步驟 2: 刷圖對象 */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  對象類型 *
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="targetType"
                      value="mac_addresses"
                      checked={formData.targetSelection.type === 'mac_addresses'}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          targetSelection: {
                            ...formData.targetSelection,
                            type: e.target.value as any,
                            macAddresses: [],
                            storeDataIds: []
                          }
                        });
                        clearErrorIfExists();
                      }}
                      className="mr-2"
                    />
                    指定 MAC 地址
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="targetType"
                      value="store_data"
                      checked={formData.targetSelection.type === 'store_data'}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          targetSelection: {
                            ...formData.targetSelection,
                            type: e.target.value as any,
                            macAddresses: [],
                            storeDataIds: []
                          }
                        });
                        clearErrorIfExists();
                      }}
                      className="mr-2"
                    />
                    指定門店數據
                  </label>
                </div>
              </div>

              {formData.targetSelection.type === 'mac_addresses' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    選擇設備 * ({deviceOptions.length} 個可用設備)
                  </label>
                  {deviceOptions.length === 0 ? (
                    <div className="text-center py-4 text-gray-500 border border-gray-300 rounded-md">
                      沒有找到設備，請確認門店中有設備數據
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {/* 搜尋框 */}
                      <input
                        type="text"
                        placeholder="搜尋設備 (MAC地址、編號、備註)"
                        value={deviceSearchTerm}
                        onChange={(e) => setDeviceSearchTerm(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />

                      <div className="border border-gray-300 rounded-md">
                        {/* 全選控制 */}
                        <div className="p-2 border-b border-gray-200 flex items-center justify-between bg-gray-50">
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filteredDeviceOptions.length > 0 && filteredDeviceOptions.every(device => formData.targetSelection.macAddresses?.includes(device.macAddress))}
                              onChange={(e) => {
                                const currentMacAddresses = formData.targetSelection.macAddresses || [];
                                const filteredMacAddresses = filteredDeviceOptions.map(device => device.macAddress);

                                if (e.target.checked) {
                                  // 全選當前過濾的設備，保留不在過濾結果中的已選項目
                                  const existingNonFiltered = currentMacAddresses.filter(mac => !filteredMacAddresses.includes(mac));
                                  setFormData({
                                    ...formData,
                                    targetSelection: {
                                      ...formData.targetSelection,
                                      macAddresses: [...existingNonFiltered, ...filteredMacAddresses]
                                    }
                                  });
                                } else {
                                  // 取消選擇當前過濾的設備，保留不在過濾結果中的已選項目
                                  const remainingMacAddresses = currentMacAddresses.filter(mac => !filteredMacAddresses.includes(mac));
                                  setFormData({
                                    ...formData,
                                    targetSelection: {
                                      ...formData.targetSelection,
                                      macAddresses: remainingMacAddresses
                                    }
                                  });
                                }
                                clearErrorIfExists();
                              }}
                              className="mr-2"
                            />
                            <span className="text-sm font-medium">全選</span>
                          </label>
                          <span className="text-sm text-gray-500">
                            已選擇: {formData.targetSelection.macAddresses?.length || 0} / {filteredDeviceOptions.length}
                          </span>
                        </div>

                        {/* 設備列表 */}
                        <div className="max-h-48 overflow-y-auto p-2">
                          {filteredDeviceOptions.length === 0 ? (
                            <div className="text-center py-4 text-gray-500">
                              沒有找到符合條件的設備
                            </div>
                          ) : (
                            filteredDeviceOptions.map((device) => (
                              <label key={device.id} className="flex items-center py-1 hover:bg-gray-50 rounded">
                                <input
                                  type="checkbox"
                                  checked={formData.targetSelection.macAddresses?.includes(device.macAddress) || false}
                                  onChange={(e) => {
                                    const macAddresses = formData.targetSelection.macAddresses || [];
                                    if (e.target.checked) {
                                      setFormData({
                                        ...formData,
                                        targetSelection: {
                                          ...formData.targetSelection,
                                          macAddresses: [...macAddresses, device.macAddress]
                                        }
                                      });
                                    } else {
                                      setFormData({
                                        ...formData,
                                        targetSelection: {
                                          ...formData.targetSelection,
                                          macAddresses: macAddresses.filter(mac => mac !== device.macAddress)
                                        }
                                      });
                                    }
                                    clearErrorIfExists();
                                  }}
                                  className="mr-2"
                                />
                                <span
                                  className="text-sm"
                                  title={`${device.macAddress}-(${device.code || ''})-(${device.note || ''})`}
                                >
                                  {device.macAddress}-({truncateText(device.code || '', 8)})-({truncateText(device.note || '', 8)})
                                </span>
                              </label>
                            ))
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {formData.targetSelection.type === 'store_data' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    選擇門店數據 * ({storeDataOptions.length} 個可用數據)
                  </label>
                  {storeDataOptions.length === 0 ? (
                    <div className="text-center py-4 text-gray-500 border border-gray-300 rounded-md">
                      沒有找到門店數據，請先在門店設置中創建門店專屬資料
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {/* 搜尋框 */}
                      <input
                        type="text"
                        placeholder="搜尋門店數據 (ID)"
                        value={storeDataSearchTerm}
                        onChange={(e) => setStoreDataSearchTerm(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />

                      <div className="border border-gray-300 rounded-md">
                        {/* 全選控制 */}
                        <div className="p-2 border-b border-gray-200 flex items-center justify-between bg-gray-50">
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filteredStoreDataOptions.length > 0 && filteredStoreDataOptions.every(storeData => formData.targetSelection.storeDataIds?.includes(storeData.id))}
                              onChange={(e) => {
                                const currentStoreDataIds = formData.targetSelection.storeDataIds || [];
                                const filteredStoreDataIds = filteredStoreDataOptions.map(storeData => storeData.id);

                                if (e.target.checked) {
                                  // 全選當前過濾的門店數據，保留不在過濾結果中的已選項目
                                  const existingNonFiltered = currentStoreDataIds.filter(id => !filteredStoreDataIds.includes(id));
                                  setFormData({
                                    ...formData,
                                    targetSelection: {
                                      ...formData.targetSelection,
                                      storeDataIds: [...existingNonFiltered, ...filteredStoreDataIds]
                                    }
                                  });
                                } else {
                                  // 取消選擇當前過濾的門店數據，保留不在過濾結果中的已選項目
                                  const remainingStoreDataIds = currentStoreDataIds.filter(id => !filteredStoreDataIds.includes(id));
                                  setFormData({
                                    ...formData,
                                    targetSelection: {
                                      ...formData.targetSelection,
                                      storeDataIds: remainingStoreDataIds
                                    }
                                  });
                                }
                                clearErrorIfExists();
                              }}
                              className="mr-2"
                            />
                            <span className="text-sm font-medium">全選</span>
                          </label>
                          <span className="text-sm text-gray-500">
                            已選擇: {formData.targetSelection.storeDataIds?.length || 0} / {filteredStoreDataOptions.length}
                          </span>
                        </div>

                        {/* 門店數據列表 */}
                        <div className="max-h-48 overflow-y-auto p-2">
                          {filteredStoreDataOptions.length === 0 ? (
                            <div className="text-center py-4 text-gray-500">
                              沒有找到符合條件的門店數據
                            </div>
                          ) : (
                            filteredStoreDataOptions.map((storeData) => (
                              <label key={storeData.id} className="flex items-center py-1 hover:bg-gray-50 rounded">
                                <input
                                  type="checkbox"
                                  checked={formData.targetSelection.storeDataIds?.includes(storeData.id) || false}
                                  onChange={(e) => {
                                    const storeDataIds = formData.targetSelection.storeDataIds || [];
                                    if (e.target.checked) {
                                      setFormData({
                                        ...formData,
                                        targetSelection: {
                                          ...formData.targetSelection,
                                          storeDataIds: [...storeDataIds, storeData.id]
                                        }
                                      });
                                    } else {
                                      setFormData({
                                        ...formData,
                                        targetSelection: {
                                          ...formData.targetSelection,
                                          storeDataIds: storeDataIds.filter(id => id !== storeData.id)
                                        }
                                      });
                                    }
                                    clearErrorIfExists();
                                  }}
                                  className="mr-2"
                                />
                                <span className="text-sm">
                                  {storeData.id} ({storeData.boundDeviceCount} 個設備)
                                </span>
                              </label>
                            ))
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* 步驟 3: 觸發條件 */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  執行類型 *
                </label>
                <select
                  value={formData.trigger.type}
                  onChange={(e) => {
                    setFormData({
                      ...formData,
                      trigger: {
                        ...formData.trigger,
                        type: e.target.value as any,
                        executeDate: '',
                        weekDays: []
                      }
                    });
                    clearErrorIfExists();
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="once">單次執行</option>
                  <option value="daily">每日執行</option>
                  <option value="weekly">每週執行</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  執行時間 *
                </label>
                <input
                  type="time"
                  value={formData.trigger.executeTime}
                  onChange={(e) => {
                    setFormData({
                      ...formData,
                      trigger: { ...formData.trigger, executeTime: e.target.value }
                    });
                    clearErrorIfExists();
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {formData.trigger.type === 'once' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    執行日期 *
                  </label>
                  <input
                    type="date"
                    value={formData.trigger.executeDate}
                    onChange={(e) => {
                      setFormData({
                        ...formData,
                        trigger: { ...formData.trigger, executeDate: e.target.value }
                      });
                      clearErrorIfExists();
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}

              {formData.trigger.type === 'weekly' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    執行星期 *
                  </label>
                  <div className="grid grid-cols-7 gap-2">
                    {['日', '一', '二', '三', '四', '五', '六'].map((day, index) => (
                      <label key={index} className="flex flex-col items-center">
                        <input
                          type="checkbox"
                          checked={formData.trigger.weekDays?.includes(index) || false}
                          onChange={(e) => {
                            const weekDays = formData.trigger.weekDays || [];
                            if (e.target.checked) {
                              setFormData({
                                ...formData,
                                trigger: {
                                  ...formData.trigger,
                                  weekDays: [...weekDays, index]
                                }
                              });
                            } else {
                              setFormData({
                                ...formData,
                                trigger: {
                                  ...formData.trigger,
                                  weekDays: weekDays.filter(d => d !== index)
                                }
                              });
                            }
                            clearErrorIfExists();
                          }}
                          className="mb-1"
                        />
                        <span className="text-sm">{day}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 步驟 4: 執行策略 */}
          {currentStep === 4 && (
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="useSystemSettings"
                  checked={true}
                  disabled={true}
                  className="mr-2 opacity-50 cursor-not-allowed"
                />
                <label htmlFor="useSystemSettings" className="text-sm text-gray-700">
                  使用系統設定（強制啟用）
                </label>
              </div>

              <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
                <h4 className="text-sm font-medium text-blue-800 mb-2">執行策略說明</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• 刷圖計畫將使用系統配置中的所有傳送參數</li>
                  <li>• 包括：網關並發數、重試次數、超時時間等</li>
                  <li>• 智能網關選擇功能取決於裝置本身的設定</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* 底部按鈕 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={currentStep > 1 ? () => setCurrentStep(currentStep - 1) : handleClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
          >
            {currentStep > 1 ? '上一步' : '取消'}
          </button>

          <div className="flex space-x-2">
            {currentStep < stepTitles.length ? (
              <button
                onClick={handleNextStep}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                下一步
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={loading}
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
              >
                {loading ? '創建中...' : '創建計畫'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
