import React from 'react';
import { TemplateElement, DisplayColorType } from '../../../types';
import { FormField, NumberInput, RestrictedColorInput, PropertyDivider } from './FormComponents';

interface LinePropertiesProps {
  element: TemplateElement;
  updateElement: (updates: Partial<TemplateElement>) => void;
  colorType?: string | DisplayColorType; // 新增：模板的顏色類型
}

export const LineProperties: React.FC<LinePropertiesProps> = ({ element, updateElement, colorType }) => (
  <div>
    <FormField label="線條寬度">
      <NumberInput
        value={element.lineWidth || 1}
        onChange={(value) => updateElement({ lineWidth: value })}
        min={1}
        max={10}
      />
    </FormField>

    <PropertyDivider />

    <FormField label="線條顏色">
      <RestrictedColorInput
        value={element.lineColor || '#000000'}
        onChange={(value) => updateElement({ lineColor: value })}
        colorType={colorType}
      />
    </FormField>
  </div>
);