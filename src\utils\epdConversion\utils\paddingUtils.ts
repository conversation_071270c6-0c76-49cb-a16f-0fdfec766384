import { DisplayColorType } from '../../../types';

/**
 * 計算對齊後的寬度
 */
export function calculatePaddedWidth(
  width: number,
  colorType: DisplayColorType
): number {
  switch (colorType) {
    case DisplayColorType.BW:  // "Gray16"
      return Math.ceil(width / 2) * 2;  // 2的倍數
    case DisplayColorType.BWR: // "Black & White & Red"
      return Math.ceil(width / 8) * 8;  // 8的倍數
    case DisplayColorType.BWRY: // "Black & White & Red & Yellow"
      return Math.ceil(width / 4) * 4;  // 4的倍數
    default:
      throw new Error(`不支援的顏色類型: ${colorType}`);
  }
}

/**
 * 對圖像數據進行寬度填充
 */
export function padImageData(
  imageData: ImageData,
  targetWidth: number,
  paddingColor: string = '#FFFFFF'
): ImageData {
  const { width, height, data } = imageData;
  
  if (width >= targetWidth) {
    return imageData; // 無需填充
  }
  
  // 解析填充顏色
  const paddingRGB = hexToRgb(paddingColor);
  
  // 創建新的 ImageData
  const newImageData = new ImageData(targetWidth, height);
  const newData = newImageData.data;
  
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < targetWidth; x++) {
      const newIndex = (y * targetWidth + x) * 4;
      
      if (x < width) {
        // 複製原始像素
        const oldIndex = (y * width + x) * 4;
        newData[newIndex] = data[oldIndex];         // R
        newData[newIndex + 1] = data[oldIndex + 1]; // G
        newData[newIndex + 2] = data[oldIndex + 2]; // B
        newData[newIndex + 3] = data[oldIndex + 3]; // A
      } else {
        // 填充像素
        newData[newIndex] = paddingRGB.r;     // R
        newData[newIndex + 1] = paddingRGB.g; // G
        newData[newIndex + 2] = paddingRGB.b; // B
        newData[newIndex + 3] = 255;          // A
      }
    }
  }
  
  return newImageData;
}

/**
 * 將 hex 顏色轉換為 RGB
 */
function hexToRgb(hex: string): { r: number; g: number; b: number } {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) {
    // 默認白色
    return { r: 255, g: 255, b: 255 };
  }
  
  return {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  };
}
