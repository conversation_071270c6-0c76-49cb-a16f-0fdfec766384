const express = require('express');
const router = express.Router();
const { ObjectId } = require('mongodb');
const { authenticate, checkPermission } = require('../middleware/auth');

// MongoDB 連接信息
const collectionName = 'refreshPlans';

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取數據集合
const getCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();

  // 檢查 refreshPlans 集合是否存在，若不存在則創建
  const collections = await db.listCollections({ name: collectionName }).toArray();
  if (collections.length === 0) {
    console.log(`創建 ${collectionName} 集合`);
    await db.createCollection(collectionName);
    
    // 創建索引
    const collection = db.collection(collectionName);
    await collection.createIndex({ storeId: 1 });
    await collection.createIndex({ status: 1 });
    await collection.createIndex({ 'trigger.type': 1 });
    await collection.createIndex({ enabled: 1 });
  }

  const collection = db.collection(collectionName);
  return { collection, client };
};

// 驗證計畫數據
const validatePlanData = (planData) => {
  const errors = [];

  // 驗證必填欄位
  if (!planData.name || planData.name.trim() === '') {
    errors.push('計畫名稱為必填項');
  }

  if (!planData.storeId || planData.storeId.trim() === '') {
    errors.push('門店ID為必填項');
  }

  // 驗證觸發配置
  if (!planData.trigger || !planData.trigger.type) {
    errors.push('觸發類型為必填項');
  } else {
    const { trigger } = planData;
    
    // 驗證執行時間格式
    if (!trigger.executeTime || !/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(trigger.executeTime)) {
      errors.push('執行時間格式不正確，應為 HH:mm 格式');
    }

    // 根據觸發類型驗證特定欄位
    if (trigger.type === 'once' && !trigger.executeDate) {
      errors.push('單次執行需要指定執行日期');
    }

    if (trigger.type === 'weekly' && (!trigger.weekDays || !Array.isArray(trigger.weekDays) || trigger.weekDays.length === 0)) {
      errors.push('每週執行需要指定執行星期');
    }
  }

  // 驗證對象選擇
  if (!planData.targetSelection || !planData.targetSelection.type) {
    errors.push('刷圖對象類型為必填項');
  } else {
    const { targetSelection } = planData;
    
    if (targetSelection.type === 'mac_addresses') {
      if (!targetSelection.macAddresses || !Array.isArray(targetSelection.macAddresses) || targetSelection.macAddresses.length === 0) {
        errors.push('指定MAC地址模式需要至少選擇一個MAC地址');
      }
    } else if (targetSelection.type === 'store_data') {
      if (!targetSelection.storeDataIds || !Array.isArray(targetSelection.storeDataIds) || targetSelection.storeDataIds.length === 0) {
        errors.push('指定門店數據模式需要至少選擇一項門店數據');
      }
    }
  }

  return errors;
};

// 獲取門店的刷圖計畫列表
router.get('/stores/:storeId/refresh-plans', authenticate, checkPermission(['store-settings:view']), async (req, res) => {
  try {
    const { storeId } = req.params;
    const { page = 1, limit = 10, status, search = '' } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);

    const { collection } = await getCollection();

    // 構建查詢條件
    const query = { storeId };

    if (status) {
      query.status = status;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // 獲取總數
    const total = await collection.countDocuments(query);

    // 獲取分頁數據
    const plans = await collection.find(query)
      .sort({ createdAt: -1 })
      .skip((pageNum - 1) * limitNum)
      .limit(limitNum)
      .toArray();

    // 從調度器獲取實際的下次執行時間
    const taskScheduler = require('../services/taskScheduler');
    const plansWithNextRun = plans.map(plan => {
      const nextRunTime = taskScheduler.getNextRunTime(plan._id.toString());
      return {
        ...plan,
        nextRun: nextRunTime ? nextRunTime.toISOString() : plan.nextRun
      };
    });

    res.json({
      success: true,
      data: {
        plans: plansWithNextRun,
        total,
        page: pageNum,
        limit: limitNum
      }
    });
  } catch (error) {
    console.error('獲取刷圖計畫列表失敗:', error);
    res.status(500).json({ success: false, error: '獲取刷圖計畫列表失敗' });
  }
});

// 檢查計畫名稱是否重複
router.post('/stores/:storeId/refresh-plans/check-name', authenticate, checkPermission(['store-settings:view']), async (req, res) => {
  try {
    const { storeId } = req.params;
    const { name, excludePlanId } = req.body;

    if (!name || name.trim() === '') {
      return res.json({ success: true, isDuplicate: false });
    }

    const { collection } = await getCollection();

    // 構建查詢條件
    const query = {
      storeId,
      name: name.trim()
    };

    // 如果是編輯模式，排除當前計畫
    if (excludePlanId) {
      query._id = { $ne: new ObjectId(excludePlanId) };
    }

    const existingPlan = await collection.findOne(query);

    res.json({
      success: true,
      isDuplicate: !!existingPlan
    });
  } catch (error) {
    console.error('檢查計畫名稱重複失敗:', error);
    res.status(500).json({ success: false, error: '檢查計畫名稱重複失敗' });
  }
});

// 獲取計畫統計信息 - 必須在 :planId 路由之前
router.get('/stores/:storeId/refresh-plans/statistics', authenticate, checkPermission(['store-settings:view']), async (req, res) => {
  try {
    const { storeId } = req.params;
    const { period = 'week', planIds } = req.query;

    // 計算時間範圍
    const now = new Date();
    let startDate;

    switch (period) {
      case 'day':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // 獲取執行記錄集合
    const { client, db } = await getDbConnection();
    const executionCollection = db.collection('executionRecords');

    // 檢查集合是否存在
    const collections = await db.listCollections({ name: 'executionRecords' }).toArray();
    if (collections.length === 0) {
      console.log('執行記錄集合不存在，返回空統計');
      return res.json({
        success: true,
        data: {
          period,
          totalExecutions: 0,
          successExecutions: 0,
          failedExecutions: 0,
          successRate: 0,
          avgProcessingTime: 0,
          deviceStats: {
            totalDevices: 0,
            successDevices: 0,
            failedDevices: 0
          }
        }
      });
    }

    // 構建查詢條件
    const query = {
      storeId,
      startTime: { $gte: startDate }
    };

    if (planIds) {
      const planIdArray = Array.isArray(planIds) ? planIds : [planIds];
      query.planId = { $in: planIdArray.map(id => new ObjectId(id)) };
    }

    // 獲取統計數據
    const totalExecutions = await executionCollection.countDocuments(query);
    const successExecutions = await executionCollection.countDocuments({
      ...query,
      status: 'completed'
    });
    const failedExecutions = await executionCollection.countDocuments({
      ...query,
      status: 'failed'
    });

    // 獲取平均處理時間
    const avgTimeResult = await executionCollection.aggregate([
      { $match: { ...query, status: 'completed', endTime: { $exists: true } } },
      {
        $addFields: {
          processingTime: {
            $subtract: ['$endTime', '$startTime']
          }
        }
      },
      {
        $group: {
          _id: null,
          avgTime: { $avg: '$processingTime' }
        }
      }
    ]).toArray();

    const avgProcessingTime = avgTimeResult.length > 0 ? avgTimeResult[0].avgTime : 0;

    // 獲取設備處理統計
    const deviceStatsResult = await executionCollection.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalDevices: { $sum: { $ifNull: ['$result.totalDevices', 0] } },
          successDevices: { $sum: { $ifNull: ['$result.successDevices', 0] } },
          failedDevices: { $sum: { $ifNull: ['$result.failedDevices', 0] } }
        }
      }
    ]).toArray();

    const deviceStats = deviceStatsResult.length > 0 ? deviceStatsResult[0] : {
      totalDevices: 0,
      successDevices: 0,
      failedDevices: 0
    };

    res.json({
      success: true,
      data: {
        period,
        totalExecutions,
        successExecutions,
        failedExecutions,
        successRate: totalExecutions > 0 ? (successExecutions / totalExecutions * 100).toFixed(2) : 0,
        avgProcessingTime: Math.round(avgProcessingTime / 1000), // 轉換為秒
        deviceStats
      }
    });
  } catch (error) {
    console.error('獲取統計信息失敗:', error);
    res.status(500).json({ success: false, error: '獲取統計信息失敗' });
  }
});

// 獲取調度器狀態和運行中的計畫 - 必須在 :planId 路由之前
router.get('/stores/:storeId/refresh-plans/scheduler-status', authenticate, checkPermission(['store-settings:view']), async (req, res) => {
  try {
    const { storeId } = req.params;
    const taskScheduler = require('../services/taskScheduler');

    // 獲取調度器狀態
    const schedulerStatus = taskScheduler.getSchedulerStatus();

    // 過濾出當前門店的計畫
    const storeActiveTasks = schedulerStatus.activeTasks.filter(task => task.storeId === storeId);

    // 獲取運行中的計畫
    const { client, db } = await getDbConnection();
    const plansCollection = db.collection('refreshPlans');

    const runningPlans = await plansCollection.find({
      storeId,
      status: 'running'
    }).toArray();

    res.json({
      success: true,
      data: {
        schedulerStatus: {
          isHealthy: schedulerStatus.isHealthy,
          schedulerStartTime: schedulerStatus.schedulerStartTime,
          totalActiveTasks: storeActiveTasks.length
        },
        activeTasks: storeActiveTasks,
        runningPlans: runningPlans.map(plan => ({
          _id: plan._id,
          name: plan.name,
          status: plan.status,
          lastRun: plan.lastRun,
          statistics: plan.statistics
        }))
      }
    });
  } catch (error) {
    console.error('獲取調度器狀態失敗:', error);
    res.status(500).json({ success: false, error: '獲取調度器狀態失敗' });
  }
});

// 獲取單個刷圖計畫
router.get('/stores/:storeId/refresh-plans/:planId', authenticate, checkPermission(['store-settings:view']), async (req, res) => {
  try {
    const { storeId, planId } = req.params;

    // 驗證 planId 是否為有效的 ObjectId
    if (!ObjectId.isValid(planId)) {
      return res.status(400).json({ success: false, error: '無效的計畫ID格式' });
    }

    const { collection } = await getCollection();

    const plan = await collection.findOne({
      _id: new ObjectId(planId),
      storeId
    });

    if (!plan) {
      return res.status(404).json({ success: false, error: '刷圖計畫不存在' });
    }

    res.json({ success: true, data: plan });
  } catch (error) {
    console.error('獲取刷圖計畫失敗:', error);
    res.status(500).json({ success: false, error: '獲取刷圖計畫失敗' });
  }
});

// 創建刷圖計畫
router.post('/stores/:storeId/refresh-plans', authenticate, checkPermission(['store-settings:create']), async (req, res) => {
  try {
    const { storeId } = req.params;
    const planData = req.body;

    // 設置門店ID
    planData.storeId = storeId;

    // 驗證計畫數據
    const validationErrors = validatePlanData(planData);
    if (validationErrors.length > 0) {
      return res.status(400).json({ 
        success: false, 
        error: '數據驗證失敗', 
        details: validationErrors 
      });
    }

    const { collection } = await getCollection();

    // 檢查計畫名稱是否重複
    const existingPlan = await collection.findOne({ 
      storeId, 
      name: planData.name 
    });
    
    if (existingPlan) {
      return res.status(400).json({ 
        success: false, 
        error: '計畫名稱已存在' 
      });
    }

    // 創建新計畫
    const now = new Date();
    const newPlan = {
      ...planData,
      status: planData.enabled ? 'active' : 'inactive',
      lastRun: null,
      nextRun: null,
      statistics: {
        totalRuns: 0,
        successRuns: 0,
        failedRuns: 0,
        lastRunResult: null
      },
      createdAt: now,
      updatedAt: now,
      createdBy: req.user._id.toString()
    };

    const result = await collection.insertOne(newPlan);
    const createdPlan = await collection.findOne({ _id: result.insertedId });

    // 如果計畫啟用，註冊到調度器
    if (createdPlan.enabled) {
      const taskScheduler = require('../services/taskScheduler');
      await taskScheduler.registerPlan(createdPlan);
    }

    // 廣播刷圖計畫創建事件
    try {
      const websocketService = require('../services/websocketService');
      websocketService.broadcastRefreshPlanUpdate(storeId, {
        planId: createdPlan._id.toString(),
        planData: {
          // 廣播完整的計畫數據
          ...createdPlan,
          _id: createdPlan._id.toString(),
          updatedFields: ['_id', 'name', 'status', 'enabled', 'lastRun', 'nextRun', 'statistics']
        }
      }, 'create');
    } catch (error) {
      console.error('廣播刷圖計畫創建事件失敗:', error);
    }

    res.status(201).json({ success: true, data: createdPlan });
  } catch (error) {
    console.error('創建刷圖計畫失敗:', error);
    res.status(500).json({ success: false, error: '創建刷圖計畫失敗' });
  }
});

// 更新刷圖計畫
router.put('/stores/:storeId/refresh-plans/:planId', authenticate, checkPermission(['store-settings:update']), async (req, res) => {
  try {
    const { storeId, planId } = req.params;
    const updateData = req.body;

    // 驗證 planId 是否為有效的 ObjectId
    if (!ObjectId.isValid(planId)) {
      return res.status(400).json({ success: false, error: '無效的計畫ID格式' });
    }

    const { collection } = await getCollection();

    // 查找現有計畫
    const existingPlan = await collection.findOne({ 
      _id: new ObjectId(planId), 
      storeId 
    });

    if (!existingPlan) {
      return res.status(404).json({ success: false, error: '刷圖計畫不存在' });
    }

    // 創建用於驗證的完整計畫數據（合併現有數據和更新數據）
    const planDataForValidation = {
      ...existingPlan,
      ...updateData,
      storeId,
      // 確保不覆蓋這些系統欄位
      _id: existingPlan._id,
      createdAt: existingPlan.createdAt,
      createdBy: existingPlan.createdBy,
      statistics: existingPlan.statistics,
      lastRun: existingPlan.lastRun
    };

    // 驗證更新後的數據
    const validationErrors = validatePlanData(planDataForValidation);
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        error: '數據驗證失敗',
        details: validationErrors
      });
    }

    // 檢查計畫名稱是否與其他計畫重複
    if (updateData.name && updateData.name !== existingPlan.name) {
      const duplicatePlan = await collection.findOne({
        storeId,
        name: updateData.name,
        _id: { $ne: new ObjectId(planId) }
      });

      if (duplicatePlan) {
        return res.status(400).json({
          success: false,
          error: '計畫名稱已存在'
        });
      }
    }

    // 準備更新數據（只包含實際要更新的欄位）
    const updatedPlan = {
      ...updateData,
      status: updateData.enabled !== undefined ? (updateData.enabled ? 'active' : 'inactive') : existingPlan.status,
      updatedAt: new Date()
    };

    console.log('更新計畫數據:', updatedPlan);

    await collection.updateOne(
      { _id: new ObjectId(planId) },
      { $set: updatedPlan }
    );

    const result = await collection.findOne({ _id: new ObjectId(planId) });
    console.log('更新後的計畫數據:', result);

    // 重新註冊調度任務
    const taskScheduler = require('../services/taskScheduler');
    console.log(`重新註冊計畫調度: ${result.name}, enabled: ${result.enabled}`);

    // 先取消現有的調度
    await taskScheduler.unregisterPlan(planId);

    // 如果計畫啟用，重新註冊（這會自動更新 nextRun 並廣播事件）
    if (result.enabled) {
      console.log(`重新註冊啟用的計畫: ${result.name}`);
      await taskScheduler.registerPlan(result);
      console.log(`計畫 ${result.name} 重新註冊完成`);
    } else {
      // 如果計畫被停用，需要手動廣播更新事件
      console.log(`計畫 ${result.name} 已停用，廣播停用事件`);
      try {
        const websocketService = require('../services/websocketService');
        const updatedResult = await collection.findOne({ _id: new ObjectId(planId) });

        // 確保日期格式正確
        const convertToISOString = (dateValue) => {
          if (!dateValue) return null;

          // 處理 MongoDB 日期對象 { _date: ... }
          if (dateValue._date) {
            return new Date(dateValue._date).toISOString();
          }

          // 處理標準 Date 對象
          if (dateValue instanceof Date) {
            return dateValue.toISOString();
          }

          // 處理字符串格式的日期
          if (typeof dateValue === 'string') {
            return new Date(dateValue).toISOString();
          }

          // 嘗試直接轉換
          try {
            return new Date(dateValue).toISOString();
          } catch (error) {
            console.error('日期轉換失敗:', dateValue, error);
            return null;
          }
        };

        const planDataToSend = {
          ...updatedResult,
          _id: updatedResult._id.toString(),
          // 確保所有日期字段是 ISO 字符串格式
          nextRun: convertToISOString(updatedResult.nextRun),
          lastRun: convertToISOString(updatedResult.lastRun),
          createdAt: convertToISOString(updatedResult.createdAt),
          updatedAt: convertToISOString(updatedResult.updatedAt),
          updatedFields: [...Object.keys(updateData)]
        };

        websocketService.broadcastRefreshPlanUpdate(storeId, {
          planId: updatedResult._id.toString(),
          planData: planDataToSend
        }, 'update');
        console.log(`計畫 ${result.name} 停用事件廣播完成`);
      } catch (error) {
        console.error('廣播計畫停用更新事件失敗:', error);
      }
    }

    // 添加一個小延遲，確保調度器的廣播完成
    await new Promise(resolve => setTimeout(resolve, 100));

    // 獲取最終的計畫數據
    const finalResult = await collection.findOne({ _id: new ObjectId(planId) });

    res.json({ success: true, data: finalResult });
  } catch (error) {
    console.error('更新刷圖計畫失敗:', error);
    res.status(500).json({ success: false, error: '更新刷圖計畫失敗' });
  }
});

// 刪除刷圖計畫
router.delete('/stores/:storeId/refresh-plans/:planId', authenticate, checkPermission(['store-settings:delete']), async (req, res) => {
  try {
    const { storeId, planId } = req.params;

    // 驗證 planId 是否為有效的 ObjectId
    if (!ObjectId.isValid(planId)) {
      return res.status(400).json({ success: false, error: '無效的計畫ID格式' });
    }

    const { collection } = await getCollection();

    // 查找計畫
    const plan = await collection.findOne({ 
      _id: new ObjectId(planId), 
      storeId 
    });

    if (!plan) {
      return res.status(404).json({ success: false, error: '刷圖計畫不存在' });
    }

    // 取消調度任務
    const taskScheduler = require('../services/taskScheduler');
    await taskScheduler.unregisterPlan(planId);

    // 刪除計畫
    await collection.deleteOne({ _id: new ObjectId(planId) });

    // 廣播刷圖計畫刪除事件
    try {
      const websocketService = require('../services/websocketService');
      websocketService.broadcastRefreshPlanUpdate(storeId, {
        planId: plan._id.toString(),
        planData: {
          _id: plan._id.toString(),
          name: plan.name,
          status: plan.status,
          enabled: plan.enabled,
          lastRun: plan.lastRun,
          nextRun: plan.nextRun,
          statistics: plan.statistics,
          updatedFields: ['_id']
        }
      }, 'delete');
    } catch (error) {
      console.error('廣播刷圖計畫刪除事件失敗:', error);
    }

    res.status(204).send();
  } catch (error) {
    console.error('刪除刷圖計畫失敗:', error);
    res.status(500).json({ success: false, error: '刪除刷圖計畫失敗' });
  }
});

// 手動執行刷圖計畫
router.post('/stores/:storeId/refresh-plans/:planId/execute', authenticate, checkPermission(['store-settings:update']), async (req, res) => {
  try {
    const { storeId, planId } = req.params;

    // 驗證 planId 是否為有效的 ObjectId
    if (!ObjectId.isValid(planId)) {
      return res.status(400).json({ success: false, error: '無效的計畫ID格式' });
    }

    const { collection } = await getCollection();

    // 查找計畫
    const plan = await collection.findOne({
      _id: new ObjectId(planId),
      storeId
    });

    if (!plan) {
      return res.status(404).json({ success: false, error: '刷圖計畫不存在' });
    }

    // 檢查計畫是否正在執行
    if (plan.status === 'running') {
      return res.status(400).json({ success: false, error: '計畫正在執行中，請稍後再試' });
    }

    // 創建執行記錄（標記為手動執行）
    const executionEngine = require('../services/executionEngine');
    const executionId = await executionEngine.createExecutionRecord(plan, 'manual');

    // 異步執行計畫
    executionEngine.execute(plan, executionId)
      .catch(error => {
        console.error(`手動執行計畫失敗: ${error.message}`);
      });

    res.json({
      success: true,
      data: {
        executionId,
        message: '計畫執行已啟動'
      }
    });
  } catch (error) {
    console.error('手動執行刷圖計畫失敗:', error);
    res.status(500).json({ success: false, error: '手動執行刷圖計畫失敗' });
  }
});

// 獲取計畫執行記錄
router.get('/stores/:storeId/refresh-plans/:planId/executions', authenticate, checkPermission(['store-settings:view']), async (req, res) => {
  try {
    const { storeId, planId } = req.params;
    const { page = 1, limit = 10, status, startDate, endDate } = req.query;

    // 驗證 planId 是否為有效的 ObjectId
    if (!ObjectId.isValid(planId)) {
      return res.status(400).json({ success: false, error: '無效的計畫ID格式' });
    }
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);

    // 獲取執行記錄集合
    const { client, db } = await getDbConnection();
    const executionCollection = db.collection('executionRecords');

    // 構建查詢條件
    const query = { planId: new ObjectId(planId), storeId };

    if (status) {
      query.status = status;
    }

    if (startDate || endDate) {
      query.startTime = {};
      if (startDate) {
        query.startTime.$gte = new Date(startDate);
      }
      if (endDate) {
        query.startTime.$lte = new Date(endDate);
      }
    }

    // 獲取總數
    const total = await executionCollection.countDocuments(query);

    // 獲取分頁數據
    const executions = await executionCollection.find(query)
      .sort({ startTime: -1 })
      .skip((pageNum - 1) * limitNum)
      .limit(limitNum)
      .toArray();

    res.json({
      success: true,
      data: {
        executions,
        total,
        page: pageNum,
        limit: limitNum
      }
    });
  } catch (error) {
    console.error('獲取執行記錄失敗:', error);
    res.status(500).json({ success: false, error: '獲取執行記錄失敗' });
  }
});

// 獲取執行記錄詳情
router.get('/stores/:storeId/refresh-plans/executions/:executionId', authenticate, checkPermission(['store-settings:view']), async (req, res) => {
  try {
    const { storeId, executionId } = req.params;

    // 驗證 executionId 是否為有效的 ObjectId
    if (!ObjectId.isValid(executionId)) {
      return res.status(400).json({ success: false, error: '無效的執行記錄ID格式' });
    }

    // 獲取執行記錄集合
    const { client, db } = await getDbConnection();
    const executionCollection = db.collection('executionRecords');

    const execution = await executionCollection.findOne({
      _id: new ObjectId(executionId),
      storeId
    });

    if (!execution) {
      return res.status(404).json({ success: false, error: '執行記錄不存在' });
    }

    res.json({ success: true, data: execution });
  } catch (error) {
    console.error('獲取執行記錄詳情失敗:', error);
    res.status(500).json({ success: false, error: '獲取執行記錄詳情失敗' });
  }
});



// 重新載入調度器（管理員功能）
router.post('/refresh-plans/scheduler/reload', authenticate, checkPermission(['system:admin']), async (req, res) => {
  try {
    const taskScheduler = require('../services/taskScheduler');
    await taskScheduler.reloadAllPlans();

    res.json({
      success: true,
      message: '調度器已重新載入'
    });
  } catch (error) {
    console.error('重新載入調度器失敗:', error);
    res.status(500).json({ success: false, error: '重新載入調度器失敗' });
  }
});

// 導出路由器和初始化函數
module.exports = { router, initDB };
