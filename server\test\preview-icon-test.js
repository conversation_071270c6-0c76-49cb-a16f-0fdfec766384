const { JSDOM } = require('jsdom');
const { createCanvas } = require('canvas');
const fs = require('fs');
const path = require('path');

console.log('=== 開始預覽服務圖標測試 ===\n');

// 創建虛擬DOM環境
function createVirtualDom() {
  const dom = new JSDOM(`
    <!DOCTYPE html>
    <html>
      <head>
        <style>
          body { margin: 0; padding: 0; }
          .preview-component { position: relative; }
          .elements-container { position: relative; }
        </style>
      </head>
      <body>
        <div id="root"></div>
      </body>
    </html>
  `, {
    url: "http://localhost",
    pretendToBeVisual: true,
    resources: "usable"
  });

  return dom;
}

/**
 * 渲染模板元素到DOM（簡化版，專注於圖標）
 */
function renderTemplateElements(elements, elementsContainer, document) {
  elements.forEach((element, index) => {
    const elementDiv = document.createElement('div');
    elementDiv.setAttribute('data-element-id', String(index));
    elementDiv.setAttribute('data-element-type', element.type);
    elementDiv.style.position = 'absolute';
    elementDiv.style.left = `${element.x}px`;
    elementDiv.style.top = `${element.y}px`;
    elementDiv.style.width = `${element.width}px`;
    elementDiv.style.height = `${element.height}px`;

    // 處理元素旋轉
    if (element.rotation) {
      elementDiv.style.transform = `rotate(${element.rotation}deg)`;
      elementDiv.style.transformOrigin = 'center center';
    }

    if (element.type === 'icon') {
      // 圖標元素渲染 - 使用 Lucide SVG 檔案
      const { renderIconSvg } = require('../utils/iconRenderer');
      
      // 計算圖標參數 - 與前端保持一致
      const iconType = element.iconType || 'circle';
      const iconColor = element.lineColor || '#000';
      const iconSize = Math.min(element.width, element.height) * 0.8;
      const strokeWidth = element.lineWidth || 2;
      
      console.log(`渲染圖標: ${iconType}, 尺寸: ${iconSize}, 顏色: ${iconColor}, 線條寬度: ${strokeWidth}`);
      
      // 生成 SVG 內容
      const iconSvg = renderIconSvg(iconType, {
        size: iconSize,
        color: iconColor,
        strokeWidth: strokeWidth
      });
      
      // 設置容器樣式 - 與前端完全一致
      elementDiv.style.border = 'none';
      elementDiv.style.backgroundColor = 'transparent';
      elementDiv.style.display = 'flex';
      elementDiv.style.justifyContent = 'center';
      elementDiv.style.alignItems = 'center';
      elementDiv.style.overflow = 'visible';
      
      // 直接插入 SVG 內容
      elementDiv.innerHTML = iconSvg;
      
      console.log(`✓ 圖標 ${iconType} 渲染完成`);
    }

    elementsContainer.appendChild(elementDiv);
  });
}

/**
 * 渲染畫布為圖像（簡化版，專注於圖標）
 */
async function renderCanvasToImage(containerRef, canvasWidth, canvasHeight, scale = 2) {
  try {
    console.log(`渲染畫布為圖像，原始尺寸: ${canvasWidth}x${canvasHeight}，縮放比例: ${scale}`);

    // 創建高解析度畫布
    const highResCanvas = createCanvas(canvasWidth * scale, canvasHeight * scale);
    const highResCtx = highResCanvas.getContext('2d');

    // 設置背景
    highResCtx.fillStyle = '#ffffff';
    highResCtx.fillRect(0, 0, canvasWidth * scale, canvasHeight * scale);

    // 獲取所有元素
    const elements = containerRef.querySelectorAll('[data-element-type]');
    console.log(`找到 ${elements.length} 個元素需要渲染`);

    // 渲染任務數組
    const renderTasks = [];

    for (const element of elements) {
      const elementType = element.getAttribute('data-element-type');
      
      // 獲取元素位置和尺寸
      const x = parseFloat(element.style.left) * scale;
      const y = parseFloat(element.style.top) * scale;
      const width = parseFloat(element.style.width) * scale;
      const height = parseFloat(element.style.height) * scale;
      
      // 獲取旋轉角度
      const transform = element.style.transform;
      let rotation = 0;
      if (transform && transform.includes('rotate')) {
        const rotateMatch = transform.match(/rotate\(([^)]+)\)/);
        if (rotateMatch) {
          rotation = parseFloat(rotateMatch[1]);
        }
      }

      console.log(`處理元素: ${elementType}, 位置: (${x/scale}, ${y/scale}), 尺寸: ${width/scale}x${height/scale}, 旋轉: ${rotation}°`);

      if (elementType === 'icon') {
        // 繪製圖標
        renderTasks.push(() => {
          // 獲取圖標的 SVG 內容
          let iconElement = element.querySelector('svg');
          
          if (iconElement) {
            console.log(`找到 SVG 元素，開始渲染圖標`);
            
            // 獲取 SVG 的 viewBox 和路徑數據
            const viewBox = iconElement.getAttribute('viewBox') || '0 0 24 24';
            const [, , vbWidth, vbHeight] = viewBox.split(' ').map(Number);
            
            // 獲取所有路徑元素
            const paths = iconElement.querySelectorAll('path, circle, line, rect, polygon');
            
            if (paths.length > 0) {
              highResCtx.save(); // 保存當前狀態
              
              if (rotation !== 0) {
                // 計算旋轉中心點
                const centerX = x + width / 2;
                const centerY = y + height / 2;
                
                // 移動到旋轉中心點
                highResCtx.translate(centerX, centerY);
                // 應用旋轉
                highResCtx.rotate((rotation * Math.PI) / 180);
                // 移回原點，但現在是相對於旋轉後的坐標系
                highResCtx.translate(-centerX, -centerY);
              }
              
              // 設置縮放比例，將 SVG 坐標系映射到元素尺寸
              const scaleX = width / vbWidth;
              const scaleY = height / vbHeight;
              
              // 移動到元素位置並應用縮放
              highResCtx.translate(x, y);
              highResCtx.scale(scaleX, scaleY);
              
              // 設置 SVG 樣式
              const strokeColor = iconElement.getAttribute('stroke') || '#000';
              const strokeWidth = parseFloat(iconElement.getAttribute('stroke-width') || '2');
              const fillColor = iconElement.getAttribute('fill') || 'none';
              
              highResCtx.strokeStyle = strokeColor;
              highResCtx.lineWidth = strokeWidth;
              highResCtx.lineCap = 'round';
              highResCtx.lineJoin = 'round';
              
              if (fillColor !== 'none') {
                highResCtx.fillStyle = fillColor;
              }
              
              // 使用簡化的 SVG 渲染器來渲染整個 SVG 元素
              const { renderSvgElement } = require('../utils/simpleSvgRenderer');

              console.log('使用簡化 SVG 渲染器渲染圖標');

              renderSvgElement(highResCtx, iconElement, {
                scaleX: 1, // 縮放已經在上面應用了
                scaleY: 1,
                offsetX: 0,
                offsetY: 0,
                strokeColor: strokeColor,
                strokeWidth: strokeWidth,
                fillColor: fillColor
              });
              
              highResCtx.restore(); // 恢復狀態
              console.log(`✓ 圖標渲染到畫布完成`);
            }
          } else {
            console.log(`未找到 SVG 元素，繪製佔位符`);
            
            // 如果沒有找到 SVG，繪製一個佔位符
            highResCtx.save();
            
            if (rotation !== 0) {
              const centerX = x + width / 2;
              const centerY = y + height / 2;
              highResCtx.translate(centerX, centerY);
              highResCtx.rotate((rotation * Math.PI) / 180);
              highResCtx.translate(-centerX, -centerY);
            }
            
            highResCtx.fillStyle = '#f0f0f0';
            highResCtx.fillRect(x, y, width, height);
            highResCtx.strokeStyle = '#000000';
            highResCtx.strokeRect(x, y, width, height);
            
            highResCtx.fillStyle = '#000000';
            highResCtx.font = `${12 * scale}px sans-serif`;
            highResCtx.textAlign = 'center';
            highResCtx.textBaseline = 'middle';
            highResCtx.fillText('Icon', x + width / 2, y + height / 2);
            
            highResCtx.textAlign = 'start';
            highResCtx.textBaseline = 'alphabetic';
            highResCtx.restore();
          }
        });
      }
    }

    // 執行所有渲染任務
    console.log(`執行 ${renderTasks.length} 個渲染任務...`);
    renderTasks.forEach(task => task());

    // 創建最終尺寸的畫布
    console.log(`創建最終尺寸的畫布: ${canvasWidth}x${canvasHeight}`);
    const finalCanvas = createCanvas(canvasWidth, canvasHeight);
    const finalCtx = finalCanvas.getContext('2d');

    // 使用高質量的縮放算法
    finalCtx.imageSmoothingEnabled = true;
    try {
      finalCtx.imageSmoothingQuality = 'high';
    } catch (e) {
      console.log('imageSmoothingQuality 不被支持，使用默認值');
    }

    // 將高解析度畫布縮小到最終尺寸
    finalCtx.drawImage(
      highResCanvas,
      0, 0, canvasWidth * scale, canvasHeight * scale,
      0, 0, canvasWidth, canvasHeight
    );

    console.log(`畫布渲染完成`);
    return finalCanvas;
  } catch (error) {
    console.error('渲染畫布為圖像時出錯:', error);
    return null;
  }
}

/**
 * 測試預覽服務中的圖標渲染
 */
async function testPreviewIconRendering() {
  try {
    // 創建測試模板 - 簡化版，只測試一個圖標
    const testTemplate = {
      id: 'preview-icon-test',
      name: '預覽圖標測試',
      width: 200,
      height: 200,
      elements: [
        {
          id: 'icon1',
          type: 'icon',
          x: 75,
          y: 75,
          width: 50,
          height: 50,
          iconType: 'star',
          lineColor: '#000000',
          lineWidth: 2
        }
      ]
    };

    console.log('創建虛擬DOM環境...');
    const dom = createVirtualDom();
    const { window } = dom;
    const { document } = window;

    // 創建容器
    const tempContainer = document.createElement('div');
    tempContainer.className = 'preview-component';
    document.body.appendChild(tempContainer);

    const containerRef = document.createElement('div');
    containerRef.style.position = 'absolute';
    containerRef.style.left = '-9999px';
    containerRef.style.top = '0';
    containerRef.style.visibility = 'hidden';
    containerRef.style.width = `${testTemplate.width}px`;
    containerRef.style.height = `${testTemplate.height}px`;
    tempContainer.appendChild(containerRef);

    const elementsContainer = document.createElement('div');
    elementsContainer.className = 'elements-container';
    elementsContainer.style.position = 'relative';
    elementsContainer.style.width = '100%';
    elementsContainer.style.height = '100%';
    containerRef.appendChild(elementsContainer);

    console.log('渲染模板元素...');
    renderTemplateElements(testTemplate.elements, elementsContainer, document);

    console.log('渲染畫布為圖像...');
    const renderedCanvas = await renderCanvasToImage(containerRef, testTemplate.width, testTemplate.height);

    if (renderedCanvas) {
      // 保存結果
      const outputPath = path.join(__dirname, 'preview-icon-test-output.png');
      const buffer = renderedCanvas.toBuffer('image/png');
      fs.writeFileSync(outputPath, buffer);

      console.log(`✓ 預覽圖標測試完成，結果已保存到: ${outputPath}`);
    } else {
      console.log('✗ 預覽圖標測試失敗');
    }

    // 清理DOM
    document.body.removeChild(tempContainer);

  } catch (error) {
    console.error('測試過程中發生錯誤:', error);
  }
}

// 執行測試
testPreviewIconRendering().then(() => {
  console.log('\n=== 預覽服務圖標測試完成 ===');
});
