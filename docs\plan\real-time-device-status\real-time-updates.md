# 設備和網關即時狀態更新系統

## 概述

本文檔描述了EPD管理系統中設備和網關即時狀態更新功能的實現。該系統通過WebSocket連接提供實時的設備和網關狀態更新，讓用戶能夠即時看到設備的在線狀態、電池電量、信號強度、圖片更新狀態等信息。

## 系統架構

### 前端架構

```
WebSocket客戶端 (websocketClient.ts)
├── 連接管理
├── 事件處理
├── 訂閱管理
└── 狀態恢復

設備列表頁面 (DevicesPage.tsx)
├── 即時狀態更新Hook
├── 狀態指示器
├── 統計面板
└── 自動狀態同步

網關管理頁面 (GatewaysPage.tsx)
├── 即時狀態更新Hook
├── 狀態指示器
├── 統計面板
└── 自動狀態同步
```

### 後端架構

```
WebSocket服務器
├── 設備狀態廣播
├── 網關狀態廣播
├── 訂閱管理
└── 事件分發

設備狀態監控
├── 狀態變化檢測
├── 批量狀態更新
├── 圖片更新狀態追蹤
└── 電池和信號監控

網關狀態監控
├── 連接狀態檢測
├── 固件版本追蹤
├── IP地址更新
└── 性能指標監控
```

## 功能特性

### 設備即時更新

1. **設備狀態監控**
   - 在線/離線狀態
   - 最後見到時間
   - 電池電量
   - 信號強度(RSSI)

2. **圖片更新狀態**
   - 圖片代碼追蹤
   - 更新狀態顯示
   - 同步狀態指示

3. **統計信息**
   - 在線設備數量
   - 離線設備數量
   - 已更新圖片數量
   - 待更新圖片數量

### 網關即時更新

1. **網關狀態監控**
   - 在線/離線狀態
   - 最後見到時間
   - IP地址變化
   - 連接信息

2. **固件信息**
   - WiFi固件版本
   - 藍牙固件版本
   - 型號信息
   - 性能指標

3. **統計信息**
   - 在線網關數量
   - 離線網關數量
   - 已識別型號數量
   - 網關型號種類

## 技術實現

### WebSocket客戶端

#### 核心類型定義

```typescript
// 設備狀態事件
interface DeviceStatusEvent {
  type: 'device_status_update';
  storeId: string;
  devices: Array<{
    _id: string;
    status: string;
    lastSeen: string;
    imageUpdateStatus?: string;
    data?: {
      battery?: number;
      rssi?: number;
      imageCode?: string;
    };
  }>;
  timestamp: string;
}

// 網關狀態事件
interface GatewayStatusEvent {
  type: 'gateway_status_update';
  storeId: string;
  gateways: Array<{
    _id: string;
    status: string;
    lastSeen: string;
    name?: string;
    model?: string;
    wifiFirmwareVersion?: string;
    btFirmwareVersion?: string;
    ipAddress?: string;
  }>;
  timestamp: string;
}
```

#### 訂閱管理

```typescript
// 設備狀態訂閱
public subscribeDeviceStatus(storeId?: string, options?: any) {
  if (storeId) {
    this.subscribedStores.add(storeId);
  }

  this.send({
    type: 'subscribe_device_status',
    storeId,
    options: options || {
      includeImageStatus: true,
      includeBatteryInfo: true
    },
    timestamp: new Date().toISOString()
  });
}

// 網關狀態訂閱
public subscribeGatewayStatus(storeId?: string, options?: any) {
  if (storeId) {
    this.subscribedGatewayStores.add(storeId);
  }

  this.send({
    type: 'subscribe_gateway_status',
    storeId,
    options: options || {
      includeConnectionInfo: true,
      includeFirmwareInfo: true
    },
    timestamp: new Date().toISOString()
  });
}
```

### 前端整合

#### 設備列表頁面整合

```typescript
// 設備狀態即時更新Hook
useEffect(() => {
  if (!store?.id || !isRealTimeEnabled) return;

  const handleDeviceStatusUpdate = (event: DeviceStatusEvent) => {
    if (event.storeId !== store.id) return;

    // 選擇性更新設備狀態
    setDevices(prevDevices => {
      return prevDevices.map(device => {
        const update = event.devices.find(d => d._id === device._id);
        if (update) {
          return {
            ...device,
            status: update.status as DeviceStatus,
            lastSeen: new Date(update.lastSeen),
            imageUpdateStatus: update.imageUpdateStatus || device.imageUpdateStatus,
            data: {
              ...device.data,
              battery: update.data?.battery ?? device.data?.battery,
              rssi: update.data?.rssi ?? device.data?.rssi,
              imageCode: update.data?.imageCode ?? device.data?.imageCode
            }
          };
        }
        return device;
      });
    });
  };

  const unsubscribe = subscribeToDeviceStatus(
    store.id,
    handleDeviceStatusUpdate,
    {
      includeImageStatus: true,
      includeBatteryInfo: true
    }
  );

  return () => unsubscribe();
}, [store?.id, isRealTimeEnabled]);
```

#### 網關管理頁面整合

```typescript
// 網關狀態即時更新Hook
useEffect(() => {
  if (!store?.id || !isRealTimeEnabled) return;

  const handleGatewayStatusUpdate = (event: GatewayStatusEvent) => {
    if (event.storeId !== store.id) return;

    // 選擇性更新網關狀態
    setGateways(prevGateways => {
      return prevGateways.map(gateway => {
        const update = event.gateways.find(g => g._id === gateway._id);
        if (update) {
          return {
            ...gateway,
            status: update.status as GatewayStatus,
            lastSeen: new Date(update.lastSeen),
            name: update.name || gateway.name,
            model: update.model || gateway.model,
            wifiFirmwareVersion: update.wifiFirmwareVersion || gateway.wifiFirmwareVersion,
            btFirmwareVersion: update.btFirmwareVersion || gateway.btFirmwareVersion,
            ipAddress: update.ipAddress || gateway.ipAddress
          };
        }
        return gateway;
      });
    });
  };

  const unsubscribe = subscribeToGatewayStatus(
    store.id,
    handleGatewayStatusUpdate,
    {
      includeConnectionInfo: true,
      includeFirmwareInfo: true
    }
  );

  return () => unsubscribe();
}, [store?.id, isRealTimeEnabled]);
```

## 用戶界面

### 即時更新狀態指示器

每個頁面都包含一個狀態指示器，顯示即時更新的連接狀態：

- **綠色圓點**: 已連接，正在接收即時更新
- **黃色圓點**: 正在連接中
- **紅色圓點**: 連接已斷開
- **灰色圓點**: 即時更新已關閉

用戶可以通過點擊"開啟/關閉"按鈕來控制即時更新功能。

### 統計面板

#### 設備統計面板
- 在線設備數量 (綠色)
- 離線設備數量 (灰色)
- 已更新圖片數量 (藍色)
- 待更新圖片數量 (橙色)

#### 網關統計面板
- 在線網關數量 (綠色)
- 離線網關數量 (灰色)
- 已識別型號數量 (藍色)
- 網關型號種類 (紫色)

## 性能優化

### 選擇性更新
- 只更新有變化的設備/網關
- 保留未變化的屬性值
- 避免不必要的重新渲染

### 連接管理
- 自動重連機制
- 訂閱狀態恢復
- 心跳檢測

### 內存管理
- 及時清理事件監聽器
- 組件卸載時取消訂閱
- 避免內存洩漏

## 錯誤處理

### 連接錯誤
- 自動重試連接
- 顯示連接狀態
- 降級到手動刷新

### 數據錯誤
- 驗證接收的數據
- 忽略無效更新
- 記錄錯誤日誌

### 網絡錯誤
- 檢測網絡狀態
- 暫停/恢復訂閱
- 用戶友好的錯誤提示

## 配置選項

### 訂閱選項

#### 設備狀態訂閱
```typescript
{
  includeImageStatus: true,    // 包含圖片更新狀態
  includeBatteryInfo: true     // 包含電池信息
}
```

#### 網關狀態訂閱
```typescript
{
  includeConnectionInfo: true, // 包含連接信息
  includeFirmwareInfo: true    // 包含固件信息
}
```

### 連接配置
- 重連間隔: 5秒
- 最大重連次數: 無限制
- 心跳間隔: 30秒
- 訂閱恢復: 自動

## 使用指南

### 啟用即時更新

1. 進入設備列表或網關管理頁面
2. 查看頁面頂部的即時更新狀態指示器
3. 如果顯示"已關閉"，點擊"開啟"按鈕
4. 等待連接建立（狀態變為綠色）

### 查看統計信息

1. 在頁面頂部查看統計面板
2. 統計數據會即時更新
3. 不同顏色代表不同類型的統計

### 故障排除

1. **連接失敗**: 檢查網絡連接，刷新頁面
2. **數據不更新**: 確認即時更新已開啟
3. **性能問題**: 可以暫時關閉即時更新

## 未來改進

### 計劃功能
- 自定義更新頻率
- 更細粒度的訂閱控制
- 歷史狀態追蹤
- 狀態變化通知

### 性能優化
- 增量更新
- 數據壓縮
- 批量處理
- 智能緩存

## 總結

設備和網關即時狀態更新系統為EPD管理系統提供了實時的狀態監控能力，大大提升了用戶體驗和操作效率。通過WebSocket技術實現的雙向通信，確保了數據的實時性和準確性，同時通過合理的架構設計保證了系統的穩定性和可擴展性。
