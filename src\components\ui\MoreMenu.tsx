import React, { useState, useEffect, useRef } from 'react';
import { MoreHorizontal } from 'lucide-react';

// 定義選單項目的介面
interface MenuItem {
  label: string;
  onClick: () => void;
}

// 定義 MoreMenu 的 props 介面
interface MoreMenuProps {
  // 選單項目列表
  menuItems: MenuItem[];
  // 按鈕顏色風格
  buttonStyle?: 'blue' | 'green' | 'gray';
  // 按鈕文字
  buttonText?: string;
  // 自定義按鈕樣式
  buttonClassName?: string;
  // 自定義下拉選單樣式
  dropdownClassName?: string;
}

export const MoreMenu: React.FC<MoreMenuProps> = ({
  menuItems,
  buttonStyle = 'green',
  buttonText = 'More',
  buttonClassName,
  dropdownClassName,
}) => {
  // 控制選單顯示狀態
  const [showMenu, setShowMenu] = useState(false);
  // 創建一個 ref 用於追蹤選單容器
  const menuRef = useRef<HTMLDivElement>(null);

  // 根據指定的按鈕風格生成對應的樣式類名
  const getButtonColorClass = () => {
    switch (buttonStyle) {
      case 'blue':
        return 'bg-blue-500 hover:bg-blue-600';
      case 'green':
        return 'bg-green-500 hover:bg-green-600';
      case 'gray':
        return 'bg-gray-500 hover:bg-gray-600';
      default:
        return 'bg-green-500 hover:bg-green-600';
    }
  };

  // 預設按鈕樣式
  const defaultButtonClass = `flex items-center gap-1 px-4 py-2 ${getButtonColorClass()} text-white rounded-md relative`;
  // 使用提供的自定義樣式或預設樣式
  const finalButtonClass = buttonClassName || defaultButtonClass;

  // 切換選單顯示狀態
  const toggleMenu = () => {
    setShowMenu(!showMenu);
  };

  // 處理點擊外部關閉選單
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };

    // 添加事件監聽器
    document.addEventListener('mousedown', handleClickOutside);
    
    // 清理事件監聽器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [menuRef]);

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={toggleMenu}
        className={finalButtonClass}
      >
        <MoreHorizontal size={18} />
        {buttonText}
      </button>
      
      {showMenu && (
        <div className={`absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg z-50 ${dropdownClassName || ''}`}>
          <ul className="py-1">
            {menuItems.map((item, index) => (
              <li key={index}>
                <button
                  onClick={() => {
                    item.onClick();
                    setShowMenu(false);
                  }}
                  className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                >
                  {item.label}
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default MoreMenu;