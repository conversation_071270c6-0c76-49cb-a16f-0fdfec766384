import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { DataField, DataFieldType, StoreData } from '../types';
import { updateStoreData } from '../utils/api/storeDataApi';

interface EditStoreDataModalProps {
  isOpen: boolean;
  dataFields: DataField[];
  storeData: StoreData | null;
  storeId: string; // 門店ID
  onClose: () => void;
  onSuccess: () => void;
}

function EditStoreDataModal({ isOpen, dataFields, storeData, storeId, onClose, onSuccess }: EditStoreDataModalProps) {
  // 初始化表單數據
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 當門店資料變更時，更新表單數據
  useEffect(() => {
    if (storeData) {
      const initialData: Record<string, any> = {};
      dataFields.forEach(field => {
        // 使用門店資料中的值，如果不存在則使用預設值
        if (field.type === DataFieldType.NUMBER) {
          initialData[field.id] = storeData[field.id] !== undefined ? storeData[field.id] : (field.defaultValue || 0);
        } else {
          initialData[field.id] = storeData[field.id] !== undefined ? storeData[field.id] : (field.defaultValue || '');
        }
      });
      setFormData(initialData);
    }
  }, [storeData, dataFields]);

  // 當欄位值變更時
  const handleChange = (fieldId: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }));

    // 清除該欄位的錯誤
    if (errors[fieldId]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }
  };

  // 表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 驗證表單
    const newErrors: Record<string, string> = {};

    // 在提交前處理表單數據
    const processedFormData = { ...formData };

    dataFields.forEach(field => {
      // 處理數字類型
      if (field.type === DataFieldType.NUMBER) {
        // 如果是空字串或未定義，則設為 0
        if (formData[field.id] === '' || formData[field.id] === undefined) {
          processedFormData[field.id] = 0;
        } else if (isNaN(Number(formData[field.id]))) {
          newErrors[field.id] = '必須是數字';
        } else {
          // 確保是數字類型
          processedFormData[field.id] = Number(formData[field.id]);
        }
      }
    });

    // 更新表單數據為處理後的數據
    setFormData(processedFormData);

    // 如果有錯誤，顯示錯誤並停止提交
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    if (!storeData) {
      setErrors({
        form: '門店資料不存在'
      });
      return;
    }

    try {
      setIsSubmitting(true);
      
      // 使用從 props 傳入的 storeId
      if (!storeId) {
        throw new Error('門店ID不能為空，請確認是否在正確的門店頁面');
      }
      
      console.log('使用的門店ID:', storeId);
      
      // 呼叫 API 更新門店資料，傳遞 uid 和 storeId 作為參數
      await updateStoreData(storeData.uid, processedFormData, storeId);

      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('更新門店資料失敗:', err);
      setErrors({
        form: err.message || '更新門店資料失敗，請重試'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 如果模態窗口不開啟或沒有門店資料，不渲染任何內容
  if (!isOpen || !storeData) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center overflow-y-auto">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        {/* 標題欄 */}
        <div className="flex justify-between items-center border-b border-gray-200 px-6 py-4">
          <h2 className="text-xl font-bold text-gray-800">編輯門店資料</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        {/* 表單 */}
        <form onSubmit={handleSubmit} className="px-6 py-4">
          {/* 一般錯誤信息 */}
          {errors.form && (
            <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700">
              {errors.form}
            </div>
          )}

          {/* 動態生成表單欄位 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {dataFields
              .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
              .map(field => (
                <div key={field.id} className="mb-4">
                  <label
                    htmlFor={`field-${field.id}`}
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {field.name}
                    {field.id === 'id' && <span className="text-red-500 ml-1">*</span>}
                  </label>

                  {/* 根據欄位類型生成對應輸入控件 */}
                  {field.type === DataFieldType.NUMBER ? (
                    <input
                      type="number"
                      id={`field-${field.id}`}
                      value={formData[field.id] || ''}
                      onChange={(e) => handleChange(field.id, e.target.value)}
                      className={`w-full px-3 py-2 border ${
                        errors[field.id] ? 'border-red-500' : 'border-gray-300'
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      placeholder={field.description || ''}
                      disabled={field.id === 'id'} // ID 欄位禁用編輯
                    />
                  ) : field.type === DataFieldType.BARCODE_CODE128 ||
                      field.type === DataFieldType.BARCODE_EAN13 ||
                      field.type === DataFieldType.BARCODE_UPC_A ? (
                    <input
                      type="text"
                      id={`field-${field.id}`}
                      value={formData[field.id] || ''}
                      onChange={(e) => handleChange(field.id, e.target.value)}
                      className={`w-full px-3 py-2 border ${
                        errors[field.id] ? 'border-red-500' : 'border-gray-300'
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      placeholder={`${field.description || ''} (${field.type})`}
                      disabled={field.id === 'id'} // ID 欄位禁用編輯
                    />
                  ) : field.type === DataFieldType.QR_CODE ? (
                    <input
                      type="text"
                      id={`field-${field.id}`}
                      value={formData[field.id] || ''}
                      onChange={(e) => handleChange(field.id, e.target.value)}
                      className={`w-full px-3 py-2 border ${
                        errors[field.id] ? 'border-red-500' : 'border-gray-300'
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      placeholder={`${field.description || ''} (QR Code)`}
                      disabled={field.id === 'id'} // ID 欄位禁用編輯
                    />
                  ) : (
                    // 預設為純文本輸入框
                    <input
                      type="text"
                      id={`field-${field.id}`}
                      value={formData[field.id] || ''}
                      onChange={(e) => handleChange(field.id, e.target.value)}
                      className={`w-full px-3 py-2 border ${
                        errors[field.id] ? 'border-red-500' : 'border-gray-300'
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      placeholder={field.description || ''}
                      disabled={field.id === 'id'} // ID 欄位禁用編輯
                    />
                  )}

                  {/* 錯誤訊息 */}
                  {errors[field.id] && (
                    <p className="mt-1 text-sm text-red-500">{errors[field.id]}</p>
                  )}
                </div>
              ))}
          </div>

          {/* 按鈕 */}
          <div className="flex justify-end gap-3 mt-6 border-t border-gray-200 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              disabled={isSubmitting}
            >
              取消
            </button>
            <button
              type="submit"
              className={`px-4 py-2 bg-violet-500 text-white rounded-md ${
                isSubmitting ? 'opacity-75 cursor-not-allowed' : 'hover:bg-violet-600'
              }`}
              disabled={isSubmitting}
            >
              {isSubmitting ? '儲存中...' : '確認修改'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// 在文件末尾明確導出組件
export { EditStoreDataModal };
