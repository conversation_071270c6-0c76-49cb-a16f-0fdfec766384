/**
 * 泡泡通知工具函數
 * 基於模板創建成功的泡泡訊息實現，支持不同類型和手動關閉
 */

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

interface NotificationOptions {
  type?: NotificationType;
  duration?: number; // 自動消失時間（毫秒），0 表示不自動消失
  showCloseButton?: boolean; // 是否顯示關閉按鈕
}

/**
 * 顯示泡泡通知
 * @param message 通知訊息
 * @param options 通知選項
 */
export const showBubbleNotification = (
  message: string, 
  options: NotificationOptions = {}
): void => {
  const {
    type = 'success',
    duration = 3000,
    showCloseButton = true
  } = options;

  // 建立自定義彈出訊息元素
  const messageDiv = document.createElement('div');
  
  // 設置基本樣式 - 添加玻璃效果
  messageDiv.style.position = 'fixed';
  messageDiv.style.top = '20px';
  messageDiv.style.left = '50%';
  messageDiv.style.transform = 'translateX(-50%)';
  messageDiv.style.padding = '15px 25px';
  messageDiv.style.borderRadius = '12px';
  messageDiv.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.1)';
  messageDiv.style.backdropFilter = 'blur(16px)';
  (messageDiv.style as any).webkitBackdropFilter = 'blur(16px)';
  messageDiv.style.zIndex = '9999';
  messageDiv.style.display = 'flex';
  messageDiv.style.alignItems = 'center';
  messageDiv.style.gap = '10px';
  messageDiv.style.maxWidth = '500px';
  messageDiv.style.minWidth = '200px';
  messageDiv.style.fontSize = '14px';
  messageDiv.style.fontWeight = '600';
  messageDiv.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.1)';
  messageDiv.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
  messageDiv.style.opacity = '0';
  
  // 根據類型設置顏色 - 使用半透明背景配合玻璃效果
  switch (type) {
    case 'success':
      messageDiv.style.backgroundColor = 'rgba(76, 175, 80, 0.85)';
      messageDiv.style.color = 'white';
      messageDiv.style.border = '1px solid rgba(76, 175, 80, 0.3)';
      break;
    case 'error':
      messageDiv.style.backgroundColor = 'rgba(244, 67, 54, 0.85)';
      messageDiv.style.color = 'white';
      messageDiv.style.border = '1px solid rgba(244, 67, 54, 0.3)';
      break;
    case 'warning':
      messageDiv.style.backgroundColor = 'rgba(255, 152, 0, 0.85)';
      messageDiv.style.color = 'white';
      messageDiv.style.border = '1px solid rgba(255, 152, 0, 0.3)';
      break;
    case 'info':
      messageDiv.style.backgroundColor = 'rgba(33, 150, 243, 0.85)';
      messageDiv.style.color = 'white';
      messageDiv.style.border = '1px solid rgba(33, 150, 243, 0.3)';
      break;
  }

  // 添加圖標
  const iconSpan = document.createElement('span');
  iconSpan.style.fontSize = '16px';
  iconSpan.style.lineHeight = '1';
  
  switch (type) {
    case 'success':
      iconSpan.textContent = '✓';
      break;
    case 'error':
      iconSpan.textContent = '✕';
      break;
    case 'warning':
      iconSpan.textContent = '⚠';
      break;
    case 'info':
      iconSpan.textContent = 'ℹ';
      break;
  }
  
  messageDiv.appendChild(iconSpan);

  // 添加訊息文字
  const messageSpan = document.createElement('span');
  messageSpan.textContent = message;
  messageSpan.style.flex = '1';
  messageDiv.appendChild(messageSpan);

  // 添加關閉按鈕
  if (showCloseButton) {
    const closeButton = document.createElement('button');
    closeButton.textContent = '×';
    closeButton.style.background = 'rgba(255, 255, 255, 0.2)';
    closeButton.style.border = '1px solid rgba(255, 255, 255, 0.3)';
    closeButton.style.borderRadius = '50%';
    closeButton.style.color = 'inherit';
    closeButton.style.fontSize = '16px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.padding = '2px 6px';
    closeButton.style.marginLeft = '10px';
    closeButton.style.lineHeight = '1';
    closeButton.style.opacity = '0.8';
    closeButton.style.transition = 'all 0.2s ease';
    closeButton.style.width = '24px';
    closeButton.style.height = '24px';
    closeButton.style.display = 'flex';
    closeButton.style.alignItems = 'center';
    closeButton.style.justifyContent = 'center';

    closeButton.addEventListener('mouseenter', () => {
      closeButton.style.opacity = '1';
      closeButton.style.background = 'rgba(255, 255, 255, 0.3)';
      closeButton.style.transform = 'scale(1.1)';
    });

    closeButton.addEventListener('mouseleave', () => {
      closeButton.style.opacity = '0.8';
      closeButton.style.background = 'rgba(255, 255, 255, 0.2)';
      closeButton.style.transform = 'scale(1)';
    });

    closeButton.addEventListener('click', () => {
      removeNotification(messageDiv);
    });

    messageDiv.appendChild(closeButton);
  }

  // 添加到頁面
  document.body.appendChild(messageDiv);

  // 淡入動畫
  setTimeout(() => {
    messageDiv.style.opacity = '1';
  }, 10);

  // 自動移除（如果設置了持續時間）
  if (duration > 0) {
    setTimeout(() => {
      removeNotification(messageDiv);
    }, duration);
  }
};

/**
 * 移除通知
 * @param messageDiv 通知元素
 */
const removeNotification = (messageDiv: HTMLElement): void => {
  if (messageDiv.parentNode) {
    messageDiv.style.opacity = '0';
    messageDiv.style.transform = 'translateX(-50%) translateY(-20px) scale(0.95)';
    messageDiv.style.backdropFilter = 'blur(8px)';
    (messageDiv.style as any).webkitBackdropFilter = 'blur(8px)';

    setTimeout(() => {
      if (messageDiv.parentNode) {
        document.body.removeChild(messageDiv);
      }
    }, 300);
  }
};

/**
 * 便捷方法
 */
export const showSuccessNotification = (message: string, duration?: number) => {
  showBubbleNotification(message, { type: 'success', duration });
};

export const showErrorNotification = (message: string, duration?: number) => {
  showBubbleNotification(message, { type: 'error', duration });
};

export const showWarningNotification = (message: string, duration?: number) => {
  showBubbleNotification(message, { type: 'warning', duration });
};

export const showInfoNotification = (message: string, duration?: number) => {
  showBubbleNotification(message, { type: 'info', duration });
};
