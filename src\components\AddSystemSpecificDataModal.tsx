import React, { useState, useEffect } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { DataField, DataFieldType } from '../types';
import { createSystemSpecificData, checkSystemSpecificDataIdExists } from '../utils/api/systemSpecificDataApi';

interface AddSystemSpecificDataModalProps {
  isOpen: boolean;
  dataFields: DataField[];
  onClose: () => void;
  onSuccess: () => void;
}

export function AddSystemSpecificDataModal({ isOpen, dataFields, onClose, onSuccess }: AddSystemSpecificDataModalProps) {
  // 初始化表單數據
  const initialFormData: Record<string, any> = {};
  dataFields.forEach(field => {
    // 如果是數值類型，則預設為 0
    if (field.type === DataFieldType.NUMBER) {
      initialFormData[field.id] = field.defaultValue || 0;
    } else {
      initialFormData[field.id] = field.defaultValue || '';
    }
  });

  const [formData, setFormData] = useState<Record<string, any>>(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCheckingId, setIsCheckingId] = useState(false);
  const [idExists, setIdExists] = useState(false);

  // 當欄位值變更時
  const handleChange = (fieldId: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }));

    // 清除該欄位的錯誤
    if (errors[fieldId]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }

    // 如果是 ID 欄位，且值不為空，則檢查 ID 是否已存在
    if (fieldId === 'id' && value && value.trim() !== '') {
      checkIdExistence(value);
    } else if (fieldId === 'id') {
      // 如果 ID 欄位為空，重置檢查狀態
      setIdExists(false);
    }
  };

  // 檢查 ID 是否已存在
  const checkIdExistence = async (id: string) => {
    try {
      setIsCheckingId(true);
      const exists = await checkSystemSpecificDataIdExists(id);
      setIdExists(exists);
      if (exists) {
        setErrors(prev => ({
          ...prev,
          id: 'ID 已存在，請使用其他 ID'
        }));
      }
    } catch (error) {
      console.error('檢查 ID 失敗:', error);
    } finally {
      setIsCheckingId(false);
    }
  };

  // 處理表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 驗證表單
    const newErrors: Record<string, string> = {};

    // 檢查必填欄位
    dataFields.forEach(field => {
      if (field.id === 'id' && (!formData[field.id] || formData[field.id].trim() === '')) {
        newErrors[field.id] = 'ID 為必填項';
      }
    });

    // 處理表單數據
    const processedFormData = { ...formData };

    // 將數值類型的欄位轉換為數字
    dataFields.forEach(field => {
      if (field.type === DataFieldType.NUMBER && processedFormData[field.id] !== undefined) {
        const numValue = Number(processedFormData[field.id]);
        processedFormData[field.id] = isNaN(numValue) ? 0 : numValue;
      }
    });

    // 更新表單數據為處理後的數據
    setFormData(processedFormData);

    // 檢查 ID 是否存在（即使前面已經檢查過，這裡再次檢查以防漏掉）
    if (formData.id && !newErrors.id) {
      try {
        const exists = await checkSystemSpecificDataIdExists(formData.id);
        if (exists) {
          newErrors.id = `ID 已存在，請使用其他 ID`;
          setIdExists(true);
        }
      } catch (error) {
        console.error('提交時檢查 ID 失敗:', error);
      }
    }

    // 如果有錯誤，顯示錯誤並停止提交
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // 如果 ID 已存在，停止提交
    if (idExists) {
      setErrors(prev => ({
        ...prev,
        id: 'ID 已存在，請使用其他 ID'
      }));
      return;
    }

    try {
      setIsSubmitting(true);

      // 呼叫 API 創建系統專屬數據
      await createSystemSpecificData(formData);

      // 重置表單並關閉模態窗
      setFormData(initialFormData);
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('創建系統專屬數據失敗:', err);
      setErrors({
        form: err.message || '創建系統專屬數據失敗，請重試'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 如果模態窗口不開啟，不渲染任何內容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center overflow-y-auto">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        {/* 標題欄 */}
        <div className="flex justify-between items-center border-b border-gray-200 px-6 py-4">
          <h2 className="text-xl font-bold text-gray-800">新增系統專屬數據</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        {/* 表單 */}
        <form onSubmit={handleSubmit} className="px-6 py-4">
          {/* 一般錯誤信息 */}
          {errors.form && (
            <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700">
              {errors.form}
            </div>
          )}

          {/* 動態生成表單欄位 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {dataFields.map(field => (
              <div key={field.id} className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  {field.name}
                  {field.id === 'id' && <span className="text-red-500">*</span>}
                </label>
                <input
                  type={field.type === DataFieldType.NUMBER ? 'number' : 'text'}
                  className={`w-full px-3 py-2 border ${
                    errors[field.id] ? 'border-red-500' : 'border-gray-300'
                  } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  value={formData[field.id] || ''}
                  onChange={(e) => handleChange(field.id, e.target.value)}
                />
                {errors[field.id] && (
                  <p className="text-red-500 text-xs mt-1 flex items-center">
                    <AlertCircle size={12} className="mr-1" />
                    {errors[field.id]}
                  </p>
                )}
                {field.id === 'id' && idExists && !errors[field.id] && (
                  <p className="text-red-500 text-xs mt-1 flex items-center">
                    <AlertCircle size={12} className="mr-1" />
                    ID 已存在，請使用其他 ID
                  </p>
                )}
              </div>
            ))}
          </div>

          {/* 提交按鈕 */}
          <div className="flex justify-end mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-2 hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300"
              disabled={isSubmitting || isCheckingId}
            >
              {isSubmitting ? '提交中...' : '確認'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
