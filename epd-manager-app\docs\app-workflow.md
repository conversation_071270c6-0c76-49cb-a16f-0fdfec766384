# EPD Manager App 工作流程

## 1. 整體工作流程

EPD Manager App 的主要工作流程包括以下步驟：

1. **用戶認證**：用戶登入系統
2. **門店選擇**：用戶選擇要管理的門店
3. **網關掃描**：掃描本地網絡中的網關設備
4. **網關註冊**：將選定的網關註冊到服務器
5. **網關配置**：將服務器返回的 WebSocket 連接信息發送給網關
6. **完成設置**：網關與服務器建立 WebSocket 連接，開始正常工作

## 2. 詳細流程圖

```mermaid
flowchart TD
    Start([開始]) --> Login[用戶登入]
    Login --> Auth{認證成功?}
    Auth -->|否| Login
    Auth -->|是| StoreSelect[選擇門店]
    
    StoreSelect --> GatewayList[查看網關列表]
    GatewayList --> HasGateways{有網關?}
    
    HasGateways -->|是| ManageGateways[管理現有網關]
    HasGateways -->|否| ScanGateways[掃描本地網關]
    ManageGateways --> ScanGateways
    
    ScanGateways --> FoundGateways{發現網關?}
    FoundGateways -->|否| ScanGateways
    FoundGateways -->|是| SelectGateway[選擇要註冊的網關]
    
    SelectGateway --> RegisterGateway[註冊網關到服務器]
    RegisterGateway --> RegisterSuccess{註冊成功?}
    RegisterSuccess -->|否| RegisterGateway
    
    RegisterSuccess -->|是| ConfigureGateway[配置網關 WebSocket 連接]
    ConfigureGateway --> ConfigSuccess{配置成功?}
    ConfigSuccess -->|否| ConfigureGateway
    
    ConfigSuccess -->|是| GatewayConnected[網關已連接到服務器]
    GatewayConnected --> End([結束])
```

## 3. 用戶認證流程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant Server as 後端服務器
    
    User->>App: 輸入用戶名和密碼
    App->>Server: 發送登入請求
    Server->>Server: 驗證用戶憑證
    Server-->>App: 返回認證結果
    
    alt 認證成功
        Server-->>App: 返回 JWT Token
        App->>App: 存儲 Token
        App-->>User: 顯示登入成功
    else 認證失敗
        Server-->>App: 返回錯誤信息
        App-->>User: 顯示錯誤消息
    end
```

## 4. 網關掃描流程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant Gateway as 網關設備
    
    User->>App: 點擊掃描網關按鈕
    App->>App: 檢查網絡權限
    
    alt 權限已授予
        App->>Gateway: 發送 UDP 廣播掃描消息
        Gateway-->>App: 回應掃描消息
        App->>App: 收集網關信息
        App-->>User: 顯示發現的網關列表
    else 權限未授予
        App-->>User: 請求網絡權限
        User->>App: 授予權限
        App->>Gateway: 發送 UDP 廣播掃描消息
    end
```

## 5. 網關註冊流程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant Server as 後端服務器
    
    User->>App: 選擇要註冊的網關
    App->>App: 顯示網關詳情和表單
    User->>App: 填寫網關名稱等信息
    User->>App: 確認註冊
    
    App->>Server: 發送網關註冊請求
    Server->>Server: 創建網關記錄
    Server->>Server: 生成 WebSocket 連接信息
    Server-->>App: 返回註冊結果和連接信息
    
    alt 註冊成功
        App-->>User: 顯示註冊成功消息
        App->>App: 存儲網關信息和連接信息
    else 註冊失敗
        App-->>User: 顯示錯誤消息
    end
```

## 6. 網關配置流程

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as 移動應用
    participant Gateway as 網關設備
    participant Server as 後端服務器
    
    User->>App: 確認配置網關
    
    alt HTTP 配置方法
        App->>Gateway: 發送 HTTP 請求（含 WebSocket 連接信息）
        Gateway-->>App: 返回配置結果
    else TCP 配置方法
        App->>Gateway: 建立 TCP 連接
        App->>Gateway: 發送 WebSocket 連接信息
        Gateway-->>App: 返回配置結果
    end
    
    alt 配置成功
        App-->>User: 顯示配置成功消息
        Gateway->>Server: 建立 WebSocket 連接
        Server-->>Gateway: 確認連接
    else 配置失敗
        App-->>User: 顯示錯誤消息
        App-->>User: 提供重試選項
    end
```

## 7. 狀態轉換圖

```mermaid
stateDiagram-v2
    [*] --> 未登入
    未登入 --> 已登入: 用戶登入
    已登入 --> 未登入: 用戶登出
    
    已登入 --> 門店選擇
    門店選擇 --> 網關管理: 選擇門店
    
    網關管理 --> 網關掃描: 點擊掃描按鈕
    網關掃描 --> 網關列表: 發現網關
    網關列表 --> 網關註冊: 選擇網關
    
    網關註冊 --> 網關配置: 註冊成功
    網關配置 --> 網關管理: 配置成功
    
    網關管理 --> 門店選擇: 切換門店
    門店選擇 --> 已登入: 返回
    已登入 --> [*]: 退出應用
```

## 8. 用戶界面流程

```mermaid
flowchart TD
    LoginScreen[登入頁面] --> StoreListScreen[門店列表頁面]
    StoreListScreen --> GatewayListScreen[網關列表頁面]
    
    GatewayListScreen --> GatewayScanScreen[網關掃描頁面]
    GatewayScanScreen --> GatewayListScreen
    
    GatewayListScreen --> GatewayDetailScreen[網關詳情頁面]
    GatewayScanScreen --> AddGatewayScreen[添加網關頁面]
    
    AddGatewayScreen --> GatewayConfigScreen[網關配置頁面]
    GatewayConfigScreen --> GatewayListScreen
    
    GatewayListScreen --> StoreListScreen
    StoreListScreen --> LoginScreen
```

## 9. 錯誤處理流程

### 9.1 網關掃描錯誤

```mermaid
flowchart TD
    Start([開始掃描]) --> CheckPermission{檢查權限}
    CheckPermission -->|權限不足| RequestPermission[請求權限]
    RequestPermission --> PermissionGranted{權限已授予?}
    PermissionGranted -->|是| StartScan[開始掃描]
    PermissionGranted -->|否| ShowError[顯示錯誤]
    
    CheckPermission -->|權限足夠| StartScan
    StartScan --> NetworkAvailable{網絡可用?}
    NetworkAvailable -->|否| ShowNetworkError[顯示網絡錯誤]
    NetworkAvailable -->|是| Scanning[掃描中]
    
    Scanning --> Timeout{超時?}
    Timeout -->|是| ShowTimeoutError[顯示超時錯誤]
    Timeout -->|否| GatewaysFound{發現網關?}
    
    GatewaysFound -->|是| ShowResults[顯示結果]
    GatewaysFound -->|否| ShowNoGatewayError[顯示未發現網關]
    
    ShowError --> End([結束])
    ShowNetworkError --> End
    ShowTimeoutError --> End
    ShowResults --> End
    ShowNoGatewayError --> End
```

### 9.2 網關配置錯誤

```mermaid
flowchart TD
    Start([開始配置]) --> SelectMethod{選擇配置方法}
    
    SelectMethod -->|HTTP| TryHTTP[嘗試 HTTP 配置]
    SelectMethod -->|TCP| TryTCP[嘗試 TCP 配置]
    
    TryHTTP --> HTTPSuccess{HTTP 成功?}
    HTTPSuccess -->|是| ConfigSuccess[配置成功]
    HTTPSuccess -->|否| TryTCP
    
    TryTCP --> TCPSuccess{TCP 成功?}
    TCPSuccess -->|是| ConfigSuccess
    TCPSuccess -->|否| ShowError[顯示錯誤]
    
    ShowError --> RetryOption{重試?}
    RetryOption -->|是| Start
    RetryOption -->|否| End([結束])
    
    ConfigSuccess --> End
```

## 10. 數據流圖

```mermaid
flowchart TD
    User[用戶] -->|輸入認證信息| Auth[認證處理]
    Auth -->|認證結果| User
    
    User -->|選擇門店| StoreSelect[門店選擇]
    StoreSelect -->|門店列表| User
    
    User -->|掃描網關| Scanner[UDP 掃描器]
    Scanner -->|網關列表| User
    
    User -->|選擇網關| Register[網關註冊]
    Register -->|註冊結果| User
    
    User -->|確認配置| Config[網關配置]
    Config -->|配置結果| User
    
    Auth -->|Token| API[API 客戶端]
    StoreSelect -->|門店 ID| API
    Register -->|網關信息| API
    
    API -->|API 請求| Server[後端服務器]
    Server -->|API 回應| API
    
    Scanner -->|UDP 廣播| Gateway[網關設備]
    Gateway -->|UDP 回應| Scanner
    
    Config -->|配置信息| Gateway
    Gateway -->|配置結果| Config
    
    Gateway -->|WebSocket 連接| Server
    Server -->|WebSocket 消息| Gateway
```
