#!/bin/bash

# EPD Manager Docker 鏡像導出工具 (Linux/macOS)
echo "========================================"
echo "EPD Manager Docker 鏡像導出工具 (Linux/macOS)"
echo "========================================"
echo

# 檢查Docker是否安裝
if ! command -v docker &> /dev/null; then
    echo "[錯誤] Docker 未安裝，請先安裝 Docker"
    exit 1
fi

# 檢查Docker是否運行
if ! docker info &> /dev/null; then
    echo "[錯誤] Docker 未運行，請先啟動 Docker 服務"
    exit 1
fi

# 檢查docker-compose是否安裝
if ! command -v docker-compose &> /dev/null; then
    echo "[錯誤] docker-compose 未安裝，請先安裝 docker-compose"
    exit 1
fi

# 檢查是否在正確的目錄
if [ ! -f "docker-compose.yml" ]; then
    echo "[錯誤] 請在專案根目錄執行此腳本"
    echo "當前目錄: $(pwd)"
    exit 1
fi

echo "[信息] 正在構建 Docker 鏡像..."
docker-compose build
if [ $? -ne 0 ]; then
    echo "[錯誤] Docker 鏡像構建失敗"
    exit 1
fi

echo
echo "[信息] 正在導出所有鏡像到單一檔案..."
docker save -o release/epd-manager-all.tar \
  epd-manager-lite-epd-manager:latest \
  mongo:7-jammy

if [ $? -ne 0 ]; then
    echo "[錯誤] 鏡像導出失敗"
    exit 1
fi

echo
echo "[成功] 所有鏡像已導出到單一檔案："
echo "  - epd-manager-all.tar (包含 EPD Manager 應用 + MongoDB 資料庫)"
echo
echo "[提示] 您現在可以將 release 資料夾複製到目標主機並執行部署"
echo

# 顯示檔案大小
if [ -f "release/epd-manager-all.tar" ]; then
    echo "檔案大小:"
    ls -lh release/epd-manager-all.tar | awk '{print $5 " - " $9}'
fi

echo
echo "部署命令:"
echo "  Windows: deploy.bat"
echo "  Linux/macOS: ./deploy.sh"
echo
