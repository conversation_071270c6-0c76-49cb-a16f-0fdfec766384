import React from 'react';
import { Button, ButtonProps } from '../ui/button';
import { usePermission } from '../../hooks/usePermission';

interface PermissionButtonProps extends ButtonProps {
  /**
   * 需要的權限標識符
   */
  permission?: string;
  
  /**
   * 需要的權限標識符列表（任意一個）
   */
  anyPermissions?: string[];
  
  /**
   * 需要的權限標識符列表（全部）
   */
  allPermissions?: string[];
  
  /**
   * 當沒有權限時是否禁用按鈕
   * 如果為 true，則在沒有權限時禁用按鈕
   * 如果為 false，則在沒有權限時隱藏按鈕
   */
  disableWhenNoPermission?: boolean;
  
  /**
   * 子元素
   */
  children: React.ReactNode;
}

/**
 * 權限按鈕組件
 * 根據用戶權限控制按鈕的顯示或禁用狀態
 */
export const PermissionButton: React.FC<PermissionButtonProps> = ({
  permission,
  anyPermissions,
  allPermissions,
  disableWhenNoPermission = false,
  children,
  ...props
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermission();
  
  // 如果正在加載權限，不渲染按鈕
  if (loading) {
    return null;
  }
  
  // 檢查權限
  let hasAccess = true;
  
  if (permission) {
    hasAccess = hasPermission(permission);
  }
  
  if (anyPermissions && anyPermissions.length > 0) {
    hasAccess = hasAccess && hasAnyPermission(anyPermissions);
  }
  
  if (allPermissions && allPermissions.length > 0) {
    hasAccess = hasAccess && hasAllPermissions(allPermissions);
  }
  
  // 根據權限和設置渲染按鈕
  if (!hasAccess) {
    if (disableWhenNoPermission) {
      // 禁用按鈕
      return (
        <Button {...props} disabled>
          {children}
        </Button>
      );
    } else {
      // 隱藏按鈕
      return null;
    }
  }
  
  // 有權限，正常渲染按鈕
  return <Button {...props}>{children}</Button>;
};

export default PermissionButton;
