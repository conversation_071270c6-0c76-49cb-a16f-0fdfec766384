import React from 'react';
import { TemplateElement } from '../../../types';

// 控制點枚舉
export enum ControlHandle {
  TopLeft = 'tl',
  TopRight = 'tr',
  BottomLeft = 'bl',
  BottomRight = 'br',
  Top = 't',
  Right = 'r',
  Bottom = 'b',
  Left = 'l',
  Rotate = 'rotate'
}

// 控制點尺寸
const HANDLE_SIZE = 8;
const ROTATE_HANDLE_OFFSET = 20;

// 旋轉角度計算
export const calculateRotationAngle = (
  center: { x: number; y: number },
  point: { x: number; y: number },
  originalAngle: number = 0
) => {
  const angle = Math.atan2(point.y - center.y, point.x - center.x) * (180 / Math.PI);
  return angle;
};

// 控制點位置計算
export const getControlHandlePositions = (
  element: TemplateElement,
  handleSize = HANDLE_SIZE,
  rotateHandleOffset = ROTATE_HANDLE_OFFSET
) => {
  const { x, y, width, height, rotation = 0, type } = element;

  // 元素中心點
  const centerX = Math.round(x + width / 2);
  const centerY = Math.round(y + height / 2);

  // 調試信息
  console.log(`控制點計算 - 元素ID: ${element.id}, 位置: (${x}, ${y}), 尺寸: ${width}x${height}, 旋轉: ${rotation}度`);
  console.log(`控制點計算 - 中心點: (${centerX}, ${centerY})`);

  // 根據元素類型決定顯示哪些控制點
  const rawPositions: Record<string, { x: number; y: number }> = {};

  // 對於線段元素，只顯示兩個端點
  if (type === 'line') {
    // 計算線段實際的端點位置（考慮元素坐標和尺寸）
    const lineStartX = Math.round(x);
    const lineStartY = Math.round(y + height / 2);
    const lineEndX = Math.round(x + width);
    const lineEndY = Math.round(y + height / 2);

    // 將控制點設置到線段的兩端
    rawPositions[ControlHandle.Left] = { x: Math.round(lineStartX - handleSize / 2), y: Math.round(lineStartY - handleSize / 2) };
    rawPositions[ControlHandle.Right] = { x: Math.round(lineEndX - handleSize / 2), y: Math.round(lineEndY - handleSize / 2) };

    // 添加旋轉控制點（放在線段中心點的正上方）
    rawPositions[ControlHandle.Top] = { x: Math.round(centerX - handleSize / 2), y: Math.round(y - handleSize / 2) };
    rawPositions[ControlHandle.Rotate] = { x: Math.round(centerX - handleSize / 2), y: Math.round(y - rotateHandleOffset - handleSize / 2) };
  }
  // 對於圓形和橢圓，顯示上下左右四個控制點
  else if (type === 'circle' || type === 'ellipse') {
    rawPositions[ControlHandle.Top] = { x: Math.round(centerX - handleSize / 2), y: Math.round(y - handleSize / 2) };
    rawPositions[ControlHandle.Right] = { x: Math.round(x + width - handleSize / 2), y: Math.round(centerY - handleSize / 2) };
    rawPositions[ControlHandle.Bottom] = { x: Math.round(centerX - handleSize / 2), y: Math.round(y + height - handleSize / 2) };
    rawPositions[ControlHandle.Left] = { x: Math.round(x - handleSize / 2), y: Math.round(centerY - handleSize / 2) };
    // 添加旋轉控制點
    rawPositions[ControlHandle.Rotate] = { x: Math.round(centerX - handleSize / 2), y: Math.round(y - rotateHandleOffset - handleSize / 2) };
  }
  // 對於矩形、正方形和其他形狀，顯示四個角落的控制點
  else {
    rawPositions[ControlHandle.TopLeft] = { x: Math.round(x - handleSize / 2), y: Math.round(y - handleSize / 2) };
    rawPositions[ControlHandle.TopRight] = { x: Math.round(x + width - handleSize / 2), y: Math.round(y - handleSize / 2) };
    rawPositions[ControlHandle.BottomLeft] = { x: Math.round(x - handleSize / 2), y: Math.round(y + height - handleSize / 2) };
    rawPositions[ControlHandle.BottomRight] = { x: Math.round(x + width - handleSize / 2), y: Math.round(y + height - handleSize / 2) };

    // 添加邊緣控制點
    rawPositions[ControlHandle.Top] = { x: Math.round(centerX - handleSize / 2), y: Math.round(y - handleSize / 2) };
    rawPositions[ControlHandle.Right] = { x: Math.round(x + width - handleSize / 2), y: Math.round(centerY - handleSize / 2) };
    rawPositions[ControlHandle.Bottom] = { x: Math.round(centerX - handleSize / 2), y: Math.round(y + height - handleSize / 2) };
    rawPositions[ControlHandle.Left] = { x: Math.round(x - handleSize / 2), y: Math.round(centerY - handleSize / 2) };

    // 添加旋轉控制點
    rawPositions[ControlHandle.Rotate] = { x: Math.round(centerX - handleSize / 2), y: Math.round(y - rotateHandleOffset - handleSize / 2) };
  }

  // 如果沒有旋轉，直接返回未經旋轉的位置
  if (rotation === 0) {
    console.log('控制點計算 - 無旋轉, 原始位置:', rawPositions);
    return rawPositions;
  }

  // 如果有旋轉，計算旋轉後的控制點位置
  const rotatedPositions: Record<string, { x: number; y: number }> = {};

  // 轉換角度為弧度
  const rad = (rotation * Math.PI) / 180;
  const cos = Math.cos(rad);
  const sin = Math.sin(rad);

  console.log(`控制點計算 - 旋轉角度: ${rotation}度, cos: ${cos}, sin: ${sin}`);

  // 計算每個控制點旋轉後的位置
  for (const [key, point] of Object.entries(rawPositions)) {
    // 計算點的中心位置
    const pointCenterX = point.x + handleSize / 2;
    const pointCenterY = point.y + handleSize / 2;

    // 先將點位置轉換為相對於元素中心點的坐標
    const relativeX = pointCenterX - centerX;
    const relativeY = pointCenterY - centerY;

    // 進行旋轉計算
    const rotatedRelativeX = relativeX * cos - relativeY * sin;
    const rotatedRelativeY = relativeX * sin + relativeY * cos;

    // 轉換回絕對坐標，並調整控制點位置
    rotatedPositions[key] = {
      x: Math.round(centerX + rotatedRelativeX - handleSize / 2),
      y: Math.round(centerY + rotatedRelativeY - handleSize / 2)
    };

    console.log(`控制點計算 - 控制點 ${key}: 原始位置 (${point.x}, ${point.y}), 旋轉後 (${rotatedPositions[key].x}, ${rotatedPositions[key].y})`);
  }

  console.log('控制點計算 - 旋轉後所有點位置:', rotatedPositions);
  return rotatedPositions;
};

// 控制點元件
interface ControlPointProps {
  position: { x: number; y: number };
  type: ControlHandle;
  onMouseDown: (type: ControlHandle, e: React.MouseEvent) => void;
}

export const ControlPoint: React.FC<ControlPointProps> = ({ position, type, onMouseDown }) => {
  // 根據控制點類型設置不同樣式
  const isCorner = [
    ControlHandle.TopLeft, ControlHandle.TopRight,
    ControlHandle.BottomLeft, ControlHandle.BottomRight
  ].includes(type);

  const isEdge = [
    ControlHandle.Top, ControlHandle.Right,
    ControlHandle.Bottom, ControlHandle.Left
  ].includes(type);

  const isRotate = type === ControlHandle.Rotate;

  // 根據控制點類型設置不同的游標樣式
  let cursor = 'default';
  if (type === ControlHandle.TopLeft || type === ControlHandle.BottomRight) cursor = 'nwse-resize';
  if (type === ControlHandle.TopRight || type === ControlHandle.BottomLeft) cursor = 'nesw-resize';
  if (type === ControlHandle.Top || type === ControlHandle.Bottom) cursor = 'ns-resize';
  if (type === ControlHandle.Left || type === ControlHandle.Right) cursor = 'ew-resize';
  if (type === ControlHandle.Rotate) cursor = 'grab';

  return (
    <div
      style={{
        position: 'absolute',
        left: position.x,
        top: position.y,
        width: HANDLE_SIZE,
        height: HANDLE_SIZE,
        borderRadius: isRotate ? '50%' : isCorner ? '0' : '50%',
        backgroundColor: isRotate ? '#3b82f6' : 'white',
        border: `1px solid ${isRotate ? '#2563eb' : '#3b82f6'}`,
        cursor,
        zIndex: 100
      }}
      onMouseDown={(e) => onMouseDown(type, e)}
    />
  );
};

// 旋轉連接線元件
export const RotationConnector: React.FC<{
  from: { x: number; y: number };
  to: { x: number; y: number };
}> = ({ from, to }) => {
  // 計算從起點到終點的角度
  const angle = Math.atan2(to.y - from.y, to.x - from.x) * (180 / Math.PI);
  // 計算兩點之間的距離
  const distance = Math.sqrt(Math.pow(to.x - from.x, 2) + Math.pow(to.y - from.y, 2));

  // 調試信息
  console.log(`RotationConnector 渲染 - 起點: (${from.x}, ${from.y}), 終點: (${to.x}, ${to.y})`);
  console.log(`RotationConnector 渲染 - 角度: ${angle}度, 距離: ${distance}px`);

  return (
    <div
      style={{
        position: 'absolute',
        left: from.x + HANDLE_SIZE / 2,
        top: from.y + HANDLE_SIZE / 2,
        width: 1,
        height: distance,
        backgroundColor: '#3b82f6',
        transformOrigin: 'top',
        transform: `rotate(${angle}deg)`,
        zIndex: 99
      }}
    />
  );
};

// 調整大小函數
export const resizeElement = (
  element: TemplateElement,
  handle: ControlHandle,
  movementX: number,
  movementY: number,
  aspectRatio: boolean = false
): Partial<TemplateElement> => {
  let { x, y, width, height } = element;

  // 依據控制點類型調整元素尺寸
  switch (handle) {
    case ControlHandle.TopLeft:
      if (aspectRatio) {
        // 保持寬高比例
        const ratio = width / height;
        const newWidth = width - movementX;
        const newHeight = height - movementY;

        if (Math.abs(movementX) > Math.abs(movementY)) {
          height = Math.round(newWidth / ratio);
          width = Math.round(newWidth);
          y = Math.round(y + (element.height - height));
          x = Math.round(x + movementX);
        } else {
          width = Math.round(newHeight * ratio);
          height = Math.round(newHeight);
          x = Math.round(x + (element.width - width));
          y = Math.round(y + movementY);
        }
      } else {
        width = Math.round(width - movementX);
        height = Math.round(height - movementY);
        x = Math.round(x + movementX);
        y = Math.round(y + movementY);
      }
      break;

    case ControlHandle.TopRight:
      if (aspectRatio) {
        const ratio = width / height;
        const newWidth = width + movementX;
        const newHeight = height - movementY;

        if (Math.abs(movementX) > Math.abs(movementY)) {
          height = Math.round(newWidth / ratio);
          width = Math.round(newWidth);
          y = Math.round(y + (element.height - height));
        } else {
          width = Math.round(newHeight * ratio);
          height = Math.round(newHeight);
          y = Math.round(y + movementY);
        }
      } else {
        width = Math.round(width + movementX);
        height = Math.round(height - movementY);
        y = Math.round(y + movementY);
      }
      break;

    case ControlHandle.BottomLeft:
      if (aspectRatio) {
        const ratio = width / height;
        const newWidth = width - movementX;
        const newHeight = height + movementY;

        if (Math.abs(movementX) > Math.abs(movementY)) {
          height = Math.round(newWidth / ratio);
          width = Math.round(newWidth);
          x = Math.round(x + movementX);
        } else {
          width = Math.round(newHeight * ratio);
          height = Math.round(newHeight);
          x = Math.round(x + (element.width - width));
        }
      } else {
        width = Math.round(width - movementX);
        height = Math.round(height + movementY);
        x = Math.round(x + movementX);
      }
      break;

    case ControlHandle.BottomRight:
      if (aspectRatio) {
        const ratio = width / height;
        const newWidth = width + movementX;
        const newHeight = height + movementY;

        if (Math.abs(movementX) > Math.abs(movementY)) {
          height = Math.round(newWidth / ratio);
          width = Math.round(newWidth);
        } else {
          width = Math.round(newHeight * ratio);
          height = Math.round(newHeight);
        }
      } else {
        width = Math.round(width + movementX);
        height = Math.round(height + movementY);
      }
      break;

    case ControlHandle.Top:
      height = Math.round(height - movementY);
      y = Math.round(y + movementY);
      break;

    case ControlHandle.Right:
      width = Math.round(width + movementX);
      break;

    case ControlHandle.Bottom:
      height = Math.round(height + movementY);
      break;

    case ControlHandle.Left:
      width = Math.round(width - movementX);
      x = Math.round(x + movementX);
      break;
  }

  // 確保尺寸不會變成負數
  if (width < 5) width = 5;
  if (height < 5) height = 5;

  return { x, y, width, height };
};