// 簡單測試腳本
const { MongoClient, ObjectId } = require('mongodb');

const MONGODB_URI = 'mongodb://localhost:27017';
const DB_NAME = 'resourceManagement';

async function simpleTest() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('✅ 連接到 MongoDB');
    
    const db = client.db(DB_NAME);
    
    // 檢查門店
    const storesCollection = db.collection('stores');
    const storeCount = await storesCollection.countDocuments();
    console.log(`📊 門店數量: ${storeCount}`);
    
    if (storeCount === 0) {
      // 創建測試門店
      const testStore = {
        id: 'test-store-001',
        name: '測試門店',
        address: '測試地址',
        status: 'active',
        managerId: new ObjectId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        storeSpecificData: [],
        gatewayManagement: {},
        deviceManagement: {},
        storeSettings: {}
      };
      
      await storesCollection.insertOne(testStore);
      console.log('✅ 創建測試門店');
    }
    
    // 檢查刷圖計畫集合
    const refreshPlansCollection = db.collection('refreshPlans');
    const planCount = await refreshPlansCollection.countDocuments();
    console.log(`📊 刷圖計畫數量: ${planCount}`);
    
    // 檢查設備
    const devicesCollection = db.collection('devices');
    const deviceCount = await devicesCollection.countDocuments();
    console.log(`📊 設備數量: ${deviceCount}`);
    
    console.log('✅ 測試完成');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
  } finally {
    await client.close();
  }
}

simpleTest();
