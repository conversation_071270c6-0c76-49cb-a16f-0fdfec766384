import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import Cookies from 'js-cookie';
import { buildEndpointUrl } from '../utils/api/apiConfig';

// 用戶接口
export interface User {
  _id: string;
  username: string;
  name?: string;
  email?: string;
  phone?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 認證狀態接口
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;

  // 登入
  login: (username: string, password: string, rememberMe?: boolean) => Promise<void>;
  // 登出
  logout: () => Promise<void>;
  // 檢查登入狀態
  checkAuth: () => Promise<boolean>;
  // 修改密碼
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  // 清除錯誤
  clearError: () => void;
}

// 創建認證狀態管理
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: null,
      token: null,
      loading: false,
      error: null,

      // 登入
      login: async (username, password, rememberMe = false) => {
        console.log('authStore.login 被調用:', { username, rememberMe });
        try {
          console.log('設置 loading 狀態');
          set({ loading: true, error: null });

          console.log('發送登入請求到後端');
          const response = await fetch(buildEndpointUrl('auth', 'login'), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password, rememberMe }),
            credentials: 'include', // 包含 cookie
          });

          console.log('收到後端響應:', response.status);
          const data = await response.json();
          console.log('響應數據:', data);

          if (!response.ok) {
            console.error('登入失敗:', data.error);
            throw new Error(data.error || '登入失敗');
          }

          console.log('登入成功，更新狀態');
          set({
            isAuthenticated: true,
            user: data.user,
            token: data.token,
            loading: false,
          });
          console.log('狀態已更新');
        } catch (error: any) {
          console.error('登入過程中發生錯誤:', error);
          set({
            isAuthenticated: false,
            user: null,
            token: null,
            loading: false,
            error: error.message || '登入失敗',
          });
        }
      },

      // 登出
      logout: async () => {
        try {
          set({ loading: true });

          await fetch(buildEndpointUrl('auth', 'logout'), {
            method: 'POST',
            credentials: 'include', // 包含 cookie
          });

          // 清除 cookie
          Cookies.remove('token');

          set({
            isAuthenticated: false,
            user: null,
            token: null,
            loading: false,
          });
        } catch (error) {
          set({ loading: false });
        }
      },

      // 檢查登入狀態
      checkAuth: async () => {
        try {
          set({ loading: true });

          const { token } = get();

          const response = await fetch(buildEndpointUrl('auth', 'check'), {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              ...(token ? { Authorization: `Bearer ${token}` } : {}),
            },
            credentials: 'include', // 包含 cookie
          });

          const data = await response.json();

          if (!response.ok) {
            set({
              isAuthenticated: false,
              user: null,
              loading: false,
            });
            return false;
          }

          set({
            isAuthenticated: true,
            user: data.user,
            loading: false,
          });

          return true;
        } catch (error) {
          set({
            isAuthenticated: false,
            user: null,
            loading: false,
          });
          return false;
        }
      },

      // 修改密碼
      changePassword: async (currentPassword, newPassword) => {
        try {
          set({ loading: true, error: null });

          const { token } = get();

          const response = await fetch(buildEndpointUrl('auth', 'password'), {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              ...(token ? { Authorization: `Bearer ${token}` } : {}),
            },
            body: JSON.stringify({ currentPassword, newPassword }),
            credentials: 'include', // 包含 cookie
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.error || '修改密碼失敗');
          }

          set({ loading: false });
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || '修改密碼失敗',
          });
        }
      },

      // 清除錯誤
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'auth-storage', // localStorage 的鍵名
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
      }),
    }
  )
);
