/**
 * SVG 路徑渲染器 - 將 SVG 路徑數據渲染到 Canvas 上下文
 * 這個模組解決了 Node.js 環境中 Path2D 不可用的問題
 */

// 緩存 SVGPathData 模組
let SVGPathData = null;

/**
 * 動態載入 SVGPathData 模組
 */
async function loadSVGPathData() {
  if (!SVGPathData) {
    const module = await import('svg-pathdata');
    SVGPathData = module.SVGPathData;
  }
  return SVGPathData;
}

/**
 * 渲染 SVG 路徑到 Canvas 上下文
 * @param {CanvasRenderingContext2D} ctx Canvas 上下文
 * @param {string} pathData SVG 路徑數據 (d 屬性)
 * @param {boolean} fill 是否填充
 * @param {boolean} stroke 是否描邊
 */
async function renderSvgPath(ctx, pathData, fill = false, stroke = true) {
  try {
    // 動態載入 SVGPathData
    const SVGPathDataClass = await loadSVGPathData();

    // 解析 SVG 路徑數據
    const pathCommands = new SVGPathDataClass(pathData).toAbs().commands;
    
    ctx.beginPath();
    
    for (const command of pathCommands) {
      switch (command.type) {
        case SVGPathDataClass.MOVE_TO:
          ctx.moveTo(command.x, command.y);
          break;

        case SVGPathDataClass.LINE_TO:
          ctx.lineTo(command.x, command.y);
          break;

        case SVGPathDataClass.HORIZ_LINE_TO:
          ctx.lineTo(command.x, ctx.currentPoint?.y || 0);
          break;

        case SVGPathDataClass.VERT_LINE_TO:
          ctx.lineTo(ctx.currentPoint?.x || 0, command.y);
          break;

        case SVGPathDataClass.CURVE_TO:
          ctx.bezierCurveTo(
            command.x1, command.y1,
            command.x2, command.y2,
            command.x, command.y
          );
          break;

        case SVGPathDataClass.SMOOTH_CURVE_TO:
          // 平滑三次貝茲曲線 - 需要計算反射控制點
          const currentPoint = getCurrentPoint(ctx);
          const reflectedCP = getReflectedControlPoint(ctx, pathCommands, command);
          ctx.bezierCurveTo(
            reflectedCP.x, reflectedCP.y,
            command.x2, command.y2,
            command.x, command.y
          );
          break;

        case SVGPathDataClass.QUAD_TO:
          ctx.quadraticCurveTo(command.x1, command.y1, command.x, command.y);
          break;

        case SVGPathDataClass.SMOOTH_QUAD_TO:
          // 平滑二次貝茲曲線
          const smoothCP = getSmoothQuadControlPoint(ctx, pathCommands, command);
          ctx.quadraticCurveTo(smoothCP.x, smoothCP.y, command.x, command.y);
          break;

        case SVGPathDataClass.ARC:
          await renderArc(ctx, command);
          break;

        case SVGPathDataClass.CLOSE_PATH:
          ctx.closePath();
          break;

        default:
          console.warn(`不支援的 SVG 路徑命令: ${command.type}`);
          break;
      }
    }
    
    // 執行填充和描邊
    if (fill) {
      ctx.fill();
    }
    if (stroke) {
      ctx.stroke();
    }
    
  } catch (error) {
    console.error('渲染 SVG 路徑時發生錯誤:', error);
    // 如果路徑渲染失敗，繪製一個簡單的佔位符
    renderFallbackShape(ctx, fill, stroke);
  }
}

/**
 * 獲取當前路徑點（模擬）
 * 注意：Canvas API 沒有直接提供當前點，這裡是簡化實現
 */
function getCurrentPoint(ctx) {
  // 這是一個簡化的實現，實際上需要追蹤路徑狀態
  return { x: 0, y: 0 };
}

/**
 * 計算平滑曲線的反射控制點
 */
function getReflectedControlPoint(ctx, pathCommands, currentCommand) {
  // 簡化實現：返回當前點
  return { x: currentCommand.x2 || 0, y: currentCommand.y2 || 0 };
}

/**
 * 計算平滑二次曲線的控制點
 */
function getSmoothQuadControlPoint(ctx, pathCommands, currentCommand) {
  // 簡化實現：返回當前點
  return { x: currentCommand.x || 0, y: currentCommand.y || 0 };
}

/**
 * 渲染橢圓弧
 * @param {CanvasRenderingContext2D} ctx Canvas 上下文
 * @param {Object} arcCommand 弧命令對象
 */
function renderArc(ctx, arcCommand) {
  try {
    // 獲取當前點
    const currentPoint = getCurrentPoint(ctx);
    
    // 如果是圓弧（rx === ry），使用簡化的圓弧渲染
    if (arcCommand.rX === arcCommand.rY) {
      const radius = arcCommand.rX;
      const centerX = (currentPoint.x + arcCommand.x) / 2;
      const centerY = (currentPoint.y + arcCommand.y) / 2;
      
      // 計算起始和結束角度
      const startAngle = Math.atan2(currentPoint.y - centerY, currentPoint.x - centerX);
      const endAngle = Math.atan2(arcCommand.y - centerY, arcCommand.x - centerX);
      
      ctx.arc(centerX, centerY, radius, startAngle, endAngle, !arcCommand.sweepFlag);
    } else {
      // 橢圓弧的完整實現比較複雜，這裡使用簡化版本
      // 直接連線到終點
      ctx.lineTo(arcCommand.x, arcCommand.y);
    }
  } catch (error) {
    console.error('渲染弧形時發生錯誤:', error);
    // 如果弧形渲染失敗，直接連線到終點
    ctx.lineTo(arcCommand.x, arcCommand.y);
  }
}

/**
 * 渲染備用形狀（當路徑解析失敗時）
 */
function renderFallbackShape(ctx, fill, stroke) {
  // 繪製一個簡單的圓形作為備用
  ctx.beginPath();
  ctx.arc(12, 12, 8, 0, 2 * Math.PI); // 在 24x24 viewBox 中心繪製圓形
  
  if (fill) {
    ctx.fill();
  }
  if (stroke) {
    ctx.stroke();
  }
}

/**
 * 渲染完整的 SVG 元素到 Canvas
 * @param {CanvasRenderingContext2D} ctx Canvas 上下文
 * @param {Element} svgElement SVG DOM 元素
 * @param {Object} options 渲染選項
 */
async function renderSvgElement(ctx, svgElement, options = {}) {
  const {
    scaleX = 1,
    scaleY = 1,
    offsetX = 0,
    offsetY = 0,
    strokeColor = '#000',
    strokeWidth = 2,
    fillColor = 'none'
  } = options;
  
  ctx.save();
  
  // 應用變換
  ctx.translate(offsetX, offsetY);
  ctx.scale(scaleX, scaleY);
  
  // 設置樣式
  ctx.strokeStyle = strokeColor;
  ctx.lineWidth = strokeWidth;
  ctx.lineCap = 'round';
  ctx.lineJoin = 'round';
  
  if (fillColor !== 'none') {
    ctx.fillStyle = fillColor;
  }
  
  // 獲取所有路徑元素
  const pathElements = svgElement.querySelectorAll('path, circle, line, rect, polygon');
  
  for (const element of pathElements) {
    try {
      if (element.tagName === 'path') {
        const d = element.getAttribute('d');
        if (d) {
          await renderSvgPath(ctx, d, fillColor !== 'none', true);
        }
      } else if (element.tagName === 'circle') {
        const cx = parseFloat(element.getAttribute('cx') || '0');
        const cy = parseFloat(element.getAttribute('cy') || '0');
        const r = parseFloat(element.getAttribute('r') || '0');
        
        ctx.beginPath();
        ctx.arc(cx, cy, r, 0, 2 * Math.PI);
        if (fillColor !== 'none') {
          ctx.fill();
        }
        ctx.stroke();
      } else if (element.tagName === 'line') {
        const x1 = parseFloat(element.getAttribute('x1') || '0');
        const y1 = parseFloat(element.getAttribute('y1') || '0');
        const x2 = parseFloat(element.getAttribute('x2') || '0');
        const y2 = parseFloat(element.getAttribute('y2') || '0');
        
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
      } else if (element.tagName === 'rect') {
        const x = parseFloat(element.getAttribute('x') || '0');
        const y = parseFloat(element.getAttribute('y') || '0');
        const width = parseFloat(element.getAttribute('width') || '0');
        const height = parseFloat(element.getAttribute('height') || '0');
        
        ctx.beginPath();
        ctx.rect(x, y, width, height);
        if (fillColor !== 'none') {
          ctx.fill();
        }
        ctx.stroke();
      }
    } catch (error) {
      console.error(`渲染 ${element.tagName} 元素時發生錯誤:`, error);
    }
  }
  
  ctx.restore();
}

module.exports = {
  renderSvgPath,
  renderSvgElement
};
